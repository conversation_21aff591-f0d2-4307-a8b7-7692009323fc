//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-02 16:37:00
//FilePath: /yaml_scan/utils/rand/randomip.go
//Description:

package rand

import (
	"crypto/rand"
	"errors"
	"fmt"
	"net"

	iputil "yaml_scan/utils/ip"
)

const (
	maxIterations = 255
)

// getRandomIP: 从给定的 IP 网络中生成一个随机的 IP 地址。
//
//	@param ipnet *net.IPNet: 要生成随机 IP 的网络范围
//	@param size int:IP 地址的大小（4 表示 IPv4，16 表示 IPv6）
//	@return net.IP net.IP: 返回生成的随机 IP 地址
func getRandomIP(ipnet *net.IPNet, size int) net.IP {
	// 获取网络的基础 IP 地址
	ip := ipnet.IP
	var iteration int

	for iteration < maxIterations {
		iteration++
		// 获取网络掩码的位数
		ones, _ := ipnet.Mask.Size()
		// 完整字节数
		quotient := ones / 8
		// 剩余的位数
		remainder := ones % 8
		var r []byte
		// 根据 size 创建随机字节切片
		switch size {
		case 4, 16:
			r = make([]byte, size)
		default:
			return ip
		}
		// 生成随机字节
		_, _ = rand.Read(r)
		// 根据网络掩码和随机字节生成新的 IP 地址
		for i := 0; i <= quotient; i++ {
			// 对最后一个字节进行处理
			if i == quotient {
				shifted := byte(r[i]) >> remainder
				r[i] = ipnet.IP[i] + (^ipnet.IP[i] & shifted)
			} else {
				r[i] = ipnet.IP[i]
			}
		}

		ip = r
		// 如果生成的 IP 地址与基础 IP 相同，则继续生成
		if !ip.Equal(ipnet.IP) {
			break
		}
	}

	return ip
}

// GetRandomIPWithCidr: 从给定的 CIDR 列表中随机选择一个 CIDR，并生成一个随机的 IP 地址。
//
//	@param cidrs ...string: 一个或多个 CIDR 字符串
//	@return net.IP net.IP: 返回生成的随机 IP 地址
//	@return error error:可能的错误
func GetRandomIPWithCidr(cidrs ...string) (net.IP, error) {
	if len(cidrs) == 0 {
		return nil, fmt.Errorf("must specify at least one cidr")
	}
	// 随机选择一个 CIDR
	i, err := IntN(len(cidrs))
	if err != nil {
		return nil, err
	}
	cidr := cidrs[i]

	if !iputil.IsCIDR(cidr) {
		return nil, fmt.Errorf("%s is not a valid cidr", cidr)
	}

	baseIp, ipnet, err := net.ParseCIDR(cidr)
	if err != nil {
		return nil, err
	}

	switch {
	case ipnet.Mask[len(ipnet.Mask)-1] == 255:
		// 如果是一个完整的 IPv4 网络，返回基础 IP
		return baseIp, nil
	case iputil.IsIPv4(baseIp.String()):
		// 如果是 IPv4，生成随机的 IPv4 地址
		return getRandomIP(ipnet, 4), nil
	case iputil.IsIPv6(baseIp.String()):
		// 如果是 IPv6，生成随机的 IPv6 地址
		return getRandomIP(ipnet, 16), nil
	default:
		return nil, errors.New("invalid base ip")
	}
}
