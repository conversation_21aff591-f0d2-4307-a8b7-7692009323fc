// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-17 17:24:54
// FilePath: /yaml_scan/pkg/templates/log.go
// Description: 
package templates



var (
	Colorizer                       aurora.Aurora
	SeverityColorizer               func(severity.Severity) string
	deprecatedProtocolNameTemplates = mapsutil.SyncLockMap[string, bool]{Map: mapsutil.Map[string, bool]{}} //templates that still use deprecated protocol names
)

// TemplateLogMessage returns a beautified log string for a template
func TemplateLogMessage(id, name string, authors []string, templateSeverity severity.Severity) string {
	if Colorizer == nil || SeverityColorizer == nil {
		return ""
	}
	// Display the message for the template
	return fmt.Sprintf("[%s] %s (%s) [%s]",
		Colorizer.BrightBlue(id).String(),
		Colorizer.Bold(name).String(),
		Colorizer.BrightYellow(appendAtSignToAuthors(authors)).String(),
		SeverityColorizer(templateSeverity))
}

// PrintDeprecatedProtocolNameMsgIfApplicable prints a message if deprecated protocol names are used
// Unless mode is silent we print a message for deprecated protocol name
func PrintDeprecatedProtocolNameMsgIfApplicable(isSilent bool, verbose bool) {
	count := 0
	_ = deprecatedProtocolNameTemplates.Iterate(func(k string, v bool) error {
		count++
		return nil
	})
	if count > 0 && !isSilent {
		gologger.Print().Msgf("[%v] Found %v templates loaded with deprecated protocol syntax, update before v3 for continued support.\n", aurora.Yellow("WRN").String(), count)
	}
	if config.DefaultConfig.LogAllEvents {
		_ = deprecatedProtocolNameTemplates.Iterate(func(k string, v bool) error {
			gologger.Print().Msgf("  - %s\n", k)
			return nil
		})
	}
	deprecatedProtocolNameTemplates.Lock()
	deprecatedProtocolNameTemplates.Map = make(map[string]bool)
	deprecatedProtocolNameTemplates.Unlock()
}