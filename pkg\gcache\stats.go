//
// Author: chenjb
// Version: V1.0
// Date: 2025-04-29 16:45:34
// FilePath: /yaml_scan/pkg/gcache/stats.go
// Description: 统计信息

package gcache

import "sync/atomic"

// statsAccessor 接口定义了访问缓存统计信息的方法
type statsAccessor interface {
	// HitCount 返回缓存命中的次数
	HitCount() uint64
	// MissCount 返回缓存未命中的次数
	MissCount() uint64
	// LookupCount 返回缓存查找的总次数 (命中次数 + 未命中次数
	LookupCount() uint64
	// HitRate 返回缓存的命中率 (命中次数 / 总查找次数)
	HitRate() float64
}

// stats 结构体用于存储缓存的统计信息
type stats struct {
	hitCount  uint64 // 命中次数
	missCount uint64 // 未命中次数
}

// IncrHitCount: 原子地增加命中次数并返回新值
//
//	@receiver st *stats:
//	@return uint64 uint64:
func (st *stats) IncrHitCount() uint64 {
	return atomic.AddUint64(&st.hitCount, 1)
}

// IncrMissCount 原子地增加未命中次数并返回新值。
func (st *stats) IncrMissCount() uint64 {
	return atomic.AddUint64(&st.missCount, 1)
}

// HitCount 返回命中次数
func (st *stats) HitCount() uint64 {
	return atomic.LoadUint64(&st.hitCount)
}

// MissCount 返回未命中次数
func (st *stats) MissCount() uint64 {
	return atomic.LoadUint64(&st.missCount)
}

// LookupCount 返回查找次数
func (st *stats) LookupCount() uint64 {
	return st.HitCount() + st.MissCount()
}

// HitRate 返回缓存的命中率
func (st *stats) HitRate() float64 {
	hc, mc := st.HitCount(), st.MissCount()
	total := hc + mc
	if total == 0 {
		return 0.0
	}
	return float64(hc) / float64(total)
}
