//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-25 14:34:43
//FilePath: /yaml_scan/pkg/gcache/arc.go
//Description:自适应缓存替换算法 根据最近未使用和最少频率使用动态调整 结合了LFU和LRU算法

package gcache

import (
	"time"
	"yaml_scan/pkg/gcache/list"
)

// arcItem 表示 ARC 缓存中的一个项。
type arcItem[K comparable, V any] struct {
	clock      Clock      // 用于获取当前时间的时钟接口
	key        K          // 缓存项的键
	value      V          // 缓存项的值
	expiration *time.Time // 缓存项的过期时间
}

// arcList 是 ARC 缓存中用于存储键的列表结构
type arcList[K comparable] struct {
	l    *list.List[K]          // 使用双向链表实现
	keys map[K]*list.Element[K] // 存储键与链表元素的映射
}

// ARC 表示自适应替换缓存。
// t1 列表
// 作用: t1 列表用于存储最近使用的缓存项，代表高频访问的项。
// 特点:
// 该列表中的项是最常被访问的，通常是用户最需要的数据。
// 当一个项被访问时，它会被移动到 t1 的前面，表示它是最近使用的。
// t1 列表的大小是动态的，随着缓存的使用情况而变化。
// 2. t2 列表
// 作用: t2 列表用于存储较少访问的缓存项，代表低频访问的项。
// 特点:
// 该列表中的项是相对较少被访问的，但仍然是有效的缓存项。
// 当一个项从 t1 中移除时，如果它仍然有效（未过期），则会被移动到 t2。
// t2 列表的项在访问时也会被移动到前面，表示它们最近被使用。
// 3. b1 列表
// 作用: b1 列表用于存储被替换的高频项。
// 特点:
// 当 t1 中的项被替换（例如，缓存已满），如果该项在 t1 中被访问过，它会被移到 b1 列表中。
// b1 列表中的项是高频项，但由于某种原因（如缓存满），它们被移除出 t1。
// 这些项可以在未来被重新访问并移回 t1，如果它们再次被使用。
// 4. b2 列表
// 作用: b2 列表用于存储被替换的低频项。
// 特点:
// 当 t2 中的项被替换时，如果该项在 t2 中被访问过，它会被移到 b2 列表中。
// b2 列表中的项是低频项，但由于某种原因（如缓存满），它们被移除出 t2。
// 这些项在未来也可以被重新访问并移回 t2，如果它们再次被使用。

// ARC 是一个自适应替换缓存 通过在 LRU 和 LFU 之间动态平衡来优化缓存命中率
type ARC[K comparable, V any] struct {
	baseCache[K, V]                      // 基础缓存结构
	items           map[K]*arcItem[K, V] // 存储缓存项的映射

	part int         // t1 和 t2 之间的分割点，用于动态调整
	t1   *arcList[K] // 最近使用（LRU）的缓存条目列表
	t2   *arcList[K] // 经常使用（LFU）的缓存条目列表
	b1   *arcList[K] // t1 的最近驱逐条目（幽灵列表）
	b2   *arcList[K] // t2 的最近驱逐条目（幽灵列表）
}

// newARCList 创建一个新的 arcList 实例
func newARCList[K comparable]() *arcList[K] {
	return &arcList[K]{
		l:    list.New[K](), // 创建新的双向链表
		keys: make(map[K]*list.Element[K]),
	}
}

// init 初始化 ARC 缓存的 items 映射和列表
func (c *ARC[K, V]) init() {
	c.items = make(map[K]*arcItem[K, V], c.size)
	c.t1 = newARCList[K]()
	c.t2 = newARCList[K]()
	c.b1 = newARCList[K]()
	c.b2 = newARCList[K]()
}

// newARC 创建一个新的 ARC 缓存实例。
// @param cb *CacheBuilder: CacheBuilder 实例，包含缓存配置
// @return *ARC *ARC: 新创建的 ARC 缓存实例
func newARC[K comparable, V any](cb *CacheBuilder[K, V]) *ARC[K, V] {
	c := &ARC[K, V]{}
	buildCache(&c.baseCache, cb)

	c.init()
	c.loadGroup.cache = c
	return c
}

// Set 插入或更新指定的键值对
// @param key: 要设置的键
// @param value: 对应的值
// @return error: 操作失败时的错误
func (c *ARC[K, V]) set(key K, value V) (*arcItem[K, V], error) {
	var err error
	// 如果定义了序列化函数，则调用它
	if c.serializeFunc != nil {
		value, err = c.serializeFunc(key, value)
		if err != nil {
			return nil, err
		}
	}

	// 检查键是否已存在
	item, ok := c.items[key]
	if ok {
		item.value = value
	} else {
		// 创建新的 arcItem
		item = &arcItem[K, V]{
			clock: c.clock,
			key:   key,
			value: value,
		}
		c.items[key] = item
	}

	// 设置过期时间
	if c.expiration != nil {
		t := c.clock.Now().Add(*c.expiration)
		item.expiration = &t
	}

	// 添加项的回调
	defer func() {
		if c.addedFunc != nil {
			c.addedFunc(key, value)
		}
	}()

	// 如果项已经在 t1 或 t2 中，直接返回
	if c.t1.Has(key) || c.t2.Has(key) {
		return item, nil
	}

	// 如果键在 b1 中（t1 幽灵列表命中）
	if elt := c.b1.Lookup(key); elt != nil {
		// 增加 t1 分区
		c.setPart(minInt(c.size, c.part+maxInt(c.b2.Len()/c.b1.Len(), 1)))
		// 替换项
		c.replace(key)
		// 从 b1 中移除
		c.b1.Remove(key, elt)
		// 将键加入 t2（经常使用）
		c.t2.PushFront(key)
		return item, nil
	}

	// 如果键在 b2 中（t2 幽灵列表命中）
	if elt := c.b2.Lookup(key); elt != nil {
		// 减少 t1 分区
		c.setPart(maxInt(0, c.part-maxInt(c.b1.Len()/c.b2.Len(), 1)))
		// 替换项
		c.replace(key)
		// 从 b2 中移除
		c.b2.Remove(key, elt)
		// 将键加入 t2（经常使用）
		c.t2.PushFront(key)
		return item, nil
	}

	// 如果缓存已满且 t1 + b1 的总长度等于缓存大小
	if c.isCacheFull() && c.t1.Len()+c.b1.Len() == c.size {
		// 如果 t1 未满 从 b1 中移除尾部元素
		if c.t1.Len() < c.size {
			c.b1.RemoveTail()
			c.replace(key)
		} else {
			// 从 t1 中移除尾部元素
			pop := c.t1.RemoveTail()
			item, ok := c.items[pop]
			if ok {
				// 从 items 中删除
				delete(c.items, pop)
				if c.evictedFunc != nil {
					c.evictedFunc(item.key, item.value)
				}
			}
		}
	} else {
		// 检查总长度是否超过缓存大小
		total := c.t1.Len() + c.b1.Len() + c.t2.Len() + c.b2.Len()
		// 如果总长度达到或超过缓存大小
		if total >= c.size {
			if total == (2 * c.size) {
				if c.b2.Len() > 0 {
					// 如果 b2 不为空 从 b2 移除尾部
					c.b2.RemoveTail()
				} else {
					c.b1.RemoveTail()
				}
			}
			c.replace(key)
		}
	}
	// 将新项添加到 t1
	c.t1.PushFront(key)
	return item, nil
}

// Set 插入或更新指定的键值对
// @param key: 要设置的键
// @param value: 对应的值
// @return error: 操作失败时的错误
func (c *ARC[K, V]) Set(key K, value V) error {
	// 加写锁，确保并发安全
	c.mu.Lock()
	defer c.mu.Unlock()
	_, err := c.set(key, value)
	return err
}

// SetWithExpire 插入或更新指定的键值对，并设置过期时间
// @param key: 要设置的键
// @param value: 对应的值
// @param expiration: 过期时间
// @return error: 操作失败时的错误
func (c *ARC[K, V]) SetWithExpire(key K, value V, expiration time.Duration) error {
	// 加写锁，确保并发安全
	c.mu.Lock()
	defer c.mu.Unlock()
	item, err := c.set(key, value)
	if err != nil {
		return err
	}

	// 计算过期时间
	t := c.clock.Now().Add(expiration)
	item.expiration = &t
	return nil
}

// getValue 内部方法，用于获取原始值
// @param key: 要获取的键
// @param enableCount: 是否触发统计
// @return V: 键对应的值
// @return error: 操作失败时的错误
func (c *ARC[K, V]) getValue(key K, enableCount bool) (V, error) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 检查 t1 列表
	if elt := c.t1.Lookup(key); elt != nil {
		// 从 t1 中移除该项
		c.t1.Remove(key, elt)
		// 获取缓存项
		item := c.items[key]

		// 检查项是否过期
		if !item.IsExpired(nil) {
			// 将项添加到 t2
			c.t2.PushFront(key)
			// 增加命中计数
			if enableCount {
				c.stats.IncrHitCount()
			}
			// 自动延长过期时间
			c.autoLease(item)
			return item.value, nil
		} else {
			// 删除过期项
			delete(c.items, key)
			// 将项添加到 b1
			c.b1.PushFront(key)
			if c.evictedFunc != nil {
				c.evictedFunc(item.key, item.value)
			}
		}
	}
	// 检查 t2 列表
	if elt := c.t2.Lookup(key); elt != nil {
		item := c.items[key]
		if !item.IsExpired(nil) {
			c.t2.MoveToFront(elt)
			if enableCount {
				c.stats.IncrHitCount()
			}
			c.autoLease(item)
			return item.value, nil
		} else {
			delete(c.items, key)
			c.t2.Remove(key, elt)
			// 将键移到 t2 前端
			c.b2.PushFront(key)
			if c.evictedFunc != nil {
				c.evictedFunc(item.key, item.value)
			}
		}
	}

	// 增加未命中计数
	if enableCount {
		c.stats.IncrMissCount()
	}
	return c.nilV, KeyNotFoundError
}

// get 内部方法，用于获取值
// @param key: 要获取的键
// @param enableCount: 是否触发统计
// @return V: 键对应的值
// @return error: 操作失败时的错误
func (c *ARC[K, V]) get(key K, enableCount bool) (V, error) {
	v, err := c.getValue(key, enableCount)
	if err != nil {
		return c.nilV, err
	}
	if c.deserializeFunc != nil {
		return c.deserializeFunc(key, v)
	}
	return v, nil
}

// getWithLoader  使用 LoaderFunc 加载值
// @param key: 要加载的键
// @param isWait: 是否等待加载完成
// @return V: 加载的值
// @return error: 加载过程中的错误。
func (c *ARC[K, V]) getWithLoader(key K, isWait bool) (V, error) {
	// 如果没有定义加载函数，返回未找到错误
	if c.loaderExpireFunc == nil {
		return c.nilV, KeyNotFoundError
	}
	// 调用 load 方法，尝试加载值
	value, _, err := c.load(key, func(v V, expiration *time.Duration, e error) (V, error) {
		if e != nil {
			return c.nilV, e
		}
		c.mu.Lock()
		defer c.mu.Unlock()
		// 将加载的值设置到缓存中
		item, err := c.set(key, v)
		if err != nil {
			return c.nilV, err
		}
		// 如果有过期时间，设置过期时间
		if expiration != nil {
			t := c.clock.Now().Add(*expiration)
			item.expiration = &t
		}
		return v, nil
	}, isWait)
	if err != nil {
		return c.nilV, err
	}
	return value, nil
}

// Get 从缓存中获取键的值，如果未找到则通过加载器加载。
// @param key: 要获取的键
// @return  V: 键对应的值
// @return error: 操作失败时的错误
func (c *ARC[K, V]) Get(key K) (V, error) {
	v, err := c.get(key, false)
	if err == KeyNotFoundError {
		return c.getWithLoader(key, true)
	}
	return v, err
}

// GetIFPresent  从缓存中获取指定键的值，不等待函数加载完成 异步调用
// @param  key: 要获取的键
// @return  V: 键对应的值
// @return error: 操作失败时的错误
func (c *ARC[K, V]) GetIFPresent(key K) (V, error) {
	v, err := c.get(key, false)
	if err == KeyNotFoundError {
		return c.getWithLoader(key, false)
	}
	return v, err
}

// GetALL 返回缓存中的所有键值对。
// @param checkExpired: 是否检查过期条目
// @return map[K]V: 缓存中的键值对映射
func (c *ARC[K, V]) GetALL(checkExpired bool) map[K]V {
	c.mu.RLock()
	defer c.mu.RUnlock()
	// 创建一个新的映射以存储结果
	items := make(map[K]V, len(c.items))
	now := time.Now()
	for k, item := range c.items {
		// 检查是否需要检查过期
		if !checkExpired || c.has(k, &now) {
			items[k] = item.value
		}
	}
	return items
}

// has 检查缓存中是否存在指定键的有效项。
// @param key: 要检查的键
// @param now: 当前时间
// @return bool: 如果键存在且未过期，返回 true
func (c *ARC[K, V]) has(key K, now *time.Time) bool {
	item, ok := c.items[key]
	if !ok {
		return false
	}
	return !item.IsExpired(now)
}

// Has 检查缓存中是否存在指定键的有效项。
// @param key: 要检查的键
// @return bool: 如果键存在且未过期，返回 true
func (c *ARC[K, V]) Has(key K) bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	now := time.Now()
	return c.has(key, &now)
}

// remove 内部方法，删除指定的键
// @param key: 要删除的键
// @return bool: 如果键存在并被删除，返回 true。
func (c *ARC[K, V]) remove(key K) bool {
	// 检查 t1 列表中是否存在该键
	if elt := c.t1.Lookup(key); elt != nil {
		// 从 t1 中移除
		c.t1.Remove(key, elt)
		// 获取对应的缓存项
		item := c.items[key]
		// 从 items 中删除
		delete(c.items, key)
		// 将键推入 b1 列表
		c.b1.PushFront(key)
		if c.evictedFunc != nil {
			c.evictedFunc(key, item.value)
		}
		return true
	}

	// 检查 t2 列表中是否存在该键
	if elt := c.t2.Lookup(key); elt != nil {
		// 从 t2 中移除
		c.t2.Remove(key, elt)
		// 获取对应的缓存项
		item := c.items[key]
		delete(c.items, key)
		// 将键推入 b2 列表
		c.b2.PushFront(key)
		if c.evictedFunc != nil {
			c.evictedFunc(key, item.value)
		}
		return true
	}

	return false
}


// Remove 删除指定的键
// @param key: 要删除的键
// @return bool: 如果键存在并被删除，返回 true
func (c *ARC[K, V]) Remove(key K) bool {
	c.mu.Lock()
	defer c.mu.Unlock()

	return c.remove(key)
}

// Keys 返回缓存中的所有键
func (c *ARC[K, V]) Keys(checkExpired bool) []K {
	c.mu.RLock()
	defer c.mu.RUnlock()
	// 创建一个切片以存储结果
	keys := make([]K, 0, len(c.items))
	now := time.Now()
	for k := range c.items {
		// 检查是否需要检查过期
		if !checkExpired || c.has(k, &now) {
			keys = append(keys, k)
		}
	}
	return keys
}

// Len 返回缓存中项的数量
// @param checkExpired: 是否检查过期条目
// @return int: 缓存中的项数
func (c *ARC[K, V]) Len(checkExpired bool) int {
	c.mu.RLock()
	defer c.mu.RUnlock()
	// 如果不检查过期，直接返回项的数量
	if !checkExpired {
		return len(c.items)
	}
	var length int
	now := time.Now()
	for k := range c.items {
		if c.has(k, &now) {
			length++
		}
	}
	return length
}

// Purge 用于完全清空缓存
func (c *ARC[K, V]) Purge() {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 如果定义了清除回调函数，则遍历所有项并调用该函数
	if c.purgeVisitorFunc != nil {
		for _, item := range c.items {
			c.purgeVisitorFunc(item.key, item.value)
		}
	}

	c.init()
}

// isCacheFull 检查缓存是否已满。
func (c *ARC[K, V]) isCacheFull() bool {
	// 检查 t1 和 t2 的总长度是否等于缓存大小
	return (c.t1.Len() + c.t2.Len()) == c.size
}

// setPart 设置 t1 和 t2 的分配部分
// @param p: 分割点
func (c *ARC[K, V]) setPart(p int) {
	// 检查缓存是否已满
	if c.isCacheFull() {
		c.part = p
	}
}

// replace  替换缓存中的条目，用于在缓存满时驱逐条目
// @param key: 当前操作的键
func (c *ARC[K, V]) replace(key K) {
	// 检查缓存是否已满
	if !c.isCacheFull() {
		return
	}
	// 被驱逐的键
	var old K
	// 判断是否从 t1 中替换
	// 如果 t1 不为空，且 (b2 包含 key 且 t1 长度等于 part) 或 t1 长度大于 part
	if c.t1.Len() > 0 && ((c.b2.Has(key) && c.t1.Len() == c.part) || (c.t1.Len() > c.part)) {
		// 从 t1 中移除尾部元素
		old = c.t1.RemoveTail()
		// 将移除的键加入 b1（t1 幽灵列表）
		c.b1.PushFront(old)
	} else if c.t2.Len() > 0 { // 如果 t2 不为空
		// 从 t2 中移除尾部元素
		old = c.t2.RemoveTail()
		// 将移除的键加入 b2（t2 幽灵列表）
		c.b2.PushFront(old)
	} else {
		// 从 t1 中移除尾部元素
		old = c.t1.RemoveTail()
		// 将移除的键加入 b1
		c.b1.PushFront(old)
	}

	// 从 items 中删除旧项
	item, ok := c.items[old]
	if ok {
		delete(c.items, old)
		if c.evictedFunc != nil {
			c.evictedFunc(item.key, item.value)
		}
	}
}

// autoLease 自动延长缓存项的过期时间。
func (c *ARC[K, V]) autoLease(item *arcItem[K, V]) {
	if item.expiration == nil {
		return
	}
	if c.lease == nil {
		return
	}
	t := item.clock.Now().Add(*c.lease)
	item.expiration = &t
}

// IsExpired 返回该项是否已过期的布尔值。
func (it *arcItem[K, V]) IsExpired(now *time.Time) bool {
	// 如果没有设置过期时间，则认为未过期
	if it.expiration == nil {
		return false
	}
	if now == nil {
		t := it.clock.Now()
		now = &t
	}
	return it.expiration.Before(*now)
}

// Has 检查 arcList 中是否存在指定的键。
func (al *arcList[K]) Has(key K) bool {
	_, ok := al.keys[key]
	return ok
}

// Lookup 查找指定键对应的链表元素。
func (al *arcList[K]) Lookup(key K) *list.Element[K] {
	elt := al.keys[key]
	return elt
}

// Len 返回 arcList 中的元素数量。
func (al *arcList[K]) Len() int {
	return al.l.Len()
}

// MoveToFront 将指定的链表元素移动到 arcList 的前面。
func (al *arcList[K]) MoveToFront(elt *list.Element[K]) {
	al.l.MoveToFront(elt)
}

// PushFront 将指定的键添加到 arcList 的前面。
// 如果键已存在，则将其移动到前面。
func (al *arcList[K]) PushFront(key K) {
	// 检查键是否已存在
	if elt, ok := al.keys[key]; ok {
		al.l.MoveToFront(elt)
		return
	}
	// 如果不存在，添加新元素到前面
	elt := al.l.PushFront(key)
	al.keys[key] = elt
}

// Remove 从 arcList 中删除指定的键及其对应的链表元素。
func (al *arcList[K]) Remove(key K, elt *list.Element[K]) {
	delete(al.keys, key)
	al.l.Remove(elt)
}

// RemoveTail 从 arcList 中删除尾部元素，并返回该元素的键。
func (al *arcList[K]) RemoveTail() K {
	// 获取链表的尾部元素
	elt := al.l.Back()
	al.l.Remove(elt)

	// 获取尾部元素的键
	key := elt.Value
	delete(al.keys, key)

	return key
}

// maxInt: 返回两个数中的最大数
//
//	@param x int:
//	@param y int:
//	@return int int:
func maxInt(x, y int) int {
	if x > y {
		return x
	}
	return y
}

// minInt: 返回两个数中的最小数
//
//	@param x int:
//	@param y int:
//	@return int int:
func minInt(x, y int) int {
	if x < y {
		return x
	}
	return y
}
