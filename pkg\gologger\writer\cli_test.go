package writer


import (
	"testing"
	"os"
	"io"

	"yaml_scan/pkg/gologger/levels"
)


func TestWrite(t *testing.T) {
	// 创建两个管道，分别用于捕获标准输出和标准错误。
	stdoutR, stdoutW, err := os.Pipe()
	if err != nil {
		t.Fatalf("Failed to create stdout pipe: %v", err)
	}
	stderrR, stderrW, err := os.Pipe()
	if err != nil {
		t.Fatalf("Failed to create stderr pipe: %v", err)
	}

	// 备份原始的 os.Stdout 和 os.Stderr，然后将它们重定向到我们的缓冲区，以便捕获输出
	originalStdout := os.Stdout
	originalStderr := os.Stderr

	defer func() {
		os.Stdout = originalStdout
		os.Stderr = originalStderr
	}()
	os.Stdout = stdoutW
	os.Stderr = stderrW

	// 创建 CLI 实例
	logger := NewCLI()

	// 测试 LevelSilent
	message := []byte("This is a silent message")
	logger.Write(message, levels.LevelSilent)
	
	
	stdoutBuf := make([]byte, 1024)
	n, err := stdoutR.Read(stdoutBuf)
	if err != nil && err != io.EOF {
		t.Fatalf("Failed to read from stdout pipe: %v", err)
	}

	// Check if the output is correct
	if string(stdoutBuf[:n]) != "This is a silent message\n" {
		t.Errorf("Expected stdout to be 'This is a silent message\\n', got '%s'", string(stdoutBuf[:n]))
	}


	// 测试其他日志级别
	logger.Write(message, levels.LevelError)
	

	stderrBuf := make([]byte, 1024)
	n, err = stderrR.Read(stderrBuf)
	if err != nil && err != io.EOF {
		t.Fatalf("Failed to read from stderr pipe: %v", err)
	}

	// Check if the output is correct
	if string(stderrBuf[:n]) != "This is a silent message\n" {
		t.Errorf("Expected stderr to be 'This is a silent message\\n', got '%s'", string(stderrBuf[:n]))
	}

	stderrW.Close()
	stderrR.Close()
	stdoutR.Close()


}