//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-16 20:15:54
//FilePath: /yaml_scan/pkg/retryabledns/retryabledns.go
//Description:
package retryabledns

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"net"
	"net/url"
	"sync"
	"sync/atomic"
	"time"
	"yaml_scan/pkg/retryabledns/doh"
	"yaml_scan/pkg/retryabledns/hostsfile"
	iputil "yaml_scan/utils/ip"
	mapsutil "yaml_scan/utils/maps"
	sliceutil "yaml_scan/utils/slice"

	"github.com/miekg/dns"
	"golang.org/x/net/proxy"
)

// Client 是一个 DNS 解析器客户端，用于解析主机名。
type Client struct {
	resolvers    []Resolver                              // 解析器列表
	options      Options                                 // 客户端选项
	serversIndex uint32                                  // 当前服务器索引
	TCPFallback  bool                                    // 是否启用 TCP 回退
	udpClient    *dns.Client                             // UDP 客户端
	udpConnPool  mapsutil.SyncLockMap[string, *ConnPool] // UDP 连接池
	tcpClient    *dns.Client                             // TCP 客户端
	dohClient    *doh.Client                             // DoH 客户端
	dotClient    *dns.Client                             // DoT 客户端
	udpProxy     proxy.Dialer                            // UDP 代理
	tcpProxy     proxy.Dialer                            // TCP 代理
	dotProxy     proxy.Dialer                            // DoT 代理
	knownHosts   map[string][]string                     // 主机映射
}

// ErrRetriesExceeded 超过最大重试次数时返回的错误
var ErrRetriesExceeded = errors.New("could not resolve, max retries exceeded")


func init() {
	var err error
	internalRangeCheckerInstance, err = newInternalRangeChecker()
	if err != nil {
		fmt.Printf("could not initialize range checker: %s\n", err)
	}
}

// New: 创建一个DNS客户端
//
//	@param baseResolvers []string: 基础解析器列表，例如 ["*******", "*******"]，指定要使用的 DNS 服务器地址
//	@param maxRetries int: 最大重试次数，定义查询失败时客户端的重试次数
//	@return *Client *Client: 新创建的 DNS 客户端实例
//	@return error error: 如果创建过程中出现问题，返回错误
func New(baseResolvers []string, maxRetries int) (*Client, error) {
	return NewWithOptions(Options{BaseResolvers: baseResolvers, MaxRetries: maxRetries})
}

// NewWithOptions: 创建一个新的 DNS 客户端实例，并根据提供的选项进行配置。
//
//	@param options Options: 客户端的配置选项，包含解析器列表、超时、代理等设置。
//	@return *Client *Client: 配置好的 DNS 客户端实例。
//	@return error error: 如果配置失败，返回具体的错误信息；否则返回 nil。
func NewWithOptions(options Options) (*Client, error) {
	// 验证传入的选项是否有效
	if err := options.Validate(); err != nil {
		return nil, err
	}
	// 解析并去重基础解析器列表
	parsedBaseResolvers := parseResolvers(sliceutil.Dedupe(options.BaseResolvers))

	// 如果启用了 Hostsfile 选项，解析默认的 hosts 文件
	var knownHosts map[string][]string
	if options.Hostsfile {
		knownHosts, _ = hostsfile.ParseDefault()
	}

	// 如果未设置CNMAE最大默认跟踪次数，则使用默认值
	if options.MaxPerCNAMEFollows == 0 {
		options.MaxPerCNAMEFollows = DefaultMaxPerCNAMEFollows
	}

	// 创建 DoH 客户端的 HTTP 客户端，并应用配置选项
	httpClient := doh.NewHttpClient(
		doh.WithTimeout(options.Timeout),
		doh.WithInsecureSkipVerify(),
		doh.WithProxy(options.Proxy), // 设置代理（如果提供则生效，否则无操作）
	)

	// 如果指定了代理，则强制所有解析器使用 TCP 协议
	if options.Proxy != "" {
		for i, resolver := range parsedBaseResolvers {
			if networkResolver, ok := resolver.(*NetworkResolver); ok && networkResolver.Protocol == UDP {
				// 将 UDP 解析器转换为 TCP 解析器
				parsedBaseResolvers[i] = &NetworkResolver{
					Protocol: TCP,
					Host:     networkResolver.Host,
					Port:     networkResolver.Port,
				}
			}
		}
	}

	// 创建用于 UDP、TCP 和 DoT 的拨号器，设置本地地址
	udpDialer := &net.Dialer{LocalAddr: options.GetLocalAddr(UDP)}
	tcpDialer := &net.Dialer{LocalAddr: options.GetLocalAddr(TCP)}
	dotDialer := &net.Dialer{LocalAddr: options.GetLocalAddr(TCP)}

	// 创建 UDP 客户端
	udpClient := &dns.Client{
		Net:     "", // 留空以使用默认网络
		Timeout: options.Timeout,
		Dialer:  udpDialer,
	}

	// 创建 TCP 客户端
	tcpClient := &dns.Client{
		Net:     TCP.String(),
		Timeout: options.Timeout,
		Dialer:  tcpDialer,
	}

	// 创建 DoH 客户端
	dohClient := doh.NewWithOptions(
		doh.Options{
			HttpClient: httpClient,
		},
	)
	// 创建 DoT 客户端
	dotClient := &dns.Client{
		Net:     "tcp-tls",
		Timeout: options.Timeout,
		Dialer:  dotDialer,
	}

	// 初始化 Client 结构体
	client := Client{
		options:    options,
		resolvers:  parsedBaseResolvers,
		udpClient:  udpClient,
		tcpClient:  tcpClient,
		dohClient:  dohClient,
		dotClient:  dotClient,
		knownHosts: knownHosts,
	}

	// 如果指定了代理，创建代理拨号器
	if options.Proxy != "" {
		proxyURL, err := url.Parse(options.Proxy)
		if err != nil {
			return nil, fmt.Errorf("invalid proxy URL: %v", err)
		}
		// 为 UDP 创建代理拨号器
		proxyDialer, err := proxy.FromURL(proxyURL, udpDialer)
		if err != nil {
			return nil, fmt.Errorf("error creating proxy dialer: %v", err)
		}
		// 为 TCP 创建代理拨号器
		tcpProxyDialer, err := proxy.FromURL(proxyURL, tcpDialer)
		if err != nil {
			return nil, fmt.Errorf("error creating proxy dialer: %v", err)
		}
		// 为 DoT 创建代理拨号器
		dotProxyDialer, err := proxy.FromURL(proxyURL, dotDialer)
		if err != nil {
			return nil, fmt.Errorf("error creating proxy dialer: %v", err)
		}

		// 设置代理拨号器到客户端实例
		client.udpProxy = proxyDialer
		client.tcpProxy = tcpProxyDialer
		client.dotProxy = dotProxyDialer
	}

	// 如果连接池线程数大于 1，创建 UDP 连接池
	if options.ConnectionPoolThreads > 1 {
		client.udpConnPool = mapsutil.SyncLockMap[string, *ConnPool]{
			Map: make(mapsutil.Map[string, *ConnPool]),
		}
		for _, resolver := range client.resolvers {
			// 解析 resolver 的主机和端口
			resolverHost, resolverPort, err := net.SplitHostPort(resolver.String())
			if err != nil {
				return nil, err
			}
			// 创建 NetworkResolver 实例用于连接池
			networkResolver := NetworkResolver{
				Protocol: UDP,
				Port:     resolverPort,
				Host:     resolverHost,
			}
			// 为每个 resolver 创建连接池
			udpConnPool, err := NewConnPool(networkResolver, options.ConnectionPoolThreads)
			if err != nil {
				return nil, err
			}
			// 将连接池存储到客户端的连接池映射中
			_ = client.udpConnPool.Set(resolver.String(), udpConnPool)
		}
	}
	return &client, nil
}

// ResolveWithSyscall: 通过系统调用尝试解析主机名
//
//	@receiver c *Client:
//	@param host string: 要解析的主机名
//	@return *DNSData *DNSData: 包含解析出的IP地址的DNS数据
//	@return error error: 如果解析失败，返回错误
func (c *Client) ResolveWithSyscall(host string) (*DNSData, error) {
	// 使用系统解析器查找给定主机的IP地址
	ips, err := net.LookupIP(host)
	if err != nil {
		return nil, err
	}

	// 初始化DNSData结构体，用于存储解析结果
	var d DNSData
	d.Host = host
	// 遍历解析出的IP地址，将其分类为IPv4或IPv6
	for _, ip := range ips {
		if ipv4 := ip.To4(); ipv4 != nil {
			// 如果是IPv4地址，添加到A记录中
			d.A = append(d.A, ip.String())
		} else if ipv6 := ip.To16(); ipv6 != nil {
			// 如果是IPv6地址，添加到AAAA记录中
			d.AAAA = append(d.AAAA, ip.String())
		}
	}

	return &d, nil
}

// dialWithProxy: 使用代理拨号器拨号到指定的网络和地址，返回 DNS 连接。
//
//	@receiver c *Client:
//	@param dialer proxy.Dialer: 代理拨号器，用于创建网络连接。
//	@param network string: 网络类型，例如 "tcp" 或 "udp"。
//	@param addr string:  目标地址，例如 "*******:53"。
//	@return *dns.Conn *dns.Conn: DNS 连接对象，用于后续 DNS 查询。
//	@return error error: 如果拨号失败，返回错误信息。
func (c *Client) dialWithProxy(dialer proxy.Dialer, network, addr string) (*dns.Conn, error) {
	conn, err := dialer.Dial(network, addr)
	if err != nil {
		return nil, err
	}
	return &dns.Conn{Conn: conn}, nil
}

// queryMultiple:  执行多个 DNS 查询类型，支持重试和代理。
//
//	@receiver c *Client:
//	@param host string: 要查询的主机名，例如 "example.com"。
//	@param requestTypes []uint16: DNS 查询类型列表，例如 dns.TypeA, dns.TypeAAAA。
//	@param resolver Resolver:指定的解析器，如果为 nil，则使用客户端的解析器列表。
//	@return *DNSData *DNSData: 查询结果，包含 DNS 记录（如 A、AAAA 等）。
//	@return error error: 如果查询失败，返回错误信息。
func (c *Client) queryMultiple(host string, requestTypes []uint16, resolver Resolver) (*DNSData, error) {
	var (
		hasResolver bool    = resolver != nil // 判断是否指定了解析器
		dnsdata     DNSData                   // 初始化 DNS 查询结果
		err         error
	)

	// 如果启用 hosts 文件支持，检查已知主机并填充结果
	if c.options.Hostsfile {
		if ips, ok := c.knownHosts[host]; ok {
			for _, ip := range ips {
				if iputil.IsIPv4(ip) {
					// 添加 IPv4 地址
					dnsdata.A = append(dnsdata.A, ip)
				} else if iputil.IsIPv6(ip) {
					// 添加 IPv6 地址
					dnsdata.AAAA = append(dnsdata.AAAA, ip)
				}
			}
		}
		if len(dnsdata.AAAA)+len(dnsdata.A) > 0 {
			dnsdata.HostsFile = true // 标记结果来自 hosts 文件
		}
	}

	// 创建 DNS 查询消息
	msg := &dns.Msg{}
	// 设置唯一的消息 ID
	msg.Id = dns.Id()
	// 配置 EDNS0，缓冲区大小为 4096 字节
	msg.SetEdns0(4096, false)

	// 遍历所有请求类型，逐一执行查询
	for _, requestType := range requestTypes {
		// 转换为完全限定域名 (FQDN)
		name := dns.Fqdn(host)
		// 初始化查询问题
		msg.Question = make([]dns.Question, 1)

		switch requestType {
		case dns.TypeAXFR:
			// 处理 AXFR 类型查询
			msg.SetAxfr(name)
		case dns.TypePTR:
			// 处理 PTR 类型查询，调整域名
			var err error
			if net.ParseIP(host) != nil {
				// 将 IP 转换为反向查询域名
				name, err = dns.ReverseAddr(host)
				if err != nil {
					return nil, err
				}
			}
			fallthrough
		default:
			// 启用递归查询
			msg.RecursionDesired = true
			question := dns.Question{
				Name:   name,
				Qtype:  requestType,
				Qclass: dns.ClassINET,
			}
			// 设置查询问题
			msg.Question[0] = question
		}

		var (
			resp   *dns.Msg           // DNS 响应消息
			trResp chan *dns.Envelope // AXFR 传输通道
			i      int                // 重试计数器
		)

		// 执行重试循环，最多重试 MaxRetries 次
		for i = 0; i < c.options.MaxRetries; i++ {
			// 轮询解析器索引
			index := atomic.AddUint32(&c.serversIndex, 1)
			if !hasResolver {
				// 选择解析器
				resolver = c.resolvers[index%uint32(len(c.resolvers))]
			}

			switch r := resolver.(type) {
			case *NetworkResolver:
				// 处理网络解析器
				if requestType == dns.TypeAXFR {
					var dnsconn *dns.Conn
					switch r.Protocol {
					case TCP:
						dnsconn, err = c.tcpClient.Dial(resolver.String())
					case UDP:
						dnsconn, err = c.udpClient.Dial(resolver.String())
					case DOT:
						dnsconn, err = c.dotClient.Dial(resolver.String())
					default:
						dnsconn, err = c.tcpClient.Dial(resolver.String())
					}
					if err != nil {
						break
					}
					defer dnsconn.Close()
					dnsTransfer := &dns.Transfer{Conn: dnsconn}
					trResp, err = dnsTransfer.In(msg, resolver.String())
				} else {
					switch r.Protocol {
					case TCP:
						if c.tcpProxy != nil {
							var tcpConn *dns.Conn
							tcpConn, err = c.dialWithProxy(c.tcpProxy, "tcp", resolver.String())
							if err != nil {
								break
							}
							defer tcpConn.Close()
							resp, _, err = c.tcpClient.ExchangeWithConn(msg, tcpConn)
						} else {
							resp, _, err = c.tcpClient.Exchange(msg, resolver.String())
						}
					case UDP:
						if c.options.ConnectionPoolThreads > 1 {
							if udpConnPool, ok := c.udpConnPool.Get(resolver.String()); ok {
								resp, _, err = udpConnPool.Exchange(context.TODO(), c.udpClient, msg)
							}
						} else {
							resp, _, err = c.udpClient.Exchange(msg, resolver.String())
						}
					case DOT:
						resp, _, err = c.dotClient.Exchange(msg, resolver.String())
					}
				}
			case *DohResolver:
				// 处理 DoH 解析器
				method := doh.MethodPost
				if r.Protocol == GET {
					method = doh.MethodGet
				}
				resp, err = c.dohClient.QueryWithDOHMsg(method, doh.Resolver{URL: r.URL}, msg)
			}

			// 如果查询失败，继续重试
			if err != nil || (trResp == nil && resp == nil) {
				continue
			}

			// 处理截断响应，使用 TCP 重新查询
			if resp != nil && resp.Truncated && c.TCPFallback {
				resp, _, err = c.tcpClient.Exchange(msg, resolver.String())
				if err != nil || resp == nil {
					continue
				}
			}

			// 解析响应数据
			switch requestType {
			case dns.TypeAXFR:
				err = dnsdata.ParseFromEnvelopeChan(trResp)
			default:
				err = dnsdata.ParseFromMsg(resp)
			}

			// 保留原始响应数据
			dnsdata.RawResp = resp

			// 填充基本信息
			dnsdata.Host = host
			switch {
			case resp != nil:
				dnsdata.StatusCode = dns.RcodeToString[resp.Rcode]
				dnsdata.StatusCodeRaw = resp.Rcode
				dnsdata.Raw += resp.String()
			case trResp != nil:
				// pass
			}
			dnsdata.Timestamp = time.Now()
			dnsdata.Resolver = append(dnsdata.Resolver, resolver.String())

			// 如果解析失败或无数据，继续重试
			if err != nil || !dnsdata.contains() {
				continue
			}
			// 去重结果
			dnsdata.dedupe()

			// 如果查询成功，退出重试循环
			if resp != nil && resp.Rcode == dns.RcodeSuccess {
				break
			}
			if trResp != nil {
				break
			}
		}
		// 重试次数耗尽，返回错误
		if i == c.options.MaxRetries && err != nil {
			err = errors.Join(ErrRetriesExceeded, err)
			break
		}
	}

	return &dnsdata, err
}

// QueryMultiple:执行多个 DNS 查询类型，使用客户端的解析器列表。
//
//	@receiver c *Client:
//	@param host string: 要查询的主机名，例如 "example.com"。
//	@param requestTypes []uint16: DNS 查询类型列表，例如 dns.TypeA, dns.TypeAAAA。
//	@return *DNSData *DNSData: 查询结果，包含 DNS 记录（如 A、AAAA 等）。
//	@return error error:
func (c *Client) QueryMultiple(host string, requestTypes []uint16) (*DNSData, error) {
	return c.queryMultiple(host, requestTypes, nil)
}

// Resolve: 解析主机名，执行 A 和 AAAA 类型的查询。
//
//	@receiver c *Client:
//	@param host string: 要解析的主机名，例如 "example.com"。
//	@return *DNSData *DNSData: 查询结果，包含 A 和 AAAA 记录。
//	@return error error: 如果查询失败，返回错误信息。
func (c *Client) Resolve(host string) (*DNSData, error) {
	return c.QueryMultiple(host, []uint16{dns.TypeA, dns.TypeAAAA})
}

// Do:发送一个提供的 DNS 请求并返回原始的本地响应。
//
//	@receiver c *Client:
//	@param msg *dns.Msg:要发送的 DNS 查询消息。
//	@return *dns.Msg *dns.Msg:  返回的 DNS 响应消息。
//	@return error error:如果请求失败或重试次数耗尽，返回的错误。
func (c *Client) Do(msg *dns.Msg) (*dns.Msg, error) {
	// 存储 DNS 响应
	var resp *dns.Msg
	var err error

	// 根据配置的最大重试次数进行循环尝试
	for i := 0; i < c.options.MaxRetries; i++ {
		// 使用原子操作递增服务器索引，确保线程安全
		index := atomic.AddUint32(&c.serversIndex, 1)
		// 从解析器列表中轮询选择一个解析器
		resolver := c.resolvers[index%uint32(len(c.resolvers))]

		// 处理不同的处理器类型
		switch r := resolver.(type) {
		case *NetworkResolver:
			switch r.Protocol {
			case TCP:
				// 如果配置了 TCP 代理，使用代理拨号建立连接
				if c.tcpProxy != nil {
					var tcpConn *dns.Conn
					tcpConn, err = c.dialWithProxy(c.tcpProxy, "tcp", resolver.String())
					if err != nil {
						break
					}
					defer tcpConn.Close()
					resp, _, err = c.tcpClient.ExchangeWithConn(msg, tcpConn)
				} else {
					// 无代理时，直接使用 TCP 客户端发送查询
					resp, _, err = c.tcpClient.Exchange(msg, resolver.String())
				}
			case UDP:
				// 如果启用了连接池，从池中获取连接并发送查询
				if c.options.ConnectionPoolThreads > 1 {
					if udpConnPool, ok := c.udpConnPool.Get(resolver.String()); ok {
						resp, _, err = udpConnPool.Exchange(context.TODO(), c.udpClient, msg)
					}
				} else if c.udpProxy != nil {
					// 如果配置了 UDP 代理，使用代理拨号建立连接
					var udpConn *dns.Conn
					udpConn, err = c.dialWithProxy(c.udpProxy, "udp", resolver.String())
					if err != nil {
						break
					}
					defer udpConn.Close()
					resp, _, err = c.udpClient.ExchangeWithConn(msg, udpConn)
				} else {
					resp, _, err = c.udpClient.Exchange(msg, resolver.String())
				}
			case DOT:
				// 使用 DNS over TLS (DOT) 客户端发送查询
				resp, _, err = c.dotClient.Exchange(msg, resolver.String())
			}
		case *DohResolver:
			// 处理 DNS over HTTPS (DoH) 请求
			method := doh.MethodPost
			if r.Protocol == GET {
				method = doh.MethodGet
			}
			// 使用 DoH 客户端发送查询
			resp, err = c.dohClient.QueryWithDOHMsg(method, doh.Resolver{URL: r.URL}, msg)
		}

		// 如果查询失败或响应为空，继续下一次重试
		if err != nil || resp == nil {
			continue
		}

		// 如果响应状态码不是成功，继续下一次重试
		if resp.Rcode != dns.RcodeSuccess {
			continue
		}

		// 如果收到非空响应且状态码为成功，停止重试并返回结果
		return resp, nil
	}
	return resp, ErrRetriesExceeded
}

// Query: 发送一个提供的 DNS 请求并返回增强的响应。
//
//	@receiver c *Client:
//	@param host string: 要查询的主机名，例如 "example.com"。
//	@param requestType uint16: DNS 查询类型，例如 dns.TypeA 表示 IPv4 地址查询。
//	@return *DNSData *DNSData: 查询结果，包含 DNS 记录的详细信息。
//	@return error error:如果查询失败，返回具体的错误信息。
func (c *Client) Query(host string, requestType uint16) (*DNSData, error) {
	return c.QueryMultiple(host, []uint16{requestType})
}

// A: 用于执行 A 记录查询。
//
//	@receiver c *Client:
//	@param host string: 要查询的主机名，例如 "example.com"。
//	@return *DNSData *DNSData:查询结果，包含 A 记录（IPv4 地址）的详细信息。
//	@return error error:如果查询失败，返回具体的错误信息。
func (c *Client) A(host string) (*DNSData, error) {
	return c.QueryMultiple(host, []uint16{dns.TypeA})
}

// AAAA: 用于执行 AAAA 记录查询
//
//	@receiver c *Client:
//	@param host string: 要查询的主机名，例如 "example.com"。
//	@return *DNSData *DNSData: 查询结果，包含 AAAA 记录（IPv6 地址）的详细信息。
//	@return error error: 如果查询失败，返回具体的错误信息。
func (c *Client) AAAA(host string) (*DNSData, error) {
	return c.QueryMultiple(host, []uint16{dns.TypeAAAA})
}

// MX: 用于执行 MX 记录查询。
//
//	@receiver c *Client:
//	@param host string: 要查询的主机名，例如 "example.com"。
//	@return *DNSData *DNSData: 查询结果，包含 MX 记录的详细信息。
//	@return error error: 如果查询失败，返回具体的错误信息。
func (c *Client) MX(host string) (*DNSData, error) {
	return c.QueryMultiple(host, []uint16{dns.TypeMX})
}

// CNAME: 用于执行 CNAME 记录查询。
//
//	@receiver c *Client:
//	@param host string: 要查询的主机名，例如 "example.com"。
//	@return *DNSData *DNSData: 查询结果，包含 CNAME 记录的详细信息。
//	@return error error: 如果查询失败，返回具体的错误信息
func (c *Client) CNAME(host string) (*DNSData, error) {
	return c.QueryMultiple(host, []uint16{dns.TypeCNAME})
}

// SOA: 用于执行 SOA 记录查询。
//
//	@receiver c *Client:
//	@param host string: 要查询的主机名，例如 "example.com"。
//	@return *DNSData *DNSData: 查询结果，包含 SOA 记录的详细信息。
//	@return error error: 如果查询失败，返回具体的错误信息
func (c *Client) SOA(host string) (*DNSData, error) {
	return c.QueryMultiple(host, []uint16{dns.TypeSOA})
}

// TXT: 用于执行 TXT 记录查询。
//
//	@receiver c *Client:
//	@param host string: 要查询的主机名，例如 "example.com"。
//	@return *DNSData *DNSData: 查询结果，包含 TXT 记录的详细信息。
//	@return error error: 如果查询失败，返回具体的错误信息
func (c *Client) TXT(host string) (*DNSData, error) {
	return c.QueryMultiple(host, []uint16{dns.TypeTXT})
}

// SRV: 用于执行 SRV 记录查询。
//
//	@receiver c *Client:
//	@param host string: 要查询的主机名，例如 "example.com"。
//	@return *DNSData *DNSData: 查询结果，包含 SRV 记录的详细信息。
//	@return error error: 如果查询失败，返回具体的错误信息
func (c *Client) SRV(host string) (*DNSData, error) {
	return c.QueryMultiple(host, []uint16{dns.TypeSRV})
}

// PTR: 用于执行 PTR 记录查询。
//
//	@receiver c *Client:
//	@param host string:  要查询的 IP 地址，例如 "*************"。
//	@return *DNSData *DNSData: 查询结果，包含 PTR 记录的详细信息。
//	@return error error: 如果查询失败，返回具体的错误信息
func (c *Client) PTR(host string) (*DNSData, error) {
	return c.QueryMultiple(host, []uint16{dns.TypePTR})
}

// ANY: 用于执行 ANY 记录查询
//
//	@receiver c *Client:
//	@param host string: 要查询的主机名，例如 "example.com"。
//	@return *DNSData *DNSData: 查询结果，包含 ANY 记录（所有类型记录）的详细信息。
//	@return error error: 如果查询失败，返回具体的错误信息
func (c *Client) ANY(host string) (*DNSData, error) {
	return c.QueryMultiple(host, []uint16{dns.TypeANY})
}

// NS: 用于执行 NS 记录查询
//
//	@receiver c *Client:
//	@param host string: 要查询的主机名，例如 "example.com"。
//	@return *DNSData *DNSData: 查询结果，包含 NS 记录的详细信息。
//	@return error error: 如果查询失败，返回具体的错误信息
func (c *Client) NS(host string) (*DNSData, error) {
	return c.QueryMultiple(host, []uint16{dns.TypeNS})
}

// QueryMultipleWithResolver: 使用指定的解析器发送多个 DNS 请求并返回数据。
//
//	@receiver c *Client:
//	@param host string: 要查询的主机名，例如 "example.com"。
//	@param requestTypes []uint16: DNS 查询类型列表，例如 [dns.TypeA, dns.TypeAAAA]。
//	@param resolver Resolver: 指定的 DNS 解析器，用于执行查询。如果为 nil，则使用默认解析器。
//	@return *DNSData *DNSData: 查询结果，包含 DNS 记录的详细信息，例如 A、AAAA 记录等。
//	@return error error:如果查询失败，返回具体的错误信息
func (c *Client) QueryMultipleWithResolver(host string, requestTypes []uint16, resolver Resolver) (*DNSData, error) {
	return c.queryMultiple(host, requestTypes, resolver)
}

// axfr: 执行 DNS 区域传输（AXFR）查询，返回区域数据。
//
//	@receiver c *Client:
//	@param host string: 要查询的主机名，例如 "example.com"。
//	@return *AXFRData *AXFRData:  包含主机名和区域传输数据的结构体。
//	@return error error: 如果查询失败（例如 NS 记录查询失败或所有 AXFR 查询失败），返回错误信息。
func (c *Client) axfr(host string) (*AXFRData, error) {
	// 查询主机的 NS 记录以获取名称服务器列表
	dnsData, err := c.NS(host)
	if err != nil {
		return nil, err
	}
	// 初始化解析器列表，用于 AXFR 查询
	var resolvers []Resolver

	// 遍历 NS 记录，解析每个名称服务器的 A 记录以获取 IP 地址
	for _, ns := range dnsData.NS {
		nsData, err := c.A(ns)
		if err != nil {
			continue
		}
		// 将每个 A 记录转换为 NetworkResolver，指定 TCP 协议和端口 53
		for _, a := range nsData.A {
			resolvers = append(resolvers, &NetworkResolver{Protocol: TCP, Host: a, Port: "53"})
		}
	}

	// 将客户端的默认解析器追加到解析器列表
	resolvers = append(resolvers, c.resolvers...)

	var data []*DNSData
	// 对每个解析器执行 AXFR 查询
	for _, resolver := range resolvers {
		nsData, err := c.QueryMultipleWithResolver(host, []uint16{dns.TypeAXFR}, resolver)
		if err != nil {
			continue
		}
		data = append(data, nsData)
	}

	return &AXFRData{Host: host, DNSData: data}, nil
}

// AXFR: AXFR 是 axfr 方法的公共接口，用于执行 DNS 区域传输查询。
//
//	@receiver c *Client:
//	@param host string: 要查询的主机名，例如 "example.com"。
//	@return *AXFRData *AXFRData: 包含主机名和区域传输数据的结构体。
//	@return error error: 如果查询失败，返回错误信息
func (c *Client) AXFR(host string) (*AXFRData, error) {
	return c.axfr(host)
}

// CAA: 用于执行 CAA 记录查询
//
//	@receiver c *Client:
//	@param host string: 要查询的主机名，例如 "example.com"。
//	@return *DNSData *DNSData: 查询结果，包含 CAA 记录的详细信息。
//	@return error error: 如果查询失败，返回具体的错误信息
func (c *Client) CAA(host string) (*DNSData, error) {
	return c.QueryMultiple(host, []uint16{dns.TypeCAA})
}

// QueryParallel: 并行向多个解析器发送 DNS 请求并返回查询结果。
//
//	@receiver c *Client:
//	@param host string:  要查询的主机名，例如 "example.com"。
//	@param requestType uint16:  DNS 查询类型，例如 dns.TypeA
//	@param resolvers []string: NS 解析器的地址列表，例如 ["*******:53", "*******:53"]。
//	@return []*DNSData []*DNSData: 包含每个解析器查询结果的 DNSData 切片。
//	@return error error:
func (c *Client) QueryParallel(host string, requestType uint16, resolvers []string) ([]*DNSData, error) {
	// 创建 DNS 查询消息
	msg := dns.Msg{}
	// 设置查询问题，规范化主机名为规范格式并指定查询类型
	msg.SetQuestion(dns.CanonicalName(host), requestType)

	var dnsdatas []*DNSData

	// 使用 WaitGroup 确保所有 goroutine 完成
	var wg sync.WaitGroup
	// 遍历解析器列表，为每个解析器启动一个 goroutine
	for _, resolver := range resolvers {
		var dnsdata DNSData
		dnsdatas = append(dnsdatas, &dnsdata)
		// 增加 WaitGroup 计数器
		wg.Add(1)
		// 启动 goroutine 执行 DNS 查询
		go func(resolver string, dnsdata *DNSData) {
			defer wg.Done()
			// 发送 DNS 查询，复制消息以避免并发修改
			resp, err := dns.Exchange(msg.Copy(), resolver)
			if err != nil {
				return
			}
			// 解析响应消息到 DNSData
			err = dnsdata.ParseFromMsg(resp)
			if err != nil {
				return
			}
			// 填充 DNSData 的基本字段
			dnsdata.Host = host
			dnsdata.StatusCode = dns.RcodeToString[resp.Rcode]
			dnsdata.StatusCodeRaw = resp.Rcode
			dnsdata.Timestamp = time.Now()
			dnsdata.Resolver = append(dnsdata.Resolver, resolver)
			dnsdata.RawResp = resp
			dnsdata.Raw = resp.String()
			// 去除重复的记录
			dnsdata.dedupe()
		}(resolver, &dnsdata)
	}

	// 等待所有 goroutine 完成
	wg.Wait()

	return dnsdatas, nil
}

// Trace: 追踪指定域名的 DNS 查询路径，使用提供的查询类型。
//
//	@receiver c *Client:
//	@param host string: 要追踪的域名，例如 "example.com"。
//	@param requestType uint16:DNS 查询类型，例如 dns.TypeA
//	@param maxrecursion int:最大递归深度，限制查询的迭代次数。
//	@return *TraceData *TraceData: 追踪结果，包含每次查询的 DNSData 列表。
//	@return error error:如果查询过程中发生错误（例如网络错误），返回具体的错误信息。
func (c *Client) Trace(host string, requestType uint16, maxrecursion int) (*TraceData, error) {
	// 初始化追踪数据结构体
	var tracedata TraceData
	// 将主机名转换为规范格式
	host = dns.CanonicalName(host)

	// 创建 DNS 查询消息
	msg := dns.Msg{}
	msg.SetQuestion(host, requestType)

	// 初始化解析器列表，从根 DNS 服务器开始
	servers := RootDNSServersIPv4
	// 使用 map 跟踪NS，防止重复查询
	seenNS := make(map[string]struct{})
	// 使用 map 跟踪已跟随的 CNAME，防止无限循环
	seenCName := make(map[string]int)
	// 迭代查询，直到达到最大递归深度或无新解析器
	for i := 1; i < maxrecursion; i++ {
		// 设置查询问题，确保每次迭代使用正确的域名和查询类型
		msg.SetQuestion(host, requestType)
		// 并行向当前解析器列表发送 DNS 查询
		dnsdatas, err := c.QueryParallel(host, requestType, servers)
		if err != nil {
			return nil, err
		}

		// 记录已访问的名称服务器
		for _, server := range servers {
			seenNS[server] = struct{}{}
		}

		// 如果没有返回任何 DNS 数据，结束追踪
		if len(dnsdatas) == 0 {
			return &tracedata, nil
		}

		// 将有效的 DNS 数据添加到追踪结果
		for _, dnsdata := range dnsdatas {
			if dnsdata != nil && len(dnsdata.Resolver) > 0 {
				tracedata.DNSData = append(tracedata.DNSData, dnsdata)
			}
		}

		// 初始化新名称服务器列表和下一个 CNAME
		var newNSResolvers []string
		var nextCname string

		// 遍历查询结果，提取 NS 记录和 CNAME 记录
		for _, d := range dnsdatas {
			// 处理 NS 记录，将其解析为 IP 地址并添加到新解析器列表
			for _, ns := range d.NS {
				ips, err := net.LookupIP(ns)
				if err != nil {
					continue
				}
				for _, ip := range ips {
					if ip.To4() != nil {
						newNSResolvers = append(newNSResolvers, net.JoinHostPort(ip.String(), "53"))
					}
				}
			}
			// 处理 CNAME 记录，选择第一个作为下一个查询目标
			for _, cname := range d.CNAME {
				if nextCname == "" {
					nextCname = cname
					break
				}
			}
		}
		// 去除重复的名称服务器
		newNSResolvers = sliceutil.Dedupe(newNSResolvers)

		// 如果没有新的名称服务器，结束追踪
		if len(newNSResolvers) == 0 {
			break
		}

		// 随机选择一个新的名称服务器
		randomServer := newNSResolvers[rand.Intn(len(newNSResolvers))]
		// 如果选择的服务器已被访问且没有新的 CNAME，结束追踪
		if _, ok := seenNS[randomServer]; ok && nextCname == "" {
			break
		}

		// 更新解析器列表，仅使用选中的服务器
		servers = []string{randomServer}

		// 如果存在 CNAME，更新主机名并检查跟随次数
		if nextCname != "" {
			seenCName[nextCname]++
			// 超过最大 CNAME 跟随次数，结束追踪
			if seenCName[nextCname] > c.options.MaxPerCNAMEFollows {
				break
			}
			host = nextCname
		}
	}

	return &tracedata, nil
}

// Close: 关闭客户端的 UDP 连接池。
//
//	@receiver c *Client:
func (c *Client) Close() {
	_ = c.udpConnPool.Iterate(func(_ string, connPool *ConnPool) error {
		connPool.Close()
		return nil
	})
}
