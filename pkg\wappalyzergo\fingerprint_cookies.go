// Author: chenjb
// Version: V1.0
// Date: 2025-07-18 11:32:06
// FilePath: /yaml_scan/pkg/wappalyzergo/fingerprint_cookies.go
// Description: 实现基于HTTP Cookie的技术指纹识别功能
// Cookie是Web应用技术栈识别的重要信息源，包含会话管理、框架标识、安全机制等关键信息

package wappalyzergo

import "strings"

// checkCookies 检查HTTP Cookie中的技术指纹
// Cookie名称和值通常包含特定的技术标识符，是识别Web框架、CMS和会话管理技术的重要依据
//
// 参数:
//   - cookies: Cookie字符串列表，每个元素包含完整的Cookie定义
//            格式如: ["sessionid=abc123; Path=/; HttpOnly", "csrftoken=xyz789; Secure"]
//
// 返回值:
//   - []matchPartResult: 匹配到的技术列表，每个结果包含：
//     * application: 技术名称（如"Django", "Laravel", "ASP.NET"等）
//     * version: 从Cookie值中提取的版本号（如果可用）
//     * confidence: 匹配置信度（0-100），Cookie匹配通常具有中等到高等置信度
//
// 常见的技术指纹Cookie:
//   - PHPSESSID: PHP会话管理
//   - JSESSIONID: Java/Tomcat会话
//   - ASP.NET_SessionId: ASP.NET会话
//   - csrftoken: Django CSRF保护
//   - laravel_session: Laravel框架
//   - wordpress_*: WordPress CMS
//   - drupal_*: Drupal CMS
//
// 工作原理:
//   1. 标准化Cookie数据为键值对映射
//   2. 使用预编译的正则表达式对Cookie名称和值进行匹配
//   3. 从匹配结果中提取技术名称和版本信息
//   4. 返回所有匹配的技术列表
//
// 使用示例:
//   cookies := []string{
//       "PHPSESSID=abc123; Path=/",
//       "csrftoken=xyz789; HttpOnly",
//       "laravel_session=def456; Secure"
//   }
//   results := wappalyzer.checkCookies(cookies)
//   // 可能返回: [{"PHP", "", 85}, {"Django", "", 80}, {"Laravel", "", 90}]
func (s *Wappalyze) checkCookies(cookies []string) []matchPartResult {
	// 标准化Cookie数据为键值对映射
	normalized := s.normalizeCookies(cookies)

	// 使用指纹库对Cookie数据进行模式匹配
	technologies := s.fingerprints.matchMapString(normalized, cookiesPart)
	return technologies
}

// keyValuePairLength 定义Cookie键值对的预期长度
// 标准的Cookie格式为"name=value"，分割后应该有2个部分
const keyValuePairLength = 2

// normalizeCookies 标准化Cookie字符串列表为键值对映射
// 该函数将原始的Cookie字符串解析为简化的名称-值映射，便于后续的指纹匹配
//
// 参数:
//   - cookies: 原始Cookie字符串列表，来自HTTP响应的Set-Cookie头部
//            每个字符串可能包含多个属性，如: "sessionid=abc123; Path=/; HttpOnly"
//
// 返回值:
//   - map[string]string: 标准化后的Cookie映射，键为Cookie名称，值为Cookie值
//                      例如: {"sessionid": "abc123", "csrftoken": "xyz789"}
//
// 处理逻辑:
//   1. 遍历所有Cookie字符串
//   2. 去除字符串两端的空白字符
//   3. 按等号分割获取Cookie名称和值
//   4. 忽略格式不正确的Cookie（不包含等号或分割后长度不足）
//   5. 将有效的Cookie名称和值添加到结果映射中
//
// 注意事项:
//   - 只提取Cookie的名称和值，忽略其他属性（如Path、Domain、HttpOnly等）
//   - 如果Cookie字符串格式不正确，会被跳过而不会导致错误
//   - 不处理Cookie值中可能存在的特殊字符或编码
//
// 使用场景:
//   - 简化Cookie处理，便于正则表达式匹配
//   - 统一Cookie数据格式
//   - 提高Cookie指纹匹配的效率
func (s *Wappalyze) normalizeCookies(cookies []string) map[string]string {
	// 创建标准化结果映射
	normalized := make(map[string]string)

	// 遍历所有Cookie字符串
	for _, part := range cookies {
		// 去除字符串两端的空白字符，然后按等号分割
		// SplitN限制分割次数为2，确保只在第一个等号处分割
		// 这样可以正确处理Cookie值中包含等号的情况
		parts := strings.SplitN(strings.Trim(part, " "), "=", keyValuePairLength)
		
		// 检查分割结果是否符合预期格式（应该有名称和值两部分）
		if len(parts) < keyValuePairLength {
			continue // 跳过格式不正确的Cookie
		}
		
		// 将Cookie名称和值添加到标准化映射中
		// parts[0]是Cookie名称，parts[1]是Cookie值
		normalized[parts[0]] = parts[1]
	}
	return normalized
}

// findSetCookie 从HTTP响应头中提取并解析Set-Cookie值
// 该方法专门处理Set-Cookie头部的复杂格式，支持多种分隔符和格式变体
//
// 参数:
//   - headers: 标准化后的HTTP响应头映射，键和值均为小写
//
// 返回值:
//   - []string: 解析后的Cookie字符串列表，每个元素包含一个Cookie定义
//
// 处理逻辑:
//   1. 检查是否存在"set-cookie"头部
//   2. 按空格分割Set-Cookie头部值
//   3. 进一步按逗号和分号分割，处理多种分隔符格式
//   4. 过滤空字符串，返回有效的Cookie列表
//
// 支持的分隔符:
//   - 空格: 用于分离不同的Cookie或属性
//   - 逗号: 用于分离多个Cookie（某些服务器实现）
//   - 分号: 用于分离Cookie属性（标准格式）
//
// 注意事项:
//   - 这种解析方式可能在某些复杂的Cookie格式下不够精确
//   - 主要用于快速提取Cookie信息，不保证完全符合HTTP标准
//   - 适用于大多数常见的Cookie格式
//
// 使用场景:
//   - 从HTTP响应头中提取Cookie信息
//   - 预处理Cookie数据以便进行技术识别
//   - 处理不同服务器的Cookie格式变体
func (s *Wappalyze) findSetCookie(headers map[string]string) []string {
	// 检查是否存在Set-Cookie头部
	value, ok := headers["set-cookie"]
	if !ok {
		return nil // 如果没有Set-Cookie头部，返回nil
	}

	// 创建结果切片用于存储解析后的Cookie
	var values []string
	
	// 首先按空格分割Set-Cookie头部值
	for _, v := range strings.Split(value, " ") {
		// 跳过空字符串
		if v == "" {
			continue
		}
		
		// 检查当前部分是否包含逗号分隔符
		if strings.Contains(v, ",") {
			// 按逗号进一步分割并添加到结果中
			values = append(values, strings.Split(v, ",")...)
		} else if strings.Contains(v, ";") {
			// 检查是否包含分号分隔符
			// 按分号进一步分割并添加到结果中
			values = append(values, strings.Split(v, ";")...)
		} else {
			// 如果不包含特殊分隔符，直接添加到结果中
			values = append(values, v)
		}
	}
	return values
}


