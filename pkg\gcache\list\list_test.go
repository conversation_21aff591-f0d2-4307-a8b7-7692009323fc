//
// Author: chenjb
// Version: V1.0
// Date: 2025-05-10 09:46:51
// FilePath: /yaml_scan/pkg/gcache/list/list_test.go
// Description:

//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-20 17:16:12
//FilePath: /yaml_scan/pkg/gcache/list/list_test.go
//Description:

package list

import (
	"testing"

	"github.com/stretchr/testify/require"
)

// TestListBasicOperations 测试链表的基本操作
func TestListBasicOperations(t *testing.T) {
	l := New[int]() // 创建一个新的整数链表

	// 测试 PushFront：前端插入
	e1 := l.PushFront(1)                 // 在前端插入 1
	require.Equal(t, 1, l.Len())         // 验证长度为 1
	require.Equal(t, 1, l.Front().Value) // 验证前端元素为 1
	require.Equal(t, 1, l.Back().Value)  // 验证后端元素为 1

	// 测试 PushBack：后端插入
	e2 := l.PushBack(2)                  // 在后端插入 2
	require.Equal(t, 2, l.Len())         // 验证长度为 2
	require.Equal(t, 1, l.Front().Value) // 验证前端元素为 1
	require.Equal(t, 2, l.Back().Value)  // 验证后端元素为 2

	// 测试 InsertAfter：在特定元素后插入
	e3 := l.InsertAfter(3, e1)           // 在 e1 之后插入 3
	require.Equal(t, 3, l.Len())         // 验证长度为 3
	require.Equal(t, 3, e1.Next().Value) // 验证 e1 的下一个元素为 3
	require.Equal(t, 3, e3.Value)        // 验证 e3 的值为 3

	// 测试 InsertBefore：在特定元素前插入
	e4 := l.InsertBefore(0, e1)          // 在 e1 之前插入 0
	require.Equal(t, 4, l.Len())         // 验证长度为 4
	require.Equal(t, 0, l.Front().Value) // 验证前端元素为 0
	require.Equal(t, 0, e4.Value)        // 验证 e4 的值为 0

	// 测试 Remove：移除元素
	l.Remove(e2)                 // 移除 e2
	require.Equal(t, 3, l.Len()) // 验证长度为 3
	require.Nil(t, e2.list)      // 验证 e2 已被移除，不属于任何链表

	// 测试 MoveToFront：移动到前端
	l.MoveToFront(e3)                    // 将 e3 移动到前端
	require.Equal(t, 3, l.Front().Value) // 验证前端元素为 3

	// 测试 MoveToBack：移动到后端
	l.MoveToBack(e4)                    // 将 e4 移动到后端
	require.Equal(t, 0, l.Back().Value) // 验证后端元素为 0
}

// TestListPushBackList 测试 PushBackList 方法
func TestListPushBackList(t *testing.T) {
	l1 := New[int]() // 创建链表 l1
	l1.PushBack(1)   // 插入 1
	l1.PushBack(2)   // 插入 2

	l2 := New[int]() // 创建链表 l2
	l2.PushBack(3)   // 插入 3
	l2.PushBack(4)   // 插入 4

	l1.PushBackList(l2)                   // 将 l2 的副本插入到 l1 的后端
	require.Equal(t, 4, l1.Len())         // 验证 l1 长度为 4
	require.Equal(t, 1, l1.Front().Value) // 验证 l1 前端为 1
	require.Equal(t, 4, l1.Back().Value)  // 验证 l1 后端为 4
}

// TestListPushFrontList 测试 PushFrontList 方法
func TestListPushFrontList(t *testing.T) {
	l1 := New[int]() // 创建链表 l1
	l1.PushBack(1)   // 插入 1
	l1.PushBack(2)   // 插入 2

	l2 := New[int]() // 创建链表 l2
	l2.PushBack(3)   // 插入 3
	l2.PushBack(4)   // 插入 4

	l1.PushFrontList(l2)                  // 将 l2 的副本插入到 l1 的前端
	require.Equal(t, 4, l1.Len())         // 验证 l1 长度为 4
	require.Equal(t, 3, l1.Front().Value) // 验证 l1 前端为 3
	require.Equal(t, 2, l1.Back().Value)  // 验证 l1 后端为 2
}

// TestListMoveBefore 测试 MoveBefore 方法
func TestListMoveBefore(t *testing.T) {
	l := New[int]()     // 创建链表
	e1 := l.PushBack(1) // 插入 1
	e2 := l.PushBack(2) // 插入 2
	e3 := l.PushBack(3) // 插入 3

	l.MoveBefore(e3, e2)                 // 将 e3 移动到 e2 之前
	require.Equal(t, 1, l.Front().Value) // 验证前端为 1
	require.Equal(t, 3, e1.Next().Value) // 验证 e1 的下一个为 3
	require.Equal(t, 2, e3.Next().Value) // 验证 e3 的下一个为 2
}

// TestListMoveAfter 测试 MoveAfter 方法
func TestListMoveAfter(t *testing.T) {
	l := New[int]()     // 创建链表
	e1 := l.PushBack(1) // 插入 1
	e2 := l.PushBack(2) // 插入 2
	_ = l.PushBack(3)   // 插入 3

	l.MoveAfter(e1, e2)                  // 将 e1 移动到 e2 之后
	require.Equal(t, 2, l.Front().Value) // 验证前端为 2
	require.Equal(t, 1, e2.Next().Value) // 验证 e2 的下一个为 1
	require.Equal(t, 3, e1.Next().Value) // 验证 e1 的下一个为 3
}
