// Author: chenjb
// Version: V1.0
// Date: 2025-06-24 15:57:39
// FilePath: /yaml_scan/pkg/tlsx/tlsx/openssl/options.go
// Description: OpenSSL命令选项和协议定义
package openssl

import (
	"crypto/x509"
	"errors"
	"fmt"
	"strings"
)

// SupportedTLSVersion 定义OpenSSL模式支持的TLS版本列表
// 注意：TLS 1.3支持取决于OpenSSL版本，某些旧版本可能不支持
var SupportedTLSVersions = []string{
	"tls10",
	"tls11",
	"tls12",
	// "tls13",
}

// Protocols 定义支持的协议类型枚举
// 包括TLS和DTLS的各个版本
type Protocols int

const (
	TLSv1          Protocols = iota // TLS 1.0协议
	TLSv1_1                         // TLS 1.1协议
	TLSv1_2                         // TLS 1.2协议
	TLSv1_3                         // TLS 1.3协议
	DTLSv1                          // DTLS 1.0协议（用于UDP）
	DTLSv1_2                        // DTLS 1.2协议（用于UDP）
	TLSUnsupported                  // 不支持的协议版本
)

// String 将协议枚举转换为OpenSSL命令行参数字符串
// @receiver p
// @return string string:
func (p *Protocols) String() string {
	switch *p {
	case 0:
		return "tls1"
	case 1:
		return "tls1_1"
	case 2:
		return "tls1_2"
	// case 3:
	// 	return "tls1_3"
	case 4:
		return "dtls1"
	case 5:
		return "dtls1_2"
	default:
		return ""
	}
}

// getProtocol 将TLS版本字符串转换为协议枚举类型
// 该函数负责将用户友好的版本字符串映射到内部协议枚举
// @param versionTLS string:
// @return Protocols Protocols:
// @return error error:
func getProtocol(versionTLS string) (Protocols, error) {
	switch versionTLS {
	case "tls10":
		return TLSv1, nil
	case "tls11":
		return TLSv1_1, nil
	case "tls12":
		return TLSv1_2, nil
	// case "tls13":
	// 	tlsversion = TLSv1_3
	// case "dtls10":
	// 	tlsversion = DTLSv1
	// case "dtls12":
	// 	tlsversion = DTLSv1_2
	default:
		return TLSUnsupported, errors.New("unsupported version")
	}
}

// Options 定义OpenSSL命令行选项结构
type Options struct {
	Address       string    // 连接地址，格式为"host:port"，对应-connect参数
	Cipher        []string  // 要使用的密码套件列表，对应-cipher参数（以逗号分隔）
	ServerName    string    // TLS扩展中的服务器名称指示（SNI），对应-servername参数
	CertChain     bool      // 是否显示完整证书链，对应-showcerts参数
	Protocol      Protocols // 要使用的TLS/DTLS协议版本，对应-tls1_2等参数
	CAFile        string    // CA证书文件路径，对应-CAfile参数，用于证书验证
	SkipCertParse bool      // 是否跳过证书解析和验证，仅用于内部控制（不影响命令行）
}

// Args  根据配置选项生成OpenSSL s_client命令的参数列表
// 该方法将Options结构体中的配置转换为可执行的命令行参数
// @receiver o 
// @return []string []string:   OpenSSL命令参数列表，第一个元素始终为"s_client"
// @return error error: 如果必要参数缺失则返回错误
// 生成的命令格式示例:
//   ["s_client", "-connect", "example.com:443", "-servername", "example.com", "-tls1_2"]
func (o *Options) Args() ([]string, error) {
	args := []string{"s_client"}

	// 添加连接地址参数（必需）
	if o.Address != "" {
		args = append(args, "-connect", o.Address)
	} else {
		return args, fmt.Errorf("openssl: address missing")
	}

	// 添加密码套件参数（可选）
	// 多个密码套件用逗号分隔为单个字符串
	if len(o.Cipher) != 0 {
		args = append(args, "-cipher", strings.Join(o.Cipher, ","))
	}

	// 添加SNI服务器名称参数（可选）
	if o.ServerName != "" {
		args = append(args, "-servername", o.ServerName)
	}

	// 添加证书链显示参数（可选）
	if o.CertChain {
		args = append(args, "-showcerts")
	}

	// 添加协议版本参数（可选）
	// 协议字符串前需要添加"-"前缀
	if o.Protocol.String() != "" {
		args = append(args, "-"+o.Protocol.String())
	}

	// 添加CA证书文件参数（可选）
	if o.CAFile != "" {
		args = append(args, "-CAfile", o.CAFile)
	}

	return args, nil
}

// Session 表示OpenSSL返回的TLS会话详细信息
type Session struct {
	Protocol  string // 协商使用的TLS协议版本，如"TLSv1.2"、"TLSv1.3"等
	Cipher    string // 协商使用的密码套件名称，如"ECDHE-RSA-AES256-GCM-SHA384"
	SessionID string // TLS会话标识符，用于会话复用（十六进制字符串）
	MasterKey string // TLS主密钥，用于加密通信（十六进制字符串，敏感信息）
}

// getTLSVersion 将OpenSSL格式的协议版本转换为内部标准格式
// 该方法负责统一不同来源的TLS版本表示方式
// @receiver s 
// @return string string: 
// 版本映射规则:
//   - "TLSv1" -> "tls10" (TLS 1.0)
//   - "TLSv1.1" -> "tls11" (TLS 1.1)
//   - "TLSv1.2" -> "tls12" (TLS 1.2)
func (s *Session) getTLSVersion() string {
	switch s.Protocol {
	case "TLSv1":
		return "tls10"
	case "TLSv1.1":
		return "tls11"
	case "TLSv1.2":
		return "tls12"
	// case "TLSv1.3":
	// 	return "tls13"
	default:
		return s.Protocol
	}
}

// Response 表示OpenSSL s_client命令的完整响应结果
type Response struct {
	AllCerts           []*x509.Certificate // 服务器返回的完整证书链，按从叶证书到根证书的顺序排列
	Session            *Session            // TLS会话详细信息，包含协议版本、密码套件等协商结果
	ClientCertRequired bool                // 服务器是否要求客户端提供证书进行双向认证
}
