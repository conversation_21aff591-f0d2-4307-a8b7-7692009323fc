//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-21 15:44:30
//FilePath: /yaml_scan/pkg/goflags/group_test.go
//Description:

package goflags

import (
	"testing"
)

// TestGroup 测试 FlagData 的 Group 方法
func TestGroup(t *testing.T) {
	// Create a FlagData instance
	flagData := &FlagData{}

	// Set the group name
	groupName := "testGroup"
	flagData.Group(groupName)

	// Check if the group name is set correctly
	if flagData.group != groupName {
		t.Errorf("Expected group name to be '%s', got '%s'", groupName, flagData.group)
	}
}

// TestSetGroup 测试 FlagSet 的 SetGroup 方法
func TestSetGroup(t *testing.T) {
	// 创建一个 FlagSet 实例
	flagSet := &FlagSet{}

	// 设置分组名称和描述
	groupName := "testGroup"
	description := "This is a test group"
	flagSet.SetGroup(groupName, description)

	// 检查分组是否正确添加
	if len(flagSet.groups) != 1 {
		t.<PERSON><PERSON><PERSON>("Expected groups length to be 1, got %d", len(flagSet.groups))
	}
	if flagSet.groups[0].name != groupName {
		t.Errorf("Expected group name to be '%s', got '%s'", groupName, flagSet.groups[0].name)
	}
	if flagSet.groups[0].description != description {
		t.Errorf("Expected group description to be '%s', got '%s'", description, flagSet.groups[0].description)
	}
}

// TestCreateGroup 测试 FlagSet 的 CreateGroup 方法
func TestCreateGroup(t *testing.T) {
	// 创建一个 FlagSet 实例
	flagSet := &FlagSet{}

	// 创建 FlagData 实例
	flag1 := &FlagData{}
	flag2 := &FlagData{}

	// 创建分组名称和描述
	groupName := "testGroup"
	description := "This is a test group"

	// 创建分组并添加标志
	flagSet.CreateGroup(groupName, description, flag1, flag2)

	if len(flagSet.groups) != 1 {
		t.Errorf("Expected groups length to be 1, got %d", len(flagSet.groups))
	}
	if flagSet.groups[0].name != groupName {
		t.Errorf("Expected group name to be '%s', got '%s'", groupName, flagSet.groups[0].name)
	}
	if flagSet.groups[0].description != description {
		t.Errorf("Expected group description to be '%s', got '%s'", description, flagSet.groups[0].description)
	}

	// Check if the flags' group names are set correctly
	if flag1.group != groupName {
		t.Errorf("Expected flag1's group name to be '%s', got '%s'", groupName, flag1.group)
	}
	if flag2.group != groupName {
		t.Errorf("Expected flag2's group name to be '%s', got '%s'", groupName, flag2.group)
	}
}

func TestGetGroupbyName(t *testing.T) {
	flagSet := FlagSet{
		groups: []groupData{
			{name: "Network", description: "Network related flags"},
			{name: "Storage", description: "Storage related flags"},
		},
	}

	tests := []struct {
		name     string
		expected string
	}{
		{"Network", "Network related flags"},
		{"network", "Network related flags"}, // 测试不区分大小写
		{"Storage", "Storage related flags"},
		{"storage", "Storage related flags"}, // 测试不区分大小写
		{"Unknown", ""},                      // 测试未找到的情况
	}

	for _, test := range tests {
		group := flagSet.getGroupbyName(test.name)
		if test.expected == "" {
			if group != (groupData{}) {
				t.Errorf("Expected no group for %s, got %v", test.name, group)
			}
		} else {
			if group.description != test.expected {
				t.Errorf("Expected description '%s' for %s, got '%s'", test.expected, test.name, group.description)
			}
		}
	}
}
