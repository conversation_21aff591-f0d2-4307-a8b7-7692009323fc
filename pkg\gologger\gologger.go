package gologger

import (
	"fmt"
	"os"
	"strings"
	"time"
	"runtime"

	"yaml_scan/pkg/config"
	"yaml_scan/pkg/gologger/formatter"
	"yaml_scan/pkg/gologger/levels"
	"yaml_scan/pkg/gologger/writer"
)

var (
	// 映射
	labels = map[levels.Level]string{
		levels.LevelFatal:   "FTL",
		levels.LevelError:   "ERR",
		levels.LevelInfo:    "INF",
		levels.LevelWarning: "WRN",
		levels.LevelDebug:   "DBG",
		levels.LevelVerbose: "VER",
	}
	// DefaultLogger is the default logging instance
	DefaultLogger *Logger
)


func init() {
	// 加载日志配置
	logConfig := config.GetLogConfig()

	DefaultLogger = &Logger{}
	// 设置日志级别
	level, _ := levels.ParseLevel(logConfig.MaxLevel)
	DefaultLogger.SetMaxLevel(level)
	// 设置包含调用栈信息
	DefaultLogger.SetIncludeCallerInfo(true)
	// 设置时间戳
	DefaultLogger.timestamp = true
	// 设置日志格式化
	DefaultLogger.SetFormatter(formatter.NewCLI(true))
	// 设置写入
	options := &writer.FileWithRotationOptions{
		Location:         logConfig.Location,
		Rotate:           logConfig.Rotate,
		RotationInterval: logConfig.RotationDuration,
		Rotationcheck:    logConfig.CheckDuration,
		MaxSize:          logConfig.MaxSize,
		Compress:         logConfig.Compress,
		BackupTimeFormat: logConfig.BackupTimeFormat,
		ArchiveFormat:    logConfig.ArchiveFormat,
		RotateEachHour:   logConfig.RotateEachHour,
		RotateEachDay:    logConfig.RotateEachDay,
		FileName:         logConfig.FileName,
	}

	fileWriter, err := writer.NewFileWithRotation(options)
	if err != nil {
		fmt.Printf("write file err: %s\n", err)
		return
	}
	DefaultLogger.SetWriter(fileWriter)
}


// Logger 是一个以美观和快速的方式记录结构化数据的记录器
type Logger struct {
	writer            writer.Writer  // 输出方式
	maxLevel          levels.Level // 日志最大级别
	formatter         formatter.Formatter // 格式化方法
	timestampMinLevel levels.Level // 指定从哪个日志级别开始添加时间戳。只有级别大于或等于 timestampMinLevel 的日志事件才会包含时间戳
	timestamp         bool // 是否在日志中包含时间戳 如果为 true，则根据 timestampMinLevel 的设置添加时间戳；如果为 false，则不添加时间戳。
	includeCallerInfo bool // 是否在日志中包含调用者信息
}


// Event是要用数据写入的日志事件
type Event struct {
	logger   *Logger  //日志记录器
	level    levels.Level  // 日志级别
	message  string  // 日志消息
	metadata map[string]string  //附加的原数据
	Function string // 函数名
	Line int // 行号
}

// SetIncludeCallerInfo 设置是否在日志中包含调用者信息
func (l *Logger) SetIncludeCallerInfo(include bool) {
	l.includeCallerInfo = include
}


// SetMaxLevel 设置 Logger 结构体的最大日志级别
func (l *Logger) SetMaxLevel(level levels.Level) {
	l.maxLevel = level
}


// SetFormatter 设置 Logger 结构体的格式化器
func (l *Logger) SetFormatter(formatter formatter.Formatter) {
	l.formatter = formatter
}

// SetWriter sets the writer instance for a logger
func (l *Logger) SetWriter(writer writer.Writer) {
	l.writer = writer
}


// 检查日志事件的级别是否被启用 如果事件的级别小于或等于最大级别 则返回 true，表示该级别的日志是启用的；否则返回 false
func isCurrentLevelEnabled(e *Event) bool {
	return e.level <= e.logger.maxLevel
}


// Log logs a message to a logger instance  记录日志事件。
func (l *Logger) Log(event *Event) {

	if !isCurrentLevelEnabled(event) {
		return
	}
	//使用 strings.TrimSuffix 去除消息末尾的换行符，以确保格式化时不会出现多余的换行。
	event.message = strings.TrimSuffix(event.message, "\n")

	// 如果需要包含调用者信息，则获取函数名和行号
	if l.includeCallerInfo {
		functionName, lineNumber := GetFunctionNameAndLine()
		event.Function = functionName
		event.Line = lineNumber
	}
	
	//格式化日志事件:
	data, err := l.formatter.Format(&formatter.LogEvent{
		Message:  event.message,
		Level:    event.level,
		Metadata: event.metadata,
		Function: event.Function,
		Line: event.Line,
	})
	if err != nil {
		return
	}
	//写入日志
	l.writer.Write(data, event.level)

	//如果日志级别是 LevelFatal，则调用 os.Exit(1) 退出程序，表示发生了致命错误。
	if event.level == levels.LevelFatal {
		os.Exit(1)
	}
}



// Str adds a string metadata item to the log
func (e *Event) Str(key, value string) *Event {
	e.metadata[key] = value
	return e
}

// Msg logs a message to the logger
func (e *Event) Msg(message string) {
	e.message = message
	e.logger.Log(e)
}


// Msgf logs a printf style message to the logger
func (e *Event) Msgf(format string, args ...interface{}) {
	e.message = fmt.Sprintf(format, args...)
	e.logger.Log(e)
}

// MsgFunc logs a message with lazy evaluation. 允许在需要时计算消息，适用于计算开销较大的情况。
// Useful when computing the message can be resource heavy.
func (e *Event) MsgFunc(messageSupplier func() string) {
	if !isCurrentLevelEnabled(e) {
		return
	}
	e.message = messageSupplier()
	e.logger.Log(e)
}


//设置事件的元数据中的标签，标签根据日志级别进行设置
func (e *Event) setLevelMetadata(level levels.Level) {
	e.metadata["label"] = labels[level]
}

// TimeStamp 给日志时间添加时间戳
func (e *Event) TimeStamp() *Event {
	e.metadata["timestamp"] = time.Now().Format("2006-01-02 15:04:05")
	return e
}


// 创建一个新的事件，带有指定的日志级别和日志记录器
func newEventWithLevelAndLogger(level levels.Level, l *Logger) *Event {

	event := &Event{
		logger:   l,
		level:    level,
		metadata: make(map[string]string),
	}
	// 如果日志记录器启用了时间戳，并且当前日志级别大于等于最小时间戳级别，则添加时间戳
	if l.timestamp && level >= l.timestampMinLevel {
		event.TimeStamp()
	}
	return event
}


//  创建一个带有默认日志记录器的事件
func newDefaultEventWithLevel(level levels.Level) *Event {
	return newEventWithLevelAndLogger(level, DefaultLogger)
}


// 创建一个信息级别的日志事件，并设置默认标签
func Info() *Event {
	event := newDefaultEventWithLevel(levels.LevelInfo)
	event.setLevelMetadata(levels.LevelInfo)
	return event
}


// 创建一个警告级别的日志事件，并设置默认标签
func Warning() *Event {
	event := newDefaultEventWithLevel(levels.LevelWarning)
	event.setLevelMetadata(levels.LevelWarning)
	return event
}

// 创建一个错误级别的日志事件，并设置默认标签
func Error() *Event {
	event := newDefaultEventWithLevel(levels.LevelError)
	event.setLevelMetadata(levels.LevelError)
	return event
}

// 创建一个debug级别的日志事件，并设置默认标签
func Debug() *Event {
	event := newDefaultEventWithLevel(levels.LevelDebug)
	event.setLevelMetadata(levels.LevelDebug)
	return event
}

// 创建一个Fatal级别的日志事件，遇到错误会退出
func Fatal() *Event {
	event := newDefaultEventWithLevel(levels.LevelFatal)
	event.setLevelMetadata(levels.LevelFatal)
	return event
}

// Silent 在标准输出上打印字符串，不带任何额外的标签
func Silent() *Event {
	event := newDefaultEventWithLevel(levels.LevelSilent)
	return event
}


// Print 在stderr上打印不带任何额外标签的字符串。
func Print() *Event {
	event := newDefaultEventWithLevel(levels.LevelInfo)
	return event
}

// Verbose 只在Verbose输出模式下打印字符串。
func Verbose() *Event {
	event := newDefaultEventWithLevel(levels.LevelVerbose)
	event.setLevelMetadata(levels.LevelVerbose)
	return event
}

// Info writes a info message on the screen with the default label
func (l *Logger) Info() *Event {
	event := newEventWithLevelAndLogger(levels.LevelInfo, l)
	event.setLevelMetadata(levels.LevelInfo)
	return event
}

// Warning writes a warning message on the screen with the default label
func (l *Logger) Warning() *Event {
	event := newEventWithLevelAndLogger(levels.LevelWarning, l)
	event.setLevelMetadata(levels.LevelWarning)
	return event
}

// Error writes a error message on the screen with the default label
func (l *Logger) Error() *Event {
	event := newEventWithLevelAndLogger(levels.LevelError, l)
	event.setLevelMetadata(levels.LevelError)
	return event
}

// Debug writes an error message on the screen with the default label
func (l *Logger) Debug() *Event {
	event := newEventWithLevelAndLogger(levels.LevelDebug, l)
	event.setLevelMetadata(levels.LevelDebug)
	return event
}

// Fatal exits the program if we encounter a fatal error
func (l *Logger) Fatal() *Event {
	event := newEventWithLevelAndLogger(levels.LevelFatal, l)
	event.setLevelMetadata(levels.LevelFatal)
	return event
}

// Print prints a string on screen without any extra labels.
func (l *Logger) Print() *Event {
	event := newEventWithLevelAndLogger(levels.LevelSilent, l)
	return event
}

// Verbose prints a string only in verbose output mode.
func (l *Logger) Verbose() *Event {
	event := newEventWithLevelAndLogger(levels.LevelVerbose, l)
	event.setLevelMetadata(levels.LevelVerbose)
	return event
}

// 获取当前函数名和行号
func GetFunctionNameAndLine() (string, int) {
	// 从调用栈中获取信息
	var skip int
	for {
		pc, file, line, ok := runtime.Caller(skip)
		if !ok {
			return "unknown", 0
		}
		fn := runtime.FuncForPC(pc)
		if fn == nil {
			return "unknown", 0
		}
		fullFuncName := fn.Name()
		// 检查是否是日志库内部的函数
		if !isInternalFunction(fullFuncName) {
			// 找到最外层的调用者
			parts := strings.Split(fullFuncName, ".")
			functionName := parts[len(parts)-1] // 取最后一个部分作为函数名
			return functionName + " (" + file + ":" + fmt.Sprintf("%d", line) + ")", line
		}
		skip++ // 跳过当前函数
	}
}


// isInternalFunction 检查函数是否属于日志库内部
func isInternalFunction(funcName string) bool {
	// 这里可以根据需要添加更多的内部包名
	internalPackages := []string{"gologger"}
	for _, pkg := range internalPackages {
		if strings.Contains(funcName, pkg) {
			return true
		}
	}
	return false
}

// Label applies a custom label on the log event
func (e *Event) Label(label string) *Event {
	e.metadata["label"] = label
	return e
}