package formatter

import (
	"io"
)


// Tee 格式化器可以将事件写入一个 writer，同时使用现有的格式化器进行输出
type Tee struct {
	Wrapper Formatter  // 用于格式化的包装器
	w       io.Writer  // 目标写入器

	Formatter Formatter  // 默认的格式化器
}

// NewTee 返回一个新的 TeeWriter，使用默认的 JSON 消息
func NewTee(wrapper Formatter, w io.Writer) (teeW *Tee) {
	teeW = &Tee{
		Wrapper:   wrapper,
		Formatter: &JSON{},  // 默认使用 JSON 格式化器
	}
	teeW.w = w
	return
}

// Format 方法保存事件并将事件转发给内部的 Wrapper
func (tee *Tee) Format(event *LogEvent) (bts []byte, err error) {
	if event == nil {
		return
	}
	// 获取事件的标签
	label := event.Metadata["label"]
	
	// 使用默认的格式化器格式化事件
	bts, err = tee.Formatter.Format(event)
	// 格式化过程中删除了 Metadata 中的 label 键 - 如果我们想要颜色，需要再次添加它
	if label != "" {
		event.Metadata["label"] = label
	}
	if err != nil {
		return
	}

	// 忽略写入错误，以防止数据完全丢失
	_, _ = tee.w.Write(append(bts, []byte("\n")...))

	return tee.Wrapper.Format(event)
}