package formatter

import (
	"bytes"
	"fmt"
	
	"yaml_scan/pkg/gologger/levels"

	"github.com/logrusorgru/aurora"
)

type CLI struct {
	NoUseColors bool  // 是否在命令行输出中使用颜色
	aurora      aurora.Aurora // 用于处理终端颜色的库
}

// 确保 CLI 结构体实现了 Formatter 接口
var _ Formatter = &CLI{}


// NewCLI 创建一个cli formatter实例
func NewCLI(noUseColors bool) *CLI {
	return &CLI{NoUseColors: noUseColors, aurora: aurora.NewAurora(!noUseColors)}
}


// Format 格式化日志事件
func (c *CLI) Format(event *LogEvent) ([]byte, error) {
	// 为标签添加颜色
	c.colorizeLabel(event)
	
	// 创建一个字节缓冲区，用于高效构建输出字符串
	buffer := &bytes.Buffer{}
	buffer.Grow(len(event.Message))  // 预分配缓冲区大小以提高性能

	// 处理标签（如果存在）
	label, ok := event.Metadata["label"]
	if label != "" && ok {
		buffer.WriteRune('[')
		buffer.WriteString(label)
		buffer.WriteRune(']')
		buffer.WriteRune(' ')
		delete(event.Metadata, "label")  // 从元数据中删除标签，避免重复输出
	}

	// 处理时间戳（如果存在）
	timestamp, ok := event.Metadata["timestamp"]
	if timestamp != "" && ok {
		buffer.WriteRune('[')
		buffer.WriteString(timestamp)
		buffer.WriteRune(']')
		buffer.WriteRune(' ')
		delete(event.Metadata, "timestamp")  // 从元数据中删除时间戳
	}

	// 处理函数名和行号
	function := event.Function
	line := event.Line
	if function != "" && line > 0 {
		buffer.WriteRune('[')
		buffer.WriteString(function)
		buffer.WriteString(":")
		buffer.WriteString(fmt.Sprintf("%d", line))
		buffer.WriteRune(']')
		buffer.WriteRune(' ')
	}

	// 添加日志消息
	buffer.WriteString(event.Message)

	// 处理其他元数据键值对
	for k, v := range event.Metadata {
		buffer.WriteRune(' ')
		buffer.WriteString(c.colorizeKey(k))  // 为键添加颜色（如果启用）
		buffer.WriteRune('=')
		buffer.WriteString(v)
	}
	// 将缓冲区内容转换为字节切片并返回
	data := buffer.Bytes()
	return data, nil
}


// colorizeKey 方法为元数据键添加颜色（如果启用） 这里只加粗字体
func (c *CLI) colorizeKey(key string) string {
	if c.NoUseColors {
		return key
	}
	return c.aurora.Bold(key).String()   // 否则，将键加粗
}


// colorizeLabel 为标签添加颜色
func (c *CLI) colorizeLabel(event *LogEvent) {
	label := event.Metadata["label"]
	// 如果标签为空或不使用颜色，直接返回
	if label == "" || c.NoUseColors {
		return
	}
	switch event.Level {
	case levels.LevelSilent:
		return
	case levels.LevelInfo, levels.LevelVerbose:
		event.Metadata["label"] = c.aurora.Blue(label).String()  // 信息和详细级别使用蓝色
	case levels.LevelFatal:
		event.Metadata["label"] = c.aurora.Bold(aurora.Red(label)).String()  // 致命级别使用粗体红色
	case levels.LevelError:
		event.Metadata["label"] = c.aurora.Red(label).String()  // 错误级别使用红色
	case levels.LevelDebug:
		event.Metadata["label"] = c.aurora.Magenta(label).String()  // 调试级别使用品红色
	case levels.LevelWarning:
		event.Metadata["label"] = c.aurora.Yellow(label).String()  // 警告级别使用黄色
	}
}
