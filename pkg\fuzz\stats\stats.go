// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-17 16:23:44
// FilePath: /yaml_scan/pkg/fuzz/stats/stats.go
// Description: 
package stats

// Tracker is a stats tracker module for fuzzing server
type Tracker struct {
	database *simpleStats
}

type ErrorEvent struct {
	TemplateID string
	URL        string
	Error      string
}


// NewTracker creates a new tracker instance
func NewTracker() (*Tracker, error) {
	db, err := NewSimpleStats()
	if err != nil {
		return nil, errors.Wrap(err, "could not create new tracker")
	}

	tracker := &Tracker{
		database: db,
	}
	return tracker, nil
}

func (t *Tracker) RecordErrorEvent(event ErrorEvent) {
	if err := t.database.InsertError(event); err != nil {
		log.Printf("could not insert error record: %s", err)
	}
}