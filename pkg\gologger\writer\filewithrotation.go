package writer

import (
	"os"
	"time"
	"path/filepath"
	"fmt"
	"sync"
	"strings"
	"errors"

	"yaml_scan/pkg/gologger/levels"

	"github.com/mholt/archiver/v3"
	"gopkg.in/djherbis/times.v1"
)


// FileWithRotationOptions 包含日志写入器的配置选项
type FileWithRotationOptions struct {
	Location         string  // 日志存储位置
	Rotate           bool  // 是否启用轮换
	Rotationcheck    time.Duration  // 轮换检查的时间间隔
	RotationInterval time.Duration  // 轮换时间间隔
	FileName         string  // 日志文件名
	Compress         bool   // 是否压缩旧日志
	MaxSize          int  // 最大文件大小（MB）
	BackupTimeFormat string   // 备份时间格式
	ArchiveFormat    string  // 归档格式
	RotateEachHour bool  // 是否每小时轮换
	RotateEachDay  bool    // 是否每天轮换
}

var _ Writer = &FileWithRotation{}

// DefaultFileWithRotationOptions 是默认的日志配置选项
var DefaultFileWithRotationOptions FileWithRotationOptions


func init() {
	// 设置默认日志目录为当前工作目录下的 "logs" 文件夹
	if dir, err := os.Getwd(); err == nil {
		DefaultFileWithRotationOptions.Location = filepath.Join(dir, "logs")
	}

	// 设置日志轮换检查的时间间隔
	DefaultFileWithRotationOptions.Rotationcheck = time.Duration(10 * time.Second)

	// 设置当前日志文件名为 "processname.log"
	DefaultFileWithRotationOptions.FileName = fmt.Sprintf("%s.log", filepath.Base(os.Args[0]))
	DefaultFileWithRotationOptions.BackupTimeFormat = "2006-01-02 15-04-05"  // 备份时间格式
	DefaultFileWithRotationOptions.ArchiveFormat = "gz"   // 归档格式
}


// FileWithRotation 是一个支持轮换的并发文件写入器
type FileWithRotation struct {
	options     *FileWithRotationOptions  // 配置选项
	mutex       *sync.Mutex   // 互斥锁，确保并发安全
	logFile     *os.File  // 当前日志文件
	logfileTime time.Time   // 日志文件的最后修改时间
}


//  scheduler 启动一个定时器，定期执行指定的函数
func scheduler(tick *time.Ticker, f func()) {
	for range tick.C {
		f()
	}
}


// Close 关闭并刷新日志文件
func (w *FileWithRotation) Close() {
	_ = w.logFile.Sync()
	w.logFile.Close()
}


// renameAndCompressLogs 重命名并压缩旧日志文件
func (w *FileWithRotation) renameAndCompressLogs() {
	// 当前日志文件名
	filename := filepath.Join(w.options.Location, w.options.FileName)
	// 去掉扩展名
	fileExt := filepath.Ext(filename)
	//  去掉扩展名
	filenameBase := strings.TrimSuffix(filename, fileExt)
	timeToSave := time.Now()
	// 根据配置决定时间截断
	if w.options.RotateEachHour {
		timeToSave = timeToSave.Truncate(1 * time.Hour)
	} else if w.options.RotateEachDay {
		timeToSave = timeToSave.Truncate(24 * time.Hour)
	}
	// 生成新的文件名
	tmpFilename := filenameBase + "." + timeToSave.Format(w.options.BackupTimeFormat) + fileExt
	// 重命名当前日志文件
	_ = os.Rename(filename, tmpFilename)

	if w.options.Compress {
		// 异步压缩旧日志文件
		go func(filename string) {
			err := archiver.CompressFile(tmpFilename, filename+"."+w.options.ArchiveFormat)
			if err == nil {
				// 压缩成功后删除原始文件
				os.RemoveAll(tmpFilename)
			}
		}(tmpFilename)
	}
}

// CreateFile 创建一个新的日志文件
func (w *FileWithRotation) CreateFile(filename string) (*os.File, error) {
	f, err := os.OpenFile(filename, os.O_APPEND|os.O_CREATE|os.O_RDWR, 0755)
	if err != nil {
		return nil, err
	}
	return f, nil
}

// getChangeTime 获取文件的最后修改时间
func getChangeTime(filename string) (time.Time, error) {
	timeNow := time.Now()
	t, err := times.Stat(filename)
	if err != nil {
		return timeNow, err
	}

	if t.HasChangeTime() {
		return t.ChangeTime(), nil
	}

	return timeNow, errors.New("no change time")
}

// newLogger 创建新的日志文件
func (w *FileWithRotation) newLogger() (err error) {
	filename := filepath.Join(w.options.Location, w.options.FileName)
	logFile, err := w.CreateFile(filename)
	if err != nil {
		return err
	}
	w.logFile = logFile

	// 获取文件的最后修改时间
	w.logfileTime, err = getChangeTime(filename)
	if err != nil {
		return err
	}

	return nil
}

// newLoggerSync 以同步方式创建新的日志文件
func (w *FileWithRotation) newLoggerSync() (err error) {
	w.mutex.Lock()
	defer w.mutex.Unlock()

	return w.newLogger()
}


// checkAndRotate 检查是否需要轮换日志文件
func (w *FileWithRotation) checkAndRotate() {
	timeNow := time.Now()
	// 检查当前日志文件大小
	currentFileSizeMb, err := w.logFile.Stat()
	if err != nil {
		return
	}
	// 如果最大文件大小大于零，并且当前文件的大小大于或等于最大文件大小
	filesizeCheck := w.options.MaxSize > 0 && currentFileSizeMb.Size() >= int64(w.options.MaxSize*1024*1024)
	// 如果设置了轮换时间间隔，并且日志文件的最后修改时间加上这个间隔早于当前时间
	filechangedateCheck := w.options.RotationInterval > 0 && w.logfileTime.Add(w.options.RotationInterval).Before(timeNow)
	// 如果启用了每小时轮换，并且日志文件的最后修改时间与当前时间在同一天，但小时不同 需要轮换日志文件
	rotateEachHourCheck := w.options.RotateEachHour && w.logfileTime.Day() == timeNow.Day() && w.logfileTime.Hour() != timeNow.Hour()
	// 如果启用了每天轮换，并且日志文件的最后修改时间与当前时间在不同的日期
	rotateEachDayCheck := w.options.RotateEachDay && w.logfileTime.Day() != timeNow.Day()

	// 如果满足任一轮换条件，则进行轮换
	if filesizeCheck || filechangedateCheck || rotateEachHourCheck || rotateEachDayCheck {
		w.mutex.Lock()
		// 确保在轮换后解锁
		defer w.mutex.Unlock()
		w.Close()
		// 重命名并压缩旧日志
		w.renameAndCompressLogs()
		// 创建新的日志文件
		_ = w.newLogger()
	}
}


// NewFileWithRotation 返回一个新的文件写入器实例
func NewFileWithRotation(options *FileWithRotationOptions) (*FileWithRotation, error) {
	fwr := &FileWithRotation{
		options: options,
		mutex:   &sync.Mutex{},
	}
	// 如果启用轮换，启动一个监控协程
	if fwr.options.Rotate {
		go scheduler(time.NewTicker(options.Rotationcheck), fwr.checkAndRotate)
	}

	// 创建日志存储目录
	err := os.MkdirAll(fwr.options.Location, 0755)
	if err != nil {
		return nil, err
	}

	// 初始化日志文件
	err = fwr.newLoggerSync()
	if err != nil {
		return nil, err
	}

	return fwr, nil
}

// Write 将数据写入日志文件
func (w *FileWithRotation) Write(data []byte, level levels.Level) {
	w.mutex.Lock()
	defer w.mutex.Unlock()

	switch level {
	case levels.LevelSilent:
		_, err := w.logFile.Write(data)
		if err != nil {
			return
		}

		_, err = w.logFile.Write([]byte("\n"))
		if err != nil {
			return
		}

	default:
		_, err := w.logFile.Write(data)
		if err != nil {
			return
		}
		_, err = w.logFile.Write([]byte("\n"))
		if err != nil {
			return
		}
	}
}
