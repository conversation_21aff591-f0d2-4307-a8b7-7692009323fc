// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-17 17:15:20
// FilePath: /yaml_scan/pkg/utils/yaml/yaml_decode.go
// Description: 
package yaml

// DecodeAndValidate is a wrapper for yaml Decode adding struct validation
func DecodeAndValidate(r io.Reader, v interface{}) error {
	if err := yaml.NewDecoder(r).Decode(v); err != nil {
		return err
	}
	if validate == nil {
		validate = validator.New()
	}

	if err := validate.Struct(v); err != nil {
		if _, ok := err.(*validator.InvalidValidationError); ok {
			return err
		}
		errs := []string{}
		for _, err := range err.(validator.ValidationErrors) {
			errs = append(errs, err.Namespace()+": "+err.Tag())
		}
		return errors.Wrap(errors.New(strings.Join(errs, ", ")), "validation failed for these fields")
	}
	return nil
}

