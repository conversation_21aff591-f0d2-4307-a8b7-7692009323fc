//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-02 17:15:27
//FilePath: /yaml_scan/utils/rand/randomip_test.go
//Description:

package rand

import (
	"net"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestGetRandomIP 测试 getRandomIP 函数的正确性
func TestGetRandomIP(t *testing.T) {
	_, ipnet, err := net.ParseCIDR("***********/24") // 解析 CIDR
	require.NoError(t, err)                          // 确保没有错误

	randomIP := getRandomIP(ipnet, 4) // 生成随机的 IPv4 地址

	// 检查生成的 IP 是否在指定的网络范围内
	require.True(t, ipnet.Contains(randomIP), "Generated IP should be within the CIDR range")
}

// TestGetRandomIPWithCidr 测试 GetRandomIPWithCidr 函数的正确性
func TestGetRandomIPWithCidr(t *testing.T) {
	cidrs := []string{
		"***********/24", // 有效的 IPv4 CIDR
		"10.0.0.0/8",     // 有效的 IPv4 CIDR
		"2001:db8::/32",  // 有效的 IPv6 CIDR
	}

	for _, cidr := range cidrs {
		randomIP, err := GetRandomIPWithCidr(cidr)                                             // 调用 GetRandomIPWithCidr 函数
		require.NoError(t, err)                                                                // 确保没有错误
		require.True(t, net.ParseIP(randomIP.String()) != nil, "Generated IP should be valid") // 确保生成的 IP 是有效的
	}
}
