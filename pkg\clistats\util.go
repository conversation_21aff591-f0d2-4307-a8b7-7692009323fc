// Author: chenjb
// Version: V1.0
// Date: 2025-06-18 17:06:47
// FilePath: /yaml_scan/pkg/clistats/util.go
// Description:
package clistats

import (
	"fmt"
	"strconv"
	"time"
)

// String 返回各种简单类型的字符串表示形式，可用于静态指标展示
//
// 此函数针对不同的基本类型提供了专门的格式化处理，对于不支持的类型使用fmt.Sprint。
// 强烈建议只使用简单易于表示的类型。
//
// @param from interface{}: 需要转换为字符串的值，可以是多种类型
// @return string string:  转换后的字符串表示
func String(from interface{}) string {
	// 特殊处理nil值
	if from == nil {
		return "n/a"
	}

	// 根据不同类型进行专门处理
	switch T := from.(type) {
	case string:
		return T // 字符串类型直接返回
	case fmt.Stringer:
		return T.String() // 实现了Stringer接口的类型调用String方法
	case bool:
		return strconv.FormatBool(T) // 布尔类型转换为"true"或"false"
	case int:
		return strconv.FormatInt(int64(T), 10) // 整数转为十进制字符串
	case int32:
		return strconv.FormatInt(int64(T), 10) // 32位整数转为十进制字符串
	case int64:
		return strconv.FormatInt(T, 10) // 64位整数转为十进制字符串
	case uint32:
		return strconv.FormatUint(uint64(T), 10) // 32位无符号整数转为十进制字符串
	case uint64:
		return strconv.FormatUint(T, 10) // 64位无符号整数转为十进制字符串
	case float32:
		return strconv.FormatFloat(float64(T), 'E', -1, 32) // 32位浮点数转为科学计数法
	case float64:
		return strconv.FormatFloat(T, 'E', -1, 64) // 64位浮点数转为科学计数法
	case []byte:
		return string(T) // 字节切片转为字符串
	case *[]byte:
		return string(*T) // 字节切片指针转为字符串
	case *string:
		return *T // 字符串指针取值
	default:
		return fmt.Sprintf("%v", from) // 其他类型使用通用格式化
	}
}

// FmtDuration 格式化持续时间为小时:分钟:秒格式
// 将time.Duration类型格式化为"小时:分钟:秒"的形式，如"1:23:45"表示1小时23分钟45秒
// @param d time.Duration: 需要格式化的持续时间
// @return string string: 格式化后的时间字符串，格式为"H:MM:SS"
func FmtDuration(d time.Duration) string {
	d = d.Round(time.Second)  // 四舍五入到秒级
	h := d / time.Hour        // 计算小时部分
	d -= h * time.Hour        // 减去小时部分
	m := d / time.Minute      // 计算分钟部分
	d -= m * time.Minute      // 减去分钟部分
	s := d / time.Second      // 计算秒部分
	return fmt.Sprintf("%d:%02d:%02d", h, m, s)  // 格式化为"H:MM:SS"形式
}
