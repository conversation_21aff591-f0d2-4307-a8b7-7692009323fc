// Package output 提供TLS扫描结果的输出处理功能
package output

import (
	"bytes"
	"fmt"
	"os"
	"regexp"
	"strings"
	"sync"
	"yaml_scan/pkg/tlsx/tlsx/clients"
	mapsutil "yaml_scan/utils/maps"

	jsoniter "github.com/json-iterator/go"
	"github.com/logrusorgru/aurora"
	"golang.org/x/exp/maps"
	errorutil "yaml_scan/utils/errors"
)

var (
	// globalDedupe 全局域名去重映射表
	// 用于在使用`-dns`标志时显示唯一域名，避免重复输出
	// TLS JSON结构已经包含每个证书的唯一域名
	// 此全局去重主要用于CLI模式下的多输入场景
	// 例如：google.com和youtube.com可能有相同的通配符证书或重叠域名
	globalDedupe = mapsutil.NewSyncLockMap[string, struct{}]()
)

// Writer 输出写入器接口
type Writer interface {
	// Close 关闭输出写入器接口
	Close() error
	// Write 将TLS扫描响应事件写入到文件和/或屏幕
	Write(*clients.Response) error
}

// decolorizerRegex 用于移除ANSI颜色代码的正则表达式
// 在写入文件时需要移除颜色代码，保持文件内容的纯文本格式
var decolorizerRegex = regexp.MustCompile(`\x1B\[[0-9;]*[a-zA-Z]`)

// StandardWriter 标准输出写入器结构
type StandardWriter struct {
	json        bool          // 是否使用JSON格式输出
	aurora      aurora.Aurora // 颜色高亮处理器，用于终端彩色输出
	outputFile  *fileWriter   // 文件输出写入器，可选
	outputMutex *sync.Mutex   // 输出互斥锁，确保并发安全

	options *clients.Options // 扫描配置选项，控制输出内容和格式
}

// New 创建新的输出写入器实例
// 根据配置选项初始化输出写入器，支持屏幕和文件输出
// @param options *clients.Options: TLS扫描的配置选项，包含输出格式和文件路径等设置
// @return Writer Writer: 输出写入器接口实例
// @return error error: 
func New(options *clients.Options) (Writer, error) {
	var outputFile *fileWriter

	// 如果指定了输出文件，创建文件写入器
	if options.OutputFile != "" {
		output, err := newFileOutputWriter(options.OutputFile)
		if err != nil {
			return nil, errorutil.NewWithErr(err).Msgf("could not create output file")
		}
		outputFile = output
	}

	// 创建标准写入器实例
	writer := &StandardWriter{
		json:        options.JSON,
		aurora:      aurora.NewAurora(!options.NoColor),
		outputFile:  outputFile,
		outputMutex: &sync.Mutex{},
		options:     options,
	}
	return writer, nil
}

// Write 将TLS扫描响应事件写入到文件和/或屏幕
// 该方法是输出处理的核心，负责格式化数据并输出到指定目标
// @receiver w 
// @param event *clients.Response: TLS扫描响应事件，包含证书信息、连接详情等
// @return error error: 写入过程中的错误，成功时为nil
func (w *StandardWriter) Write(event *clients.Response) error {
	var data []byte
	var err error

	// 根据配置选择输出格式
	if w.json {
		data, err = w.formatJSON(event)
	} else {
		data, err = w.formatStandard(event)
	}
	if err != nil {
		return errorutil.NewWithErr(err).Msgf("could not format output")
	}

	// 移除末尾换行符，统一处理换行
	data = bytes.TrimSuffix(data, []byte("\n"))
	if len(data) == 0 {
		// 当使用-dns标志且两个域名有相同证书时会发生去重，导致空数据
		return nil
	}

	// 使用互斥锁确保并发输出安全
	w.outputMutex.Lock()
	defer w.outputMutex.Unlock()

	// 输出到标准输出（屏幕）
	_, _ = os.Stdout.Write(data)
	_, _ = os.Stdout.Write([]byte("\n"))
	if w.outputFile != nil {
		// 非JSON格式需要移除颜色代码，保持文件内容为纯文本
		if !w.json {
			data = decolorizerRegex.ReplaceAll(data, []byte(""))
		}
		if writeErr := w.outputFile.Write(data); writeErr != nil {
			return errorutil.NewWithErr(err).Msgf("could not write to output")
		}
	}
	return nil
}

// Close 关闭输出写入器
// 释放相关资源，主要是关闭文件写入器
func (w *StandardWriter) Close() error {
	var err error
	if w.outputFile != nil {
		err = w.outputFile.Close()
	}
	return err
}

// formatJSON 将输出格式化为JSON格式
// @receiver w 
// @param output *clients.Response: TLS扫描响应结构
// @return []byte []byte: JSON格式的字节数据
// @return error error: 
func (w *StandardWriter) formatJSON(output *clients.Response) ([]byte, error) {
	return jsoniter.Marshal(output)
}

// formatStandard  将输出格式化为标准客户端格式
// 根据配置选项生成用户友好的文本输出，支持颜色高亮
// @receiver w 
// @param output *clients.Response: LS扫描响应结构
// @return []byte []byte: 格式化后的文本数据
// @return error error: 
func (w *StandardWriter) formatStandard(output *clients.Response) ([]byte, error) {
	if output == nil {
		return nil, errorutil.New("empty certificate response")
	}

	if output.CertificateResponse == nil {
		return nil, errorutil.New("empty leaf certificate")
	}
	cert := output.CertificateResponse
	builder := &bytes.Buffer{}

	// DNS模式：仅显示证书中的域名列表
	if w.options.DisplayDns {
		for _, hname := range cert.Domains {
			// 使用全局去重避免重复域名输出
			if _, ok := globalDedupe.Get(hname); ok {
				continue
			}
			_ = globalDedupe.Set(hname, struct{}{})
			builder.WriteString(hname)
			builder.WriteString("\n")
		}
		outputdata := builder.Bytes()
		return outputdata, nil
	}

	// 构建输出前缀：主机名和端口信息
	if !w.options.RespOnly {
		builder.WriteString(output.Host)
		builder.WriteString(":")
		builder.WriteString(output.Port)

		// 多IP扫描模式下显示实际连接的IP地址
		if (w.options.ScanAllIPs || len(w.options.IPVersion) > 0) && output.IP != "" {
			builder.WriteString(" (")
			builder.WriteString(output.IP)
			builder.WriteString(")")
		}
	}

	// 保存输出前缀	
	outputPrefix := builder.String()
	// 重置缓冲区用于后续内容
	builder.Reset()

	// 处理证书名称输出（SAN和CN）
	var names []string
	if w.options.SAN {
		// 添加主题备用名称（Subject Alternative Names）
		names = append(names, cert.SubjectAN...)
	}
	if w.options.CN {
		// 添加通用名称（Common Name）
		names = append(names, cert.SubjectCN)
	}

	// 对证书名称进行去重和规范化处理
	uniqueNames := uniqueNormalizeCertNames(names)
	if len(uniqueNames) > 0 {
		for _, name := range uniqueNames {
			if w.options.RespOnly {
				// 仅响应模式：只输出名称，不包含前缀
				builder.WriteString(name)
				builder.WriteString("\n")
			} else {
				// 标准模式：包含前缀和颜色高亮
				builder.WriteString(outputPrefix)
				builder.WriteString(" [")
				builder.WriteString(w.aurora.Cyan(name).String())
				builder.WriteString("]\n")
			}
		}
	}

	// 基础输出：当没有特殊显示选项时，显示基本的主机端口信息
	if !w.options.SAN && !w.options.CN && !w.options.TlsCiphersEnum {
		builder.WriteString(outputPrefix)
	}

	// 探测状态显示
	if !output.ProbeStatus {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Red("failed").String())
		builder.WriteString("]")
	}

	// 服务器名称指示（SNI）显示
	if w.options.ServerName != nil {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Blue(output.ServerName).String())
		builder.WriteString("]")
	}

	// 证书主题组织信息显示
	if w.options.SO && len(cert.SubjectOrg) > 0 {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.BrightYellow(strings.Join(cert.SubjectOrg, ",")).String())
		builder.WriteString("]")
	}

	// TLS版本显示
	if w.options.TLSVersion {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Blue(strings.ToUpper(output.Version)).String())
		builder.WriteString("]")
	}
	// 密码套件显示
	if w.options.Cipher {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Green(output.Cipher).String())
		builder.WriteString("]")
	}

	// 过期证书标识
	if w.options.Expired && cert.Expired {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Red("expired").String())
		builder.WriteString("]")
	}

	// 自签名证书标识
	if w.options.SelfSigned && cert.SelfSigned {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Yellow("self-signed").String())
		builder.WriteString("]")
	}

	// 主机名不匹配标识
	if w.options.MisMatched && cert.MisMatched {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Yellow("mismatched").String())
		builder.WriteString("]")
	}

	// 已撤销证书标识
	if w.options.Revoked && cert.Revoked {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Red("revoked").String())
		builder.WriteString("]")
	}

	// 不受信任证书标识
	if w.options.Untrusted && cert.Untrusted {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Yellow("untrusted").String())
		builder.WriteString("]")
	}

	// 通配符证书标识
	if w.options.WildcardCertCheck && cert.WildCardCert {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Yellow("wildcard").String())
		builder.WriteString("]")
	}

	// 证书序列号显示
	if w.options.Serial {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.BrightCyan(cert.Serial).String())
		builder.WriteString("]")
	}

	// 证书指纹哈希显示
	if w.options.Hash != "" {
		hashOpts := strings.Split(w.options.Hash, ",")

		for _, hash := range hashOpts {
			var value string
			builder.WriteString(" [")

			// 根据指定的哈希类型选择相应的指纹值
			switch hash {
			case "md5":
				value = cert.FingerprintHash.MD5
			case "sha1":
				value = cert.FingerprintHash.SHA1
			case "sha256":
				value = cert.FingerprintHash.SHA256
			}
			builder.WriteString(w.aurora.BrightMagenta(value).String())
			builder.WriteString("]")
		}
	}

	// JARM指纹显示（TLS服务器指纹识别）
	if w.options.Jarm && output.JarmHash != "" {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Magenta(output.JarmHash).String())
		builder.WriteString("]")
	}

	// JA3S指纹显示（TLS服务器响应指纹识别）
	if w.options.Ja3 && output.Ja3Hash != "" {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Magenta(output.Ja3Hash).String())
		builder.WriteString("]")
	}
	
	if w.options.Ja3s && output.Ja3sHash != "" {
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Magenta(output.Ja3sHash).String())
		builder.WriteString("]")
	}

		// 密码套件枚举结果显示
	if w.options.TlsCiphersEnum {
		for _, v := range output.TlsCiphers {
			ct := v.Ciphers.ColorCode(w.aurora)
			all := []string{}
			all = append(all, ct.Insecure...)
			all = append(all, ct.Weak...)
			all = append(all, ct.Secure...)
			all = append(all, ct.Unknown...)
			if len(all) > 0 {
				builder.WriteString(outputPrefix)
				builder.WriteString(fmt.Sprintf(" [%v] [%v]\n", w.aurora.Magenta(v.Version), strings.Join(all, ",")))
			}
		}
	} else if w.options.TlsVersionsEnum {
		// TLS版本枚举结果显示
		builder.WriteString(" [")
		builder.WriteString(w.aurora.Magenta(strings.Join(output.VersionEnum, ",")).String())
		builder.WriteString("]")
	}

	outputdata := builder.Bytes()
	return outputdata, nil
}

// uniqueNormalizeCertNames 从证书备用名称中移除通配符并去重
// 该函数处理证书中的域名列表，移除通配符前缀并返回唯一的域名列表
// @param names []string: 证书中的域名列表，可能包含通配符域名
// @return []string []string: 重和规范化后的域名列表
// 处理逻辑:
//   - 移除通配符前缀"*."（如*.example.com -> example.com）
//   - 使用map进行去重处理
//   - 返回唯一的域名列表
func uniqueNormalizeCertNames(names []string) []string {
	unique := make(map[string]struct{})
	for _, value := range names {
		// 移除通配符前缀"*."
		replaced := strings.Replace(value, "*.", "", -1)
		unique[replaced] = struct{}{}
	}
	return maps.Keys(unique)
}
