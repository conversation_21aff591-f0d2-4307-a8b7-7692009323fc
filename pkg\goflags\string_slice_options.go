package goflags


var StringSliceOptions = Options{}

// FileNormalizedStringSliceOptions 表示一个路径项的列表
// Tokenization: Comma 输入字符串中的路径项将通过逗号进行分割
// Normalization: Standard 对路径项进行统一处理，将其转换为小写
// Type: []string 存储多个字符串
// Example: -flag /value/1 -flag value2 => {"/value/1", "value2"}
var FileNormalizedStringSliceOptions = Options{
	IsEmpty:    isEmpty,  // 检查字符串切片是否为空的函数
	Normalize:  normalizeLowercase,  // 将字符串转换为小写并去除两端空白和引号的函数
	IsFromFile: isFromFile,  // 检查给定字符串是否表示有效文件路径的函数 恒为true
}


// FileStringSliceOptions 表示存储在文件中的项的列表 标准化
// Tokenization: Standard输入字符串将按照标准方式进行分割
// Normalization: Standard 对字符串进行统一处理
var FileStringSliceOptions = Options{
	IsEmpty:    isEmpty,  // 检查字符串切片是否为空的函数
	Normalize:  normalizeTrailingParts,   //标准化字符串的函数，去除两端空白字符
	IsFromFile: isFromFile,  // 检查给定字符串是否表示有效文件路径的函数
	IsRaw:      func(s string) bool { return true },  // 原始字符串检查函数，始终返回 true
}


// FileCommaSeparatedStringSliceOptions  表示包含以逗号分隔的项的文件列表
// Tokenization: Comma 输入字符串中的项将通过逗号进行分割
// Normalization: None 不进行任何标准化处理。读取的字符串将保持原样
// Type: []string  它将存储多个字符串
// test.txt content:
// value1
// value2
//
// Example: -flag test.txt => {"value1", "value2"}
var FileCommaSeparatedStringSliceOptions = Options{
	IsEmpty:    isEmpty,  // 检查字符串切片是否为空的函数
	IsFromFile: isFromFile,   // 检查字符串是否表示有效文件路径的函数
}


// CommaSeparatedStringSliceOptions 表示一个由逗号分隔的字符串列表
// Tokenization: 逗号
// Normalization: 无
// Type: []string
// 示例: -flag value1,value2 => {"value1", "value2"}
var CommaSeparatedStringSliceOptions = Options{
	IsEmpty: isEmpty,
}