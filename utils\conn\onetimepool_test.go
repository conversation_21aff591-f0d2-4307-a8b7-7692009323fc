package connpool

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestNewOneTimePool 测试创建 OneTimePool 实例。
func TestNewOneTimePool(t *testing.T) {
	ctx := context.Background() // 创建Background上下文
	address := "localhost:8080" // 设置目标地址
	poolSize := 5               // 设置连接池大小

	pool, err := NewOneTimePool(ctx, address, poolSize) // 创建连接池
	require.NoError(t, err) // 确保没有错误发生
	require.NotNil(t, pool) // 确保连接池实例不为 nil
	require.Equal(t, address, pool.address) // 确保地址正确
	require.Equal(t, poolSize, cap(pool.idleConnections)) // 确保连接池大小正确
}


// TestAcquire 测试从连接池中获取连接。
func TestAcquire(t *testing.T) {
	ctx := context.Background() // 创建Background上下文
	address := "localhost:8080" // 设置目标地址
	pool, err := NewOneTimePool(ctx, address, 5) // 创建连接池
	require.NoError(t, err) // 确保没有错误发生

	mockConn := &MockConn{} // 创建模拟连接
	pool.idleConnections <- mockConn // 手动将连接放入空闲连接通道

	conn, err := pool.Acquire(ctx) // 从连接池获取连接
	require.NoError(t, err) // 确保没有错误发生
	require.Equal(t, mockConn, conn) // 确保获取的连接是模拟连接

	// 测试获取连接时上下文取消
	cancelCtx, cancel := context.WithCancel(ctx) // 创建可取消的上下文
	cancel() // 取消上下文
	conn, err = pool.Acquire(cancelCtx) // 尝试从连接池获取连接
	require.Error(t, err) // 确保发生错误
}

// TestRun 测试 Run 方法。
func TestRun(t *testing.T) {
	ctx, cancel := context.WithCancel(context.Background()) // 创建可取消的上下文
	defer cancel() // 确保在测试结束时取消上下文

	address := "localhost:8080" // 设置目标地址
	pool, err := NewOneTimePool(ctx, address, 5) // 创建连接池
	require.NoError(t, err) // 确保没有错误发生

	pool.Dialer = nil // 设置连接池的拨号器

	go func() {
		err := pool.Run() // 启动连接池
		require.NoError(t, err) // 确保没有错误发生
	}()

	time.Sleep(2 * time.Millisecond) // 等待连接创建

	// 测试获取连接
	conn, err := pool.Acquire(ctx) // 从连接池获取连接
	require.NoError(t, err) // 确保没有错误发生
	require.NotNil(t, conn) // 确保获取的连接不为 nil

	// 关闭连接池
	err = pool.Close() // 关闭连接池
	require.NoError(t, err) // 确保没有错误发生
	require.True(t, true, "Expected connection to be closed") // 确保连接已关闭
}