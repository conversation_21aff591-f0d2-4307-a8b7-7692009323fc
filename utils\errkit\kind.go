// Author: chenjb
// Version: V1.0
// Date: 2025-05-20 19:13:06
// FilePath: /yaml_scan/utils/errkit/kind.go
// Description: 定义了错误类型(ErrKind)及其相关方法，提供了错误分类和层次结构
package errkit

import (
	"context"
	"errors"
	"os"
	"strings"

	"golang.org/x/exp/maps"
)

var (
	// ErrKindNetworkTemporary 表示与网络操作相关的临时错误
	// 这些错误可能通过使用指数退避重试操作来解决
	// 例如：等待头部超时、I/O超时等
	ErrKindNetworkTemporary = NewPrimitiveErrKind("network-temporary-error", "temporary network error", isNetworkTemporaryErr)
	// ErrKindNetworkPermanent 表示与网络操作相关的永久性错误
	// 这些错误可能无法通过重试解决，需要手动干预
	// 例如：找不到主机地址
	ErrKindNetworkPermanent = NewPrimitiveErrKind("network-permanent-error", "permanent network error", isNetworkPermanentErr)
	// ErrKindDeadline 表示逻辑操作中的超时错误
	// 这些是本身设置的自定义截止时间，防止无限挂起
	// 大多数情况下是服务器端问题（例如：服务器连接但完全不响应）
	// 需要手动干预
	ErrKindDeadline = NewPrimitiveErrKind("deadline-error", "deadline error", isDeadlineErr)
	// ErrKindUnknown 表示未知的错误类型
	// 尚未实现，作为转换slog项时的后备选项
	ErrKindUnknown = NewPrimitiveErrKind("unknown-error", "unknown error", nil)
)

var (
	// DefaultErrorKinds 是分类中使用的默认错误类型
	// 如果打算添加更多默认错误类型，必须在该包的init()函数中完成
	// 以避免竞态条件
	DefaultErrorKinds = []ErrKind{
		ErrKindNetworkTemporary,
		ErrKindNetworkPermanent,
		ErrKindDeadline,
	}
)

// ErrKind 是表示错误类型的接口
type ErrKind interface {
	// Is 检查当前错误类型是否与给定错误类型相同
	Is(ErrKind) bool
	// IsParent 检查当前错误类型是否是给定错误类型的父类型
	// 这允许错误的层次分类和应用程序特定处理
	IsParent(ErrKind) bool
	// Represents 检查给定错误是否属于此类型
	Represents(*ErrorX) bool
	// Description 返回错误类型的预定义描述
	// 可用于在出错时向用户显示友好的错误消息
	Description() string
	// String 返回错误类型的字符串表示
	String() string
}

// primitiveErrKind 是用于分类的错误类型
type primitiveErrKind struct {
	id         string             // 错误类型的唯一标识符
	info       string             // 错误类型的描述信息
	represents func(*ErrorX) bool // 用于判断错误是否属于此类型的函数
}

// Is 检查当前错误类型是否与给定错误类型相同
// @receiver e 
// @param kind ErrKind: 给定的错误类型
// @return bool bool: 
func (e *primitiveErrKind) Is(kind ErrKind) bool {
	return e.id == kind.String()
}

// 检查当前错误类型是否是给定错误类型的父类型
// 对于基本错误类型，始终返回false
func (e *primitiveErrKind) IsParent(kind ErrKind) bool {
	return false
}

// Represents 检查给定错误是否属于此类型
func (e *primitiveErrKind) Represents(err *ErrorX) bool {
	if e.represents != nil {
		return e.represents(err)
	}
	return false
}

// String 返回错误类型的字符串表示（即其ID）
func (e *primitiveErrKind) String() string {
	return e.id
}

// Description 返回错误类型的描述信息
func (e *primitiveErrKind) Description() string {
	return e.info
}

// NewPrimitiveErrKind 创建新的基本错误类型
func NewPrimitiveErrKind(id string, info string, represents func(*ErrorX) bool) ErrKind {
	p := &primitiveErrKind{id: id, info: info, represents: represents}
	return p
}

// isNetworkTemporaryErr 检查给定错误是否是临时网络错误
func isNetworkTemporaryErr(err *ErrorX) bool {
	if err.Cause() != nil {
		return os.IsTimeout(err.Cause())
	}
	v := err.Cause()
	switch {
	case os.IsTimeout(v):
		return true
	case strings.Contains(v.Error(), "Client.Timeout exceeded while awaiting headers"):
		return true
	}
	return false
}

// isNetworkPermanentErr 检查给定错误是否是永久性网络错误
func isNetworkPermanentErr(err *ErrorX) bool {
	if err.Cause() == nil {
		return false
	}
	v := err.Cause().Error()
	switch {
	case strings.Contains(v, "no address found"):
		return true
	case strings.Contains(v, "no such host"):
		return true
	case strings.Contains(v, "could not resolve host"):
		return true
	case strings.Contains(v, "port closed or filtered"):
		return true
	case strings.Contains(v, "connect: connection refused"):
		return true
	case strings.Contains(v, "Unable to connect"):
		// 使用HTTP(S)代理时发生
		return true
	case strings.Contains(v, "host unreachable"):
		// 使用SOCKS代理时发生
		return true
	}
	return false
}

// isDeadlineErr 检查给定错误是否是截止时间错误
func isDeadlineErr(err *ErrorX) bool {
	if err.Cause() == nil {
		return false
	}
	v := err.Cause()
	switch {
	case errors.Is(v, os.ErrDeadlineExceeded):
		return true
	case errors.Is(v, context.DeadlineExceeded):
		return true
	case errors.Is(v, context.Canceled):
		return true
	}

	return false
}

// multiKind 是包含多个错误类型的复合错误类型
type multiKind struct {
	kinds []ErrKind  // 存储包含的所有错误类型
}

// Is 检查当前复合错误类型是否包含给定错误类型
func (e *multiKind) Is(kind ErrKind) bool {
	for _, k := range e.kinds {
		if k.Is(kind) {
			return true
		}
	}
	return false
}

// IsParent 检查当前复合错误类型是否包含给定错误类型的父类型
func (e *multiKind) IsParent(kind ErrKind) bool {
	for _, k := range e.kinds {
		if k.IsParent(kind) {
			return true
		}
	}
	return false
}

// Represents 检查给定错误是否属于此复合类型中的任何一种
func (e *multiKind) Represents(err *ErrorX) bool {
	for _, k := range e.kinds {
		if k.Represents(err) {
			return true
		}
	}
	return false
}

// String 返回复合错误类型的字符串表示，用逗号连接所有包含的类型
func (e *multiKind) String() string {
	var str string
	for _, k := range e.kinds {
		str += k.String() + ","
	}
	return strings.TrimSuffix(str, ",")
}

// Description 返回复合错误类型的描述，包含所有包含类型的描述
func (e *multiKind) Description() string {
	var str string
	for _, k := range e.kinds {
		str += k.Description() + "\n"
	}
	return strings.TrimSpace(str)
}

// CombineErrKinds 将多个错误类型合并为一个错误类型
// 不推荐但在需要时可用
// 目前在ErrorX打印错误时使用
// 建议实现层次错误类型，而不是在errkit外部使用此功能
func CombineErrKinds(kind ...ErrKind) ErrKind {
	// 在合并时，还将子错误类型整合到父类型中
	// 但请注意，它目前不支持深度嵌套的子类型
	// 只能整合直接子类型
	f := &multiKind{}
	uniq := map[ErrKind]struct{}{}
	// 收集所有唯一错误类型
	for _, k := range kind {
		if k == nil || k.String() == "" {
			continue
		}
		if val, ok := k.(*multiKind); ok {
			for _, v := range val.kinds {
				uniq[v] = struct{}{}
			}
		} else {
			uniq[k] = struct{}{}
		}
	}
	all := maps.Keys(uniq)
	// 去除那些已有父类型的子类型
	for _, k := range all {
		for u := range uniq {
			if k.IsParent(u) {
				delete(uniq, u)
			}
		}
	}
	if len(uniq) > 1 {
		// 检查并删除未知错误类型
		for k := range uniq {
			if k.Is(ErrKindUnknown) {
				delete(uniq, k)
			}
		}
	}

	f.kinds = maps.Keys(uniq)
	// 限制错误类型数量
	if len(f.kinds) > MaxErrorDepth {
		f.kinds = f.kinds[:MaxErrorDepth]
	}
	// 如果只有一个类型，直接返回它
	if len(f.kinds) == 1 {
		return f.kinds[0]
	}
	return f
}

// GetErrorKind 从错误中返回第一个错误类型
// 可以将额外的错误类型作为可选参数传递
func GetErrorKind(err error, defs ...ErrKind) ErrKind {
	// 解析错误
	x := &ErrorX{}
	parseError(x, err)
	
	if x.kind != nil {
		if val, ok := x.kind.(*multiKind); ok && len(val.kinds) > 0 {
			// 如果是复合类型，返回第一个类型
			return val.kinds[0]
		}
		return x.kind
	}
	// 如果没有找到类型，返回默认错误类型
	// 尝试使用给定的defs判断
	for _, def := range defs {
		if def.Represents(x) {
			return def
		}
	}
	// 检查默认错误类型
	for _, def := range DefaultErrorKinds {
		if def.Represents(x) {
			return def
		}
	}
	return ErrKindUnknown
}

// GetAllErrorKinds 从错误中返回所有错误类型
// 除非有充分理由，否则不应使用此函数
func GetAllErrorKinds(err error, defs ...ErrKind) []ErrKind {
	kinds := []ErrKind{}
	// 解析错误
	x := &ErrorX{}
	parseError(x, err)
	if x.kind != nil {
		if val, ok := x.kind.(*multiKind); ok {
			// 如果是复合类型，添加所有包含的类型
			kinds = append(kinds, val.kinds...)
		} else {
			kinds = append(kinds, x.kind)
		}
	}
	// 检查是否匹配给定的defs
	for _, def := range defs {
		if def.Represents(x) {
			kinds = append(kinds, def)
		}
	}
	// 如果没有找到任何类型，返回未知类型
	if len(kinds) == 0 {
		kinds = append(kinds, ErrKindUnknown)
	}
	return kinds
}
