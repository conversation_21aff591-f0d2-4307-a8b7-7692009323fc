package levels

import "testing"

func TestString(t *testing.T) {
	test := []struct{
		level Level
		expected string
	}{
		{LevelFatal, "fatal"},
		{LevelSilent, "silent"},
		{LevelError, "error"},
		{LevelInfo, "info"},
		{LevelWarning, "warning"},
		{LevelDebug, "debug"},
		{LevelVerbose, "verbose"},
	}

	for _, test := range test {
		if result := test.level.String(); result != test.expected {
			t.Errorf("Level.String() = %v, want %v", result, test.expected)
		}
	}

}


func TestParseLevel(t *testing.T) {
	tests := []struct {
		input    string
		expected Level
		hasError bool
	}{
		{"fatal", LevelFatal, false},
		{"silent", LevelSilent, false},
		{"error", LevelError, false},
		{"info", LevelInfo, false},
		{"warning", LevelWarning, false},
		{"debug", LevelDebug, false},
		{"verbose", LevelVerbose, false},
		{"unknown", LevelInfo, true}, // 期望返回默认级别和错误
		{"", LevelInfo, true},         // 空字符串也应返回默认级别和错误
	}

	for _, test := range tests {
		result, err := ParseLevel(test.input)
		if (err != nil) != test.hasError {
			t.Errorf("ParseLevel(%q) error = %v, wantError %v", test.input, err, test.hasError)
		}
		if result != test.expected {
			t.Errorf("ParseLevel(%q) = %v, want %v", test.input, result, test.expected)
		}
	}
}