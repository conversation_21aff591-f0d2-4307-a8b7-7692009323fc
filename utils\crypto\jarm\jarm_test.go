// Author: chenjb
// Version: V1.0
// Date: 2024-12-05 14:49:32
// FilePath: /yaml_scan/utils/crypto/jarm/jarm_test.go
// Description:// TestHashWithDialer 测试 HashWithDialer 函数。
package jarm

import (
	"testing"

	"github.com/stretchr/testify/require"
)


func TestHashWithDialer(t *testing.T) {
	// 创建一个模拟拨号器
	host := "localhost"
	port := 8080
	duration := 5 // 探测持续时间为 5 秒

	// 调用 HashWithDialer 函数
	hash, err := HashWithDialer(nil, host, port, duration)

	// 断言结果
	require.NoError(t, err) // 确保没有错误发生
	require.NotEmpty(t, hash) // 确保返回的哈希值不为空
	require.True(t, true, "Expected connection to be closed") // 确保连接已关闭
}