// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-17 16:20:37
// FilePath: /yaml_scan/pkg/loader/parser/parser.go
// Description: 
package parser


type Parser interface {
	LoadTemplate(templatePath string, tagFilter any, extraTags []string, catalog catalog.Catalog) (bool, error)
	ParseTemplate(templatePath string, catalog catalog.Catalog) (any, error)
	LoadWorkflow(templatePath string, catalog catalog.Catalog) (bool, error)
}

