// 
// Author: chenjb
// Version: V1.0
// Date: 2025-07-01 14:38:24
// FilePath: /yaml_scan/pkg/tlsx/tlsx/clients/util_test.go
// Description: 
package clients

import (
	"context"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"crypto/x509/pkix"
	"errors"
	"math/big"
	"net"
	"testing"
	"time"
	"yaml_scan/pkg/fastdialer"

	"github.com/stretchr/testify/require"
)

// TestIntersectStringSlices 测试字符串切片交集计算功能
// 验证两个字符串切片的交集计算逻辑
func TestIntersectStringSlices(t *testing.T) {
	tests := []struct {
		name        string   // 测试用例名称
		s1          []string // 第一个切片
		s2          []string // 第二个切片
		expected    []string // 期望的交集结果
		description string   // 测试描述
	}{
		{
			name:        "正常交集",
			s1:          []string{"a", "b", "c", "d"},
			s2:          []string{"c", "d", "e", "f"},
			expected:    []string{"c", "d"},
			description: "应该返回两个切片的共同元素",
		},
		{
			name:        "无交集",
			s1:          []string{"a", "b", "c"},
			s2:          []string{"d", "e", "f"},
			expected:    []string{},
			description: "无共同元素时应该返回空切片",
		},
		{
			name:        "完全相同",
			s1:          []string{"a", "b", "c"},
			s2:          []string{"a", "b", "c"},
			expected:    []string{"a", "b", "c"},
			description: "完全相同的切片应该返回所有元素",
		},
		{
			name:        "一个为空",
			s1:          []string{"a", "b", "c"},
			s2:          []string{},
			expected:    []string{},
			description: "一个切片为空时应该返回空切片",
		},
		{
			name:        "都为空",
			s1:          []string{},
			s2:          []string{},
			expected:    []string{},
			description: "两个切片都为空时应该返回空切片",
		},
		{
			name:        "包含重复元素",
			s1:          []string{"a", "b", "b", "c"},
			s2:          []string{"b", "c", "c", "d"},
			expected:    []string{"b", "b", "c"},
			description: "应该正确处理重复元素",
		},
		{
			name:        "单元素交集",
			s1:          []string{"a"},
			s2:          []string{"a"},
			expected:    []string{"a"},
			description: "单元素相同时应该返回该元素",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IntersectStringSlices(tt.s1, tt.s2)
			
			// 验证结果长度
			require.Len(t, result, len(tt.expected), tt.description)
			
			// 验证结果内容（由于顺序可能不同，需要检查每个元素是否存在）
			for _, expected := range tt.expected {
				require.Contains(t, result, expected, "结果应该包含期望的元素: %s", expected)
			}
			
			// 验证结果中没有多余的元素
			for _, actual := range result {
				require.Contains(t, tt.expected, actual, "结果不应该包含意外的元素: %s", actual)
			}
			
			t.Logf("交集结果: %v", result)
		})
	}
}

// TestTrimWildcardPrefix 测试通配符前缀移除功能
// 验证域名通配符前缀的正确移除
func TestTrimWildcardPrefix(t *testing.T) {
	tests := []struct {
		name        string // 测试用例名称
		hostname    string // 输入的主机名
		expected    string // 期望的结果
		description string // 测试描述
	}{
		{
			name:        "通配符域名",
			hostname:    "*.example.com",
			expected:    "example.com",
			description: "应该移除通配符前缀",
		},
		{
			name:        "普通域名",
			hostname:    "www.example.com",
			expected:    "www.example.com",
			description: "普通域名应该保持不变",
		},
		{
			name:        "空字符串",
			hostname:    "",
			expected:    "",
			description: "空字符串应该保持不变",
		},
		{
			name:        "只有通配符",
			hostname:    "*.",
			expected:    "",
			description: "只有通配符前缀时应该返回空字符串",
		},
		{
			name:        "多级通配符",
			hostname:    "*.api.example.com",
			expected:    "api.example.com",
			description: "应该移除多级域名的通配符前缀",
		},
		{
			name:        "中间包含星号",
			hostname:    "api*.example.com",
			expected:    "api*.example.com",
			description: "中间的星号不应该被移除",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := trimWildcardPrefix(tt.hostname)
			require.Equal(t, tt.expected, result, tt.description)
		})
	}
}

// TestFormatToSerialNumber 测试证书序列号格式化功能
// 验证大整数序列号到十六进制字符串的转换
func TestFormatToSerialNumber(t *testing.T) {
	tests := []struct {
		name         string   // 测试用例名称
		serialNumber *big.Int // 输入的序列号
		expected     string   // 期望的格式化结果
		description  string   // 测试描述
	}{
		{
			name:         "nil序列号",
			serialNumber: nil,
			expected:     "",
			description:  "nil序列号应该返回空字符串",
		},
		{
			name:         "零值序列号",
			serialNumber: big.NewInt(0),
			expected:     "",
			description:  "零值序列号应该返回空字符串",
		},
		{
			name:         "小整数序列号",
			serialNumber: big.NewInt(255),
			expected:     "FF",
			description:  "小整数应该正确格式化",
		},
		{
			name:         "大整数序列号",
			serialNumber: big.NewInt(65535),
			expected:     "FF:FF",
			description:  "大整数应该用冒号分隔",
		},
		{
			name:         "单字节序列号",
			serialNumber: big.NewInt(16),
			expected:     "10",
			description:  "单字节序列号应该正确格式化",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := FormatToSerialNumber(tt.serialNumber)
			require.Equal(t, tt.expected, result, tt.description)
			
			// 验证格式（如果不为空）
			if result != "" {
				// 验证是否为大写十六进制
				for _, char := range result {
					require.True(t, 
						(char >= '0' && char <= '9') || 
						(char >= 'A' && char <= 'F') || 
						char == ':', 
						"结果应该只包含大写十六进制字符和冒号")
				}
				
				// 验证冒号分隔格式
				if len(result) > 2 {
					require.True(t, result[len(result)-1] != ':', "结果不应该以冒号结尾")
				}
			}
		})
	}
}

// TestGetUniqueDomainsFromCert 测试从证书中提取唯一域名功能
// 验证证书域名的提取和去重逻辑
func TestGetUniqueDomainsFromCert(t *testing.T) {
	tests := []struct {
		name        string                // 测试用例名称
		resp        *CertificateResponse  // 输入的证书响应
		description string                // 测试描述
	}{
		{
			name: "包含通配符和普通域名",
			resp: &CertificateResponse{
				SubjectCN: "*.example.com",
				SubjectAN: []string{"www.example.com", "api.example.com", "*.sub.example.com"},
			},
			description: "应该正确处理通配符和普通域名",
		},
		{
			name: "只有主题通用名称",
			resp: &CertificateResponse{
				SubjectCN: "www.example.com",
				SubjectAN: []string{},
			},
			description: "只有CN时应该返回该域名",
		},
		{
			name: "只有主题备用名称",
			resp: &CertificateResponse{
				SubjectCN: "",
				SubjectAN: []string{"www.example.com", "api.example.com"},
			},
			description: "只有SAN时应该返回所有SAN域名",
		},
		{
			name: "包含重复域名",
			resp: &CertificateResponse{
				SubjectCN: "example.com",
				SubjectAN: []string{"example.com", "www.example.com", "example.com"},
			},
			description: "应该去除重复的域名",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetUniqueDomainsFromCert(tt.resp)
			
			// 基本验证
			require.NotNil(t, result, "结果不应该为nil")
			
			// 验证去重效果
			uniqueMap := make(map[string]bool)
			for _, domain := range result {
				require.False(t, uniqueMap[domain], "结果中不应该有重复域名: %s", domain)
				uniqueMap[domain] = true
			}
			
			// 验证通配符前缀被移除
			for _, domain := range result {
				require.False(t, domain[:2] == "*.", "结果中不应该包含通配符前缀: %s", domain)
			}
			
			t.Logf("提取的唯一域名: %v", result)
		})
	}
}

// TestConvertx509toResponse 测试X.509证书转换为响应结构功能
// 验证证书信息的完整转换和状态检查
func TestConvertx509toResponse(t *testing.T) {
	// 创建测试证书
	cert := createTestX509Certificate(t)

	// 创建配置选项
	options := &Options{
		DisplayDns: true,
		Revoked:    false,
		HardFail:   false,
	}

	tests := []struct {
		name        string // 测试用例名称
		hostname    string // 主机名
		showcert    bool   // 是否显示证书
		description string // 测试描述
	}{
		{
			name:        "完整转换包含证书",
			hostname:    "test.example.com",
			showcert:    true,
			description: "应该包含完整的证书信息和PEM格式证书",
		},
		{
			name:        "转换不包含证书",
			hostname:    "test.example.com",
			showcert:    false,
			description: "应该包含证书信息但不包含PEM格式证书",
		},
		{
			name:        "空主机名",
			hostname:    "",
			showcert:    false,
			description: "空主机名应该正常处理",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := Convertx509toResponse(options, tt.hostname, cert, tt.showcert)

			// 基本验证
			require.NotNil(t, result, "结果不应该为nil")

			// 验证基本字段
			require.Equal(t, cert.Subject.CommonName, result.SubjectCN, "主题CN应该匹配")
			require.Equal(t, cert.Issuer.CommonName, result.IssuerCN, "颁发者CN应该匹配")
			require.Equal(t, cert.NotBefore, result.NotBefore, "生效时间应该匹配")
			require.Equal(t, cert.NotAfter, result.NotAfter, "过期时间应该匹配")

			// 验证状态检查
			require.Equal(t, IsExpired(cert.NotAfter), result.Expired, "过期状态应该匹配")
			require.Equal(t, IsSelfSigned(cert.AuthorityKeyId, cert.SubjectKeyId), result.SelfSigned, "自签名状态应该匹配")

			// 验证指纹哈希
			require.NotEmpty(t, result.FingerprintHash.MD5, "MD5指纹不应该为空")
			require.NotEmpty(t, result.FingerprintHash.SHA1, "SHA1指纹不应该为空")
			require.NotEmpty(t, result.FingerprintHash.SHA256, "SHA256指纹不应该为空")

			// 验证序列号
			require.NotEmpty(t, result.Serial, "序列号不应该为空")

			// 验证证书内容
			if tt.showcert {
				require.NotEmpty(t, result.Certificate, "应该包含PEM格式证书")
				require.Contains(t, result.Certificate, "BEGIN CERTIFICATE", "应该包含PEM开始标记")
			} else {
				require.Empty(t, result.Certificate, "不应该包含PEM格式证书")
			}

			// 验证域名提取（如果启用了DisplayDns）
			if options.DisplayDns {
				require.NotNil(t, result.Domains, "域名列表不应该为nil")
			}

			t.Logf("转换结果 - CN: %s, 过期: %v, 自签名: %v",
				result.SubjectCN, result.Expired, result.SelfSigned)
		})
	}
}

// createTestX509Certificate 创建用于测试的X.509证书
// 生成一个简单的自签名证书用于测试
func createTestX509Certificate(t *testing.T) *x509.Certificate {
	// 生成私钥
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	require.NoError(t, err, "生成私钥不应该出错")

	// 创建证书模板
	template := x509.Certificate{
		SerialNumber: big.NewInt(12345),
		Subject: pkix.Name{
			Organization:  []string{"Test Org"},
			Country:       []string{"US"},
			Province:      []string{"CA"},
			Locality:      []string{"Test City"},
			StreetAddress: []string{"123 Test St"},
			PostalCode:    []string{"12345"},
			CommonName:    "test.example.com",
		},
		Issuer: pkix.Name{
			Organization:  []string{"Test CA"},
			Country:       []string{"US"},
			CommonName:    "Test CA",
		},
		NotBefore:    time.Now().Add(-24 * time.Hour),
		NotAfter:     time.Now().Add(365 * 24 * time.Hour),
		KeyUsage:     x509.KeyUsageKeyEncipherment | x509.KeyUsageDigitalSignature,
		ExtKeyUsage:  []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth},
		DNSNames:     []string{"test.example.com", "www.test.example.com"},
		EmailAddresses: []string{"<EMAIL>"},
	}

	// 创建自签名证书
	certDER, err := x509.CreateCertificate(rand.Reader, &template, &template, &privateKey.PublicKey, privateKey)
	require.NoError(t, err, "创建证书不应该出错")

	// 解析证书
	cert, err := x509.ParseCertificate(certDER)
	require.NoError(t, err, "解析证书不应该出错")

	return cert
}

// TestGetConnWithIPVersion 测试IP版本相关的连接功能
// 验证IP版本配置对连接建立的影响
func TestGetConnWithIPVersion(t *testing.T) {
	// 创建快速拨号器
	dialer, err := fastdialer.NewDialer(fastdialer.DefaultOptions)
	require.NoError(t, err, "创建拨号器不应该出错")

	tests := []struct {
		name        string   // 测试用例名称
		hostname    string   // 主机名
		ip          string   // IP地址
		port        string   // 端口
		scanAllIPs  bool     // 是否扫描所有IP
		ipVersion   []string // IP版本配置
		description string   // 测试描述
	}{
		{
			name:        "启用ScanAllIPs使用IP连接",
			hostname:    "www.baidu.com",
			ip:          "",
			port:        "443",
			scanAllIPs:  true,
			ipVersion:   []string{},
			description: "启用ScanAllIPs时应该使用IP地址连接",
		},
		{
			name:        "默认配置使用主机名连接",
			hostname:    "www.baidu.com",
			ip:          "",
			port:        "443",
			scanAllIPs:  false,
			ipVersion:   []string{},
			description: "默认配置应该使用主机名连接",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建配置选项
			options := &Options{
				Timeout:    10,
				ScanAllIPs: tt.scanAllIPs,
				IPVersion:  tt.ipVersion,
				Fastdialer: dialer,
			}

			ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
			defer cancel()

			conn, err := GetConn(ctx, tt.hostname, tt.ip, tt.port, options)
			require.NoError(t, err, tt.description)
			require.NotNil(t, conn, "连接不应该为nil")

			// 关闭连接
			if conn != nil {
				conn.Close()
			}

			t.Logf("连接成功 - 配置: ScanAllIPs=%v, IPVersion=%v", tt.scanAllIPs, tt.ipVersion)
		})
	}
}

// TestGetConnTimeout 测试连接超时设置功能
// 验证连接超时的正确设置和处理
func TestGetConnTimeout(t *testing.T) {
	// 创建快速拨号器
	dialer, err := fastdialer.NewDialer(fastdialer.DefaultOptions)
	require.NoError(t, err, "创建拨号器不应该出错")

	tests := []struct {
		name        string // 测试用例名称
		timeout     int    // 超时设置
		description string // 测试描述
	}{
		{
			name:        "默认超时",
			timeout:     0,
			description: "超时为0时应该使用默认5秒超时",
		},
		{
			name:        "自定义超时",
			timeout:     15,
			description: "应该使用自定义的超时时间",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建配置选项
			options := &Options{
				Timeout:    tt.timeout,
				Fastdialer: dialer,
			}

			ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
			defer cancel()

			conn, err := GetConn(ctx, "www.baidu.com", "", "443", options)
			require.NoError(t, err, tt.description)
			require.NotNil(t, conn, "连接不应该为nil")

			// 验证超时设置
			expectedTimeout := tt.timeout
			if expectedTimeout == 0 {
				expectedTimeout = 5 // 默认超时
			}
			require.Equal(t, expectedTimeout, options.Timeout, "超时时间应该正确设置")

			// 关闭连接
			if conn != nil {
				conn.Close()
			}
		})
	}
}

// TestIsClientCertRequiredError 测试客户端证书要求错误检查功能
// 验证TLS客户端证书相关错误的识别
func TestIsClientCertRequiredError(t *testing.T) {
	tests := []struct {
		name        string // 测试用例名称
		err         error  // 输入的错误
		expected    bool   // 期望的结果
		description string // 测试描述
	}{
		{
			name:        "nil错误",
			err:         nil,
			expected:    false,
			description: "nil错误应该返回false",
		},
		{
			name:        "普通错误",
			err:         errors.New("connection failed"),
			expected:    false,
			description: "普通错误应该返回false",
		},
		{
			name: "bad certificate错误",
			err: &net.OpError{
				Op:  "remote error",
				Err: errors.New("tls: bad certificate"),
			},
			expected:    true,
			description: "bad certificate错误应该返回true",
		},
		{
			name: "certificate required错误",
			err: &net.OpError{
				Op:  "remote error",
				Err: errors.New("tls: certificate required"),
			},
			expected:    true,
			description: "certificate required错误应该返回true",
		},
		{
			name: "其他TLS错误",
			err: &net.OpError{
				Op:  "remote error",
				Err: errors.New("tls: handshake failure"),
			},
			expected:    false,
			description: "其他TLS错误应该返回false",
		},
		{
			name: "非remote error的OpError",
			err: &net.OpError{
				Op:  "dial",
				Err: errors.New("connection refused"),
			},
			expected:    false,
			description: "非remote error的OpError应该返回false",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsClientCertRequiredError(tt.err)
			require.Equal(t, tt.expected, result, tt.description)
		})
	}
}

// TestGetConn 测试网络连接建立功能
// 验证TCP连接的建立逻辑（使用真实网络地址）
func TestGetConn(t *testing.T) {
	// 创建快速拨号器
	dialer, err := fastdialer.NewDialer(fastdialer.DefaultOptions)
	require.NoError(t, err, "创建拨号器不应该出错")

	// 创建配置选项
	options := &Options{
		Timeout:    10,
		ScanAllIPs: false,
		Fastdialer: dialer,
	}

	tests := []struct {
		name        string // 测试用例名称
		hostname    string // 主机名
		ip          string // IP地址
		port        string // 端口
		expectError bool   // 是否期望出现错误
		description string // 测试描述
	}{
		{
			name:        "使用主机名连接",
			hostname:    "www.baidu.com",
			ip:          "",
			port:        "443",
			expectError: false,
			description: "使用主机名连接应该成功",
		},
		{
			name:        "无效的主机和IP",
			hostname:    "",
			ip:          "",
			port:        "443",
			expectError: true,
			description: "主机和IP都为空应该返回错误",
		},
		{
			name:        "无效的端口",
			hostname:    "www.baidu.com",
			ip:          "",
			port:        "",
			expectError: true,
			description: "端口为空应该返回错误",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
			defer cancel()

			conn, err := GetConn(ctx, tt.hostname, tt.ip, tt.port, options)

			if tt.expectError {
				require.Error(t, err, tt.description)
				require.Nil(t, conn, "出错时连接应该为nil")
			} else {
				require.NoError(t, err, tt.description)
				require.NotNil(t, conn, "连接不应该为nil")
				
				// 关闭连接
				if conn != nil {
					conn.Close()
				}
			}
		})
	}
}
