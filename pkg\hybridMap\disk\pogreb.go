// Author: chenjb
// Version: V1.0
// Date: 2025-05-16 09:59:48
// FilePath: /yaml_scan/pkg/hybridMap/disk/pogreb.go
// Description: 基于 Pogreb 的磁盘存储引擎，提供高性能的键值存储能力
package disk

import (
	"bytes"
	"strconv"
	"sync"
	"time"

	"github.com/akrylysov/pogreb"
)

var (
	// OpenPogrebDB 打开 PogrebDB 数据库的函数变量
	// 允许在运行时动态替换实现，方便测试和扩展
	OpenPogrebDB func(string) (DB, error)
)

// init 初始化函数，设置 OpenPogrebDB 的默认实现
func init() {
	OpenPogrebDB = openPogrebDB
}

// PogrebDB - 表示 Pogreb 数据库实现
// 使用 github.com/akrylysov/pogreb 库实现了 DB 接口
type PogrebDB struct {
	db *pogreb.DB
	sync.RWMutex
}

// openPogrebDB  打开指定路径的 Pogreb 数据库
// @param path string: 数据库存储路径
// @return DB DB: 数据库实例接口
// @return error error: 可能的错误
func openPogrebDB(path string) (DB, error) {
	// 使用默认配置打开数据库
	db, err := pogreb.Open(path, nil)
	if err != nil {
		return nil, err
	}

	pdb := new(PogrebDB)
	pdb.db = db

	return pdb, nil
}

// Size 返回数据库占用的字节数
// @receiver ldb
// @return int64 int64: 数据库大小，如果无法获取统计信息则返回0
func (pdb *PogrebDB) Size() int64 {
	size, err := pdb.db.FileSize()
	if err != nil {
		return 0
	}
	return size
}

// Close 关闭数据库连接并释放相关资源
func (pdb *PogrebDB) Close() {
	pdb.db.Close()
}

// GC - 运行垃圾回收，对数据库进行压缩以回收空间
func (pdb *PogrebDB) GC() error {
	_, err := pdb.db.Compact()
	return err
}

// Incr 将指定键的值增加指定数量
// @receiver ldb
// @param k string: 要增加值的键
// @param by int64: 要增加的数量
// @return int64 int64: 增加后的新值
// @return error error: 可能的错误
func (pdb *PogrebDB) Incr(k string, by int64) (int64, error) {
	// 使用互斥锁保证操作原子性
	pdb.Lock()
	defer pdb.Unlock()

	// 尝试获取当前值，如果不存在则使用空字节数组
	val, err := pdb.get(k)
	if err != nil {
		val = []byte{}
	}

	// 将字节数组转换为整数并增加指定值
	valFloat, _ := strconv.ParseInt(string(val), 10, 64)
	valFloat += by

	// 存储新值
	err = pdb.set([]byte(k), intToByteSlice(valFloat), -1)
	if err != nil {
		return 0, err
	}

	return valFloat, nil
}

// set 内部方法，设置键值对并处理过期时间
// @receiver ldb
// @param k []byte:  键名字节数组
// @param v []byte:  值字节数组
// @param ttl time.Duration:  过期时间，-1表示永不过期
// @return error error: 可能的错误
func (pdb *PogrebDB) set(k, v []byte, ttl time.Duration) error {
	var expires int64
	if ttl > 0 {
		expires = time.Now().Add(ttl).Unix()
	}
	// 将过期时间添加到值的前面，形如 "expireTime;actualValue"
	expiresBytes := append(intToByteSlice(expires), expSeparator[:]...)
	v = append(expiresBytes, v...)
	return pdb.db.Put(k, v)
}

// Set 设置键值对并处理过期时间
// @receiver ldb
// @param k string:  键名
// @param v []byte:  值字节数组
// @param ttl time.Duration:  过期时间，-1表示永不过期
// @return error error: 可能的错误
func (pdb *PogrebDB) Set(k string, v []byte, ttl time.Duration) error {
	return pdb.set([]byte(k), v, ttl)
}

// MSet 批量设置多个键值对
// @receiver ldb
// @param data map[string][]byte: 要设置的键值对映射
// @return error error: 可能的错误
// Pogreb 不支持批量操作，需要单独实现
func (pdb *PogrebDB) MSet(data map[string][]byte) error {
	return nil
}

// get 内部方法，获取指定键的值并处理过期逻辑
// @receiver pdb
// @param k string: 要获取的键名
// @return []byte []byte: 键对应的值（如果存在且未过期）
// @return error error: 可能的错误，如键不存在或已过期
func (pdb *PogrebDB) get(k string) ([]byte, error) {
	var data []byte
	var err error

	delete := false

	// 从数据库中读取原始数据
	item, err := pdb.db.Get([]byte(k))
	if err != nil {
		return []byte{}, err
	}

	// 如果没有找到数据
	if len(item) == 0 {
		return []byte{}, ErrNotFound
	}

	// 分离过期时间和实际数据
	parts := bytes.SplitN(item, []byte(expSeparator), 2)
	expires, actual := parts[0], parts[1]

	// 检查是否已过期
	if exp, _ := strconv.Atoi(string(expires)); exp > 0 && int(time.Now().Unix()) >= exp {
		delete = true
	}
	data = actual

	// 如果过期则删除该键
	if delete {
		errDelete := pdb.db.Delete([]byte(k))
		if errDelete != nil {
			return data, errDelete
		}
		return data, ErrNotFound
	}
	return data, nil
}

// Get 获取指定键的值
// @receiver ldb
// @param k string: 要获取的键名
// @return []byte []byte: 键对应的值（如果存在且未过期）
// @return error error: 可能的错误，如键不存在或已过期
func (pdb *PogrebDB) Get(k string) ([]byte, error) {
	return pdb.get(k)
}

// MGet 批量获取多个键的值
// @receiver ldb
// @param keys []string:  要获取的键名列表
// @return [] []: 对应的值列表，返回的数组内项的顺序与传入的keys一致
// 如果某个键不存在或已过期，对应位置为空字节数组
func (pdb *PogrebDB) MGet(keys []string) [][]byte {
	var data [][]byte
	for _, key := range keys {
		val, err := pdb.get(key)
		if err != nil {
			data = append(data, []byte{})
			continue
		}
		data = append(data, val)
	}
	return data
}

// TTL 返回指定键值对的剩余生存时间（秒）
// @receiver ldb
// @param key string:  要查询的键名
// @return int64 int64: 剩余生存时间（秒），-1表示永不过期，-2表示键不存在或已过期
func (pdb *PogrebDB) TTL(key string) int64 {
	// 获取键对应的原始值
	item, err := pdb.db.Get([]byte(key))
	if err != nil {
		return -2
	}

	// 提取过期时间
	parts := bytes.SplitN(item, []byte(expSeparator), 2)
	exp, _ := strconv.Atoi(string(parts[0]))
	if exp == 0 {
		return -1
	}

	// 计算剩余时间
	now := time.Now().Unix()
	if now >= int64(exp) {
		return -2
	}

	return int64(exp) - now
}

// MDel 批量删除多个键
// @receiver ldb
// @param keys []string: 要删除的键名列表
// @return error error: 可能的错误
// 不支持批量操作
func (pdb *PogrebDB) MDel(keys []string) error {
	return nil
}

// Del 删除指定的键
// @receiver ldb
// @param key string: 要删除的键名
// @return error error: 可能得错误
func (pdb *PogrebDB) Del(key string) error {
	return pdb.db.Delete([]byte(key))
}

// Scan 使用指定的处理函数遍历整个存储
// @receiver ldb
// @param scannerOpt ScannerOptions: 扫描选项，包含偏移量、前缀过滤、处理函数等
// @return error error: 可能的错误
func (pdb *PogrebDB) Scan(scannerOpt ScannerOptions) error {
	valid := func(k []byte) bool {
		if k == nil {
			return false
		}

		if scannerOpt.Prefix != "" && !bytes.HasPrefix(k, []byte(scannerOpt.Prefix)) {
			return false
		}

		return true
	}

	it := pdb.db.Items()
	for {
		key, val, err := it.Next()
		if err == pogreb.ErrIterationDone {
			break
		}
		if err != nil {
			return err
		}

		if !valid(key) {
			continue
		}


		parts := bytes.SplitN(val, []byte(expSeparator), 2)
		_, data := parts[0], parts[1]
		if scannerOpt.Handler(key, data) != nil {
			break // 如果处理函数返回错误，中断扫描
		}
	}

	return nil
}
