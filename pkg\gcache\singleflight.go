//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-17 19:15:58
//FilePath: /yaml_scan/pkg/gcache/singleflight.go
//Description: 重复函数调用抑制

package gcache

import (
	"sync"
)

// call 是一个正在进行或已完成的 Do 调用
type call[V any] struct {
	wg  sync.WaitGroup // 用于等待调用完成的 WaitGroup
	val V              // 调用的返回值
	err error          // 调用过程中发生的错误
}

// Group 结构体表示一类工作，并形成一个命名空间，
// 在该命名空间中可以执行带有重复抑制的任务单元。
type Group[K comparable, V any] struct {
	cache Cache[K, V]    // 关联的缓存实例
	mu    sync.Mutex     // 保护 m 的互斥锁
	m     map[K]*call[V] // 延迟初始化的映射，用于存储正在进行的调用

	nilV V // 用于快速返回泛型nil
}

// Do 执行给定的函数，并确保对于给定的键只有一个执行在进行中
// 如果有重复的调用，重复的调用者会等待原始调用完成并接收相同的结果。
// @param key: 键，用于标识任务 
// @param fn: 要执行的函数，返回值和错误
// @param isWait: 是否等待任务完成	
// @return V: 函数返回的值
// @return bool: 是否调用了 fn 函数
// @return error: 函数返回的错误
func (g *Group[K, V]) Do(key K, fn func() (V, error), isWait bool) (V, bool, error) {
	// 加锁以保护共享资源
	g.mu.Lock()
	// 尝试从缓存中获取值
	v, err := g.cache.get(key, true)
	// 如果缓存命中 返回值
	if err == nil {
		g.mu.Unlock()
		return v, false, nil
	}

	// 如果映射未初始化 初始化
	if g.m == nil {
		g.m = make(map[K]*call[V])
	}
	// 检查是否已有正在进行的调用
	if c, ok := g.m[key]; ok {
		g.mu.Unlock()
		// 如果不等待，返回未找到错误
		if !isWait {
			return g.nilV, false, KeyNotFoundError
		}
		// 等待调用完成
		c.wg.Wait()
		return c.val, false, c.err
	}

	// 创建新的 call 实例
	c := new(call[V])
	// 增加等待组计数
	c.wg.Add(1)
	
	g.m[key] = c
	g.mu.Unlock()

	// 如果不等待，异步执行调用
	if !isWait {
		go g.call(c, key, fn)
		return g.nilV, false, KeyNotFoundError
	}
	// 如果等待，执行调用并返回结果
	v, err = g.call(c, key, fn)
	return v, true, err
}

// call: 执行给定的函数并处理结果
func (g *Group[K, V]) call(c *call[V], key K, fn func() (V, error)) (V, error) {
	// 执行给定的函数并处理结果
	c.val, c.err = fn()
	// 标记调用完成
	c.wg.Done()

	// 保护共享资源，删除正在进行的调用
	g.mu.Lock()
	delete(g.m, key)
	g.mu.Unlock()

	return c.val, c.err
}
