//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-17 16:52:48
//FilePath: /yaml_scan/pkg/gcache/cache.go
//Description:

package gcache

import (
	"errors"
	"time"
)

const (
	TYPE_SIMPLE = "simple" //简单缓存类型
	TYPE_LRU    = "lru"    // LRU (Least Recently Used) 缓存类型
	TYPE_LFU    = "lfu"    // LFU (Least Frequently Used) 缓存类型
	TYPE_ARC    = "arc"    // ARC (Adaptive Replacement Cache) 缓存类型
)

// KeyNotFoundError 缓存中未找到指定键的错误。
var KeyNotFoundError = errors.New("Key not found.")

// Cache 是一个泛型缓存接口，定义了缓存的基本操作。
// K 代表键的类型，必须是可比较的。
// V 代表值的类型可以是任意类型。
type Cache[K comparable, V any] interface {
	// Set 插入或更新指定的键值对。
	Set(key K, value V) error

	// SetWithExpire 插入或更新指定的键值对，并设置一个过期时间。
	SetWithExpire(key K, value V, expiration time.Duration) error

	// Get 返回指定键的值（如果存在于缓存中）。
	// 如果键不存在且缓存配置了 LoaderFunc，则调用 LoaderFunc 加载数据并插入缓存。
	// 如果键不存在且缓存未配置 LoaderFunc，则返回 KeyNotFoundError。
	Get(key K) (V, error)

	// GetIFPresent 返回指定键的值（如果存在于缓存中）。
	// 如果键不存在且缓存配置了 LoaderFunc，则调用 LoaderFunc 加载数据 异步执行
	GetIFPresent(key K) (V, error)

	// GetAll 返回一个包含缓存中所有键值对的映射。
	// checkExpired 参数决定是否在返回前检查并移除已过期的键值对。
	GetALL(checkExpired bool) map[K]V

	// get 是内部方法，用于获取值。
	// enableCount 是否进行命中未命中次数统计
	// 该方法通常由 Get 和 GetIFPresent 内部使用。
	get(key K, enableCount bool) (V, error)

	// Remove 从缓存中移除指定的键。
	// 如果键存在并成功移除，返回 true；否则返回 false。
	Remove(key K) bool

	// Purge 清空缓存中的所有键值对。
	Purge()

	// Keys 返回一个包含缓存中所有键的切片。
	// checkExpired 参数决定是否在返回前检查并移除已过期的键值对。.
	Keys(checkExpired bool) []K

	// Len 返回缓存中当前存储的项的数量。
	// checkExpired 参数决定是否在计算数量前检查并移除已过期的键值对。
	Len(checkExpired bool) int

	// Has 返回缓存中是否存在指定的键。
	Has(key K) bool

	// statsAccessor 用于访问统计信息
	statsAccessor
}

// CacheBuilder 结构体用于构建缓存实例，包含缓存的配置参数
type CacheBuilder[K comparable, V any] struct {
	clock            Clock                                // 时钟接口，用于获取当前时间
	tp               string                               // 缓存类型，例如 "simple", "lru" 等
	size             int                                  // 缓存的最大容量，限制缓存条目数量
	loaderExpireFunc func(k K) (V, *time.Duration, error) // 加载函数，在缓存未命中时调用以获取数据，并可指定过期时间
	evictedFunc      func(k K, v V)                       // 驱逐回调函数，当缓存条目被移除时触发
	purgeVisitorFunc func(k K, v V)                       // 清空访问函数，在 Purge 操作时遍历所有缓存项并调用
	addedFunc        func(k K, v V)                       // 添加回调函数，当新的缓存项被添加时调用
	serializeFunc    func(k K, v V) (V, error)            // 序列化函数，用于序列化缓存值
	deserializeFunc  func(k K, v V) (V, error)            // 反序列化函数，用于反序列化缓存值
	expiration       *time.Duration                       // 默认的缓存项过期时间
	lease            *time.Duration                       // 缓存项的租约时间（通常用于 ARC 缓存）
}

// New 创建并返回一个新的 CacheBuilder 实例
// @param size int: 缓存的最大容量
// @return *CacheBuilder *CacheBuilder: CacheBuilder 实例
func New[K comparable, V any](size int) *CacheBuilder[K, V] {
	return &CacheBuilder[K, V]{
		clock: NewRealClock(), // 使用实际时钟作为默认时钟
		tp:    TYPE_SIMPLE,    // 默认缓存类型为简单缓存
		size:  size,           // 设置缓存容量
	}
}

// Clock  设置 CacheBuilder 的时钟。
// @param Clock Clock: 要使用的时钟
// @return CacheBuilder: 返回 CacheBuilder 实例，支持链式调用。
func (cb *CacheBuilder[K, V]) Clock(clock Clock) *CacheBuilder[K, V] {
	cb.clock = clock
	return cb
}

// LoaderFunc  设置一个加载函数。
// @param loaderFunc func: 加载函数 缓存未命中时调用
// @return CacheBuilder CacheBuilder: 返回 CacheBuilder 实例，支持链式调用。
func (cb *CacheBuilder[K, V]) LoaderFunc(loaderFunc func(k K) (V, error)) *CacheBuilder[K, V] {
	// 将不带过期时间的加载函数包装成带过期时间的函数
	cb.loaderExpireFunc = func(k K) (V, *time.Duration, error) {
		v, err := loaderFunc(k)
		return v, nil, err
	}
	return cb
}

// LoaderExpireFunc 设置带有过期时间的加载函数
// @param loaderExpireFunc: 加载函数，缓存未命中时调用，并可返回过期时间
// @return CacheBuilder CacheBuilder: CacheBuilder 实例的指针
func (cb *CacheBuilder[K, V]) LoaderExpireFunc(loaderExpireFunc func(k K) (V, *time.Duration, error)) *CacheBuilder[K, V] {
	cb.loaderExpireFunc = loaderExpireFunc
	return cb
}

// EvictType 设置缓存的驱逐类型
// @param tp: 缓存类型，如 "simple", "lru" 等
// @return *CacheBuilder[K, V]: CacheBuilder 实例的指针
func (cb *CacheBuilder[K, V]) EvictType(tp string) *CacheBuilder[K, V] {
	cb.tp = tp
	return cb
}

// Simple  设置缓存为简单类型
// @return *CacheBuilder[K, V]: CacheBuilder 实例的指针
func (cb *CacheBuilder[K, V]) Simple() *CacheBuilder[K, V] {
	return cb.EvictType(TYPE_SIMPLE)
}

// LRU 设置缓存为 LRU 类型
// @return *CacheBuilder[K, V]: CacheBuilder 实例的指针
func (cb *CacheBuilder[K, V]) LRU() *CacheBuilder[K, V] {
	return cb.EvictType(TYPE_LRU)
}

// LFU 设置缓存为LFU类型
// @return *CacheBuilder[K, V]: CacheBuilder 实例的指针
func (cb *CacheBuilder[K, V]) LFU() *CacheBuilder[K, V] {
	return cb.EvictType(TYPE_LFU)
}

// ARC  设置缓存类型为ARC类型
// @return *CacheBuilder[K, V]: CacheBuilder 实例的指针
func (cb *CacheBuilder[K, V]) ARC() *CacheBuilder[K, V] {
	return cb.EvictType(TYPE_ARC)
}

// EvictedFunc 设置驱逐回调函数
// @param evictedFunc: 驱逐函数
// @return *CacheBuilder[K, V]: CacheBuilder 实例的指针
func (cb *CacheBuilder[K, V]) EvictedFunc(evictedFunc func(k K, v V)) *CacheBuilder[K, V] {
	cb.evictedFunc = evictedFunc
	return cb
}

// PurgeVisitorFunc  设置清除回调函数
// @param purgeVisitorFunc: 清除函数
// @return *CacheBuilder[K, V]: CacheBuilder 实例的指针
func (cb *CacheBuilder[K, V]) PurgeVisitorFunc(purgeVisitorFunc func(k K, v V)) *CacheBuilder[K, V] {
	cb.purgeVisitorFunc = purgeVisitorFunc
	return cb
}

// AddedFunc 设置添加回调函数
// @param addedFunc: 添加回调函数
// @return *CacheBuilder[K, V]: CacheBuilder 实例的指针
func (cb *CacheBuilder[K, V]) AddedFunc(addedFunc func(k K, v V)) *CacheBuilder[K, V] {
	cb.addedFunc = addedFunc
	return cb
}

// DeserializeFunc 设置反序列化函数
// @param deserializeFunc: 反序列化函数
// @return *CacheBuilder[K, V]: CacheBuilder 实例的指针
func (cb *CacheBuilder[K, V]) DeserializeFunc(deserializeFunc func(k K, v V) (V, error)) *CacheBuilder[K, V] {
	cb.deserializeFunc = deserializeFunc
	return cb
}

// SerializeFunc  设置序列化函数
// @param serializeFunc: 序列化函数
// @return *CacheBuilder[K, V]: CacheBuilder 实例的指针
func (cb *CacheBuilder[K, V]) SerializeFunc(serializeFunc func(k K, v V) (V, error)) *CacheBuilder[K, V] {
	cb.serializeFunc = serializeFunc
	return cb
}

// Expiration 设置默认过期时间
// @param expiration: 过期时间
// @return *CacheBuilder[K, V]: CacheBuilder 实例的指针
func (cb *CacheBuilder[K, V]) Expiration(expiration time.Duration) *CacheBuilder[K, V] {
	cb.expiration = &expiration
	return cb
}

// Lease 设置租约时间
// @param lease: 租约时间，当条目被访问成功时自动续租
// @return *CacheBuilder[K, V]: CacheBuilder 实例的指针
func (cb *CacheBuilder[K, V]) Lease(lease time.Duration) *CacheBuilder[K, V] {
	cb.lease = &lease
	return cb
}

// Build  构建缓存实例
// @return Cache[K, V]: 构建好的缓存实例
func (cb *CacheBuilder[K, V]) Build() Cache[K, V] {
	if cb.size <= 0 && cb.tp != TYPE_SIMPLE {
		panic("gcache: Cache size <= 0")
	}

	// 调用内部 build 方法创建缓存实例
	return cb.build()
}

// build  根据配置构建具体类型的缓存
// @return Cache[K, V]: 构建好的缓存实例
func (cb *CacheBuilder[K, V]) build() Cache[K, V] {
	switch cb.tp {
	case TYPE_SIMPLE:
		return newSimpleCache(cb)
	case TYPE_LRU:
		return newLRUCache(cb)
	case TYPE_LFU:
		return newLFUCache(cb)
	case TYPE_ARC:
		return newARC(cb)
	default:
		panic("gcache: Unknown type " + cb.tp)
	}
}
