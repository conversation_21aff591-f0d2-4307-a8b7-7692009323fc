//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-07 11:06:20
//FilePath: /yaml_scan/pkg/networkpolicy/cidr.go
//Description:

package networkpolicy

// DefaultIPv4DenylistRanges 是一个包含默认 IPv4 拒绝列表的 CIDR 范围的切片。
// 这些地址范围通常被认为是不应被允许的地址，
// 包括私有地址、环回地址、链路本地地址等。
var DefaultIPv4DenylistRanges = []string{
	"0.0.0.0/8", // 当前网络（仅有效作为源地址）
	// "10.0.0.0/8",      // 私有网络
	// "**********/10",   // 共享地址空间
	"*********/8", // 环回地址
	// "***********/16",  // 链路本地（许多云提供商的元数据端点）
	// "**********/12",   // 私有网络
	// "*********/24",    // IETF 协议分配
	// "*********/24",    // TEST-NET-1，文档和示例
	// "***********/24",  // IPv6 到 IPv4 的中继（包括 2002::/16）
	// "***********/16",  // 私有网络
	// "**********/15",   // 网络基准测试
	// "************/24", // TEST-NET-2，文档和示例
	// "***********/24",  // TEST-NET-3，文档和示例
	// "*********/4",     // IP 多播（以前的 D 类网络）
	// "240.0.0.0/4",     // 保留（以前的 E 类网络）
}

// DefaultIPv6DenylistRanges 是一个包含默认 IPv6 拒绝列表的 CIDR 范围的切片。
// 这些地址范围通常被认为是不应被允许的地址，
// 包括环回地址、链路本地地址、文档地址等。
var DefaultIPv6DenylistRanges = []string{
	"::1/128", // 环回地址
	// "64:ff9b::/96",  // IPv4/IPv6 转换（RFC 6052）
	// "100::/64",      // 丢弃前缀（RFC 6666）
	// "2001::/32",     // Teredo 隧道
	// "2001:10::/28",  // 已弃用（以前的 ORCHID）
	// "2001:20::/28",  // ORCHIDv2
	// "2001:db8::/32", // 用于文档和示例源代码的地址
	// "2002::/16",     // 6to4
	"fc00::/7",      // 唯一本地地址
	"fe80::/10",     // 链路本地地址
	// "ff00::/8",      // 多播
}
