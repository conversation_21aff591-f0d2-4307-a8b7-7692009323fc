// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 14:18:28
// FilePath: /yaml_scan/pkg/cidr/shuffle_test.go
// Description:
package cidr

import (
	"net"
	"strconv"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestItemString 测试Item结构体的String方法
func TestItemString(t *testing.T) {
	// 测试用例
	tests := []struct {
		name     string // 测试名称
		ip       string // IP地址
		port     int    // 端口号
		expected string // 期望结果
	}{
		{
			name:     "IPv4地址加端口",
			ip:       "***********",
			port:     80,
			expected: "***********:80",
		},
		{
			name:     "IPv6地址加端口",
			ip:       "2001:db8::1",
			port:     443,
			expected: "[2001:db8::1]:443", // IPv6地址会被中括号包围
		},
		{
			name:     "特殊端口号",
			ip:       "127.0.0.1",
			port:     65535, // 最大端口号
			expected: "127.0.0.1:65535",
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建Item实例
			item := Item{
				IP:   tt.ip,
				Port: tt.port,
			}

			// 调用String方法获取结果
			result := item.String()

			// 验证结果
			require.Equal(t, tt.expected, result, "Item.String()结果不符合预期")
		})
	}
}

// TestPickIP 测试从CIDR列表中选择指定索引的IP地址
func TestPickIP(t *testing.T) {
	// 测试用例
	tests := []struct {
		name     string   // 测试名称
		cidrs    []string // CIDR列表
		index    int64    // 索引
		expected string   // 期望的IP地址
	}{
		{
			name:     "从单个/24网络中选择第一个IP",
			cidrs:    []string{"***********/24"},
			index:    0,
			expected: "***********",
		},
		{
			name:     "从单个/24网络中选择中间IP",
			cidrs:    []string{"***********/24"},
			index:    10,
			expected: "***********0",
		},
		{
			name:     "从单个/24网络中选择最后一个IP",
			cidrs:    []string{"***********/24"},
			index:    255,
			expected: "***********55",
		},
		{
			name:     "从多个CIDR网络中选择跨越第一个网络的IP",
			cidrs:    []string{"***********/24", "10.0.0.0/16"},
			index:    300, // 256 + 44
			expected: "*********",
		},
		{
			name:     "索引超出范围",
			cidrs:    []string{"***********/24"}, // 共256个IP
			index:    1000,                       // 超出范围
			expected: "",
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 转换CIDR字符串为IPNet对象
			var ipnets []*net.IPNet
			for _, cidrStr := range tt.cidrs {
				_, ipnet, err := net.ParseCIDR(cidrStr)
				require.NoError(t, err, "解析CIDR出错")
				ipnets = append(ipnets, ipnet)
			}

			// 调用PickIP函数
			result := PickIP(ipnets, tt.index)

			// 验证结果
			require.Equal(t, tt.expected, result, "PickIP结果不符合预期")
		})
	}
}

// TestPickSubnetIP 测试从单个CIDR网络中选择指定索引的IP地址
func TestPickSubnetIP(t *testing.T) {
	// 测试用例
	tests := []struct {
		name     string // 测试名称
		cidr     string // CIDR
		index    int64  // 索引
		expected string // 期望的IP地址
	}{
		{
			name:     "从/24网络中选择第一个IP",
			cidr:     "***********/24",
			index:    0,
			expected: "***********",
		},
		{
			name:     "从/24网络中选择最后一个IP",
			cidr:     "***********/24",
			index:    255,
			expected: "***********55",
		},
		{
			name:     "从/16网络中选择IP",
			cidr:     "10.0.0.0/16",
			index:    513, // 2*256 + 1 = ********
			expected: "********",
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 解析CIDR
			_, ipnet, err := net.ParseCIDR(tt.cidr)
			require.NoError(t, err, "解析CIDR出错")

			// 调用PickSubnetIP函数
			result := PickSubnetIP(ipnet, tt.index)

			// 验证结果
			require.Equal(t, tt.expected, result, "PickSubnetIP结果不符合预期")
		})
	}
}

// TestPickPort 测试从端口列表中选择指定索引的端口
func TestPickPort(t *testing.T) {
	// 测试用例
	tests := []struct {
		name     string // 测试名称
		ports    []int  // 端口列表
		index    int    // 索引
		expected int    // 期望的端口号
	}{
		{
			name:     "选择第一个端口",
			ports:    []int{80, 443, 8080},
			index:    0,
			expected: 80,
		},
		{
			name:     "选择中间端口",
			ports:    []int{80, 443, 8080},
			index:    1,
			expected: 443,
		},
		{
			name:     "选择最后一个端口",
			ports:    []int{80, 443, 8080},
			index:    2,
			expected: 8080,
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 调用PickPort函数
			result := PickPort(tt.ports, tt.index)

			// 验证结果
			require.Equal(t, tt.expected, result, "PickPort结果不符合预期")
		})
	}
}

// TestAsIPV4CIDR 测试将IPv4地址转换为CIDR表示
func TestAsIPV4CIDR(t *testing.T) {
	// 测试用例
	tests := []struct {
		name           string // 测试名称
		input          string // 输入字符串
		expectedIP     string // 期望的IP地址
		expectedPrefix int    // 期望的前缀长度
		expectNil      bool   // 是否期望返回nil
	}{
		{
			name:           "纯IPv4地址转换",
			input:          "***********",
			expectedIP:     "***********",
			expectedPrefix: 32,
			expectNil:      false,
		},
		{
			name:           "带前缀的IPv4地址",
			input:          "***********/24",
			expectedIP:     "***********",
			expectedPrefix: 24,
			expectNil:      false,
		},
		{
			name:           "IPv4网络地址",
			input:          "10.0.0.0/8",
			expectedIP:     "10.0.0.0",
			expectedPrefix: 8,
			expectNil:      false,
		},
		{
			name:      "无效输入",
			input:     "invalid",
			expectNil: true,
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 调用AsIPV4CIDR函数
			result := AsIPV4CIDR(tt.input)

			// 验证结果
			if tt.expectNil {
				require.Nil(t, result, "预期返回nil但得到了非nil值")
				return
			}

			require.NotNil(t, result, "预期返回非nil值但得到了nil")

			// 验证IP地址
			require.Equal(t, tt.expectedIP, result.IP.String(), "IP地址不匹配")

			// 验证前缀长度
			ones, _ := result.Mask.Size()
			require.Equal(t, tt.expectedPrefix, ones, "前缀长度不匹配")
		})
	}
}

// TestCIDRsAsIPNET 测试将CIDR字符串列表转换为IPNet对象列表
func TestCIDRsAsIPNET(t *testing.T) {
	// 测试用例
	tests := []struct {
		name          string   // 测试名称
		cidrs         []string // CIDR字符串列表
		expectedCount int      // 期望的IPNet对象数量
	}{
		{
			name:          "正常CIDR列表",
			cidrs:         []string{"***********/24", "10.0.0.0/8"},
			expectedCount: 2,
		},
		{
			name:          "包含纯IP地址",
			cidrs:         []string{"***********", "********"},
			expectedCount: 2,
		},
		{
			name:          "混合有效和无效输入",
			cidrs:         []string{"***********/24", "invalid", "********"},
			expectedCount: 2, // 无效输入返回nil，不计入总数
		},
		{
			name:          "空列表",
			cidrs:         []string{},
			expectedCount: 0,
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 调用CIDRsAsIPNET函数
			result := CIDRsAsIPNET(tt.cidrs)

			// 验证结果数量
			validResults := 0
			for _, ipnet := range result {
				if ipnet != nil {
					validResults++
				}
			}
			require.Equal(t, tt.expectedCount, validResults, "有效IPNet数量不匹配")
		})
	}
}

// TestShuffleCidrsWithSeed 测试使用BlackRock算法随机访问CIDR范围内的所有IP地址
func TestShuffleCidrsWithSeed(t *testing.T) {
	// 测试用例
	tests := []struct {
		name          string   // 测试名称
		cidrs         []string // CIDR字符串列表
		seed          int64    // 随机种子
		expectedCount int      // 期望的IP地址数量
	}{
		{
			name:          "单个小型CIDR",
			cidrs:         []string{"***********/30"}, // 4个IP
			seed:          42,
			expectedCount: 4,
		},
		{
			name:          "多个小型CIDR",
			cidrs:         []string{"***********/30", "***********/30"}, // 共8个IP
			seed:          123,
			expectedCount: 8,
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 转换CIDR字符串为IPNet对象
			var ipnets []*net.IPNet
			for _, cidrStr := range tt.cidrs {
				_, ipnet, err := net.ParseCIDR(cidrStr)
				require.NoError(t, err, "解析CIDR出错")
				ipnets = append(ipnets, ipnet)
			}

			// 调用ShuffleCidrsWithSeed函数
			resultChan := ShuffleCidrsWithSeed(ipnets, tt.seed)

			// 收集所有IP地址
			ips := make(map[string]struct{})
			for item := range resultChan {
				ips[item.IP] = struct{}{}
			}

			// 验证结果数量
			require.Equal(t, tt.expectedCount, len(ips), "生成的IP地址数量不匹配")

			// 检查使用相同种子时生成的序列是否相同
			resultChan1 := ShuffleCidrsWithSeed(ipnets, tt.seed)
			resultChan2 := ShuffleCidrsWithSeed(ipnets, tt.seed)

			// 从两个通道读取第一个元素进行比较
			item1, ok1 := <-resultChan1
			require.True(t, ok1, "第一个通道为空")

			item2, ok2 := <-resultChan2
			require.True(t, ok2, "第二个通道为空")

			require.Equal(t, item1.IP, item2.IP, "相同种子应生成相同的第一个IP地址")
		})
	}
}

// TestShuffleCidrsWithPortsAndSeed 测试使用BlackRock算法随机访问CIDR范围内所有IP地址和端口的组合
func TestShuffleCidrsWithPortsAndSeed(t *testing.T) {
	// 测试用例
	tests := []struct {
		name          string   // 测试名称
		cidrs         []string // CIDR字符串列表
		ports         []int    // 端口列表
		seed          int64    // 随机种子
		expectedCount int      // 期望的组合数量
	}{
		{
			name:          "小型CIDR和少量端口",
			cidrs:         []string{"***********/30"}, // 4个IP
			ports:         []int{80, 443},             // 2个端口
			seed:          42,
			expectedCount: 8, // 4 IP * 2 ports = 8 combinations
		},
		{
			name:          "多个小型CIDR和多个端口",
			cidrs:         []string{"***********/31", "***********/31"}, // 共4个IP
			ports:         []int{80, 443, 8080},                         // 3个端口
			seed:          123,
			expectedCount: 12, // 4 IP * 3 ports = 12 combinations
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 转换CIDR字符串为IPNet对象
			var ipnets []*net.IPNet
			for _, cidrStr := range tt.cidrs {
				_, ipnet, err := net.ParseCIDR(cidrStr)
				require.NoError(t, err, "解析CIDR出错")
				ipnets = append(ipnets, ipnet)
			}

			// 调用ShuffleCidrsWithPortsAndSeed函数
			resultChan := ShuffleCidrsWithPortsAndSeed(ipnets, tt.ports, tt.seed)

			// 收集所有IP:端口组合
			combinations := make(map[string]struct{})
			for item := range resultChan {
				key := item.IP + ":" + strconv.Itoa(item.Port)
				combinations[key] = struct{}{}
			}

			// 验证结果数量
			require.Equal(t, tt.expectedCount, len(combinations), "生成的IP:端口组合数量不匹配")

			// 检查使用相同种子时生成的序列是否相同
			resultChan1 := ShuffleCidrsWithPortsAndSeed(ipnets, tt.ports, tt.seed)
			resultChan2 := ShuffleCidrsWithPortsAndSeed(ipnets, tt.ports, tt.seed)

			// 从两个通道读取第一个元素进行比较
			item1, ok1 := <-resultChan1
			require.True(t, ok1, "第一个通道为空")

			item2, ok2 := <-resultChan2
			require.True(t, ok2, "第二个通道为空")

			require.Equal(t, item1.IP, item2.IP, "相同种子应生成相同的第一个IP地址")
			require.Equal(t, item1.Port, item2.Port, "相同种子应生成相同的第一个端口号")
		})
	}
}


