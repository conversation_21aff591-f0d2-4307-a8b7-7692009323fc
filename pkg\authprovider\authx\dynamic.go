// Author: chenjb
// Version: V1.0
// Date: 2025-06-13 14:45:34
// FilePath: /yaml_scan/pkg/authprovider/authx/dynamic.go
// Description:实现了动态密钥功能，支持运行时动态生成和获取认证凭证
package authx

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"sync"
	"yaml_scan/pkg/gologger"
	"yaml_scan/pkg/protocols/common/replacer"
	sliceutil "yaml_scan/utils/slice"
)

// LazyFetchSecret 是懒加载密钥的回调函数类型
// 用于在需要时动态获取密钥
type LazyFetchSecret func(d *Dynamic) error

// Dynamic是动态密钥或凭证的结构体
// 这些是高级密钥，需要采取操作生成实际的密钥
// 例如：用户名和密码是动态密钥，使用它们进行身份验证后获得的令牌是实际密钥
type Dynamic struct {
	*Secret       `yaml:",inline"`       // 这是一个静态密钥，将在动态密钥解析后生成
	Secrets       []*Secret              `yaml:"secrets"`                    // 多个静态密钥
	TemplatePath  string                 `json:"template" yaml:"template"`   // 模板路径
	Variables     []KV                   `json:"variables" yaml:"variables"` // 变量列表
	Input         string                 `json:"input" yaml:"input"`         // (可选) 动态密钥的目标
	Extracted     map[string]interface{} `json:"-" yaml:"-"`                 // 从动态密钥中提取的值
	fetchCallback LazyFetchSecret        `json:"-" yaml:"-"`                 // 懒加载回调函数
	m             *sync.Mutex            `json:"-" yaml:"-"`                 // 用于懒加载的互斥锁
	fetched       bool                   `json:"-" yaml:"-"`                 // 检查密钥是否已被获取的标志
	error         error                  `json:"-" yaml:"-"`                 // 错误（如果有）
}

// GetDomainAndDomainRegex 获取动态密钥中定义的域名和域名正则表达式
// @receiver d 
// @return []string []string: 域名列表
// @return []string []string: 域名正则表达式列表
func (d *Dynamic) GetDomainAndDomainRegex() ([]string, []string) {
	var domains []string
	var domainRegex []string
	// 收集所有密钥中的域名和域名正则表达式
	for _, secret := range d.Secrets {
		domains = append(domains, secret.Domains...)
		domainRegex = append(domainRegex, secret.DomainsRegex...)
	}
	// 如果有主密钥，也收集其中的域名和域名正则表达式
	if d.Secret != nil {
		domains = append(domains, d.Secret.Domains...)
		domainRegex = append(domainRegex, d.Secret.DomainsRegex...)
	}
	// 去重处理
	uniqueDomains := sliceutil.Dedupe(domains)
	uniqueDomainRegex := sliceutil.Dedupe(domainRegex)
	return uniqueDomains, uniqueDomainRegex
}

// GetStrategies 返回动态密钥的认证策略
// @receiver d 
// @return []AuthStrategy []AuthStrategy: 认证策略列表，如果获取失败则返回nil
func (d *Dynamic) GetStrategies() []AuthStrategy {
	// 如果尚未获取密钥，则尝试获取
	if !d.fetched {
		_ = d.Fetch(true)
	}
	if d.error != nil {
		return nil
	}
	var strategies []AuthStrategy
	// 如果有主密钥，添加其策略
	if d.Secret != nil {
		strategies = append(strategies, d.Secret.GetStrategy())
	}
	// 添加所有子密钥的策略
	for _, secret := range d.Secrets {
		strategies = append(strategies, secret.GetStrategy())
	}
	return strategies
}

// UnmarshalJSON 实现了json.Unmarshaler接口，用于自定义JSON反序列化
// @receiver d 
// @param data []byte: 
// @return error error: 
func (d *Dynamic) UnmarshalJSON(data []byte) error {
	// 创建一个匿名结构体来避免递归调用
	type DynamicAlias Dynamic
	aux := &struct {
		*DynamicAlias
	}{
		DynamicAlias: (*DynamicAlias)(d),
	}

	// 反序列化到匿名结构体
	if err := json.Unmarshal(data, aux); err != nil {
		return err
	}

	// 再尝试将JSON数据反序列化为Secret
	var s Secret
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}

	// 将反序列化的Secret设置为动态密钥的主密钥
	d.Secret = &s
	return nil
}

// Validate 验证动态密钥的有效性
// @receiver d 
// @return error error: 可能的错误
func (d *Dynamic) Validate() error {
	// 初始化互斥锁
	d.m = &sync.Mutex{}
	if d.TemplatePath == "" {
		return errors.New(" template-path is required for dynamic secret")
	}
	if len(d.Variables) == 0 {
		return errors.New("variables are required for dynamic secret")
	}

	// 验证主密钥（如果有）
	if d.Secret != nil {
		// 在验证过程中跳过动态密钥中的cookie解析
		d.Secret.skipCookieParse = true
		if err := d.Secret.Validate(); err != nil {
			return err
		}
	}
	// 验证所有子密钥
	for _, secret := range d.Secrets {
		secret.skipCookieParse = true
		if err := secret.Validate(); err != nil {
			return err
		}
	}
	return nil
}

// Fetch  获取动态密钥
// @receiver d 
// @param isFatal bool: 如果为true，则在无法获取密钥时停止执行
// @return error error: 如果获取过程中发生错误则返回错误，否则返回nil
func (d *Dynamic) Fetch(isFatal bool) error {
	d.m.Lock()
	defer d.m.Unlock()
	if d.fetched {
		return nil
	}
	// 调用回调函数获取密钥
	d.error = d.fetchCallback(d)
	// 如果发生错误且isFatal为true，则记录致命错误并停止执行
	if d.error != nil && isFatal {
		gologger.Fatal().Msgf("Could not fetch dynamic secret: %s\n", d.error)
	}
	return d.error
}

// Error 返回动态密钥获取过程中的错误（如果有）
func (d *Dynamic) Error() error {
	return d.error
}

// SetLazyFetchCallback 设置动态密钥的懒加载回调函数
// @receiver d 
// @param callback LazyFetchSecret: 懒加载回调函数
func (d *Dynamic) SetLazyFetchCallback(callback LazyFetchSecret) {
	d.fetchCallback = func(d *Dynamic) error {
		// 调用原始回调函数
		err := callback(d)
		d.fetched = true
		if err != nil {
			d.error = err
			return err
		}
		// 检查是否提取到了值
		if len(d.Extracted) == 0 {
			return fmt.Errorf("no extracted values found for dynamic secret")
		}

		// 如果有主密钥，应用提取的值
		if d.Secret != nil {
			if err := d.applyValuesToSecret(d.Secret); err != nil {
				return err
			}
		}

		// 应用提取的值到所有子密钥
		for _, secret := range d.Secrets {
			if err := d.applyValuesToSecret(secret); err != nil {
				return err
			}
		}
		return nil
	}
}

// applyValuesToSecret 将提取的值应用到密钥中
// @receiver d 
// @param secret *Secret: 要应用值的密钥
// @return error error: 可能的错误
func (d *Dynamic) applyValuesToSecret(secret *Secret) error {
	// 评估并替换请求头中的模板变量
	for i, header := range secret.Headers {
		if strings.Contains(header.Value, "{{") {
			header.Value = replacer.Replace(header.Value, d.Extracted)
		}
		if strings.Contains(header.Key, "{{") {
			header.Key = replacer.Replace(header.Key, d.Extracted)
		}
		secret.Headers[i] = header
	}

	// 评估并替换Cookie中的模板变量
	for i, cookie := range secret.Cookies {
		if strings.Contains(cookie.Value, "{{") {
			cookie.Value = replacer.Replace(cookie.Value, d.Extracted)
		}
		if strings.Contains(cookie.Key, "{{") {
			cookie.Key = replacer.Replace(cookie.Key, d.Extracted)
		}
		if strings.Contains(cookie.Raw, "{{") {
			cookie.Raw = replacer.Replace(cookie.Raw, d.Extracted)
		}
		secret.Cookies[i] = cookie
	}

	// 评估并替换查询参数中的模板变量
	for i, query := range secret.Params {
		if strings.Contains(query.Value, "{{") {
			query.Value = replacer.Replace(query.Value, d.Extracted)
		}
		if strings.Contains(query.Key, "{{") {
			query.Key = replacer.Replace(query.Key, d.Extracted)
		}
		secret.Params[i] = query
	}

	// 检查并替换用户名、密码和令牌中的模板变量
	if strings.Contains(secret.Username, "{{") {
		secret.Username = replacer.Replace(secret.Username, d.Extracted)
	}
	if strings.Contains(secret.Password, "{{") {
		secret.Password = replacer.Replace(secret.Password, d.Extracted)
	}
	if strings.Contains(secret.Token, "{{") {
		secret.Token = replacer.Replace(secret.Token, d.Extracted)
	}

	// 现在尝试解析Cookie
	secret.skipCookieParse = false
	for i, cookie := range secret.Cookies {
		if cookie.Raw != "" {
			if err := cookie.Parse(); err != nil {
				return fmt.Errorf("[%s] invalid raw cookie in cookiesAuth: %s", d.TemplatePath, err)
			}
			secret.Cookies[i] = cookie
		}
	}
	return nil
}
