// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-18 14:50:07
// FilePath: /yaml_scan/pkg/httpx/runner/runner.go
// Description: 
package runner

// Runner is a client for running the enumeration process.
type Runner struct {
	options            *Options
	hp                 *httpx.HTTPX
	wappalyzer         *wappalyzer.Wappalyze
	scanopts           ScanOptions
	hm                 *hybrid.HybridMap
	excludeCdn         bool
	stats              clistats.StatisticsClient
	ratelimiter        ratelimit.Limiter
	HostErrorsCache    gcache.Cache[string, int]
	browser            *Browser
	pageTypeClassifier *pagetypeclassifier.PageTypeClassifier // Include this for general page classification
	pHashClusters      []pHashCluster
	simHashes          gcache.Cache[uint64, struct{}] // Include simHashes for efficient duplicate detection
	httpApiEndpoint    *Server
}

