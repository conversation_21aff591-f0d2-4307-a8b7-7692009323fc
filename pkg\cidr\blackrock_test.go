// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-07 11:58:21
// FilePath: /yaml_scan/pkg/cidr/blackrock_test.go
// Description: 
package cidr

import (
	"testing"

	"github.com/stretchr/testify/require"
)

// TestNew 测试BlackRock实例的创建和初始化
func TestNew(t *testing.T) {
	// 测试用例
	tests := []struct {
		name           string // 测试名称
		rangez         int64  // 范围参数
		seed           int64  // 种子参数
		expectedA      int64  // 期望的A参数
		expectedB      int64  // 期望的B参数
		expectedRounds int64  // 期望的轮数
	}{
		{
			name:           "正常范围值初始化",
			rangez:         100,
			seed:           42,
			expectedA:      9,  // floor(sqrt(100)) - 1
			expectedB:      12, // ceil(sqrt(100)) + 1
			expectedRounds: 3,
		},
		{
			name:           "小范围值初始化",
			rangez:         10,
			seed:           123,
			expectedA:      2, // floor(sqrt(10)) - 1
			expectedB:      6, // 调整后的值，确保A*B > rangez
			expectedRounds: 3,
		},
		{
			name:           "大范围值初始化",
			rangez:         10000,
			seed:           456,
			expectedA:      99,  // floor(sqrt(10000)) - 1
			expectedB:      102, // ceil(sqrt(10000)) + 1
			expectedRounds: 3,
		},
		{
			name:           "极小范围值初始化",
			rangez:         1,
			seed:           789,
			expectedA:      1, // 最小值为1
			expectedB:      2, // 调整后的值，确保A*B > rangez
			expectedRounds: 3,
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建BlackRock实例
			br := New(tt.rangez, tt.seed)

			// 验证初始化结果
			require.Equal(t, tt.expectedA, br.A, "参数A初始化错误")
			require.Equal(t, tt.expectedB, br.B, "参数B初始化错误")
			require.Equal(t, tt.seed, br.Seed, "种子参数初始化错误")
			require.Equal(t, tt.rangez, br.Range, "范围参数初始化错误")
			require.Equal(t, tt.expectedRounds, br.Rounds, "轮数参数初始化错误")

			// 验证A*B > rangez条件满足
			require.True(t, br.A*br.B > tt.rangez, "初始化后的A*B应大于范围")
		})
	}
}

// TestF 测试内部排列函数F的确定性和输出范围
func TestF(t *testing.T) {
	// 创建BlackRock实例
	br := New(100, 42)

	// 测试用例
	tests := []struct {
		name   string // 测试名称
		j      int64  // 轮数索引
		r      int64  // 输入值
		seed   int64  // 种子值
		repeat int    // 重复调用次数
	}{
		{
			name:   "基本功能测试",
			j:      1,
			r:      5,
			seed:   42,
			repeat: 1,
		},
		{
			name:   "重复调用测试",
			j:      2,
			r:      10,
			seed:   123,
			repeat: 10,
		},
		{
			name:   "边界值测试",
			j:      3,
			r:      0,
			seed:   456,
			repeat: 1,
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 第一次调用结果
			firstResult := br.F(tt.j, tt.r, tt.seed)

			// 重复调用并验证结果一致性
			for i := 0; i < tt.repeat; i++ {
				result := br.F(tt.j, tt.r, tt.seed)
				require.Equal(t, firstResult, result, "F函数的输出应该是确定性的")
			}

			// 验证不同输入产生不同输出
			if tt.r > 0 {
				differentResult := br.F(tt.j, tt.r+1, tt.seed)
				require.NotEqual(t, firstResult, differentResult, "不同的输入值应产生不同的输出")
			}
		})
	}
}

// TestFeUnfe 测试Fe和Unfe的互逆性质
func TestFeUnfe(t *testing.T) {
	// 测试用例
	tests := []struct {
		name string // 测试名称
		r    int64  // 轮数
		a    int64  // 参数A
		b    int64  // 参数B
		m    int64  // 输入值
		seed int64  // 种子值
	}{
		{
			name: "小输入值",
			r:    3,
			a:    10,
			b:    12,
			m:    5,
			seed: 42,
		},
		
		
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建BlackRock实例
			br := New(10000, tt.seed)

			// 应用Fe函数
			encrypted := br.Fe(tt.r, tt.a, tt.b, tt.m, tt.seed)

			// 应用Unfe函数还原
			decrypted := br.Unfe(tt.r, tt.a, tt.b, encrypted, tt.seed)

			// 验证还原结果
			require.Equal(t, tt.m, decrypted, "Unfe应该能够完全还原Fe的结果")
		})
	}
}


// TestShuffleDeterminism 测试Shuffle函数的确定性
func TestShuffleDeterminism(t *testing.T) {
	// 测试用例
	tests := []struct {
		name   string // 测试名称
		rangez int64  // 范围参数
		seed   int64  // 种子参数
		input  int64  // 输入值
	}{
		{
			name:   "小范围确定性测试",
			rangez: 100,
			seed:   42,
			input:  5,
		},
		{
			name:   "中范围确定性测试",
			rangez: 1000,
			seed:   123,
			input:  500,
		},
		{
			name:   "大范围确定性测试",
			rangez: 10000,
			seed:   456,
			input:  5000,
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建第一个BlackRock实例
			br1 := New(tt.rangez, tt.seed)

			// 创建第二个相同参数的BlackRock实例
			br2 := New(tt.rangez, tt.seed)

			// 使用第一个实例混淆
			shuffled1 := br1.Shuffle(tt.input)

			// 使用第二个实例混淆
			shuffled2 := br2.Shuffle(tt.input)

			// 验证两次混淆结果相同
			require.Equal(t, shuffled1, shuffled2, "相同参数下Shuffle应产生相同结果")

			// 创建使用不同种子的BlackRock实例
			br3 := New(tt.rangez, tt.seed+1)

			// 使用不同种子混淆
			shuffled3 := br3.Shuffle(tt.input)

			// 验证不同种子产生不同结果
			require.NotEqual(t, shuffled1, shuffled3, "不同种子应产生不同的混淆结果")
		})
	}
}

// TestRangeDistribution 测试混淆后值的分布情况
func TestRangeDistribution(t *testing.T) {
	// 范围和种子参数
	rangez := int64(100)
	seed := int64(42)

	// 创建BlackRock实例
	br := New(rangez, seed)

	// 创建计数映射，记录每个输出值的出现次数
	counts := make(map[int64]int)

	// 混淆一定数量的输入值（远大于范围）
	numTests := 1000
	for i := 0; i < numTests; i++ {
		result := br.Shuffle(int64(i % int(rangez))) // 使输入在范围内循环
		counts[result]++
	}

	// 计算有多少不同的输出值
	uniqueOutputs := len(counts)

	// 验证有合理数量的不同输出值
	// 由于我们输入的是循环的值，输出应该有合理的多样性，但不一定覆盖整个范围
	require.True(t, uniqueOutputs > int(rangez)/4, "输出值应该有合理的分布")
}


