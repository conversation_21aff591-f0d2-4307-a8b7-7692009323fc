//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 19:30:45
// FilePath: /yaml_scan/utils/reader/conn_read_test.go
// Description:

package reader

import (
	"bytes"
	"context"
	"errors"
	"io"
	"os"
	"syscall"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// 模拟一个超时错误
type timeoutError struct{}

func (e timeoutError) Error() string   { return "超时错误" }
func (e timeoutError) Timeout() bool   { return true }
func (e timeoutError) Temporary() bool { return true }

// 模拟一个固定大小的读取器
type fixedReader struct {
	data []byte
}

func (r *fixedReader) Read(p []byte) (n int, err error) {
	if len(r.data) == 0 {
		return 0, io.EOF
	}
	n = copy(p, r.data)
	r.data = r.data[n:]
	return n, nil
}

// 模拟一个返回超时错误的读取器
type timeoutReader struct {
	readCount int
	data      []byte
}

func (r *timeoutReader) Read(p []byte) (n int, err error) {
	r.readCount++
	if r.readCount > 1 {
		return 0, &timeoutError{}
	}
	n = copy(p, r.data)
	return n, nil
}

// 模拟一个返回连接拒绝错误的读取器
type connRefusedReader struct{}

func (r *connRefusedReader) Read(p []byte) (n int, err error) {
	return 0, syscall.ECONNREFUSED
}

func TestConnReadN(t *testing.T) {
	// 测试用例1: 读取少于N字节的数据
	t.Run("读取少于N字节的数据", func(t *testing.T) {
		// 创建一个require对象
		r := require.New(t)

		// 设置输入数据
		input := []byte("测试数据")
		reader := bytes.NewReader(input)

		// 调用ConnReadN
		result, err := ConnReadN(context.Background(), reader, 100)

		// 验证结果
		r.NoError(err)
		r.Equal(input, result)
	})

	// 测试用例2: N为-1，读取最大允许大小
	t.Run("N为-1时读取最大允许大小", func(t *testing.T) {
		r := require.New(t)

		input := []byte("测试数据")
		reader := bytes.NewReader(input)

		// 使用N=-1调用ConnReadN
		result, err := ConnReadN(context.Background(), reader, -1)

		r.NoError(err)
		r.Equal(input, result)
	})

	// 测试用例3: N为0，返回空字节数组
	t.Run("N为0时返回空字节数组", func(t *testing.T) {
		r := require.New(t)

		result, err := ConnReadN(context.Background(), &fixedReader{data: []byte("测试数据")}, 0)

		r.NoError(err)
		r.Empty(result)
	})

	// 测试用例4: N小于-1，返回错误
	t.Run("N小于-1时返回错误", func(t *testing.T) {
		r := require.New(t)

		_, err := ConnReadN(context.Background(), &fixedReader{data: []byte("测试数据")}, -2)

		r.Error(err)
		r.Contains(err.Error(), "cannot be less than -1")
	})

	// 测试用例5: N超过MaxReadSize，返回错误
	t.Run("N超过MaxReadSize时返回错误", func(t *testing.T) {
		r := require.New(t)

		_, err := ConnReadN(context.Background(), &fixedReader{data: []byte("测试数据")}, MaxReadSize+1)

		r.Error(err)
		r.Equal(ErrTooLarge, err)
	})

	// 测试用例6: 超时后有数据，返回数据
	t.Run("超时后有数据时返回数据", func(t *testing.T) {
		r := require.New(t)

		input := []byte("超时前数据")
		reader := &timeoutReader{data: input}

		result, err := ConnReadN(context.Background(), reader, 100)

		r.NoError(err)
		r.Equal(input, result)
	})

	// 测试用例7: EOF错误被忽略
	t.Run("EOF错误被忽略", func(t *testing.T) {
		r := require.New(t)

		input := []byte("测试数据")
		reader := bytes.NewReader(input)

		result, err := ConnReadN(context.Background(), reader, 100)

		r.NoError(err)
		r.Equal(input, result)
	})

	// 测试用例8: 连接被拒绝错误被忽略
	t.Run("连接被拒绝错误被忽略", func(t *testing.T) {
		r := require.New(t)

		reader := &connRefusedReader{}

		result, err := ConnReadN(context.Background(), reader, 10)

		r.NoError(err)
		r.Empty(result)
	})
}

func TestConnReadNWithTimeout(t *testing.T) {
	// 测试ConnReadNWithTimeout函数
	t.Run("正常读取", func(t *testing.T) {
		r := require.New(t)

		input := []byte("测试数据")
		reader := bytes.NewReader(input)

		// 设置足够长的超时时间
		result, err := ConnReadNWithTimeout(reader, 100, 1*time.Second)

		r.NoError(err)
		r.Equal(input, result)
	})

	// 测试超时情况
	t.Run("读取超时", func(t *testing.T) {
		r := require.New(t)

		// 创建一个冻结的读取器，它永远不会返回
		reader := FrozenReader{}

		// 设置非常短的超时时间
		_, err := ConnReadNWithTimeout(reader, 10, 10*time.Millisecond)

		// 我们期望得到一个错误
		r.Error(err)
	})
}

func TestIsAcceptedError(t *testing.T) {
	// 测试IsAcceptedError函数
	testCases := []struct {
		name     string
		err      error
		expected bool
	}{
		{"EOF错误", io.EOF, true},
		{"UnexpectedEOF错误", io.ErrUnexpectedEOF, true},
		{"连接被拒绝错误", syscall.ECONNREFUSED, true},
		{"其他错误", errors.New("其他错误"), false},
		{"nil错误", nil, false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			r := require.New(t)
			result := IsAcceptedError(tc.err)
			r.Equal(tc.expected, result)
		})
	}
}

func TestIsTimeout(t *testing.T) {
	// 测试IsTimeout函数
	testCases := []struct {
		name     string
		err      error
		expected bool
	}{
		{"网络超时错误", &timeoutError{}, true},
		{"上下文超时错误", context.DeadlineExceeded, true},
		{"操作系统超时错误", os.ErrDeadlineExceeded, true},
		{"其他错误", errors.New("其他错误"), false},
		{"nil错误", nil, false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			r := require.New(t)
			result := IsTimeout(tc.err)
			r.Equal(tc.expected, result)
		})
	}
}
