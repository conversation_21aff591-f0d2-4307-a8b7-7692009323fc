// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 17:08:57
// FilePath: /yaml_scan/pkg/retryablehttp/digest_auth_client/authorization.go
// Description: 实现HTTP摘要认证的授权头生成，包括计算摘要响应和授权字符串的功能
package digestauthclient

import (
	"bytes"
	"crypto/md5"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"hash"
	"io"
	"net/url"
	"strings"
	"time"
)

// authorization 结构体存储HTTP摘要认证所需的所有字段
type authorization struct {
	Algorithm string // 未加引号，使用的哈希算法（如MD5、SHA-256）
	Cnonce    string // 加引号，客户端生成的随机数，用于防止重放攻击
	Nc        int    // 未加引号，请求计数器，随每次请求递增
	Nonce     string // 加引号，服务器提供的随机字符串，用于生成摘要
	Opaque    string // 加引号，服务器提供的不透明字符串，原样返回
	Qop       string // 未加引号，服务质量参数，通常为"auth"或"auth-int"
	Realm     string // 加引号，保护区域名称
	Response  string // 加引号，计算出的摘要响应
	URI       string // 加引号，请求的URI路径
	Userhash  bool   // 加引号，是否对用户名进行哈希处理
	Username  string // 加引号，用户名
	Username_ string // 加引号，备用用户名（未使用）
}

// newAuthorization 创建一个新的授权对象并初始化基本字段
// @param dr *DigestRequest: 包含摘要请求信息的对象
// @return *authorization *authorization: 初始化的授权对象
// @return error error:
func newAuthorization(dr *DigestRequest) (*authorization, error) {

	// 创建授权对象并从WWW-Authenticate头中提取基本信息
	ah := authorization{
		Algorithm: dr.Wa.Algorithm, // 使用服务器指定的哈希算法
		Cnonce:    "",              // 客户端随机数，稍后生成
		Nc:        0,               // 请求计数器，初始值为0
		Nonce:     dr.Wa.Nonce,     // 服务器提供的随机字符串
		Opaque:    dr.Wa.Opaque,    // 服务器提供的不透明字符串
		Qop:       "",              // 服务质量参数，稍后根据服务器支持设置
		Realm:     dr.Wa.Realm,     // 保护区域名称
		Response:  "",              // 摘要响应，稍后计算
		URI:       "",              // 请求URI，稍后设置
		Userhash:  dr.Wa.Userhash,  // 是否对用户名进行哈希处理
		Username:  "",              // 用户名，稍后设置
		Username_: "",              // 备用用户名（未使用）
	}

	// 调用方法完成授权对象的初始化
	return ah.refreshAuthorization(dr)
}

const (
	algorithmMD5        = "MD5"
	algorithmMD5Sess    = "MD5-SESS"
	algorithmSHA256     = "SHA-256"
	algorithmSHA256Sess = "SHA-256-SESS"
)

// refreshAuthorization 刷新授权信息，生成新的响应值
// @receiver ah
// @param dr *DigestRequest: 包含摘要请求信息的对象
// @return *authorization *authorization:  更新后的授权对象
// @return error error: 可能的错误
func (ah *authorization) refreshAuthorization(dr *DigestRequest) (*authorization, error) {

	ah.Username = dr.Username

	// 如果启用了用户名哈希，则对用户名进行哈希处理
	if ah.Userhash {
		ah.Username = ah.hash(fmt.Sprintf("%s:%s", ah.Username, ah.Realm))
	}

	ah.Nc++

	// 生成客户端随机数，使用时间戳和用户名
	ah.Cnonce = ah.hash(fmt.Sprintf("%d:%s:my_value", time.Now().UnixNano(), dr.Username))

	url, err := url.Parse(dr.URI)
	if err != nil {
		return nil, err
	}

	// 设置URI为请求路径（不包含主机部分）
	ah.URI = url.RequestURI()
	// 计算摘要响应
	ah.Response = ah.computeResponse(dr)

	return ah, nil
}

// computeResponse 根据RFC 2617计算摘要认证的响应值
// @receiver ah
// @param dr *DigestRequest: 包含摘要请求信息的对象
// @return s string: 计算出的摘要响应字符串
func (ah *authorization) computeResponse(dr *DigestRequest) (s string) {

	// 计算KD(H(A1), ...) 其中KD是一个键派生函数
	kdSecret := ah.hash(ah.computeA1(dr))
	kdData := fmt.Sprintf("%s:%08x:%s:%s:%s", ah.Nonce, ah.Nc, ah.Cnonce, ah.Qop, ah.hash(ah.computeA2(dr)))

	// 计算最终的响应哈希
	return ah.hash(fmt.Sprintf("%s:%s", kdSecret, kdData))
}

// computeA1 计算摘要公式中的A1值
// @receiver ah
// @param dr *DigestRequest: 包含摘要请求信息的对象
// @return string string:  根据选定算法计算的A1值
func (ah *authorization) computeA1(dr *DigestRequest) string {

	algorithm := strings.ToUpper(ah.Algorithm)

	// 对于标准MD5和SHA-256算法，A1 = 用户名:域:密码
	if algorithm == "" || algorithm == algorithmMD5 || algorithm == algorithmSHA256 {
		return fmt.Sprintf("%s:%s:%s", ah.Username, ah.Realm, dr.Password)
	}

	// 对于会话模式的算法，A1 = H(用户名:域:密码):nonce:cnonce
	if algorithm == algorithmMD5Sess || algorithm == algorithmSHA256Sess {
		upHash := ah.hash(fmt.Sprintf("%s:%s:%s", ah.Username, ah.Realm, dr.Password))
		return fmt.Sprintf("%s:%s:%s", upHash, ah.Nonce, ah.Cnonce)
	}

	return ""
}

// computeA2 计算摘要公式中的A2值
// @receiver ah
// @param dr *DigestRequest: 包含摘要请求信息的对象
// @return string string:  根据选定算法计算的A2值
func (ah *authorization) computeA2(dr *DigestRequest) string {
	// 对于auth-int模式，A2 = 方法:uri:H(实体体)
	if strings.Contains(dr.Wa.Qop, "auth-int") {
		ah.Qop = "auth-int"
		return fmt.Sprintf("%s:%s:%s", dr.Method, ah.URI, ah.hash(dr.Body))
	}

	// 对于auth模式或未指定QOP，A2 = 方法:uri
	if dr.Wa.Qop == "auth" || dr.Wa.Qop == "" {
		ah.Qop = "auth"
		return fmt.Sprintf("%s:%s", dr.Method, ah.URI)
	}

	return ""
}

// hash 根据算法计算字符串的哈希值
// @receiver ah 
// @param a string: 需要计算哈希的字符串
// @return string string: 十六进制编码的哈希值
func (ah *authorization) hash(a string) string {
	var h hash.Hash
	algorithm := strings.ToUpper(ah.Algorithm)

	if algorithm == "" || algorithm == algorithmMD5 || algorithm == algorithmMD5Sess {
		h = md5.New()
	} else if algorithm == algorithmSHA256 || algorithm == algorithmSHA256Sess {
		h = sha256.New()
	} else {
		// unknown algorithm
		return ""
	}

	io.WriteString(h, a)
	return hex.EncodeToString(h.Sum(nil))
}

// toString 将授权对象转换为HTTP授权头的值
// @receiver ah 
// @return string string: 
func (ah *authorization) toString() string {
	var buffer bytes.Buffer

	buffer.WriteString("Digest ")

	if ah.Username != "" {
		buffer.WriteString(fmt.Sprintf("username=\"%s\", ", ah.Username))
	}

	if ah.Realm != "" {
		buffer.WriteString(fmt.Sprintf("realm=\"%s\", ", ah.Realm))
	}

	if ah.Nonce != "" {
		buffer.WriteString(fmt.Sprintf("nonce=\"%s\", ", ah.Nonce))
	}

	if ah.URI != "" {
		buffer.WriteString(fmt.Sprintf("uri=\"%s\", ", ah.URI))
	}

	if ah.Response != "" {
		buffer.WriteString(fmt.Sprintf("response=\"%s\", ", ah.Response))
	}

	if ah.Algorithm != "" {
		buffer.WriteString(fmt.Sprintf("algorithm=%s, ", ah.Algorithm))
	}

	if ah.Cnonce != "" {
		buffer.WriteString(fmt.Sprintf("cnonce=\"%s\", ", ah.Cnonce))
	}

	if ah.Opaque != "" {
		buffer.WriteString(fmt.Sprintf("opaque=\"%s\", ", ah.Opaque))
	}

	if ah.Qop != "" {
		buffer.WriteString(fmt.Sprintf("qop=%s, ", ah.Qop))
	}

	if ah.Nc != 0 {
		buffer.WriteString(fmt.Sprintf("nc=%08x, ", ah.Nc))
	}

	if ah.Userhash {
		buffer.WriteString("userhash=true, ")
	}

	s := buffer.String()

	return strings.TrimSuffix(s, ", ")
}
