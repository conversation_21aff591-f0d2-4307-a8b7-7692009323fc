// Author: chenjb
// Version: V1.0
// Date: 2025-06-13 14:54:20
// FilePath: /yaml_scan/pkg/authprovider/authx/bearer_auth.go
// Description: 实现了令牌认证(Bearer Token)策略，用于HTTP Bearer Token认证
package authx

import (
	"net/http"
	"yaml_scan/pkg/retryablehttp"
)

var (
	// 确保BearerTokenAuthStrategy实现了AuthStrategy接口
	_ AuthStrategy = &BearerTokenAuthStrategy{}
)

// BearerTokenAuthStrategy 	是令牌认证策略的实现
type BearerTokenAuthStrategy struct {
	Data *Secret
}

// NewBearerTokenAuthStrategy 创建一个新的令牌认证策略
// @param data *Secret: 
// @return *BearerTokenAuthStrategy *BearerTokenAuthStrategy: 
func NewBearerTokenAuthStrategy(data *Secret) *BearerTokenAuthStrategy {
	return &BearerTokenAuthStrategy{Data: data}
}

// Apply 将令牌认证策略应用到HTTP请求上
// 它会使用密钥中的令牌设置HTTP Authorization头
// @receiver s 
// @param req *http.Request: 
func (s *BearerTokenAuthStrategy) Apply(req *http.Request) {
	req.Header.Set("Authorization", "Bearer "+s.Data.Token)
}

// ApplyOnRR 将令牌认证策略应用到可重试的HTTP请求上
// 它会使用密钥中的令牌设置HTTP Authorization头
// @receiver s 
// @param req *retryablehttp.Request: 
func (s *BearerTokenAuthStrategy) ApplyOnRR(req *retryablehttp.Request) {
	req.Header.Set("Authorization", "Bearer "+s.Data.Token)
}

