// Author: chenjb
// Version: V1.0
// Date: 2025-06-30 17:14:41
// FilePath: /yaml_scan/pkg/tlsx/tlsx/openssl/openssl_test.go
// Description:
package openssl

import (
	"testing"
	"yaml_scan/pkg/fastdialer"
	"yaml_scan/pkg/tlsx/tlsx/clients"

	"github.com/stretchr/testify/require"
)

// TestIsAvailable 测试OpenSSL可用性检查功能
// 验证系统中OpenSSL的安装状态
func TestIsAvailable(t *testing.T) {
	// 检查OpenSSL是否可用
	available := IsAvailable()

	// 记录检查结果
	if available {
		t.Log("OpenSSL可用，可以进行完整测试")
	} else {
		t.Log("OpenSSL不可用，将跳过需要OpenSSL的测试")
	}

	// 验证返回值是布尔类型
	require.IsType(t, true, available, "IsAvailable应该返回布尔值")
}

// TestNew 测试OpenSSL客户端的创建功能
// 验证不同配置下OpenSSL客户端的正确创建
func TestNew(t *testing.T) {
	// 首先检查OpenSSL是否可用
	if !IsAvailable() {
		t.Skip("OpenSSL不可用，跳过测试")
	}
	dialer, _ := fastdialer.NewDialer(fastdialer.DefaultOptions)

	tests := []struct {
		name        string           // 测试用例名称
		options     *clients.Options // 输入的配置选项
		expectError bool             // 是否期望出现错误
		description string           // 测试用例描述
	}{
		{
			name: "默认配置创建OpenSSL客户端",
			options: &clients.Options{
				Timeout:    5,
				Retries:    3,
				Fastdialer: dialer,
			},
			expectError: false,
			description: "使用默认配置创建OpenSSL客户端应该成功",
		},
		{
			name: "启用证书验证",
			options: &clients.Options{
				Timeout:                 5,
				Retries:                 3,
				VerifyServerCertificate: true,
				Fastdialer:              dialer,
			},
			expectError: false,
			description: "启用证书验证的OpenSSL客户端应该成功创建",
		},
		{
			name: "指定最小TLS版本",
			options: &clients.Options{
				Timeout:    5,
				Retries:    3,
				MinVersion: "tls12",
				Fastdialer: dialer,
			},
			expectError: false,
			description: "指定最小TLS版本应该成功创建客户端",
		},
		{
			name: "指定最大TLS版本",
			options: &clients.Options{
				Timeout:    5,
				Retries:    3,
				MaxVersion: "tls13",
				Fastdialer: dialer,
			},
			expectError: false,
			description: "指定最大TLS版本应该成功创建客户端",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行OpenSSL客户端创建
			client, err := New(tt.options)

			if tt.expectError {
				// 期望出现错误的情况
				require.Error(t, err, "应该返回错误: %s", tt.description)
				require.Nil(t, client, "出错时client应该为nil")
			} else {
				// 期望成功的情况
				require.NoError(t, err, "不应该返回错误: %s", tt.description)
				require.NotNil(t, client, "client不应该为nil")
				require.NotNil(t, client.dialer, "dialer不应该为nil")
				require.NotNil(t, client.options, "options不应该为nil")

				t.Logf("OpenSSL客户端创建成功")
			}
		})
	}
}

// TestConnectWithOptions 测试OpenSSL连接功能
// 使用真实的网络地址进行连接测试
func TestConnectWithOptions(t *testing.T) {
	// 检查OpenSSL是否可用
	if !IsAvailable() {
		t.Skip("OpenSSL不可用，跳过连接测试")
	}
	dialer, _ := fastdialer.NewDialer(fastdialer.DefaultOptions)
	// 创建OpenSSL客户端
	client, err := New(&clients.Options{
		Timeout:    15,
		Retries:    2,
		Fastdialer: dialer,
	})
	require.NoError(t, err, "创建OpenSSL客户端不应该出错")

	tests := []struct {
		name        string                 // 测试用例名称
		hostname    string                 // 目标主机名
		ip          string                 // 目标IP地址
		port        string                 // 目标端口
		options     clients.ConnectOptions // 连接选项
		expectError bool                   // 是否期望出现错误
		description string                 // 测试用例描述
	}{
		{
			name:        "连接百度HTTPS",
			hostname:    "www.baidu.com",
			ip:          "",
			port:        "443",
			options:     clients.ConnectOptions{},
			expectError: false,
			description: "连接百度的HTTPS服务应该成功",
		},
		{
			name:     "使用SNI连接",
			hostname: "www.baidu.com",
			ip:       "",
			port:     "443",
			options: clients.ConnectOptions{
				SNI: "www.baidu.com",
			},
			expectError: false,
			description: "使用SNI连接应该成功",
		},
		{
			name:     "指定TLS版本连接",
			hostname: "www.baidu.com",
			ip:       "",
			port:     "443",
			options: clients.ConnectOptions{
				VersionTLS: "tls12",
			},
			expectError: false,
			description: "指定TLS 1.2版本连接应该成功",
		},
		{
			name:        "连接不存在的端口",
			hostname:    "www.baidu.com",
			ip:          "",
			port:        "9999",
			options:     clients.ConnectOptions{},
			expectError: true,
			description: "连接不存在的端口应该失败",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行连接
			response, err := client.ConnectWithOptions(tt.hostname, tt.ip, tt.port, tt.options)

			if tt.expectError {
				// 期望出现错误的情况
				require.Error(t, err, "应该返回错误: %s", tt.description)
			} else {
				// 期望成功的情况
				require.NoError(t, err, "不应该返回错误: %s", tt.description)
				require.NotNil(t, response, "响应不应该为nil")
				require.Equal(t, tt.port, response.Port, "端口应该匹配")
				require.Equal(t, tt.hostname, response.Host, "主机名应该匹配")

				// 验证TLS版本和密码套件
				require.NotEmpty(t, response.Version, "TLS版本不应该为空")
				require.NotEmpty(t, response.Cipher, "密码套件不应该为空")

				// 验证证书信息
				require.NotNil(t, response.CertificateResponse, "证书响应不应该为nil")

				t.Logf("连接成功 - TLS版本: %s, 密码套件: %s", response.Version, response.Cipher)
			}
		})
	}
}

// TestEnumerateCiphers 测试密码套件枚举功能
// 验证OpenSSL客户端的密码套件枚举能力
func TestEnumerateCiphers(t *testing.T) {
	// 检查OpenSSL是否可用
	if !IsAvailable() {
		t.Skip("OpenSSL不可用，跳过密码套件枚举测试")
	}
	dialer, _ := fastdialer.NewDialer(fastdialer.DefaultOptions)
	// 创建OpenSSL客户端
	client, err := New(&clients.Options{
		Timeout:    20,
		Retries:    2,
		Fastdialer: dialer,
	})
	require.NoError(t, err, "创建OpenSSL客户端不应该出错")

	// 测试密码套件枚举
	ciphers, err := client.EnumerateCiphers("www.baidu.com", "", "443", clients.ConnectOptions{
		VersionTLS:  "tls12",
		CipherLevel: []clients.CipherSecLevel{clients.All},
	})

	require.NoError(t, err, "密码套件枚举不应该出错")
	require.NotNil(t, ciphers, "密码套件列表不应该为nil")

	// 验证枚举结果
	if len(ciphers) > 0 {
		t.Logf("枚举到 %d 个密码套件", len(ciphers))

		// 验证密码套件名称格式
		for _, cipher := range ciphers {
			require.NotEmpty(t, cipher, "密码套件名称不应该为空")
		}

		// 显示前几个密码套件作为示例
		maxShow := 5
		if len(ciphers) < maxShow {
			maxShow = len(ciphers)
		}
		t.Logf("示例密码套件: %v", ciphers[:maxShow])
	} else {
		t.Log("未枚举到密码套件（可能是网络或服务器配置原因）")
	}
}

// TestSupportedTLSVersions 测试支持的TLS版本获取功能
// 验证OpenSSL客户端的版本支持信息
func TestSupportedTLSVersions(t *testing.T) {
	// 检查OpenSSL是否可用
	if !IsAvailable() {
		t.Skip("OpenSSL不可用，跳过TLS版本测试")
	}
	dialer, _ := fastdialer.NewDialer(fastdialer.DefaultOptions)
	// 创建OpenSSL客户端
	client, err := New(&clients.Options{
		Timeout:    5,
		Retries:    1,
		Fastdialer: dialer,
	})
	require.NoError(t, err, "创建OpenSSL客户端不应该出错")

	// 获取支持的TLS版本
	versions, err := client.SupportedTLSVersions()
	require.NoError(t, err, "获取支持的TLS版本不应该出错")
	require.NotNil(t, versions, "TLS版本列表不应该为nil")
	require.NotEmpty(t, versions, "TLS版本列表不应该为空")

	// 验证版本格式
	for _, version := range versions {
		require.NotEmpty(t, version, "TLS版本不应该为空")
	}

	t.Logf("支持的TLS版本: %v", versions)
}

// TestSupportedTLSCiphers 测试支持的密码套件获取功能
// 验证OpenSSL客户端的密码套件支持信息
func TestSupportedTLSCiphers(t *testing.T) {
	// 检查OpenSSL是否可用
	if !IsAvailable() {
		t.Skip("OpenSSL不可用，跳过密码套件测试")
	}
	dialer, _ := fastdialer.NewDialer(fastdialer.DefaultOptions)
	// 创建OpenSSL客户端
	client, err := New(&clients.Options{
		Timeout:    5,
		Retries:    1,
		Fastdialer: dialer,
	})
	require.NoError(t, err, "创建OpenSSL客户端不应该出错")

	// 获取支持的密码套件
	ciphers, err := client.SupportedTLSCiphers()
	require.NoError(t, err, "获取支持的密码套件不应该出错")
	require.NotNil(t, ciphers, "密码套件列表不应该为nil")
	require.NotEmpty(t, ciphers, "密码套件列表不应该为空")

	// 验证密码套件格式
	for _, cipher := range ciphers {
		require.NotEmpty(t, cipher, "密码套件名称不应该为空")
	}

	t.Logf("支持的密码套件总数: %d", len(ciphers))

	// 显示前几个密码套件作为示例
	maxShow := 10
	if len(ciphers) < maxShow {
		maxShow = len(ciphers)
	}
	t.Logf("示例密码套件: %v", ciphers[:maxShow])
}
