//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-17 10:01:18
//FilePath: /yaml_scan/pkg/retryabledns/doh/utils_test.go
//Description:

package doh

import (
	"crypto/tls"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestNewHttpClient 测试 NewHttpClient 函数的各种配置选项
func TestNewHttpClient(t *testing.T) {
	// 测试不带任何选项的情况
	client := NewHttpClient()
	require.NotNil(t, client)
	require.Equal(t, time.Duration(0), client.Timeout)

	// 测试带 WithTimeout 选项
	timeout := 10 * time.Second
	client = NewHttpClient(WithTimeout(timeout))
	require.Equal(t, timeout, client.Timeout)

	// 测试带 WithInsecureSkipVerify 选项
	client = NewHttpClient(WithInsecureSkipVerify())
	transport, ok := client.Transport.(*http.Transport)
	require.True(t, ok)
	require.NotNil(t, transport.TLSClientConfig)
	require.True(t, transport.TLSClientConfig.InsecureSkipVerify)

	// 测试带 WithProxy 选项
	proxyURL := "http://proxy.example.com:8080"
	client = NewHttpClient(WithProxy(proxyURL))
	transport, ok = client.Transport.(*http.Transport)
	require.True(t, ok)
	require.NotNil(t, transport.Proxy)
	proxy, err := transport.Proxy(nil)
	require.NoError(t, err)
	require.Equal(t, "http://proxy.example.com:8080", proxy.String())

	// 测试多个选项组合
	client = NewHttpClient(WithTimeout(timeout), WithInsecureSkipVerify(), WithProxy(proxyURL))
	require.Equal(t, timeout, client.Timeout)
	transport, ok = client.Transport.(*http.Transport)
	require.True(t, ok)
	require.NotNil(t, transport.TLSClientConfig)
	require.True(t, transport.TLSClientConfig.InsecureSkipVerify)
	require.NotNil(t, transport.Proxy)
	proxy, err = transport.Proxy(nil)
	require.NoError(t, err)
	require.Equal(t, "http://proxy.example.com:8080", proxy.String())
}

// TestWithTimeout 测试 WithTimeout 函数
func TestWithTimeout(t *testing.T) {
	timeout := 5 * time.Second
	opt := WithTimeout(timeout)
	client := &http.Client{}
	opt(client)
	require.Equal(t, timeout, client.Timeout)
}

// TestWithInsecureSkipVerify 测试 WithInsecureSkipVerify 函数
func TestWithInsecureSkipVerify(t *testing.T) {
	opt := WithInsecureSkipVerify()
	client := &http.Client{}

	// 测试 Transport 不是 *http.Transport 的情况
	opt(client)
	transport, ok := client.Transport.(*http.Transport)
	require.True(t, ok)
	require.NotNil(t, transport.TLSClientConfig)
	require.True(t, transport.TLSClientConfig.InsecureSkipVerify)

	// 测试 Transport 已经是 *http.Transport 的情况
	client.Transport = &http.Transport{}
	opt(client)
	transport, ok = client.Transport.(*http.Transport)
	require.True(t, ok)
	require.NotNil(t, transport.TLSClientConfig)
	require.True(t, transport.TLSClientConfig.InsecureSkipVerify)

	// 测试 TLSClientConfig 已经存在的情况
	client.Transport = &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: false,
		},
	}
	opt(client)
	transport, ok = client.Transport.(*http.Transport)
	require.True(t, ok)
	require.NotNil(t, transport.TLSClientConfig)
	require.True(t, transport.TLSClientConfig.InsecureSkipVerify)
}

// TestWithProxy 测试 WithProxy 函数
func TestWithProxy(t *testing.T) {
	proxyURL := "http://proxy.example.com:8080"
	opt := WithProxy(proxyURL)
	client := &http.Client{}

	// 测试 Transport 不是 *http.Transport 的情况
	opt(client)
	transport, ok := client.Transport.(*http.Transport)
	require.True(t, ok)
	require.NotNil(t, transport.Proxy)
	proxy, err := transport.Proxy(nil)
	require.NoError(t, err)
	require.Equal(t, proxyURL, proxy.String())

	// 测试 Transport 已经是 *http.Transport 的情况
	client.Transport = &http.Transport{}
	opt(client)
	transport, ok = client.Transport.(*http.Transport)
	require.True(t, ok)
	require.NotNil(t, transport.Proxy)
	proxy, err = transport.Proxy(nil)
	require.NoError(t, err)
	require.Equal(t, proxyURL, proxy.String())

	// 测试代理 URL 为空的情况
	opt = WithProxy("")
	client = &http.Client{}
	opt(client)
	require.Nil(t, client.Transport)

}
