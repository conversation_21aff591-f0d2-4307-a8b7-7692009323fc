// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-16 14:51:43
// FilePath: /yaml_scan/pkg/authprovider/authx/cookies_auth_test.go
// Description: 
package authx

import (
	"net/http"
	"testing"
	"yaml_scan/pkg/retryablehttp"

	"github.com/stretchr/testify/require"
)

// TestCookiesAuthStrategy_Apply 测试Cookie认证策略的Apply方法
// 确保它能正确设置HTTP请求的Cookie
func TestCookiesAuthStrategy_Apply(t *testing.T) {
	// 创建测试用的密钥数据，包含多个Cookie
	secret := &Secret{
		Cookies: []Cookie{
			{Key: "session", Value: "session-id-123"},
			{Key: "user", Value: "user-id-456"},
		},
	}

	// 创建Cookie认证策略
	strategy := NewCookiesAuthStrategy(secret)

	// 创建测试用的HTTP请求
	req, err := http.NewRequest("GET", "https://example.com", nil)
	require.NoError(t, err, "创建HTTP请求不应该返回错误")

	// 应用认证策略
	strategy.Apply(req)

	// 验证Cookie是否正确设置
	cookies := req.Cookies()
	require.Len(t, cookies, 2, "应该设置了2个Cookie")

	// 检查第一个Cookie
	require.Equal(t, "session", cookies[0].Name, "第一个Cookie的名称应该正确")
	require.Equal(t, "session-id-123", cookies[0].Value, "第一个Cookie的值应该正确")

	// 检查第二个Cookie
	require.Equal(t, "user", cookies[1].Name, "第二个Cookie的名称应该正确")
	require.Equal(t, "user-id-456", cookies[1].Value, "第二个Cookie的值应该正确")
}

// TestCookiesAuthStrategy_ApplyOnRR 测试Cookie认证策略的ApplyOnRR方法
// 确保它能正确处理可重试HTTP请求的Cookie，包括处理重复Cookie的情况
func TestCookiesAuthStrategy_ApplyOnRR(t *testing.T) {
	// 创建测试用的密钥数据，包含多个Cookie
	secret := &Secret{
		Cookies: []Cookie{
			{Key: "session", Value: "new-session-id"},
			{Key: "user", Value: "new-user-id"},
		},
	}

	// 创建Cookie认证策略
	strategy := NewCookiesAuthStrategy(secret)

	// 创建测试用的可重试HTTP请求，并添加一些现有的Cookie
	httpReq, err := http.NewRequest("GET", "https://example.com", nil)
	require.NoError(t, err, "创建HTTP请求不应该返回错误")

	// 添加现有的Cookie
	httpReq.AddCookie(&http.Cookie{Name: "session", Value: "old-session-id"})
	httpReq.AddCookie(&http.Cookie{Name: "existing", Value: "existing-value"})

	req, err := retryablehttp.FromRequest(httpReq)
	require.NoError(t, err, "创建可重试HTTP请求不应该返回错误")

	// 应用认证策略
	strategy.ApplyOnRR(req)

	// 验证Cookie是否正确设置和处理
	cookies := req.Request.Cookies()
	require.Len(t, cookies, 3, "应该有3个Cookie（2个新的和1个保留的）")

	// 创建一个映射来检查Cookie
	cookieMap := make(map[string]string)
	for _, cookie := range cookies {
		cookieMap[cookie.Name] = cookie.Value
	}

	// 验证Cookie值
	require.Equal(t, "new-session-id", cookieMap["session"], "session Cookie应该被更新")
	require.Equal(t, "new-user-id", cookieMap["user"], "user Cookie应该被设置")
	require.Equal(t, "existing-value", cookieMap["existing"], "existing Cookie应该被保留")
}

// TestNewCookiesAuthStrategy 测试创建Cookie认证策略的函数
// 确保它能正确创建CookiesAuthStrategy对象
func TestNewCookiesAuthStrategy(t *testing.T) {
	// 创建测试用的密钥数据
	secret := &Secret{
		Cookies: []Cookie{
			{Key: "session", Value: "session-id-123"},
		},
	}

	// 创建Cookie认证策略
	strategy := NewCookiesAuthStrategy(secret)

	// 验证策略对象是否正确创建
	require.NotNil(t, strategy, "应该创建非空的策略对象")
	require.Equal(t, secret, strategy.Data, "策略对象应该包含正确的密钥数据")
}


