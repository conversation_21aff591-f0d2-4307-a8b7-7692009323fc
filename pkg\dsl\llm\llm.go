// 
// Author: chenjb
// Version: V1.0
// Date: 2025-05-29 09:44:26
// FilePath: /yaml_scan/pkg/dsl/llm/llm.go
// Description: 大语言模型API接口封装
package llm

import (
	"context"
	"errors"
	"os"

	"github.com/sashabaranov/go-openai"
)

// client 是OpenAI API的客户端实例
var client *openai.Client

func init() {
	// 从环境变量获取OpenAI API密钥
	openaiToken := os.Getenv("OPENAI_API_KEY")
	if openaiToken != "" {
		client = openai.NewClient(openaiToken)
	}
}

// Query 向OpenAI发送请求并获取回复文本
// @param prompt string: 发送给模型的提示文本
// @param model string: 要使用的OpenAI模型名称，例如"gpt-3.5-turbo"、"gpt-4"等
// @return string string: 模型生成的回复文本
// @return error error: 可能的错误
func Query(prompt, model string) (string, error) {
	if client == nil {
		return "", errors.New("no token defined")
	}

	resp, err := client.CreateChatCompletion(
		context.Background(),
		openai.ChatCompletionRequest{
			Model: model,
			Messages: []openai.ChatCompletionMessage{
				{
					Role:    openai.ChatMessageRoleUser,
					Content: prompt,
				},
			},
		},
	)
	if err != nil {
		return "", err
	}

	if len(resp.Choices) == 0 {
		return "", errors.New("no data")
	}

	data := resp.Choices[0].Message.Content

	return data, nil
}

