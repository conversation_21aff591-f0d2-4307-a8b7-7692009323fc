// Author: chenjb
// Version: V1.0
// Date: 2025-06-24 15:58:12
// FilePath: /yaml_scan/pkg/tlsx/tlsx/openssl/util.go
// Description:
package openssl

import (
	"strings"
	"yaml_scan/pkg/gologger"

	errorutil "yaml_scan/utils/errors"
)

// AllCiphersNames 包含OpenSSL支持的所有密码套件名称
var AllCiphersNames []string = []string{}

// cipherMap 密码套件名称的快速查找映射表
var cipherMap map[string]struct{} = map[string]struct{}{}

// toOpenSSLCiphers 验证给定的密码套件并返回有效的密码套件列表
// 检查每个密码套件是否被当前OpenSSL版本支持
// @param cipher ...string: 可变参数，要验证的密码套件名称列表
// @return []string []string:  验证通过的密码套件名称列表
// @return error error: 如果某个密码套件不被支持则返回错误
func toOpenSSLCiphers(cipher ...string) ([]string, error) {
	arr := []string{}
	for _, v := range cipher {
		if _, ok := cipherMap[v]; ok {
			arr = append(arr, v)
		} else {
			return arr, errorutil.NewWithTag("openssl", "cipher suite %v not supported", v)
		}
	}
	return arr, nil
}

// parseSessionValue 从OpenSSL会话输出行中解析值
// 解析格式为"key: value"的会话数据行
// @param line string: OpenSSL输出的单行文本
// @return string string: 解析出的值，如果解析失败则返回空字符串
func parseSessionValue(line string) string {
	parts := strings.SplitN(line, ":", 2)
    if len(parts) == 2 {
        return strings.TrimSpace(parts[1])
    }
    return ""
}

// Wrap 将err2包装到err1上，即使err1为nil也会返回err2
// 这是一个错误组合函数，用于累积多个错误
// @param err1 errorutil.Error: 
// @param err2 errorutil.Error: 
// @return errorutil.Error errorutil.Error: 
func Wrap(err1 errorutil.Error, err2 errorutil.Error) errorutil.Error {
	if err1 == nil {
		return err2
	}
	return err1.Wrap(err2)
}

// certRequiredAlerts 定义表示服务器要求客户端证书的SSL警告消息列表
var certRequiredAlerts = []string{
	"SSL alert number 42",  // bad_certificate   证书无效或缺失
	"SSL alert number 116", // certificate_required 服务器明确要求客户端证书
}

// isClientCertRequired 检查OpenSSL输出以确定错误是否由于服务器要求客户端证书
// 该函数通过分析OpenSSL的错误输出来判断是否需要进行双向TLS认证
// @param data string:
// @return bool bool:
func isClientCertRequired(data string) bool {
	// 检查每行是否包含客户端证书要求的警告信息
	for _, line := range strings.Split(data, "\n") {
		for _, alert := range certRequiredAlerts {
			if strings.Contains(line, alert) {
				return true
			}
		}
	}
	return false
}

func init() {
	// 检查OpenSSL是否在系统中可用
	if !IsAvailable() {
		return
	}
	// 获取OpenSSL支持的所有密码套件
	ciphers, err := getCiphers()
	if err != nil {
		gologger.Debug().Label("openssl").Msg(err.Error())
	}
	// 构建密码套件数据结构
	for _, v := range ciphers {
		cipherMap[v] = struct{}{}
		AllCiphersNames = append(AllCiphersNames, v)
	}
}
