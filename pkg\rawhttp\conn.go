// Author: chenjb
// Version: V1.0
// Date: 2025-07-22 17:24:40
// FilePath: /yaml_scan/pkg/rawhttp/conn.go
// Description: 网络连接管理模块，提供HTTP/HTTPS连接的建立、管理和复用功能

// Package rawhttp 网络连接管理模块
package rawhttp

import (
	"context"                         // 上下文包，用于控制超时和取消
	"crypto/tls"                      // TLS加密包
	"fmt"                             // 格式化输出包
	"io"                              // 输入输出接口包
	"net"                             // 网络包
	"net/url"                         // URL解析包
	"strings"                         // 字符串处理包
	"sync"                            // 同步原语包
	"time"                            // 时间处理包
	"yaml_scan/pkg/fastdialer"        // 快速拨号器包
	"yaml_scan/pkg/rawhttp/client"    // HTTP客户端包
	"yaml_scan/pkg/rawhttp/proxy"     // 代理支持包
)

// Dialer 定义了拨号器接口，用于建立到远程HTTP服务器的连接
// 支持普通连接、代理连接和带超时的连接
type Dialer interface {
	// Dial 拨号连接到远程HTTP服务器
	// 参数:
	//   protocol: 协议类型（http或https）
	//   addr: 目标服务器地址
	//   options: 连接选项配置
	// 返回:
	//   Conn: 建立的连接对象
	//   error: 错误信息，连接成功时为nil
	Dial(protocol, addr string, options *Options) (Conn, error)

	// DialWithProxy 通过代理拨号连接到远程HTTP服务器
	// 参数:
	//   protocol: 协议类型
	//   addr: 目标服务器地址
	//   proxyURL: 代理服务器URL
	//   timeout: 连接超时时间
	//   options: 连接选项配置
	// 返回:
	//   Conn: 建立的连接对象
	//   error: 错误信息，连接成功时为nil
	DialWithProxy(protocol, addr, proxyURL string, timeout time.Duration, options *Options) (Conn, error)

	// DialTimeout 带超时的拨号连接到远程HTTP服务器
	// 参数:
	//   protocol: 协议类型
	//   addr: 目标服务器地址
	//   timeout: 连接超时时间
	//   options: 连接选项配置
	// 返回:
	//   Conn: 建立的连接对象
	//   error: 错误信息，连接成功时为nil
	DialTimeout(protocol, addr string, timeout time.Duration, options *Options) (Conn, error)
}

// dialer 拨号器的具体实现，支持连接复用
type dialer struct {
	sync.Mutex                   // 互斥锁，保护以下字段的并发访问
	conns      map[string][]Conn // 连接池，将地址映射到可能为空的现有连接切片
}

// Dial 实现Dialer接口的Dial方法，建立到远程服务器的连接
// 参数:
//   protocol: 协议类型（http或https）
//   addr: 目标服务器地址
//   options: 连接选项配置
// 返回:
//   Conn: 建立的连接对象
//   error: 错误信息，连接成功时为nil
// 功能: 调用dialTimeout方法，超时时间设为0（无超时限制）
func (d *dialer) Dial(protocol, addr string, options *Options) (Conn, error) {
	return d.dialTimeout(protocol, addr, 0, options) // 调用带超时的拨号方法，超时设为0
}

// DialTimeout 实现Dialer接口的DialTimeout方法，带超时的连接建立
// 参数:
//   protocol: 协议类型
//   addr: 目标服务器地址
//   timeout: 连接超时时间
//   options: 连接选项配置
// 返回:
//   Conn: 建立的连接对象
//   error: 错误信息，连接成功时为nil
// 功能: 调用dialTimeout方法建立带超时的连接
func (d *dialer) DialTimeout(protocol, addr string, timeout time.Duration, options *Options) (Conn, error) {
	return d.dialTimeout(protocol, addr, timeout, options) // 调用内部的带超时拨号方法
}


// dialTimeout 内部拨号方法，支持连接复用和超时控制
// 参数:
//   protocol: 协议类型
//   addr: 目标服务器地址
//   timeout: 连接超时时间
//   options: 连接选项配置
// 返回:
//   Conn: 建立的连接对象
//   error: 错误信息，连接成功时为nil
// 功能: 首先尝试从连接池获取现有连接，如果没有则创建新连接
func (d *dialer) dialTimeout(protocol, addr string, timeout time.Duration, options *Options) (Conn, error) {
	d.Lock() // 加锁保护连接池的并发访问
	// 如果连接池未初始化，则初始化
	if d.conns == nil {
		d.conns = make(map[string][]Conn)
	}
	// 检查是否有可复用的连接
	if c, ok := d.conns[addr]; ok {
		if len(c) > 0 {
			conn := c[0]                // 获取第一个连接
			c[0] = c[len(c)-1]         // 将最后一个连接移到第一个位置
			d.Unlock()                 // 解锁
			return conn, nil           // 返回复用的连接
		}
	}
	d.Unlock() // 解锁
	// 如果没有可复用的连接，创建新连接
	c, err := clientDial(protocol, addr, timeout, options)
	return &conn{
		Client: client.NewClient(c), // 创建HTTP客户端
		Conn:   c,                   // 设置底层网络连接
		dialer: d,                   // 设置拨号器引用
	}, err
}


// DialWithProxy 通过代理服务器建立到目标服务器的连接
// 参数:
//   protocol: 协议类型（http或https）
//   addr: 目标服务器地址
//   proxyURL: 代理服务器URL
//   timeout: 连接超时时间
//   options: 连接选项配置
// 返回:
//   Conn: 建立的连接对象
//   error: 错误信息，连接成功时为nil
// 功能: 支持HTTP和SOCKS5代理，对于HTTPS还会进行TLS握手
func (d *dialer) DialWithProxy(protocol, addr, proxyURL string, timeout time.Duration, options *Options) (Conn, error) {
	var c net.Conn
	// 解析代理URL
	u, err := url.Parse(proxyURL)
	if err != nil {
		return nil, fmt.Errorf("unsupported proxy error: %w", err)
	}
	// 根据代理协议类型选择相应的拨号器
	switch u.Scheme {
	case "http":
		// 使用HTTP代理拨号器
		c, err = proxy.HTTPFastDialer(proxyURL, timeout, options.FastDialer)(addr)
	case "socks5", "socks5h":
		// 使用SOCKS5代理拨号器
		c, err = proxy.Socks5Dialer(proxyURL, timeout)(addr)
	default:
		// 不支持的代理协议
		return nil, fmt.Errorf("unsupported proxy protocol: %s", proxyURL)
	}
	if err != nil {
		return nil, fmt.Errorf("proxy error: %w", err) // 代理连接失败
	}
	// 如果是HTTPS协议，需要进行TLS握手
	if protocol == "https" {
		if c, err = TlsHandshake(c, addr, timeout); err != nil {
			return nil, fmt.Errorf("tls handshake error: %w", err)
		}
	}
	// 返回包装后的连接对象
	return &conn{
		Client: client.NewClient(c), // 创建HTTP客户端
		Conn:   c,                   // 设置底层网络连接
		dialer: d,                   // 设置拨号器引用
	}, err
}

// clientDial 客户端拨号函数，根据协议类型建立相应的连接
// 参数:
//   protocol: 协议类型（http或https）
//   addr: 目标服务器地址
//   timeout: 连接超时时间
//   options: 连接选项配置
// 返回:
//   net.Conn: 建立的网络连接
//   error: 错误信息，连接成功时为nil
// 功能: 为HTTP使用普通TCP连接，为HTTPS使用TLS加密连接
func clientDial(protocol, addr string, timeout time.Duration, options *Options) (net.Conn, error) {
	var (
		ctx    context.Context      // 上下文对象
		cancel context.CancelFunc   // 取消函数
	)
	// 如果设置了超时时间，创建带超时的上下文
	if timeout > 0 {
		ctx, cancel = context.WithTimeout(context.Background(), timeout)
		defer cancel() // 确保在函数结束时取消上下文
	} else {
		ctx = context.Background() // 使用背景上下文
	}

	// http - 处理HTTP协议连接
	if protocol == "http" {
		// 优先使用快速拨号器
		if options.FastDialer != nil {
			return options.FastDialer.Dial(ctx, "tcp", addr)
		} else if timeout > 0 {
			// 使用带超时的标准拨号
			return net.DialTimeout("tcp", addr, timeout)
		}
		// 使用标准拨号（无超时）
		return net.Dial("tcp", addr)
	}

	// https - 处理HTTPS协议连接
	// 配置TLS设置：跳过证书验证，允许重新协商
	tlsConfig := &tls.Config{InsecureSkipVerify: true, Renegotiation: tls.RenegotiateOnceAsClient}
	// 如果指定了SNI（服务器名称指示），设置服务器名称
	if options.SNI != "" {
		tlsConfig.ServerName = options.SNI
	}

	// 如果没有快速拨号器，尝试创建一个
	if options.FastDialer == nil {
		// always use fastdialer tls dial if available - 优先使用快速拨号器的TLS拨号
		opts := fastdialer.DefaultOptions
		if timeout > 0 {
			opts.DialerTimeout = timeout // 设置拨号超时
		}
		var err error
		options.FastDialer, err = fastdialer.NewDialer(opts)
		// use net.Dialer if fastdialer tls dial is not available - 如果快速拨号器不可用，使用标准拨号器
		if err != nil {
			var dialer *net.Dialer
			if timeout > 0 {
				dialer = &net.Dialer{Timeout: timeout}
			} else {
				dialer = &net.Dialer{Timeout: 8 * time.Second} // should be more than enough - 默认8秒超时应该足够
			}
			return tls.DialWithDialer(dialer, "tcp", addr, tlsConfig)
		}
	}

	// 使用快速拨号器建立TLS连接
	return options.FastDialer.DialTLS(ctx, "tcp", addr)
}

// TlsHandshake 在普通连接上执行TLS握手
// 参数:
//   conn: 已建立的普通网络连接
//   addr: 目标服务器地址
//   timeout: 握手超时时间
// 返回:
//   net.Conn: TLS加密后的连接
//   error: 错误信息，握手成功时为nil
// 功能: 将普通TCP连接升级为TLS加密连接
func TlsHandshake(conn net.Conn, addr string, timeout time.Duration) (net.Conn, error) {
	// 从地址中提取主机名（去掉端口号）
	colonPos := strings.LastIndex(addr, ":")
	if colonPos == -1 {
		colonPos = len(addr) // 如果没有冒号，使用整个地址长度
	}
	hostname := addr[:colonPos] // 提取主机名部分

	var (
		ctx    context.Context      // 上下文对象
		cancel context.CancelFunc   // 取消函数
	)
	// 如果设置了超时时间，创建带超时的上下文
	if timeout > 0 {
		ctx, cancel = context.WithTimeout(context.Background(), timeout)
		defer cancel() // 确保在函数结束时取消上下文
	} else {
		ctx = context.Background() // 使用背景上下文
	}

	// 创建TLS客户端连接，配置跳过证书验证和服务器名称
	tlsConn := tls.Client(conn, &tls.Config{
		InsecureSkipVerify: true, // 跳过证书验证（用于测试环境）
		ServerName:         hostname, // 设置服务器名称用于SNI
	})
	// 执行TLS握手
	if err := tlsConn.HandshakeContext(ctx); err != nil {
		return nil, err // 握手失败返回错误
	}
	return tlsConn, nil // 返回TLS连接
}





// Conn 定义了连接接口，组合了HTTP客户端和网络连接功能
// 继承了client.Client的所有HTTP操作方法和io.Closer的关闭方法
// 同时提供了连接超时控制和连接释放功能
type Conn interface {
	client.Client                         // HTTP客户端接口，提供HTTP请求/响应功能
	io.Closer                            // 关闭接口，提供Close方法

	SetDeadline(time.Time) error         // 设置连接的读写截止时间
	SetReadDeadline(time.Time) error     // 设置连接的读取截止时间
	SetWriteDeadline(time.Time) error    // 设置连接的写入截止时间
	Release()                            // 释放连接回连接池以供复用
}


// conn 连接的具体实现，组合了HTTP客户端、网络连接和拨号器
type conn struct {
	client.Client // HTTP客户端，提供HTTP操作功能
	net.Conn      // 底层网络连接，提供网络I/O功能
	*dialer       // 拨号器引用，用于连接管理和复用
}

// Release 释放连接回连接池以供后续复用
// 功能: 将当前连接添加回拨号器的连接池中，实现连接复用
func (c *conn) Release() {
	c.dialer.Lock()   // 加锁保护连接池
	defer c.dialer.Unlock() // 确保解锁
	addr := c.Conn.RemoteAddr().String() // 获取远程地址字符串
	// 将当前连接添加到对应地址的连接池中
	c.dialer.conns[addr] = append(c.dialer.conns[addr], c)
}