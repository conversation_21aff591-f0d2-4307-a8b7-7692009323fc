// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 17:08:25
// FilePath: /yaml_scan/pkg/retryablehttp/digest_auth_client/digest_auth_client.go
// Description: 实现HTTP摘要认证客户端，提供自动处理HTTP摘要认证的功能
package digestauthclient

import (
	"bytes"
	"crypto/tls"
	"fmt"
	"net/http"
	"time"
)

// DigestRequest 包含执行摘要认证HTTP请求所需的所有信息
type DigestRequest struct {
	Body       string           // 请求体内容
	Method     string           // HTTP方法（GET、POST等）
	Password   string           // 用户密码
	URI        string           // 请求的URI
	Username   string           // 用户名
	Header     http.Header      // HTTP请求头
	Auth       *authorization   // 授权信息，用于后续请求
	Wa         *wwwAuthenticate // 从服务器收到的认证挑战信息
	CertVal    bool             // 是否验证TLS证书
	HTTPClient *http.Client     // 自定义HTTP客户端
}

// DigestTransport t 实现http.RoundTripper接口，用于自动处理摘要认证
type DigestTransport struct {
	Password   string       // 用户密码
	Username   string       // 用户名
	HTTPClient *http.Client // 自定义HTTP客户端
}

// NewRequest 创建一个新的DigestRequest对象
// @param username string: 用户名
// @param password string: 用户密码
// @param method string: HTTP方法
// @param uri string:  请求的URI
// @param body string: 请求体内容
// @return DigestRequest DigestRequest: 配置好的摘要认证请求对象
func NewRequest(username, password, method, uri, body string) DigestRequest {
	dr := DigestRequest{}
	dr.UpdateRequest(username, password, method, uri, body)
	dr.CertVal = false
	return dr
}

// NewTransport 创建一个新的DigestTransport对象
// @param username string: 用户名
// @param password string: 用户密码
// @return DigestTransport DigestTransport:  配置好的摘要认证传输对象
func NewTransport(username, password string) DigestTransport {
	dt := DigestTransport{}
	dt.Password = password
	dt.Username = username
	return dt
}

// getHTTPClient 获取用于执行HTTP请求的客户端
// @receiver dr 
// @return *http.Client *http.Client:  配置好的HTTP客户端
func (dr *DigestRequest) getHTTPClient() *http.Client {
	// 如果已经有HTTP客户端，直接返回
	if dr.HTTPClient != nil {
		return dr.HTTPClient
	}
	// 创建TLS配置
	tlsConfig := tls.Config{}
	if !dr.CertVal {
		tlsConfig.InsecureSkipVerify = true
	}

	// 创建并返回新的HTTP客户端
	return &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tlsConfig,
		},
	}
}

// UpdateRequest 在想要重用现有连接时更新请求信息
// @receiver dr 
// @param username string: 用户名
// @param password string: 用户密码
// @param method string:  HTTP方法
// @param uri string: 请求的uri
// @param body string: 请求体内容
// @return *DigestRequest *DigestRequest: 更新后的请求对象，方便链式调用
func (dr *DigestRequest) UpdateRequest(username, password, method, uri, body string) *DigestRequest {
	dr.Body = body
	dr.Method = method
	dr.Password = password
	dr.URI = uri
	dr.Username = username
	dr.Header = make(map[string][]string)
	return dr
}

// RoundTrip 实现http.RoundTripper接口的方法
// @receiver dt 
// @param req *http.Request: HTTP请求
// @return resp *http.Response: HTTP响应
// @return err error: 可能的错误
func (dt *DigestTransport) RoundTrip(req *http.Request) (resp *http.Response, err error) {
	// 提取认证所需信息
	username := dt.Username
	password := dt.Password
	method := req.Method
	uri := req.URL.String()

	var body string
	if req.Body != nil {
		buf := new(bytes.Buffer)
		buf.ReadFrom(req.Body)
		body = buf.String()
	}

	// 创建摘要请求
	dr := NewRequest(username, password, method, uri, body)
	if dt.HTTPClient != nil {
		dr.HTTPClient = dt.HTTPClient
	}
	dr.Header.Set("User-Agent", req.Header.Get("User-Agent"))

	// 执行请求
	return dr.Execute()
}

// Execute 初始化请求并获取响应
// @receiver dr 
// @return resp *http.Response: HTTP响应
// @return err error: 可能的错误
func (dr *DigestRequest) Execute() (resp *http.Response, err error) {
	// 如果已经有授权信息，使用它执行请求
	if dr.Auth != nil {
		return dr.executeExistingDigest()
	}

	// 创建初始HTTP请求
	var req *http.Request
	if req, err = http.NewRequest(dr.Method, dr.URI, bytes.NewReader([]byte(dr.Body))); err != nil {
		return nil, err
	}
	req.Header = dr.Header

	// 获取HTTP客户端并执行请求
	client := dr.getHTTPClient()

	if resp, err = client.Do(req); err != nil {
		return nil, err
	}

	// 如果返回401未授权，处理摘要认证
	if resp.StatusCode == 401 {
		return dr.executeNewDigest(resp)
	}

	// return the resp to user to handle resp.body.Close()
	return resp, nil
}

// executeNewDigest 处理新的摘要认证挑战
// @receiver dr 
// @param resp *http.Response: 401响应，包含WWW-Authenticate头
// @return resp2 *http.Response: 带授权的HTTP请求的响应
// @return err error: 可能的错误
func (dr *DigestRequest) executeNewDigest(resp *http.Response) (resp2 *http.Response, err error) {
	var (
		auth     *authorization   // 授权信息
		wa       *wwwAuthenticate // 认证挑战信息
		waString string           // WWW-Authenticate头的值
	)

	// 认证不需要响应体，关闭它
	resp.Body.Close()

	if waString = resp.Header.Get("WWW-Authenticate"); waString == "" {
		return nil, fmt.Errorf("failed to get WWW-Authenticate header, please check your server configuration")
	}
	// 解析WWW-Authenticate头
	wa = newWwwAuthenticate(waString)
	dr.Wa = wa

	// 根据认证挑战创建授权信息
	if auth, err = newAuthorization(dr); err != nil {
		return nil, err
	}

	// 使用授权信息执行请求
	if resp2, err = dr.executeRequest(auth.toString()); err != nil {
		return nil, err
	}

	dr.Auth = auth
	return resp2, nil
}

// executeExistingDigest 使用现有的授权信息执行请求
// @receiver dr 
// @return resp *http.Response:  HTTP响应
// @return err error: 可能的错误
func (dr *DigestRequest) executeExistingDigest() (resp *http.Response, err error) {
	var auth *authorization

	// 刷新授权信息（更新nonce计数器等）
	if auth, err = dr.Auth.refreshAuthorization(dr); err != nil {
		return nil, err
	}
	dr.Auth = auth

	// 执行带授权的请求
	return dr.executeRequest(dr.Auth.toString())
}

// executeRequest 执行带授权头的HTTP请求
// @receiver dr 
// @param authString string: 授权头的值
// @return resp *http.Response: HTTP响应
// @return err error: 可能的错误
func (dr *DigestRequest) executeRequest(authString string) (resp *http.Response, err error) {
	var req *http.Request

	// 创建HTTP请求
	if req, err = http.NewRequest(dr.Method, dr.URI, bytes.NewReader([]byte(dr.Body))); err != nil {
		return nil, err
	}
	req.Header = dr.Header
	req.Header.Add("Authorization", authString)

	// 获取HTTP客户端并执行请求
	client := dr.getHTTPClient()
	return client.Do(req)
}
