//
// Author: chenjb
// Version: V1.0
// Date: 2025-05-20 16:52:23
// FilePath: /yaml_scan/pkg/fastdialer/metafiles/hostfile.go
// Description: 提供各种元数据文件的解析和管理功能

package metafiles

import (
	"bufio"
	"net"
	"os"
	"path/filepath"
	"runtime"
	"runtime/debug"
	"strings"
	"sync"

	"yaml_scan/pkg/hybridMap/hybrid"
	"yaml_scan/pkg/retryabledns"

	"github.com/dimchansky/utfbom"
)

type StorageType int

// CommentChar 定义注释字符标记
const CommentChar string = "#"

var (
	// MaxHostsEntires 限制从hosts文件中读取的最大条目数
	MaxHostsEntires = 4096
	// LoadAllEntries 是一个开关，当为true时将所有条目加载到混合存储后端
	// 即使请求了内存存储后端也会使用混合存储
	LoadAllEntries = false
)

const (
	// InMemory 表示仅使用内存存储
	InMemory StorageType = iota
	// Hybrid 表示使用混合存储（内存+磁盘）
	Hybrid
)

// HostsFilePath 系统hosts文件的默认路径
const HostsFilePath = "/etc/hosts"

// IsComment  检查行是否为注释
// @param raw string:  要检查的行内容
// @return bool bool: 如果行是注释则返回true，否则返回false
func IsComment(raw string) bool {
	return strings.HasPrefix(strings.TrimSpace(raw), CommentChar)
}

// HasComment 检查行是否包含注释
// @param raw string: 要检查的行内容
// @return bool bool: 如果行是注释则返回true，否则返回false
func HasComment(raw string) bool {
	return strings.Contains(raw, CommentChar)
}

// == 按需初始化内存hosts映射
var (
	// hmMem 内存存储的hosts映射实例
	hmMem *hybrid.HybridMap
	// hmErr 初始化过程中可能发生的错误
	hmErr error
	// hostMemInit 用于确保内存hosts映射只初始化一次的函数
	hostMemInit = sync.OnceFunc(func() {
		// 使用默认内存选项
		opts := hybrid.DefaultMemoryOptions
		// 创建新的混合映射
		hmMem, hmErr = hybrid.New(opts)
		if hmErr != nil {
			return
		}
		// 加载hosts文件到内存
		hmErr = loadHostsFile(hmMem, MaxHostsEntires)
		if hmErr != nil {
			hmMem.Close()
			return
		}
	})
)

// getHFInMemory 获取内存中的hosts文件数据
// @return *hybrid.HybridMap *hybrid.HybridMap: 内存中的hosts映射
// @return error error: 初始化过程中可能发生的错误
func getHFInMemory() (*hybrid.HybridMap, error) {
	hostMemInit()
	return hmMem, hmErr
}

// == 按需初始化混合存储hosts映射
var (
	// hmHybrid 混合存储的hosts映射实例
	hmHybrid *hybrid.HybridMap
	// hmHybErr 初始化过程中可能发生的错误
	hmHybErr error
	// hmHybInit 用于确保混合存储hosts映射只初始化一次的函数
	hmHybInit = sync.OnceFunc(func() {
		opts := hybrid.DefaultHybridOptions
		// 启用自动清理
		opts.Cleanup = true
		hmHybrid, hmHybErr = hybrid.New(opts)
		if hmHybErr != nil {
			return
		}
		// 加载hosts文件到混合存储，-1表示加载所有条目
		hmHybErr = loadHostsFile(hmHybrid, -1)
		if hmHybErr != nil {
			hmHybrid.Close()
			return
		}
		// 设置最终处理器以进行清理
		runtime.SetFinalizer(hmHybrid, func(hm *hybrid.HybridMap) {
			_ = hm.Close()
		})
	})
)

// getHFHybridStorage 获取混合存储中的hosts文件数据
// @return *hybrid.HybridMap *hybrid.HybridMap: 混合存储中的hosts映射
// @return error error: 始化过程中可能发生的错误
func getHFHybridStorage() (*hybrid.HybridMap, error) {
	hmHybInit()
	return hmHybrid, hmHybErr
}

// GetHostsFileDnsData 返回在程序生命周期内不变的DNS数据
// @param storage StorageType: 存储类型（内存或混合）
// @return *hybrid.HybridMap *hybrid.HybridMap: hosts文件DNS数据映射
// @return error error: 获取过程中可能发生的错误
func GetHostsFileDnsData(storage StorageType) (*hybrid.HybridMap, error) {
	// 如果设置了加载所有条目，强制使用混合存储
	if LoadAllEntries {
		storage = Hybrid
	}
	switch storage {
	case InMemory:
		// 返回内存存储
		return getHFInMemory()
	case Hybrid:
		// 返回混合存储
		return getHFHybridStorage()
	}
	return getHFInMemory()
}

// loadHostsFile 从hosts文件加载条目到指定的混合映射
// @param hm *hybrid.HybridMap: 目标混合映射
// @param max int: 最大加载条目数，-1表示无限制
// @return error error:  加载过程中可能发生的错误
func loadHostsFile(hm *hybrid.HybridMap, max int) error {
	// 展开环境变量并转换路径分隔符
	osHostsFilePath := os.ExpandEnv(filepath.FromSlash(HostsFilePath))

	// 如果设置了HOSTS_PATH环境变量，则使用它
	if env, isset := os.LookupEnv("HOSTS_PATH"); isset && len(env) > 0 {
		osHostsFilePath = os.ExpandEnv(filepath.FromSlash(env))
	}

	// 打开hosts文件
	file, err := os.Open(osHostsFilePath)
	if err != nil {
		return err
	}
	defer file.Close()

	// 存储DNS数据的映射
	dnsDatas := make(map[string]retryabledns.DNSData)
	// 创建扫描器，跳过UTF字节顺序标记
	scanner := bufio.NewScanner(utfbom.SkipOnly(file))
	for scanner.Scan() {
		// 检查是否达到最大条目限制
		if max > 0 && len(dnsDatas) == MaxHostsEntires {
			break
		}
		// 处理hosts文件的一行
		ip, hosts := HandleHostLine(scanner.Text())
		if ip == "" || len(hosts) == 0 {
			continue
		}
		// 解析IP地址
		netIP := net.ParseIP(ip)
		isIPv4 := netIP.To4() != nil
		isIPv6 := netIP.To16() != nil

		// 将IP添加到主机的DNS数据中
		for _, host := range hosts {
			dnsdata, ok := dnsDatas[host]
			if !ok {
				// 创建新的DNS数据
				dnsdata = retryabledns.DNSData{Host: host}
			}
			if isIPv4 {
				// 添加IPv4地址
				dnsdata.A = append(dnsdata.A, ip)
			} else if isIPv6 {
				// 添加IPv6地址
				dnsdata.AAAA = append(dnsdata.AAAA, ip)
			}
			dnsDatas[host] = dnsdata
		}
	}
	// 将所有DNS数据存入混合映射
	for host, dnsdata := range dnsDatas {
		// 序列化DNS数据
		dnsdataBytes, _ := dnsdata.Marshal()
		_ = hm.Set(host, dnsdataBytes)
	}
	// 释放内存（当加载大型hosts文件时）
	if len(dnsDatas) > 10000 && max < 0 {
		// 这会在加载大型hosts文件时释放内存
		// 在加载所有条目到混合存储时很有用
		debug.FreeOSMemory()
	}
	return nil
}

// HandleHostLine  处理hosts文件的一行
// @param raw string: 行的原始内容
// @return ip string: 解析出的IP地址
// @return hosts []string: 关联的主机名列表
func HandleHostLine(raw string) (ip string, hosts []string) {
	// 忽略注释行
	if IsComment(raw) {
		return
	}

	// 修剪注释
	if HasComment(raw) {
		commentSplit := strings.Split(raw, CommentChar)
		raw = commentSplit[0]
	}

	fields := strings.Fields(raw)
	if len(fields) == 0 {
		return
	}

	// 第一个字段应该是IP地址
	ip = fields[0]
	if net.ParseIP(ip) == nil {
		return
	}

	// 剩余字段是主机名
	hosts = fields[1:]
	return
}
