// Author: chenjb
// Version: V1.0
// Date: 2025-06-18 16:01:56
// FilePath: /yaml_scan/pkg/utils/freeport/freeport_test.go
// Description:
package freeport

import (
	"net"
	"strconv"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestGetFreeTCPPort 测试获取空闲TCP端口功能
func TestGetFreeTCPPort(t *testing.T) {
	// 测试在本地回环地址上获取空闲TCP端口
	port, err := GetFreeTCPPort("127.0.0.1")
	require.NoError(t, err, "获取空闲TCP端口应该成功")
	require.NotNil(t, port, "获取的端口不应为空")
	require.Equal(t, "127.0.0.1", port.Address, "端口地址应该匹配")
	require.Equal(t, TCP, port.Protocol, "端口协议应该是TCP")
	require.Greater(t, port.Port, 0, "端口号应该大于0")
	require.Contains(t, port.NetListenAddress, strconv.Itoa(port.Port), "监听地址应包含端口号")

	// 测试端口可用性
	addr, err := net.ResolveTCPAddr("tcp", net.JoinHostPort("127.0.0.1", strconv.Itoa(port.Port)))
	require.NoError(t, err, "解析TCP地址应该成功")
	l, err := net.ListenTCP("tcp", addr)
	require.NoError(t, err, "应该能够在获取的端口上监听")
	defer l.Close()
}

// TestGetFreeUDPPort 测试获取空闲UDP端口功能
func TestGetFreeUDPPort(t *testing.T) {
	// 测试在本地回环地址上获取空闲UDP端口
	port, err := GetFreeUDPPort("127.0.0.1")
	require.NoError(t, err, "获取空闲UDP端口应该成功")
	require.NotNil(t, port, "获取的端口不应为空")
	require.Equal(t, "127.0.0.1", port.Address, "端口地址应该匹配")
	require.Equal(t, UDP, port.Protocol, "端口协议应该是UDP")
	require.Greater(t, port.Port, 0, "端口号应该大于0")
	require.Contains(t, port.NetListenAddress, strconv.Itoa(port.Port), "监听地址应包含端口号")

	// 测试端口可用性
	addr, err := net.ResolveUDPAddr("udp", net.JoinHostPort("127.0.0.1", strconv.Itoa(port.Port)))
	require.NoError(t, err, "解析UDP地址应该成功")
	
	l, err := net.ListenUDP("udp", addr)
	require.NoError(t, err, "应该能够在获取的端口上监听")
	defer l.Close()
}

// TestGetPort 测试获取指定端口功能
func TestGetPort(t *testing.T) {
	// 先获取一个空闲端口
	tcpPort, err := GetFreeTCPPort("127.0.0.1")
	require.NoError(t, err, "获取空闲TCP端口应该成功")
	
	// 测试获取该指定端口
	port, err := GetPort(TCP, "127.0.0.1", tcpPort.Port)
	require.NoError(t, err, "获取指定TCP端口应该成功")
	require.Equal(t, tcpPort.Port, port.Port, "端口号应该匹配")
	
	// 测试获取已占用端口
	l, err := net.Listen("tcp", net.JoinHostPort("127.0.0.1", strconv.Itoa(tcpPort.Port)))
	require.NoError(t, err, "应该能够在获取的端口上监听")
	
	// 尝试获取已占用的端口应该失败
	_, err = GetPort(TCP, "127.0.0.1", tcpPort.Port)
	require.Error(t, err, "获取已占用的端口应该失败")
	
	l.Close()
}

// TestGetFreePortInRange 测试在端口范围内获取空闲端口功能
func TestGetFreePortInRange(t *testing.T) {
	// 测试无效的端口范围
	_, err := GetFreePortInRange("127.0.0.1", TCP, 9000, 8000)
	require.Error(t, err, "端口范围无效应该返回错误")
	require.Contains(t, err.Error(), "invalid interval", "错误消息应包含'invalid interval'")
	
	// 测试有效的端口范围
	port, err := GetFreePortInRange("127.0.0.1", TCP, 8000, 9000)
	require.NoError(t, err, "在有效范围内获取端口应该成功")
	require.NotNil(t, port, "获取的端口不应为空")
	require.GreaterOrEqual(t, port.Port, 8000, "端口号应该大于等于范围下限")
	require.LessOrEqual(t, port.Port, 9000, "端口号应该小于等于范围上限")
}

// TestGetFreePorts 测试获取多个空闲端口功能
func TestGetFreePorts(t *testing.T) {
	count := 3
	ports, err := GetFreePorts("127.0.0.1", TCP, count)
	require.NoError(t, err, "获取多个空闲端口应该成功")
	require.Len(t, ports, count, "应该获取到指定数量的端口")
	
	// 检查所有端口是否唯一
	portMap := make(map[int]bool)
	for _, port := range ports {
		require.False(t, portMap[port.Port], "获取的端口不应重复")
		portMap[port.Port] = true
	}
}

// TestMustGetFreeTCPPort 测试必须获取TCP端口功能
func TestMustGetFreeTCPPort(t *testing.T) {
	port := MustGetFreeTCPPort("127.0.0.1")
	require.NotNil(t, port, "获取的端口不应为空")
	require.Equal(t, TCP, port.Protocol, "端口协议应该是TCP")
	require.Greater(t, port.Port, 0, "端口号应该大于0")
}

// TestMustGetFreeUDPPort 测试必须获取UDP端口功能
func TestMustGetFreeUDPPort(t *testing.T) {
	port := MustGetFreeUDPPort("127.0.0.1")
	require.NotNil(t, port, "获取的端口不应为空")
	require.Equal(t, UDP, port.Protocol, "端口协议应该是UDP")
	require.Greater(t, port.Port, 0, "端口号应该大于0")
}

// TestGetFreePortOnInterface 测试在指定网络接口上获取空闲端口功能
func TestGetFreePortOnInterface(t *testing.T) {
	// 获取本机上的一个网络接口
	interfaces, err := net.Interfaces()
	require.NoError(t, err, "获取网络接口列表应该成功")
	require.NotEmpty(t, interfaces, "网络接口列表不应为空")
	
	// 查找一个有效的接口
	var validInterface string
	for _, itf := range interfaces {
		addrs, err := itf.Addrs()
		if err == nil && len(addrs) > 0 {
			validInterface = itf.Name
			break
		}
	}
	
	if validInterface != "" {
		port, err := GetFreePortOnInterface(validInterface, TCP)
		if err == nil {
			// 如果成功找到端口
			require.NotNil(t, port, "获取的端口不应为空")
			require.Equal(t, TCP, port.Protocol, "端口协议应该是TCP")
			require.Greater(t, port.Port, 0, "端口号应该大于0")
		} else {
			// 这里不做断言，因为在某些环境下可能无法在特定接口上获取端口
			t.Logf("在接口 %s 上获取端口失败: %v", validInterface, err)
		}
	} else {
		t.Skip("找不到有效的网络接口，跳过测试")
	}
} 

