// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-30 11:56:01
// FilePath: /yaml_scan/pkg/tlsx/tlsx/ztls/ja3/ja3_test.go
// Description: 

package ja3

import (
	"testing"

	"github.com/zmap/zcrypto/tls"
	"github.com/stretchr/testify/require"
)

// TestConstants 测试常量定义
// 验证包中定义的常量值是否正确
func TestConstants(t *testing.T) {
	// 测试字节常量
	require.Equal(t, byte(45), dashByte, "dashByte应该是ASCII码45（字符'-'）")
	require.Equal(t, byte(44), commaByte, "commaByte应该是ASCII码44（字符','）")
	
	// 测试GREASE位掩码
	require.Equal(t, uint16(0x0F0F), greaseBitmask, "GREASE位掩码应该是0x0F0F")
	
	// 测试TLS扩展常量
	require.Equal(t, uint16(0), extensionServerName, "服务器名称扩展应该是0")
	require.Equal(t, uint16(5), extensionStatusRequest, "状态请求扩展应该是5")
	require.Equal(t, uint16(10), extensionSupportedCurves, "支持的曲线扩展应该是10")
	require.Equal(t, uint16(11), extensionSupportedPoints, "支持的点格式扩展应该是11")
	require.Equal(t, uint16(13), extensionSignatureAlgorithms, "签名算法扩展应该是13")
	require.Equal(t, uint16(16), extensionALPN, "ALPN扩展应该是16")
	require.Equal(t, uint16(23), extensionExtendedMasterSecret, "扩展主密钥扩展应该是23")
	require.Equal(t, uint16(35), extensionSessionTicket, "会话票据扩展应该是35")
	require.Equal(t, uint16(13172), extensionNextProtoNeg, "下一协议协商扩展应该是13172")
	require.Equal(t, uint16(0xff01), extensionRenegotiationInfo, "重新协商信息扩展应该是0xff01")
	require.Equal(t, uint16(0x0028), extensionExtendedRandom, "扩展随机数扩展应该是0x0028")
	require.Equal(t, uint16(18), extensionSCT, "SCT扩展应该是18")
	require.Equal(t, uint16(15), extensionHeartbeat, "心跳扩展应该是15")
	
	t.Log("所有常量验证通过")
}


// TestAppendExtension 测试扩展添加功能
// 验证appendExtension函数的GREASE过滤和扩展添加逻辑
func TestAppendExtension(t *testing.T) {
	t.Run("添加非GREASE扩展", func(t *testing.T) {
		tests := []struct {
			name      string
			initial   []byte
			extension uint16
			expected  string
		}{
			{
				name:      "添加服务器名称扩展",
				initial:   []byte{},
				extension: extensionServerName,
				expected:  "0-",
			},
			{
				name:      "添加ALPN扩展",
				initial:   []byte("0-"),
				extension: extensionALPN,
				expected:  "0-16-",
			},
			{
				name:      "添加会话票据扩展",
				initial:   []byte("0-16-"),
				extension: extensionSessionTicket,
				expected:  "0-16-35-",
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				result := appendExtension(tt.initial, tt.extension)
				require.Equal(t, tt.expected, string(result), "扩展添加结果应该匹配")
			})
		}
	})

	t.Run("过滤GREASE扩展", func(t *testing.T) {
		greaseValues := []uint16{0x0A0A, 0x1A1A, 0x2A2A, 0x3A3A, 0x4A4A, 0x5A5A}
		initial := []byte("0-")

		for _, greaseVal := range greaseValues {
			result := appendExtension(initial, greaseVal)
			require.Equal(t, string(initial), string(result),
				"GREASE值应该被过滤: 0x%04X", greaseVal)
		}
	})

	t.Run("混合GREASE和非GREASE扩展", func(t *testing.T) {
		initial := []byte{}

		// 添加非GREASE扩展
		result := appendExtension(initial, extensionServerName)
		require.Equal(t, "0-", string(result))

		// 添加GREASE扩展（应该被过滤）
		result = appendExtension(result, 0x0A0A)
		require.Equal(t, "0-", string(result))

		// 添加另一个非GREASE扩展
		result = appendExtension(result, extensionALPN)
		require.Equal(t, "0-16-", string(result))
	})
}


// TestGetJa3Hash 测试JA3客户端指纹生成功能
// 验证GetJa3Hash函数的正确性
func TestGetJa3Hash(t *testing.T) {
	t.Run("基本JA3指纹生成", func(t *testing.T) {
		// 创建测试用的ClientHello
		clientHello := &tls.ClientHello{
			Version: 0x0303, // TLS 1.2
			CipherSuites: []tls.CipherSuiteID{
				0x1301, // TLS_AES_128_GCM_SHA256
				0x1302, // TLS_AES_256_GCM_SHA384
				0x1303, // TLS_CHACHA20_POLY1305_SHA256
			},
			ServerName: "example.com", // 触发SNI扩展
			SupportedCurves: []tls.CurveID{
				tls.CurveP256,
				tls.CurveP384,
			},
			SupportedPoints: []tls.PointFormat{0, 1}, // uncompressed, ansiX962_compressed_prime
			ExtendedMasterSecret: true,     // 触发扩展主密钥扩展
		}

		// 生成JA3指纹
		ja3Hash := GetJa3Hash(clientHello)

		// 验证JA3哈希
		require.NotEmpty(t, ja3Hash, "JA3哈希不应该为空")
		require.Len(t, ja3Hash, 32, "JA3哈希应该是32个字符")

		// 验证哈希只包含十六进制字符
		for _, char := range ja3Hash {
			require.True(t,
				(char >= '0' && char <= '9') ||
				(char >= 'a' && char <= 'f'),
				"JA3哈希应该只包含小写十六进制字符: %c", char)
		}

		t.Logf("生成的JA3指纹: %s", ja3Hash)
	})

	t.Run("空ClientHello处理", func(t *testing.T) {
		// 创建最小的ClientHello
		clientHello := &tls.ClientHello{
			Version:      0x0303,
			CipherSuites: []tls.CipherSuiteID{},
		}

		// 生成JA3指纹
		ja3Hash := GetJa3Hash(clientHello)

		// 验证JA3哈希
		require.NotEmpty(t, ja3Hash, "即使是空ClientHello也应该生成JA3哈希")
		require.Len(t, ja3Hash, 32, "JA3哈希应该是32个字符")

		t.Logf("空ClientHello的JA3指纹: %s", ja3Hash)
	})

	t.Run("包含GREASE值的ClientHello", func(t *testing.T) {
		// 创建包含GREASE值的ClientHello
		clientHello := &tls.ClientHello{
			Version: 0x0303, // TLS 1.2
			CipherSuites: []tls.CipherSuiteID{
				0x0A0A, // GREASE值，应该被包含（密码套件不过滤GREASE）
				0x1301, // TLS_AES_128_GCM_SHA256
				0x1A1A, // GREASE值，应该被包含
				0x1302, // TLS_AES_256_GCM_SHA384
			},
			ServerName: "example.com",
			SupportedCurves: []tls.CurveID{
				tls.CurveID(0x0A0A), // GREASE曲线，应该被包含（曲线不过滤GREASE）
				tls.CurveP256,
				tls.CurveP384,
			},
			SupportedPoints: []tls.PointFormat{0, 1},
			UnknownExtensions: [][]byte{
				{0x0A, 0x0A, 0x00, 0x00}, // GREASE扩展，应该被过滤
				{0x00, 0x10, 0x00, 0x00}, // 非GREASE扩展，应该被包含
			},
		}

		// 生成JA3指纹
		ja3Hash := GetJa3Hash(clientHello)

		// 验证JA3哈希
		require.NotEmpty(t, ja3Hash, "包含GREASE值的ClientHello应该生成JA3哈希")
		require.Len(t, ja3Hash, 32, "JA3哈希应该是32个字符")

		t.Logf("包含GREASE值的JA3指纹: %s", ja3Hash)
	})

	t.Run("所有扩展类型测试", func(t *testing.T) {
		// 创建包含各种扩展的ClientHello
		clientHello := &tls.ClientHello{
			Version:               0x0303,
			CipherSuites:          []tls.CipherSuiteID{0x1301},
			ServerName:            "test.com",
			OcspStapling:          true,
			SupportedCurves:       []tls.CurveID{tls.CurveP256},
			SupportedPoints:       []tls.PointFormat{0},
			TicketSupported:       true,
			SignatureAndHashes:    []tls.SignatureAndHash{{Hash: 4, Signature: 1}},
			SecureRenegotiation:   true,
			AlpnProtocols:         []string{"h2", "http/1.1"},
			HeartbeatSupported:    true,
			ExtendedRandom:        []byte{1, 2, 3, 4},
			ExtendedMasterSecret:  true,
			SctEnabled:            true,
		}

		// 生成JA3指纹
		ja3Hash := GetJa3Hash(clientHello)

		// 验证JA3哈希
		require.NotEmpty(t, ja3Hash, "包含所有扩展的ClientHello应该生成JA3哈希")
		require.Len(t, ja3Hash, 32, "JA3哈希应该是32个字符")

		t.Logf("包含所有扩展的JA3指纹: %s", ja3Hash)
	})
}


// TestGetJa3sHash 测试JA3S服务器指纹生成功能
// 验证GetJa3sHash函数的正确性
func TestGetJa3sHash(t *testing.T) {
	t.Run("基本JA3S指纹生成", func(t *testing.T) {
		// 创建测试用的ServerHello
		serverHello := &tls.ServerHello{
			Version:     0x0303, // TLS 1.2
			CipherSuite: 0x1301, // TLS_AES_128_GCM_SHA256
			OcspStapling:         true,
			TicketSupported:      true,
			SecureRenegotiation:  true,
			ExtendedMasterSecret: true,
		}

		// 生成JA3S指纹
		ja3sHash := GetJa3sHash(serverHello)

		// 验证JA3S哈希
		require.NotEmpty(t, ja3sHash, "JA3S哈希不应该为空")
		require.Len(t, ja3sHash, 32, "JA3S哈希应该是32个字符")

		// 验证哈希只包含十六进制字符
		for _, char := range ja3sHash {
			require.True(t,
				(char >= '0' && char <= '9') ||
				(char >= 'a' && char <= 'f'),
				"JA3S哈希应该只包含小写十六进制字符: %c", char)
		}

		t.Logf("生成的JA3S指纹: %s", ja3sHash)
	})

	t.Run("空ServerHello处理", func(t *testing.T) {
		// 创建最小的ServerHello
		serverHello := &tls.ServerHello{
			Version:     0x0303,
			CipherSuite: 0x0000,
		}

		// 生成JA3S指纹
		ja3sHash := GetJa3sHash(serverHello)

		// 验证JA3S哈希
		require.NotEmpty(t, ja3sHash, "即使是空ServerHello也应该生成JA3S哈希")
		require.Len(t, ja3sHash, 32, "JA3S哈希应该是32个字符")

		t.Logf("空ServerHello的JA3S指纹: %s", ja3sHash)
	})

	t.Run("包含未知扩展的ServerHello", func(t *testing.T) {
		// 创建包含未知扩展的ServerHello
		serverHello := &tls.ServerHello{
			Version:     0x0303,
			CipherSuite: 0x1301,
			UnknownExtensions: [][]byte{
				{0x0A, 0x0A, 0x00, 0x00}, // GREASE扩展，应该被过滤
				{0x00, 0x10, 0x00, 0x00}, // 非GREASE扩展，应该被包含
				{0x12, 0x34, 0x00, 0x00}, // 自定义扩展，应该被包含
			},
		}

		// 生成JA3S指纹
		ja3sHash := GetJa3sHash(serverHello)

		// 验证JA3S哈希
		require.NotEmpty(t, ja3sHash, "包含未知扩展的ServerHello应该生成JA3S哈希")
		require.Len(t, ja3sHash, 32, "JA3S哈希应该是32个字符")

		t.Logf("包含未知扩展的JA3S指纹: %s", ja3sHash)
	})

	t.Run("所有扩展类型测试", func(t *testing.T) {
		// 创建包含各种扩展的ServerHello
		serverHello := &tls.ServerHello{
			Version:              0x0303,
			CipherSuite:          0x1301,
			OcspStapling:         true,
			TicketSupported:      true,
			SecureRenegotiation:  true,
			HeartbeatSupported:   true,
			ExtendedRandom:       []byte{1, 2, 3, 4},
			ExtendedMasterSecret: true,
		}

		// 生成JA3S指纹
		ja3sHash := GetJa3sHash(serverHello)

		// 验证JA3S哈希
		require.NotEmpty(t, ja3sHash, "包含所有扩展的ServerHello应该生成JA3S哈希")
		require.Len(t, ja3sHash, 32, "JA3S哈希应该是32个字符")

		t.Logf("包含所有扩展的JA3S指纹: %s", ja3sHash)
	})
}
