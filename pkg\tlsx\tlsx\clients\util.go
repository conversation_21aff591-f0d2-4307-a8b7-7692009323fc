// Author: chenjb
// Version: V1.0
// Date: 2025-06-24 11:45:05
// FilePath: /yaml_scan/pkg/tlsx/tlsx/clients/util.go
// Description:TLS客户端工具函数集合，提供各种辅助功能
package clients

import (
	"context"
	"crypto/x509"
	"encoding/hex"
	"errors"
	"math/big"
	"net"
	"strings"
	"time"
	errorutil "yaml_scan/utils/errors"
	iputil "yaml_scan/utils/ip"
	mapsutil "yaml_scan/utils/maps"
)

// Convertx509toResponse 将X.509证书转换为证书响应结构
// @param options *Options: TLS连接选项
// @param hostname string:  主机名
// @param cert *x509.Certificate: X.509证书
// @param showcert bool: 是否在响应中包含证书PEM编码
// @return *CertificateResponse *CertificateResponse: 包含证书信息的响应结构
func Convertx509toResponse(options *Options, hostname string, cert *x509.Certificate, showcert bool) *CertificateResponse {
	// 收集证书中的所有域名（CN和SAN）
	domainNames := []string{cert.Subject.CommonName}
	domainNames = append(domainNames, cert.DNSNames...)

	// 创建并填充证书响应结构
	response := &CertificateResponse{
		SubjectAN:    cert.DNSNames,                                        // 主题备用名称
		Emails:       cert.EmailAddresses,                                  // 证书中的电子邮件地址
		NotBefore:    cert.NotBefore,                                       // 证书有效期开始时间
		NotAfter:     cert.NotAfter,                                        // 证书有效期结束时间
		Expired:      IsExpired(cert.NotAfter),                             // 证书是否过期
		SelfSigned:   IsSelfSigned(cert.AuthorityKeyId, cert.SubjectKeyId), // 证书是否自签名
		MisMatched:   IsMisMatchedCert(hostname, domainNames),              // 证书域名是否与主机名不匹配
		Revoked:      IsTLSRevoked(options, cert),                          // 证书是否被吊销
		WildCardCert: IsWildCardCert(domainNames),                          // 是否为通配符证书
		IssuerCN:     cert.Issuer.CommonName,                               // 颁发者通用名称
		IssuerOrg:    cert.Issuer.Organization,                             // 颁发者组织
		SubjectCN:    cert.Subject.CommonName,                              // 主题通用名称
		SubjectOrg:   cert.Subject.Organization,                            // 主题组织
		FingerprintHash: CertificateResponseFingerprintHash{ // 证书指纹哈希
			MD5:    MD5Fingerprint(cert.Raw),    // MD5指纹
			SHA1:   SHA1Fingerprint(cert.Raw),   // SHA1指纹
			SHA256: SHA256Fingerprint(cert.Raw), // SHA256指纹
		},
		Serial: FormatToSerialNumber(cert.SerialNumber), // 格式化的证书序列号
	}
	// 解析颁发者和主题的可分辨名称
	response.IssuerDN = ParseASN1DNSequenceWithZpkixOrDefault(cert.RawIssuer, cert.Issuer.String())
	response.SubjectDN = ParseASN1DNSequenceWithZpkixOrDefault(cert.RawSubject, cert.Subject.String())
		// 如果需要，包含证书的PEM编码
	if showcert {
		response.Certificate = PemEncode(cert.Raw)
	}
	// 如果启用了DNS显示，提取唯一域名
	if options.DisplayDns {
		response.Domains = GetUniqueDomainsFromCert(response)
	}
	return response
}

// GetUniqueDomainsFromCert 从证书响应中提取唯一域名
// @param resp *CertificateResponse: 证书响应结构
// @return []string []string: 从证书中提取的唯一域名列表
func GetUniqueDomainsFromCert(resp *CertificateResponse) []string {
	// 使用map去重
	domains := map[string]struct{}{}

	// 处理主题备用名称
	for _, domain := range resp.SubjectAN {
		domains[trimWildcardPrefix(domain)] = struct{}{}
	}
	// 处理主题通用名称
	if resp.SubjectCN != "" {
		domains[trimWildcardPrefix(resp.SubjectCN)] = struct{}{}
	}
	return mapsutil.GetKeys(domains)
}

// trimWildcardPrefix 移除域名的通配符前缀
// @param hostname string: 可能包含通配符前缀的域名
// @return string string: 移除通配符前缀后的域名
func trimWildcardPrefix(hostname string) string {
	return strings.TrimPrefix(hostname, "*.")
}

// IntersectStringSlices 返回两个字符串切片的交集
// @param s1 []string:  第一个字符串切片
// @param s2 []string:  第二个字符串切片
// @return []string []string: 两个切片的交集
func IntersectStringSlices(s1 []string, s2 []string) []string {
	res := []string{}
	slicemap := map[string]struct{}{}
	var rangeslice []string

	// 创建较小切片的映射，并遍历较大的切片，以提高效率
	if len(s1) < len(s2) {
		for _, v := range s1 {
			slicemap[v] = struct{}{}
		}
		rangeslice = s2
	} else {
		for _, v := range s2 {
			slicemap[v] = struct{}{}
		}
		rangeslice = s1
	}
	// 找出交集
	for _, v := range rangeslice {
		if _, ok := slicemap[v]; ok {
			res = append(res, v)
		}
	}
	return res
}

// GetConn 从用户输入获取网络连接
// @param ctx context.Context: 上下文，用于控制连接超时等
// @param hostname string: 主机名
// @param ip string: IP地址
// @param port string: 端口号
// @param inputOpts *Options: 连接选项
// @return net.Conn net.Conn: 创建的网络连接
// @return error error: 可能的错误
func GetConn(ctx context.Context, hostname, ip, port string, inputOpts *Options) (net.Conn, error) {
	var address string
	// 确定使用IP还是主机名连接
	if iputil.IsIP(ip) && (inputOpts.ScanAllIPs || len(inputOpts.IPVersion) > 0) {
		address = net.JoinHostPort(ip, port)
	} else {
		address = net.JoinHostPort(hostname, port)
	}

	// 验证参数
	if (hostname == "" && ip == "") || port == "" {
		return nil, errorutil.New("client requires valid address got port=%v,hostname=%v,ip=%v", port, hostname, ip)
	}

	// 使用FastDialer建立TCP连接
	rawConn, err := inputOpts.Fastdialer.Dial(ctx, "tcp", address)
	if err != nil {
		return nil, errorutil.New("could not dial address").Wrap(err)
	}
	if rawConn == nil {
		return nil, errorutil.New("could not connect to %s", address)
	}
	// 设置默认超时
	if inputOpts.Timeout == 0 {
		inputOpts.Timeout = 5
	}
	// 设置读写超时
	err = rawConn.SetDeadline(time.Now().Add(time.Duration(inputOpts.Timeout) * time.Second))
	return rawConn, err
}

// FormatToSerialNumber 将大整数转换为冒号分隔的十六进制字符串
// @param serialNumber *big.Int: 证书序列号(大整数)
// @return string string: 格式化的序列号字符串
//	将证书序列号从大整数格式转换为冒号分隔的十六进制字符串格式。
//	例如: 17034156255497985825694118641198758684 -> 0C:D0:A8:BE:C6:32:CF:E6:45:EC:A0:A9:B0:84:FB:1C
func FormatToSerialNumber(serialNumber *big.Int) string {
	// 检查序列号是否为空或零
	if serialNumber == nil || serialNumber.Cmp(big.NewInt(0)) == 0 {
		return ""
	}
	// 获取序列号的字节表示
	b := serialNumber.Bytes()
	if len(b) == 0 {
		return ""
	}
	// 创建足够大的缓冲区进行十六进制编码
	buf := make([]byte, 0, 3*len(b))
	x := buf[1*len(b) : 3*len(b)]
	hex.Encode(x, b)
	// 每两个十六进制字符后添加一个冒号
	for i := 0; i < len(x); i += 2 {
		buf = append(buf, x[i], x[i+1], ':')
	}
	// 转换为大写并移除最后多余的冒号
	return strings.ToUpper(string(buf[:len(buf)-1]))
}

// IsClientCertRequiredError  检查错误是否由于服务器要求客户端证书而导致
// @param err error: 要检查的错误
// @return bool bool: 如果错误是由于服务器要求客户端证书而导致，则返回true；否则返回false
func IsClientCertRequiredError(err error) bool {
	nerr := &net.OpError{}

	// 检查是否为网络操作错误，且操作类型为"remote error"
	if errors.As(err, &nerr) && nerr.Op == "remote error" {
		rErr := nerr.Err.Error()
		rErr = strings.TrimPrefix(rErr, "tls: ")
		// 检查特定的TLS错误消息
		switch rErr {
		case "bad certificate": // 证书无效
			return true
		case "certificate required": // 需要证书
			return true
		}
	}
	return false
}
