//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-04 16:53:33
//FilePath: /yaml_scan/utils/conn/onetimepool.go
//Description:

package connpool

import (
	"context"
	"net"
	"sync"
)

// Dialer 接口定义了一个用于创建网络连接的方法。
type Dialer interface {
	Dial(ctx context.Context, network, address string) (net.Conn, error)
}

// OneTimePool 是一个连接池，旨在创建仅用于一次性使用的裸连接。
type OneTimePool struct {
	address         string             // 连接的目标地址
	idleConnections chan net.Conn      // 存储空闲连接的通道
	InFlightConns   *InFlightConns     // 正在进行的连接管理
	ctx             context.Context    // 上下文，用于控制连接的生命周期
	cancel          context.CancelFunc // 取消函数，用于取消上下文
	Dialer          Dialer             // 自定义拨号器，用于创建连接
	mx              sync.RWMutex       // 读写锁，用于保护共享资源
}

// NewOneTimePool: 创建一个新的 OneTimePool 实例。
//
//	@param ctx context.Context: 上下文，用于控制连接的生命周期。
//	@param address string:  目标地址。
//	@param poolSize int:  连接池的大小。
//	@return *OneTimePool *OneTimePool: 返回一个指向 OneTimePool 的指针。
//	@return error error: 返回可能发生的错误。
func NewOneTimePool(ctx context.Context, address string, poolSize int) (*OneTimePool, error) {
	// 创建一个通道用于存储空闲连接
	idleConnections := make(chan net.Conn, poolSize)
	// 创建 InFlightConns 实例
	inFlightConns, err := NewInFlightConns()
	if err != nil {
		return nil, err
	}
	pool := &OneTimePool{
		address:         address,
		idleConnections: idleConnections,
		InFlightConns:   inFlightConns,
	}
	// 如果上下文为 nil，使用Background上下文
	if ctx == nil {
		ctx = context.Background()
	}
	// 创建可取消的上下文
	pool.ctx, pool.cancel = context.WithCancel(ctx)
	return pool, nil
}

// Acquire: 从连接池中获取一个空闲连接。
//
//	@receiver p *OneTimePool:
//	@param c context.Context:
//	@return net.Conn net.Conn:
//	@return error error:
func (p *OneTimePool) Acquire(c context.Context) (net.Conn, error) {
	select {
	case <-p.ctx.Done():
		// 检查 OneTimePool 的上下文是否已完成
		return nil, p.ctx.Err()
	case <-c.Done():
		// 检查传入的上下文是否已完成
		return nil, c.Err()
	case conn := <-p.idleConnections:
		// 从空闲连接通道中获取连接
		p.InFlightConns.Remove(conn)
		return conn, nil
	}
}

// Run: 启动连接池，持续创建连接并将其放入空闲连接通道。
//
//	@receiver p *OneTimePool:
//	@return error error:
func (p *OneTimePool) Run() error {
	for {
		select {
		case <-p.ctx.Done():
			return p.ctx.Err()
		default:
			var (
				conn net.Conn
				err  error
			)
			// 获取读锁，检查是否有自定义拨号器
			p.mx.RLock()
			hasDialer := p.Dialer != nil
			p.mx.RUnlock()

			if hasDialer {
				// 如果有自定义拨号器，使用它创建连接
				p.mx.RLock()
				conn, err = p.Dialer.Dial(p.ctx, "tcp", p.address)
				p.mx.RUnlock()
			} else {
				// 如果有自定义拨号器，使用它创建连接
				conn, err = net.Dial("tcp", p.address)
			}
			if err == nil {
				// 将连接添加到正在进行的连接中
				p.InFlightConns.Add(conn)
				select {
				case <-p.ctx.Done():
					return p.ctx.Err()
				case p.idleConnections <- conn:
					// 将连接放入空闲连接通道
				}
			}
		}
	}
}

// Close: 关闭连接池，释放资源。
//
//	@receiver p *OneTimePool:
//	@return error error:
func (p *OneTimePool) Close() error {
	// 调用取消函数以停止上下文
	p.cancel()

	// 清空拨号器的引用
	p.mx.Lock()
	p.Dialer = nil
	p.mx.Unlock()

	return p.InFlightConns.Close()
}
