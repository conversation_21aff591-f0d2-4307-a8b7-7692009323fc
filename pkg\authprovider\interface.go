// Author: chenjb
// Version: V1.0
// Date: 2025-06-13 15:12:20
// FilePath: /yaml_scan/pkg/authprovider/interface.go
// Description: 认证提供者的接口和基础结构，包含了认证策略的查询和获取功能
package authprovider

import (
	"fmt"
	"net/url"
	"yaml_scan/pkg/authprovider/authx"

	urlutil "yaml_scan/utils/url"
)

var (

	// ErrNoSecrets 表示在给定的提供者中没有找到任何密钥
	ErrNoSecrets = fmt.Errorf("no secrets in given provider")
)

var (
		// 确保FileAuthProvider实现了AuthProvider接口
	_ AuthProvider = &FileAuthProvider{}
)


// AuthProvider 是认证提供者的接口
// 它实现了一种数据结构，适合快速查找和检索认证策略
type AuthProvider interface {
	// LookupAddr 根据给定的域名/地址查找并返回适当的认证策略
	// 接受的输入格式如 scanme.sh 或 scanme.sh:443
	// 参数:
	//   - addr: 要查找的域名或地址字符串
	// 返回值:
	//   - []authx.AuthStrategy: 匹配的认证策略列表
	LookupAddr(string) []authx.AuthStrategy
	
	// LookupURL 根据给定的URL查找并返回适当的认证策略
	// 参数:
	//   - u: 有效的url.URL结构体指针
	// 返回值:
	//   - []authx.AuthStrategy: 匹配的认证策略列表
	LookupURL(*url.URL) []authx.AuthStrategy
	
	// LookupURLX 根据给定的URL查找并返回适当的认证策略
	// 接受自定义的urlutil.URL结构体并返回认证策略
	// 参数:
	//   - u: 自定义的urlutil.URL结构体指针
	// 返回值:
	//   - []authx.AuthStrategy: 匹配的认证策略列表
	LookupURLX(*urlutil.URL) []authx.AuthStrategy
	
	// GetTemplatePaths 返回认证提供者的模板路径
	// 这些路径将用于动态获取密钥
	// 返回值:
	//   - []string: 模板路径列表
	GetTemplatePaths() []string
	
	// PreFetchSecrets 预先获取认证提供者中的密钥
	// 代替懒加载方式获取密钥
	// 返回值:
	//   - error: 如果预取过程中发生错误则返回错误，否则返回nil
	PreFetchSecrets() error
}

// AuthProviderOptions  包含认证提供者的配置选项
type AuthProviderOptions struct {
	// SecretsFiles 是基于文件的认证提供者选项，包含密钥文件的路径列表
	SecretsFiles []string
	// LazyFetchSecret 是用于懒加载动态密钥的回调函数
	LazyFetchSecret authx.LazyFetchSecret
}

// NewAuthProvider 据给定的选项创建一个新的认证提供者
// @param options *AuthProviderOptions: 包含认证提供者配置的选项结构体指针
// @return AuthProvider AuthProvider: 创建的认证提供者接口实现
// @return error error: 可能的错误
func NewAuthProvider(options *AuthProviderOptions) (AuthProvider, error) {
	var providers []AuthProvider
	// 遍历所有密钥文件，为每个文件创建一个文件认证提供者
	for _, file := range options.SecretsFiles {
		provider, err := NewFileAuthProvider(file, options.LazyFetchSecret)
		if err != nil {
			return nil, err
		}
		providers = append(providers, provider)
	}
	// 将所有提供者合并为一个多重认证提供者
	return NewMultiAuthProvider(providers...), nil
}

