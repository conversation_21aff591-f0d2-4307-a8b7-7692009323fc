// Author: chenjb
// Version: V1.0
// Date: 2025-06-24 14:59:46
// FilePath: /yaml_scan/pkg/tlsx/tlsx/ztls/util.go
// Description:
package ztls

import (
	"yaml_scan/pkg/tlsx/tlsx/clients"

	"github.com/zmap/zcrypto/tls"
	"github.com/zmap/zcrypto/x509"
	errorutil "yaml_scan/utils/errors"
)

var (
	// AllCiphers 包含所有ZMap TLS库支持的密码套件数值标识符
	AllCiphers []uint16
	// AllCiphersNames 包含所有ZMap TLS库支持的密码套件名称
	AllCiphersNames []string
	// SupportedTlsVersions 包含所有ZMap TLS库支持的TLS版本
	SupportedTlsVersions []string
)

func init() {
	// 从ZMap密码套件映射表中提取所有密码套件的名称和数值标识符
	for name, cipher := range ztlsCiphers {
		AllCiphersNames = append(AllCiphersNames, name)
		AllCiphers = append(AllCiphers, cipher)
	}
	// 从版本映射表中提取所有支持的TLS版本名称
	for name := range versionStringToTLSVersion {
		SupportedTlsVersions = append(SupportedTlsVersions, name)
	}
}

// toZTLSCiphers 将密码套件名称列表转换为ZMap TLS库的数值标识符列表
// 用于将用户指定的密码套件名称转换为ZMap TLS配置中使用的数值
// @param items []string: 密码套件名称列表，支持ZMap库的扩展密码套件
// @return []uint16 []uint16: 对应的ZMap密码套件数值标识符列表
// @return error error:
func toZTLSCiphers(items []string) ([]uint16, error) {
	var convertedCiphers []uint16
	for _, item := range items {
		zcipher, ok := ztlsCiphers[item]
		if !ok {
			return nil, errorutil.NewWithTag("ztls", "cipher suite %v not supported", item)
		}
		convertedCiphers = append(convertedCiphers, zcipher)
	}
	return convertedCiphers, nil
}

// ConvertCertificateToResponse 使用ZMap zcrypto x509库将证书转换为响应结构
// 提供比标准库更详细的证书解析和状态检查功能
// @param options *clients.Options: TLS连接配置选项，包含撤销检查等设置
// @param hostname string: 目标主机名，用于证书域名匹配验证
// @param cert *x509.Certificate: ZMap X.509证书对象，包含更详细的证书信息
// @return *clients.CertificateResponse *clients.CertificateResponse: 含证书详细信息和状态检查结果的响应结构
func ConvertCertificateToResponse(options *clients.Options, hostname string, cert *x509.Certificate) *clients.CertificateResponse {
	if cert == nil {
		return nil
	}
	// 创建并填充证书响应结构
	response := &clients.CertificateResponse{
		// === 证书扩展信息 ===
		SubjectAN: cert.DNSNames,       // 主题备用名称列表（SAN）
		Emails:    cert.EmailAddresses, // 证书中包含的电子邮件地址列表

		// === 证书有效期信息 ===
		NotBefore: cert.NotBefore, // 证书生效时间
		NotAfter:  cert.NotAfter,  // 证书过期时间

		// === 证书状态检查 ===
		Expired:      clients.IsExpired(cert.NotAfter),                                                   // 检查证书是否已过期
		SelfSigned:   clients.IsSelfSigned(cert.AuthorityKeyId, cert.SubjectKeyId),                       // 检查是否为自签名证书
		MisMatched:   clients.IsMisMatchedCert(hostname, append(cert.DNSNames, cert.Subject.CommonName)), // 检查证书域名是否与目标主机匹配
		Revoked:      clients.IsZTLSRevoked(options, cert),                                               // 检查证书是否已被撤销（使用ZMap版本）
		WildCardCert: clients.IsWildCardCert(append(cert.DNSNames, cert.Subject.CommonName)),             // 检查是否为通配符证书

		// === 颁发者信息 ===
		IssuerDN:  cert.Issuer.String(),     // 颁发者可分辨名称（ZMap库提供更详细信息）
		IssuerCN:  cert.Issuer.CommonName,   // 颁发者通用名称
		IssuerOrg: cert.Issuer.Organization, // 颁发者组织名称列表

		// === 主题信息 ===
		SubjectDN:  cert.Subject.String(),     // 主题可分辨名称（ZMap库提供更详细信息）
		SubjectCN:  cert.Subject.CommonName,   // 主题通用名称
		SubjectOrg: cert.Subject.Organization, // 主题组织名称列表

		// === 证书指纹哈希 ===
		FingerprintHash: clients.CertificateResponseFingerprintHash{
			MD5:    clients.MD5Fingerprint(cert.Raw),    // MD5指纹
			SHA1:   clients.SHA1Fingerprint(cert.Raw),   // SHA1指纹
			SHA256: clients.SHA256Fingerprint(cert.Raw), // SHA256指纹
		},

		// === 证书标识 ===
		Serial: clients.FormatToSerialNumber(cert.SerialNumber), // 格式化的证书序列号
	}

	// 如果启用了DNS域名显示，提取并去重所有域名
	if options.DisplayDns {
		response.Domains = clients.GetUniqueDomainsFromCert(response)
	}

	// 如果请求包含完整证书内容，添加PEM格式的证书
	if options.Cert {
		response.Certificate = clients.PemEncode(cert.Raw)
	}
	return response
}

// ParseSimpleTLSCertificate 使用ZMap zcrypto x509库解析简化的TLS证书
// 将ZMap的SimpleCertificate转换为完整的X.509证书对象
// @param cert tls.SimpleCertificate: ZMap TLS库的SimpleCertificate对象，包含原始证书数据
// @return *x509.Certificate *x509.Certificate: 解析后的ZMap X.509证书对象，失败时为nil
func ParseSimpleTLSCertificate(cert tls.SimpleCertificate) *x509.Certificate {
	parsed, _ := x509.ParseCertificate(cert.Raw)
	return parsed
}

var ztlsCiphers = map[string]uint16{
	"TLS_NULL_WITH_NULL_NULL":                           tls.TLS_NULL_WITH_NULL_NULL,
	"TLS_RSA_WITH_NULL_MD5":                             tls.TLS_RSA_WITH_NULL_MD5,
	"TLS_RSA_WITH_NULL_SHA":                             tls.TLS_RSA_WITH_NULL_SHA,
	"TLS_RSA_EXPORT_WITH_RC4_40_MD5":                    tls.TLS_RSA_EXPORT_WITH_RC4_40_MD5,
	"TLS_RSA_WITH_RC4_128_MD5":                          tls.TLS_RSA_WITH_RC4_128_MD5,
	"TLS_RSA_WITH_RC4_128_SHA":                          tls.TLS_RSA_WITH_RC4_128_SHA,
	"TLS_RSA_EXPORT_WITH_RC2_CBC_40_MD5":                tls.TLS_RSA_EXPORT_WITH_RC2_CBC_40_MD5,
	"TLS_RSA_WITH_IDEA_CBC_SHA":                         tls.TLS_RSA_WITH_IDEA_CBC_SHA,
	"TLS_RSA_EXPORT_WITH_DES40_CBC_SHA":                 tls.TLS_RSA_EXPORT_WITH_DES40_CBC_SHA,
	"TLS_RSA_WITH_DES_CBC_SHA":                          tls.TLS_RSA_WITH_DES_CBC_SHA,
	"TLS_RSA_WITH_3DES_EDE_CBC_SHA":                     tls.TLS_RSA_WITH_3DES_EDE_CBC_SHA,
	"TLS_DH_DSS_EXPORT_WITH_DES40_CBC_SHA":              tls.TLS_DH_DSS_EXPORT_WITH_DES40_CBC_SHA,
	"TLS_DH_DSS_WITH_DES_CBC_SHA":                       tls.TLS_DH_DSS_WITH_DES_CBC_SHA,
	"TLS_DH_DSS_WITH_3DES_EDE_CBC_SHA":                  tls.TLS_DH_DSS_WITH_3DES_EDE_CBC_SHA,
	"TLS_DH_RSA_EXPORT_WITH_DES40_CBC_SHA":              tls.TLS_DH_RSA_EXPORT_WITH_DES40_CBC_SHA,
	"TLS_DH_RSA_WITH_DES_CBC_SHA":                       tls.TLS_DH_RSA_WITH_DES_CBC_SHA,
	"TLS_DH_RSA_WITH_3DES_EDE_CBC_SHA":                  tls.TLS_DH_RSA_WITH_3DES_EDE_CBC_SHA,
	"TLS_DHE_DSS_EXPORT_WITH_DES40_CBC_SHA":             tls.TLS_DHE_DSS_EXPORT_WITH_DES40_CBC_SHA,
	"TLS_DHE_DSS_WITH_DES_CBC_SHA":                      tls.TLS_DHE_DSS_WITH_DES_CBC_SHA,
	"TLS_DHE_DSS_WITH_3DES_EDE_CBC_SHA":                 tls.TLS_DHE_DSS_WITH_3DES_EDE_CBC_SHA,
	"TLS_DHE_RSA_EXPORT_WITH_DES40_CBC_SHA":             tls.TLS_DHE_RSA_EXPORT_WITH_DES40_CBC_SHA,
	"TLS_DHE_RSA_WITH_DES_CBC_SHA":                      tls.TLS_DHE_RSA_WITH_DES_CBC_SHA,
	"TLS_DHE_RSA_WITH_3DES_EDE_CBC_SHA":                 tls.TLS_DHE_RSA_WITH_3DES_EDE_CBC_SHA,
	"TLS_DH_ANON_EXPORT_WITH_RC4_40_MD5":                tls.TLS_DH_ANON_EXPORT_WITH_RC4_40_MD5,
	"TLS_DH_ANON_WITH_RC4_128_MD5":                      tls.TLS_DH_ANON_WITH_RC4_128_MD5,
	"TLS_DH_ANON_EXPORT_WITH_DES40_CBC_SHA":             tls.TLS_DH_ANON_EXPORT_WITH_DES40_CBC_SHA,
	"TLS_DH_ANON_WITH_DES_CBC_SHA":                      tls.TLS_DH_ANON_WITH_DES_CBC_SHA,
	"TLS_DH_ANON_WITH_3DES_EDE_CBC_SHA":                 tls.TLS_DH_ANON_WITH_3DES_EDE_CBC_SHA,
	"SSL_FORTEZZA_KEA_WITH_NULL_SHA":                    tls.SSL_FORTEZZA_KEA_WITH_NULL_SHA,
	"SSL_FORTEZZA_KEA_WITH_FORTEZZA_CBC_SHA":            tls.SSL_FORTEZZA_KEA_WITH_FORTEZZA_CBC_SHA,
	"TLS_KRB5_WITH_DES_CBC_SHA":                         tls.TLS_KRB5_WITH_DES_CBC_SHA,
	"TLS_KRB5_WITH_3DES_EDE_CBC_SHA":                    tls.TLS_KRB5_WITH_3DES_EDE_CBC_SHA,
	"TLS_KRB5_WITH_RC4_128_SHA":                         tls.TLS_KRB5_WITH_RC4_128_SHA,
	"TLS_KRB5_WITH_IDEA_CBC_SHA":                        tls.TLS_KRB5_WITH_IDEA_CBC_SHA,
	"TLS_KRB5_WITH_DES_CBC_MD5":                         tls.TLS_KRB5_WITH_DES_CBC_MD5,
	"TLS_KRB5_WITH_3DES_EDE_CBC_MD5":                    tls.TLS_KRB5_WITH_3DES_EDE_CBC_MD5,
	"TLS_KRB5_WITH_RC4_128_MD5":                         tls.TLS_KRB5_WITH_RC4_128_MD5,
	"TLS_KRB5_WITH_IDEA_CBC_MD5":                        tls.TLS_KRB5_WITH_IDEA_CBC_MD5,
	"TLS_KRB5_EXPORT_WITH_DES_CBC_40_SHA":               tls.TLS_KRB5_EXPORT_WITH_DES_CBC_40_SHA,
	"TLS_KRB5_EXPORT_WITH_RC2_CBC_40_SHA":               tls.TLS_KRB5_EXPORT_WITH_RC2_CBC_40_SHA,
	"TLS_KRB5_EXPORT_WITH_RC4_40_SHA":                   tls.TLS_KRB5_EXPORT_WITH_RC4_40_SHA,
	"TLS_KRB5_EXPORT_WITH_DES_CBC_40_MD5":               tls.TLS_KRB5_EXPORT_WITH_DES_CBC_40_MD5,
	"TLS_KRB5_EXPORT_WITH_RC2_CBC_40_MD5":               tls.TLS_KRB5_EXPORT_WITH_RC2_CBC_40_MD5,
	"TLS_KRB5_EXPORT_WITH_RC4_40_MD5":                   tls.TLS_KRB5_EXPORT_WITH_RC4_40_MD5,
	"TLS_PSK_WITH_NULL_SHA":                             tls.TLS_PSK_WITH_NULL_SHA,
	"TLS_DHE_PSK_WITH_NULL_SHA":                         tls.TLS_DHE_PSK_WITH_NULL_SHA,
	"TLS_RSA_PSK_WITH_NULL_SHA":                         tls.TLS_RSA_PSK_WITH_NULL_SHA,
	"TLS_RSA_WITH_AES_128_CBC_SHA":                      tls.TLS_RSA_WITH_AES_128_CBC_SHA,
	"TLS_DH_DSS_WITH_AES_128_CBC_SHA":                   tls.TLS_DH_DSS_WITH_AES_128_CBC_SHA,
	"TLS_DH_RSA_WITH_AES_128_CBC_SHA":                   tls.TLS_DH_RSA_WITH_AES_128_CBC_SHA,
	"TLS_DHE_DSS_WITH_AES_128_CBC_SHA":                  tls.TLS_DHE_DSS_WITH_AES_128_CBC_SHA,
	"TLS_DHE_RSA_WITH_AES_128_CBC_SHA":                  tls.TLS_DHE_RSA_WITH_AES_128_CBC_SHA,
	"TLS_DH_ANON_WITH_AES_128_CBC_SHA":                  tls.TLS_DH_ANON_WITH_AES_128_CBC_SHA,
	"TLS_RSA_WITH_AES_256_CBC_SHA":                      tls.TLS_RSA_WITH_AES_256_CBC_SHA,
	"TLS_DH_DSS_WITH_AES_256_CBC_SHA":                   tls.TLS_DH_DSS_WITH_AES_256_CBC_SHA,
	"TLS_DH_RSA_WITH_AES_256_CBC_SHA":                   tls.TLS_DH_RSA_WITH_AES_256_CBC_SHA,
	"TLS_DHE_DSS_WITH_AES_256_CBC_SHA":                  tls.TLS_DHE_DSS_WITH_AES_256_CBC_SHA,
	"TLS_DHE_RSA_WITH_AES_256_CBC_SHA":                  tls.TLS_DHE_RSA_WITH_AES_256_CBC_SHA,
	"TLS_DH_ANON_WITH_AES_256_CBC_SHA":                  tls.TLS_DH_ANON_WITH_AES_256_CBC_SHA,
	"TLS_RSA_WITH_NULL_SHA256":                          tls.TLS_RSA_WITH_NULL_SHA256,
	"TLS_RSA_WITH_AES_128_CBC_SHA256":                   tls.TLS_RSA_WITH_AES_128_CBC_SHA256,
	"TLS_RSA_WITH_AES_256_CBC_SHA256":                   tls.TLS_RSA_WITH_AES_256_CBC_SHA256,
	"TLS_DH_DSS_WITH_AES_128_CBC_SHA256":                tls.TLS_DH_DSS_WITH_AES_128_CBC_SHA256,
	"TLS_DH_RSA_WITH_AES_128_CBC_SHA256":                tls.TLS_DH_RSA_WITH_AES_128_CBC_SHA256,
	"TLS_DHE_DSS_WITH_AES_128_CBC_SHA256":               tls.TLS_DHE_DSS_WITH_AES_128_CBC_SHA256,
	"TLS_RSA_WITH_CAMELLIA_128_CBC_SHA":                 tls.TLS_RSA_WITH_CAMELLIA_128_CBC_SHA,
	"TLS_DH_DSS_WITH_CAMELLIA_128_CBC_SHA":              tls.TLS_DH_DSS_WITH_CAMELLIA_128_CBC_SHA,
	"TLS_DH_RSA_WITH_CAMELLIA_128_CBC_SHA":              tls.TLS_DH_RSA_WITH_CAMELLIA_128_CBC_SHA,
	"TLS_DHE_DSS_WITH_CAMELLIA_128_CBC_SHA":             tls.TLS_DHE_DSS_WITH_CAMELLIA_128_CBC_SHA,
	"TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA":             tls.TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA,
	"TLS_DH_ANON_WITH_CAMELLIA_128_CBC_SHA":             tls.TLS_DH_ANON_WITH_CAMELLIA_128_CBC_SHA,
	"TLS_RSA_EXPORT1024_WITH_RC4_56_MD5":                tls.TLS_RSA_EXPORT1024_WITH_RC4_56_MD5,
	"TLS_RSA_EXPORT1024_WITH_RC2_CBC_56_MD5":            tls.TLS_RSA_EXPORT1024_WITH_RC2_CBC_56_MD5,
	"TLS_RSA_EXPORT1024_WITH_DES_CBC_SHA":               tls.TLS_RSA_EXPORT1024_WITH_DES_CBC_SHA,
	"TLS_DHE_DSS_EXPORT1024_WITH_DES_CBC_SHA":           tls.TLS_DHE_DSS_EXPORT1024_WITH_DES_CBC_SHA,
	"TLS_RSA_EXPORT1024_WITH_RC4_56_SHA":                tls.TLS_RSA_EXPORT1024_WITH_RC4_56_SHA,
	"TLS_DHE_DSS_EXPORT1024_WITH_RC4_56_SHA":            tls.TLS_DHE_DSS_EXPORT1024_WITH_RC4_56_SHA,
	"TLS_DHE_DSS_WITH_RC4_128_SHA":                      tls.TLS_DHE_DSS_WITH_RC4_128_SHA,
	"TLS_DHE_RSA_WITH_AES_128_CBC_SHA256":               tls.TLS_DHE_RSA_WITH_AES_128_CBC_SHA256,
	"TLS_DH_DSS_WITH_AES_256_CBC_SHA256":                tls.TLS_DH_DSS_WITH_AES_256_CBC_SHA256,
	"TLS_DH_RSA_WITH_AES_256_CBC_SHA256":                tls.TLS_DH_RSA_WITH_AES_256_CBC_SHA256,
	"TLS_DHE_DSS_WITH_AES_256_CBC_SHA256":               tls.TLS_DHE_DSS_WITH_AES_256_CBC_SHA256,
	"TLS_DHE_RSA_WITH_AES_256_CBC_SHA256":               tls.TLS_DHE_RSA_WITH_AES_256_CBC_SHA256,
	"TLS_DH_ANON_WITH_AES_128_CBC_SHA256":               tls.TLS_DH_ANON_WITH_AES_128_CBC_SHA256,
	"TLS_DH_ANON_WITH_AES_256_CBC_SHA256":               tls.TLS_DH_ANON_WITH_AES_256_CBC_SHA256,
	"TLS_GOSTR341094_WITH_28147_CNT_IMIT":               tls.TLS_GOSTR341094_WITH_28147_CNT_IMIT,
	"TLS_GOSTR341001_WITH_28147_CNT_IMIT":               tls.TLS_GOSTR341001_WITH_28147_CNT_IMIT,
	"TLS_GOSTR341094_WITH_NULL_GOSTR3411":               tls.TLS_GOSTR341094_WITH_NULL_GOSTR3411,
	"TLS_GOSTR341001_WITH_NULL_GOSTR3411":               tls.TLS_GOSTR341001_WITH_NULL_GOSTR3411,
	"TLS_RSA_WITH_CAMELLIA_256_CBC_SHA":                 tls.TLS_RSA_WITH_CAMELLIA_256_CBC_SHA,
	"TLS_DH_DSS_WITH_CAMELLIA_256_CBC_SHA":              tls.TLS_DH_DSS_WITH_CAMELLIA_256_CBC_SHA,
	"TLS_DH_RSA_WITH_CAMELLIA_256_CBC_SHA":              tls.TLS_DH_RSA_WITH_CAMELLIA_256_CBC_SHA,
	"TLS_DHE_DSS_WITH_CAMELLIA_256_CBC_SHA":             tls.TLS_DHE_DSS_WITH_CAMELLIA_256_CBC_SHA,
	"TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA":             tls.TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA,
	"TLS_DH_ANON_WITH_CAMELLIA_256_CBC_SHA":             tls.TLS_DH_ANON_WITH_CAMELLIA_256_CBC_SHA,
	"TLS_PSK_WITH_RC4_128_SHA":                          tls.TLS_PSK_WITH_RC4_128_SHA,
	"TLS_PSK_WITH_3DES_EDE_CBC_SHA":                     tls.TLS_PSK_WITH_3DES_EDE_CBC_SHA,
	"TLS_PSK_WITH_AES_128_CBC_SHA":                      tls.TLS_PSK_WITH_AES_128_CBC_SHA,
	"TLS_PSK_WITH_AES_256_CBC_SHA":                      tls.TLS_PSK_WITH_AES_256_CBC_SHA,
	"TLS_DHE_PSK_WITH_RC4_128_SHA":                      tls.TLS_DHE_PSK_WITH_RC4_128_SHA,
	"TLS_DHE_PSK_WITH_3DES_EDE_CBC_SHA":                 tls.TLS_DHE_PSK_WITH_3DES_EDE_CBC_SHA,
	"TLS_DHE_PSK_WITH_AES_128_CBC_SHA":                  tls.TLS_DHE_PSK_WITH_AES_128_CBC_SHA,
	"TLS_DHE_PSK_WITH_AES_256_CBC_SHA":                  tls.TLS_DHE_PSK_WITH_AES_256_CBC_SHA,
	"TLS_RSA_PSK_WITH_RC4_128_SHA":                      tls.TLS_RSA_PSK_WITH_RC4_128_SHA,
	"TLS_RSA_PSK_WITH_3DES_EDE_CBC_SHA":                 tls.TLS_RSA_PSK_WITH_3DES_EDE_CBC_SHA,
	"TLS_RSA_PSK_WITH_AES_128_CBC_SHA":                  tls.TLS_RSA_PSK_WITH_AES_128_CBC_SHA,
	"TLS_RSA_PSK_WITH_AES_256_CBC_SHA":                  tls.TLS_RSA_PSK_WITH_AES_256_CBC_SHA,
	"TLS_RSA_WITH_SEED_CBC_SHA":                         tls.TLS_RSA_WITH_SEED_CBC_SHA,
	"TLS_DH_DSS_WITH_SEED_CBC_SHA":                      tls.TLS_DH_DSS_WITH_SEED_CBC_SHA,
	"TLS_DH_RSA_WITH_SEED_CBC_SHA":                      tls.TLS_DH_RSA_WITH_SEED_CBC_SHA,
	"TLS_DHE_DSS_WITH_SEED_CBC_SHA":                     tls.TLS_DHE_DSS_WITH_SEED_CBC_SHA,
	"TLS_DHE_RSA_WITH_SEED_CBC_SHA":                     tls.TLS_DHE_RSA_WITH_SEED_CBC_SHA,
	"TLS_DH_ANON_WITH_SEED_CBC_SHA":                     tls.TLS_DH_ANON_WITH_SEED_CBC_SHA,
	"TLS_RSA_WITH_AES_128_GCM_SHA256":                   tls.TLS_RSA_WITH_AES_128_GCM_SHA256,
	"TLS_RSA_WITH_AES_256_GCM_SHA384":                   tls.TLS_RSA_WITH_AES_256_GCM_SHA384,
	"TLS_DHE_RSA_WITH_AES_128_GCM_SHA256":               tls.TLS_DHE_RSA_WITH_AES_128_GCM_SHA256,
	"TLS_DHE_RSA_WITH_AES_256_GCM_SHA384":               tls.TLS_DHE_RSA_WITH_AES_256_GCM_SHA384,
	"TLS_DH_RSA_WITH_AES_128_GCM_SHA256":                tls.TLS_DH_RSA_WITH_AES_128_GCM_SHA256,
	"TLS_DH_RSA_WITH_AES_256_GCM_SHA384":                tls.TLS_DH_RSA_WITH_AES_256_GCM_SHA384,
	"TLS_DHE_DSS_WITH_AES_128_GCM_SHA256":               tls.TLS_DHE_DSS_WITH_AES_128_GCM_SHA256,
	"TLS_DHE_DSS_WITH_AES_256_GCM_SHA384":               tls.TLS_DHE_DSS_WITH_AES_256_GCM_SHA384,
	"TLS_DH_DSS_WITH_AES_128_GCM_SHA256":                tls.TLS_DH_DSS_WITH_AES_128_GCM_SHA256,
	"TLS_DH_DSS_WITH_AES_256_GCM_SHA384":                tls.TLS_DH_DSS_WITH_AES_256_GCM_SHA384,
	"TLS_DH_ANON_WITH_AES_128_GCM_SHA256":               tls.TLS_DH_ANON_WITH_AES_128_GCM_SHA256,
	"TLS_DH_ANON_WITH_AES_256_GCM_SHA384":               tls.TLS_DH_ANON_WITH_AES_256_GCM_SHA384,
	"TLS_PSK_WITH_AES_128_GCM_SHA256":                   tls.TLS_PSK_WITH_AES_128_GCM_SHA256,
	"TLS_PSK_WITH_AES_256_GCM_SHA384":                   tls.TLS_PSK_WITH_AES_256_GCM_SHA384,
	"TLS_DHE_PSK_WITH_AES_128_GCM_SHA256":               tls.TLS_DHE_PSK_WITH_AES_128_GCM_SHA256,
	"TLS_DHE_PSK_WITH_AES_256_GCM_SHA384":               tls.TLS_DHE_PSK_WITH_AES_256_GCM_SHA384,
	"TLS_RSA_PSK_WITH_AES_128_GCM_SHA256":               tls.TLS_RSA_PSK_WITH_AES_128_GCM_SHA256,
	"TLS_RSA_PSK_WITH_AES_256_GCM_SHA384":               tls.TLS_RSA_PSK_WITH_AES_256_GCM_SHA384,
	"TLS_PSK_WITH_AES_128_CBC_SHA256":                   tls.TLS_PSK_WITH_AES_128_CBC_SHA256,
	"TLS_PSK_WITH_AES_256_CBC_SHA384":                   tls.TLS_PSK_WITH_AES_256_CBC_SHA384,
	"TLS_PSK_WITH_NULL_SHA256":                          tls.TLS_PSK_WITH_NULL_SHA256,
	"TLS_PSK_WITH_NULL_SHA384":                          tls.TLS_PSK_WITH_NULL_SHA384,
	"TLS_DHE_PSK_WITH_AES_128_CBC_SHA256":               tls.TLS_DHE_PSK_WITH_AES_128_CBC_SHA256,
	"TLS_DHE_PSK_WITH_AES_256_CBC_SHA384":               tls.TLS_DHE_PSK_WITH_AES_256_CBC_SHA384,
	"TLS_DHE_PSK_WITH_NULL_SHA256":                      tls.TLS_DHE_PSK_WITH_NULL_SHA256,
	"TLS_DHE_PSK_WITH_NULL_SHA384":                      tls.TLS_DHE_PSK_WITH_NULL_SHA384,
	"TLS_RSA_PSK_WITH_AES_128_CBC_SHA256":               tls.TLS_RSA_PSK_WITH_AES_128_CBC_SHA256,
	"TLS_RSA_PSK_WITH_AES_256_CBC_SHA384":               tls.TLS_RSA_PSK_WITH_AES_256_CBC_SHA384,
	"TLS_RSA_PSK_WITH_NULL_SHA256":                      tls.TLS_RSA_PSK_WITH_NULL_SHA256,
	"TLS_RSA_PSK_WITH_NULL_SHA384":                      tls.TLS_RSA_PSK_WITH_NULL_SHA384,
	"TLS_RSA_WITH_CAMELLIA_128_CBC_SHA256":              tls.TLS_RSA_WITH_CAMELLIA_128_CBC_SHA256,
	"TLS_DH_DSS_WITH_CAMELLIA_128_CBC_SHA256":           tls.TLS_DH_DSS_WITH_CAMELLIA_128_CBC_SHA256,
	"TLS_DH_RSA_WITH_CAMELLIA_128_CBC_SHA256":           tls.TLS_DH_RSA_WITH_CAMELLIA_128_CBC_SHA256,
	"TLS_DHE_DSS_WITH_CAMELLIA_128_CBC_SHA256":          tls.TLS_DHE_DSS_WITH_CAMELLIA_128_CBC_SHA256,
	"TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA256":          tls.TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA256,
	"TLS_DH_ANON_WITH_CAMELLIA_128_CBC_SHA256":          tls.TLS_DH_ANON_WITH_CAMELLIA_128_CBC_SHA256,
	"TLS_RSA_WITH_CAMELLIA_256_CBC_SHA256":              tls.TLS_RSA_WITH_CAMELLIA_256_CBC_SHA256,
	"TLS_DH_DSS_WITH_CAMELLIA_256_CBC_SHA256":           tls.TLS_DH_DSS_WITH_CAMELLIA_256_CBC_SHA256,
	"TLS_DH_RSA_WITH_CAMELLIA_256_CBC_SHA256":           tls.TLS_DH_RSA_WITH_CAMELLIA_256_CBC_SHA256,
	"TLS_DHE_DSS_WITH_CAMELLIA_256_CBC_SHA256":          tls.TLS_DHE_DSS_WITH_CAMELLIA_256_CBC_SHA256,
	"TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA256":          tls.TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA256,
	"TLS_DH_ANON_WITH_CAMELLIA_256_CBC_SHA256":          tls.TLS_DH_ANON_WITH_CAMELLIA_256_CBC_SHA256,
	"TLS_RENEGO_PROTECTION_REQUEST":                     tls.TLS_RENEGO_PROTECTION_REQUEST,
	"TLS_FALLBACK_SCSV":                                 tls.TLS_FALLBACK_SCSV,
	"TLS_ECDH_ECDSA_WITH_NULL_SHA":                      tls.TLS_ECDH_ECDSA_WITH_NULL_SHA,
	"TLS_ECDH_ECDSA_WITH_RC4_128_SHA":                   tls.TLS_ECDH_ECDSA_WITH_RC4_128_SHA,
	"TLS_ECDH_ECDSA_WITH_3DES_EDE_CBC_SHA":              tls.TLS_ECDH_ECDSA_WITH_3DES_EDE_CBC_SHA,
	"TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA":               tls.TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA,
	"TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA":               tls.TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA,
	"TLS_ECDHE_ECDSA_WITH_NULL_SHA":                     tls.TLS_ECDHE_ECDSA_WITH_NULL_SHA,
	"TLS_ECDHE_ECDSA_WITH_RC4_128_SHA":                  tls.TLS_ECDHE_ECDSA_WITH_RC4_128_SHA,
	"TLS_ECDHE_ECDSA_WITH_3DES_EDE_CBC_SHA":             tls.TLS_ECDHE_ECDSA_WITH_3DES_EDE_CBC_SHA,
	"TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA":              tls.TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA,
	"TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA":              tls.TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA,
	"TLS_ECDH_RSA_WITH_NULL_SHA":                        tls.TLS_ECDH_RSA_WITH_NULL_SHA,
	"TLS_ECDH_RSA_WITH_RC4_128_SHA":                     tls.TLS_ECDH_RSA_WITH_RC4_128_SHA,
	"TLS_ECDH_RSA_WITH_3DES_EDE_CBC_SHA":                tls.TLS_ECDH_RSA_WITH_3DES_EDE_CBC_SHA,
	"TLS_ECDH_RSA_WITH_AES_128_CBC_SHA":                 tls.TLS_ECDH_RSA_WITH_AES_128_CBC_SHA,
	"TLS_ECDH_RSA_WITH_AES_256_CBC_SHA":                 tls.TLS_ECDH_RSA_WITH_AES_256_CBC_SHA,
	"TLS_ECDHE_RSA_WITH_NULL_SHA":                       tls.TLS_ECDHE_RSA_WITH_NULL_SHA,
	"TLS_ECDHE_RSA_WITH_RC4_128_SHA":                    tls.TLS_ECDHE_RSA_WITH_RC4_128_SHA,
	"TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA":               tls.TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA,
	"TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA":                tls.TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA,
	"TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA":                tls.TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA,
	"TLS_ECDH_ANON_WITH_NULL_SHA":                       tls.TLS_ECDH_ANON_WITH_NULL_SHA,
	"TLS_ECDH_ANON_WITH_RC4_128_SHA":                    tls.TLS_ECDH_ANON_WITH_RC4_128_SHA,
	"TLS_ECDH_ANON_WITH_3DES_EDE_CBC_SHA":               tls.TLS_ECDH_ANON_WITH_3DES_EDE_CBC_SHA,
	"TLS_ECDH_ANON_WITH_AES_128_CBC_SHA":                tls.TLS_ECDH_ANON_WITH_AES_128_CBC_SHA,
	"TLS_ECDH_ANON_WITH_AES_256_CBC_SHA":                tls.TLS_ECDH_ANON_WITH_AES_256_CBC_SHA,
	"TLS_SRP_SHA_WITH_3DES_EDE_CBC_SHA":                 tls.TLS_SRP_SHA_WITH_3DES_EDE_CBC_SHA,
	"TLS_SRP_SHA_RSA_WITH_3DES_EDE_CBC_SHA":             tls.TLS_SRP_SHA_RSA_WITH_3DES_EDE_CBC_SHA,
	"TLS_SRP_SHA_DSS_WITH_3DES_EDE_CBC_SHA":             tls.TLS_SRP_SHA_DSS_WITH_3DES_EDE_CBC_SHA,
	"TLS_SRP_SHA_WITH_AES_128_CBC_SHA":                  tls.TLS_SRP_SHA_WITH_AES_128_CBC_SHA,
	"TLS_SRP_SHA_RSA_WITH_AES_128_CBC_SHA":              tls.TLS_SRP_SHA_RSA_WITH_AES_128_CBC_SHA,
	"TLS_SRP_SHA_DSS_WITH_AES_128_CBC_SHA":              tls.TLS_SRP_SHA_DSS_WITH_AES_128_CBC_SHA,
	"TLS_SRP_SHA_WITH_AES_256_CBC_SHA":                  tls.TLS_SRP_SHA_WITH_AES_256_CBC_SHA,
	"TLS_SRP_SHA_RSA_WITH_AES_256_CBC_SHA":              tls.TLS_SRP_SHA_RSA_WITH_AES_256_CBC_SHA,
	"TLS_SRP_SHA_DSS_WITH_AES_256_CBC_SHA":              tls.TLS_SRP_SHA_DSS_WITH_AES_256_CBC_SHA,
	"TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256":           tls.TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256,
	"TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384":           tls.TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384,
	"TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA256":            tls.TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA256,
	"TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA384":            tls.TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA384,
	"TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256":             tls.TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256,
	"TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384":             tls.TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384,
	"TLS_ECDH_RSA_WITH_AES_128_CBC_SHA256":              tls.TLS_ECDH_RSA_WITH_AES_128_CBC_SHA256,
	"TLS_ECDH_RSA_WITH_AES_256_CBC_SHA384":              tls.TLS_ECDH_RSA_WITH_AES_256_CBC_SHA384,
	"TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256":           tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
	"TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384":           tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
	"TLS_ECDH_ECDSA_WITH_AES_128_GCM_SHA256":            tls.TLS_ECDH_ECDSA_WITH_AES_128_GCM_SHA256,
	"TLS_ECDH_ECDSA_WITH_AES_256_GCM_SHA384":            tls.TLS_ECDH_ECDSA_WITH_AES_256_GCM_SHA384,
	"TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256":             tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
	"TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384":             tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
	"TLS_ECDH_RSA_WITH_AES_128_GCM_SHA256":              tls.TLS_ECDH_RSA_WITH_AES_128_GCM_SHA256,
	"TLS_ECDH_RSA_WITH_AES_256_GCM_SHA384":              tls.TLS_ECDH_RSA_WITH_AES_256_GCM_SHA384,
	"TLS_ECDHE_PSK_WITH_RC4_128_SHA":                    tls.TLS_ECDHE_PSK_WITH_RC4_128_SHA,
	"TLS_ECDHE_PSK_WITH_3DES_EDE_CBC_SHA":               tls.TLS_ECDHE_PSK_WITH_3DES_EDE_CBC_SHA,
	"TLS_ECDHE_PSK_WITH_AES_128_CBC_SHA":                tls.TLS_ECDHE_PSK_WITH_AES_128_CBC_SHA,
	"TLS_ECDHE_PSK_WITH_AES_256_CBC_SHA":                tls.TLS_ECDHE_PSK_WITH_AES_256_CBC_SHA,
	"TLS_ECDHE_PSK_WITH_AES_128_CBC_SHA256":             tls.TLS_ECDHE_PSK_WITH_AES_128_CBC_SHA256,
	"TLS_ECDHE_PSK_WITH_AES_256_CBC_SHA384":             tls.TLS_ECDHE_PSK_WITH_AES_256_CBC_SHA384,
	"TLS_ECDHE_PSK_WITH_NULL_SHA":                       tls.TLS_ECDHE_PSK_WITH_NULL_SHA,
	"TLS_ECDHE_PSK_WITH_NULL_SHA256":                    tls.TLS_ECDHE_PSK_WITH_NULL_SHA256,
	"TLS_ECDHE_PSK_WITH_NULL_SHA384":                    tls.TLS_ECDHE_PSK_WITH_NULL_SHA384,
	"TLS_RSA_WITH_ARIA_128_CBC_SHA256":                  tls.TLS_RSA_WITH_ARIA_128_CBC_SHA256,
	"TLS_RSA_WITH_ARIA_256_CBC_SHA384":                  tls.TLS_RSA_WITH_ARIA_256_CBC_SHA384,
	"TLS_DH_DSS_WITH_ARIA_128_CBC_SHA256":               tls.TLS_DH_DSS_WITH_ARIA_128_CBC_SHA256,
	"TLS_DH_DSS_WITH_ARIA_256_CBC_SHA384":               tls.TLS_DH_DSS_WITH_ARIA_256_CBC_SHA384,
	"TLS_DH_RSA_WITH_ARIA_128_CBC_SHA256":               tls.TLS_DH_RSA_WITH_ARIA_128_CBC_SHA256,
	"TLS_DH_RSA_WITH_ARIA_256_CBC_SHA384":               tls.TLS_DH_RSA_WITH_ARIA_256_CBC_SHA384,
	"TLS_DHE_DSS_WITH_ARIA_128_CBC_SHA256":              tls.TLS_DHE_DSS_WITH_ARIA_128_CBC_SHA256,
	"TLS_DHE_DSS_WITH_ARIA_256_CBC_SHA384":              tls.TLS_DHE_DSS_WITH_ARIA_256_CBC_SHA384,
	"TLS_DHE_RSA_WITH_ARIA_128_CBC_SHA256":              tls.TLS_DHE_RSA_WITH_ARIA_128_CBC_SHA256,
	"TLS_DHE_RSA_WITH_ARIA_256_CBC_SHA384":              tls.TLS_DHE_RSA_WITH_ARIA_256_CBC_SHA384,
	"TLS_DH_ANON_WITH_ARIA_128_CBC_SHA256":              tls.TLS_DH_ANON_WITH_ARIA_128_CBC_SHA256,
	"TLS_DH_ANON_WITH_ARIA_256_CBC_SHA384":              tls.TLS_DH_ANON_WITH_ARIA_256_CBC_SHA384,
	"TLS_ECDHE_ECDSA_WITH_ARIA_128_CBC_SHA256":          tls.TLS_ECDHE_ECDSA_WITH_ARIA_128_CBC_SHA256,
	"TLS_ECDHE_ECDSA_WITH_ARIA_256_CBC_SHA384":          tls.TLS_ECDHE_ECDSA_WITH_ARIA_256_CBC_SHA384,
	"TLS_ECDH_ECDSA_WITH_ARIA_128_CBC_SHA256":           tls.TLS_ECDH_ECDSA_WITH_ARIA_128_CBC_SHA256,
	"TLS_ECDH_ECDSA_WITH_ARIA_256_CBC_SHA384":           tls.TLS_ECDH_ECDSA_WITH_ARIA_256_CBC_SHA384,
	"TLS_ECDHE_RSA_WITH_ARIA_128_CBC_SHA256":            tls.TLS_ECDHE_RSA_WITH_ARIA_128_CBC_SHA256,
	"TLS_ECDHE_RSA_WITH_ARIA_256_CBC_SHA384":            tls.TLS_ECDHE_RSA_WITH_ARIA_256_CBC_SHA384,
	"TLS_ECDH_RSA_WITH_ARIA_128_CBC_SHA256":             tls.TLS_ECDH_RSA_WITH_ARIA_128_CBC_SHA256,
	"TLS_ECDH_RSA_WITH_ARIA_256_CBC_SHA384":             tls.TLS_ECDH_RSA_WITH_ARIA_256_CBC_SHA384,
	"TLS_RSA_WITH_ARIA_128_GCM_SHA256":                  tls.TLS_RSA_WITH_ARIA_128_GCM_SHA256,
	"TLS_RSA_WITH_ARIA_256_GCM_SHA384":                  tls.TLS_RSA_WITH_ARIA_256_GCM_SHA384,
	"TLS_DHE_RSA_WITH_ARIA_128_GCM_SHA256":              tls.TLS_DHE_RSA_WITH_ARIA_128_GCM_SHA256,
	"TLS_DHE_RSA_WITH_ARIA_256_GCM_SHA384":              tls.TLS_DHE_RSA_WITH_ARIA_256_GCM_SHA384,
	"TLS_DH_RSA_WITH_ARIA_128_GCM_SHA256":               tls.TLS_DH_RSA_WITH_ARIA_128_GCM_SHA256,
	"TLS_DH_RSA_WITH_ARIA_256_GCM_SHA384":               tls.TLS_DH_RSA_WITH_ARIA_256_GCM_SHA384,
	"TLS_DHE_DSS_WITH_ARIA_128_GCM_SHA256":              tls.TLS_DHE_DSS_WITH_ARIA_128_GCM_SHA256,
	"TLS_DHE_DSS_WITH_ARIA_256_GCM_SHA384":              tls.TLS_DHE_DSS_WITH_ARIA_256_GCM_SHA384,
	"TLS_DH_DSS_WITH_ARIA_128_GCM_SHA256":               tls.TLS_DH_DSS_WITH_ARIA_128_GCM_SHA256,
	"TLS_DH_DSS_WITH_ARIA_256_GCM_SHA384":               tls.TLS_DH_DSS_WITH_ARIA_256_GCM_SHA384,
	"TLS_DH_ANON_WITH_ARIA_128_GCM_SHA256":              tls.TLS_DH_ANON_WITH_ARIA_128_GCM_SHA256,
	"TLS_DH_ANON_WITH_ARIA_256_GCM_SHA384":              tls.TLS_DH_ANON_WITH_ARIA_256_GCM_SHA384,
	"TLS_ECDHE_ECDSA_WITH_ARIA_128_GCM_SHA256":          tls.TLS_ECDHE_ECDSA_WITH_ARIA_128_GCM_SHA256,
	"TLS_ECDHE_ECDSA_WITH_ARIA_256_GCM_SHA384":          tls.TLS_ECDHE_ECDSA_WITH_ARIA_256_GCM_SHA384,
	"TLS_ECDH_ECDSA_WITH_ARIA_128_GCM_SHA256":           tls.TLS_ECDH_ECDSA_WITH_ARIA_128_GCM_SHA256,
	"TLS_ECDH_ECDSA_WITH_ARIA_256_GCM_SHA384":           tls.TLS_ECDH_ECDSA_WITH_ARIA_256_GCM_SHA384,
	"TLS_ECDHE_RSA_WITH_ARIA_128_GCM_SHA256":            tls.TLS_ECDHE_RSA_WITH_ARIA_128_GCM_SHA256,
	"TLS_ECDHE_RSA_WITH_ARIA_256_GCM_SHA384":            tls.TLS_ECDHE_RSA_WITH_ARIA_256_GCM_SHA384,
	"TLS_ECDH_RSA_WITH_ARIA_128_GCM_SHA256":             tls.TLS_ECDH_RSA_WITH_ARIA_128_GCM_SHA256,
	"TLS_ECDH_RSA_WITH_ARIA_256_GCM_SHA384":             tls.TLS_ECDH_RSA_WITH_ARIA_256_GCM_SHA384,
	"TLS_PSK_WITH_ARIA_128_CBC_SHA256":                  tls.TLS_PSK_WITH_ARIA_128_CBC_SHA256,
	"TLS_PSK_WITH_ARIA_256_CBC_SHA384":                  tls.TLS_PSK_WITH_ARIA_256_CBC_SHA384,
	"TLS_DHE_PSK_WITH_ARIA_128_CBC_SHA256":              tls.TLS_DHE_PSK_WITH_ARIA_128_CBC_SHA256,
	"TLS_DHE_PSK_WITH_ARIA_256_CBC_SHA384":              tls.TLS_DHE_PSK_WITH_ARIA_256_CBC_SHA384,
	"TLS_RSA_PSK_WITH_ARIA_128_CBC_SHA256":              tls.TLS_RSA_PSK_WITH_ARIA_128_CBC_SHA256,
	"TLS_RSA_PSK_WITH_ARIA_256_CBC_SHA384":              tls.TLS_RSA_PSK_WITH_ARIA_256_CBC_SHA384,
	"TLS_PSK_WITH_ARIA_128_GCM_SHA256":                  tls.TLS_PSK_WITH_ARIA_128_GCM_SHA256,
	"TLS_PSK_WITH_ARIA_256_GCM_SHA384":                  tls.TLS_PSK_WITH_ARIA_256_GCM_SHA384,
	"TLS_DHE_PSK_WITH_ARIA_128_GCM_SHA256":              tls.TLS_DHE_PSK_WITH_ARIA_128_GCM_SHA256,
	"TLS_DHE_PSK_WITH_ARIA_256_GCM_SHA384":              tls.TLS_DHE_PSK_WITH_ARIA_256_GCM_SHA384,
	"TLS_RSA_PSK_WITH_ARIA_128_GCM_SHA256":              tls.TLS_RSA_PSK_WITH_ARIA_128_GCM_SHA256,
	"TLS_RSA_PSK_WITH_ARIA_256_GCM_SHA384":              tls.TLS_RSA_PSK_WITH_ARIA_256_GCM_SHA384,
	"TLS_ECDHE_PSK_WITH_ARIA_128_CBC_SHA256":            tls.TLS_ECDHE_PSK_WITH_ARIA_128_CBC_SHA256,
	"TLS_ECDHE_PSK_WITH_ARIA_256_CBC_SHA384":            tls.TLS_ECDHE_PSK_WITH_ARIA_256_CBC_SHA384,
	"TLS_ECDHE_ECDSA_WITH_CAMELLIA_128_CBC_SHA256":      tls.TLS_ECDHE_ECDSA_WITH_CAMELLIA_128_CBC_SHA256,
	"TLS_ECDHE_ECDSA_WITH_CAMELLIA_256_CBC_SHA384":      tls.TLS_ECDHE_ECDSA_WITH_CAMELLIA_256_CBC_SHA384,
	"TLS_ECDH_ECDSA_WITH_CAMELLIA_128_CBC_SHA256":       tls.TLS_ECDH_ECDSA_WITH_CAMELLIA_128_CBC_SHA256,
	"TLS_ECDH_ECDSA_WITH_CAMELLIA_256_CBC_SHA384":       tls.TLS_ECDH_ECDSA_WITH_CAMELLIA_256_CBC_SHA384,
	"TLS_ECDHE_RSA_WITH_CAMELLIA_128_CBC_SHA256":        tls.TLS_ECDHE_RSA_WITH_CAMELLIA_128_CBC_SHA256,
	"TLS_ECDHE_RSA_WITH_CAMELLIA_256_CBC_SHA384":        tls.TLS_ECDHE_RSA_WITH_CAMELLIA_256_CBC_SHA384,
	"TLS_ECDH_RSA_WITH_CAMELLIA_128_CBC_SHA256":         tls.TLS_ECDH_RSA_WITH_CAMELLIA_128_CBC_SHA256,
	"TLS_ECDH_RSA_WITH_CAMELLIA_256_CBC_SHA384":         tls.TLS_ECDH_RSA_WITH_CAMELLIA_256_CBC_SHA384,
	"TLS_RSA_WITH_CAMELLIA_128_GCM_SHA256":              tls.TLS_RSA_WITH_CAMELLIA_128_GCM_SHA256,
	"TLS_RSA_WITH_CAMELLIA_256_GCM_SHA384":              tls.TLS_RSA_WITH_CAMELLIA_256_GCM_SHA384,
	"TLS_DHE_RSA_WITH_CAMELLIA_128_GCM_SHA256":          tls.TLS_DHE_RSA_WITH_CAMELLIA_128_GCM_SHA256,
	"TLS_DHE_RSA_WITH_CAMELLIA_256_GCM_SHA384":          tls.TLS_DHE_RSA_WITH_CAMELLIA_256_GCM_SHA384,
	"TLS_DH_RSA_WITH_CAMELLIA_128_GCM_SHA256":           tls.TLS_DH_RSA_WITH_CAMELLIA_128_GCM_SHA256,
	"TLS_DH_RSA_WITH_CAMELLIA_256_GCM_SHA384":           tls.TLS_DH_RSA_WITH_CAMELLIA_256_GCM_SHA384,
	"TLS_DHE_DSS_WITH_CAMELLIA_128_GCM_SHA256":          tls.TLS_DHE_DSS_WITH_CAMELLIA_128_GCM_SHA256,
	"TLS_DHE_DSS_WITH_CAMELLIA_256_GCM_SHA384":          tls.TLS_DHE_DSS_WITH_CAMELLIA_256_GCM_SHA384,
	"TLS_DH_DSS_WITH_CAMELLIA_128_GCM_SHA256":           tls.TLS_DH_DSS_WITH_CAMELLIA_128_GCM_SHA256,
	"TLS_DH_DSS_WITH_CAMELLIA_256_GCM_SHA384":           tls.TLS_DH_DSS_WITH_CAMELLIA_256_GCM_SHA384,
	"TLS_DH_ANON_WITH_CAMELLIA_128_GCM_SHA256":          tls.TLS_DH_ANON_WITH_CAMELLIA_128_GCM_SHA256,
	"TLS_DH_ANON_WITH_CAMELLIA_256_GCM_SHA384":          tls.TLS_DH_ANON_WITH_CAMELLIA_256_GCM_SHA384,
	"TLS_ECDHE_ECDSA_WITH_CAMELLIA_128_GCM_SHA256":      tls.TLS_ECDHE_ECDSA_WITH_CAMELLIA_128_GCM_SHA256,
	"TLS_ECDHE_ECDSA_WITH_CAMELLIA_256_GCM_SHA384":      tls.TLS_ECDHE_ECDSA_WITH_CAMELLIA_256_GCM_SHA384,
	"TLS_ECDH_ECDSA_WITH_CAMELLIA_128_GCM_SHA256":       tls.TLS_ECDH_ECDSA_WITH_CAMELLIA_128_GCM_SHA256,
	"TLS_ECDH_ECDSA_WITH_CAMELLIA_256_GCM_SHA384":       tls.TLS_ECDH_ECDSA_WITH_CAMELLIA_256_GCM_SHA384,
	"TLS_ECDHE_RSA_WITH_CAMELLIA_128_GCM_SHA256":        tls.TLS_ECDHE_RSA_WITH_CAMELLIA_128_GCM_SHA256,
	"TLS_ECDHE_RSA_WITH_CAMELLIA_256_GCM_SHA384":        tls.TLS_ECDHE_RSA_WITH_CAMELLIA_256_GCM_SHA384,
	"TLS_ECDH_RSA_WITH_CAMELLIA_128_GCM_SHA256":         tls.TLS_ECDH_RSA_WITH_CAMELLIA_128_GCM_SHA256,
	"TLS_ECDH_RSA_WITH_CAMELLIA_256_GCM_SHA384":         tls.TLS_ECDH_RSA_WITH_CAMELLIA_256_GCM_SHA384,
	"TLS_PSK_WITH_CAMELLIA_128_GCM_SHA256":              tls.TLS_PSK_WITH_CAMELLIA_128_GCM_SHA256,
	"TLS_PSK_WITH_CAMELLIA_256_GCM_SHA384":              tls.TLS_PSK_WITH_CAMELLIA_256_GCM_SHA384,
	"TLS_DHE_PSK_WITH_CAMELLIA_128_GCM_SHA256":          tls.TLS_DHE_PSK_WITH_CAMELLIA_128_GCM_SHA256,
	"TLS_DHE_PSK_WITH_CAMELLIA_256_GCM_SHA384":          tls.TLS_DHE_PSK_WITH_CAMELLIA_256_GCM_SHA384,
	"TLS_RSA_PSK_WITH_CAMELLIA_128_GCM_SHA256":          tls.TLS_RSA_PSK_WITH_CAMELLIA_128_GCM_SHA256,
	"TLS_RSA_PSK_WITH_CAMELLIA_256_GCM_SHA384":          tls.TLS_RSA_PSK_WITH_CAMELLIA_256_GCM_SHA384,
	"TLS_PSK_WITH_CAMELLIA_128_CBC_SHA256":              tls.TLS_PSK_WITH_CAMELLIA_128_CBC_SHA256,
	"TLS_PSK_WITH_CAMELLIA_256_CBC_SHA384":              tls.TLS_PSK_WITH_CAMELLIA_256_CBC_SHA384,
	"TLS_DHE_PSK_WITH_CAMELLIA_128_CBC_SHA256":          tls.TLS_DHE_PSK_WITH_CAMELLIA_128_CBC_SHA256,
	"TLS_DHE_PSK_WITH_CAMELLIA_256_CBC_SHA384":          tls.TLS_DHE_PSK_WITH_CAMELLIA_256_CBC_SHA384,
	"TLS_RSA_PSK_WITH_CAMELLIA_128_CBC_SHA256":          tls.TLS_RSA_PSK_WITH_CAMELLIA_128_CBC_SHA256,
	"TLS_RSA_PSK_WITH_CAMELLIA_256_CBC_SHA384":          tls.TLS_RSA_PSK_WITH_CAMELLIA_256_CBC_SHA384,
	"TLS_ECDHE_PSK_WITH_CAMELLIA_128_CBC_SHA256":        tls.TLS_ECDHE_PSK_WITH_CAMELLIA_128_CBC_SHA256,
	"TLS_ECDHE_PSK_WITH_CAMELLIA_256_CBC_SHA384":        tls.TLS_ECDHE_PSK_WITH_CAMELLIA_256_CBC_SHA384,
	"TLS_RSA_WITH_AES_128_CCM":                          tls.TLS_RSA_WITH_AES_128_CCM,
	"TLS_RSA_WITH_AES_256_CCM":                          tls.TLS_RSA_WITH_AES_256_CCM,
	"TLS_DHE_RSA_WITH_AES_128_CCM":                      tls.TLS_DHE_RSA_WITH_AES_128_CCM,
	"TLS_DHE_RSA_WITH_AES_256_CCM":                      tls.TLS_DHE_RSA_WITH_AES_256_CCM,
	"TLS_RSA_WITH_AES_128_CCM_8":                        tls.TLS_RSA_WITH_AES_128_CCM_8,
	"TLS_RSA_WITH_AES_256_CCM_8":                        tls.TLS_RSA_WITH_AES_256_CCM_8,
	"TLS_DHE_RSA_WITH_AES_128_CCM_8":                    tls.TLS_DHE_RSA_WITH_AES_128_CCM_8,
	"TLS_DHE_RSA_WITH_AES_256_CCM_8":                    tls.TLS_DHE_RSA_WITH_AES_256_CCM_8,
	"TLS_PSK_WITH_AES_128_CCM":                          tls.TLS_PSK_WITH_AES_128_CCM,
	"TLS_PSK_WITH_AES_256_CCM":                          tls.TLS_PSK_WITH_AES_256_CCM,
	"TLS_DHE_PSK_WITH_AES_128_CCM":                      tls.TLS_DHE_PSK_WITH_AES_128_CCM,
	"TLS_DHE_PSK_WITH_AES_256_CCM":                      tls.TLS_DHE_PSK_WITH_AES_256_CCM,
	"TLS_PSK_WITH_AES_128_CCM_8":                        tls.TLS_PSK_WITH_AES_128_CCM_8,
	"TLS_PSK_WITH_AES_256_CCM_8":                        tls.TLS_PSK_WITH_AES_256_CCM_8,
	"TLS_PSK_DHE_WITH_AES_128_CCM_8":                    tls.TLS_PSK_DHE_WITH_AES_128_CCM_8,
	"TLS_PSK_DHE_WITH_AES_256_CCM_8":                    tls.TLS_PSK_DHE_WITH_AES_256_CCM_8,
	"TLS_ECDHE_ECDSA_WITH_AES_128_CCM":                  tls.TLS_ECDHE_ECDSA_WITH_AES_128_CCM,
	"TLS_ECDHE_ECDSA_WITH_AES_256_CCM":                  tls.TLS_ECDHE_ECDSA_WITH_AES_256_CCM,
	"TLS_ECDHE_ECDSA_WITH_AES_128_CCM_8":                tls.TLS_ECDHE_ECDSA_WITH_AES_128_CCM_8,
	"TLS_ECDHE_ECDSA_WITH_AES_256_CCM_8":                tls.TLS_ECDHE_ECDSA_WITH_AES_256_CCM_8,
	"TLS_ECDHE_PSK_WITH_AES_128_GCM_SHA256":             tls.TLS_ECDHE_PSK_WITH_AES_128_GCM_SHA256,
	"TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256":       tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,
	"TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256":     tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256,
	"TLS_DHE_RSA_WITH_CHACHA20_POLY1305_SHA256":         tls.TLS_DHE_RSA_WITH_CHACHA20_POLY1305_SHA256,
	"TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256_OLD":   tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256_OLD,
	"TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256_OLD": tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256_OLD,
	"TLS_DHE_RSA_WITH_CHACHA20_POLY1305_SHA256_OLD":     tls.TLS_DHE_RSA_WITH_CHACHA20_POLY1305_SHA256_OLD,
	// "SSL_RSA_WITH_RC2_CBC_MD5":                          tls.SSL_RSA_WITH_RC2_CBC_MD5,
	// "SSL_RSA_WITH_IDEA_CBC_MD5":                         tls.SSL_RSA_WITH_IDEA_CBC_MD5,
	// "SSL_RSA_WITH_DES_CBC_MD5":                          tls.SSL_RSA_WITH_DES_CBC_MD5,
	// "SSL_RSA_WITH_3DES_EDE_CBC_MD5":                     tls.SSL_RSA_WITH_3DES_EDE_CBC_MD5,
	// "SSL_EN_RC2_128_CBC_WITH_MD5":                       tls.SSL_EN_RC2_128_CBC_WITH_MD5,
	// "OP_PCL_TLS10_AES_128_CBC_SHA512":                   tls.OP_PCL_TLS10_AES_128_CBC_SHA512,
}
