// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 16:41:14
// FilePath: /yaml_scan/pkg/retryablehttp/client.go
// Description: 实现了具有自动重试功能的HTTP客户端，可以容忍短暂的网络故障和服务中断
package retryablehttp

import (
	"net/http"
	"sync/atomic"
	"time"

	"golang.org/x/net/http2"
)

// Client 是用于发起HTTP请求的客户端
// 它增加了额外的功能，如自动重试，以容忍短暂的网络中断或服务不可用
type Client struct {
	// HTTPClient 是内部使用的HTTP客户端（支持HTTP/1.x和通过连接升级的HTTP/2）
	HTTPClient *http.Client
	// HTTPClient2 是内部使用的HTTP客户端，配置为在传输层级别使用原生HTTP/2
	HTTPClient2 *http.Client
	// requestCounter 是一个原子计数器，用于跟踪请求的数量
	requestCounter atomic.Uint32

	// RequestLogHook 允许用户提供一个函数，在每次重试前被调用
	// 可用于请求日志记录、监控或调试
	RequestLogHook RequestLogHook
	// ResponseLogHook 允许用户提供一个函数，在执行每个HTTP请求后被调用
	// 用于响应日志记录、监控或审计
	ResponseLogHook ResponseLogHook
	// ErrorHandler 指定要使用的自定义错误处理器（如果有）
	// 允许自定义请求失败时的行为
	ErrorHandler ErrorHandler
	// CheckRetry 指定处理重试的策略，在每个请求后调用
	// 默认策略是DefaultRetryPolicy
	// 它决定了请求失败后是否应该重试
	CheckRetry CheckRetry
	// Backoff 指定重试之间等待的策略
	// 它确定了每次重试之间的等待时间
	Backoff Backoff
	// options 包含客户端的配置选项
	options Options
}


// NewClient 创建一个带有默认设置的新客户端
// @param options Options: 客户端的配置选项，包括超时、最大重试次数等
// @return *Client *Client: 配置好的retryablehttp客户端实例
func NewClient(options Options) *Client {
	// 初始化HTTP客户端，根据options选项决定使用哪种客户端
	var httpclient *http.Client
	
	// 如果提供了自定义的HTTP客户端，则使用它
	if options.HttpClient != nil {
		httpclient = options.HttpClient
	} else if options.KillIdleConn {
		// 如果需要关闭空闲连接，使用默认非池化的客户端
		httpclient = DefaultClient()
	} else {
		// 否则使用默认池化的客户端，支持连接复用
		httpclient = DefaultPooledClient()
	}

	// 创建支持原生HTTP/2的客户端
	httpclient2 := DefaultClient()
	// 配置传输层以支持HTTP/2
	if err := http2.ConfigureTransport(httpclient2.Transport.(*http.Transport)); err != nil {
		return nil
	}

	// 初始化重试策略和退避策略
	var retryPolicy CheckRetry
	var backoff Backoff

	// 设置默认的重试策略
	retryPolicy = DefaultRetryPolicy()
	if options.CheckRetry != nil {
		retryPolicy = options.CheckRetry
	}

	// 设置默认的退避策略
	backoff = DefaultBackoff()
	if options.Backoff != nil {
		backoff = options.Backoff
	}

	// 为客户端添加超时设置
	if options.Timeout > 0 {
		httpclient.Timeout = options.Timeout
		httpclient2.Timeout = options.Timeout
	}

	// 如果需要，根据总体超时时间按比例调整每个请求的超时时间（30%）
	// 这样做是为了在多次重试的情况下，留出足够的总时间
	if options.Timeout > time.Second*15 && options.RetryMax > 1 && !options.NoAdjustTimeout {
		httpclient.Timeout = time.Duration(options.Timeout.Seconds()*0.3) * time.Second
	}

	c := &Client{
		HTTPClient:  httpclient,
		HTTPClient2: httpclient2,
		CheckRetry:  retryPolicy,
		Backoff:     backoff,
		options:     options,
	}

	// 设置是否关闭空闲连接
	c.setKillIdleConnections()
	return c
}

// setKillIdleConnections 在两种情况下设置关闭空闲连接的开关：
// 1. 如果http.Client有需要我们这样做的设置
// 2. 用户已默认启用此功能，这种情况下我们不需要做任何事情
// @receiver c 
func (c *Client) setKillIdleConnections() {
	// 检查HTTP客户端是否存在，以及是否已经设置了关闭空闲连接
	if c.HTTPClient != nil || !c.options.KillIdleConn {
		if b, ok := c.HTTPClient.Transport.(*http.Transport); ok {
			// 如果禁用了长连接或每个主机的最大连接数小于0，则设置关闭空闲连接
			// 这些设置表明用户不希望保持连接池
			c.options.KillIdleConn = b.DisableKeepAlives || b.MaxConnsPerHost < 0
		}
	}
}