// Author: chenjb
// Version: V1.0
// Date: 2025-06-16 17:30:28
// FilePath: /yaml_scan/pkg/authprovider/multi_test.go
// Description:
package authprovider

import (
	"fmt"
	"net/http"
	"net/url"
	"testing"
	"yaml_scan/pkg/authprovider/authx"
	"yaml_scan/pkg/retryablehttp"
	urlutil "yaml_scan/utils/url"

	"github.com/stretchr/testify/require"
)


// TestMultiAuthProvider_LookupAddr 测试多重认证提供者的LookupAddr方法
// 确保它能正确从所有包含的提供者中查找域名认证策略
func TestMultiAuthProvider_LookupAddr(t *testing.T) {
	// 创建模拟认证策略
	strategy1 := &mockAuthStrategy{name: "strategy1"}
	strategy2 := &mockAuthStrategy{name: "strategy2"}
	strategy3 := &mockAuthStrategy{name: "strategy3"}
	
	// 创建模拟认证提供者
	provider1 := &mockAuthProvider{
		addrMap: map[string][]authx.AuthStrategy{
			"example.com": {strategy1},
		},
	}
	provider2 := &mockAuthProvider{
		addrMap: map[string][]authx.AuthStrategy{
			"example.com": {strategy2},
			"api.example.com": {strategy3},
		},
	}
	
	// 创建多重认证提供者
	multiProvider := NewMultiAuthProvider(provider1, provider2)
	
	// 测试存在于两个提供者中的域名
	strategies := multiProvider.LookupAddr("example.com")
	require.Len(t, strategies, 1, "应该找到1个认证策略")
	
	// 测试只存在于第二个提供者中的域名
	strategies = multiProvider.LookupAddr("api.example.com")
	require.Len(t, strategies, 1, "应该找到1个认证策略")
	
	// 测试不存在的域名
	strategies = multiProvider.LookupAddr("unknown.com")
	require.Empty(t, strategies, "对于不存在的域名应该返回空的策略列表")
}

// TestMultiAuthProvider_LookupURL 测试多重认证提供者的LookupURL方法
// 确保它能正确从所有包含的提供者中查找URL认证策略
func TestMultiAuthProvider_LookupURL(t *testing.T) {
	// 创建模拟认证策略
	strategy1 := &mockAuthStrategy{name: "strategy1"}
	strategy2 := &mockAuthStrategy{name: "strategy2"}
	
	// 创建模拟认证提供者
	provider1 := &mockAuthProvider{
		urlMap: map[string][]authx.AuthStrategy{
			"example.com": {strategy1},
		},
	}
	provider2 := &mockAuthProvider{
		urlMap: map[string][]authx.AuthStrategy{
			"example.com": {strategy2},
		},
	}
	
	// 创建多重认证提供者
	multiProvider := NewMultiAuthProvider(provider1, provider2)
	
	// 创建测试URL
	testURL, err := url.Parse("https://example.com/path")
	require.NoError(t, err, "解析URL不应该返回错误")
	
	// 测试URL查找
	strategies := multiProvider.LookupURL(testURL)
	require.Len(t, strategies, 1, "应该找到1个认证策略")
	
	// 创建不匹配的URL
	unknownURL, err := url.Parse("https://unknown.com/path")
	require.NoError(t, err, "解析URL不应该返回错误")
	
	// 测试不匹配的URL
	strategies = multiProvider.LookupURL(unknownURL)
	require.Empty(t, strategies, "对于不匹配的URL应该返回空的策略列表")
}

// TestMultiAuthProvider_LookupURLX 测试多重认证提供者的LookupURLX方法
// 确保它能正确从所有包含的提供者中查找自定义URL认证策略
func TestMultiAuthProvider_LookupURLX(t *testing.T) {
	// 创建模拟认证策略
	strategy1 := &mockAuthStrategy{name: "strategy1"}
	strategy2 := &mockAuthStrategy{name: "strategy2"}
	
	// 创建模拟认证提供者
	provider1 := &mockAuthProvider{
		urlxMap: map[string][]authx.AuthStrategy{
			"example.com": {strategy1},
		},
	}
	provider2 := &mockAuthProvider{
		urlxMap: map[string][]authx.AuthStrategy{
			"example.com": {strategy2},
		},
	}
	
	// 创建多重认证提供者
	multiProvider := NewMultiAuthProvider(provider1, provider2)
	
	// 创建测试自定义URL
	testURL := &urlutil.URL{
		URL: &url.URL{
			Host: "example.com",
		},
	}
	
	// 测试自定义URL查找
	strategies := multiProvider.LookupURLX(testURL)
	require.Len(t, strategies, 1, "应该找到1个认证策略")
	
	// 创建不匹配的自定义URL
	unknownURL := &urlutil.URL{
		URL: &url.URL{
			Host: "unknown.com",
		},
	}
	
	// 测试不匹配的自定义URL
	strategies = multiProvider.LookupURLX(unknownURL)
	require.Empty(t, strategies, "对于不匹配的自定义URL应该返回空的策略列表")
}

// TestMultiAuthProvider_GetTemplatePaths 测试多重认证提供者的GetTemplatePaths方法
// 确保它能正确从所有包含的提供者中获取模板路径
func TestMultiAuthProvider_GetTemplatePaths(t *testing.T) {
	// 创建模拟认证提供者，带有模板路径
	provider1 := &mockAuthProvider{
		templatePaths: []string{"template1.yaml", "template2.yaml"},
	}
	provider2 := &mockAuthProvider{
		templatePaths: []string{"template2.yaml", "template3.yaml"},
	}
	
	// 创建多重认证提供者
	multiProvider := NewMultiAuthProvider(provider1, provider2)
	
	// 获取模板路径
	paths := multiProvider.GetTemplatePaths()
	
	// 验证结果（应该合并且去重）
	require.Len(t, paths, 4, "应该返回4个去重后的模板路径")
	require.Contains(t, paths, "template1.yaml", "应该包含template1.yaml")
	require.Contains(t, paths, "template2.yaml", "应该包含template2.yaml")
	require.Contains(t, paths, "template3.yaml", "应该包含template3.yaml")
}

// TestMultiAuthProvider_PreFetchSecrets 测试多重认证提供者的PreFetchSecrets方法
// 确保它能正确调用所有包含的提供者的预获取方法
func TestMultiAuthProvider_PreFetchSecrets(t *testing.T) {
	// 创建模拟认证提供者，带有预获取计数器
	provider1 := &mockAuthProvider{
		name: "provider1",
	}
	provider2 := &mockAuthProvider{
		name: "provider2",
	}
	
	// 创建多重认证提供者
	multiProvider := NewMultiAuthProvider(provider1, provider2)
	
	// 执行预获取
	err := multiProvider.PreFetchSecrets()
	require.NoError(t, err, "预获取不应该返回错误")
	
	// 验证两个提供者的预获取方法都被调用
	require.Equal(t, 1, provider1.preFetchCount, "provider1的预获取方法应该被调用1次")
	require.Equal(t, 1, provider2.preFetchCount, "provider2的预获取方法应该被调用1次")
	
	// 测试第一个提供者返回错误的情况
	provider1.preFetchError = true
	err = multiProvider.PreFetchSecrets()
	require.Error(t, err, "如果任一提供者返回错误，预获取应该返回错误")
	require.Contains(t, err.Error(), "provider1", "错误信息应该包含第一个提供者的名称")
}

// 模拟认证策略，用于测试
type mockAuthStrategy struct {
	name string
}

// Apply 实现AuthStrategy接口的Apply方法
func (m *mockAuthStrategy) Apply(req *http.Request) {}

// ApplyOnRR 实现AuthStrategy接口的ApplyOnRR方法
func (m *mockAuthStrategy) ApplyOnRR(req *retryablehttp.Request) {}

// 模拟认证提供者，用于测试
type mockAuthProvider struct {
	name           string
	addrMap        map[string][]authx.AuthStrategy
	urlMap         map[string][]authx.AuthStrategy
	urlxMap        map[string][]authx.AuthStrategy
	templatePaths  []string
	preFetchCount  int
	preFetchError  bool
}

// LookupAddr 实现AuthProvider接口的LookupAddr方法
func (m *mockAuthProvider) LookupAddr(host string) []authx.AuthStrategy {
	return m.addrMap[host]
}

// LookupURL 实现AuthProvider接口的LookupURL方法
func (m *mockAuthProvider) LookupURL(u *url.URL) []authx.AuthStrategy {
	return m.urlMap[u.Host]
}

// LookupURLX 实现AuthProvider接口的LookupURLX方法
func (m *mockAuthProvider) LookupURLX(u *urlutil.URL) []authx.AuthStrategy {
	return m.urlxMap[u.Host]
}

// GetTemplatePaths 实现AuthProvider接口的GetTemplatePaths方法
func (m *mockAuthProvider) GetTemplatePaths() []string {
	return m.templatePaths
}

// PreFetchSecrets 实现AuthProvider接口的PreFetchSecrets方法
func (m *mockAuthProvider) PreFetchSecrets() error {
	m.preFetchCount++
	if m.preFetchError {
		return fmt.Errorf("%s error", m.name)
	}
	return nil
}

