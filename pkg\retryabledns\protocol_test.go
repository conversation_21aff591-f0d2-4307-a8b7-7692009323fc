package retryabledns

import (
	"testing"
)


// TestProtocol_String 测试Protocol类型的String()方法
func TestProtocolString(t *testing.T) {
    // 测试UDP协议
    if UDP.String() != "udp" {
        t.<PERSON>rf("Expected UDP.String() to be 'udp', got '%s'", UDP.String())
    }

    // 测试TCP协议
    if TCP.String() != "tcp" {
        t.<PERSON><PERSON>("Expected TCP.String() to be 'tcp', got '%s'", TCP.String())
    }

    // 测试DOH协议
    if DOH.String() != "doh" {
        t.Errorf("Expected DOH.String() to be 'doh', got '%s'", DOH.String())
    }

    // 测试DOT协议
    if DOT.String() != "dot" {
        t.Errorf("Expected DOT.String() to be 'dot', got '%s'", DOT.String())
    }

    // 测试未知协议
    unknown := Protocol("unknown")
    if unknown.String() != "unknown" {
        t.<PERSON><PERSON>rf("Expected unknown.Protocol.String() to be 'unknown', got '%s'", unknown.String())
    }
}


// TestProtocol_StringWithSemicolon 测试Protocol类型的StringWithSemicolon()方法
func TestProtocolStringWithSemicolon(t *testing.T) {
    // 测试UDP协议
    if UDP.StringWithSemicolon() != "udp:" {
        t.Errorf("Expected UDP.StringWithSemicolon() to be 'udp:', got '%s'", UDP.StringWithSemicolon())
    }

    // 测试TCP协议
    if TCP.StringWithSemicolon() != "tcp:" {
        t.Errorf("Expected TCP.StringWithSemicolon() to be 'tcp:', got '%s'", TCP.StringWithSemicolon())
    }

    // 测试DOH协议
    if DOH.StringWithSemicolon() != "doh:" {
        t.Errorf("Expected DOH.StringWithSemicolon() to be 'doh:', got '%s'", DOH.StringWithSemicolon())
    }

    // 测试DOT协议
    if DOT.StringWithSemicolon() != "dot:" {
        t.Errorf("Expected DOT.StringWithSemicolon() to be 'dot:', got '%s'", DOT.StringWithSemicolon())
    }

    // 测试未知协议
    unknown := Protocol("unknown")
    if unknown.StringWithSemicolon() != "unknown:" {
        t.Errorf("Expected unknown.Protocol.StringWithSemicolon() to be 'unknown:', got '%s'", unknown.StringWithSemicolon())
    }
}


// TestDohProtocol_String 测试DohProtocol类型的String()方法
func TestDohProtocolString(t *testing.T) {
    // 测试JsonAPI协议
    if JsonAPI.String() != "jsonapi" {
        t.Errorf("Expected JsonAPI.String() to be 'jsonapi', got '%s'", JsonAPI.String())
    }

    // 测试GET协议
    if GET.String() != "get" {
        t.Errorf("Expected GET.String() to be 'get', got '%s'", GET.String())
    }

    // 测试POST协议
    if POST.String() != "post" {
        t.Errorf("Expected POST.String() to be 'post', got '%s'", POST.String())
    }

    // 测试未知协议
    unknown := DohProtocol("unknown")
    if unknown.String() != "unknown" {
        t.Errorf("Expected unknown.DohProtocol.String() to be 'unknown', got '%s'", unknown.String())
    }
}


// TestDohProtocol_StringWithSemicolon 测试DohProtocol类型的StringWithSemicolon()方法
func TestDohProtocolStringWithSemicolon(t *testing.T) {
    // 测试JsonAPI协议
    if JsonAPI.StringWithSemicolon() != ":jsonapi" {
        t.Errorf("Expected JsonAPI.StringWithSemicolon() to be ':jsonapi', got '%s'", JsonAPI.StringWithSemicolon())
    }

    // 测试GET协议
    if GET.StringWithSemicolon() != ":get" {
        t.Errorf("Expected GET.StringWithSemicolon() to be ':get', got '%s'", GET.StringWithSemicolon())
    }

    // 测试POST协议
    if POST.StringWithSemicolon() != ":post" {
        t.Errorf("Expected POST.StringWithSemicolon() to be ':post', got '%s'", POST.StringWithSemicolon())
    }

    // 测试未知协议
    unknown := DohProtocol("unknown")
    if unknown.StringWithSemicolon() != ":unknown" {
        t.Errorf("Expected unknown.DohProtocol.StringWithSemicolon() to be ':unknown', got '%s'", unknown.StringWithSemicolon())
    }
}