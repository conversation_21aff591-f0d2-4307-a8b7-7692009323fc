// Author: chenjb
// Version: V1.0
// Date: 2025-05-21 11:22:32
// FilePath: /yaml_scan/utils/errkit/kind_test.go
// Description:
package errkit

import (
	"context"
	"errors"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// 测试基本错误类型
func TestPrimitiveErrKind(t *testing.T) {
	// 创建一个基本错误类型
	testKind := NewPrimitiveErrKind("test-error", "测试错误", nil)
	
	// 验证基本属性
	require.Equal(t, "test-error", testKind.String(), "错误类型ID应为test-error")
	require.Equal(t, "测试错误", testKind.Description(), "错误类型描述应为测试错误")
	
	// 测试Is方法
	sameKind := NewPrimitiveErrKind("test-error", "另一个描述", nil)
	require.True(t, testKind.Is(sameKind), "相同ID的错误类型应视为相同")
	
	differentKind := NewPrimitiveErrKind("different-error", "不同的错误", nil)
	require.False(t, testKind.Is(differentKind), "不同ID的错误类型应视为不同")
	
	// 测试IsParent方法（基本类型总是返回false）
	require.False(t, testKind.IsParent(sameKind), "基本错误类型的IsParent应总是返回false")
	
	// 测试Represents方法
	customRepresents := NewPrimitiveErrKind("custom", "自定义", func(err *ErrorX) bool {
		return err.Cause() != nil && err.Cause().Error() == "特定错误"
	})
	
	matchingErr := FromError(errors.New("特定错误"))
	nonMatchingErr := FromError(errors.New("其他错误"))
	
	require.True(t, customRepresents.Represents(matchingErr), "应该识别出匹配的错误")
	require.False(t, customRepresents.Represents(nonMatchingErr), "不应识别出不匹配的错误")
}

// 测试网络临时错误检测
func TestIsNetworkTemporaryErr(t *testing.T) {
	// 创建一个超时错误
	timeoutErr := os.NewSyscallError("read", os.ErrDeadlineExceeded)
	err := FromError(timeoutErr)
	
	// 验证识别为临时网络错误
	require.True(t, isNetworkTemporaryErr(err), "超时错误应被识别为临时网络错误")
	
	
	// 创建非网络错误
	nonNetworkErr := errors.New("非网络错误")
	err = FromError(nonNetworkErr)
	
	// 验证不识别为临时网络错误
	require.False(t, isNetworkTemporaryErr(err), "非网络错误不应被识别为临时网络错误")
}

// 测试网络永久错误检测
func TestIsNetworkPermanentErr(t *testing.T) {
	// 创建各种永久网络错误
	noHostErr := errors.New("no such host")
	noAddressErr := errors.New("no address found for host example.com")
	connectionRefusedErr := errors.New("connect: connection refused")
	
	// 验证识别为永久网络错误
	require.True(t, isNetworkPermanentErr(FromError(noHostErr)), "找不到主机应被识别为永久网络错误")
	require.True(t, isNetworkPermanentErr(FromError(noAddressErr)), "找不到地址应被识别为永久网络错误")
	require.True(t, isNetworkPermanentErr(FromError(connectionRefusedErr)), "连接被拒绝应被识别为永久网络错误")
	
	// 创建非永久网络错误
	nonPermanentErr := errors.New("临时连接问题")
	
	// 验证不识别为永久网络错误
	require.False(t, isNetworkPermanentErr(FromError(nonPermanentErr)), "临时问题不应被识别为永久网络错误")
}

// 测试截止时间错误检测
func TestIsDeadlineErr(t *testing.T) {
	// 创建操作系统截止时间错误
	osDeadlineErr := os.ErrDeadlineExceeded
	
	// 创建上下文截止时间错误
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Millisecond)
	defer cancel()
	time.Sleep(2 * time.Millisecond)
	ctxDeadlineErr := ctx.Err() // 应该是context.DeadlineExceeded
	
	// 验证识别为截止时间错误
	require.True(t, isDeadlineErr(FromError(osDeadlineErr)), "操作系统截止时间错误应被识别")
	require.True(t, isDeadlineErr(FromError(ctxDeadlineErr)), "上下文截止时间错误应被识别")
	
	// 创建非截止时间错误
	nonDeadlineErr := errors.New("非截止时间错误")
	
	// 验证不识别为截止时间错误
	require.False(t, isDeadlineErr(FromError(nonDeadlineErr)), "非截止时间错误不应被识别")
}

// 测试复合错误类型
func TestMultiKind(t *testing.T) {
	// 创建两个基本错误类型
	kind1 := NewPrimitiveErrKind("kind1", "类型1", nil)
	kind2 := NewPrimitiveErrKind("kind2", "类型2", nil)
	
	// 使用CombineErrKinds创建复合类型
	multiKind := CombineErrKinds(kind1, kind2)
	
	// 验证String方法
	require.Contains(t, multiKind.String(), "kind1", "复合类型字符串表示应包含kind1")
	require.Contains(t, multiKind.String(), "kind2", "复合类型字符串表示应包含kind2")
	
	// 验证Description方法
	require.Contains(t, multiKind.Description(), "类型1", "复合类型描述应包含类型1")
	require.Contains(t, multiKind.Description(), "类型2", "复合类型描述应包含类型2")
	
	// 验证Is方法
	require.True(t, multiKind.Is(kind1), "复合类型应包含kind1")
	require.True(t, multiKind.Is(kind2), "复合类型应包含kind2")
	require.False(t, multiKind.Is(ErrKindUnknown), "复合类型不应包含未知类型")
}

// 测试CombineErrKinds函数
func TestCombineErrKinds(t *testing.T) {
	// 测试单个类型的情况
	singleKind := CombineErrKinds(ErrKindNetworkTemporary)
	require.Equal(t, ErrKindNetworkTemporary, singleKind, "单个类型应直接返回该类型")
	
	// 测试包含nil和空字符串的情况
	nilKind := NewPrimitiveErrKind("", "", nil)
	combinedKind := CombineErrKinds(ErrKindNetworkTemporary, nil, nilKind)
	require.Equal(t, ErrKindNetworkTemporary, combinedKind, "应忽略nil和空ID的类型")
	
	// 测试超过最大深度的情况
	originalDepth := MaxErrorDepth
	MaxErrorDepth = 2
	defer func() { MaxErrorDepth = originalDepth }()
	
	kind1 := NewPrimitiveErrKind("kind1", "类型1", nil)
	kind2 := NewPrimitiveErrKind("kind2", "类型2", nil)
	kind3 := NewPrimitiveErrKind("kind3", "类型3", nil)
	
	limitedKind := CombineErrKinds(kind1, kind2, kind3).(*multiKind)
	require.Len(t, limitedKind.kinds, 2, "应限制为最大深度")
}

// 测试GetErrorKind函数
func TestGetErrorKind(t *testing.T) {
	// 创建一个带有错误类型的错误
	err := New("测试错误").SetKind(ErrKindNetworkTemporary)
	
	// 验证GetErrorKind返回预期类型
	kind := GetErrorKind(err)
	require.Equal(t, ErrKindNetworkTemporary, kind, "应返回设置的错误类型")

}

// 测试GetAllErrorKinds函数
func TestGetAllErrorKinds(t *testing.T) {
	// 创建一个带有复合错误类型的错误
	kind1 := NewPrimitiveErrKind("kind1", "类型1", nil)
	kind2 := NewPrimitiveErrKind("kind2", "类型2", nil)
	multiKind := CombineErrKinds(kind1, kind2)
	
	err := New("测试错误").SetKind(multiKind)
	
	// 验证GetAllErrorKinds返回所有类型
	kinds := GetAllErrorKinds(err)
	require.Len(t, kinds, 2, "应返回两个错误类型")
	require.Contains(t, kinds, kind1, "返回的类型应包含kind1")
	require.Contains(t, kinds, kind2, "返回的类型应包含kind2")
	
	// 测试无类型的错误返回未知类型
	noKindErr := errors.New("无类型错误")
	kinds = GetAllErrorKinds(noKindErr)
	require.Len(t, kinds, 1, "无类型错误应返回一个类型")
	require.Equal(t, ErrKindUnknown, kinds[0], "无类型错误应返回未知类型")
} 