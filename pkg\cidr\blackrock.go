// Package cidr 提供了IP地址和CIDR处理的工具函数及相关算法
package cidr

import (
	"math" // 导入math包，用于数学运算
)

// BlackRock 密码器的实现，源自masscan项目
// BlackRock是一个确定性随机数生成器，用于生成随机但可重现的IP地址排列
// 它使用Feistel网络结构实现数据的可逆混淆，常用于网络扫描中对IP地址进行随机化排列
type BlackRock struct {
	Rounds int64 // Feistel网络的轮数，增加轮数提高复杂度
	Seed   int64 // 随机种子，决定生成序列的起点
	Range  int64 // 排列范围，即最大可生成的整数值
	A      int64 // 内部参数A，用于分割范围
	B      int64 // 内部参数B，A和B共同确保能覆盖整个Range范围
}

// New 创建一个新的BlackRock密码器实例
//
// 参数:
//   - rangez: 排列的范围，指定可生成的最大整数值
//   - seed: 随机种子，相同的种子产生相同的排列序列
//
// 返回值:
//   - *BlackRock: 初始化后的BlackRock密码器实例
//
// 函数的主要功能是初始化BlackRock密码器，设置合适的内部参数以确保
// 生成的随机序列覆盖整个指定范围，同时具有良好的随机性和可逆性。
func New(rangez, seed int64) *BlackRock {
	// 计算范围的平方根，用于设置参数A和B
	split := int64(math.Floor(math.Sqrt(float64(rangez))))

	// 创建BlackRock实例并初始化基本参数
	var blackrock BlackRock
	blackrock.Rounds = 3     // 设置默认轮数为3，这是经验值，平衡安全性和性能
	blackrock.Seed = seed    // 设置随机种子
	blackrock.Range = rangez // 设置排列范围
	blackrock.A = split - 1  // 设置参数A为平方根向下取整减1
	blackrock.B = split + 1  // 设置参数B为平方根向上取整加1

	// 确保A至少为1，防止除零错误
	if blackrock.A <= 0 {
		blackrock.A = 1
	}

	// 调整B的值，确保A*B大于range，这样能覆盖整个范围
	for blackrock.A*blackrock.B <= rangez {
		blackrock.B++
	}

	// 返回初始化好的BlackRock实例
	return &blackrock
}

// F 是Feistel网络的内部排列函数
//
// 参数:
//   - j: 当前轮数索引，用于选择不同的素数
//   - r: 输入值，通常是当前块的值
//   - seed: 随机种子，用于增加随机性
//
// 返回值:
//   - int64: 经过混淆后的输出值
//
// 函数的主要功能是对输入值进行非线性变换，是Feistel网络的核心组件。
// 它通过素数乘法、位操作和异或操作来生成具有良好扩散性的输出。
func (blackrock *BlackRock) F(j, r, seed int64) int64 {
	// 预定义的素数列表，用于增强混淆效果
	var primes = []int64{961752031, 982324657, 15485843, 961752031}

	// 对输入值r进行位操作和加法操作，结合种子值
	r = (r << (r & 0x4)) + r + seed

	// 使用选定的素数、异或运算和额外的算术操作创建最终的混淆值
	// math.Abs确保结果为正数
	return int64(math.Abs(float64((((primes[j]*r + 25) ^ r) + j))))
}

// Fe 实现Feistel网络的前向操作
//
// 参数:
//   - r: 轮数，指定Feistel网络执行的轮数
//   - a: 分割参数A，用于模运算
//   - b: 分割参数B，用于模运算
//   - m: 要加密的输入值
//   - seed: 随机种子
//
// 返回值:
//   - int64: 经过Feistel网络加密后的值
//
// 函数的主要功能是通过Feistel网络结构对输入值m进行加密变换。
// Feistel网络将输入分为左右两部分，然后通过多轮迭代，使用F函数
// 对数据进行混淆，实现可逆的加密效果。
func (blackrock *BlackRock) Fe(r, a, b, m, seed int64) int64 {
	var (
		L, R int64 // 左右两部分，Feistel网络的基本结构
		j    int64 // 轮数计数器
		tmp  int64 // 临时变量，存储中间计算结果
	)

	// 将输入m分割为左右两部分
	L = m % a // L为m除以a的余数
	R = m / a // R为m除以a的商

	// 执行r轮Feistel网络变换
	for j = 1; j <= r; j++ {
		if j&1 == 1 {
			// 奇数轮使用参数a进行模运算
			tmp = (L + blackrock.F(j, R, seed)) % a
		} else {
			// 偶数轮使用参数b进行模运算
			tmp = (L + blackrock.F(j, R, seed)) % b
		}
		// 交换L和R，这是Feistel网络的标准操作
		L = R
		R = tmp
	}

	// 根据总轮数的奇偶性，决定最终的输出格式
	if r&1 == 1 {
		// 奇数轮返回a*L + R
		return a*L + R
	}
	// 偶数轮返回a*R + L
	return a*R + L
}

// Unfe 实现Feistel网络的逆向操作
//
// 参数:
//   - r: 轮数，指定Feistel网络执行的轮数
//   - a: 分割参数A，用于模运算
//   - b: 分割参数B，用于模运算
//   - m: 要解密的输入值
//   - seed: 随机种子
//
// 返回值:
//   - int64: 经过Feistel网络解密后的原始值
//
// 函数的主要功能是对通过Fe函数加密的值进行解密，恢复原始输入值。
// 它实现了Feistel网络的逆向操作，按照相反的顺序执行轮函数，
// 并使用适当的模运算恢复原始数据。
func (blackrock *BlackRock) Unfe(r, a, b, m, seed int64) int64 {
	var (
		L, R int64 // 左右两部分，Feistel网络的基本结构
		j    int64 // 轮数计数器
		tmp  int64 // 临时变量，存储中间计算结果
	)

	// 根据总轮数的奇偶性，决定如何从输入m恢复L和R的初始值
	if r&1 == 1 {
		// 奇数轮，R是m除以a的余数，L是m除以a的商
		R = m % a
		L = m / a
	} else {
		// 偶数轮，L是m除以a的余数，R是m除以a的商
		L = m % a
		R = m / a
	}

	// 从最后一轮开始，逆向执行Feistel网络变换
	for j = r; j >= 1; j-- {
		if j&1 == 1 {
			// 奇数轮使用参数a进行模运算
			tmp = blackrock.F(j, L, seed)
			if tmp > R {
				// 处理模运算中的借位情况
				tmp -= -R
				tmp = a - (tmp % a)
				if tmp == a {
					tmp = 0
				}
			} else {
				// 正常情况下的逆向计算
				tmp = R - tmp
				tmp %= a
			}
		} else {
			// 偶数轮使用参数b进行模运算
			tmp = blackrock.F(j, L, seed)
			if tmp > R {
				// 处理模运算中的借位情况
				tmp = (tmp - R)
				tmp = b - (tmp % b)
				if tmp == b {
					tmp = 0
				}
			} else {
				// 正常情况下的逆向计算
				tmp = R - tmp
				tmp %= b
			}
		}
		// 交换R和L，这是逆向Feistel网络的标准操作
		R = L
		L = tmp
	}

	// 重新组合L和R，得到原始输入值
	return a*R + L
}

// Shuffle 对输入值进行随机化混淆
//
// 参数:
//   - m: 要混淆的输入值
//
// 返回值:
//   - int64: 混淆后的随机值，范围在[0, Range)之间
//
// 函数的主要功能是将输入值m转换为一个看似随机的值，
// 但这种随机化是确定性的，可以通过UnShuffle函数恢复。
// 如果生成的值超出有效范围，会重复应用Fe函数直到得到有效值。
func (blackrock *BlackRock) Shuffle(m int64) int64 {
	// 使用Fe函数对输入值m进行混淆
	c := blackrock.Fe(blackrock.Rounds, blackrock.A, blackrock.B, m, blackrock.Seed)

	// 如果生成的值超出了指定范围，继续应用Fe函数
	// 这确保最终结果在[0, Range)范围内
	for c >= blackrock.Range {
		c = blackrock.Fe(blackrock.Rounds, blackrock.A, blackrock.B, c, blackrock.Seed)
	}

	return c
}

// UnShuffle 恢复被Shuffle函数混淆的值
//
// 参数:
//   - m: 被混淆的值
//
// 返回值:
//   - int64: 恢复后的原始值
//
// 函数的主要功能是将通过Shuffle函数混淆的值恢复为原始输入值。
// 它使用Unfe函数实现逆向操作，对于超出范围的值会重复应用Unfe，
// 以确保正确恢复。
func (blackrock *BlackRock) UnShuffle(m int64) int64 {
	// 使用Unfe函数对混淆值m进行逆向操作
	c := blackrock.Unfe(blackrock.Rounds, blackrock.A, blackrock.B, m, blackrock.Seed)

	// 如果逆向计算的值超出了范围，继续应用Unfe函数
	// 这确保能正确恢复原始输入值
	for c >= blackrock.Range {
		c = blackrock.Unfe(blackrock.Rounds, blackrock.A, blackrock.B, c, blackrock.Seed)
	}

	return c
}
