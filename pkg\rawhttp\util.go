// Author: chenjb
// Version: V1.0
// Date: 2025-07-22 19:10:45
// FilePath: /yaml_scan/pkg/rawhttp/util.go
// Description: rawhttp工具函数模块，提供HTTP请求/响应转换、头部处理等辅助功能

// Package rawhttp 工具函数模块
package rawhttp

import (
	"bytes"                           // 字节缓冲区包
	"compress/gzip"                   // Gzip压缩包
	"fmt"                             // 格式化输出包
	"io"                              // 输入输出接口包
	"net/http"                        // 标准HTTP包
	"strings"                         // 字符串处理包
	"yaml_scan/pkg/rawhttp/client"    // HTTP客户端包
	urlutil "yaml_scan/utils/url"     // URL工具包
)

// StatusError HTTP状态错误对象
// 用于表示HTTP状态相关的错误信息
type StatusError struct {
	client.Status // 嵌入客户端状态结构体
}

// Error 实现error接口，返回状态错误的字符串表示
// 返回:
//   string: 状态错误的字符串描述
// 功能: 将HTTP状态转换为可读的错误信息
func (s *StatusError) Error() string {
	return s.Status.String() // 返回状态的字符串表示
}

// readCloser 组合了Reader和Closer接口的结构体
// 用于创建同时支持读取和关闭操作的对象
type readCloser struct {
	io.Reader // 读取接口
	io.Closer // 关闭接口
}

// toRequest 将HTTP参数转换为客户端请求对象
// 参数:
//   method: HTTP请求方法
//   path: 请求路径
//   query: 查询参数数组
//   headers: HTTP头部映射
//   body: 请求体读取器
//   options: 请求选项配置
// 返回:
//   *client.Request: 客户端请求对象
// 功能: 构建完整的HTTP请求对象，支持自定义原始字节和头部
func toRequest(method string, path string, query []string, headers map[string][]string, body io.Reader, options *Options) *client.Request {
	// 如果提供了自定义原始字节，直接使用
	if len(options.CustomRawBytes) > 0 {
		return &client.Request{RawBytes: options.CustomRawBytes}
	}
	// 转换头部格式
	reqHeaders := toHeaders(headers)
	// 如果有自定义头部，使用自定义头部
	if len(options.CustomHeaders) > 0 {
		reqHeaders = options.CustomHeaders
	}

	// 构建并返回请求对象
	return &client.Request{
		Method:  method,           // 设置请求方法
		Path:    path,             // 设置请求路径
		Query:   query,            // 设置查询参数
		Version: client.HTTP_1_1,  // 设置HTTP版本为1.1
		Headers: reqHeaders,       // 设置请求头部
		Body:    body,             // 设置请求体
	}
}

// MaxResponseReadSizeDecompress 解压缩响应的最大读取大小（10MB）
// 用于限制gzip解压缩时的内存使用，防止内存耗尽攻击
const MaxResponseReadSizeDecompress = 10 * 1024 * 1024

// toHTTPResponse 将客户端响应转换为标准HTTP响应
// 参数:
//   conn: 网络连接对象
//   resp: 客户端响应对象
// 返回:
//   *http.Response: 标准HTTP响应对象
//   error: 错误信息，转换成功时为nil
// 功能: 处理响应转换，包括gzip解压缩和头部转换
func toHTTPResponse(conn Conn, resp *client.Response) (*http.Response, error) {
	rheaders := fromHeaders(resp.Headers) // 转换头部格式
	// 构建标准HTTP响应对象
	r := http.Response{
		ProtoMinor:    resp.Version.Minor,   // 设置协议次版本号
		ProtoMajor:    resp.Version.Major,   // 设置协议主版本号
		Status:        resp.Status.String(), // 设置状态字符串
		StatusCode:    resp.Status.Code,     // 设置状态码
		Header:        rheaders,             // 设置响应头部
		ContentLength: resp.ContentLength(), // 设置内容长度
	}

	var err error
	rbody := resp.Body // 获取响应体
	// 如果响应使用gzip压缩，进行解压缩
	if headerValue(rheaders, "Content-Encoding") == "gzip" {
		rbody, err = gzip.NewReader(rbody) // 创建gzip读取器
		if err != nil {
			return nil, err // 解压缩失败返回错误
		}
		// 限制读取大小以防止内存耗尽
		limitReader := io.LimitReader(rbody, MaxResponseReadSizeDecompress)
		rbody = limitReader
	}
	// 创建组合了读取器和连接关闭器的对象
	rc := &readCloser{rbody, conn}

	r.Body = rc // 设置响应体

	return &r, nil // 返回转换后的HTTP响应
}

// toHeaders 将映射格式的头部转换为客户端头部数组
// 参数:
//   h: 头部映射，键为头部名称，值为头部值数组
// 返回:
//   []client.Header: 客户端头部数组
// 功能: 将标准头部格式转换为客户端内部使用的头部格式
func toHeaders(h map[string][]string) []client.Header {
	var r []client.Header
	// 遍历头部映射
	for k, v := range h {
		// 为每个头部值创建一个Header对象
		for _, v := range v {
			r = append(r, client.Header{Key: k, Value: v})
		}
	}
	return r // 返回头部数组
}

// fromHeaders 将客户端头部数组转换为映射格式
// 参数:
//   h: 客户端头部数组
// 返回:
//   map[string][]string: 头部映射
// 功能: 将客户端内部头部格式转换为标准头部映射格式
func fromHeaders(h []client.Header) map[string][]string {
	if h == nil {
		return nil // 如果输入为nil，返回nil
	}
	var r = make(map[string][]string) // 创建头部映射
	// 遍历头部数组
	for _, hh := range h {
		// 将头部值添加到对应键的数组中
		r[hh.Key] = append(r[hh.Key], hh.Value)
	}
	return r // 返回头部映射
}

// headerValue 获取指定头部的值，多个值用空格连接
// 参数:
//   headers: 头部映射
//   key: 头部名称
// 返回:
//   string: 头部值字符串
// 功能: 从头部映射中提取指定头部的值并连接为字符串
func headerValue(headers map[string][]string, key string) string {
	return strings.Join(headers[key], " ") // 用空格连接多个头部值
}

// firstErr 返回第一个非nil的错误
// 参数:
//   err1: 第一个错误
//   err2: 第二个错误
// 返回:
//   error: 第一个非nil的错误，如果都为nil则返回nil
// 功能: 用于错误处理，优先返回第一个错误
func firstErr(err1, err2 error) error {
	if err1 != nil {
		return err1 // 如果第一个错误不为nil，返回第一个错误
	}
	if err2 != nil {
		return err2 // 如果第二个错误不为nil，返回第二个错误
	}
	return nil // 如果都为nil，返回nil
}

// DumpRequestRaw 将HTTP请求转储为原始字节数组
// 参数:
//   method: HTTP请求方法
//   url: 目标URL地址
//   uripath: 自定义URI路径
//   headers: HTTP头部映射
//   body: 请求体读取器
//   options: 请求选项配置
// 返回:
//   []byte: 原始HTTP请求的字节表示
//   error: 错误信息，转储成功时为nil
// 功能: 生成完整的原始HTTP请求字符串，包括请求行、头部和请求体
func DumpRequestRaw(method, url, uripath string, headers map[string][]string, body io.Reader, options *Options) ([]byte, error) {
	// 如果提供了自定义原始字节，直接返回
	if len(options.CustomRawBytes) > 0 {
		return options.CustomRawBytes, nil
	}
	// 如果headers为nil，初始化为空映射
	if headers == nil {
		headers = make(map[string][]string)
	}
	// 解析URL获取各个组件
	u, err := urlutil.ParseURL(url, true)
	if err != nil {
		return nil, err // URL解析失败返回错误
	}

	// Handle only if host header is missing - 仅在缺少Host头部时处理
	_, hasHostHeader := headers["Host"]
	if !hasHostHeader {
		host := u.Host                    // 获取主机地址
		headers["Host"] = []string{host}  // 添加Host头部
	}

	// standard path - 构建标准路径
	path := u.Path
	if path == "" {
		path = "/" // 如果路径为空，设置为根路径
	}
	// 如果存在查询参数，添加到路径中
	if !u.Params.IsEmpty() {
		path += "?" + u.Params.Encode()
	}
	// override if custom one is specified - 如果指定了自定义路径，则覆盖
	if uripath != "" {
		path = uripath
	}

	// 构建请求对象
	req := toRequest(method, path, nil, headers, body, options)
	b := strings.Builder{} // 创建字符串构建器

	// 构建查询字符串
	q := strings.Join(req.Query, "&")
	if len(q) > 0 {
		q = "?" + q // 添加查询字符串前缀
	}

	// 写入请求行：方法 路径 版本
	b.WriteString(fmt.Sprintf("%s %s%s %s"+client.NewLine, req.Method, req.Path, q, req.Version.String()))

	// 写入所有请求头部
	for _, header := range req.Headers {
		if header.Value != "" {
			// 有值的头部：键: 值
			b.WriteString(fmt.Sprintf("%s: %s"+client.NewLine, header.Key, header.Value))
		} else {
			// 无值的头部：仅键
			b.WriteString(fmt.Sprintf("%s"+client.NewLine, header.Key))
		}
	}

	// 如果启用自动内容长度计算，添加Content-Length头部
	l := req.ContentLength()
	if req.AutomaticContentLength && l >= 0 {
		b.WriteString(fmt.Sprintf("Content-Length: %d"+client.NewLine, l))
	}

	// 添加空行分隔头部和请求体
	b.WriteString(client.NewLine)

	// 如果有请求体，读取并添加到输出中
	if req.Body != nil {
		var buf bytes.Buffer
		tee := io.TeeReader(req.Body, &buf) // 创建TeeReader以便读取请求体
		body, err := io.ReadAll(tee)        // 读取所有请求体数据
		if err != nil {
			return nil, err // 读取失败返回错误
		}
		b.Write(body) // 写入请求体数据
	}

	// 返回最终的原始HTTP请求字节数组，确保使用正确的换行符
	return []byte(strings.ReplaceAll(b.String(), "\n", client.NewLine)), nil
}

