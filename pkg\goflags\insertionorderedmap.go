//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-21 16:21:38
//FilePath: /yaml_scan/pkg/goflags/insertionorderedmap.go
//Description:

package goflags

// InsertionOrderedMap 结构体用于实现一个保持插入顺序的映射
// 它包含一个值为 FlagData 指针的映射和一个存储插入顺序的键的切片。
type InsertionOrderedMap struct {
	values map[string]*FlagData // 存储键值对，键为字符串，值为指向 FlagData 的指针
	keys   []string             `yaml:"-"` // 存储插入顺序的键 这个标签表示在序列化为 YAML 时，keys 字段将被忽略。这意味着在将 InsertionOrderedMap 转换为 YAML 格式时，不会包含 keys 字段
}

// newInsertionOrderedMap 创建并返回一个新的 InsertionOrderedMap 实例
// 它初始化 values 字典以存储键值对。
func newInsertionOrderedMap() InsertionOrderedMap {
	return InsertionOrderedMap{values: make(map[string]*FlagData)}
}

// Set 方法将指定的键值对添加到 InsertionOrderedMap 中。
// 如果键已经存在，则更新其值；如果键不存在，则将其添加到 keys 切片中以保持插入顺序。
//
// key: 要添加或更新的键。
// value: 与键关联的 FlagData 指针。
func (insertionOrderedMap *InsertionOrderedMap) Set(key string, value *FlagData) {
	// 检查键是否已经存在于映射中
	_, present := insertionOrderedMap.values[key]
	// 更新或添加键值对
	insertionOrderedMap.values[key] = value
	// 如果键不存在，则将其添加到 keys 切片中
	if !present {
		insertionOrderedMap.keys = append(insertionOrderedMap.keys, key)
	}
}

// forEach:对 InsertionOrderedMap 中的每个键值对执行指定的函数。
//
//	@receiver insertionOrderedMap *InsertionOrderedMap:
//	@param fn func(key string, data *FlagData): 一个函数，接受键和与之关联的 FlagData 指针。
func (insertionOrderedMap *InsertionOrderedMap) forEach(fn func(key string, data *FlagData)) {
	for _, key := range insertionOrderedMap.keys {
		fn(key, insertionOrderedMap.values[key])
	}
}
