// Author: chenjb
// Version: V1.0
// Date: 2025-06-24 17:02:13
// FilePath: /yaml_scan/pkg/tlsx/tlsx/clients/options.go
// Description:
package clients

import (
	"crypto/x509"
	"github.com/cloudflare/cfssl/revoke"
	zx509 "github.com/zmap/zcrypto/x509"
	"yaml_scan/pkg/fastdialer"
	"yaml_scan/pkg/goflags"
	"yaml_scan/pkg/gologger"
)

// Options 包含tlsx客户端的配置选项
type Options struct {
	// OutputFile 是写入输出的文件路径
	OutputFile string
	// Inputs 是要处理的输入列表
	Inputs goflags.StringSlice
	// InputList 是要处理的输入列表文件
	InputList string
	// ServerName 是TLS连接的可选服务器名称（SNI）
	ServerName goflags.StringSlice
	// RandomForEmptyServerName 在SNI为空时使用随机值
	RandomForEmptyServerName bool
	// ReversePtrSNI 执行反向PTR查询，从IP获取SNI
	ReversePtrSNI bool
	// Verbose 启用详细输出显示
	Verbose bool
	// Version 显示程序版本
	Version bool
	// JSON 启用JSON格式输出
	JSON bool
	// DisplayDns 启用从SSL证书响应中显示唯一主机名
	DisplayDns bool
	// TLSChain 启用TLS链信息的输出
	TLSChain bool
	// Deprecated: AllCiphers 为了历史兼容性而存在，不应使用
	// AllCiphers bool
	// ProbeStatus 在JSON输出中包含错误信息
	ProbeStatus bool
	// CertsOnly 使用ztls标志启用提前SSL终止
	CertsOnly bool
	// RespOnly 在CLI输出中仅显示TLS响应
	RespOnly bool
	// Silent 启用静默输出显示
	Silent bool
	// NoColor 禁用CLI输出的彩色显示
	NoColor bool
	// Retries 是TLS连接的重试次数
	Retries int
	// Timeout 是等待连接的秒数
	Timeout int
	// Concurrency 是并发处理的线程数
	Concurrency int
	// Delay 是每个线程中请求之间的等待时间
	Delay string
	// Port 是要请求的端口
	Ports goflags.StringSlice
	// Ciphers 是用于连接的自定义密码套件列表
	Ciphers goflags.StringSlice
	// CACertificate 是连接的CA证书
	CACertificate string
	// MinVersion 是可接受的最低TLS版本
	MinVersion string
	// MaxVersion 是可接受的最高TLS版本
	MaxVersion string
	// Resolvers 包含tlsx客户端的自定义解析器
	Resolvers goflags.StringSlice
	// ScanMode 是要使用的TLS连接模式
	ScanMode string
	// VerifyServerCertificate 启用对服务器证书的可选验证
	VerifyServerCertificate bool
	// OpenSSL二进制路径
	OpenSSLBinary string
	// SAN 显示主题备用名称
	SAN bool
	// CN 显示主题通用名称
	CN bool
	// SO 显示主题组织名称
	SO bool
	// TLSVersion 显示使用的TLS版本
	TLSVersion bool
	// Cipher 显示使用的密码套件
	Cipher bool
	// Expired 显示TLS证书的有效性
	Expired bool
	// SelfSigned 显示证书是否自签名
	SelfSigned bool
	// Untrusted 显示证书是否不受信任
	Untrusted bool
	// MisMatched 显示证书是否不匹配
	MisMatched bool
	// Revoked 显示证书是否已吊销
	Revoked bool
	// HardFail 定义解析失败或其他错误时的吊销状态
	// 如果HardFail为true，则在任何错误下证书都被视为已吊销
	HardFail bool
	// Hash 是要显示的证书哈希
	Hash string
	// Jarm 使用多个探针计算jarm指纹
	Jarm bool
	// Cert 以PEM格式显示证书
	Cert bool
	// Ja3 显示ja3指纹哈希
	Ja3 bool
	// Ja3s 显示ja3s指纹哈希
	Ja3s bool
	// ScanAllIPs 扫描所有IP
	ScanAllIPs bool
	// IPVersion 使用的IP版本
	IPVersion goflags.StringSlice
	// WildcardCertCheck 启用通配符证书检查
	WildcardCertCheck bool
	// TlsVersionsEnum 枚举支持的TLS版本
	TlsVersionsEnum bool
	// TlsCiphersEnum 枚举每个TLS协议支持的密码套件
	TlsCiphersEnum bool
	// TLsCipherLevel TLS密码套件安全级别
	TLsCipherLevel []string
	// ClientHello 包含客户端hello（仅ztls）
	ClientHello bool
	// ServerHello 包含服务器hello（仅ztls）
	ServerHello bool
	// HealthCheck 执行能力健康检查
	HealthCheck bool
	// DisableUpdateCheck 禁用更新检查
	DisableUpdateCheck bool
	// CipherConcurrency 密码套件枚举的并发数
	CipherConcurrency int

	// Fastdialer 是fastdialer拨号器实例
	Fastdialer *fastdialer.Dialer
	// Serial 显示证书序列号
	Serial bool
}

// IsTLSRevoked returns true if the certificate has been revoked or failed to parse
func IsTLSRevoked(options *Options, cert *x509.Certificate) bool {
	if cert == nil {
		return options.HardFail
	}
	// - false, false: an error was encountered while checking revocations.
	// - false, true:  the certificate was checked successfully, and it is not revoked.
	// - true, true:   the certificate was checked successfully, and it is revoked.
	// - true, false:  failure to check revocation status causes verification to fail
	revoked, _ := revoke.VerifyCertificate(cert)
	return revoked
}

// IsZTLSRevoked returns true if the certificate has been revoked
func IsZTLSRevoked(options *Options, cert *zx509.Certificate) bool {
	xcert, err := x509.ParseCertificate(cert.Raw)
	if err != nil {
		gologger.Debug().Msgf("ztls: failed to convert zx509->x509 while checking revocation status: %v", err)
		return options.HardFail
	}
	return IsTLSRevoked(options, xcert)
}

// EnumMode 是枚举模式类型
type EnumMode uint

const (
	None EnumMode = iota    // 无枚举
	Version                 // 枚举TLS版本
	Cipher                  // 枚举密码套件
)

// ConnectOptions 包含TLS连接的选项
type ConnectOptions struct {
	// SNI 是TLS服务器名称指示（Server Name Indication）
	SNI string
	// VersionTLS 是要使用的TLS版本
	VersionTLS string
	// Ciphers 是要使用的密码套件列表
	Ciphers []string
	// CipherLevel 是密码套件安全级别列表，仅在密码套件枚举模式下使用
	CipherLevel []CipherSecLevel
	// EnumMode 是枚举模式（版本或密码套件）
	EnumMode EnumMode
}
