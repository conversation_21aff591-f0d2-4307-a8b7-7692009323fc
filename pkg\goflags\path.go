//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-21 20:08:13
//FilePath: /yaml_scan/pkg/goflags/path.go
//Description:

package goflags

import (
	"os"
	"path/filepath"
	"strings"
	folderutil "yaml_scan/utils/folder"
)

// getToolName: 获取当前工具的名称，去掉文件扩展名。
//
//	@return string string: 工具的名称，去掉了文件扩展名。
func getToolName() string {
	appName := filepath.Base(os.Args[0])
	return strings.TrimSuffix(appName, filepath.Ext(appName))
}

// GetConfigFilePath: 获取配置文件的路径。
//
//	@receiver flagSet *FlagSet:
//	@return string string: 配置文件的路径。
//	@return error error: 如果发生错误，返回相应的错误信息。
func (flagSet *FlagSet) GetConfigFilePath() (string, error) {
	// 如果 configFilePath 已经设置，直接返回
	if flagSet.configFilePath != "" {
		return flagSet.configFilePath, nil
	}
	// 构建配置文件路径，默认使用当前目录和工具名称
	return filepath.Join(folderutil.AppConfigDirOrDefault(".", getToolName()), "config.yaml"), nil
}
