
// Author: chenjb
// Version: V1.0
// Date: 2025-05-20 19:26:20
// FilePath: /yaml_scan/utils/errkit/interfaces.go
// Description:定义了错误处理相关的接口和类型检查，为错误处理提供了标准化的接口定义
package errkit

import "encoding/json"

// 类型检查声明，确保ErrorX实现了以下接口
var (
	// 确保ErrorX实现了json.Marshaler接口
	_ json.Marshaler  = &ErrorX{}
	// 确保ErrorX实现了JoinedError接口
	_ JoinedError     = &ErrorX{}
	// 确保ErrorX实现了CauseError接口
	_ CauseError      = &ErrorX{}
	// 确保ErrorX实现了ComparableError接口
	_ ComparableError = &ErrorX{}
	// 确保ErrorX实现了error接口
	_ error           = &ErrorX{}
)	

// JoinedError 是由Join方法合并的错误实现的接口
// 用于表示一个错误包含多个底层错误
type JoinedError interface {
	// Unwrap 返回底层的错误数组
	// 通过此方法可以获取被合并的所有原始错误
	Unwrap() []error
}

// CauseError 是由有原因的错误实现的接口
// 用于表示一个错误是由另一个错误引起的
type CauseError interface {
	// Cause 返回导致此错误的原始错误，不包含任何包装
	// 用于直接获取最根本的错误原因
	Cause() error
}

// ComparableError 是由可比较的错误实现的接口
// 用于检查错误之间的关系
type ComparableError interface {
	// Is 检查当前错误是否包含给定的错误
	// 用于错误类型的比较和匹配
	Is(err error) bool
}

// WrappedError 是由被包装的错误实现的接口
// 用于表示一个错误被另一个错误包装
type WrappedError interface {
	// Unwrap 返回底层的错误
	// 与JoinedError不同，这里只返回单个错误
	Unwrap() error
}