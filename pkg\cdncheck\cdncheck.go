// Author: chenjb
// Version: V1.0
// Date: 2025-06-19 15:51:40
// FilePath: /yaml_scan/pkg/cdncheck/cdncheck.go
// Description: CDN检测包，用于识别IP是否属于CDN、WAF或云服务提供商
package cdncheck

import (
	"net"
	"strings"
	"sync"
	"yaml_scan/pkg/retryabledns"
)

// DefaultResolvers 默认的可信DNS解析服务器列表（从fastdialer获取）
var DefaultResolvers = []string{
	"*******:53",
	"*******:53",
	"*******:53",
	"*******:53",
}

var (
	// DefaultCDNProviders  存储所有默认CDN提供商名称，逗号分隔
	DefaultCDNProviders string
	// DefaultWafProviders 存储所有默认WAF提供商名称，逗号分隔
	DefaultWafProviders string
	// DefaultCloudProviders 存储所有默认云服务提供商名称，逗号分隔
	DefaultCloudProviders string
)

// Client 用于检查IP是否属于CDN、WAF或云服务提供商
// 这些IP通常应在扫描过程中排除，因为它们属于第三方防火墙
type Client struct {
	sync.Once                         // 嵌入Once以确保某些初始化操作只执行一次
	cdn          *providerScraper     // CDN提供商检测器
	waf          *providerScraper     // WAF提供商检测器
	cloud        *providerScraper     // 云服务提供商检测器
	retriabledns *retryabledns.Client // 带重试功能的DNS客户端
}

// New 创建一个具有默认选项的cdncheck客户端
// @return *Client *Client: 
func New() *Client {
	client, _ := NewWithOpts(3, []string{})
	return client
}

// NewWithOpts 创建一个具有自定义选项的cdncheck客户端
// @param MaxRetries int: 最大DNS解析重试次数，如果小于等于0则使用默认值3
// @param resolvers []string: 自定义DNS解析服务器列表，如果为空则使用DefaultResolvers
//
// @return *Client *Client: 初始化后的CDN检查客户端	
// @return error error:  初始化过程中可能出现的错误
func NewWithOpts(MaxRetries int, resolvers []string) (*Client, error) {
	if MaxRetries <= 0 {
		MaxRetries = 3
	}
	if len(resolvers) == 0 {
		resolvers = DefaultResolvers
	}
	retryabledns, err := retryabledns.New(resolvers, MaxRetries)
	if err != nil {
		return nil, err
	}
	client := &Client{
		cdn:          newProviderScraper(generatedData.CDN),
		waf:          newProviderScraper(generatedData.WAF),
		cloud:        newProviderScraper(generatedData.Cloud),
		retriabledns: retryabledns,
	}
	return client, nil
}

// CheckCDN  检查IP是否包含在CDN拒绝列表中
// @receiver c 
// @param ip net.IP: 检查的IP地址
// @return matched bool: 如果IP匹配CDN提供商范围则为true
// @return value string: 匹配的CDN提供商名称
// @return err error:  检查过程中可能出现的错误
func (c *Client) CheckCDN(ip net.IP) (matched bool, value string, err error) {
	matched, value, err = c.cdn.Match(ip)
	return matched, value, err
}

// CheckWAF checks if an IP is contained in the waf denylist
func (c *Client) CheckWAF(ip net.IP) (matched bool, value string, err error) {
	matched, value, err = c.waf.Match(ip)
	return matched, value, err
}

// CheckCloud checks if an IP is contained in the cloud denylist
func (c *Client) CheckCloud(ip net.IP) (matched bool, value string, err error) {
	matched, value, err = c.cloud.Match(ip)
	return matched, value, err
}

// Check checks if ip belongs to one of CDN, WAF and Cloud . It is generic method for Checkxxx methods
func (c *Client) Check(ip net.IP) (matched bool, value string, itemType string, err error) {
	if matched, value, err = c.cdn.Match(ip); err == nil && matched && value != "" {
		return matched, value, "cdn", nil
	}
	if matched, value, err = c.waf.Match(ip); err == nil && matched && value != "" {
		return matched, value, "waf", nil
	}
	if matched, value, err = c.cloud.Match(ip); err == nil && matched && value != "" {
		return matched, value, "cloud", nil
	}
	return false, "", "", err
}

// Check Domain with fallback checks if domain belongs to one of CDN, WAF and Cloud . It is generic method for Checkxxx methods
// Since input is domain, as a fallback it queries CNAME records and checks if domain is WAF
func (c *Client) CheckDomainWithFallback(domain string) (matched bool, value string, itemType string, err error) {
	dnsData, err := c.retriabledns.Resolve(domain)
	if err != nil {
		return false, "", "", err
	}
	matched, value, itemType, err = c.CheckDNSResponse(dnsData)
	if err != nil {
		return false, "", "", err
	}
	if matched {
		return matched, value, itemType, nil
	}
	// resolve cname
	dnsData, err = c.retriabledns.CNAME(domain)
	if err != nil {
		return false, "", "", err
	}
	return c.CheckDNSResponse(dnsData)
}

// CheckDNSResponse is same as CheckDomainWithFallback but takes DNS response as input
func (c *Client) CheckDNSResponse(dnsResponse *retryabledns.DNSData) (matched bool, value string, itemType string, err error) {
	if dnsResponse.A != nil {
		for _, ip := range dnsResponse.A {
			ipAddr := net.ParseIP(ip)
			if ipAddr == nil {
				continue
			}
			matched, value, itemType, err := c.Check(ipAddr)
			if err != nil {
				return false, "", "", err
			}
			if matched {
				return matched, value, itemType, nil
			}
		}
	}
	if dnsResponse.CNAME != nil {
		matched, discovered, itemType, err := c.CheckSuffix(dnsResponse.CNAME...)
		if err != nil {
			return false, "", itemType, err
		}
		if matched {
			// for now checkSuffix only checks for wafs
			return matched, discovered, itemType, nil
		}
	}
	return false, "", "", err
}

func (c *Client) GetDnsData(domain string) (*retryabledns.DNSData, error) {
	return c.retriabledns.Resolve(domain)
}

func mapKeys(m map[string][]string) string {
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return strings.Join(keys, ", ")
}
