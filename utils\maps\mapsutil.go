//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-15 17:21:47
//FilePath: /yaml_scan/utils/maps/mapsutil.go
//Description:

package mapsutil

import (
	"sort"

	"golang.org/x/exp/constraints"
	extmaps "golang.org/x/exp/maps"
)

// GetKeys: 获取所有的键
// K 必须是可比较的类型，意味着它可以用作映射的键。
//
//	@param maps ...map[K]V: 一个或多个映射，
//	@return []K []K:返回所有键切片
func GetKeys[K comparable, V any](maps ...map[K]V) []K {
	// 创建一个切片来存储所有的键
	var keys []K
	for _, m := range maps {
		keys = append(keys, extmaps.Keys(m)...)
	}
	return keys
}

// GetSortedKeys 返回提供的映射的所有键，并按升序排序。
// 它首先使用 GetKeys 收集所有键，然后对它们进行排序。
// K 必须是有序类型，意味着它支持比较运算符。
func GetSortedKeys[K constraints.Ordered, V any](maps ...map[K]V) []K {
	keys := GetKeys(maps...)
	sort.Slice(keys, func(i, j int) bool {
		return keys[i] < keys[j]
	})
	return keys
}
