// Author: chenjb
// Version: V1.0
// Date: 2025-07-22 19:18:23
// FilePath: /yaml_scan/pkg/rawhttp/pipelineclient.go
// Description: HTTP管道客户端实现，支持HTTP管道化请求以提高性能

// Package rawhttp HTTP管道客户端模块
package rawhttp

import (
	"io"                                    // 输入输出接口包
	"net/http"                              // 标准HTTP包
	"yaml_scan/pkg/rawhttp/clientpipeline"  // 客户端管道包
	"yaml_scan/pkg/retryablehttp"           // 可重试HTTP包
	urlutil "yaml_scan/utils/url"           // URL工具包
)

// PipelineClient 是用于发送管道化HTTP请求的客户端
// 管道化允许在单个连接上发送多个请求而无需等待响应，提高了性能
type PipelineClient struct {
	client  *clientpipeline.PipelineClient // 底层管道客户端实例
	options PipelineOptions                // 管道客户端配置选项
}

// NewPipelineClient 创建一个新的管道化HTTP请求客户端
// 参数:
//   options: 管道客户端配置选项，包含连接数、超时等设置
// 返回:
//   *PipelineClient: 新创建的管道客户端实例
// 功能: 初始化管道客户端，配置连接池和请求处理参数
func NewPipelineClient(options PipelineOptions) *PipelineClient {
	client := &PipelineClient{
		client: &clientpipeline.PipelineClient{
			Dial:               options.Dialer,            // 设置拨号器
			Addr:               options.Host,              // 设置目标主机地址
			MaxConns:           options.MaxConnections,    // 设置最大连接数
			MaxPendingRequests: options.MaxPendingRequests, // 设置最大待处理请求数
			ReadTimeout:        options.Timeout,           // 设置读取超时时间
		},
		options: options, // 保存配置选项
	}
	return client // 返回初始化完成的客户端
}

// Head 发送HEAD请求到指定URL
// 参数:
//   url: 目标URL地址
// 返回:
//   *http.Response: HTTP响应对象
//   error: 错误信息，如果请求成功则为nil
// 功能: 通过管道发送HEAD请求，用于获取资源的元信息
func (c *PipelineClient) Head(url string) (*http.Response, error) {
	return c.DoRaw("HEAD", url, "", nil, nil) // 调用DoRaw方法发送HEAD请求
}

// Get 发送GET请求到指定URL
// 参数:
//   url: 目标URL地址
// 返回:
//   *http.Response: HTTP响应对象
//   error: 错误信息，如果请求成功则为nil
// 功能: 通过管道发送GET请求，用于获取指定URL的资源内容
func (c *PipelineClient) Get(url string) (*http.Response, error) {
	return c.DoRaw("GET", url, "", nil, nil) // 调用DoRaw方法发送GET请求
}

// Post 发送POST请求到指定URL
// 参数:
//   url: 目标URL地址
//   mimetype: 请求体的MIME类型
//   body: 请求体内容的读取器
// 返回:
//   *http.Response: HTTP响应对象
//   error: 错误信息，如果请求成功则为nil
// 功能: 通过管道发送POST请求，用于提交数据到服务器
func (c *PipelineClient) Post(url string, mimetype string, body io.Reader) (*http.Response, error) {
	headers := make(map[string][]string)        // 创建HTTP头部映射
	headers["Content-Type"] = []string{mimetype} // 设置Content-Type头部
	return c.DoRaw("POST", url, "", headers, body) // 调用DoRaw方法发送POST请求
}

// Do 发送标准HTTP请求并返回响应
// 参数:
//   req: 标准的http.Request请求对象
// 返回:
//   *http.Response: HTTP响应对象
//   error: 错误信息，如果请求成功则为nil
// 功能: 将标准HTTP请求转换为管道请求并发送
func (c *PipelineClient) Do(req *http.Request) (*http.Response, error) {
	method := req.Method        // 获取请求方法
	headers := req.Header       // 获取请求头部
	url := req.URL.String()     // 获取请求URL字符串
	body := req.Body           // 获取请求体
	return c.DoRaw(method, url, "", headers, body) // 调用DoRaw方法发送请求
}

// Dor 发送可重试HTTP请求并返回响应
// 参数:
//   req: retryablehttp.Request可重试请求对象
// 返回:
//   *http.Response: HTTP响应对象
//   error: 错误信息，如果请求成功则为nil
// 功能: 将可重试HTTP请求转换为管道请求并发送
func (c *PipelineClient) Dor(req *retryablehttp.Request) (*http.Response, error) {
	method := req.Method        // 获取请求方法
	headers := req.Header       // 获取请求头部
	url := req.URL.String()     // 获取请求URL字符串
	body := req.Body           // 获取请求体

	return c.do(method, url, "", headers, body, c.options) // 调用内部do方法发送请求
}

// DoRaw 执行原始HTTP管道请求
// 参数:
//   method: HTTP请求方法（GET、POST、PUT等）
//   url: 目标URL地址
//   uripath: 自定义URI路径，如果为空则使用URL中的路径
//   headers: HTTP请求头部映射
//   body: 请求体内容的读取器
// 返回:
//   *http.Response: HTTP响应对象
//   error: 错误信息，如果请求成功则为nil
// 功能: 使用默认选项发送原始HTTP管道请求
func (c *PipelineClient) DoRaw(method, url, uripath string, headers map[string][]string, body io.Reader) (*http.Response, error) {
	return c.do(method, url, uripath, headers, body, c.options) // 调用内部do方法，使用默认选项
}

// DoRawWithOptions 使用自定义选项执行原始HTTP管道请求
// 参数:
//   method: HTTP请求方法
//   url: 目标URL地址
//   uripath: 自定义URI路径
//   headers: HTTP请求头部映射
//   body: 请求体内容的读取器
//   options: 自定义的管道选项
// 返回:
//   *http.Response: HTTP响应对象
//   error: 错误信息，如果请求成功则为nil
// 功能: 使用指定的选项发送原始HTTP管道请求
func (c *PipelineClient) DoRawWithOptions(method, url, uripath string, headers map[string][]string, body io.Reader, options PipelineOptions) (*http.Response, error) {
	return c.do(method, url, uripath, headers, body, options) // 调用内部do方法，使用指定选项
}

// do 执行HTTP管道请求的核心实现方法
// 参数:
//   method: HTTP请求方法
//   url: 目标URL地址
//   uripath: 自定义URI路径
//   headers: HTTP请求头部映射
//   body: 请求体内容的读取器
//   options: 管道选项配置
// 返回:
//   *http.Response: HTTP响应对象
//   error: 错误信息，如果请求成功则为nil
// 功能: 处理HTTP管道请求的完整流程，包括URL解析、请求构建、发送和响应转换
func (c *PipelineClient) do(method, url, uripath string, headers map[string][]string, body io.Reader, options PipelineOptions) (*http.Response, error) {
	// 如果headers为nil，初始化为空映射
	if headers == nil {
		headers = make(map[string][]string)
	}
	// 解析URL，获取各个组件
	u, err := urlutil.ParseURL(url, true)
	if err != nil {
		return nil, err // 如果URL解析失败，返回错误
	}
	// standard path - 构建标准路径
	path := u.Path
	if path == "" {
		path = "/" // 如果路径为空，设置为根路径
	}
	// 如果存在查询参数，添加到路径中
	if !u.Params.IsEmpty() {
		path += "?" + u.Params.Encode()
	}
	// override if custom one is specified - 如果指定了自定义路径，则覆盖
	if uripath != "" {
		path = uripath
	}

	// 构建管道请求对象
	req := clientpipeline.ToRequest(method, path, nil, headers, body)
	var resp clientpipeline.Response // 创建响应对象

	// 通过管道客户端发送请求
	err = c.client.Do(req, &resp)

	// response => net/http response - 将管道响应转换为标准HTTP响应
	r := http.Response{
		StatusCode:    resp.Status.Code,     // 设置状态码
		ContentLength: resp.ContentLength(), // 设置内容长度
		Header:        make(http.Header),    // 初始化头部映射
	}

	// 复制所有响应头部到标准HTTP响应中
	for _, header := range resp.Headers {
		r.Header.Set(header.Key, header.Value)
	}

	// 设置响应体，使用NopCloser包装以符合io.ReadCloser接口
	r.Body = io.NopCloser(resp.Body)

	return &r, err // 返回转换后的HTTP响应
}

