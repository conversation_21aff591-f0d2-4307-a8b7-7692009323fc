package formatter


import (
	"time"

	jsoniter "github.com/json-iterator/go"
)


// 定义一个结构体 JSON，用于实现 JSON 格式化器
type JSON struct{}

// 确保 JSON 结构体实现了 Formatter 接口
var _ Formatter = &JSON{}

// jsoniterCfg 是一个配置好的 jsoniter 实例，用于高效地处理 JSON
var jsoniterCfg jsoniter.API

//配置 jsoniter，使其在序列化时按键排序。Froze() 方法用于创建一个不可变的配置实例，以提高性能
func init() {
	jsoniterCfg = jsoniter.Config{SortMapKeys: true}.Froze()
}

func NewJSON() *JSON {
	return &JSON{}
}

// Format 将日志事件数据格式化为字节切片
func (j *JSON) Format(event *LogEvent) ([]byte, error) {

	// 创建一个 map 用于存储日志数据 键为字符串，值为任意类型。
	data := make(map[string]interface{})

	//检查 event.Metadata 中是否存在 label。如果存在且不为空，将其存储为 level，并从 Metadata 中删除。
	if label, ok := event.Metadata["label"]; ok {
		if label != "" {
			data["level"] = label
			delete(event.Metadata, "label")
		}
	}
	// 遍历 event.Metadata 中的其他键值对，将它们添加到 data 中
	for k, v := range event.Metadata {
		data[k] = v
	}

	//将日志消息添加到 data 中。
	data["msg"] = event.Message
	// 获取当前时间并格式化为字符串，添加到 data 中。
	data["timestamp"] = time.Now().Format("2006-01-02 15:04:05")

	//使用 jsoniter 将 data 转换为 JSON 格式的字节切片并返回。
	return jsoniterCfg.Marshal(data)
}