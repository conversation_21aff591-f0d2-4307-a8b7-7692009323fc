// Author: chenjb
// Version: V1.0
// Date: 2025-06-18 11:52:03
// FilePath: /yaml_scan/pkg/protocols/common/protocolstate/headless.go
// Description:
package protocolstate

import "yaml_scan/pkg/networkpolicy"

var (
	ErrURLDenied         = errorutil.NewWithFmt("headless: url %v dropped by rule: %v")
	ErrHostDenied        = errorutil.NewWithFmt("host %v dropped by network policy")
	NetworkPolicy        *networkpolicy.NetworkPolicy
	allowLocalFileAccess bool
)

