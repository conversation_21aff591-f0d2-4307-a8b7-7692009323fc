// Author: chenjb
// Version: V1.0
// Date: 2025-05-19 19:35:12
// FilePath: /yaml_scan/pkg/hybridMap/hybrid/hybrid_test.go
// Description:
package hybrid

import (
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestHybridMapMemory 测试纯内存模式下的HybridMap功能
//
// 该测试用例验证HybridMap在纯内存模式下的基本功能：
// 1. 创建内存模式的HybridMap实例
// 2. 测试Set操作能否正确存储数据
// 3. 测试Get操作能否正确获取存在的数据
// 4. 测试Get对不存在键的处理
// 5. 测试Del操作能否正确删除数据
// 6. 测试Size方法能否正确返回键值对数量
func TestHybridMapMemory(t *testing.T) {
	// 创建一个使用内存模式的HybridMap
	// 使用默认内存选项，数据只保存在内存中
	hm, err := New(DefaultMemoryOptions)
	require.NoError(t, err, "创建内存模式HybridMap应成功")
	// 确保测试结束后关闭资源，防止资源泄漏
	defer hm.Close()

	// 测试Set和Get操作
	// 首先，存储一个键值对
	err = hm.Set("key1", []byte("value1"))
	require.NoError(t, err, "内存模式Set操作应成功")

	// 验证能否正确获取刚刚存储的数据
	value, exists := hm.Get("key1")
	require.True(t, exists, "应能获取到已存在的键")
	require.Equal(t, []byte("value1"), value, "获取的值应与设置的值相同")

	// 测试不存在的键
	// 尝试获取一个不存在的键，应返回不存在
	_, exists = hm.Get("nonexistent")
	require.False(t, exists, "不应能获取到不存在的键")

	// 测试Del操作
	// 删除已存在的键，然后验证是否被删除
	err = hm.Del("key1")
	require.NoError(t, err, "Del操作应成功")
	_, exists = hm.Get("key1")
	require.False(t, exists, "删除后不应能获取到该键")

	// 测试Size方法
	// 添加一个键值对，然后检查大小是否为1
	err = hm.Set("key2", []byte("value2"))
	require.NoError(t, err)
	require.Equal(t, int64(1), hm.Size(), "Size应返回正确的键值对数量")
}

// TestHybridMapDisk 测试纯磁盘模式下的HybridMap功能
//
// 该测试用例验证HybridMap在纯磁盘模式下的基本功能：
// 1. 创建临时目录用于存储数据文件
// 2. 创建磁盘模式的HybridMap实例
// 3. 测试Set操作能否正确将数据写入磁盘
// 4. 测试Get操作能否正确从磁盘读取数据
// 5. 测试Del操作能否正确从磁盘删除数据
func TestHybridMapDisk(t *testing.T) {
	// 创建临时目录
	// 使用系统临时目录创建一个唯一的测试目录
	tmpDir, err := os.MkdirTemp("", "hybridmap-test")
	require.NoError(t, err, "创建临时目录应成功")
	// 确保测试结束后删除临时目录，清理资源
	defer os.RemoveAll(tmpDir)

	// 创建一个使用磁盘模式的HybridMap
	// 使用默认磁盘选项，但指定自定义路径
	options := DefaultDiskOptions
	options.Path = tmpDir
	hm, err := New(options)
	require.NoError(t, err, "创建磁盘模式HybridMap应成功")
	defer hm.Close()

	// 测试Set和Get操作
	// 在磁盘上存储一个键值对
	err = hm.Set("key1", []byte("value1"))
	require.NoError(t, err, "磁盘模式Set操作应成功")

	// 验证能否从磁盘正确读取存储的数据
	value, exists := hm.Get("key1")
	require.True(t, exists, "应能获取到已存在的键")
	require.Equal(t, []byte("value1"), value, "获取的值应与设置的值相同")

	// 测试Del操作
	// 从磁盘删除数据，然后验证是否被删除
	err = hm.Del("key1")
	require.NoError(t, err, "Del操作应成功")
	_, exists = hm.Get("key1")
	require.False(t, exists, "删除后不应能获取到该键")

}

// TestHybridMapHybrid 测试混合模式下的HybridMap功能
//
// 该测试用例验证HybridMap在混合模式下的功能：
// 1. 创建临时目录用于磁盘存储
// 2. 创建混合模式的HybridMap实例，设置较短的内存过期时间
// 3. 测试数据初始存储在内存中
// 4. 测试等待内存数据过期后，数据会从磁盘读取
// 5. 测试Del操作能否同时从内存和磁盘删除数据
// 6. 测试Scan能否遍历所有存储的键值对
func TestHybridMapHybrid(t *testing.T) {
	// 创建临时目录用于磁盘存储部分
	tmpDir, err := os.MkdirTemp("", "hybridmap-test")
	require.NoError(t, err, "创建临时目录应成功")
	defer os.RemoveAll(tmpDir)

	// 创建一个使用混合模式的HybridMap
	// 使用默认混合选项，但指定自定义路径和较短的内存过期时间
	options := DefaultHybridOptions
	options.Path = tmpDir
	options.MemoryExpirationTime = 1 * time.Second // 设置较短的内存过期时间以便测试
	hm, err := New(options)
	require.NoError(t, err, "创建混合模式HybridMap应成功")
	defer hm.Close()

	// 测试Set和Get操作
	// 存储一个键值对（此时应该存储在内存中）
	err = hm.Set("key1", []byte("value1"))
	require.NoError(t, err, "混合模式Set操作应成功")

	// 从内存中获取数据
	// 此时数据应存在于内存缓存中
	value, exists := hm.Get("key1")
	require.True(t, exists, "应能从内存获取到已存在的键")
	require.Equal(t, []byte("value1"), value, "获取的值应与设置的值相同")

	// 等待内存中的数据过期
	// 等待时间略大于设置的过期时间，确保清理任务已经执行
	time.Sleep(2 * time.Second)

	// 应从磁盘中获取数据
	// 此时内存中的数据已过期，应该从磁盘获取
	value, exists = hm.Get("key1")
	require.True(t, exists, "应能从磁盘获取到已存在的键")
	require.Equal(t, []byte("value1"), value, "从磁盘获取的值应与设置的值相同")

	// 测试Del操作
	// 删除键值对，应同时从内存和磁盘中删除
	err = hm.Del("key1")
	require.NoError(t, err, "Del操作应成功")
	_, exists = hm.Get("key1")
	require.False(t, exists, "删除后不应能获取到该键")

	// 测试Scan方法
	// 添加两个不同的键值对
	err = hm.Set("key2", []byte("value2"))
	require.NoError(t, err)
	err = hm.Set("key3", []byte("value3"))
	require.NoError(t, err)

	// 使用Scan遍历所有键值对
	scannedItems := make(map[string]string)
	hm.Scan(func(k []byte, v []byte) error {
		// 将遍历到的键值对存储到map中以便验证
		scannedItems[string(k)] = string(v)
		return nil
	})
	// 验证是否遍历到了所有键值对
	require.Equal(t, 2, len(scannedItems), "Scan应遍历所有键值对")
	require.Equal(t, "value2", scannedItems["key2"], "Scan应返回正确的值")
	require.Equal(t, "value3", scannedItems["key3"], "Scan应返回正确的值")
}

// TestHybridMapCleanup 测试HybridMap的清理功能
//
// 该测试用例验证HybridMap在关闭时是否能正确清理磁盘资源：
// 1. 创建具有清理功能的HybridMap
// 2. 验证磁盘存储路径是否被创建
// 3. 执行一些基本操作，确保数据可以正常存取
// 4. 关闭HybridMap，触发清理逻辑
// 5. 验证磁盘存储路径是否已被删除
func TestHybridMapCleanup(t *testing.T) {
	// 创建具有清理功能的HybridMap
	// 使用默认磁盘选项，启用清理功能
	options := DefaultDiskOptions
	options.Cleanup = true
	hm, err := New(options)
	require.NoError(t, err, "创建带清理功能的HybridMap应成功")

	// 保存磁盘路径以便后续检查
	path := hm.diskmapPath

	// 检查路径是否存在
	// 此时路径应该已被创建
	_, err = os.Stat(path)
	require.NoError(t, err, "磁盘存储路径应存在")

	// 测试基本操作
	// 确保数据可以正常存取，验证实例功能正常
	err = hm.Set("key1", []byte("value1"))
	require.NoError(t, err)
	value, exists := hm.Get("key1")
	require.True(t, exists)
	require.Equal(t, []byte("value1"), value)

	// 关闭HybridMap，应触发清理
	// Close方法应该删除磁盘存储目录
	err = hm.Close()
	require.NoError(t, err, "关闭HybridMap应成功")

	// 检查路径是否已被删除
	// 由于启用了清理功能，路径应该不再存在
	_, err = os.Stat(path)
	require.True(t, os.IsNotExist(err), "清理后磁盘存储路径应被删除")
}

// TestMemoryGuard 测试内存监控功能
//
// 该测试用例验证内存监控功能是否正常工作：
// 1. 创建带内存监控的HybridMap，设置较小的内存限制
// 2. 等待内存监控执行几次检查
// 3. 存储大量数据，触发内存保护机制
// 4. 验证是否触发了强制使用磁盘模式
// 5. 确认即使在强制使用磁盘模式下，数据仍然可以正确存取
func TestMemoryGuard(t *testing.T) {
	// 创建带内存监控的HybridMap
	// 使用默认混合选项，启用内存监控，设置较小的内存限制
	options := DefaultHybridOptions
	options.MemoryGuard = true
	options.MemoryGuardTime = 100 * time.Millisecond
	options.MaxMemorySize = 100 // 设置非常小的内存限制以便触发MemoryGuardForceDisk
	hm, err := New(options)
	require.NoError(t, err, "创建带内存监控的HybridMap应成功")
	defer hm.Close()

	// 等待内存监控检查执行
	// 确保内存监控协程有足够时间执行几次检查
	time.Sleep(200 * time.Millisecond)

	// 设置大量数据以触发内存监控
	// 创建一个较大的字节数组，大小超过设置的内存限制
	largeData := make([]byte, 1000)
	for i := range largeData {
		largeData[i] = byte(i % 256)
	}

	// 执行一些Set操作
	// 存储大量数据，应该触发内存保护机制
	err = hm.Set("large_key", largeData)
	require.NoError(t, err)

	// TuneMemory应已被调用，检查是否为强制磁盘模式
	// 由于内存使用超过阈值，应该触发强制磁盘模式
	require.True(t, hm.options.MemoryGuardForceDisk, "大内存使用应触发强制磁盘模式")

	// 检查是否仍能正常获取数据
	// 即使在强制磁盘模式下，数据访问应该仍然正常
	value, exists := hm.Get("large_key")
	require.True(t, exists)
	require.Equal(t, largeData, value)
}
