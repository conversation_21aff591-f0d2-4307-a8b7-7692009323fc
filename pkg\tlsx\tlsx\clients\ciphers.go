// Author: chenjb
// Version: V1.0
// Date: 2025-06-23 20:20:04
// FilePath: /yaml_scan/pkg/tlsx/tlsx/clients/ciphers.go
// Description:TLS密码套件安全级别定义和相关工具函数
package clients

import (
	"strings"
	"yaml_scan/pkg/tlsx/assets"
)

// CipherSecLevel 表示密码套件的安全级别
type CipherSecLevel uint

const (
	All      CipherSecLevel = iota // 表示所有密码套件，用于不限制安全级别的场景
	Weak                           // 弱密码套件，存在已知漏洞或使用了强度不足的算法，如RC4、DES等
	Insecure                       // 不安全密码套件，虽不如Weak级别危险，但仍有安全隐患，如较弱的密钥交换算法
	Secure                         // 安全密码套件，符合当前安全标准，使用了强加密算法和安全的密钥交换方法
	Unknown                        // 未知安全级别的密码套件，未在预定义安全级别映射中找到
)

// GetCiphersWithLevel 返回具有指定安全级别的密码套件列表
// @param cipherList []string:  要筛选的密码套件名称列表，可能包含多种安全级别的密码套件
// @param SecLevel ...CipherSecLevel: 可变参数，指定要包含的安全级别，可以同时指定多个级别
// @return []string []string: 符合指定安全级别的密码套件列表，如果指定了多个安全级别，返回所有符合任一级别的密码套件
func GetCiphersWithLevel(cipherList []string, SecLevel ...CipherSecLevel) []string {
	toEnumerate := []string{}
	if len(SecLevel) == 0 {
		// 如果未指定安全级别，返回所有密码套件
		return cipherList
	}
		// 遍历指定的安全级别，分别获取符合各级别的密码套件
	for _, level := range SecLevel {
		switch level {
		case All:
			// 如果指定了All级别，直接返回所有密码套件，无需进一步筛选
			return cipherList
		case Weak:
			// 获取输入列表中属于弱安全级别的密码套件
			// 通过与预定义的弱密码套件列表取交集实现
			toEnumerate = append(toEnumerate, IntersectStringSlices(cipherList, assets.GetWeakCipherSuites())...)
		case Insecure:
			// 获取输入列表中属于不安全级别的密码套件
			// 通过与预定义的不安全密码套件列表取交集实现
			toEnumerate = append(toEnumerate, IntersectStringSlices(cipherList, assets.GetInSecureCipherSuites())...)
		case Secure:
			// 获取输入列表中属于安全级别的密码套件
			// 通过与预定义的安全密码套件列表取交集实现
			toEnumerate = append(toEnumerate, IntersectStringSlices(cipherList, assets.GetSecureCipherSuites())...)
		}
	}
	return toEnumerate
}

// GetCipherLevel 返回给定密码套件的安全级别
// @param cipherName string:  要评估的密码套件名称，如"TLS_RSA_WITH_AES_128_CBC_SHA"
// @return CipherSecLevel CipherSecLevel: 密码套件的安全级别
func GetCipherLevel(cipherName string) CipherSecLevel {
	for k, v := range assets.CipherSecLevel {
		if strings.EqualFold(k, cipherName) {
			switch v {
			case "Recommended":
				return Secure
			case "Secure":
				return Secure
			case "Insecure":
				return Insecure
			case "Weak":
				return Weak
			}
		}
	}
	return Unknown
}