// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-18 11:23:55
// FilePath: /yaml_scan/pkg/catalog/disk/find.go
// Description: 
package disk


// PrintDeprecatedPathsMsgIfApplicable prints a warning message if any deprecated paths are found
// Unless mode is silent warning message is printed
func PrintDeprecatedPathsMsgIfApplicable(isSilent bool) {
	if !updateutils.IsOutdated("v9.4.3", config.DefaultConfig.TemplateVersion) {
		return
	}
	if deprecatedPathsCounter > 0 && !isSilent {
		gologger.Print().Msgf("[%v] Found %v template[s] loaded with deprecated paths, update before v3 for continued support.\n", aurora.Yellow("WRN").String(), deprecatedPathsCounter)
	}
}

