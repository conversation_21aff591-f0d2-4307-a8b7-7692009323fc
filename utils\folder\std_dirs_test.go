//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-21 20:22:48
//FilePath: /yaml_scan/utils/folder/std_dirs_test.go
//Description:

package folderutil

import (
	"path/filepath"
	"testing"
)

// TestUserConfigDirOrDefault 测试 UserConfigDirOrDefault 函数
func TestUserConfigDirOrDefault(t *testing.T) {
	defaultDir := "/tmp/defaultConfigDir"

	// 测试获取用户配置目录
	userConfigDir := UserConfigDirOrDefault(defaultDir)
	if userConfigDir == "" {
		t.Errorf("Expected user config directory, got empty string")
	}

	
}

// TestAppConfigDirOrDefault 测试 AppConfigDirOrDefault 函数
func TestAppConfigDirOrDefault(t *testing.T) {
	defaultAppConfigDir := "/tmp/defaultAppConfigDir"
	toolName := "mytool"

	// 测试获取应用程序配置目录
	appConfigDir := AppConfigDirOrDefault(defaultAppConfigDir, toolName)
	expectedDir := filepath.Join(UserConfigDirOrDefault(""), toolName)

	if appConfigDir != expectedDir {
		t.Errorf("Expected app config directory %s, got %s", expectedDir, appConfigDir)
	}

}
