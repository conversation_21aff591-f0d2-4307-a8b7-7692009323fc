// Author: chenjb
// Version: V1.0
// Date: 2025-07-25
// FilePath: /yaml_scan/pkg/rawhttp/options_test.go
// Description: rawhttp配置选项模块单元测试

package rawhttp

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"yaml_scan/pkg/fastdialer"
	"yaml_scan/pkg/rawhttp/client"
)

// TestOptions 测试Options结构体
func TestOptions(t *testing.T) {
	// 测试创建空的Options
	options := &Options{}
	require.NotNil(t, options, "Options应该被创建")
	
	// 测试设置各种选项
	options.Timeout = 60 * time.Second
	options.FollowRedirects = true
	options.MaxRedirects = 5
	options.AutomaticHostHeader = true
	options.AutomaticContentLength = true
	options.ForceReadAllBody = true
	options.Proxy = "http://proxy.example.com:8080"
	options.ProxyDialTimeout = 10 * time.Second
	options.SNI = "example.com"
	
	// 验证所有字段都被正确设置
	require.Equal(t, 60*time.Second, options.Timeout, "超时时间应该正确")
	require.True(t, options.FollowRedirects, "应该跟踪重定向")
	require.Equal(t, 5, options.MaxRedirects, "最大重定向次数应该正确")
	require.True(t, options.AutomaticHostHeader, "应该自动添加Host头部")
	require.True(t, options.AutomaticContentLength, "应该自动计算Content-Length")
	require.True(t, options.ForceReadAllBody, "应该强制读取所有响应体")
	require.Equal(t, "http://proxy.example.com:8080", options.Proxy, "代理URL应该正确")
	require.Equal(t, 10*time.Second, options.ProxyDialTimeout, "代理连接超时应该正确")
	require.Equal(t, "example.com", options.SNI, "SNI应该正确")
}

// TestOptions_CustomHeaders 测试自定义头部设置
func TestOptions_CustomHeaders(t *testing.T) {
	options := &Options{}
	
	// 设置自定义头部
	customHeaders := client.Headers{
		{Key: "X-Custom-Header", Value: "custom-value"},
		{Key: "Authorization", Value: "Bearer token123"},
	}
	options.CustomHeaders = customHeaders
	
	require.Equal(t, customHeaders, options.CustomHeaders, "自定义头部应该正确设置")
	require.Len(t, options.CustomHeaders, 2, "应该有2个自定义头部")
	
	// 验证头部内容
	require.Equal(t, "X-Custom-Header", options.CustomHeaders[0].Key, "第一个头部键应该正确")
	require.Equal(t, "custom-value", options.CustomHeaders[0].Value, "第一个头部值应该正确")
	require.Equal(t, "Authorization", options.CustomHeaders[1].Key, "第二个头部键应该正确")
	require.Equal(t, "Bearer token123", options.CustomHeaders[1].Value, "第二个头部值应该正确")
}

// TestOptions_CustomRawBytes 测试自定义原始字节设置
func TestOptions_CustomRawBytes(t *testing.T) {
	options := &Options{}
	
	// 设置自定义原始字节
	customBytes := []byte("GET / HTTP/1.1\r\nHost: example.com\r\n\r\n")
	options.CustomRawBytes = customBytes
	
	require.Equal(t, customBytes, options.CustomRawBytes, "自定义原始字节应该正确设置")
	require.Len(t, options.CustomRawBytes, len(customBytes), "原始字节长度应该正确")
}

// TestOptions_FastDialer 测试FastDialer设置
func TestOptions_FastDialer(t *testing.T) {
	options := &Options{}
	
	// 创建FastDialer
	opts := fastdialer.DefaultOptions
	fastDialer, err := fastdialer.NewDialer(opts)
	require.NoError(t, err, "创建FastDialer应该成功")
	
	// 设置FastDialer
	options.FastDialer = fastDialer
	
	require.Equal(t, fastDialer, options.FastDialer, "FastDialer应该正确设置")
	require.NotNil(t, options.FastDialer, "FastDialer不应该为nil")
}

// TestDefaultOptions 测试默认选项
func TestDefaultOptions(t *testing.T) {
	require.NotNil(t, DefaultOptions, "默认选项不应该为nil")
	
	// 验证默认值
	require.Equal(t, 30*time.Second, DefaultOptions.Timeout, "默认超时应该是30秒")
	require.True(t, DefaultOptions.FollowRedirects, "默认应该跟踪重定向")
	require.Equal(t, 10, DefaultOptions.MaxRedirects, "默认最大重定向次数应该是10")
	require.True(t, DefaultOptions.AutomaticHostHeader, "默认应该自动添加Host头部")
	require.True(t, DefaultOptions.AutomaticContentLength, "默认应该自动计算Content-Length")
	
	// 验证未设置的字段为零值
	require.False(t, DefaultOptions.ForceReadAllBody, "默认不应该强制读取所有响应体")
	require.Empty(t, DefaultOptions.Proxy, "默认代理应该为空")
	require.Equal(t, time.Duration(0), DefaultOptions.ProxyDialTimeout, "默认代理超时应该为0")
	require.Empty(t, DefaultOptions.SNI, "默认SNI应该为空")
	require.Nil(t, DefaultOptions.CustomHeaders, "默认自定义头部应该为nil")
	require.Nil(t, DefaultOptions.CustomRawBytes, "默认自定义原始字节应该为nil")
	require.Nil(t, DefaultOptions.FastDialer, "默认FastDialer应该为nil")
}

// TestOptions_Copy 测试选项复制（验证选项是独立的）
func TestOptions_Copy(t *testing.T) {
	// 创建原始选项
	original := &Options{
		Timeout:                30 * time.Second,
		FollowRedirects:        true,
		MaxRedirects:           5,
		AutomaticHostHeader:    true,
		AutomaticContentLength: true,
		Proxy:                  "http://proxy.example.com:8080",
	}
	
	// 创建新选项并修改
	copy := &Options{
		Timeout:                60 * time.Second,
		FollowRedirects:        false,
		MaxRedirects:           10,
		AutomaticHostHeader:    false,
		AutomaticContentLength: false,
		Proxy:                  "http://other-proxy.example.com:8080",
	}
	
	// 验证两个选项是独立的
	require.NotEqual(t, original.Timeout, copy.Timeout, "超时时间应该不同")
	require.NotEqual(t, original.FollowRedirects, copy.FollowRedirects, "重定向设置应该不同")
	require.NotEqual(t, original.MaxRedirects, copy.MaxRedirects, "最大重定向次数应该不同")
	require.NotEqual(t, original.AutomaticHostHeader, copy.AutomaticHostHeader, "自动Host头部设置应该不同")
	require.NotEqual(t, original.AutomaticContentLength, copy.AutomaticContentLength, "自动Content-Length设置应该不同")
	require.NotEqual(t, original.Proxy, copy.Proxy, "代理设置应该不同")
}

// TestOptions_ZeroValues 测试零值选项
func TestOptions_ZeroValues(t *testing.T) {
	options := &Options{}
	
	// 验证所有字段的零值
	require.Equal(t, time.Duration(0), options.Timeout, "默认超时应该为0")
	require.False(t, options.FollowRedirects, "默认不跟踪重定向")
	require.Equal(t, 0, options.MaxRedirects, "默认最大重定向次数为0")
	require.False(t, options.AutomaticHostHeader, "默认不自动添加Host头部")
	require.False(t, options.AutomaticContentLength, "默认不自动计算Content-Length")
	require.False(t, options.ForceReadAllBody, "默认不强制读取所有响应体")
	require.Empty(t, options.Proxy, "默认代理为空")
	require.Equal(t, time.Duration(0), options.ProxyDialTimeout, "默认代理超时为0")
	require.Empty(t, options.SNI, "默认SNI为空")
	require.Nil(t, options.CustomHeaders, "默认自定义头部为nil")
	require.Nil(t, options.CustomRawBytes, "默认自定义原始字节为nil")
	require.Nil(t, options.FastDialer, "默认FastDialer为nil")
}
