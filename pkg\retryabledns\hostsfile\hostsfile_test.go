//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-16 20:30:00
//FilePath: /yaml_scan/pkg/retryabledns/hostsfile/hostsfile_test.go
//Description:

package hostsfile

import (
	"os"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestParse 测试 Parse 函数的正确性
func TestParse(t *testing.T) {
	// 假设我们有一个临时的 hosts 文件内容
	testHostsContent := `
		# This is a comment
		127.0.0.1 localhost
		*********** example.com
		*********** example.com
		# Another comment
		******** test.local
	`

	// 创建一个临时文件并写入测试内容
	tempFile, err := os.CreateTemp("", "hostsfile_test_")
	require.NoError(t, err)
	defer os.Remove(tempFile.Name()) // 测试结束后删除临时文件

	_, err = tempFile.WriteString(testHostsContent)
	require.NoError(t, err)
	tempFile.Close() // 关闭文件

	// 调用 Parse 函数进行解析
	hosts, err := Parse(tempFile.Name())
	require.NoError(t, err) // 确保没有错误

	// 断言解析结果是否符合预期
	expected := map[string][]string{
		"localhost":   {"127.0.0.1"},
		"example.com": {"***********", "***********"},
		"test.local":  {"********"},
	}
	require.Equal(t, expected, hosts) // 断言结果
}
