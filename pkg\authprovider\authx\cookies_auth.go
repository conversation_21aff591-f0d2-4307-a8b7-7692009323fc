// Author: chenjb
// Version: V1.0
// Date: 2025-06-13 14:54:52
// FilePath: /yaml_scan/pkg/authprovider/authx/cookies_auth.go
// Description:实现了Cookie认证策略，用于通过HTTP Cookie进行认证
package authx

import (
	"net/http"
	"slices"
	"yaml_scan/pkg/retryablehttp"
)

var (
	// 确保CookiesAuthStrategy实现了AuthStrategy接口
	_ AuthStrategy = &CookiesAuthStrategy{}
)

// CookiesAuthStrategy 是Cookie认证策略的实现
type CookiesAuthStrategy struct {
	Data *Secret
}

// NewCookiesAuthStrategy 创建一个新的Cookie认证策略
func NewCookiesAuthStrategy(data *Secret) *CookiesAuthStrategy {
	return &CookiesAuthStrategy{Data: data}
}

// Apply 将Cookie认证策略应用到HTTP请求上
// 它会使用密钥中的Cookie键值对设置HTTP Cookie
// @receiver s 
// @param req *http.Request: 
func (s *CookiesAuthStrategy) Apply(req *http.Request) {
	for _, cookie := range s.Data.Cookies {
		c := &http.Cookie{
			Name:  cookie.Key,
			Value: cookie.Value,
		}
		req.AddCookie(c)
	}
}

// ApplyOnRR 将Cookie认证策略应用到可重试的HTTP请求上
// 它会使用密钥中的Cookie键值对设置HTTP Cookie，同时处理现有Cookie的冲突
// @receiver s 
// @param req *retryablehttp.Request: 
func (s *CookiesAuthStrategy) ApplyOnRR(req *retryablehttp.Request) {
	// 获取请求中已有的Cookie
	existingCookies := req.Cookies()

	// 删除与新Cookie同名的现有Cookie，避免冲突
	for _, newCookie := range s.Data.Cookies {
		for i, existing := range existingCookies {
			if existing.Name == newCookie.Key {
				existingCookies = slices.Delete(existingCookies, i, i+1)
				break
			}
		}
	}

	// 清除并重新设置剩余的Cookie
	req.Header.Del("Cookie")
	for _, cookie := range existingCookies {
		req.AddCookie(cookie)
	}
	// 添加新的Cookie
	for _, cookie := range s.Data.Cookies {
		req.AddCookie(&http.Cookie{
			Name:  cookie.Key,
			Value: cookie.Value,
		})
	}
}

