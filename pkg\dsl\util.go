// Author: chenjb
// Version: V1.0
// Date: 2025-05-28 15:04:15
// FilePath: /yaml_scan/pkg/dsl/util.go
// Description: 实现DSL包中使用的各种工具函数，包括字符串处理、时间格式化、随机生成等功能
package dsl

import (
	"bytes"
	"crypto/rand"
	"encoding/hex"
	"errors"
	"fmt"
	"hash"
	"net"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
	iputil "yaml_scan/utils/ip"
	randint "yaml_scan/utils/rand"

	"github.com/kataras/jwt"
)

// maxIterations 定义循环操作的最大迭代次数，防止无限循环
const maxIterations = 255

// toString 快速将任意接口类型转换为字符串
// @param data interface{}: 要转换为字符串的任意类型数据
// @return string string: 转换后的字符串
func toString(data interface{}) string {
	switch s := data.(type) {
	case nil:
		return ""
	case string:
		return s
	case bool:
		return strconv.FormatBool(s)
	case float64:
		return strconv.FormatFloat(s, 'f', -1, 64)
	case float32:
		return strconv.FormatFloat(float64(s), 'f', -1, 32)
	case int:
		return strconv.Itoa(s)
	case int64:
		return strconv.FormatInt(s, 10)
	case int32:
		return strconv.Itoa(int(s))
	case int16:
		return strconv.FormatInt(int64(s), 10)
	case int8:
		return strconv.FormatInt(int64(s), 10)
	case uint:
		return strconv.FormatUint(uint64(s), 10)
	case uint64:
		return strconv.FormatUint(s, 10)
	case uint32:
		return strconv.FormatUint(uint64(s), 10)
	case uint16:
		return strconv.FormatUint(uint64(s), 10)
	case uint8:
		return strconv.FormatUint(uint64(s), 10)
	case []byte:
		return string(s)
	case fmt.Stringer:
		return s.String()
	case error:
		return s.Error()
	default:
		return fmt.Sprintf("%v", data)
	}
}

// insertInto 按指定间隔在字符串中插入分隔符
// @param s string: 原始字符串
// @param interval int: 插入间隔
// @param sep rune: 要插入的分隔符
// @return string string: 插入分隔符后的字符串
func insertInto(s string, interval int, sep rune) string {
	var buffer bytes.Buffer
	before := interval - 1
	last := len(s) - 1
	for i, char := range s {
		buffer.WriteRune(char)
		// 如果当前位置需要插入分隔符且不是最后一个字符
		if i%interval == before && i != last {
			buffer.WriteRune(sep)
		}
	}
	buffer.WriteRune(sep)
	return buffer.String()
}

// appendSingleDigitZero 在单个数字前添加零，实现两位数填充
// @param value string: 输入的字符串数字
// @return string string: 处理后的字符串，如果是单个数字则前面补零
func appendSingleDigitZero(value string) string {
	if len(value) == 1 && (!strings.HasPrefix(value, "0") || value == "0") {
		builder := &strings.Builder{}
		builder.WriteRune('0')
		builder.WriteString(value)
		newVal := builder.String()
		return newVal
	}
	return value
}

// formatDateTime 格式化日期时间字符串中的特定部分
// @param inputFormat string: 输入的格式化字符串模板
// @param matchValue string: 要替换的匹配值
// @param timeFragment int: 时间片段值
// @return string string: 替换后的格式化字符串
func formatDateTime(inputFormat string, matchValue string, timeFragment int) string {
	return strings.ReplaceAll(inputFormat, matchValue, appendSingleDigitZero(strconv.Itoa(timeFragment)))
}

// doSimpleTimeFormat 根据格式化片段和时间对象生成格式化的时间字符串
// @param dateTimeFormatFragment [][]string: 日期时间格式化片段的二维数组
// @param currentTime time.Time: 当前时间对象
// @param dateTimeFormat string: 日期时间格式字符串
// @return interface{} interface{}: 格式化后的时间字符串
// @return error error: 可能得错误
func doSimpleTimeFormat(dateTimeFormatFragment [][]string, currentTime time.Time, dateTimeFormat string) (interface{}, error) {
	for _, currentFragment := range dateTimeFormatFragment {
		if len(currentFragment) < 2 {
			continue
		}
		prefixedFormatFragment := currentFragment[0]
		switch currentFragment[1] {
		case "Y", "y":
			dateTimeFormat = formatDateTime(dateTimeFormat, prefixedFormatFragment, currentTime.Year())
		case "M":
			dateTimeFormat = formatDateTime(dateTimeFormat, prefixedFormatFragment, int(currentTime.Month()))
		case "D", "d":
			dateTimeFormat = formatDateTime(dateTimeFormat, prefixedFormatFragment, currentTime.Day())
		case "H", "h":
			dateTimeFormat = formatDateTime(dateTimeFormat, prefixedFormatFragment, currentTime.Hour())
		case "m":
			dateTimeFormat = formatDateTime(dateTimeFormat, prefixedFormatFragment, currentTime.Minute())
		case "S", "s":
			dateTimeFormat = formatDateTime(dateTimeFormat, prefixedFormatFragment, currentTime.Second())
		default:
			return nil, fmt.Errorf("invalid date time format string: %s", prefixedFormatFragment)
		}
	}
	return dateTimeFormat, nil
}

// parseTimeOrNow  解析时间参数或使用当前时间
// @param arguments []interface{}: 参数列表，可能包含时间值
// @return time.Time time.Time:  解析后的时间对象
// @return error error: 可能得错误
func parseTimeOrNow(arguments []interface{}) (time.Time, error) {
	var currentTime time.Time
	if len(arguments) == 2 {
		switch inputUnixTime := arguments[1].(type) {
		case time.Time:
			currentTime = inputUnixTime
		case string:
			unixTime, err := strconv.ParseInt(inputUnixTime, 10, 64)
			if err != nil {
				return time.Time{}, errors.New("invalid argument type")
			}
			currentTime = time.Unix(unixTime, 0)
		case int64, float64:
			currentTime = time.Unix(int64(inputUnixTime.(float64)), 0)
		default:
			return time.Time{}, errors.New("invalid argument type")
		}
	} else {
		currentTime = time.Now()
	}
	return currentTime, nil
}

// toHexEncodedHash  使用指定的哈希算法计算数据的哈希值并转为十六进制字符串
// @param hashToUse hash.Hash: 要使用的哈希算法实例
// @param data string:  要计算哈希的字符串数据
// @return interface{} interface{}: 十六进制编码的哈希字符串
// @return error error: 可能得错误
func toHexEncodedHash(hashToUse hash.Hash, data string) (interface{}, error) {
	if _, err := hashToUse.Write([]byte(data)); err != nil {
		return nil, err
	}
	return hex.EncodeToString(hashToUse.Sum(nil)), nil
}

// toChunks 将字符串按指定大小分割成多个块
// @param input string: 输入字符串
// @param chunkSize int: 每个块的大小
// @return []string []string: 分割后的字符串
func toChunks(input string, chunkSize int) []string {
	if chunkSize <= 0 || chunkSize >= len(input) {
		return []string{input}
	}
	var chunks = make([]string, 0, (len(input)-1)/chunkSize+1)
	currentLength := 0
	currentStart := 0
	for i := range input {
		if currentLength == chunkSize {
			chunks = append(chunks, input[currentStart:i])
			currentLength = 0
			currentStart = i
		}
		currentLength++
	}
	chunks = append(chunks, input[currentStart:])
	return chunks
}

// toStringSlice 将不同类型的切片转换为字符串切片
// @param v interface{}:  输入的切片，可以是不同类型
// @return m []string:  转换后的字符串切片
func toStringSlice(v interface{}) (m []string) {
	switch vv := v.(type) {
	case []string:
		for _, item := range vv {
			m = append(m, toString(item))
		}
	case []int:
		for _, item := range vv {
			m = append(m, toString(item))
		}
	case []float64:
		for _, item := range vv {
			m = append(m, toString(item))
		}
	}
	return
}

// TrimAll 从字符串中移除所有指定的字符
// @param s string: 原始字符串
// @param cutset string: 移除的字符集合
// @return string string: 处理后的字符串
func TrimAll(s, cutset string) string {
	for _, c := range cutset {
		s = strings.ReplaceAll(s, string(c), "")
	}
	return s
}

// RandSeq 生成指定长度的随机字符序列
// @param base string: 字符集，从中选择随机字符
// @param n int:  要生成的序列长度
// @return string string: 生成的随机字符序列
func RandSeq(base string, n int) string {
	b := make([]rune, n)
	for i := range b {
		rint, _ := randint.IntN(len(base))
		b[i] = rune(base[rint])
	}
	return string(b)
}

// GetRandomIPWithCidr 根据CIDR生成随机IP地址
// @param cidrs ...string:  CIDR格式的IP范围列表
// @return net.IP net.IP: 生成的随机IP地址
// @return error error: 可能的错误
func GetRandomIPWithCidr(cidrs ...string) (net.IP, error) {
	if len(cidrs) == 0 {
		return nil, fmt.Errorf("must specify at least one cidr")
	}
	i, err := randint.IntN(len(cidrs))
	if err != nil {
		return nil, err
	}
	cidr := cidrs[i]

	if !iputil.IsCIDR(cidr) {
		return nil, fmt.Errorf("%s is not a valid cidr", cidr)
	}

	baseIp, ipnet, err := net.ParseCIDR(cidr)
	if err != nil {
		return nil, err
	}

	switch {
	case ipnet.Mask[len(ipnet.Mask)-1] == 255:
		return baseIp, nil
	case iputil.IsIPv4(baseIp.String()):
		return getRandomIP(ipnet, 4), nil
	case iputil.IsIPv6(baseIp.String()):
		return getRandomIP(ipnet, 16), nil
	default:
		return nil, errors.New("invalid base ip")
	}
}

// getRandomIP 在给定的IP网络范围内生成随机IP
// @param ipnet *net.IPNet: IP网络范围
// @param size int: P地址的字节大小（4表示IPv4，16表示IPv6）
// @return net.IP net.IP: 生成的随机IP地址
func getRandomIP(ipnet *net.IPNet, size int) net.IP {
	ip := ipnet.IP
	var iteration int

	for iteration < maxIterations {
		iteration++
		ones, _ := ipnet.Mask.Size()
		quotient := ones / 8
		remainder := ones % 8
		var r []byte
		switch size {
		case 4, 16:
			r = make([]byte, size)
		default:
			return ip
		}

		_, _ = rand.Read(r)

		for i := 0; i <= quotient; i++ {
			if i == quotient {
				shifted := byte(r[i]) >> remainder
				r[i] = ipnet.IP[i] + (^ipnet.IP[i] & shifted)
			} else {
				r[i] = ipnet.IP[i]
			}
		}

		ip = r

		if !ip.Equal(ipnet.IP) {
			break
		}
	}

	return ip
}

// stringNumberToDecimal 将字符串表示的数字转换为十进制浮点数
// @param args []interface{}: 参数列表，第一个参数应为待转换的字符串
// @param prefix string:  数字前缀，如"0x"表示十六进制，"0b"表示二进制
// @param base int:  数字的进制，如10表示十进制，16表示十六进制
// @return interface{} interface{}: 转换后的十进制浮点数
// @return error error: 可能的错误
func stringNumberToDecimal(args []interface{}, prefix string, base int) (interface{}, error) {
	input := toString(args[0])
	if strings.HasPrefix(input, prefix) {
		base = 0
	}
	if number, err := strconv.ParseInt(input, base, 64); err == nil {
		return float64(number), err
	}
	return nil, fmt.Errorf("invalid number: %s", input)
}

// pkcs5padding 实现PKCS#5填充算法，用于加密数据
// @param ciphertext []byte: 原始数据
// @param blockSize int: 加密块大小
// @param after int: 填充后的附加参数，用于某些特殊算法
// @return []byte []byte: 填充后的数据
func pkcs5padding(ciphertext []byte, blockSize int, after int) []byte {
	padding := (blockSize - len(ciphertext)%blockSize)
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

// algNONE 定义了表示JWT算法"none"的结构体
type algNONE struct {
	algValue string // 算法名称
}

// Name 返回算法的名称
func (a *algNONE) Name() string {
	return a.algValue
}

// Sign 实现JWT算法接口的签名方法
func (a *algNONE) Sign(key jwt.PrivateKey, headerAndPayload []byte) ([]byte, error) {
	return nil, nil
}

func (a *algNONE) Verify(key jwt.PublicKey, headerAndPayload []byte, signature []byte) error {
	if !bytes.Equal(signature, []byte{}) {
		return jwt.ErrTokenSignature
	}

	return nil
}

// isjwtAlgorithmNone 检查给定的字符串是否表示"none"算法
func isjwtAlgorithmNone(alg string) bool {
	alg = strings.TrimSpace(alg)
	return strings.ToLower(alg) == "none"
}

var (
	publicIP string    // 缓存的公共IP地址
	getOnce  sync.Once // 确保只获取一次IP地址的同步对象
)

// GetPublicIP 获取主机的公共IP地址
func GetPublicIP() string {
	getOnce.Do(func() {
		publicIP, _ = iputil.WhatsMyIP()
	})
	return publicIP
}

// aggregate 将字符串数组按字母顺序排序并格式化为带缩进的字符串
// @param values []string: 要处理的字符串数组
// @return string string: 排序和格式化后的字符串
func aggregate(values []string) string {
	sort.Strings(values)

	builder := &strings.Builder{}
	for _, value := range values {
		builder.WriteRune('\t')
		builder.WriteString(value)
		builder.WriteRune('\n')
	}
	return builder.String()
}
