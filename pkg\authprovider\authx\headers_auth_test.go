// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-16 14:53:00
// FilePath: /yaml_scan/pkg/authprovider/authx/headers_auth_test.go
// Description: 
package authx

import (
	"net/http"
	"testing"
	"yaml_scan/pkg/retryablehttp"

	"github.com/stretchr/testify/require"
)

// TestHeadersAuthStrategy_Apply 测试请求头认证策略的Apply方法
// 确保它能正确设置HTTP请求的自定义请求头
func TestHeadersAuthStrategy_Apply(t *testing.T) {
	// 创建测试用的密钥数据，包含多个请求头
	secret := &Secret{
		Headers: []KV{
			{Key: "X-API-Key", Value: "test-api-key-123"},
			{Key: "X-Custom-Header", Value: "custom-value"},
		},
	}

	// 创建请求头认证策略
	strategy := NewHeadersAuthStrategy(secret)

	// 创建测试用的HTTP请求
	req, err := http.NewRequest("GET", "https://example.com", nil)
	require.NoError(t, err, "创建HTTP请求不应该返回错误")

	// 应用认证策略
	strategy.Apply(req)

	// 验证请求头是否正确设置
	apiKeyHeader := req.Header.Get("X-API-Key")
	require.Equal(t, "test-api-key-123", apiKeyHeader, "X-API-Key头应该被正确设置")

	customHeader := req.Header.Get("X-Custom-Header")
	require.Equal(t, "custom-value", customHeader, "X-Custom-Header头应该被正确设置")
}

// TestHeadersAuthStrategy_ApplyOnRR 测试请求头认证策略的ApplyOnRR方法
// 确保它能正确设置可重试HTTP请求的自定义请求头
func TestHeadersAuthStrategy_ApplyOnRR(t *testing.T) {
	// 创建测试用的密钥数据，包含多个请求头
	secret := &Secret{
		Headers: []KV{
			{Key: "X-API-Key", Value: "test-api-key-123"},
			{Key: "X-Custom-Header", Value: "custom-value"},
		},
	}

	// 创建请求头认证策略
	strategy := NewHeadersAuthStrategy(secret)

	// 创建测试用的可重试HTTP请求
	httpReq, err := http.NewRequest("GET", "https://example.com", nil)
	require.NoError(t, err, "创建HTTP请求不应该返回错误")
	req, err := retryablehttp.FromRequest(httpReq)
	require.NoError(t, err, "创建可重试HTTP请求不应该返回错误")

	// 应用认证策略
	strategy.ApplyOnRR(req)

	// 验证请求头是否正确设置
	apiKeyHeader := req.Header.Get("X-API-Key")
	require.Equal(t, "test-api-key-123", apiKeyHeader, "X-API-Key头应该被正确设置")

	customHeader := req.Header.Get("X-Custom-Header")
	require.Equal(t, "custom-value", customHeader, "X-Custom-Header头应该被正确设置")
}

// TestNewHeadersAuthStrategy 测试创建请求头认证策略的函数
// 确保它能正确创建HeadersAuthStrategy对象
func TestNewHeadersAuthStrategy(t *testing.T) {
	// 创建测试用的密钥数据
	secret := &Secret{
		Headers: []KV{
			{Key: "X-API-Key", Value: "test-api-key-123"},
		},
	}

	// 创建请求头认证策略
	strategy := NewHeadersAuthStrategy(secret)

	// 验证策略对象是否正确创建
	require.NotNil(t, strategy, "应该创建非空的策略对象")
	require.Equal(t, secret, strategy.Data, "策略对象应该包含正确的密钥数据")
}


