// 
// Author: chenjb
// Version: V1.0
// Date: 2025-05-30 14:40:46
// FilePath: /yaml_scan/pkg/dsl/deserialization/deserialization_test.go
// Description: 
package deserialization

import (
	"encoding/base64"
	"encoding/hex"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestGenerateJavaGadget 测试生成Java反序列化payload的主函数
func TestGenerateJavaGadget(t *testing.T) {
	// 测试不同类型的gadget生成
	t.Run("测试不同gadget类型生成", func(t *testing.T) {
		testCases := []struct {
			name     string
			gadget   string
			cmd      string
			encoding string
			hasData  bool // 是否应该返回非空结果
		}{
			{"DNS gadget", "dns", "http://example.com", "raw", true},
			{"JDK7u21 gadget", "jdk7u21", "calc.exe", "base64-raw", true},
			{"JDK8u20 gadget", "jdk8u20", "calc.exe", "base64-raw", true},
			{"Commons-Collections31 gadget", "commons-collections3.1", "calc.exe", "raw", true},
			{"Commons-Collections40 gadget", "commons-collections4.0", "calc.exe", "raw", true},
			{"Groovy1 gadget", "groovy1", "calc.exe", "raw", true},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				result := GenerateJavaGadget(tc.gadget, tc.cmd, tc.encoding)
				if tc.hasData {
					require.NotEmpty(t, result, "应该生成非空payload")
				} else {
					require.Empty(t, result, "不支持的gadget应该返回空字符串")
				}
			})
		}
	})

	// 测试不同编码方式
	t.Run("测试不同编码方式", func(t *testing.T) {
		gadget := "commons-collections3.1"
		cmd := "echo test"
		encodings := []string{"raw", "hex", "base64-raw", "gzip-base64"}

		results := make(map[string]string)
		for _, encoding := range encodings {
			result := GenerateJavaGadget(gadget, cmd, encoding)
			require.NotEmpty(t, result, "使用 %s 编码应该生成非空payload", encoding)
			results[encoding] = result
		}

		// 验证不同编码方式生成的结果不同
		for i, enc1 := range encodings {
			for j, enc2 := range encodings {
				if i != j {
					require.NotEqual(t, results[enc1], results[enc2], 
						"不同编码方式 %s 和 %s 应该生成不同的结果", enc1, enc2)
				}
			}
		}

		// 验证hex编码生成的结果是有效的十六进制字符串
		_, err := hex.DecodeString(results["hex"])
		require.NoError(t, err, "hex编码应该生成有效的十六进制字符串")

		// 验证base64编码生成的结果是有效的base64字符串
		base64String := strings.ReplaceAll(results["base64-raw"], "%2B", "+")
		_, err = base64.StdEncoding.DecodeString(base64String)
		require.NoError(t, err, "base64编码应该生成有效的base64字符串")
	})
}

// TestGadgetEncodingHelper 测试payload编码辅助函数
func TestGadgetEncodingHelper(t *testing.T) {
	testData := []byte("test data for encoding")

	t.Run("raw编码", func(t *testing.T) {
		result := gadgetEncodingHelper(testData, "raw")
		require.Equal(t, string(testData), result, "raw编码应该返回原始数据")
	})

	t.Run("hex编码", func(t *testing.T) {
		result := gadgetEncodingHelper(testData, "hex")
		expected := hex.EncodeToString(testData)
		require.Equal(t, expected, result, "hex编码结果不符合预期")
	})

	t.Run("base64-raw编码", func(t *testing.T) {
		result := gadgetEncodingHelper(testData, "base64-raw")
		expected := base64.StdEncoding.EncodeToString(testData)
		require.Equal(t, expected, result, "base64-raw编码结果不符合预期")
	})

	t.Run("默认编码(URL安全base64)", func(t *testing.T) {
		result := gadgetEncodingHelper(testData, "")
		expected := strings.ReplaceAll(base64.StdEncoding.EncodeToString(testData), "+", "%2B")
		require.Equal(t, expected, result, "默认编码结果不符合预期")
	})
}

// TestUrlsafeBase64Encode 测试URL安全的base64编码函数
func TestUrlsafeBase64Encode(t *testing.T) {
	// 生成包含"+"号的测试数据
	testData := []byte{251, 239, 190} // 这将生成一个包含"+"的base64编码

	result := urlsafeBase64Encode(testData)
	
	// 标准base64编码结果
	stdBase64 := base64.StdEncoding.EncodeToString(testData)
	
	// 验证结果中的"+"被替换为"%2B"
	expected := strings.ReplaceAll(stdBase64, "+", "%2B")
	require.Equal(t, expected, result, "URL安全base64编码应该将+替换为%2B")
	
	// 验证结果中不包含"+"
	require.NotContains(t, result, "+", "URL安全base64编码结果不应包含+号")
}

// TestGenerateDNSPayload 测试生成DNS交互型payload的函数
func TestGenerateDNSPayload(t *testing.T) {
	t.Run("有效URL", func(t *testing.T) {
		url := "http://example.com"
		result := generateDNSPayload(url)
		require.NotNil(t, result, "对有效URL应该生成非空payload")
		
		// 检查payload中是否包含主机名
		payload := string(result)
		require.Contains(t, payload, "example.com", "payload应该包含主机名")
		require.Contains(t, payload, "http", "payload应该包含协议")
	})
	
	t.Run("无效URL", func(t *testing.T) {
		url := "://invalid"
		result := generateDNSPayload(url)
		require.Nil(t, result, "对无效URL应该返回nil")
	})
}

// TestPayloadGeneration 测试不同gadget的payload生成
func TestPayloadGeneration(t *testing.T) {
	cmd := "echo test"
	
	// 测试各种gadget生成函数
	t.Run("Commons-Collections31", func(t *testing.T) {
		result := generateCommonsCollections31Payload(cmd)
		require.NotNil(t, result, "应该生成非空payload")
		require.Greater(t, len(result), 100, "payload长度应该合理")
	})
	
	t.Run("Commons-Collections40", func(t *testing.T) {
		result := generateCommonsCollections40Payload(cmd)
		require.NotNil(t, result, "应该生成非空payload")
		require.Greater(t, len(result), 100, "payload长度应该合理")
	})
	
	t.Run("Groovy1", func(t *testing.T) {
		result := generateGroovy1Payload(cmd)
		require.NotNil(t, result, "应该生成非空payload")
		require.Greater(t, len(result), 100, "payload长度应该合理")
	})
	
	t.Run("JDK7u21", func(t *testing.T) {
		result := generatejdk7u21Payload(cmd)
		require.NotNil(t, result, "应该生成非空payload")
		require.Greater(t, len(result), 100, "payload长度应该合理")
	})
	
	t.Run("JDK8u20", func(t *testing.T) {
		result := generatejdk8u20Payload(cmd)
		require.NotNil(t, result, "应该生成非空payload")
		require.Greater(t, len(result), 100, "payload长度应该合理")
	})
} 

