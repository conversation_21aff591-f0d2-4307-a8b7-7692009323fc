//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-29 16:28:00
//FilePath: /yaml_scan/utils/maps/synclock_map.go
//Description:

package mapsutil

import (
	"errors"
	"sync"
	"sync/atomic"
)

// SyncLock 为通用映射提供同步和锁定功能。
type SyncLockMap[K, V comparable] struct {
	ReadOnly atomic.Bool  // 标记映射是否为只读
	mu       sync.RWMutex // 读写锁
	Map      Map[K, V]    // 内部映射
}

// SyncLockMapOption 是一个函数类型，用于配置 SyncLockMap 的选项。
type SyncLockMapOption[K, V comparable] func(slm *SyncLockMap[K, V])

// NewSyncLockMap: 创建一个新的 SyncLockMap。
// 如果提供了现有的映射，则使用它；否则，创建一个新的映射。
func NewSyncLockMap[K, V comparable](options ...SyncLockMapOption[K, V]) *SyncLockMap[K, V] {
	// 创建一个新的 SyncLockMap 实例
	slm := &SyncLockMap[K, V]{}

	// 应用所有提供的选项
	for _, option := range options {
		option(slm)
	}
	// 如果没有提供现有的映射，则创建一个新的映射
	if slm.Map == nil {
		slm.Map = make(Map[K, V])
	}

	return slm
}

// Get 获取指定键的值，返回值和一个布尔值，指示键是否存在。
func (s *SyncLockMap[K, V]) Get(k K) (V, bool) {
	// 获取读锁
	s.mu.RLock()
	defer s.mu.RUnlock()
	// 从映射中获取值
	v, ok := s.Map[k]

	return v, ok
}

// Set 设置指定键的值，如果映射为只读，则返回错误。
func (s *SyncLockMap[K, V]) Set(k K, v V) error {
	if s.ReadOnly.Load() {
		return errors.New("syncLockMap map is currently in read-only mode")
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	s.Map[k] = v

	return nil
}

// Delete: 从 SyncLockMap 中删除指定的键 k。
func (s *SyncLockMap[K, V]) Delete(k K) {
	s.mu.Lock()
	defer s.mu.Unlock()

	delete(s.Map, k)
}

// Iterate 使用回调函数同步地遍历 SyncLockMap 中的所有键值对。
func (s *SyncLockMap[K, V]) Iterate(f func(k K, v V) error) error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	for k, v := range s.Map {
		if err := f(k, v); err != nil {
			return err
		}
	}
	return nil
}

// Clear：清空map
func (s *SyncLockMap[K, V]) Clear() bool {
	s.mu.Lock()
	defer s.mu.Unlock()

	return s.Map.Clear()
}
