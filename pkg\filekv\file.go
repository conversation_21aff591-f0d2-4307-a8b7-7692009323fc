// Author: chenjb
// Version: V1.0
// Date: 2025-05-16 10:42:47
// FilePath: /yaml_scan/pkg/hybridMap/file/file.go
// Description:基于文件的键值存储实现，支持多种去重策略和压缩存储功能
package filekv

import (
	"bufio"
	"bytes"
	"compress/zlib"
	"errors"
	"io"
	"os"
	"sync"

	fileutil "yaml_scan/utils/file"
	permissionutil "yaml_scan/utils/permission"

	"github.com/bits-and-blooms/bloom/v3"
	lru "github.com/hashicorp/golang-lru/v2"
	"github.com/syndtr/goleveldb/leveldb"
)

var (
	// ErrItemExists 表示项目已经存在（用于去重处理）
	ErrItemExists = errors.New("item already exist")
	// ErrItemFiltered 表示项目被过滤器过滤掉了
	ErrItemFiltered = errors.New("item filtered")
)

// FileDB - 文件数据库的实现结构
// 支持多种去重策略和压缩存储，可以高效处理大量数据
type FileDB struct {
	stats       Stats                        // 存储数据库统计信息
	options     Options                      // 数据库配置选项
	tmpDbName   string                       // 临时数据库文件名 用于中间处理过程
	tmpDb       *os.File                     // 临时数据库文件句柄
	tmpDbWriter io.WriteCloser               // 临时数据库写入器
	db          *os.File                     // 主数据库文件句柄 存储最终去重后的数据
	dbWriter    io.WriteCloser               // 主数据库写入器
	mapdb       map[string]struct{}          // 使用Go的map进行去重
	mdb         *lru.Cache[string, struct{}] // LRU缓存，用于有限内存下的去重
	bdb         *bloom.BloomFilter           // 布隆过滤器，用于概率性去重
	ddb         *leveldb.DB                  // 基于磁盘的数据库，用于大规模去重
	ddbName     string                       // 磁盘数据库路径

	sync.RWMutex // 读写锁，保证并发安全
}

// Open 创建并打开一个新的基于文件的数据库
// @param options Options:  配置数据库行为的选项
// @return *FileDB *FileDB:  创建的文件数据库实例
// @return error error: 如果发生错误，返回相关错误信息
func Open(options Options) (*FileDB, error) {
	// 打开主数据库文件，使用读写、创建和追加模式
	// 主数据库文件用于存储最终处理后的数据
	db, err := os.OpenFile(options.Path, os.O_RDWR|os.O_CREATE|os.O_APPEND, permissionutil.ConfigFilePermission)
	if err != nil {
		return nil, err
	}

	// 创建临时文件用于处理过程中的数据
	tmpFileName, err := fileutil.GetTempFileName()
	if err != nil {
		return nil, err
	}
	// 打开临时数据库文件，使用读写、创建和截断模式
	// 截断模式确保每次打开时都是一个空文件
	tmpDb, err := os.OpenFile(tmpFileName, os.O_RDWR|os.O_CREATE|os.O_TRUNC, permissionutil.TempFilePermission)
	if err != nil {
		return nil, err
	}

	// 创建文件数据库实例
	fdb := &FileDB{
		tmpDbName: tmpFileName,
		options:   options,
		db:        db,
		tmpDb:     tmpDb,
	}

	// 根据选项决定是否使用压缩
	if options.Compress {
		fdb.tmpDbWriter = zlib.NewWriter(fdb.tmpDb)
		fdb.dbWriter = zlib.NewWriter(fdb.db)
	} else {
		// 否则直接使用文件作为写入器
		fdb.tmpDbWriter = fdb.tmpDb
		fdb.dbWriter = fdb.db
	}

	return fdb, nil
}

// Process 处理添加的文件/切片/元素，将临时数据库中的内容处理后写入主数据库
// @receiver fdb 
// @return error error:  如果处理过程中发生错误，返回相关错误信息
func (fdb *FileDB) Process() error {
	// 如果使用了压缩，需要特别关闭写入器
	if fdb.options.Compress {
		if err := fdb.tmpDbWriter.Close(); err != nil {
			return err
		}
	}

	// 关闭临时文件并重新打开它进行读取
	_ = fdb.tmpDb.Close()
	var err error
	fdb.tmpDb, err = os.Open(fdb.tmpDbName)
	if err != nil {
		return err
	}

	var maxItems uint
	switch {
		// 如果指定了最大项目数，使用指定值
	case fdb.options.MaxItems > 0:
		maxItems = fdb.options.MaxItems
	case fdb.stats.NumberOfAddedItems < MaxItems:
		// 如果添加的项目数小于默认最大值，使用添加的项目数
		maxItems = fdb.stats.NumberOfAddedItems
	default:
		maxItems = MaxItems
	}

	// 根据去重策略初始化相应的数据结构
	switch fdb.options.Dedupe {
	case MemoryMap:
		// 使用Go的普通map进行去重
		fdb.mapdb = make(map[string]struct{}, maxItems)
	case MemoryLRU:
		// 使用LRU缓存进行去重，限制内存使用
		fdb.mdb, err = lru.New[string, struct{}](int(maxItems))
		if err != nil {
			return err
		}
	case MemoryFilter:
		// 使用布隆过滤器进行概率性去重
		fdb.bdb = bloom.NewWithEstimates(maxItems, FpRatio)
	case DiskFilter:
		// 使用磁盘数据库进行大规模去重
		fdb.ddbName, err = os.MkdirTemp("", fileutil.ExecutableName())
		if err != nil {
			return err
		}
		fdb.ddb, err = leveldb.OpenFile(fdb.ddbName, nil)
		if err != nil {
			return err
		}
	}

	// 准备从临时文件读取数据
	var tmpDbReader io.Reader
	// 如果使用了压缩，需要创建解压缩读取器
	if fdb.options.Compress {
		var err error
		tmpDbReader, err = zlib.NewReader(fdb.tmpDb)
		if err != nil {
			return err
		}
	} else {
		tmpDbReader = fdb.tmpDb
	}

	// 逐行读取临时文件内容，应用去重策略后写入主数据库
	sc := bufio.NewScanner(tmpDbReader)
	buf := make([]byte, BufferSize)
	sc.Buffer(buf, BufferSize)
	for sc.Scan() {
		_ = fdb.Set(sc.Bytes(), nil)
	}

	fdb.tmpDb.Close()

	// 将数据刷新到磁盘，关闭主数据库
	fdb.dbWriter.Close()
	fdb.db.Close()

	// 清理去重数据结构，释放资源
	switch fdb.options.Dedupe {
	case MemoryMap:
		fdb.mapdb = nil
	case MemoryLRU:
		fdb.mdb.Purge()
	case MemoryFilter:
		fdb.bdb.ClearAll()
	case DiskFilter:
		fdb.ddb.Close()
		os.RemoveAll(fdb.ddbName)
	}

	return nil
}

// Reset 重置数据库状态
// @receiver fdb 
// @return error error: 如果重置过程中发生错误，返回相关错误信息
func (fdb *FileDB) Reset() error {
	// 清理去重缓存
	switch fdb.options.Dedupe {
	case MemoryMap:
		fdb.mapdb = nil
	case MemoryLRU:
		fdb.mdb.Purge()
	case MemoryFilter:
		fdb.bdb.ClearAll()
	case DiskFilter:
		// 对于磁盘数据库，需要关闭、删除并重新打开
		fdb.ddb.Close()
		os.RemoveAll(fdb.ddbName)
		var err error
		fdb.ddb, err = leveldb.OpenFile(fdb.ddbName, nil)
		if err != nil {
			return err
		}
	}

	// 重置临时文件
	fdb.tmpDb.Close()
	var err error
	// 重新打开临时文件，使用截断模式清空内容
	fdb.tmpDb, err = os.Create(fdb.tmpDbName)
	if err != nil {
		return err
	}

	// 重置主数据库文件
	fdb.db.Close()
	fdb.db, err = os.Create(fdb.tmpDbName)
	if err != nil {
		return err
	}

	// 重新初始化写入器
	if fdb.options.Compress {
		fdb.tmpDbWriter = zlib.NewWriter(fdb.tmpDb)
		fdb.dbWriter = zlib.NewWriter(fdb.db)
	} else {
		fdb.tmpDbWriter = fdb.tmpDb
		fdb.dbWriter = fdb.db
	}

	// 重置统计信息
	fdb.stats = Stats{}

	return nil
}

// Size 获取数据库文件的大小
// @receiver fdb 
// @return int64 int64: 文件大小（字节）
func (fdb *FileDB) Size() int64 {
	osstat, err := fdb.db.Stat()
	if err != nil {
		return 0
	}
	return osstat.Size()
}

// Close  关闭数据库并清理资源
// @receiver fdb 
func (fdb *FileDB) Close() {
	tmpDBFilename := fdb.tmpDb.Name()
	_ = fdb.tmpDb.Close()
	os.RemoveAll(tmpDBFilename)

	_ = fdb.db.Close()
	dbFilename := fdb.db.Name()
	if fdb.options.Cleanup {
		os.RemoveAll(dbFilename)
	}

	if fdb.ddbName != "" {
		fdb.ddb.Close()
		os.RemoveAll(fdb.ddbName)
	}
}

// set 内部方法，设置键值
// @receiver fdb 
// @param k []byte: 键
// @param v []byte: 只值
// @return error error: 可能得错误
func (fdb *FileDB) set(k, v []byte) error {
	var s bytes.Buffer
	s.Write(k)
	s.WriteString(Separator)
	s.Write(v)
	s.WriteString(NewLine)
	_, err := fdb.dbWriter.Write(s.Bytes())
	if err != nil {
		return err
	}
	fdb.stats.NumberOfItems++
	return nil
}

// Set 设置键值
// @receiver fdb 
// @param k []byte: 键
// @param v []byte: 只值
// @return error error: 可能得错误
func (fdb *FileDB) Set(k, v []byte) error {
	// 根据去重策略检查键是否已存在
	switch fdb.options.Dedupe {
	case MemoryMap:
		if _, ok := fdb.mapdb[string(k)]; ok {
			fdb.stats.NumberOfDupedItems++
			return ErrItemExists
		}
		fdb.mapdb[string(k)] = struct{}{}
	case MemoryLRU:
		if ok, _ := fdb.mdb.ContainsOrAdd(string(k), struct{}{}); ok {
			fdb.stats.NumberOfDupedItems++
			return ErrItemExists
		}
	case MemoryFilter:
		if ok := fdb.bdb.TestOrAdd(k); ok {
			fdb.stats.NumberOfDupedItems++
			return ErrItemExists
		}
	case DiskFilter:
		if ok, err := fdb.ddb.Has(k, nil); err == nil && ok {
			fdb.stats.NumberOfDupedItems++
			return ErrItemExists
		} else if err == nil && !ok {
			_ = fdb.ddb.Put(k, []byte{}, nil)
		}
	}

	if fdb.shouldSkip(k, v) {
		fdb.stats.NumberOfFilteredItems++
		return ErrItemFiltered
	}

	fdb.stats.NumberOfItems++
	return fdb.set(k, v)
}

// Scan 扫描数据库中的所有项并应用处理函数
// @receiver fdb 
// @param handler func([]byte, []byte) error: 处理每个键值对的函数
// @return error error:  如果扫描或处理过程中发生错误，返回相关错误信息
func (fdb *FileDB) Scan(handler func([]byte, []byte) error) error {
	// 重新打开数据库文件进行读取
	dbCopy, err := os.Open(fdb.options.Path)
	if err != nil {
		return err
	}
	defer dbCopy.Close()

	var dbReader io.ReadCloser
	// 如果使用了压缩，创建解压缩读取器
	if fdb.options.Compress {
		dbReader, err = zlib.NewReader(dbCopy)
		if err != nil {
			return err
		}
	} else {
		dbReader = dbCopy
	}
	// 使用Scanner逐行读取
	sc := bufio.NewScanner(dbReader)
	buf := make([]byte, BufferSize)
	sc.Buffer(buf, BufferSize)
	for sc.Scan() {
		// 如果包含分隔符，则分割为键和值
		tokens := bytes.SplitN(sc.Bytes(), []byte(Separator), 2)
		var k, v []byte
		if len(tokens) > 0 {
			k = tokens[0]
		}
		if len(tokens) > 1 {
			v = tokens[1]
		}
		if err := handler(k, v); err != nil {
			return err
		}
	}
	return nil
}
