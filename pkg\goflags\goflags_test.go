package goflags

import (
	"bytes"
	"flag"
	"os"
	"testing"
)


func TestFlagDataHash(t *testing.T) {
	flag := &FlagData{short: "short", long: "This is long"}
	expectedHash := "v1_fc595b03a9cc01cc755e9233eb59aa39"
	if got := flag.Hash(); got != expectedHash {
		t.<PERSON><PERSON><PERSON>("Expected hash %q, got %q", expectedHash, got)
	}
}

func TestUniqueDeduper(t *testing.T) {
	deduper := newUniqueDeduper()

	flag1 := &FlagData{short: "flag1", long: "This is flag 1"}
	flag2 := &FlagData{short: "flag1", long: "This is flag 1"} // 重复的标志
	flag3 := &FlagData{short: "flag2", long: "This is flag 2"}

	if !deduper.isUnique(flag1) {
		t.Error("Expected flag1 to be unique")
	}
	if deduper.isUnique(flag2) {
		t.<PERSON>r("Expected flag2 to not be unique")
	}
	if !deduper.isUnique(flag3) {
		t.Error("Expected flag3 to be unique")
	}
}

// TestGenerateDefaultConfig 测试 generateDefaultConfig 函数
func TestGenerateDefaultConfig(t *testing.T) {
	var testValue string
	flagSet := &FlagSet{
		CommandLine:    flag.NewFlagSet("test", flag.ExitOnError),
		flagKeys: newInsertionOrderedMap(),
		Marshal: true, // 启用序列化
	}
	flagSet.StringVar(&testValue, "example", "default_value", "This is an example flag.")
	// 生成默认配置
	config := flagSet.generateDefaultConfig()

	// 检查生成的配置是否包含预期内容
	expectedOutput := "# goflags.test config file\n# generated by goflags\n\nexample: default_value\n"

	if !bytes.Equal(config, []byte(expectedOutput)) {
		t.Errorf("Expected config:\n%s\nGot:\n%s", expectedOutput, config)
	}
}


// TestReadConfigFile 测试 readConfigFile 函数
func TestReadConfigFile(t *testing.T) {
	// 创建一个临时配置文件
	configFile := "./test_config.yaml"
	defer os.Remove(configFile) // 测试结束后删除文件

	// 写入测试配置
	err := os.WriteFile(configFile, []byte("example: test_value\n"), 0644)
	if err != nil {
		t.Fatalf("创建配置文件时出错: %v", err)
	}

	// 创建 FlagSet 实例并定义标志
	flagSet := &FlagSet{
		CommandLine:    flag.NewFlagSet("test", flag.ExitOnError),
		configOnlyKeys: newInsertionOrderedMap(),
	}
	var exampleFlag string
	flagSet.CommandLine.StringVar(&exampleFlag, "example", "default", "an example flag")

	// 读取配置文件
	if err := flagSet.readConfigFile(configFile); err != nil {
		t.Errorf("读取配置文件时出错: %v", err)
	}

	// 检查标志值是否被正确设置
	if exampleFlag != "test_value" {
		t.Errorf("期望标志值为 'test_value', 但实际为 '%s'", exampleFlag)
	}
}