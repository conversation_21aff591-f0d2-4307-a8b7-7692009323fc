//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-21 10:51:00
//FilePath: /yaml_scan/pkg/goflags/enum_var_test.go
//Description:

package goflags

import (
	"testing"
)

func TestEnumVar(t *testing.T) {
	allowed := AllowdTypes{
		"option1": 0,
		"option2": 1,
		"option3": 2,
	}

	var value string
	enumVar := EnumVar{
		allowedTypes: allowed,
		value:       &value,
	}

	// 测试设置有效值
	if err := enumVar.Set("option1"); err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	if enumVar.String() != "option1" {
		t.<PERSON><PERSON><PERSON>("Expected value 'option1', got %s", enumVar.String())
	}

	// 测试设置无效值
	err := enumVar.Set("invalid")
	if err == nil {
		t.Fatal("Expected error for invalid value, got none")
	}
	expectedError := "allowed values are option1, option2, option3"
	if err.Error() != expectedError {
		t.<PERSON><PERSON>rf("Expected error message '%s', got '%s'", expectedError, err.Error())
	}
}