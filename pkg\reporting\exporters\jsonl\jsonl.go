// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-17 17:13:24
// FilePath: /yaml_scan/pkg/reporting/exporters/jsonl/jsonl.go
// Description: 
package jsonl


// Options contains the configuration options for JSONL exporter client
type Options struct {
	// File is the file to export found JSONL result to
	File string `yaml:"file"`
	// OmitRaw whether to exclude the raw request and response from the output
	OmitRaw bool `yaml:"omit-raw"`
	// BatchSize the number of records to keep in memory before writing them out to the JSONL file or 0 to disable
	// batching (default)
	BatchSize int `yaml:"batch-size"`
}
