// Author: chenjb
// Version: V1.0
// Date: 2025-07-18 11:32:06
// FilePath: /yaml_scan/pkg/wappalyzergo/fingerprint_cookies_test.go
// Description: fingerprint_cookies.go 的单元测试文件，测试基于HTTP Cookie的技术指纹识别功能

package wappalyzergo

import (
    "testing"

    "github.com/stretchr/testify/require"
)

// TestCheckCookies 测试Cookie指纹检查功能
func TestCheckCookies(t *testing.T) {
    wappalyzer, err := New()
    require.NoError(t, err, "创建实例不应失败")

    t.Run("检测PHP会话Cookie", func(t *testing.T) {
        cookies := []string{
            "PHPSESSID=abc123; Path=/",
            "other_cookie=value",
        }

        results := wappalyzer.checkCookies(cookies)
        require.NotNil(t, results, "结果不应为nil")
    })

    t.Run("检测Laravel框架Cookie", func(t *testing.T) {
        cookies := []string{
            "laravel_session=def456; HttpOnly; Secure",
            "XSRF-TOKEN=xyz789",
        }

        results := wappalyzer.checkCookies(cookies)
        require.NotNil(t, results, "结果不应为nil")
    })

    t.Run("检测Django CSRF Cookie", func(t *testing.T) {
        cookies := []string{
            "csrftoken=abc123; Path=/",
            "sessionid=def456; HttpOnly",
        }

        results := wappalyzer.checkCookies(cookies)
        require.NotNil(t, results, "结果不应为nil")
    })

    t.Run("检测ASP.NET会话Cookie", func(t *testing.T) {
        cookies := []string{
            "ASP.NET_SessionId=xyz789; Path=/; HttpOnly",
            "__RequestVerificationToken=abc123",
        }

        results := wappalyzer.checkCookies(cookies)
        require.NotNil(t, results, "结果不应为nil")
    })

    t.Run("检测Java/Tomcat会话Cookie", func(t *testing.T) {
        cookies := []string{
            "JSESSIONID=123ABC456DEF; Path=/app",
        }

        results := wappalyzer.checkCookies(cookies)
        require.NotNil(t, results, "结果不应为nil")
    })

    t.Run("检测WordPress Cookie", func(t *testing.T) {
        cookies := []string{
            "wordpress_logged_in_abc123=user%7C123456",
            "wp-settings-1=libraryContent%3Dbrowse",
        }

        results := wappalyzer.checkCookies(cookies)
        require.NotNil(t, results, "结果不应为nil")
    })

    t.Run("检测Drupal Cookie", func(t *testing.T) {
        cookies := []string{
            "SESS123abc=def456ghi789",
            "drupal_toolbar_collapsed=0",
        }

        results := wappalyzer.checkCookies(cookies)
        require.NotNil(t, results, "结果不应为nil")
    })

    t.Run("混合技术栈Cookie", func(t *testing.T) {
        cookies := []string{
            "PHPSESSID=abc123; Path=/",
            "laravel_session=def456; Secure",
            "csrftoken=xyz789; HttpOnly",
        }

        results := wappalyzer.checkCookies(cookies)
        require.NotNil(t, results, "结果不应为nil")
        t.Logf("检测到 %d 个技术指纹", len(results))
    })

    t.Run("空Cookie列表", func(t *testing.T) {
        cookies := []string{}

        results := wappalyzer.checkCookies(cookies)
        require.NotNil(t, results, "空Cookie结果不应为nil")
        require.Empty(t, results, "空Cookie应该返回空结果")
    })

    t.Run("无效Cookie格式", func(t *testing.T) {
        cookies := []string{
            "invalid_cookie_without_equals",
            "=value_without_name",
            "",
        }

        results := wappalyzer.checkCookies(cookies)
        require.NotNil(t, results, "无效Cookie结果不应为nil")
    })

    t.Run("包含特殊字符的Cookie", func(t *testing.T) {
        cookies := []string{
            "session_id=abc%20123%3D%3D; Path=/",
            "user_data=name%3DJohn%26age%3D30",
        }

        results := wappalyzer.checkCookies(cookies)
        require.NotNil(t, results, "特殊字符Cookie结果不应为nil")
    })
}

// TestNormalizeCookies 测试Cookie标准化功能
func TestNormalizeCookies(t *testing.T) {
    wappalyzer, err := New()
    require.NoError(t, err, "创建实例不应失败")

    t.Run("标准化基本Cookie", func(t *testing.T) {
        cookies := []string{
            "PHPSESSID=abc123",
            "sessionid=def456",
        }

        normalized := wappalyzer.normalizeCookies(cookies)
        require.NotNil(t, normalized, "标准化结果不应为nil")
        require.Len(t, normalized, 2, "应该有2个Cookie")
        require.Equal(t, "abc123", normalized["PHPSESSID"], "PHPSESSID值应该匹配")
        require.Equal(t, "def456", normalized["sessionid"], "sessionid值应该匹配")
    })

    t.Run("标准化带属性的Cookie", func(t *testing.T) {
        cookies := []string{
            "PHPSESSID=abc123; Path=/; HttpOnly",
            "laravel_session=def456; Secure; SameSite=Strict",
        }

        normalized := wappalyzer.normalizeCookies(cookies)
        require.NotNil(t, normalized, "标准化结果不应为nil")
        require.Len(t, normalized, 2, "应该有2个Cookie")
        require.Equal(t, "abc123", normalized["PHPSESSID"], "PHPSESSID值应该匹配")
        require.Equal(t, "def456", normalized["laravel_session"], "laravel_session值应该匹配")
    })

    t.Run("标准化包含等号的Cookie值", func(t *testing.T) {
        cookies := []string{
            "encoded_data=key1=value1&key2=value2",
            "base64_token=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxMjM0NTY3ODkwIn0=",
        }

        normalized := wappalyzer.normalizeCookies(cookies)
        require.NotNil(t, normalized, "标准化结果不应为nil")
        require.Len(t, normalized, 2, "应该有2个Cookie")
        require.Equal(t, "key1=value1&key2=value2", normalized["encoded_data"], "编码数据应该匹配")
        require.Equal(t, "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxMjM0NTY3ODkwIn0=", normalized["base64_token"], "Base64令牌应该匹配")
    })

    t.Run("标准化带空格的Cookie", func(t *testing.T) {
        cookies := []string{
            " PHPSESSID=abc123 ",
            "  sessionid=def456  ",
        }

        normalized := wappalyzer.normalizeCookies(cookies)
        require.NotNil(t, normalized, "标准化结果不应为nil")
        require.Len(t, normalized, 2, "应该有2个Cookie")
        require.Equal(t, "abc123", normalized["PHPSESSID"], "PHPSESSID值应该匹配")
        require.Equal(t, "def456", normalized["sessionid"], "sessionid值应该匹配")
    })

    t.Run("跳过无效格式的Cookie", func(t *testing.T) {
        cookies := []string{
            "PHPSESSID=abc123",
            "invalid_cookie_without_equals",
            "sessionid=def456",
            "=value_without_name",
            "",
        }

        normalized := wappalyzer.normalizeCookies(cookies)
        require.NotNil(t, normalized, "标准化结果不应为nil")
        require.Len(t, normalized, 2, "应该只有2个有效Cookie")
        require.Equal(t, "abc123", normalized["PHPSESSID"], "PHPSESSID值应该匹配")
        require.Equal(t, "def456", normalized["sessionid"], "sessionid值应该匹配")
    })

    t.Run("处理空Cookie值", func(t *testing.T) {
        cookies := []string{
            "empty_cookie=",
            "normal_cookie=value",
        }

        normalized := wappalyzer.normalizeCookies(cookies)
        require.NotNil(t, normalized, "标准化结果不应为nil")
        require.Len(t, normalized, 2, "应该有2个Cookie")
        require.Equal(t, "", normalized["empty_cookie"], "空Cookie值应该为空字符串")
        require.Equal(t, "value", normalized["normal_cookie"], "正常Cookie值应该匹配")
    })

    t.Run("处理重复Cookie名称", func(t *testing.T) {
        cookies := []string{
            "sessionid=first_value",
            "sessionid=second_value",
        }

        normalized := wappalyzer.normalizeCookies(cookies)
        require.NotNil(t, normalized, "标准化结果不应为nil")
        require.Len(t, normalized, 1, "重复Cookie应该只有一个")
        require.Equal(t, "second_value", normalized["sessionid"], "应该使用最后一个值")
    })

    t.Run("空Cookie列表", func(t *testing.T) {
        cookies := []string{}

        normalized := wappalyzer.normalizeCookies(cookies)
        require.NotNil(t, normalized, "空列表结果不应为nil")
        require.Empty(t, normalized, "空列表应该返回空映射")
    })

    t.Run("处理特殊字符Cookie", func(t *testing.T) {
        cookies := []string{
            "special_chars=value%20with%20spaces",
            "unicode_cookie=测试值",
            "symbols=!@#$%^&*()",
        }

        normalized := wappalyzer.normalizeCookies(cookies)
        require.NotNil(t, normalized, "特殊字符结果不应为nil")
        require.Len(t, normalized, 3, "应该有3个Cookie")
        require.Equal(t, "value%20with%20spaces", normalized["special_chars"], "编码字符应该保持原样")
        require.Equal(t, "测试值", normalized["unicode_cookie"], "Unicode字符应该保持原样")
        require.Equal(t, "!@#$%^&*()", normalized["symbols"], "符号应该保持原样")
    })
}

// TestFindSetCookie 测试从HTTP头部提取Set-Cookie值
func TestFindSetCookie(t *testing.T) {
    wappalyzer, err := New()
    require.NoError(t, err, "创建实例不应失败")

    t.Run("提取基本Set-Cookie", func(t *testing.T) {
        headers := map[string]string{
            "set-cookie": "PHPSESSID=abc123; Path=/",
        }

        cookies := wappalyzer.findSetCookie(headers)
        require.NotNil(t, cookies, "Cookie列表不应为nil")
        require.NotEmpty(t, cookies, "Cookie列表不应为空")
    })

    t.Run("提取多个Cookie（空格分隔）", func(t *testing.T) {
        headers := map[string]string{
            "set-cookie": "PHPSESSID=abc123 sessionid=def456",
        }

        cookies := wappalyzer.findSetCookie(headers)
        require.NotNil(t, cookies, "Cookie列表不应为nil")
        require.NotEmpty(t, cookies, "Cookie列表不应为空")
    })

    t.Run("提取多个Cookie（逗号分隔）", func(t *testing.T) {
        headers := map[string]string{
            "set-cookie": "PHPSESSID=abc123,sessionid=def456",
        }

        cookies := wappalyzer.findSetCookie(headers)
        require.NotNil(t, cookies, "Cookie列表不应为nil")
        require.NotEmpty(t, cookies, "Cookie列表不应为空")
    })

    t.Run("提取多个Cookie（分号分隔）", func(t *testing.T) {
        headers := map[string]string{
            "set-cookie": "PHPSESSID=abc123;Path=/;HttpOnly",
        }

        cookies := wappalyzer.findSetCookie(headers)
        require.NotNil(t, cookies, "Cookie列表不应为nil")
        require.NotEmpty(t, cookies, "Cookie列表不应为空")
    })

    t.Run("提取复杂格式的Set-Cookie", func(t *testing.T) {
        headers := map[string]string{
            "set-cookie": "PHPSESSID=abc123; Path=/; HttpOnly sessionid=def456; Secure laravel_session=xyz789",
        }

        cookies := wappalyzer.findSetCookie(headers)
        require.NotNil(t, cookies, "Cookie列表不应为nil")
        require.NotEmpty(t, cookies, "Cookie列表不应为空")
    })

    t.Run("处理混合分隔符", func(t *testing.T) {
        headers := map[string]string{
            "set-cookie": "PHPSESSID=abc123,sessionid=def456;Path=/ laravel_session=xyz789",
        }

        cookies := wappalyzer.findSetCookie(headers)
        require.NotNil(t, cookies, "Cookie列表不应为nil")
        require.NotEmpty(t, cookies, "Cookie列表不应为空")
    })

    t.Run("无Set-Cookie头部", func(t *testing.T) {
        headers := map[string]string{
            "content-type": "text/html",
            "server":       "nginx",
        }

        cookies := wappalyzer.findSetCookie(headers)
        require.Nil(t, cookies, "无Set-Cookie应该返回nil")
    })

    t.Run("空Set-Cookie值", func(t *testing.T) {
        headers := map[string]string{
            "set-cookie": "",
        }

        cookies := wappalyzer.findSetCookie(headers)
        require.NotNil(t, cookies, "空值结果不应为nil")
        require.Empty(t, cookies, "空值应该返回空列表")
    })

    t.Run("只包含空格的Set-Cookie", func(t *testing.T) {
        headers := map[string]string{
            "set-cookie": "   ",
        }

        cookies := wappalyzer.findSetCookie(headers)
        require.NotNil(t, cookies, "空格结果不应为nil")
        require.Empty(t, cookies, "只有空格应该返回空列表")
    })

    t.Run("包含空字符串的Set-Cookie", func(t *testing.T) {
        headers := map[string]string{
            "set-cookie": "PHPSESSID=abc123  sessionid=def456",
        }

        cookies := wappalyzer.findSetCookie(headers)
        require.NotNil(t, cookies, "Cookie列表不应为nil")
        require.NotEmpty(t, cookies, "Cookie列表不应为空")
        // 验证空字符串被过滤
        for _, cookie := range cookies {
            require.NotEmpty(t, cookie, "Cookie不应为空字符串")
        }
    })

    t.Run("处理特殊字符的Set-Cookie", func(t *testing.T) {
        headers := map[string]string{
            "set-cookie": "encoded_cookie=value%20with%20spaces; Path=/test unicode_cookie=测试值",
        }

        cookies := wappalyzer.findSetCookie(headers)
        require.NotNil(t, cookies, "特殊字符Cookie列表不应为nil")
        require.NotEmpty(t, cookies, "特殊字符Cookie列表不应为空")
    })
}

// TestKeyValuePairLength 测试常量定义
func TestKeyValuePairLength(t *testing.T) {
    t.Run("验证常量值", func(t *testing.T) {
        require.Equal(t, 2, keyValuePairLength, "键值对长度应该为2")
    })
}

// TestCookieIntegration 测试Cookie处理的完整流程
func TestCookieIntegration(t *testing.T) {
    wappalyzer, err := New()
    require.NoError(t, err, "创建实例不应失败")

    t.Run("完整Cookie处理流程", func(t *testing.T) {
        // 模拟HTTP响应头
        headers := map[string]string{
            "set-cookie": "PHPSESSID=abc123; Path=/ laravel_session=def456; Secure csrftoken=xyz789; HttpOnly",
        }

        // 1. 从头部提取Cookie
        cookieStrings := wappalyzer.findSetCookie(headers)
        require.NotNil(t, cookieStrings, "提取的Cookie不应为nil")
        require.NotEmpty(t, cookieStrings, "提取的Cookie不应为空")

        // 2. 标准化Cookie
        normalized := wappalyzer.normalizeCookies(cookieStrings)
        require.NotNil(t, normalized, "标准化Cookie不应为nil")
        require.NotEmpty(t, normalized, "标准化Cookie不应为空")

        // 3. 进行指纹识别
        results := wappalyzer.checkCookies(cookieStrings)
        require.NotNil(t, results, "指纹识别结果不应为nil")

        t.Logf("提取到 %d 个Cookie字符串", len(cookieStrings))
        t.Logf("标准化为 %d 个Cookie键值对", len(normalized))
        t.Logf("识别到 %d 个技术指纹", len(results))
    })

    t.Run("真实场景Cookie测试", func(t *testing.T) {
        // 模拟真实的WordPress网站Cookie
        cookies := []string{
            "wordpress_test_cookie=WP Cookie check",
            "wordpress_logged_in_hash=username%7C1234567890%7Chash",
            "wp-settings-1=libraryContent%3Dbrowse%26editor%3Dtinymce",
            "wp-settings-time-1=1234567890",
        }

        results := wappalyzer.checkCookies(cookies)
        require.NotNil(t, results, "WordPress Cookie结果不应为nil")
    })

    t.Run("真实场景Laravel Cookie测试", func(t *testing.T) {
        // 模拟真实的Laravel应用Cookie
        cookies := []string{
            "laravel_session=eyJpdiI6IjdGM3JQVjJhOUNiVGhxdz09IiwidmFsdWUiOiJoK3JcL0E9PSIsIm1hYyI6IjEyMzQ1Njc4OTAifQ%3D%3D",
            "XSRF-TOKEN=eyJpdiI6IjdGM3JQVjJhOUNiVGhxdz09IiwidmFsdWUiOiJoK3JcL0E9PSIsIm1hYyI6IjEyMzQ1Njc4OTAifQ%3D%3D",
            "remember_web_hash=eyJpdiI6IjdGM3JQVjJhOUNiVGhxdz09IiwidmFsdWUiOiJoK3JcL0E9PSIsIm1hYyI6IjEyMzQ1Njc4OTAifQ%3D%3D",
        }

        results := wappalyzer.checkCookies(cookies)
        require.NotNil(t, results, "Laravel Cookie结果不应为nil")
    })

    t.Run("真实场景Django Cookie测试", func(t *testing.T) {
        // 模拟真实的Django应用Cookie
        cookies := []string{
            "csrftoken=abc123def456ghi789jkl012mno345pqr678stu901vwx234yz",
            "sessionid=abc123def456ghi789jkl012mno345pqr678stu901vwx234yz",
            "django_language=zh-cn",
        }

        results := wappalyzer.checkCookies(cookies)
        require.NotNil(t, results, "Django Cookie结果不应为nil")
    })
}