//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-23 20:21:20
// FilePath: /yaml_scan/pkg/tlsx/tlsx/tls/tls.go
// Description: 标准Go TLS库实现的TLS客户端

package tls

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"errors"
	"net"
	"os"
	"time"
	"yaml_scan/pkg/fastdialer"
	"yaml_scan/pkg/gologger"
	"yaml_scan/pkg/tlsx/output/stats"
	"yaml_scan/pkg/tlsx/tlsx/clients"
	"yaml_scan/utils/conn"
	errorutil "yaml_scan/utils/errors"
	iputil "yaml_scan/utils/ip"
	stringsutil "yaml_scan/utils/strings"

	"github.com/rs/xid"
)

// Client  是使用crypto/tls的TLS抓取客户端
// 该结构体实现了clients.Implementation接口，提供标准TLS连接功能
type Client struct {
	dialer    *fastdialer.Dialer // 用于创建网络连接
	tlsConfig *tls.Config        // TLS配置
	options   *clients.Options   // TLS连接选项
}

// versionToTLSVersionString 将TLS版本号转换为版本字符串
// 例如将tls.VersionTLS12转换为"tls12"
var versionToTLSVersionString = map[uint16]string{
	tls.VersionTLS10: "tls10", // TLS 1.0
	tls.VersionTLS11: "tls11", // TLS 1.1
	tls.VersionTLS12: "tls12", // TLS 1.2
	tls.VersionTLS13: "tls13", // TLS 1.3
}

// versionStringToTLSVersion 将TLS版本字符串转换为版本号
// 例如将"tls12"转换为tls.VersionTLS12
var versionStringToTLSVersion = map[string]uint16{
	"tls10": tls.VersionTLS10, // TLS 1.0
	"tls11": tls.VersionTLS11, // TLS 1.1
	"tls12": tls.VersionTLS12, // TLS 1.2
	"tls13": tls.VersionTLS13, // TLS 1.3
}

// New 创建一个新的使用crypto/tls的抓取客户端
// @param options *clients.Options:  TLS连接选项
// @return *Client *Client: 创建的TLS客户端
// @return error error: 可能的错误
func New(options *clients.Options) (*Client, error) {
	// 创建基本客户端
	c := &Client{
		dialer: options.Fastdialer,
		tlsConfig: &tls.Config{
			MinVersion:         tls.VersionTLS10,                 // 默认最小支持TLS 1.0
			MaxVersion:         tls.VersionTLS13,                 // 默认最大支持TLS 1.3
			InsecureSkipVerify: !options.VerifyServerCertificate, // 是否验证服务器证书
		},
		options: options,
	}

	// 如果没有提供快速拨号器，创建一个默认的拨号器
	if c.dialer == nil {
		var err error
		c.dialer, err = fastdialer.NewDialer(fastdialer.DefaultOptions)
		if err != nil {
			return nil, err
		}
	}

	// 如果指定了密码套件，使用自定义密码套件
	if len(options.Ciphers) > 0 {
		if customCiphers, err := toTLSCiphers(options.Ciphers); err != nil {
			return nil, errorutil.NewWithTag("ctls", "could not get tls ciphers").Wrap(err)
		} else {
			c.tlsConfig.CipherSuites = customCiphers
		}
	} else {
		// 除非明确指定，客户端应该所有支持的密码套件
		// 注意: Go标准库默认只支持一个安全/默认的密码套件列表
		c.tlsConfig.CipherSuites = AllCiphers
	}

	// 如果提供了CA证书，配置根CA证书
	if options.CACertificate != "" {
		caCert, err := os.ReadFile(options.CACertificate)
		if err != nil {
			return nil, errorutil.NewWithTag("ctls", "could not read ca certificate").Wrap(err)
		}
		certPool := x509.NewCertPool()
		if !certPool.AppendCertsFromPEM(caCert) {
			gologger.Error().Msgf("Could not append parsed ca-cert to config!")
		}
		c.tlsConfig.RootCAs = certPool
	}

	// 如果指定了最小TLS版本，设置最小版本
	if options.MinVersion != "" {
		version, ok := versionStringToTLSVersion[options.MinVersion]
		if !ok {
			return nil, errorutil.NewWithTag("ctls", "invalid min version specified: %s", options.MinVersion)
		} else {
			c.tlsConfig.MinVersion = version
		}
	}

	// 如果指定了最大TLS版本，设置最大版本
	if options.MaxVersion != "" {
		version, ok := versionStringToTLSVersion[options.MaxVersion]
		if !ok {
			return nil, errorutil.NewWithTag("ctls", "invalid max version specified: %s", options.MaxVersion)
		} else {
			c.tlsConfig.MaxVersion = version
		}
	}
	return c, nil
}

// ConnectWithOptions 连接到主机并获取响应数据
// @receiver c
// @param hostname string: 主机名
// @param ip string: IP地址
// @param port string: 端口号
// @param options clients.ConnectOptions: 连接选项
// @return *clients.Response *clients.Response: 连接响应
// @return error error: 可能的错误
func (c *Client) ConnectWithOptions(hostname, ip, port string, options clients.ConnectOptions) (*clients.Response, error) {
	// 根据选项获取配置
	config, err := c.getConfig(hostname, ip, port, options)
	if err != nil {
		return nil, errorutil.NewWithErr(err).Msgf("failed to connect got cfg error")
	}

	// 创建上下文，如果设置了超时，添加超时控制
	ctx := context.Background()
	if c.options.Timeout != 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, time.Duration(c.options.Timeout)*time.Second)
		defer cancel()
	}

	// 设置网络连接
	rawConn, err := clients.GetConn(ctx, hostname, ip, port, c.options)
	if err != nil {
		return nil, errorutil.NewWithErr(err).Msgf("failed to setup connection").WithTag("ctls")
	}
	// defer rawConn.Close() //internally done by conn.Close() so just a placeholder

	// 是否需要客户端请求证书
	var clientCertRequired bool
	// 创建TLS客户端连接
	conn := tls.Client(rawConn, config)
	// 执行TLS握手
	err = conn.HandshakeContext(ctx)
	if err != nil {
		if clients.IsClientCertRequiredError(err) {
			clientCertRequired = true
		} else {
			rawConn.Close()
			return nil, errorutil.NewWithTag("ctls", "could not do handshake").Wrap(err)
		}
	}
	defer conn.Close()

	// 获取连接状态
	connectionState := conn.ConnectionState()
	if len(connectionState.PeerCertificates) == 0 {
		return nil, errorutil.New("no certificates returned by server")
	}

	// 获取TLS版本和密码套件
	tlsVersion := versionToTLSVersionString[connectionState.Version]
	tlsCipher := tls.CipherSuiteName(connectionState.CipherSuite)

	// 提取证书信息
	leafCertificate := connectionState.PeerCertificates[0]
	certificateChain := connectionState.PeerCertificates[1:]

	// 获取远程IP地址
	resolvedIP, _, err := net.SplitHostPort(rawConn.RemoteAddr().String())
	if err != nil {
		return nil, err
	}

	// 创建响应
	now := time.Now()
	response := &clients.Response{
		Timestamp:           &now,
		Host:                hostname,
		IP:                  resolvedIP,
		ProbeStatus:         true,
		Port:                port,
		Version:             tlsVersion,
		Cipher:              tlsCipher,
		TLSConnection:       "ctls",
		CertificateResponse: clients.Convertx509toResponse(c.options, hostname, leafCertificate, c.options.Cert),
		ServerName:          config.ServerName,
	}
	// 检查证书链是否受信任
	response.Untrusted = clients.IsUntrustedCA(certificateChain)
	
	// 如果需要，添加证书链信息
	if c.options.TLSChain {
		for _, cert := range certificateChain {
			// 将证书链中的每个证书转换为响应格式并添加到响应中
			// 证书链对于验证服务器证书的真实性和完整性非常重要
			response.Chain = append(response.Chain, clients.Convertx509toResponse(c.options, hostname, cert, c.options.Cert))
		}
	}

	// crypto/tls允许在不提供客户端证书的情况下完成握手，即使需要客户端证书
	// 直到实际使用底层连接时才会返回错误。因此，我们将暂时
	// 跳过为TLS 1.3服务器设置ClientCertRequired，因为在这个阶段我们还不知道
	// 是否需要客户端证书。
	if response.Version != "tls13" {
		// 只为非TLS 1.3连接设置客户端证书要求标志
		// TLS 1.3处理客户端证书的方式与早期版本不同
		response.ClientCertRequired = &clientCertRequired
	}
	return response, nil
}

// EnumerateCiphers 枚举目标支持的密码套件
// @receiver c 
// @param hostname string:  主机名
// @param ip string: IP地址
// @param port string: 端口号
// @param options clients.ConnectOptions: 连接选项
// @return []string []string: 支持的密码套件列表
// @return error error:  可能的错误
func (c *Client) EnumerateCiphers(hostname, ip, port string, options clients.ConnectOptions) ([]string, error) {
	// 根据给定的安全级别筛选密码套件
	toEnumerate := clients.GetCiphersWithLevel(AllCiphersNames, options.CipherLevel...)

	// TLS 1.3不支持密码套件枚举
	if options.VersionTLS == "tls13" {
		return nil, errorutil.NewWithTag("ctls", "cipher enum not supported in ctls with tls1.3")
	}

	enumeratedCiphers := []string{}

	// 获取基础配置
	baseCfg, err := c.getConfig(hostname, ip, port, options)
	if err != nil {
		return enumeratedCiphers, errorutil.NewWithErr(err).Msgf("failed to setup cfg")
	}
	gologger.Debug().Label("ctls").Msgf("Starting cipher enumeration with %v ciphers and version %v", len(toEnumerate), options.VersionTLS)

	// 获取网络地址
	var address string
	if iputil.IsIP(ip) && (c.options.ScanAllIPs || len(c.options.IPVersion) > 0) {
		address = net.JoinHostPort(ip, port)
	} else {
		address = net.JoinHostPort(hostname, port)
	}

	// 设置并发数
	threads := c.options.CipherConcurrency
	if len(toEnumerate) < threads {
		threads = len(toEnumerate)
	}

	// 设置连接池
	pool, err := connpool.NewOneTimePool(context.Background(), address, threads)
	if err != nil {
		return enumeratedCiphers, errorutil.NewWithErr(err).Msgf("failed to setup connection pool")
	}
	pool.Dialer = c.dialer
	go func() {
		if err := pool.Run(); err != nil && !errors.Is(err, context.Canceled) {
			gologger.Error().Msgf("tlsx: ctls: failed to run connection pool: %v", err)
		}
	}()
	defer pool.Close()

	// 遍历所有要枚举的密码套件
	for _, v := range toEnumerate {
		// 创建新的基础连接并传递给TLS客户端
		baseConn, err := pool.Acquire(context.Background())
		if err != nil {
			return enumeratedCiphers, errorutil.NewWithErr(err).WithTag("ctls")
		}
		stats.IncrementCryptoTLSConnections()
		baseCfg.CipherSuites = []uint16{tlsCiphers[v]}

		// 创建TLS客户端连接
		conn := tls.Client(baseConn, baseCfg)

			// 尝试握手，如果成功，记录使用的密码套件
		if err := conn.Handshake(); err == nil {
			ciphersuite := conn.ConnectionState().CipherSuite
			enumeratedCiphers = append(enumeratedCiphers, tls.CipherSuiteName(ciphersuite))
		}
		_ = conn.Close() // 内部关闭baseConn
	}
	return enumeratedCiphers, nil
}

// SupportedTLSVersions 返回标准TLS库支持的TLS版本列表
func (c *Client) SupportedTLSVersions() ([]string, error) {
	return SupportedTlsVersions, nil
}

// SupportedTLSCiphers 返回标准TLS库支持的密码套件列表
func (c *Client) SupportedTLSCiphers() ([]string, error) {
	return AllCiphersNames, nil
}

// getConfig 根据主机名、IP、端口和选项获取TLS配置
// @receiver c
// @param hostname string: 主机名
// @param ip string: IP地址
// @param port string: 端口号
// @param options clients.ConnectOptions: 连接选项
// @return *tls.Config *tls.Config: TLS配置
// @return error error: 可能的错误
func (c *Client) getConfig(hostname, ip, port string, options clients.ConnectOptions) (*tls.Config, error) {
	// 在枚举模式下，如果给定的选项不受支持，则返回
	if options.EnumMode == clients.Version && (options.VersionTLS == "" || !stringsutil.EqualFoldAny(options.VersionTLS, SupportedTlsVersions...)) {
		return nil, errorutil.NewWithTag("ctls", "tlsversion `%v` not supported in ctls", options.VersionTLS)
	}
	// 获取基础TLS配置
	config := c.tlsConfig

	// 如果ServerName为空，设置ServerName（SNI）
	if config.ServerName == "" {
		// 创建配置副本
		cfg := config.Clone()
		// 优先使用选项中指定的SNI
		if options.SNI != "" {
			cfg.ServerName = options.SNI
		} else if iputil.IsIP(hostname) && c.options.RandomForEmptyServerName {
			// 如果主机名是IP且设置了随机SNI选项，使用随机SNI
			// 使用随机SNI将返回服务器的默认证书
			cfg.ServerName = xid.New().String()
		} else {
			// 否则使用主机名作为SNI
			cfg.ServerName = hostname
		}

		config = cfg
	}

	// 如果指定了TLS版本，设置最小和最大版本
	if options.VersionTLS != "" {
		version, ok := versionStringToTLSVersion[options.VersionTLS]
		if !ok {
			return nil, errorutil.New("invalid tls version specified: %s", options.VersionTLS).WithTag("ctls")
		}
		config.MinVersion = version
		config.MaxVersion = version
	}	
	
	// 如果指定了密码套件且不是密码套件枚举模式，设置自定义密码套件
	if len(options.Ciphers) > 0 && options.EnumMode != clients.Cipher {
		customCiphers, err := toTLSCiphers(options.Ciphers)
		if err != nil {
			return nil, errorutil.NewWithTag("ctls", "could not get tls ciphers").Wrap(err)
		}
		c.tlsConfig.CipherSuites = customCiphers
	}
	
	// 如果是密码套件枚举模式且TLS版本不受支持，返回错误
	if options.EnumMode == clients.Cipher && !stringsutil.EqualFoldAny(options.VersionTLS, SupportedTlsVersions...) {
		return nil, errorutil.NewWithTag("ctls", "cipher enum with version %v not implemented", options.VersionTLS)
	}
	return config, nil
}
