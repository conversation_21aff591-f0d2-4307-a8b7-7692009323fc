// Author: chenjb
// Version: V1.0
// Date: 2025-05-16 16:53:54
// FilePath: /yaml_scan/pkg/hybridMap/disk/buntdb_test.go
// Description:
package disk

import (
	"os"
	"strconv"
	"testing"

	"github.com/stretchr/testify/require"
)

// 测试前的设置：创建模拟的 BuntDB 实现
func setupBuntDBTest(t *testing.T) (*BuntDB, string, func()) {
	// 创建临时测试目录
	tempDir, err := os.MkdirTemp("", "buntdb-test")
	require.NoError(t, err, "创建临时测试目录失败")

	// 创建数据库文件路径
	dbPath := tempDir + "/test.db"

	// 打开 BuntDB 数据库
	db, err := OpenBuntDB(dbPath)
	require.NoError(t, err, "打开 BuntDB 数据库应该成功")

	// 返回清理函数
	cleanupFn := func() {
		db.Close()
		os.RemoveAll(tempDir)
	}

	return db, tempDir, cleanupFn
}

// TestBuntDBBasicOperations 测试 BuntDB 的基本操作，包括添加、获取、删除键值对
func TestBuntDBBasicOperations(t *testing.T) {
	// 设置测试环境
	bdb, _, cleanup := setupBuntDBTest(t)
	defer cleanup()

	// 1. 测试设置和获取键值对
	testKey := "test-key"
	testValue := []byte("test-value")

	// 1.1 设置键值对
	err := bdb.Set(testKey, testValue, 0) // 永不过期
	require.NoError(t, err, "设置键值对应该成功")

	// 1.2 获取键值对
	value, err := bdb.Get(testKey)
	require.NoError(t, err, "获取存在的键应该成功")
	require.Equal(t, testValue, value, "获取的值应该与设置的值相同")

	// 1.3 获取不存在的键
	_, err = bdb.Get("non-existent-key")
	require.Error(t, err, "获取不存在的键应该返回错误")

	// 2. 测试删除键
	err = bdb.Del(testKey)
	require.NoError(t, err, "删除键应该成功")

	// 2.1 验证删除后无法获取
	_, err = bdb.Get(testKey)
	require.Error(t, err, "获取已删除的键应该返回错误")

	// 3. 测试批量操作
	// 3.1 批量设置
	data := map[string][]byte{
		"key1": []byte("value1"),
		"key2": []byte("value2"),
		"key3": []byte("value3"),
	}
	err = bdb.MSet(data)
	require.NoError(t, err, "批量设置键值对应该成功")

	// 3.2 批量获取
	results := bdb.MGet([]string{"key1", "key2", "key3", "nonexistent-key"})
	require.Len(t, results, 4, "应该返回 4 个结果")
	require.Equal(t, []byte("value1"), results[0], "第一个值应该是 value1")
	require.Equal(t, []byte("value2"), results[1], "第二个值应该是 value2")
	require.Equal(t, []byte("value3"), results[2], "第三个值应该是 value3")
	require.Empty(t, results[3], "第四个值应该是空字节数组")

	// 3.3 批量删除
	err = bdb.MDel([]string{"key1", "key2", "key3"})
	require.NoError(t, err, "批量删除键应该成功")

	// 3.4 验证批量删除成功
	for _, key := range []string{"key1", "key2", "key3"} {
		_, err = bdb.Get(key)
		require.Error(t, err, "获取已删除的键应该返回错误")
	}
}

// TestBuntDBIncr 测试 BuntDB 的自增操作功能
func TestBuntDBIncr(t *testing.T) {
	// 设置测试环境
	bdb, _, cleanup := setupBuntDBTest(t)
	defer cleanup()

	// 1. 首先需要设置一个初始值
	err := bdb.Set("counter", []byte("5"), 0)
	require.NoError(t, err, "设置计数器初始值应该成功")

	// 2. 测试对存在的键进行自增
	result, err := bdb.Incr("counter", 3)
	require.NoError(t, err, "对存在的键进行自增应该成功")
	require.Equal(t, int64(8), result, "自增后结果应该为 8")

	// 3. 验证值已正确存储
	value, err := bdb.Get("counter")
	require.NoError(t, err, "获取计数器应该成功")
	require.Equal(t, "8", string(value), "存储的计数器值应该是 '8'")

	// 4. 测试负值自增
	result, err = bdb.Incr("counter", -10)
	require.NoError(t, err, "负值自增应该成功")
	require.Equal(t, int64(-2), result, "负值自增后结果应该为 -2")

	// 5. 验证更新后的值
	value, err = bdb.Get("counter")
	require.NoError(t, err, "获取更新后的计数器应该成功")
	require.Equal(t, "-2", string(value), "存储的计数器值应该更新为 '-2'")
}

// TestBuntDBScan 测试 BuntDB 的扫描功能
func TestBuntDBScan(t *testing.T) {
	// 设置测试环境
	bdb, _, cleanup := setupBuntDBTest(t)
	defer cleanup()

	// 1. 准备测试数据
	testData := map[string][]byte{
		"user:1": []byte("Alice"),
		"user:2": []byte("Bob"),
		"user:3": []byte("Charlie"),
		"post:1": []byte("Hello World"),
		"post:2": []byte("Testing BuntDB"),
		"post:3": []byte("Scan Feature"),
	}

	for k, v := range testData {
		err := bdb.Set(k, v, 0)
		require.NoError(t, err, "设置测试数据应该成功")
	}

	// 2. 测试前缀扫描
	userKeys := make([]string, 0)
	userValues := make([]string, 0)

	err := bdb.Scan(ScannerOptions{
		Prefix:      "user:",
		FetchValues: true,
		Handler: func(k []byte, v []byte) error {
			userKeys = append(userKeys, string(k))
			userValues = append(userValues, string(v))
			return nil
		},
	})

	require.NoError(t, err, "前缀扫描应该成功")
	require.Len(t, userKeys, 3, "应该有 3 个用户键")
	require.Contains(t, userKeys, "user:1", "应包含 user:1")
	require.Contains(t, userKeys, "user:2", "应包含 user:2")
	require.Contains(t, userKeys, "user:3", "应包含 user:3")
	require.Contains(t, userValues, "Alice", "应包含 Alice")
	require.Contains(t, userValues, "Bob", "应包含 Bob")
	require.Contains(t, userValues, "Charlie", "应包含 Charlie")

	// 3. 测试偏移量扫描
	offsetKeys := make([]string, 0)
	err = bdb.Scan(ScannerOptions{
		Offset:        "post:2",
		IncludeOffset: false,
		Handler: func(k []byte, v []byte) error {
			offsetKeys = append(offsetKeys, string(k))
			return nil
		},
	})

	require.NoError(t, err, "偏移量扫描应该成功")
	require.Contains(t, offsetKeys, "post:3", "应包含 post:3")
	require.NotContains(t, offsetKeys, "post:2", "不应包含 post:2")
}

// TestBuntDBGC 测试 BuntDB 的垃圾回收功能
func TestBuntDBGC(t *testing.T) {
	// 设置测试环境
	bdb, _, cleanup := setupBuntDBTest(t)
	defer cleanup()

	// 1. 添加数据
	for i := 0; i < 100; i++ {
		key := "key_" + strconv.Itoa(i)
		value := []byte("This is test data for GC testing")
		err := bdb.Set(key, value, 0)
		require.NoError(t, err, "设置测试数据应该成功")
	}

	// 2. 删除部分数据
	for i := 0; i < 50; i++ {
		key := "key_" + strconv.Itoa(i)
		err := bdb.Del(key)
		require.NoError(t, err, "删除测试数据应该成功")
	}

	// 3. 运行垃圾回收
	err := bdb.GC()
	require.NoError(t, err, "GC 应该成功")
} 