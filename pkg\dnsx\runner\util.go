// Author: chenjb
// Version: V1.0
// Date: 2025-06-19 19:13:11
// FilePath: /yaml_scan/pkg/dnsx/runner/util.go
// Description:  提供DNS查询运行器的工具函数
package runner

import (
	"fmt"
	"net/url"
	"strings"
	"time"
	fileutil "yaml_scan/utils/file"
)

const (
	stdinMarker = "-"  // 标准输入的标记
	Comma       = ","  // 逗号分隔符
	NewLine     = "\n" // 新行分隔符
)

// linesInFile  从文件中读取所有行并返回字符串切片
// @param fileName string: 要读取的文件路径
// @return []string []string: 文件的所有行组成的字符串切片
// @return error error: 如果读取过程中发生错误，返回相应的错误
func linesInFile(fileName string) ([]string, error) {
	result := []string{}
	f, err := fileutil.ReadFile(fileName)
	if err != nil {
		return nil, err
	}
	for line := range f {
		result = append(result, line)
	}
	return result, nil
}

// isURL 测试一个字符串是否是结构良好的URL
// @param toTest string: 待测试的字符串
// @return bool bool: 如果字符串是一个有效的URL则返回true，否则返回false
func isURL(toTest string) bool {
	_, err := url.ParseRequestURI(toTest)
	if err != nil {
		return false
	}

	u, err := url.Parse(toTest)
	if err != nil || u.Scheme == "" || u.Host == "" {
		return false
	}

	return true
}

// extractDomain 从URL中提取域名部分
// @param URL string: 输入的URL字符串
// @return string string: 提取的域名，如果无法解析URL则返回空字符串
func extractDomain(URL string) string {
	u, err := url.Parse(URL)
	if err != nil {
		return ""
	}

	return u.Hostname()
}

// prepareResolver 准备解析器地址，确保它包含端口号
// @param resolver string: 解析器地址字符串
// @return string string: 处理后的解析器地址，如果没有端口号会添加默认的53端口
func prepareResolver(resolver string) string {
	resolver = strings.TrimSpace(resolver)
	if !strings.Contains(resolver, ":") {
		resolver += ":53"
	}
	return resolver
}

// fmtDuration 将时间持续时间格式化为小时:分钟:秒的字符串格式
// @param d time.Duration:  要格式化的时间持续时间
// @return string string:  格式化后的时间字符串，格式为"小时:分钟:秒"
func fmtDuration(d time.Duration) string {
	d = d.Round(time.Second) // 将持续时间四舍五入到秒
	h := d / time.Hour // 计算小时部分
	d -= h * time.Hour // 减去小时部分
	m := d / time.Minute // 计算分钟部分
	d -= m * time.Minute // 减去分钟部分
	s := d / time.Second // 计算秒部分
	return fmt.Sprintf("%d:%02d:%02d", h, m, s) // 格式化为小时:分钟:秒
}