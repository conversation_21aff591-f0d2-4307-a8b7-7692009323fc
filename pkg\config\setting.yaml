# 日志配置
log:
  location: "/log/module/yaml_scan"  # 日志存取目录
  rotate: true         # 是否启用轮换
  rotation_interval: 24h # 轮换时间间隔
  rotate_check: 1h  # 轮换检测时间
  max_size: 10        # 最大文件大小（MB）
  compress: true      # 是否压缩旧日志
  backup_time_format: "2006-01-02 15-04-05" # 备份时间格式
  archive_format: "gz" # 归档格式
  rotate_each_hour: false # 是否每小时轮换
  rotate_each_day: true  # 是否每天轮换
  max_level: "info"  # 日志最大级别
  file_name: "yaml_scan.log"  # 日志文件名
# flag参数配置
flag:
  caseSensitive: true  # 是否区别大小写
  description: "基于yaml模板的漏洞扫描引擎"
  max_redirects: 10  # HTTP模板最大重定向次数
# 模板配置
template:
  templates_directory: "template"  # 模板目录
# 超时配置
timeout:
  dial_timeout: 10  # 是 fastdialer 的拨号超时（默认 10s）
  tcp_read_timeout: 5 # TCP（网络协议）从连接读取的超时（默认 5s）
  http_response_header_timeout: 10 # HTTP 响应头的超时（默认 10s）
  http_timeout: 30  # HTTP 客户端的超时
  jscompiler_execution_timeout: 20 # JavaScript 编译器执行的超时/截止时间
  code_execution_timeout: 30 # 代码执行的超时