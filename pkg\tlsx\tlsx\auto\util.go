// Author: chenjb
// Version: V1.0
// Date: 2025-06-24 16:14:45
// FilePath: /yaml_scan/pkg/tlsx/tlsx/auto/util.go
// Description:自动模式的工具函数和全局变量
package auto

import (
	"yaml_scan/pkg/tlsx/tlsx/openssl"
	"yaml_scan/pkg/tlsx/tlsx/tls"
	"yaml_scan/pkg/tlsx/tlsx/ztls"
	sliceutil "yaml_scan/utils/slice"
)

var (
	// allCiphersNames 包含所有TLS实现支持的密码套件名称
	allCiphersNames      []string
	// supportedTlsVersions 包含所有TLS实现支持的TLS版本
	supportedTlsVersions []string
)

func init() {
	// 聚合所有密码套件名称
	allCiphersNames = append(tls.AllCiphersNames, ztls.AllCiphersNames...)
	allCiphersNames = append(allCiphersNames, openssl.AllCiphersNames...)
	// 聚合所有支持的TLS版本
	supportedTlsVersions = append(tls.SupportedTlsVersions, ztls.SupportedTlsVersions...)
	supportedTlsVersions = append(supportedTlsVersions, openssl.SupportedTLSVersions...)
	// 去重处理，确保列表中没有重复项
	allCiphersNames = sliceutil.Dedupe(allCiphersNames)
	supportedTlsVersions = sliceutil.Dedupe(supportedTlsVersions)
}
