// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-18 15:30:18
// FilePath: /yaml_scan/pkg/utils/freeport/freeport.go
// Description: 获取空闲网络端口的工具包，支持TCP和UDP协议
package freeport

import (
	"errors"
	"fmt"
	"net"
)

// Protocol 表示支持的网络协议类型
type Protocol uint8

const (
	TCP Protocol = iota
	UDP
)

// Port 表示从内核获取的端口信息
type Port struct {
	// Address 是端口的地址（例如 127.0.0.1）
	Address string
	// Port 是端口号	
	Port int
	// Protocol 是端口的协议类型（TCP 或 UDP）
	Protocol Protocol
	// NetListenAddress 是完整的可监听地址（可直接用于 net.Listen）
	NetListenAddress string
}

// GetFreePortOnInterface 根据网络接口名称和协议获取空闲端口
// @param interfaceName string:  网络接口名称（如"eth0"、"lo"等）
// @param protocol Protocol: 网络协议类型（TCP或UDP）
// @return *Port *Port: 包含空闲端口信息的结构体指针
// @return error error: 可能的错误
func GetFreePortOnInterface(interfaceName string, protocol Protocol) (*Port, error) {
	// 通过接口名称获取网络接口信息
	itf, err := net.InterfaceByName(interfaceName)
	if err != nil {
		return nil, err
	}
	// 获取该接口上的所有地址
	addresses, err := itf.Addrs()
	if err != nil {
		return nil, err
	}
	for _, address := range addresses {
		switch protocol {
		case UDP:
			// 如果是UDP协议，尝试获取空闲UDP端口
			if port, err := GetFreeUDPPort(address.String()); err == nil {
				return port, nil
			}
		default:
				// 默认使用TCP协议，尝试获取空闲TCP端口
			if port, err := GetFreeTCPPort(address.String()); err == nil {
				return port, nil
			}
		}
	}
	return nil, fmt.Errorf("couldn't find any free port on interface %s", interfaceName)
}

// GetFreePort 从指定IP地址和协议获取空闲端口
// @param address string: IP地址字符串
// @param protocol Protocol: 网络协议类型（TCP或UDP）
// @return *Port *Port: 包含空闲端口信息的结构体指针
// @return error error: 
func GetFreePort(address string, protocol Protocol) (*Port, error) {
	switch protocol {
	case UDP:
		return GetFreeUDPPort(address)
	default:
		return GetFreeTCPPort(address)
	}
}

// GetFreePorts 获取指定数量的空闲端口
// @param address string: IP地址字符串
// @param protocol Protocol: 网络协议类型（TCP或UDP）
// @param count int: 需要获取的端口数量
// @return []*Port []*Port: 包含多个空闲端口信息的结构体指针切片
// @return error error: 可能的错误
func GetFreePorts(address string, protocol Protocol, count int) ([]*Port, error) {
	// 创建指定大小的端口切片
	ports := make([]*Port, count)
	for i := 0; i < count; i++ {
		port, err := GetFreePort(address, protocol)
		if err != nil {
			return nil, err
		}
		ports[i] = port
	}
	return ports, nil
}

// GetFreePortInRange 在指定端口范围内获取空闲端口
// @param address string: IP地址字符串
// @param protocol Protocol: 网络协议类型（TCP或UDP）
// @param minPort int: 端口范围下限
// @param maxPort int: 端口范围上限
// @return *Port *Port: 包含空闲端口信息的结构体指针
// @return error error: 
func GetFreePortInRange(address string, protocol Protocol, minPort, maxPort int) (*Port, error) {
	if minPort > maxPort {
		return nil, errors.New("invalid interval")
	}
	for port := minPort; port <= maxPort; port++ {
		if port, err := GetPort(protocol, address, port); err == nil {
			return port, nil
		}
	}
	return nil, fmt.Errorf("couldn't find free ports between %d and %d", minPort, maxPort)
}

// GetFreeTCPPort 在指定地址上获取空闲TCP端口
//
// @param address string: IP地址字符串
// @return *Port *Port: 包含空闲TCP端口信息的结构体指针
// @return error error: 可能的错误
func GetFreeTCPPort(address string) (*Port, error) {
	// 解析TCP地址，端口设为0让系统自动分配
	addr, err := net.ResolveTCPAddr("tcp", address+":0")
	if err != nil {
		return nil, err
	}

	l, err := net.ListenTCP("tcp", addr)
	if err != nil {
		return nil, err
	}
	if err := l.Close(); err != nil {
		return nil, err
	}
	var port int
	if tcpAddr, ok := l.Addr().(*net.TCPAddr); ok {
		port = tcpAddr.Port
	}
	
	// 创建并返回端口信息
	// 我们可以直接使用address作为GetPort的参数，但这种方式保留了原始代码的精神，
	// 允许监听IP以某种方式发生变化
	return &Port{Address: address, Port: port, Protocol: TCP, NetListenAddress: l.Addr().String()}, nil
}

// GetPort 检查指定协议和端口是否可用
// @param protocol Protocol:  网络协议类型（TCP或UDP）
// @param address string:  IP地址字符串
// @param port int: 要检查的端口号
// @return *Port *Port: 如果端口可用，返回包含端口信息的结构体指针
// @return error error: 
func GetPort(protocol Protocol, address string, port int) (*Port, error) {
	// 组合主机地址和端口
	hostport := net.JoinHostPort(address, fmt.Sprint(port))
	switch protocol {
	case UDP:
		// 如果是UDP协议
		// 解析UDP地址
		addr, err := net.ResolveUDPAddr("udp", hostport)
		if err != nil {
			return nil, err
		}
		l, err := net.ListenUDP("udp", addr)
		if err != nil {
			return nil, err
		}
		if err := l.Close(); err != nil {
			return nil, err
		}
		// 创建并返回端口信息
		return &Port{Address: address, Port: port, Protocol: UDP, NetListenAddress: l.LocalAddr().String()}, nil
	default:
		// 默认使用TCP协议
		// 尝试监听该TCP地址
		l, err := net.Listen("tcp", hostport)
		if err != nil {
			return nil, err
		}
		if err := l.Close(); err != nil {
			return nil, err
		}
		// 创建并返回端口信息
		return &Port{Address: address, Port: port, Protocol: TCP, NetListenAddress: l.Addr().String()}, nil

	}
}

// GetFreeUDPPort 在指定地址上获取空闲UDP端口
// @param address string: IP地址字符串
// @return *Port *Port: 包含空闲UDP端口信息的结构体指针
// @return error error: 可能的错误
func GetFreeUDPPort(address string) (*Port, error) {
	// 解析UDP地址，端口设为0让系统自动分配
	addr, err := net.ResolveUDPAddr("udp", address+":0")
	if err != nil {
		return nil, err
	}

	// 尝试在该地址上监听
	l, err := net.ListenUDP("udp", addr)
	if err != nil {
		return nil, err
	}
	// 关闭监听器，释放端口
	if err := l.Close(); err != nil {
		return nil, err
	}
	var port int
	// 从监听地址中提取端口号
	if udpAddr, ok := l.LocalAddr().(*net.UDPAddr); ok {
		port = udpAddr.Port
	}

	// 创建并返回端口信息
	// 这通常应该等于address，但如果由于某种原因操作系统重新映射了地址，
	// 这种方式会尊重返回的地址
	return &Port{Address: address, Port: port, Protocol: UDP, NetListenAddress: l.LocalAddr().String()}, nil
}

// MustGetFreeTCPPort 获取空闲TCP端口，如果失败则panic
// @param address string: IP地址字符串
// @return *Port *Port: 
func MustGetFreeTCPPort(address string) *Port {
	port, err := GetFreeTCPPort(address)
	if err != nil {
		panic(err)
	}
	return port
}

// MustGetFreeUDPPort 获取空闲UDP端口，如果失败则panic
// @param address string: 
// @return *Port *Port: 
func MustGetFreeUDPPort(address string) *Port {
	port, err := GetFreeUDPPort(address)
	if err != nil {
		panic(err)
	}
	return port
}

