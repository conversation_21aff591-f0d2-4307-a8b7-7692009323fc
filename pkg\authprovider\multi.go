// Author: chenjb
// Version: V1.0
// Date: 2025-06-13 15:34:31
// FilePath: /yaml_scan/pkg/authprovider/multi.go
// Description: 实现了多重认证提供者，可以组合多个认证提供者并按顺序查询
package authprovider

import (
	"net/url"
	"yaml_scan/pkg/authprovider/authx"
	urlutil "yaml_scan/utils/url"
)

// MultiAuthProvider 是多个认证提供者的便捷包装器
// 它返回给定域名的第一个匹配认证策略
// 如果给定域名有多个认证策略，则返回第一个
type MultiAuthProvider struct {
	// Providers 存储所有包含的认证提供者
	Providers []AuthProvider
}

// NewMultiAuthProvider  创建一个新的多重认证提供者
// @param providers ...AuthProvider: 
// @return AuthProvider AuthProvider: 
func NewMultiAuthProvider(providers ...AuthProvider) AuthProvider {
	return &MultiAuthProvider{Providers: providers}
}

// LookupAddr 根据给定的域名/地址查找并返回适当的认证策略
// 它会按顺序查询所有包含的认证提供者，并返回第一个匹配的结果
// @receiver m 
// @param host string: 要查找的域名或地址字符串
// @return []authx.AuthStrategy []authx.AuthStrategy: 第一个提供者匹配的认证策略列表，如果没有匹配则返回nil
func (m *MultiAuthProvider) LookupAddr(host string) []authx.AuthStrategy {
	for _, provider := range m.Providers {
		strategy := provider.LookupAddr(host)
		if len(strategy) > 0 {
			return strategy
		}
	}
	return nil
}

// LookupURL 根据给定的URL查找并返回适当的认证策略
// 它会按顺序查询所有包含的认证提供者，并返回第一个匹配的结果
// @receiver m 
// @param u *url.URL: 
// @return []authx.AuthStrategy []authx.AuthStrategy: 
func (m *MultiAuthProvider) LookupURL(u *url.URL) []authx.AuthStrategy {
	for _, provider := range m.Providers {
		strategy := provider.LookupURL(u)
		if strategy != nil {
			return strategy
		}
	}
	return nil
}

// LookupURLX 根据给定的URL查找并返回适当的认证策略
// 它会按顺序查询所有包含的认证提供者，并返回第一个匹配的结果
// @receiver m 
// @param u *urlutil.URL: 
// @return []authx.AuthStrategy []authx.AuthStrategy: 
func (m *MultiAuthProvider) LookupURLX(u *urlutil.URL) []authx.AuthStrategy {
	for _, provider := range m.Providers {
		strategy := provider.LookupURLX(u)
		if strategy != nil {
			return strategy
		}
	}
	return nil
}

// GetTemplatePaths 返回所有认证提供者的模板路径
// @receiver m 
// @return []string []string: 
func (m *MultiAuthProvider) GetTemplatePaths() []string {
	var res []string
	for _, provider := range m.Providers {
		res = append(res, provider.GetTemplatePaths()...)
	}
	return res
}

// PreFetchSecrets 预先获取所有认证提供者中的密钥
// @receiver m 
// @return error error: 
func (m *MultiAuthProvider) PreFetchSecrets() error {
	for _, provider := range m.Providers {
		if err := provider.PreFetchSecrets(); err != nil {
			return err
		}
	}
	return nil
}

