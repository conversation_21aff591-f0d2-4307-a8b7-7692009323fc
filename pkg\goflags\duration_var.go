//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-20 16:14:59
//FilePath: /yaml_scan/pkg/goflags/duration_var.go
//Description:

package goflags

import (
	"time"

	timeutil "yaml_scan/utils/time"

	"github.com/pkg/errors"
)

// durationValue 是 time.Duration 的包装类型，用于实现 flag.Value 接口
type durationValue time.Duration

// newDurationValue: 创建一个新的 durationValue 并将其指向给定的 time.Duration 指针
//
//	@param val time.Duration: 默认的持续时间值。
//	@param p *time.Duration:  指向 time.Duration 的指针，用于存储解析后的值。
//	@return *durationValue *durationValue: 返回一个指向 durationValue 的指针。
func newDurationValue(val time.Duration, p *time.Duration) *durationValue {
	*p = val
	return (*durationValue)(p)
}

// Set: 解析字符串 s 为 time.Duration，并将结果存储在 durationValue 中
//
//	@receiver d *durationValue:
//	@param s string: 表示持续时间的字符串，可以包含单位（如 "300ms", "2h", "1d"）。
//	@return error error:如果解析失败，返回错误信息。
func (d *durationValue) Set(s string) error {
	v, err := timeutil.ParseDuration(s)
	if err != nil {
		err = errors.New("parse error")
	}
	*d = durationValue(v)
	return err
}
// 返回 durationValue 的字符串表示
func (d *durationValue) String() string { return (*time.Duration)(d).String() }

// DurationVarP: 添加一个支持短名称和长名称的持续时间标志
//  @receiver flagSet *FlagSet: 
//  @param field *time.Duration: 指向 time.Duration 的指针，用于存储标志的值。
//  @param long string: 标志的长名称。
//  @param short string:  标志的短名称。
//  @param defaultValue time.Duration: 标志的默认持续时间值。
//  @param usage string:  标志的使用说明。
//  @return *FlagData *FlagData: 返回一个 FlagData 指针，包含标志的相关信息。
func (flagSet *FlagSet) DurationVarP(field *time.Duration, long, short string, defaultValue time.Duration, usage string) *FlagData {
	flagData := &FlagData{
		usage:        usage,
		long:         long,
		defaultValue: defaultValue,
	}
	if short != "" {
		flagData.short = short
		flagSet.CommandLine.Var(newDurationValue(defaultValue, field), short, usage)
		flagSet.flagKeys.Set(short, flagData)
	}
	flagSet.CommandLine.Var(newDurationValue(defaultValue, field), long, usage)
	flagSet.flagKeys.Set(long, flagData)
	return flagData
}
