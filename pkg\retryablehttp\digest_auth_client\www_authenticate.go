// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 17:09:37
// FilePath: /yaml_scan/pkg/retryablehttp/digest_auth_client/www_authenticate.go
// Description: 解析和存储HTTP摘要认证中服务器返回的WWW-Authenticate头信息
package digestauthclient

import (
	"regexp"
	"strings"
)

// wwwAuthenticate 结构体保存HTTP摘要认证中服务器返回的各参数
type wwwAuthenticate struct {
	Algorithm string // 未加引号，使用的哈希算法，如MD5或SHA-256
	Domain    string // 加引号，认证涵盖的域（URI空间）
	Nonce     string // 加引号，服务器生成的随机字符串，用于防止重放攻击
	Opaque    string // 加引号，服务器提供的数据，客户端应在后续请求中原样返回
	Qop       string // 加引号，服务质量保护选项，如"auth"或"auth-int"
	Realm     string // 加引号，保护区域名称，指示受保护资源的范围
	Stale     bool   // 未加引号，指示nonce是否已过期但凭证仍然有效
	Charset   string // 加引号，指示凭证参数的字符编码
	Userhash  bool   // 加引号，指示是否对用户名进行哈希处理
}

// newWwwAuthenticate 创建一个新的授权对象并初始化基本字段
// @param s string:  WWW-Authenticate头的值
// @return *wwwAuthenticate *wwwAuthenticate: 解析后的认证信息结构体
func newWwwAuthenticate(s string) *wwwAuthenticate {

	var wa = wwwAuthenticate{}

	// 使用正则表达式提取算法信息
	algorithmRegex := regexp.MustCompile(`algorithm="([^ ,]+)"`)
	algorithmMatch := algorithmRegex.FindStringSubmatch(s)
	if algorithmMatch != nil {
		wa.Algorithm = algorithmMatch[1]
	}

	// 提取域信息
	domainRegex := regexp.MustCompile(`domain="(.+?)"`)
	domainMatch := domainRegex.FindStringSubmatch(s)
	if domainMatch != nil {
		wa.Domain = domainMatch[1]
	}

	// 提取随机数信息
	nonceRegex := regexp.MustCompile(`nonce="(.+?)"`)
	nonceMatch := nonceRegex.FindStringSubmatch(s)
	if nonceMatch != nil {
		wa.Nonce = nonceMatch[1]
	}

	// 提取不透明字符串
	opaqueRegex := regexp.MustCompile(`opaque="(.+?)"`)
	opaqueMatch := opaqueRegex.FindStringSubmatch(s)
	if opaqueMatch != nil {
		wa.Opaque = opaqueMatch[1]
	}

	// 提取服务质量选项
	qopRegex := regexp.MustCompile(`qop="(.+?)"`)
	qopMatch := qopRegex.FindStringSubmatch(s)
	if qopMatch != nil {
		wa.Qop = qopMatch[1]
	}
	
	// 提取保护区域名称
	realmRegex := regexp.MustCompile(`realm="(.+?)"`)
	realmMatch := realmRegex.FindStringSubmatch(s)
	if realmMatch != nil {
		wa.Realm = realmMatch[1]
	}

	// 提取nonce是否过期但凭证仍有效的标志
	staleRegex := regexp.MustCompile(`stale=([^ ,])"`)
	staleMatch := staleRegex.FindStringSubmatch(s)
	if staleMatch != nil {
		wa.Stale = (strings.ToLower(staleMatch[1]) == "true")
	}

	// 提取字符集信息
	charsetRegex := regexp.MustCompile(`charset="(.+?)"`)
	charsetMatch := charsetRegex.FindStringSubmatch(s)
	if charsetMatch != nil {
		wa.Charset = charsetMatch[1]
	}

	// 提取是否对用户名进行哈希处理的标志
	userhashRegex := regexp.MustCompile(`userhash=([^ ,])"`)
	userhashMatch := userhashRegex.FindStringSubmatch(s)
	if userhashMatch != nil {
		wa.Userhash = (strings.ToLower(userhashMatch[1]) == "true")
	}

	return &wa
}

