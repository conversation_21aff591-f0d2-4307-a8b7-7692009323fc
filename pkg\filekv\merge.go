// 
// Author: chenjb
// Version: V1.0
// Date: 2025-05-16 10:43:23
// FilePath: /yaml_scan/pkg/hybridMap/file/merge.go
// Description: 实现文件数据库的数据合并功能，支持多种数据源的合并
package filekv

import (
	"bufio"
	"bytes"
	"io"
	"os"
)

// Merge 合并多种类型的数据到文件数据库
// @receiver f 
// @param items ...interface{}: 任意数量的不同类型的数据源，支持多种数据类型
// @return uint uint:  成功合并的数据项数量，表示添加了多少项
// @return error error: 如果合并过程中发生错误，返回相关错误信息
func (f *FileDB) Merge(items ...interface{}) (uint, error) {
	var count uint
	for _, item := range items {
		switch itemData := item.(type) {
		case [][]byte:
			// 处理字节数组切片
			for _, data := range itemData {
				// 写入数据到临时数据库
				if _, err := f.tmpDbWriter.Write(data); err != nil {
					return 0, err
				}
				// 添加换行符
				if _, err := f.tmpDbWriter.Write([]byte(NewLine)); err != nil {
					return 0, err
				}
				count++
				f.stats.NumberOfAddedItems++
			}
		case []string:
			// 处理字符串切片
			for _, data := range itemData {
				// 写入字符串数据和换行符到临时数据库
				_, err := f.tmpDbWriter.Write([]byte(data + NewLine))
				if err != nil {
					return 0, err
				}
				count++
				f.stats.NumberOfAddedItems++
			}
		case io.Reader:
			// 处理Reader接口
			c, err := f.MergeReader(itemData)
			if err != nil {
				return 0, err
			}
			count += c
		case string:
			// 处理字符串
			c, err := f.MergeFile(itemData)
			if err != nil {
				return 0, err
			}
			count += c
		}
	}
	return count, nil
}

// shouldSkip 判断是否应该跳过某个键值对
// @receiver f 
// @param k []byte: 键
// @param v []byte: 值
// @return bool bool: 如果应该跳过，返回true；否则返回false
func (f *FileDB) shouldSkip(k, v []byte) bool {
	// 如果设置了跳过空键，且键为空，则跳过
	if f.options.SkipEmpty && len(k) == 0 {
		return true
	}

	// 如果配置了过滤回调函数，调用它判断是否跳过
	if f.options.FilterCallback != nil {
		return f.options.FilterCallback(k, v)
	}

	return false
}


// MergeFile 从文件中合并数据
// @receiver f 
// @param filename string: 要合并的文件路径，文件必须可读
// @return uint uint:  成功合并的数据项数量，表示从文件中读取并添加了多少行
// @return error error: 可能出现的错误
func (f *FileDB) MergeFile(filename string) (uint, error) {
	// 打开指定文件
	newF, err := os.Open(filename)
	if err != nil {
		return 0, err
	}
	defer newF.Close()

	return f.MergeReader(newF)
}

// MergeReader  从Reader合并数据
// @receiver f 
// @param reader io.Reader: 实现了io.Reader接口的数据源
// @return uint uint: 成功合并的数据项数量，表示从Reader中读取并添加了多少行
// @return error error: 可能出现的错误
func (f *FileDB) MergeReader(reader io.Reader) (uint, error) {
	var count uint
	// 使用bufio.Scanner逐行读取数据
	sc := bufio.NewScanner(reader)
	buf := make([]byte, BufferSize)
	sc.Buffer(buf, BufferSize)
	var itemToWrite bytes.Buffer
	for sc.Scan() {
		itemToWrite.Write(sc.Bytes())
		itemToWrite.WriteString(NewLine)
		// 写入临时数据库
		if _, err := f.tmpDbWriter.Write(itemToWrite.Bytes()); err != nil {
			return 0, err
		}
		itemToWrite.Reset()
		count++
		f.stats.NumberOfAddedItems++
	}
	return count, nil
}

