// Author: chenjb
// Version: V1.0
// Date: 2025-07-18 11:15:02
// FilePath: /yaml_scan/pkg/wappalyzergo/patterns.go
// Description: 定义指纹模式解析和匹配功能，提供正则表达式处理和版本提取能力
package wappalyzergo

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
)

// ParsedPattern 封装了一个正则表达式及其相关的元数据
// 用于指纹的匹配和版本提取，提供了完整的模式匹配功能
type ParsedPattern struct {
	regex *regexp.Regexp // 编译后的正则表达式，用于实际的模式匹配

	Confidence int    // 匹配置信度（0-100），表示该模式匹配结果的可信程度
	Version    string // 版本提取模式，用于从匹配结果中提取版本号
	SkipRegex  bool   // 是否跳过正则表达式匹配，用于简单的字符串存在性检查
}

const (
	// verCap1 捕获一组数字后跟一个或多个 ".\d+" 模式
	// 用于匹配常见的版本号格式，如 "1.2.3"
	verCap1        = `(\d+(?:\.\d+)+)` // captures 1 set of digits '\d+' followed by one or more '\.\d+' patterns
	
	// verCap1Fill 是 verCap1 的占位符，用于正则表达式预处理
	// 在处理过程中临时替换版本捕获组，避免与量词限制冲突
	verCap1Fill    = "__verCap1__"
	
	// verCap1Limited 是 verCap1 的限制版本，添加了数字长度限制
	// 防止正则表达式回溯攻击，限制每组数字最多20位
	verCap1Limited = `(\d{1,20}(?:\.\d{1,20}){1,20})`

	// verCap2 捕获一个或多个 "\d+." 模式后跟一组数字
	// 用于匹配另一种常见的版本号格式，如 "1.2.3"
	verCap2        = `((?:\d+\.)+\d+)` // captures 1 or more '\d+\.' patterns followed by 1 set of digits '\d+'
	
	// verCap2Fill 是 verCap2 的占位符，用于正则表达式预处理
	// 在处理过程中临时替换版本捕获组，避免与量词限制冲突
	verCap2Fill    = "__verCap2__"
	
	// verCap2Limited 是 verCap2 的限制版本，添加了数字长度限制
	// 防止正则表达式回溯攻击，限制每组数字最多20位
	verCap2Limited = `((?:\d{1,20}\.){1,20}\d{1,20})`
)

// ParsePattern 解析指纹模式字符串并编译为ParsedPattern
// @param pattern string: 指纹模式字符串，支持多种格式：
//     * 简单正则: "nginx"
//     * 带版本提取: "nginx/([\\d.]+)"
//     * 带置信度: "nginx\\;confidence:90"
//     * 复合模式: "nginx/([\\d.]+)\\;confidence:95\\;version:\\1"
// @return *ParsedPattern *ParsedPattern: 解析后的模式对象，包含编译的正则表达式
// @return error error: 解析过程中的错误
func ParsePattern(pattern string) (*ParsedPattern, error) {
	// 使用 "\;" 分隔符拆分模式字符串
	// 第一部分是正则表达式模式，后续部分是元数据（如置信度、版本提取规则）
	parts := strings.Split(pattern, "\\;")
	// 默认置信度为100
	p := &ParsedPattern{Confidence: 100}

	// 如果第一部分为空，表示跳过正则匹配
	// 这种情况通常用于简单的存在性检查或特殊处理
	if parts[0] == "" {
		p.SkipRegex = true
	}
	
	// 遍历所有部分进行解析
	for i, part := range parts {
		if i == 0 {
			// 第一部分是正则表达式模式
			if p.SkipRegex {
				continue
			}
			regexPattern := part

			// 保存版本捕获组，避免与后续处理冲突
			// 使用占位符临时替换版本捕获模式
			regexPattern = strings.ReplaceAll(regexPattern, verCap1, verCap1Fill)
			regexPattern = strings.ReplaceAll(regexPattern, verCap2, verCap2Fill)

			// 处理量词，限制匹配长度以防止回溯攻击
			// 将 "+" 替换为 "{1,250}"，将 "*" 替换为 "{0,250}"
			// 先处理转义的加号 "\+"，避免误替换
			regexPattern = strings.ReplaceAll(regexPattern, "\\+", "__escapedPlus__")
			regexPattern = strings.ReplaceAll(regexPattern, "+", "{1,250}")
			regexPattern = strings.ReplaceAll(regexPattern, "*", "{0,250}")
			regexPattern = strings.ReplaceAll(regexPattern, "__escapedPlus__", "\\+")

			// 恢复版本捕获组，使用带限制的版本
			// 这确保版本捕获组的安全性和有效性
			regexPattern = strings.ReplaceAll(regexPattern, verCap1Fill, verCap1Limited)
			regexPattern = strings.ReplaceAll(regexPattern, verCap2Fill, verCap2Limited)

			// 编译正则表达式，添加 (?i) 前缀使匹配不区分大小写
			var err error
			p.regex, err = regexp.Compile("(?i)" + regexPattern)
			if err != nil {
				return nil, err
			}
		} else {
			// 处理元数据部分（如置信度、版本提取规则）
			// 格式为 "key:value"
			keyValue := strings.SplitN(part, ":", 2)
			if len(keyValue) < 2 {
				continue
			}

			// 根据键名处理不同类型的元数据
			switch keyValue[0] {
			case "confidence":
				// 解析置信度值（0-100）
				conf, err := strconv.Atoi(keyValue[1])
				if err != nil {
					// If conversion fails, keep default confidence
					p.Confidence = 100
				} else {
					p.Confidence = conf
				}
			case "version":
				// 设置版本提取模式
				// 通常包含 \1, \2 等反向引用，指向正则表达式的捕获组
				p.Version = keyValue[1]
			}
		}
	}
	return p, nil
}

// Evaluate 评估目标字符串是否匹配当前模式
// 该方法执行实际的模式匹配并提取版本信息
//
// 参数:
//   - target: 要匹配的目标字符串
//
// 返回值:
//   - bool: 是否匹配成功
//   - string: 提取的版本号，如果未提取到则为空字符串
//
// 处理逻辑:
//   1. 如果设置了SkipRegex，直接返回匹配成功
//   2. 如果正则表达式为nil，返回匹配失败
//   3. 使用正则表达式匹配目标字符串
//   4. 如果匹配成功，尝试提取版本信息
//
// 使用示例:
//   pattern, _ := ParsePattern("nginx/([\\d.]+)")
//   matched, version := pattern.Evaluate("Server: nginx/1.18.0")
//   // matched = true, version = "1.18.0"
func (p *ParsedPattern) Evaluate(target string) (bool, string) {
	// 如果设置了跳过正则匹配，直接返回匹配成功
	// 这种情况通常用于简单的存在性检查
	if p.SkipRegex {
		return true, ""
	}
	
	// 如果正则表达式为nil，返回匹配失败
	// 这可能是由于编译错误或未初始化
	if p.regex == nil {
		return false, ""
	}

	// 使用正则表达式匹配目标字符串
	// FindStringSubmatch返回完整匹配和所有捕获组
	submatches := p.regex.FindStringSubmatch(target)
	if len(submatches) == 0 {
		return false, ""
	}
	
	// 如果匹配成功，尝试提取版本信息
	extractedVersion, _ := p.extractVersion(submatches)
	return true, extractedVersion
}

// extractVersion 使用提供的模式从目标字符串中提取版本信息
// 该方法根据Version字段定义的模式从正则匹配结果中提取版本号
//
// 参数:
//   - submatches: 正则表达式匹配结果，包含完整匹配和所有捕获组
//
// 返回值:
//   - string: 提取的版本号，如果未提取到则为空字符串
//   - error: 提取过程中的错误
//
// 处理逻辑:
//   1. 检查匹配结果是否为空
//   2. 使用Version字段定义的模式替换反向引用
//   3. 处理可能的三元表达式
//   4. 返回最终的版本字符串
//
// 使用示例:
//   // 假设正则表达式为 "nginx/([\\d.]+)"，Version为 "\\1"
//   // 匹配结果为 ["nginx/1.18.0", "1.18.0"]
//   version, _ := pattern.extractVersion(submatches)
//   // version = "1.18.0"
func (p *ParsedPattern) extractVersion(submatches []string) (string, error) {
	// 检查匹配结果是否为空
	if len(submatches) == 0 {
		return "", nil // No matches found
	}

	// 使用Version字段定义的模式作为结果模板
	result := p.Version
	
	// 替换所有反向引用（\1, \2等）为对应的捕获组内容
	// 从索引1开始，跳过完整匹配（索引0）
	for i, match := range submatches[1:] { // Start from 1 to skip the entire match
		placeholder := fmt.Sprintf("\\%d", i+1)
		result = strings.ReplaceAll(result, placeholder, match)
	}

	// 处理结果中的三元表达式
	// 例如: "\\1?\\1:unknown" 表示如果\\1存在则使用\\1，否则使用"unknown"
	result, err := evaluateVersionExpression(result, submatches[1:])
	if err != nil {
		return "", err
	}
	
	// 移除结果中的前后空白字符
	return strings.TrimSpace(result), nil
}

// evaluateVersionExpression 处理版本字符串中的三元表达式
// 该函数解析和评估版本模式中的条件表达式
//
// 参数:
//   - expression: 包含三元表达式的版本模式
//   - submatches: 正则表达式捕获组结果
//
// 返回值:
//   - string: 评估后的版本字符串
//   - error: 评估过程中的错误
//
// 三元表达式格式:
//   - condition?trueValue:falseValue
//   - 如果condition为真，返回trueValue，否则返回falseValue
//   - 在版本提取中，通常用于处理可选的版本信息
//
// 使用示例:
//   // 表达式 "\\1?\\1:unknown" 表示如果\\1存在则使用\\1，否则使用"unknown"
//   version, _ := evaluateVersionExpression("\\1?\\1:unknown", ["1.18.0"])
//   // version = "1.18.0"
func evaluateVersionExpression(expression string, submatches []string) (string, error) {
	// 检查是否包含三元表达式（包含问号）
	if strings.Contains(expression, "?") {
		// 按问号分割，得到条件和结果部分
		parts := strings.Split(expression, "?")
		if len(parts) != 2 {
			return "", fmt.Errorf("invalid ternary expression: %s", expression)
		}

		// 分割真值和假值部分
		trueFalseParts := strings.Split(parts[1], ":")
		if len(trueFalseParts) != 2 {
			return "", fmt.Errorf("invalid true/false parts in ternary expression: %s", expression)
		}

		// 简单的存在性检查
		// 如果真值部分不为空，根据捕获组是否存在返回真值或假值
		if trueFalseParts[0] != "" { // Simple existence check
			if len(submatches) == 0 {
				return trueFalseParts[1], nil
			}
			return trueFalseParts[0], nil
		}
		
		// 处理特殊情况：假值为空
		if trueFalseParts[1] == "" {
			if len(submatches) == 0 {
				return "", nil
			}
			return trueFalseParts[0], nil
		}
		
		// 默认返回假值
		return trueFalseParts[1], nil
	}

	// 如果不包含三元表达式，直接返回原始表达式
	return expression, nil
}
@
