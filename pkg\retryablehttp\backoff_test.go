// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-11 16:30:44
// FilePath: /yaml_scan/pkg/retryablehttp/backoff_test.go
// Description: 
package retryablehttp


import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestDefaultBackoff 测试默认退避策略
func TestDefaultBackoff(t *testing.T) {
	r := require.New(t)

	// 创建退避策略函数
	backoff := DefaultBackoff()
	r.NotNil(backoff, "退避策略函数不应为nil")

	// 定义测试参数
	minDuration := 1 * time.Second
	maxDuration := 30 * time.Second

	// 测试用例1: 初始尝试 (attemptNum=0)
	t.Run("初始尝试", func(t *testing.T) {
		r := require.New(t)
		duration := backoff(minDuration, maxDuration, 0, nil)
		r.Equal(minDuration, duration, "初始尝试应返回最小等待时间")
	})

	// 测试用例2: 第一次重试 (attemptNum=1)
	t.Run("第一次重试", func(t *testing.T) {
		r := require.New(t)
		duration := backoff(minDuration, maxDuration, 1, nil)
		// 2^1 * minDuration = 2s
		r.Equal(2*minDuration, duration, "第一次重试应返回2倍的最小等待时间")
	})

	// 测试用例3: 第二次重试 (attemptNum=2)
	t.Run("第二次重试", func(t *testing.T) {
		r := require.New(t)
		duration := backoff(minDuration, maxDuration, 2, nil)
		// 2^2 * minDuration = 4s
		r.Equal(4*minDuration, duration, "第二次重试应返回4倍的最小等待时间")
	})

	// 测试用例4: 超过最大值
	t.Run("超过最大值", func(t *testing.T) {
		r := require.New(t)
		// 使用很高的尝试次数，使计算的退避时间超过最大值
		duration := backoff(minDuration, maxDuration, 10, nil) // 2^10 = 1024s
		r.Equal(maxDuration, duration, "超过最大值时应返回最大等待时间")
	})
}

// TestLinearJitterBackoff 测试线性抖动退避策略
func TestLinearJitterBackoff(t *testing.T) {
	r := require.New(t)

	// 创建退避策略函数
	backoff := LinearJitterBackoff()
	r.NotNil(backoff, "退避策略函数不应为nil")

	// 定义测试参数
	minDuration := 1 * time.Second
	maxDuration := 30 * time.Second

	// 测试用例1: 检查多次调用的范围 (attemptNum=1)
	t.Run("检查值范围-尝试1", func(t *testing.T) {
		r := require.New(t)

		// 多次调用，验证结果在预期范围内
		for i := 0; i < 100; i++ {
			duration := backoff(minDuration, maxDuration, 1, nil)
			// 对于attemptNum=1，结果应该在min和max之间，乘以1+1=2
			r.GreaterOrEqual(duration, minDuration*2, "退避时间应大于等于2倍最小值")
			r.LessOrEqual(duration, maxDuration*2, "退避时间应小于等于2倍最大值")
		}
	})

	// 测试用例2: min=max的情况
	t.Run("min等于max", func(t *testing.T) {
		r := require.New(t)
		sameValue := 5 * time.Second
		duration := backoff(sameValue, sameValue, 1, nil)
		r.Equal(sameValue*2, duration, "当min=max时，应返回值乘以attemptNum+1")
	})

	// 测试用例3: 检查尝试次数的影响
	t.Run("检查尝试次数影响", func(t *testing.T) {
		r := require.New(t)

		// 收集不同尝试次数的多次调用结果
		attempt1Times := []time.Duration{}
		attempt2Times := []time.Duration{}

		for i := 0; i < 100; i++ {
			attempt1Times = append(attempt1Times, backoff(minDuration, maxDuration, 1, nil))
			attempt2Times = append(attempt2Times, backoff(minDuration, maxDuration, 2, nil))
		}

		// 计算平均值，后者应该约为前者的1.5倍（因为尝试次数增加了）
		var sum1, sum2 time.Duration
		for i := 0; i < len(attempt1Times); i++ {
			sum1 += attempt1Times[i]
			sum2 += attempt2Times[i]
		}

		avg1 := sum1 / time.Duration(len(attempt1Times))
		avg2 := sum2 / time.Duration(len(attempt2Times))

		// 考虑到随机性，我们只能粗略比较
		r.Greater(avg2, avg1, "平均退避时间应随尝试次数增加而增加")
	})
}

// TestFullJitterBackoff 测试全抖动退避策略
func TestFullJitterBackoff(t *testing.T) {
	r := require.New(t)

	// 创建退避策略函数
	backoff := FullJitterBackoff()
	r.NotNil(backoff, "退避策略函数不应为nil")

	// 定义测试参数
	minDuration := 100 * time.Millisecond
	maxDuration := 10 * time.Second

	// 测试用例1: 检查多次调用的范围 (attemptNum=1)
	t.Run("检查值范围", func(t *testing.T) {
		r := require.New(t)

		// 多次调用，验证结果在预期范围内
		for i := 0; i < 100; i++ {
			duration := backoff(minDuration, maxDuration, 1, nil)
			r.GreaterOrEqual(duration, minDuration, "退避时间应大于等于最小值")
			r.LessOrEqual(duration, maxDuration, "退避时间应小于等于最大值")
		}
	})

	// 测试用例2: 检查尝试次数的影响
	t.Run("检查尝试次数影响", func(t *testing.T) {
		r := require.New(t)

		// 对于更高的尝试次数，平均值应该更大
		attempt1Times := []time.Duration{}
		attempt3Times := []time.Duration{}

		for i := 0; i < 100; i++ {
			attempt1Times = append(attempt1Times, backoff(minDuration, maxDuration, 1, nil))
			attempt3Times = append(attempt3Times, backoff(minDuration, maxDuration, 3, nil))
		}

		// 计算平均值
		var sum1, sum3 time.Duration
		for i := 0; i < len(attempt1Times); i++ {
			sum1 += attempt1Times[i]
			sum3 += attempt3Times[i]
		}

		avg1 := sum1 / time.Duration(len(attempt1Times))
		avg3 := sum3 / time.Duration(len(attempt3Times))

		// 考虑到随机性，但尝试次数更高应该产生更大的平均等待时间
		r.Greater(avg3, avg1, "平均退避时间应随尝试次数增加而增加")
	})

}

// TestExponentialJitterBackoff 测试指数抖动退避策略
func TestExponentialJitterBackoff(t *testing.T) {
	r := require.New(t)

	// 创建退避策略函数
	backoff := ExponentialJitterBackoff()
	r.NotNil(backoff, "退避策略函数不应为nil")

	// 定义测试参数
	minDuration := 100 * time.Millisecond
	maxDuration := 10 * time.Second

	// 测试用例1: 检查初始调用的范围
	t.Run("检查初始值范围", func(t *testing.T) {
		r := require.New(t)

		duration := backoff(minDuration, maxDuration, 0, nil)
		// 对于attemptNum=0，基本值是min，加上抖动，应该大于min
		r.GreaterOrEqual(duration, minDuration, "初始退避时间应大于等于最小值")
		// 抖动不会使它超过2*min
		r.LessOrEqual(duration, 2*minDuration, "初始退避时间应不超过2倍最小值")
	})

	// 测试用例2: 检查尝试次数的影响
	t.Run("检查尝试次数影响", func(t *testing.T) {
		r := require.New(t)

		attempt1Times := []time.Duration{}
		attempt2Times := []time.Duration{}

		for i := 0; i < 50; i++ {
			attempt1Times = append(attempt1Times, backoff(minDuration, maxDuration, 1, nil))
			attempt2Times = append(attempt2Times, backoff(minDuration, maxDuration, 2, nil))
		}

		// 计算平均值
		var sum1, sum2 time.Duration
		for i := 0; i < len(attempt1Times); i++ {
			sum1 += attempt1Times[i]
			sum2 += attempt2Times[i]
		}

		avg1 := sum1 / time.Duration(len(attempt1Times))
		avg2 := sum2 / time.Duration(len(attempt2Times))

		// 指数增长意味着第二次尝试的平均等待时间应该明显大于第一次
		r.Greater(avg2, time.Duration(1)*avg1, "指数退避应使平均等待时间显著增加")
	})

	// 测试用例3: 检查最大值限制
	t.Run("检查最大值限制", func(t *testing.T) {
		r := require.New(t)

		// 使用很高的尝试次数，应该受最大值限制
		for i := 0; i < 50; i++ {
			duration := backoff(minDuration, maxDuration, 20, nil) // 2^20很大
			r.Equal(maxDuration, duration, "非常高的尝试次数应返回最大等待时间")
		}
	})
}
