//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-23 20:18:11
// FilePath: /yaml_scan/pkg/tlsx/tlsx/tlsx.go
// Description:: TLS 扫描工具包主文件，用于提供TLS连接和分析功能

package tlsx

import (
	"strconv"
	"yaml_scan/pkg/fastdialer"
	"yaml_scan/pkg/tlsx/tlsx/auto"
	"yaml_scan/pkg/tlsx/tlsx/clients"
	"yaml_scan/pkg/tlsx/tlsx/jarm"
	"yaml_scan/pkg/tlsx/tlsx/openssl"
	"yaml_scan/pkg/tlsx/tlsx/tls"
	"yaml_scan/pkg/tlsx/tlsx/ztls"
	errorutil "yaml_scan/utils/errors"
	sliceutil "yaml_scan/utils/slice"
)

// Service 是tlsx模块的服务结构体
type Service struct {
	options *clients.Options       // TLS连接的配置选项
	client  clients.Implementation // 具体的TLS客户端实现
}

// New 创建一个新的tlsx服务模块
// @param options *clients.Options: TLS连接的配置选项，包含超时时间、重试次数、扫描模式等
// @return *Service *Service: 创建的服务实例
// @return error error: 可能的错误
func New(options *clients.Options) (*Service, error) {
	service := &Service{
		options: options,
	}
	// 如果没有提供快速拨号器，创建一个默认的拨号器
	if options.Fastdialer == nil {
		var err error
		options.Fastdialer, err = fastdialer.NewDialer(fastdialer.DefaultOptions)
		if err != nil {
			return nil, err
		}
	}

	var err error
	// 根据扫描模式选择合适的TLS客户端实现
	switch options.ScanMode {
	case "ztls":
		// 使用ztls实现
		service.client, err = ztls.New(options)
	case "ctls":
		// 使用原生tls实现
		service.client, err = tls.New(options)
	case "openssl":
		// 使用openssl实现
		service.client, err = openssl.New(options)
	case "auto":
		// 使用自动模式，会尝试多种实现
		service.client, err = auto.New(options)
	default:
		// 默认使用原生TLS实现
		service.client, err = tls.New(options)
		options.ScanMode = "ctls"
	}
	if err != nil {
		return nil, errorutil.NewWithTag("auto", "could not create tls service").Wrap(err)
	}
	return service, nil
}

// Connect 连接到指定的主机和端口，使用默认连接选项
// 这是ConnectWithOptions的简化版本，使用空的连接选项
// @receiver s
// @param host string: 目标主机名或域名
// @param ip string: 目标IP地址
// @param port string: 目标端口号（字符串格式）
// @return *clients.Response *clients.Response: TLS连接响应信息，包含证书、版本等详细信息
// @return error error: 连接过程中的错误，如果成功则为nil
func (s *Service) Connect(host, ip, port string) (*clients.Response, error) {
	return s.ConnectWithOptions(host, ip, port, clients.ConnectOptions{})
}

// ConnectWithOptions 使用自定义选项连接到指定的主机和端口
// 支持TLS版本枚举、密码套件枚举、JARM指纹识别等高级功能
// @receiver s
// @param host string: 目标主机名或域名
// @param ip string: 目标IP地址
// @param port string: 目标端口号（字符串格式）
// @param options clients.ConnectOptions:连接选项，包含SNI、TLS版本、枚举模式等配置
// @return *clients.Response *clients.Response: TLS连接响应信息，
// @return error error: 连接过程中的错误，如果成功则为nil
func (s *Service) ConnectWithOptions(host, ip, port string, options clients.ConnectOptions) (*clients.Response, error) {
	var resp *clients.Response
	var err error

	// 输入参数验证：确保提供了有效的地址和端口
	if (host == "" && ip == "") || port == "" {
		return nil, errorutil.NewWithTag("tlsx", "tlsx requires valid address got port=%v,hostname=%v,ip=%v", port, host, ip)
	}

	// 根据扫描模式决定是否进行重试
	if s.options.ScanMode != "auto" && s.options.ScanMode != "" {
		// 非auto模式需要进行重试，因为auto模式内部已经有fallback机制
		// 这可以被视为重试机制的一部分
		for i := 0; i < s.options.Retries; i++ {
			if resp, err = s.client.ConnectWithOptions(host, ip, port, options); resp != nil {
				err = nil
				break
			}
		}
	} else {
		// auto模式或空模式，只尝试一次连接
		if resp, err = s.client.ConnectWithOptions(host, ip, port, options); resp != nil {
			err = nil
		}
	}

	// 检查连接结果：如果既没有响应也没有错误，说明出现异常情况
	if resp == nil && err == nil {
		return nil, errorutil.NewWithTag("auto", "no response returned for connection")
	}
	// 处理连接错误
	if err != nil {
		wrappedErr := errorutil.NewWithTag("auto", "could not connect to host").Wrap(err)

		return nil, wrappedErr
	}

	// 如果启用了JARM指纹识别，计算JARM哈希值
	if s.options.Jarm {
		port, _ := strconv.Atoi(port)
		jarmhash, err := jarm.HashWithDialer(s.options.Fastdialer, host, port, s.options.Timeout)
		if err != nil {
			return resp, err
		}
		resp.JarmHash = jarmhash
	}

	// 如果启用了TLS版本枚举，枚举所有支持的TLS版本
	if s.options.TlsVersionsEnum {
		options.EnumMode = clients.Version
		supportedTlsVersions := []string{resp.Version}
		enumeratedTlsVersions, _ := s.enumTlsVersions(host, ip, port, options)
		supportedTlsVersions = append(supportedTlsVersions, enumeratedTlsVersions...)
		resp.VersionEnum = sliceutil.Dedupe(supportedTlsVersions)
	}

	// 如果启用了TLS密码套件枚举，为每个支持的TLS版本枚举密码套件
	var supportedTlsCiphers []clients.TlsCiphers
	if s.options.TlsCiphersEnum {
		options.EnumMode = clients.Cipher
		for _, supportedTlsVersion := range resp.VersionEnum {
			options.VersionTLS = supportedTlsVersion
			enumeratedTlsCiphers, _ := s.enumTlsCiphers(host, ip, port, options)
			enumeratedTlsCiphers = sliceutil.Dedupe(enumeratedTlsCiphers)
			// 识别密码套件类型（安全级别等）
			cipherTypes := clients.IdentifyCiphers(enumeratedTlsCiphers)
			supportedTlsCiphers = append(supportedTlsCiphers, clients.TlsCiphers{Version: supportedTlsVersion, Ciphers: cipherTypes})
		}
		resp.TlsCiphers = supportedTlsCiphers
	}
	return resp, nil
}

// enumTlsVersions 枚举目标主机支持的所有TLS版本
// 通过尝试连接不同的TLS版本来确定服务器的支持情况
// @receiver s 
// @param host string: 目标主机名或域名
// @param ip string: 目标IP地址
// @param port string: 目标端口号（字符串格式）	
// @param options clients.ConnectOptions: 连接选项，包含枚举相关配置
// @return []string []string: 服务器支持的TLS版本列表
// @return error error: 
func (s *Service) enumTlsVersions(host, ip, port string, options clients.ConnectOptions) ([]string, error) {
	var enumeratedTlsVersions []string
	// 获取客户端支持的所有TLS版本
	clientSupportedTlsVersions, err := s.client.SupportedTLSVersions()
	if err != nil {
		return nil, err
	}
	for _, tlsVersion := range clientSupportedTlsVersions {
		options.VersionTLS = tlsVersion
		// 尝试使用指定版本连接
		if resp, err := s.client.ConnectWithOptions(host, ip, port, options); err == nil && resp != nil && resp.Version == tlsVersion {
			enumeratedTlsVersions = append(enumeratedTlsVersions, tlsVersion)
		}
	}
	return enumeratedTlsVersions, nil
}

// enumTlsCiphers 枚举指定TLS版本下服务器支持的密码套件
// 根据配置的密码套件安全级别进行枚举
// @receiver s 
// @param host string:  目标主机名或域名
// @param ip string:  目标IP地址
// @param port string:  目标端口号（字符串格式）
// @param options clients.ConnectOptions: 连接选项，包含TLS版本和枚举配置	
// @return []string []string: 服务器支持的密码套件列表
// @return error error: 
func (s *Service) enumTlsCiphers(host, ip, port string, options clients.ConnectOptions) ([]string, error) {
	options.EnumMode = clients.Cipher
	// 根据配置的密码套件级别设置枚举范围
	for _, cipher := range s.options.TLsCipherLevel {

		switch cipher {
		case "weak":
			// 添加弱密码套件到枚举范围
			options.CipherLevel = append(options.CipherLevel, clients.Weak)
		case "secure":
			// 添加安全密码套件到枚举范围
			options.CipherLevel = append(options.CipherLevel, clients.Secure)
		case "insecure":
			// 添加不安全密码套件到枚举范围
			options.CipherLevel = append(options.CipherLevel, clients.Insecure)
		default:
			// 默认添加所有密码套件到枚举范围
			options.CipherLevel = append(options.CipherLevel, clients.All)
		}
	}
	// 调用客户端的密码套件枚举功能
	return s.client.EnumerateCiphers(host, ip, port, options)
}
