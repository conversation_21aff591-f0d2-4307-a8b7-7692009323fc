//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-15 16:24:39
//FilePath: /yaml_scan/pkg/input/formats/openapi/examples.go
//Description:

package openapi

import (
	"fmt"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/pkg/errors"
)

// cachedSchema 用于缓存OpenAPI 3 Schema对象的处理结果
type cachedSchema struct {
	// pending 表示当前Schema对象是否正在处理中
	// 如果为true，表示该Schema对象的示例生成尚未完成，可能用于检测循环引用。
	pending bool

	// out 存储生成的示例结构
	// 这是从Schema对象生成的示例，类型为interface{}，可以是任何类型，具体取决于Schema的定义。
	out interface{}
}

var (
	// ErrRecursive 是指模式由于无限递归而无法表示。
	ErrRecursive = errors.New("Recursive schema")

	// ErrNoExample 当没有找到操作示例
	ErrNoExample = errors.New("No example found")
)

// getSchemaExample: 从OpenAPI 3的Schema对象中提取示例值
//
//	@param schema *openapi3.Schema: 表示OpenAPI 3的Schema对象。
//	@return interface{} interface{}:  返回Schema对象中的示例值。如果没有示例值，返回nil。
//	@return bool bool:  如果成功提取到示例值，返回true；否则返回false。
//
// 逻辑:
// - 优先返回Schema对象中显式定义的Example值。
// - 如果没有Example值，则返回Default值（如果存在）。
// - 如果没有Default值，但Enum值列表不为空，则返回Enum列表中的第一个值。
// - 如果以上条件都不满足，返回nil和false。
func getSchemaExample(schema *openapi3.Schema) (interface{}, bool) {
	if schema.Example != nil {
		return schema.Example, true
	}

	if schema.Default != nil {
		return schema.Default, true
	}

	if len(schema.Enum) > 0 {
		return schema.Enum[0], true
	}
	return nil, false
}

// stringFormatExample: 根据给定的格式返回一个示例字符串

// @param format string: 表示字符串的格式。
// @return string string: 返回与格式对应的示例字符串。如果格式不在已知列表中，返回空字符串。这些格式大多基于JSON Schema的字符串格式规范
func stringFormatExample(format string) string {
	switch format {
	case "date":
		// https://tools.ietf.org/html/rfc3339
		return "2018-07-23"
	case "date-time":
		// This is the date/time of API Sprout's first commit! :-)
		return "2018-07-23T22:58:00-07:00"
	case "time":
		return "22:58:00-07:00"
	case "email":
		return "<EMAIL>"
	case "hostname":
		// https://tools.ietf.org/html/rfc2606#page-2
		return "example.com"
	case "ipv4":
		// https://tools.ietf.org/html/rfc5737
		return "************"
	case "ipv6":
		// https://tools.ietf.org/html/rfc3849
		return "2001:0db8:85a3:0000:0000:8a2e:0370:7334"
	case "uri":
		return "https://tools.ietf.org/html/rfc3986"
	case "uri-template":
		// https://tools.ietf.org/html/rfc6570
		return "http://example.com/dictionary/{term:1}/{term}"
	case "json-pointer":
		// https://tools.ietf.org/html/rfc6901
		return "#/components/parameters/term"
	case "regex":
		// https://stackoverflow.com/q/3296050/164268
		return "/^1?$|^(11+?)\\1+$/"
	case "uuid":
		// https://www.ietf.org/rfc/rfc4122.txt
		return "f81d4fae-7dec-11d0-a765-00a0c91e6bf6"
	case "password":
		return "********"
	case "binary":
		return "sagefuzzertest"
	}
	return ""
}

// excludeFromMode: 根据模式排除特定的Schema对象
//
//	@param schema *openapi3.Schema: 表示OpenAPI 3的Schema对象。
//	@return bool bool: 如果Schema对象应被排除，返回true；否则返回false。
//
// 逻辑:
// - 如果Schema对象为nil，返回true，表示应被排除。
// - 如果Schema对象的ReadOnly属性为true，返回true，表示在请求模式下应被排除。
func excludeFromMode(schema *openapi3.Schema) bool {
	if schema == nil {
		return true
	}

	if schema.ReadOnly {
		return true
	}
	return false
}

// isRequired: 检查给定的键是否在Schema对象中被标记为必需
//
//	@param schema *openapi3.Schema:表示OpenAPI 3的Schema对象。
//	@param key string:表示需要检查的键名。
//	@return bool bool: 果键在Schema对象的必需字段列表中，返回true；否则返回false。
func isRequired(schema *openapi3.Schema, key string) bool {
	for _, req := range schema.Required {
		if req == key {
			return true
		}
	}

	return false
}

// openAPIExample: 从OpenAPI 3的Schema对象生成一个示例结构
//
//	@param schema *openapi3.Schema: 表示OpenAPI 3的Schema对象。
//	@param cache map[*openapi3.Schema]*cachedSchema: 用于缓存已处理的Schema对象，避免重复处理和递归问题。
//	@return out interface{}: 返回生成的示例结构，可以是任何类型，具体取决于Schema的定义。
//	@return err error: 如果在生成过程中出现错误，返回一个error对象，否则返回nil。
func openAPIExample(schema *openapi3.Schema, cache map[*openapi3.Schema]*cachedSchema) (out interface{}, err error) {
	// 检查Schema是否有示例值，如果有则直接返回
	if ex, ok := getSchemaExample(schema); ok {
		return ex, nil
	}
	// 检查缓存中是否已有该Schema的处理结果
	cached, ok := cache[schema]
	if !ok {
		// 如果没有缓存，创建一个新的缓存条目并标记为处理中
		cached = &cachedSchema{
			pending: true,
		}
		cache[schema] = cached
	} else if cached.pending {
		// 如果缓存条目标记为处理中，说明存在递归引用，返回递归错误
		return nil, ErrRecursive
	} else {
		// 如果缓存中已有结果，直接返回缓存的结果
		return cached.out, nil
	}

	// 使用defer确保在函数返回前更新缓存条目
	defer func() {
		cached.pending = false
		cached.out = out
	}()

	// 处理组合关键字
	if len(schema.OneOf) > 0 {
		var ex interface{}
		var err error
		// 尝试从OneOf的候选项中生成示例，直到成功为止
		for _, candidate := range schema.OneOf {
			ex, err = openAPIExample(candidate.Value, cache)
			if err == nil {
				break
			}
		}
		return ex, err
	}
	if len(schema.AnyOf) > 0 {
		var ex interface{}
		var err error

		for _, candidate := range schema.AnyOf {
			ex, err = openAPIExample(candidate.Value, cache)
			if err == nil {
				break
			}
		}
		return ex, err
	}
	if len(schema.AllOf) > 0 {
		example := map[string]interface{}{}

		// 合并AllOf的所有子Schema的示例
		for _, allOf := range schema.AllOf {
			candidate, err := openAPIExample(allOf.Value, cache)
			if err != nil {
				return nil, err
			}

			value, ok := candidate.(map[string]interface{})
			if !ok {
				return nil, ErrNoExample
			}

			for k, v := range value {
				example[k] = v
			}
		}
		return example, nil
	}

	// 根据Schema的类型生成示例
	switch {
	case schema.Type.Is("boolean"):
		// 布尔类型返回true作为示例
		return true, nil
	case schema.Type.Is("number"), schema.Type.Is("integer"):
		// 数字和整数类型生成示例
		value := 0.0

		// 处理最小值约束
		if schema.Min != nil && *schema.Min > value {
			value = *schema.Min
			if schema.ExclusiveMin {
				if schema.Max != nil {
					// 如果有最大值，取中间值
					value = (*schema.Min + *schema.Max) / 2.0
				} else {
					value++
				}
			}
		}
		// 处理最大值约束
		if schema.Max != nil && *schema.Max < value {
			value = *schema.Max
			if schema.ExclusiveMax {
				if schema.Min != nil {
					// 如果有最小值，取中间值
					value = (*schema.Min + *schema.Max) / 2.0
				} else {
					value--
				}
			}
		}
		// 处理倍数约束
		if schema.MultipleOf != nil && int(value)%int(*schema.MultipleOf) != 0 {
			value += float64(int(*schema.MultipleOf) - (int(value) % int(*schema.MultipleOf)))
		}
		// 如果是整数类型，返回整数值
		if schema.Type.Is("integer") {
			return int(value), nil
		}
		return value, nil
	case schema.Type.Is("string"):
		// 字符串类型生成示例
		if ex := stringFormatExample(schema.Format); ex != "" {
			return ex, nil
		}
		example := "string"

		// 处理最小长度约束
		for schema.MinLength > uint64(len(example)) {
			example += example
		}
		// 处理最大长度约束
		if schema.MaxLength != nil && *schema.MaxLength < uint64(len(example)) {
			example = example[:*schema.MaxLength]
		}
		return example, nil
	case schema.Type.Is("array"), schema.Items != nil:
		// 数组类型生成示例
		example := []interface{}{}

		if schema.Items != nil && schema.Items.Value != nil {
			ex, err := openAPIExample(schema.Items.Value, cache)
			if err != nil {
				return nil, fmt.Errorf("can't get example for array item: %+v", err)
			}

			example = append(example, ex)
			// 处理最小项数约束
			for uint64(len(example)) < schema.MinItems {
				example = append(example, ex)
			}
		}
		return example, nil
	case schema.Type.Is("object"), len(schema.Properties) > 0:
		// 对象类型生成示例
		example := map[string]interface{}{}

		for k, v := range schema.Properties {
			if excludeFromMode(v.Value) {
				continue
			}

			ex, err := openAPIExample(v.Value, cache)
			if err == ErrRecursive {
				if isRequired(schema, k) {
					return nil, fmt.Errorf("can't get example for '%s': %+v", k, err)
				}
			} else if err != nil {
				return nil, fmt.Errorf("can't get example for '%s': %+v", k, err)
			} else {
				example[k] = ex
			}
		}
		// 处理附加属性
		if schema.AdditionalProperties.Has != nil && schema.AdditionalProperties.Schema != nil {
			addl := schema.AdditionalProperties.Schema.Value

			if !excludeFromMode(addl) {
				ex, err := openAPIExample(addl, cache)
				if err == ErrRecursive {
					// 如果是递归的，不添加
				} else if err != nil {
					return nil, fmt.Errorf("can't get example for additional properties: %+v", err)
				} else {
					example["additionalPropertyName"] = ex
				}
			}
		}
		return example, nil
	}
	return nil, ErrNoExample
}

// generateExampleFromSchema: 从OpenAPI 3的Schema对象创建一个示例结构

// OpenAPI 3的Schema对象是JSON Schema的扩展子集。
// 参考: https://github.com/OAI/OpenAPI-Specification/blob/master/versions/3.0.1.md#schemaObject
//
//	@param schema *openapi3.Schema: 表示OpenAPI 3的Schema对象。
//	@return interface{} interface{}: 返回生成的示例结构，可以是任何类型，具体取决于Schema的定义。
//	@return error error: 果在生成过程中出现错误，返回一个error对象，否则返回nil。
func generateExampleFromSchema(schema *openapi3.Schema) (interface{}, error) {
	// 传递一个空的缓存映射，用于存储已处理的Schema对象以避免重复处理
	return openAPIExample(schema, make(map[*openapi3.Schema]*cachedSchema))
}
