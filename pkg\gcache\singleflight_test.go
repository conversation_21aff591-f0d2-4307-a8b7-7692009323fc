//
// Author: chenjb
// Version: V1.0
// Date: 2025-04-30 11:31:30
// FilePath: /yaml_scan/pkg/gcache/singleflight_test.go
// Description:

// Author: chenjb
// Version: V1.0
// Date: 2025-04-30 10:17:51
// FilePath: /yaml_scan/pkg/gcache/singleflight_test.go
// Description:
package gcache

import (
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestGroupDo 测试 Group 的 Do 方法
func TestGroupDo(t *testing.T) {
	cache := New[string, int](100).Build() // 创建一个缓存实例
	group := &Group[string, int]{cache: cache} // 创建 Group 实例

	// 测试缓存命中
	err := cache.Set("key1", 100) // 设置缓存
	require.NoError(t, err)       // 确保设置成功
	v, called, err := group.Do("key1", func() (int, error) { return 0, nil }, true)
	require.NoError(t, err)       // 确保无错误
	require.False(t, called)      // 验证未调用 fn
	require.Equal(t, 100, v)      // 验证返回缓存中的值

	// 测试加载函数
	loaderCalled := false
	loader := func() (int, error) {
		loaderCalled = true
		return 200, nil
	}
	v, called, err = group.Do("key2", loader, true)
	require.NoError(t, err)       // 确保无错误
	require.True(t, called)       // 验证调用了 fn
	require.True(t, loaderCalled) // 验证 loader 被调用
	require.Equal(t, 200, v)      // 验证返回加载的值

	// 测试不等待的情况
	v, called, err = group.Do("key3", loader, false)
	require.ErrorIs(t, err, KeyNotFoundError) // 验证返回 KeyNotFoundError
	require.False(t, called)                         // 验证未调用 fn
	require.Equal(t, 0, v)                           // 验证返回 nilV（int 的零值）
}

// TestGroupDoConcurrent 测试 Group 的并发安全性
func TestGroupDoConcurrent(t *testing.T) {
	cache := New[string, int](100).Build() // 创建缓存实例
	group := &Group[string, int]{cache: cache} // 创建 Group 实例

	var wg sync.WaitGroup
	loader := func() (int, error) {
		time.Sleep(100 * time.Millisecond) // 模拟加载延迟
		return 300, nil
	}

	var calledCount int32

	// 启动多个 goroutine 并发调用 Do
	for i := 0; i < 5; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			v, called, err := group.Do("key4", loader, true)
			require.NoError(t, err)  // 确保无错误
			require.Equal(t, 300, v) // 验证返回加载的值
			
			if called {
				atomic.AddInt32(&calledCount, 1)
			}
			
		}()
	}

	// 等待所有 goroutine 完成
	wg.Wait()

	require.Equal(t, int32(1), calledCount) // 验证只有一个 goroutine 调用了 fn
}