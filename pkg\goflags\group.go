//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-21 15:42:23
//FilePath: /yaml_scan/pkg/goflags/group.go
//Description:

package goflags

import (
	"strings"
)

// groupData 结构体用于存储标志组的信息
type groupData struct {
	name        string // 组名
	description string // 组描述
}

// Group 设置标志的分组
//
// name: 要设置的分组名称
func (flagData *FlagData) Group(name string) {
	flagData.group = name
}

// SetGroup 设置一个命令行选项的分组，包括名称和描述
// 该函数将一个新的分组添加到 FlagSet 的 groups 列表中。
// 分组的顺序与传入的顺序一致，便于在帮助信息中显示。
func (flagSet *FlagSet) SetGroup(name, description string) {
	flagSet.groups = append(flagSet.groups, groupData{name: name, description: description})
}

// CreateGroup 在 FlagSet 中创建一个新的分组，并将指定的标志添加到该分组
//
// groupName: 分组的名称
// description: 分组的描述
// flags: 可变参数，表示要添加到该分组的标志
func (flagSet *FlagSet) CreateGroup(groupName, description string, flags ...*FlagData) {
	flagSet.SetGroup(groupName, description)
	for _, currentFlag := range flags {
		currentFlag.Group(groupName)
	}
}

// getGroupbyName: 根据名称或描述查找并返回相应的分组数据
//
//	@receiver flagSet *FlagSet:
//	@param name string: 要查找的分组名称或描述。
//	@return groupData groupData: 返回找到的分组数据。如果未找到，则返回一个空的 groupData 实例。
func (flagSet *FlagSet) getGroupbyName(name string) groupData {
	for _, group := range flagSet.groups {
		if strings.EqualFold(group.name, name) || strings.EqualFold(group.description, name) {
			return group
		}
	}
	return groupData{}
}

// normalizeGroupDescription: 返回规范化的分组描述字段
//
//	@param description string: 要规范化的分组描述字符串。
//	@return string string: 返回转换为大写形式的分组描述。
func normalizeGroupDescription(description string) string {
	return strings.ToUpper(description)
}
