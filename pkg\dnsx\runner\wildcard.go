// Author: chenjb
// Version: V1.0
// Date: 2025-06-19 19:24:49
// FilePath: /yaml_scan/pkg/dnsx/runner/wildcard.go
// Description:  提供DNS通配符检测功能，用于识别和过滤DNS通配符记录
package runner

import (
	"strings"

	"github.com/rs/xid"
)

// IsWildcard 检查主机是否是通配符域名
// 通配符域名是指一个域名配置了通配符DNS记录（如*.domain.com）,
// 会返回相同IP地址，无论子域名是什么。此函数通过生成随机子域名
// 并比较其IP地址来检测通配符域名。
// @receiver r
// @param host string:  要检查的主机名
// @return bool bool:  如果主机是通配符域名则返回true，否则返回false
func (r *Runner) IsWildcard(host string) bool {
	orig := make(map[string]struct{})
	wildcards := make(map[string]struct{})

	// 查询原始主机的A记录
	in, err := r.dnsx.QueryOne(host)
	if err != nil || in == nil {
		return false
	}
	for _, A := range in.A {
		orig[A] = struct{}{}
	}

	// 从主机名中提取子域名部分
	subdomainPart := strings.TrimSuffix(host, "."+r.options.WildcardDomain)
	subdomainTokens := strings.Split(subdomainPart, ".")
	// 构建一个数组，预先分配切片，并创建通配符生成前缀。
	// 我们在开头使用一个随机前缀，比如 %rand%.domain.tld
	// 为子域名的每个层级生成一个排列。
	var hosts []string
	hosts = append(hosts, r.options.WildcardDomain)

	if len(subdomainTokens) > 0 {
		// 构建各级子域名组合
		for i := 1; i < len(subdomainTokens); i++ {
			newhost := strings.Join(subdomainTokens[i:], ".") + "." + r.options.WildcardDomain
			hosts = append(hosts, newhost)
		}
	}

	// 遍历为随机生成的所有主机
	for _, h := range hosts {
		r.wildcardscachemutex.Lock()
		listip, ok := r.wildcardscache[h]
		r.wildcardscachemutex.Unlock()
		if !ok {
			// 如果缓存中没有，使用随机字符串作为子域名前缀查询
			in, err := r.dnsx.QueryOne(xid.New().String() + "." + h)
			if err != nil || in == nil {
				continue
			}
			listip = in.A
			r.wildcardscachemutex.Lock()
			r.wildcardscache[h] = in.A
			r.wildcardscachemutex.Unlock()
		}

		// 获取所有记录并添加到通配符映射中
		for _, A := range listip {
			if _, ok := wildcards[A]; !ok {
				wildcards[A] = struct{}{}
			}
		}
	}

	// 检查原始IP是否在通配符IP中
	for a := range orig {
		if _, ok := wildcards[a]; ok {
			return true
		}
	}

	return false
}
