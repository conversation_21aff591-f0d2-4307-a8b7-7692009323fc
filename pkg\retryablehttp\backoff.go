// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 16:43:29
// FilePath: /yaml_scan/pkg/retryablehttp/backoff.go
// Description:实现HTTP请求重试的退避策略，包括多种退避算法如指数退避、线性抖动退避和全抖动退避
package retryablehttp

import (
	"math"
	"math/rand"
	"net/http"
	"sync"
	"time"
)

// Backoff 指定了重试之间等待多长时间的策略
// 它在请求失败后被调用，以确定在再次尝试之前应该等待多长时间
//
// 参数:
//   - min: 最小等待时间
//   - max: 最大等待时间
//   - attemptNum: 当前尝试次数（从0开始）
//   - resp: HTTP响应（如果有）
//
// 返回:
//   - time.Duration: 下一次重试前应等待的时间
type Backoff func(min, max time.Duration, attemptNum int, resp *http.Response) time.Duration



// DefaultBackoff  提供Client.Backoff的默认回调函数
// 它将根据尝试次数执行指数退避，并受到提供的最小和最大持续时间的限制
// 指数退避算法: 等待时间 = 2^尝试次数 * 最小等待时间
// 如果计算出的等待时间超过最大值，则使用最大等待时间
// @return min min: 
// @return max time.Duration: 
// @return attemptNum int: 
// @return resp *http.Response: 
// @return func(min, max time.Duration, attemptNum int, resp *http.Response) time.Duration func(min, max time.Duration, attemptNum int, resp *http.Response) time.Duration: 
func DefaultBackoff() func(min, max time.Duration, attemptNum int, resp *http.Response) time.Duration {
	// 计算指数退避时间：2^attemptNum * min
	return func(min, max time.Duration, attemptNum int, resp *http.Response) time.Duration {
		mult := math.Pow(2, float64(attemptNum)) * float64(min)

		sleep := time.Duration(mult)
		// 检查是否发生了溢出或超过最大值
		if float64(sleep) != mult || sleep > max {
			sleep = max
		}
		return sleep
	}
}

// LinearJitterBackoff 提供Client.Backoff的一个回调，
// 它将基于尝试次数执行线性退避，并添加抖动以防止"惊群效应"
//
// min和max在这里不是绝对值。要乘以尝试次数的值将从min和max之间随机选择，
// 因此它们限定了抖动范围。
//
// 例如:
//   - 要获得每次重试递增1秒的严格线性退避，将两者都设为1秒（1s, 2s, 3s, 4s, ...）
//   - 要获得以1秒为中心、每次重试递增的小抖动，设置为接近1秒的值，如最小800ms最大1200ms
//     (892ms, 2102ms, 2945ms, 4312ms, ...)
//   - 要获得极端抖动，设置为非常宽的范围，如最小100ms最大20s
//     (15382ms, 292ms, 51321ms, 35234ms, ...)
//
// 返回:
//   - func: 计算带有线性抖动的退避时间的函数
// @return min min: 
// @return max time.Duration: 
// @return attemptNum int: 
// @return resp *http.Response: 
// @return func(min, max time.Duration, attemptNum int, resp *http.Response) time.Duration func(min, max time.Duration, attemptNum int, resp *http.Response) time.Duration: 
func LinearJitterBackoff() func(min, max time.Duration, attemptNum int, resp *http.Response) time.Duration {
	// 创建一个全局随机数生成器并用它生成退避的随机数
	// 使用互斥锁保护随机源的并发访问
	rand := rand.New(rand.NewSource(int64(time.Now().Nanosecond())))
	randMutex := &sync.Mutex{}

	return func(min, max time.Duration, attemptNum int, resp *http.Response) time.Duration {
		// attemptNum总是从0开始，但我们希望从1开始进行乘法
		attemptNum++

		if max <= min {
			// 如果max小于等于min，不清楚该怎么处理，或者它们相同，
			// 所以返回min * attemptNum
			return min * time.Duration(attemptNum)
		}

		// 选择一个介于min和max之间的随机数，并乘以attemptNum
		// attemptNum从0开始，所以我们总是在这里递增
		// 首先获取一个随机百分比，然后将其应用于min和max之间的差值，再加到min上
		randMutex.Lock()
		jitter := rand.Float64() * float64(max-min)
		randMutex.Unlock()

		jitterMin := int64(jitter) + int64(min)
		return time.Duration(jitterMin * int64(attemptNum))
	}
}

// FullJitterBackoff  实现了带抖动的封顶指数退避
// 算法很快，因为它不使用浮点数运算
// 它返回一个介于[0...n]之间的随机数
// https://aws.amazon.com/blogs/architecture/exponential-backoff-and-jitter/
// @return min min: 
// @return max time.Duration: 
// @return attemptNum int: 
// @return resp *http.Response: 
// @return func(min, max time.Duration, attemptNum int, resp *http.Response) time.Duration func(min, max time.Duration, attemptNum int, resp *http.Response) time.Duration: 
func FullJitterBackoff() func(min, max time.Duration, attemptNum int, resp *http.Response) time.Duration {
	// 创建一个全局随机数生成器并用它生成退避的随机数
	// 使用互斥锁保护随机源的并发访问
	rand := rand.New(rand.NewSource(int64(time.Now().Nanosecond())))
	randMutex := &sync.Mutex{}

	return func(min, max time.Duration, attemptNum int, resp *http.Response) time.Duration {
		// 计算基本持续时间：attemptNum * 1秒 * 2
		// 1000000000表示1秒（纳秒），左移1位等同于乘以2
		duration := attemptNum * 1000000000 << 1

		randMutex.Lock()
		// 从[0, duration-attemptNum)范围内选择一个随机数，再加上最小值
		jitter := rand.Intn(duration-attemptNum) + int(min)
		randMutex.Unlock()

		// 如果计算的抖动值超过最大值，则使用最大值
		if jitter > int(max) {
			return max
		}

		return time.Duration(jitter)
	}
}

// ExponentialJitterBackoff 提供Client.Backoff的一个回调，
// 它将基于尝试次数执行指数退避，并添加抖动以防止"惊群效应"
//
// min和max在这里不是绝对值。要乘以尝试次数的值将从min和max之间随机选择，
// 因此它们限定了抖动范围。
// @return min min: 
// @return max time.Duration: 
// @return attemptNum int: 
// @return resp *http.Response: 
// @return func(min, max time.Duration, attemptNum int, resp *http.Response) time.Duration func(min, max time.Duration, attemptNum int, resp *http.Response) time.Duration: 
func ExponentialJitterBackoff() func(min, max time.Duration, attemptNum int, resp *http.Response) time.Duration {
	// 创建一个全局随机数生成器并用它生成退避的随机数
	// 使用互斥锁保护随机源的并发访问
	rand := rand.New(rand.NewSource(int64(time.Now().Nanosecond())))
	randMutex := &sync.Mutex{}

	return func(min, max time.Duration, attemptNum int, resp *http.Response) time.Duration {
		minf := float64(min)
		// 计算指数值：2^attemptNum * min
		mult := math.Pow(2, float64(attemptNum)) * minf

		// 生成一个在[0, mult-minf]范围内的随机抖动值
		randMutex.Lock()
		jitter := rand.Float64() * (mult - minf)
		randMutex.Unlock()

		// 将抖动值添加到指数值
		mult = mult + jitter

		// 转换为time.Duration类型
		sleep := time.Duration(mult)
		// 如果超过最大值，则使用最大值
		if sleep > max {
			sleep = max
		}

		return sleep
	}
}