// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-07 11:52:15
// FilePath: /yaml_scan/pkg/cidr/util_test.go
// Description: 
package cidr

import (
	"net"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestInc 测试IP地址递增函数
func TestInc(t *testing.T) {
	// 测试用例
	tests := []struct {
		name     string // 测试名称
		ip       string // 输入IP地址
		expected string // 期望的递增后IP地址
	}{
		{
			name:     "IPv4普通地址递增",
			ip:       "***********",
			expected: "***********",
		},
		{
			name:     "IPv4地址字节进位",
			ip:       "***********55",
			expected: "***********",
		},
		{
			name:     "IPv4地址多字节进位",
			ip:       "***************",
			expected: "***********",
		},
		{
			name:     "IPv4最大地址递增",
			ip:       "***************",
			expected: "::1:0:0:0", // 循环回到0
		},
		{
			name:     "IPv6普通地址递增",
			ip:       "2001:db8::1",
			expected: "2001:db8::2",
		},
		{
			name:     "IPv6地址字节进位",
			ip:       "2001:db8::ff",
			expected: "2001:db8::100",
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 解析IP地址
			ip := net.ParseIP(tt.ip)
			require.NotNil(t, ip, "解析IP地址出错")

			// 调用被测试函数
			result := inc(ip)

			// 验证结果
			expected := net.ParseIP(tt.expected)
			require.Equal(t, expected.String(), result.String(), "IP地址递增错误")
		})
	}
}

// TestEscape 测试字符串URL编码函数
func TestEscape(t *testing.T) {
	// 测试用例
	tests := []struct {
		name     string // 测试名称
		input    string // 输入字符串
		expected string // 期望的编码结果
	}{
		{
			name:     "普通IP地址编码",
			input:    "127.0.0.1",
			expected: "%31%32%37%2E%30%2E%30%2E%31",
		},
		{
			name:     "IPv6地址编码",
			input:    "::1",
			expected: "%3A%3A%31",
		},
		{
			name:     "包含特殊字符的字符串编码",
			input:    "<EMAIL>",
			expected: "%74%65%73%74%40%65%78%61%6D%70%6C%65%2E%63%6F%6D",
		},
		{
			name:     "空字符串编码",
			input:    "",
			expected: "",
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 调用被测试函数
			result := escape(tt.input)

			// 验证结果
			require.Equal(t, tt.expected, result, "字符串URL编码错误")
		})
	}
}

// TestRandomHex 测试随机十六进制字符串生成函数
func TestRandomHex(t *testing.T) {
	// 测试用例
	tests := []struct {
		name      string // 测试名称
		byteCount int    // 要生成的随机字节数
		suffix    []byte // 附加的后缀
		suffixHex string // 后缀的十六进制表示
	}{
		{
			name:      "生成5个随机字节",
			byteCount: 5,
			suffix:    []byte{0xAA, 0xBB, 0xCC},
			suffixHex: "aabbcc",
		},
		{
			name:      "生成10个随机字节",
			byteCount: 10,
			suffix:    []byte{0x12, 0x34, 0x56},
			suffixHex: "123456",
		},
		{
			name:      "不附加后缀",
			byteCount: 8,
			suffix:    []byte{},
			suffixHex: "",
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 调用被测试函数
			result, err := RandomHex(tt.byteCount, tt.suffix)

			// 验证没有错误
			require.NoError(t, err, "生成随机十六进制字符串出错")

			// 验证结果长度
			expectedLen := tt.byteCount*2 + len(tt.suffixHex)
			require.Equal(t, expectedLen, len(result), "生成的十六进制字符串长度错误")

			// 验证后缀
			if len(tt.suffix) > 0 {
				require.True(t, len(result) >= len(tt.suffixHex), "结果长度不足以包含后缀")
				require.Equal(t, tt.suffixHex, result[len(result)-len(tt.suffixHex):], "结果不包含预期的后缀")
			}
		})
	}
}

// TestTotalIPSInCidrs 测试计算CIDR列表中IP地址总数的函数
func TestTotalIPSInCidrs(t *testing.T) {
	// 测试用例
	tests := []struct {
		name     string   // 测试名称
		cidrs    []string // CIDR列表
		expected uint64   // 期望的IP地址总数
	}{
		{
			name:     "单个IPv4 CIDR",
			cidrs:    []string{"***********/24"},
			expected: 256, // 2^(32-24) = 256
		},
		{
			name:     "多个IPv4 CIDR",
			cidrs:    []string{"***********/24", "10.0.0.0/16"},
			expected: 256 + 65536, // 2^(32-24) + 2^(32-16) = 256 + 65536 = 65792
		},
		{
			name:     "IPv4 /32单个地址",
			cidrs:    []string{"***********/32"},
			expected: 1, // 2^(32-32) = 1
		},
		{
			name:     "IPv4 /31网络(两个地址)",
			cidrs:    []string{"***********/31"},
			expected: 2, // 2^(32-31) = 2
		},
		{
			name:     "空CIDR列表",
			cidrs:    []string{},
			expected: 0,
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 转换CIDR字符串为IPNet对象
			var ipnets []*net.IPNet
			for _, cidr := range tt.cidrs {
				_, ipnet, err := net.ParseCIDR(cidr)
				require.NoError(t, err, "解析CIDR出错")
				ipnets = append(ipnets, ipnet)
			}

			// 调用被测试函数
			result := TotalIPSInCidrs(ipnets)

			// 验证结果
			require.Equal(t, tt.expected, result, "CIDR中IP地址总数计算错误")
		})
	}
}


