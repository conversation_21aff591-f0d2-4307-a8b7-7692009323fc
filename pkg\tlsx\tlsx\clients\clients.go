//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-23 19:46:26
// FilePath: /yaml_scan/pkg/tlsx/tlsx/clients/clients.go
// Description:

package clients

import (
	"bytes"
	"crypto/md5"
	"crypto/sha1"
	"crypto/sha256"
	"crypto/x509"
	"encoding/hex"
	"encoding/pem"
	"math"
	"strings"
	"time"
	"yaml_scan/pkg/retryablehttp"
	"yaml_scan/pkg/tlsx/assets"
	stringsutil "yaml_scan/utils/strings"

	"github.com/cloudflare/cfssl/log"
	"github.com/cloudflare/cfssl/revoke"
	"github.com/logrusorgru/aurora"
	zasn1 "github.com/zmap/zcrypto/encoding/asn1"
	ztls "github.com/zmap/zcrypto/tls"
	zpkix "github.com/zmap/zcrypto/x509/pkix"
)

// Implementation 是由TLSX客户端实现的接口
type Implementation interface {
	// ConnectWithOptions 连接到主机并获取响应数据
	// hostname: 主机名
	// ip: IP地址
	// port: 端口号
	// options: 连接选项
	// 返回连接响应和可能的错误
	ConnectWithOptions(hostname, ip, port string, options ConnectOptions) (*Response, error)

	// EnumerateCiphers 枚举目标主机支持的密码套件
	// hostname: 主机名
	// ip: IP地址
	// port: 端口号
	// options: 连接选项
	// 返回支持的密码套件列表和可能的错误
	EnumerateCiphers(hostname, ip, port string, options ConnectOptions) ([]string, error)

	// SupportedTLSVersions 返回客户端支持的TLS版本列表
	// 返回支持的TLS版本列表和可能的错误
	SupportedTLSVersions() ([]string, error)

	// SupportedTLSCiphers 返回客户端支持的TLS密码套件列表
	// 返回支持的TLS密码套件列表和可能的错误
	SupportedTLSCiphers() ([]string, error)
}

// ColorCode returns a clone of CipherTypes with Colored Strings
func (c *CipherTypes) ColorCode(a aurora.Aurora) CipherTypes {
	ct := CipherTypes{}
	for _, v := range c.Weak {
		ct.Weak = append(ct.Weak, a.BrightYellow(v).String())
	}
	for _, v := range c.Insecure {
		ct.Insecure = append(ct.Insecure, a.BrightRed(v).String())
	}
	for _, v := range c.Secure {
		ct.Secure = append(ct.Secure, a.BrightGreen(v).String())
	}
	for _, v := range c.Unknown {
		ct.Unknown = append(ct.Unknown, a.BrightMagenta(v).String())
	}
	return ct
}

// IdentifyCiphers 从给定的密码套件列表中识别密码套件的安全类型
// 根据密码套件的安全级别将其分类到不同的类别中
// @param cipherList []string: 要分类的密码套件名称列表
// @return CipherTypes CipherTypes: 按安全级别分类的密码套件结构
func IdentifyCiphers(cipherList []string) CipherTypes {
	ct := CipherTypes{}
	for _, v := range cipherList {
		switch GetCipherLevel(v) {
		case Insecure:
			ct.Insecure = append(ct.Insecure, v)
		case Secure:
			ct.Secure = append(ct.Secure, v)
		case Weak:
			ct.Weak = append(ct.Weak, v)
		default:
			ct.Unknown = append(ct.Unknown, v)
		}
	}
	return ct
}


// CertificateDistinguishedName 表示X.509证书的可分辨名称结构
type CertificateDistinguishedName struct {
	Country            []string `json:"country,omitempty"`             // 国家代码列表（如"CN", "US"）
	Organization       []string `json:"organization,omitempty"`        // 组织名称列表（如公司名）
	OrganizationalUnit []string `json:"organizational_unit,omitempty"` // 组织单位列表（如部门名）
	Locality           []string `json:"locality,omitempty"`            // 地区名称列表（如城市名）
	Province           []string `json:"province,omitempty"`            // 省份或州名列表
	StreetAddress      []string `json:"street_address,omitempty"`      // 街道地址列表
	CommonName         string   `json:"common_name,omitempty"`         // 通用名称（通常是域名或个人姓名）
}

// MD5Fingerprint 使用MD5哈希算法创建数据的指纹
// @param data []byte: 要计算哈希的字节数据
// @return string string: 十六进制编码的MD5哈希值
func MD5Fingerprint(data []byte) string {
	sum := md5.Sum(data)
	return hex.EncodeToString(sum[:])
}

// SHA1Fingerprint 用SHA1哈希算法创建数据的指纹
// @param data []byte: 
// @return string string: 
func SHA1Fingerprint(data []byte) string {
	sum := sha1.Sum(data)
	return hex.EncodeToString(sum[:])
}

// SHA256Fingerprint 使用SHA256哈希算法创建数据的指纹
// @param data []byte: 
// @return string string: 
func SHA256Fingerprint(data []byte) string {
	sum := sha256.Sum256(data)
	return hex.EncodeToString(sum[:])
}

// IsExpired 返回证书是否已过期
// @param notAfter time.Time: 证书的过期时间
// @return bool bool: 如果证书已过期，则返回true；否则返回false

//	通过比较当前时间和证书的过期时间，判断证书是否已过期。
//	如果当前时间晚于证书的过期时间，则证书已过期
func IsExpired(notAfter time.Time) bool {
	remaining := math.Round(time.Since(notAfter).Seconds())
	return remaining > 0
}

// IsSelfSigned 返回证书是否为自签名
// @param authorityKeyID []byte: 颁发者密钥ID
// @param subjectKeyID []byte: 主题密钥ID
// @return bool bool:  如果证书是自签名的，则返回true；否则返回false
//	检查证书是否自签名，方法是比较颁发者密钥ID和主题密钥ID。
//	遵循: https://security.stackexchange.com/a/162263/250973
//	如果颁发者密钥ID为空，或者颁发者密钥ID等于主题密钥ID，则证书是自签名的。
func IsSelfSigned(authorityKeyID, subjectKeyID []byte) bool {
	if len(authorityKeyID) == 0 || bytes.Equal(authorityKeyID, subjectKeyID) {
		return true
	}
	return false
}

// IsMisMatchedCert 如果证书名称（主题通用名称+备用名称）不包含主机，则返回true
// @param host string: 目标主机名
// @param alternativeNames []string: 证书中的备用名称列表，包括主题通用名称
// @return bool bool: 如果证书名称与主机不匹配，则返回true；否则返回false
//	检查证书的名称是否与目标主机名匹配。
//	对于普通证书，直接比较名称和主机是否相等。
//	对于通配符证书，检查通配符模式是否匹配主机名。
func IsMisMatchedCert(host string, alternativeNames []string) bool {
	hostTokens := strings.Split(host, ".")
	for _, alternativeName := range alternativeNames {
	// 如果不是通配符，当名称与主机匹配时返回false
		if !strings.Contains(alternativeName, "*") {
			if strings.EqualFold(alternativeName, host) {
				return false
			}
		} else {
			// 尝试将通配符名称与主机匹配
			nameTokens := strings.Split(alternativeName, ".")
			if len(hostTokens) == len(nameTokens) {
				matched := false
				for i, token := range nameTokens {
					if i == 0 {
						// 匹配最左边的标记
						matched = matchWildCardToken(token, hostTokens[i])
						if !matched {
							break
						}
					} else {
						// 匹配所有其他标记
						matched = stringsutil.EqualFoldAny(token, hostTokens[i])
						if !matched {
							break
						}
					}
				}
				// 如果所有名称标记都与主机标记匹配，则返回false
				if matched {
					return false
				}
			}
		}
	}
	return true
}

// IsUntrustedCA 如果证书是自签名CA，则返回true
// @param certs []*x509.Certificate:  X.509证书列表
// @return bool bool: 如果存在自签名CA且不在根证书列表中，则返回true；否则返回false
//	检查证书列表中是否存在自签名CA证书，且该证书不在可信根证书列表中。
//	这种情况通常表示证书链不可信。
func IsUntrustedCA(certs []*x509.Certificate) bool {
	for _, c := range certs {
		if c != nil && c.IsCA && IsSelfSigned(c.AuthorityKeyId, c.SubjectKeyId) && !assets.IsRootCert(c) {
			return true
		}
	}
	return false
}

// IsZTLSUntrustedCA 如果证书是自签名CA，则返回true
// @param certs []ztls.SimpleCertificate: ZMap的简化证书列表
// @return bool bool: 如果存在自签名CA且不在根证书列表中，则返回true；否则返回false
//	将ZMap的简化证书转换为标准X.509证书，然后检查是否存在自签名CA证书，
//	且该证书不在可信根证书列表中。这种情况通常表示证书链不可信。
func IsZTLSUntrustedCA(certs []ztls.SimpleCertificate) bool {
	for _, cert := range certs {
		parsedCert, _ := x509.ParseCertificate(cert.Raw)
		if parsedCert != nil && parsedCert.IsCA && IsSelfSigned(parsedCert.AuthorityKeyId, parsedCert.SubjectKeyId) && !assets.IsRootCert(parsedCert) {
			return true
		}
	}
	return false
}

// matchWildCardToken 匹配通配符名称标记和主机标记
// @param name string: 可能包含通配符的名称标记
// @param host string: 主机名称
// @return bool bool: 如果通配符名称标记匹配主机标记，则返回true；否则返回false
func matchWildCardToken(name, host string) bool {
	if strings.Contains(name, "*") {
		nameSubTokens := strings.Split(name, "*")
		if strings.HasPrefix(name, "*") {
			// 前缀通配符（*.example.com）
			return strings.HasSuffix(host, nameSubTokens[1])
		} else if strings.HasSuffix(name, "*") {
			// 后缀通配符（prefix.*）
			return strings.HasPrefix(host, nameSubTokens[0])
		} else {
			// 中间通配符（pre*.suffix）
			return strings.HasPrefix(host, nameSubTokens[0]) &&
				strings.HasSuffix(host, nameSubTokens[1])
		}
	}
	return strings.EqualFold(name, host)
}

// IsWildCardCert 如果证书是通配符证书，则返回true
// @param names []string:  证书中的名称列表
// @return bool bool: 
//	检查证书的名称列表中是否包含通配符名称（形如"*.example.com"）。
//	通配符证书可以匹配多个子域。
func IsWildCardCert(names []string) bool {
	for _, name := range names {
		if strings.Contains(name, "*.") {
			return true
		}
	}
	return false
}

// PemEncode 将原始证书编码为PEM格式
// @param cert []byte: 原始证书字节
// @return string string: PEM编码的证书字符串
func PemEncode(cert []byte) string {
	var buf bytes.Buffer
	if err := pem.Encode(&buf, &pem.Block{Type: "CERTIFICATE", Bytes: cert}); err != nil {
		return ""
	}
	return buf.String()
}

// ParseASN1DNSequenceWithZpkixOrDefault 返回ASN1DNSequence的解析值或默认字符串值
// @param data []byte: 要解析的ASN.1编码数据
// @param defaultValue string: 解析失败时返回的默认值
// @return string string:  解析的可分辨名称字符串，或默认值
func ParseASN1DNSequenceWithZpkixOrDefault(data []byte, defaultValue string) string {
	if value := ParseASN1DNSequenceWithZpkix(data); value != "" {
		return value
	}
	return defaultValue
}

// ParseASN1DNSequenceWithZpkix 尝试使用zpkix和zasn1库解析TLS DN的原始ASN.1编码
// @param data []byte: 要解析的ASN.1编码数据	
// @return string string:  解析的可分辨名称字符串，如果解析失败则返回空字符串
//	使用ZMap的ASN.1和PKIX库解析TLS可分辨名称的原始ASN.1编码。
//	这些库包含标准库未解析的额外信息，可能很有用。
//	如果解析失败，返回空字符串，将使用标准库数据。
func ParseASN1DNSequenceWithZpkix(data []byte) string {
	var rdnSequence zpkix.RDNSequence
	var name zpkix.Name
	if _, err := zasn1.Unmarshal(data, &rdnSequence); err != nil {
		return ""
	}
	name.FillFromRDNSequence(&rdnSequence)
	dnParsedString := name.String()
	return dnParsedString
}

func init() {
	// 为cfssl指定默认值
	log.Level = log.LevelError
	revoke.HTTPClient = retryablehttp.DefaultClient()
	revoke.HTTPClient.Timeout = time.Duration(5) * time.Second
}
