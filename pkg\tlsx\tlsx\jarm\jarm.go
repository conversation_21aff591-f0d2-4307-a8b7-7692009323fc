// Package jarm 提供JARM TLS指纹识别功能
// JARM是一种主动的TLS服务器指纹识别方法，通过发送多个特制的TLS握手包
// 并分析服务器响应来生成唯一的指纹哈希，用于识别和分类TLS服务器
package jarm

import (
	"context"
	"fmt"
	"net"
	"strings"
	"time"
	"yaml_scan/pkg/fastdialer"
	"yaml_scan/pkg/gologger"
	"yaml_scan/utils/conn"

	gojarm "github.com/hdm/jarm-go"
)

// poolCount 定义连接池的大小
const poolCount = 3

// HashWithDialer 用指定的拨号器对单个主机/端口执行JARM指纹识别
// 通过发送多个TLS握手探测包并分析服务器响应来生成JARM哈希
// @param dialer *fastdialer.Dialer: 快速拨号器实例，用于建立网络连接
// @param host string: 目标主机名或IP地址
// @param port int: 目标端口号
// @param duration int: 每个探测的超时时间（秒）
// @return string string: 生成的JARM指纹哈希字符串
// @return error error: 指纹识别过程中的错误，成功时为nil
func HashWithDialer(dialer *fastdialer.Dialer, host string, port int, duration int) (string, error) {
	results := []string{}
	// 构建目标地址
	addr := net.JoinHostPort(host, fmt.Sprintf("%d", port))

	// 转换超时时间为Duration类型
	timeout := time.Duration(duration) * time.Second

	// 创建连接池，因为JARM需要发送多个探测包
	// 使用连接池可以复用连接，提高效率
	pool, err := connpool.NewOneTimePool(context.Background(), addr, poolCount)
	if err != nil {
		return "", err
	}
	pool.Dialer = dialer

	defer pool.Close() //nolint

	// 启动连接池的后台运行
	go func() {
		if err := pool.Run(); err != nil {
			gologger.Error().Msgf("tlsx: jarm: failed to run connection pool: %v", err)
		}
	}() //nolint

	// 遍历所有JARM探测包
	for _, probe := range gojarm.GetProbes(host, port) {
		// 从连接池获取一个连接
		conn, err := pool.Acquire(context.TODO())
		if err != nil {
			continue
		}
		if conn == nil {
			continue
		}	
		// 设置写入超时，防止连接挂起
		_ = conn.SetWriteDeadline(time.Now().Add(timeout))

		// 发送TLS ClientHello探测包
		_, err = conn.Write(gojarm.BuildProbe(probe))
		if err != nil {
			results = append(results, "")
			_ = conn.Close()
			continue
		}
		// 设置读取超时，防止等待响应时挂起
		_ = conn.SetReadDeadline(time.Now().Add(timeout))

		// 读取服务器响应（最大1484字节，足够包含TLS ServerHello）
		buff := make([]byte, 1484)
		_, _ = conn.Read(buff)
		_ = conn.Close()

		// 解析服务器的ServerHello响应
		ans, err := gojarm.ParseServerHello(buff, probe)
		if err != nil {
			results = append(results, "")
			continue
		}
		results = append(results, ans)
	}

	// 将所有探测结果组合并生成最终的JARM哈希
	hash := gojarm.RawHashToFuzzyHash(strings.Join(results, ","))
	return hash, nil
}
