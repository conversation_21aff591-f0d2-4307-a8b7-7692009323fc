// Author: chenjb
// Version: V1.0
// Date: 2025-05-28 15:00:57
// FilePath: /yaml_scan/pkg/dsl/dsl.go
// Description: 提供自定义函数和表达式求值功能
package dsl

import (
	"errors"
	"fmt"
	"regexp"
	"strings"

	maputils "yaml_scan/utils/maps"

	"github.com/Knetic/govaluate"
	"github.com/logrusorgru/aurora"
)

var (
	// functions 存储所有注册的DSL函数
	functions []dslFunction
	// DefaultHelperFunctions 是预编译的govaluate DSL函数列表
	DefaultHelperFunctions map[string]govaluate.ExpressionFunction
	// FunctionNames 是用于表达式评估的函数名称列表
	FunctionNames []string
	// funcSignatureRegex 是用于解析函数签名的正则表达式
	funcSignatureRegex = regexp.MustCompile(`(\w+)\s*\((?:([\w\d,\s]+)\s+([.\w\d{}&*]+))?\)([\s.\w\d{}&*]+)?`)
)

// AddFunction 添加一个新的DSL函数到函数列表中
// @param function dslFunction: 要添加的DSL函数
// @return error error: 如果函数名称已存在则返回错误
func AddFunction(function dslFunction) error {
	// 检查是否有同名函数已经存在
	for _, f := range functions {
		if function.Name == f.Name {
			return errors.New("duplicate helper function key defined")
		}
	}
	functions = append(functions, function)
	return nil
}

// MustAddFunction 添加一个新的DSL函数，如果添加失败则触发panic
// @param function dslFunction:  要添加的DSL函数
func MustAddFunction(function dslFunction) {
	if err := AddFunction(function); err != nil {
		panic(err)
	}
}

// AddMultiSignatureHelperFunction 允许创建支持多个签名的辅助函数
// @param key string: 函数名
// @param signatureparts []string: 函数签名部分的列表
// @param cacheable bool: 是否可缓存函数结果
// @param value func(args ...interface{}) (interface{}, error):实际执行的函数
// @return error error: 添加过程中的错误
func AddMultiSignatureHelperFunction(key string, signatureparts []string, cacheable bool, value func(args ...interface{}) (interface{}, error)) error {
	function := NewWithMultipleSignatures(key, signatureparts, cacheable, value)
	return AddFunction(function)
}

// getDslFunctionSignatures 获取所有DSL函数的签名
// @return []string []string: 所有函数签名的列表
func getDslFunctionSignatures() []string {
	var result []string
	for _, function := range functions {
		result = append(result, function.GetSignatures()...)
	}
	return result
}

// colorizeDslFunctionSignatures 为DSL函数签名添加彩色标记
// @return []string []string:
func colorizeDslFunctionSignatures() []string {
	signatures := getDslFunctionSignatures()

	// 定义将文本转换为橙色的函数
	colorToOrange := func(value string) string {
		return aurora.Index(208, value).String()
	}

	result := make([]string, 0, len(signatures))

	// 处理每个签名
	for _, signature := range signatures {
		subMatchSlices := funcSignatureRegex.FindAllStringSubmatch(signature, -1)
		if len(subMatchSlices) != 1 {
			result = append(result, signature)
			continue
		}
		matches := subMatchSlices[0]
		if len(matches) != 5 {
			result = append(result, signature)
			continue
		}

		// 解析函数参数
		functionParameters := strings.Split(matches[2], ",")

		var coloredParameterAndTypes []string
		// 为每个参数添加颜色
		for _, functionParameter := range functionParameters {
			functionParameter = strings.TrimSpace(functionParameter)
			paramAndType := strings.Split(functionParameter, " ")
			if len(paramAndType) == 1 {
				coloredParameterAndTypes = append(coloredParameterAndTypes, paramAndType[0])
			} else if len(paramAndType) == 2 {
				coloredParameterAndTypes = append(coloredParameterAndTypes, fmt.Sprintf("%s %s", paramAndType[0], colorToOrange(paramAndType[1])))
			}
		}

		// 组合带颜色的完整函数签名
		highlightedParams := strings.TrimSpace(fmt.Sprintf("%s %s", strings.Join(coloredParameterAndTypes, ", "), colorToOrange(matches[3])))
		colorizedDslSignature := fmt.Sprintf("%s(%s)%s", aurora.BrightYellow(matches[1]).String(), highlightedParams, colorToOrange(matches[4]))

		result = append(result, colorizedDslSignature)
	}

	return result
}

// GetPrintableDslFunctionSignatures 获取可打印的DSL函数签名
// @param noColor bool: 是否禁用颜色标记
// @return string string: 所有函数签名组合成的字符串
func GetPrintableDslFunctionSignatures(noColor bool) string {
	if noColor {
		return aggregate(getDslFunctionSignatures())
	}
	return aggregate(colorizeDslFunctionSignatures())
}

// NewWithPositionalArgs 创建一个基于位置参数的DSL函数
// @param name string: 函数名称
// @param numberOfArgs int: 参数数量
// @param cacheable bool: 函数结果是否可缓存
// @param expr govaluate.ExpressionFunction: 表达式函数实现
// @return dslFunction dslFunction: 创建的DSL函数
func NewWithPositionalArgs(name string, numberOfArgs int, cacheable bool, expr govaluate.ExpressionFunction) dslFunction {
	function := dslFunction{
		Name:               name,
		NumberOfArgs:       numberOfArgs,
		ExpressionFunction: expr,
		IsCacheable:        cacheable,
	}
	return function
}

// NewWithMultipleSignatures 创建一个支持多个签名的DSL函数
// @param name string: 函数名称
// @param signatures []string: 函数签名列表
// @param cacheable bool: 函数结果是否可缓存
// @param expr govaluate.ExpressionFunction: 表达式函数实现
// @return dslFunction dslFunction: 创建的DSL函数
func NewWithMultipleSignatures(name string, signatures []string, cacheable bool, expr govaluate.ExpressionFunction) dslFunction {
	function := dslFunction{
		Name:               name,
		Signatures:         signatures,
		ExpressionFunction: expr,
		IsCacheable:        cacheable,
	}

	return function
}

// NewWithSingleSignature 创建一个只有单个签名的DSL函数
// @param name string: 函数名称
// @param signature string: 函数签名
// @param cacheable bool: 函数结果是否可缓存
// @param logic govaluate.ExpressionFunction: 表达式函数实现
// @return dslFunction dslFunction: 创建的DSL函数
func NewWithSingleSignature(name, signature string, cacheable bool, logic govaluate.ExpressionFunction) dslFunction {
	return NewWithMultipleSignatures(name, []string{signature}, cacheable, logic)
}

// HelperFunctions  返回所有DSL辅助函数
// @return map map: 函数名到函数实现的映射
func HelperFunctions() map[string]govaluate.ExpressionFunction {
	helperFunctions := make(map[string]govaluate.ExpressionFunction)

	for _, function := range functions {
		helperFunctions[function.Name] = function.Exec
		// 为了向后兼容，添加去掉下划线的版本
		helperFunctions[strings.ReplaceAll(function.Name, "_", "")] = function.Exec
	}

	return helperFunctions
}

// GetFunctionNames 获取辅助函数的名称列表
// @param heperFunctions map[string]govaluate.ExpressionFunction:  辅助函数映射
// @return []string []string: 
func GetFunctionNames(heperFunctions map[string]govaluate.ExpressionFunction) []string {
	return maputils.GetKeys(heperFunctions)
}

func init() {
	Index()
	Len()
	ToLower()
	ToUpper()
	Sort()
	Zip()
	Uniq()
	Repeat()
	Replace()
	ReplaceRegex()
	Trim()
	TrimLeft()
	TrimRight()
	TrimSpace()
	TrimPrefix()
	TrimSuffix()
	Reverse()
	Base64()
	Gzip()
	GzipDecode()
	Zlib()
	ZlibDecode()
	Deflate()
	Inflate()
	Base64Py()
	DateTime()
	Base64Decode()
	UrlDecode()
	UrlEncode()
	HexDecode()
	HexEncode()
	Hmac()
	HtmlEscape()
	HtmlUnEscape()
	MD5()
	Sha1()
	Sha256()
	Sha512()
	Mmh3()
	Contains()
	ContainsAll()
	ContainsAny()
	StartsWith()
	LineStartsWith()
	EndsWith()
	LineEndsWith()
	Concat()
	Split()
	Join()
	Reg()
	RegexAll()
	RegexAny()
	EqualsAny()
	RemoveBadChars()
	RandChar()
	RandBase()
	RandTextAlp()
	RandTextAlpha()
	RandTextNumeric()
	RandInt()
	RandIp()
	GenJavaGadget()
	UnixTime()
	ToUnixTime()
	WaitFor()
	CompareVersion()
	Padding()
	PrintDebug()
	ToNumber()
	ToString()
	DecToHex()
	HexToDec()
	OctToDec()
	BinToDec()
	SubStr()
	AesCBC()
	AesGCM()
	GenerateJwt()
	JsonMinify()
	JsonPrettify()
	IpFormat()
	Unpack()
	XOR()
	PublicIp()
	Jarm()
	Count()

	DefaultHelperFunctions = HelperFunctions()
	FunctionNames = GetFunctionNames(DefaultHelperFunctions)
}
