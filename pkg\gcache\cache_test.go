// Author: chenjb
// Version: V1.0
// Date: 2025-04-30 15:27:13
// FilePath: /yaml_scan/pkg/gcache/cache_test.go
// Description:
package gcache

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestNewCacheBuilder 测试 New 函数创建 CacheBuilder 实例
func TestNewCacheBuilder(t *testing.T) {
	size := 100                       // 定义缓存容量
	builder := New[string, int](size) // 创建 CacheBuilder 实例

	// 验证默认值
	require.NotNil(t, builder)                    // 确保 builder 不为 nil
	require.Equal(t, TYPE_SIMPLE, builder.tp)     // 默认类型为 "simple"
	require.Equal(t, size, builder.size)          // 容量与传入参数一致
	require.IsType(t, RealClock{}, builder.clock) // 时钟类型为 RealClock
	require.Nil(t, builder.loaderExpireFunc)      // 加载函数为 nil
	require.Nil(t, builder.evictedFunc)           // 逐出回调为 nil
	require.Nil(t, builder.purgeVisitorFunc)      // 清除访问者函数为 nil
	require.Nil(t, builder.addedFunc)             // 添加回调为 nil
	require.Nil(t, builder.serializeFunc)         // 序列化函数为 nil
	require.Nil(t, builder.deserializeFunc)       // 反序列化函数为 nil
	require.Nil(t, builder.expiration)            // 默认过期时间为 nil
	require.Nil(t, builder.lease)                 // 默认租约时间为 nil
}

// TestCacheBuilderMethods 测试 CacheBuilder 的方法设置功能
func TestCacheBuilderMethods(t *testing.T) {
	builder := New[string, int](100) // 创建 CacheBuilder 实例

	// 测试设置 LoaderFunc
	loader := func(k string) (int, error) {
		return 42, nil // 返回固定值 42
	}
	builder.LoaderFunc(loader)                  // 设置加载函数
	require.NotNil(t, builder.loaderExpireFunc) // 验证 loaderExpireFunc 不为 nil

	// 测试设置 Expiration
	expiration := time.Minute                         // 过期时间为 1 分钟
	builder.Expiration(expiration)                    // 设置过期时间
	require.NotNil(t, builder.expiration)             // 验证 expiration 不为 nil
	require.Equal(t, expiration, *builder.expiration) // 验证值正确

	// 测试设置 Lease
	lease := time.Hour                      // 租约时间为 1 小时
	builder.Lease(lease)                    // 设置租约时间
	require.NotNil(t, builder.lease)        // 验证 lease 不为 nil
	require.Equal(t, lease, *builder.lease) // 验证值正确
}

// TestBuildCache 测试 buildCache 函数
func TestBuildCache(t *testing.T) {
	cb := New[string, int](100)       // 创建 CacheBuilder
	base := &baseCache[string, int]{} // 创建 baseCache 实例
	buildCache(base, cb)              // 调用 buildCache

	// 验证 baseCache 字段是否正确设置
	require.Equal(t, cb.clock, base.clock) // 时钟
	require.Equal(t, cb.size, base.size)   // 容量
	// require.Equal(t, cb.loaderExpireFunc, base.loaderExpireFunc) // 加载函数
	require.Equal(t, cb.expiration, base.expiration) // 过期时间
	require.Equal(t, cb.lease, base.lease)           // 租约时间
	//require.Equal(t, cb.addedFunc, base.addedFunc)               // 添加回调
	//require.Equal(t, cb.deserializeFunc, base.deserializeFunc)   // 反序列化函数
	//require.Equal(t, cb.serializeFunc, base.serializeFunc)       // 序列化函数
	//require.Equal(t, cb.evictedFunc, base.evictedFunc)           // 逐出回调
	//require.Equal(t, cb.purgeVisitorFunc, base.purgeVisitorFunc) // 清除访问者函数
	require.NotNil(t, base.stats) // 统计信息不为 nil
}
