// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-23 17:41:43
// FilePath: /yaml_scan/pkg/dnsx/runner/options_test.go
// Description: 
package runner



import (
	"os"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestShouldLoadResume 测试是否应该加载恢复文件的逻辑
func TestShouldLoadResume(t *testing.T) {
	// 创建临时的测试恢复文件
	tmpFile, err := os.CreateTemp("", DefaultResumeFile)
	require.NoError(t, err, "应该能够创建临时恢复文件")
	tmpFilePath := tmpFile.Name()

	// 确保测试结束后删除临时文件
	defer func() {
		tmpFile.Close()
		os.Remove(tmpFilePath)
	}()

	// 测试用例
	tests := []struct {
		name        string
		options     Options
		expectLoad  bool
		setupFunc   func()
		cleanupFunc func()
	}{
		{
			name: "启用恢复但文件不存在",
			options: Options{
				Resume: true,
			},
			expectLoad: false,
		},
		{
			name: "未启用恢复但文件存在",
			options: Options{
				Resume: false,
			},
			expectLoad: false,
			setupFunc: func() {
				// 模拟恢复文件存在
				os.Rename(tmpFilePath, DefaultResumeFile)
			},
			cleanupFunc: func() {
				// 清理测试文件
				os.Remove(DefaultResumeFile)
			},
		},
		{
			name: "启用恢复且文件存在",
			options: Options{
				Resume: true,
			},
			expectLoad: false,
			setupFunc: func() {
				// 模拟恢复文件存在
				os.Rename(tmpFilePath, DefaultResumeFile)
			},
			cleanupFunc: func() {
				// 清理测试文件
				os.Remove(DefaultResumeFile)
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			// 设置测试环境
			if test.setupFunc != nil {
				test.setupFunc()
			}

			// 测试函数
			result := test.options.ShouldLoadResume()
			require.Equal(t, test.expectLoad, result, "ShouldLoadResume结果应为%v", test.expectLoad)

			// 清理测试环境
			if test.cleanupFunc != nil {
				test.cleanupFunc()
			}
		})
	}
}

// TestShouldSaveResume 测试是否应该保存恢复文件的逻辑
func TestShouldSaveResume(t *testing.T) {
	options := Options{}
	require.True(t, options.ShouldSaveResume(), "应该始终返回true")
}

// TestArgumentHasStdin 测试参数是否为标准输入标记的函数
func TestArgumentHasStdin(t *testing.T) {
	tests := []struct {
		name    string
		arg     string
		isStdin bool
	}{
		{
			name:    "标准输入标记",
			arg:     "-",
			isStdin: true,
		},
		{
			name:    "普通字符串",
			arg:     "example.com",
			isStdin: false,
		},
		{
			name:    "带有破折号的字符串",
			arg:     "example-domain.com",
			isStdin: false,
		},
		{
			name:    "空字符串",
			arg:     "",
			isStdin: false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := argumentHasStdin(test.arg)
			require.Equal(t, test.isStdin, result, "argumentHasStdin('%s')应该返回%v",
				test.arg, test.isStdin)
		})
	}
}

// TestConfigureRcodes 测试DNS响应码配置功能
func TestConfigureRcodes(t *testing.T) {
	tests := []struct {
		name        string
		rcodeInput  string
		expectError bool
		expectCodes map[int]struct{}
	}{
		{
			name:        "单个有效名称",
			rcodeInput:  "nxdomain",
			expectError: false,
			expectCodes: map[int]struct{}{3: {}},
		},
		{
			name:        "多个有效名称",
			rcodeInput:  "nxdomain,servfail,refused",
			expectError: false,
			expectCodes: map[int]struct{}{3: {}, 2: {}, 5: {}},
		},
		{
			name:        "有效数字",
			rcodeInput:  "3,5",
			expectError: false,
			expectCodes: map[int]struct{}{3: {}, 5: {}},
		},
		{
			name:        "名称和数字混合",
			rcodeInput:  "nxdomain,5,refused",
			expectError: false,
			expectCodes: map[int]struct{}{3: {}, 5: {}, }, // 注意：5重复
		},
		{
			name:        "无效RCode",
			rcodeInput:  "invalid",
			expectError: true,
			expectCodes: nil,
		},
		{
			name:        "空字符串",
			rcodeInput:  "",
			expectError: false,
			expectCodes: map[int]struct{}{},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			// 创建选项对象
			options := &Options{
				RCode: test.rcodeInput,
			}

			// 调用configureRcodes函数
			err := options.configureRcodes()

			// 检查错误
			if test.expectError {
				require.Error(t, err, "应该返回错误")
			} else {
				require.NoError(t, err, "不应返回错误")

				// 验证rcodes映射
				require.Equal(t, len(test.expectCodes), len(options.rcodes),
					"rcodes映射的长度应为%d", len(test.expectCodes))

				// 验证每个期望的code是否在映射中
				for code := range test.expectCodes {
					_, exists := options.rcodes[code]
					require.True(t, exists, "rcodes映射应包含code %d", code)
				}

				// 验证hasRCodes标志
				expectHasRCodes := test.rcodeInput != ""
				require.Equal(t, expectHasRCodes, options.hasRCodes,
					"hasRCodes应为%v", expectHasRCodes)
			}
		})
	}
}

// TestConfigureQueryOptions 测试DNS查询类型配置功能
func TestConfigureQueryOptions(t *testing.T) {
	tests := []struct {
		name        string
		options     Options
		expectA     bool
		expectAAAA  bool
		expectCNAME bool
		expectANY   bool
	}{
		{
			name: "启用A记录",
			options: Options{
				A: true,
			},
			expectA:     true,
			expectAAAA:  false,
			expectCNAME: false,
			expectANY:   false,
		},
		{
			name: "启用QueryAll",
			options: Options{
				QueryAll: true,
			},
			expectA:     true,
			expectAAAA:  true,
			expectCNAME: true,
			expectANY:   false, // ANY被排除
		},
		{
			name: "启用QueryAll但排除A",
			options: Options{
				QueryAll:    true,
				ExcludeType: []string{"a"},
			},
			expectA:     false,
			expectAAAA:  true,
			expectCNAME: true,
			expectANY:   false,
		},
		{
			name: "启用多种类型",
			options: Options{
				A:     true,
				AAAA:  true,
				CNAME: true,
				ANY:   true,
			},
			expectA:     true,
			expectAAAA:  true,
			expectCNAME: true,
			expectANY:   true,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			// 复制选项进行测试
			options := test.options

			// 调用configureQueryOptions函数
			options.configureQueryOptions()

			// 验证是否根据预期配置了查询类型
			require.Equal(t, test.expectA, options.A, "A记录应为%v", test.expectA)
			require.Equal(t, test.expectAAAA, options.AAAA, "AAAA记录应为%v", test.expectAAAA)
			require.Equal(t, test.expectCNAME, options.CNAME, "CNAME记录应为%v", test.expectCNAME)
			require.Equal(t, test.expectANY, options.ANY, "ANY记录应为%v", test.expectANY)

			// 验证其他配置
			if test.options.QueryAll {
				require.True(t, options.Response, "QueryAll时Response应为true")
				require.Contains(t, options.ExcludeType, "any", "QueryAll时应排除ANY查询类型")
			}
		})
	}
}
