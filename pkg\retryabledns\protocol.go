//
//Author: chenjb
//Version: V1.0
//Date: 2025-04-21 16:22:09
//FilePath: /yaml_scan/pkg/retryabledns/protocol.go
//Description: 协议相关定义

package retryabledns

// Protocol 定义协议类型
type Protocol string

const (
	UDP Protocol = "udp" // UDP 协议
	TCP Protocol = "tcp" // TCP 协议
	DOH Protocol = "doh" // DNS over HTTPS 协议
	DOT Protocol = "dot" // DNS over TLS 协议
)

// String: 返回 Protocol 的字符串表示
//
//	@receiver p Protocol:
//	@return string string:
func (p Protocol) String() string {
	return string(p)
}

// StringWithSemicolon: 返回协议的字符串表示，后面带有冒号
//
//	@receiver p Protocol:
//	@return string string:
func (p Protocol) StringWithSemicolon() string {
	return p.String() + ":"
}

// 定义doh协议类型
type DohProtocol string

const (
	JsonAPI DohProtocol = "jsonapi" // 用JSON格式的API
	GET     DohProtocol = "get"     // GET方法
	POST    DohProtocol = "post"    // POST方法
)

// String: 返回doh协议的字符串形式
//
//	@receiver p DohProtocol:
//	@return string string:
func (p DohProtocol) String() string {
	return string(p)
}

// StringWithSemicolon: 将DohProtocol类型的值转换为带冒号的字符串
//
//	@receiver p DohProtocol:
//	@return string string:
func (p DohProtocol) StringWithSemicolon() string {
	return ":" + p.String()
}
