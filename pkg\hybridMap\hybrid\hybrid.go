package hybrid

import (
	"os"
	"path/filepath"
	"runtime"
	"time"
	"yaml_scan/pkg/hybridMap/cache"
	"yaml_scan/pkg/hybridMap/disk"
	fileutil "yaml_scan/utils/file"
	stringsutil "yaml_scan/utils/strings"
)

// MapType 表示存储映射类型的枚举
type MapType int

const (
	Memory MapType = iota // 纯内存存储模式
	Disk                  // 纯磁盘存储模式
	Hybrid                // 混合存储模式（同时使用内存和磁盘）
)

// DBType 表示支持的磁盘数据库类型的枚举
type DBType int

const (
	LevelDB  DBType = iota // LevelDB数据库类型
	PogrebDB               // PogrebDB数据库类型
	BBoltDB                // BBoltDB数据库类型
	BuntDB                 // BuntDB数据库类型

)

// Options 定义HybridMap的配置选项
type Options struct {
	MemoryExpirationTime time.Duration // 内存中数据的过期时间
	DiskExpirationTime   time.Duration // 磁盘中数据的过期时间
	JanitorTime          time.Duration // 清理过期数据的间隔时间
	Type                 MapType       // 存储类型（内存、磁盘或混合）
	DBType               DBType        // 磁盘数据库类型
	MemoryGuardForceDisk bool          // 是否强制使用磁盘存储（内存保护机制触发时）
	MemoryGuard          bool          // 是否启用内存保护机制
	MaxMemorySize        int           // 最大内存使用量（字节）
	MemoryGuardTime      time.Duration // 内存监控检查的时间间隔
	Path                 string        // 磁盘存储路径，为空时使用临时目录
	Cleanup              bool          // 关闭时是否清理磁盘存储文件
	Name                 string        // 存储名称（用于某些数据库如BBoltDB的桶名）
	RemoveOlderThan      time.Duration // RemoveOlderThan 在临时文件夹中删除超过指定时间的临时hmap
}

// DefaultOptions 默认配置选项，使用内存存储
var DefaultOptions = Options{
	Type:                 Memory,
	MemoryExpirationTime: time.Duration(5) * time.Minute,
	JanitorTime:          time.Duration(1) * time.Minute,
}

// DefaultMemoryOptions 默认内存存储配置选项
var DefaultMemoryOptions = Options{
	Type: Memory,
}

// DefaultDiskOptions 默认磁盘存储配置选项
var DefaultDiskOptions = Options{
	Type:            Disk,
	DBType:          LevelDB,
	Cleanup:         true,
	RemoveOlderThan: 24 * time.Hour * 2, // 启动时删除超过2天的临时数据库
}

// DefaultHybridOptions 默认混合存储配置选项
var DefaultHybridOptions = Options{
	Type:                 Hybrid,
	DBType:               PogrebDB,
	MemoryGuardForceDisk: true,
	MemoryExpirationTime: time.Duration(5) * time.Minute,
	JanitorTime:          time.Duration(1) * time.Minute,
}

// HybridMap 实现了一个混合存储系统，可以同时在内存和磁盘上存储数据
type HybridMap struct {
	options     *Options     // 配置选项
	memorymap   cache.Cache  // 内存缓存
	diskmap     disk.DB      // 磁盘存储
	diskmapPath string       // 磁盘存储路径
	memoryguard *memoryguard // 内存监控器
}

// New 创建并返回一个新的HybridMap实例
// @param options Options: HybridMap的配置选项
// @return *HybridMap *HybridMap: 新创建的HybridMap实例
// @return error error: 创建过程中可能发生的错误
func New(options Options) (*HybridMap, error) {
	// 获取可执行文件名，用于创建唯一的临时目录名
	executableName := fileutil.ExecutableName()

	// 由于可能存在系统故障，第一个操作是删除超过指定时间的临时文件
	// 如果启用了清理且指定了最大保存时间
	if options.Cleanup && options.Path == "" && options.RemoveOlderThan > 0 {
		targetCleanupDir := os.TempDir()
		tmpFiles, err := os.ReadDir(targetCleanupDir)
		if err != nil {
			return nil, err
		}
		now := time.Now()
		for _, tmpFile := range tmpFiles {
			// 跳过非文件夹项
			if !tmpFile.IsDir() {
				continue
			}
			// 跳过不包含可执行文件名的文件夹
			if !stringsutil.ContainsAny(tmpFile.Name(), executableName) {
				continue
			}

			tmpFileInfo, err := tmpFile.Info()
			if err != nil {
				continue
			}
			modTime := tmpFileInfo.ModTime()
			// 如果文件夹修改时间超过指定的最大保存时间，则删除它
			if now.Sub(modTime) > options.RemoveOlderThan {
				targetFolderFullPath := filepath.Join(targetCleanupDir, tmpFileInfo.Name())
				os.RemoveAll(targetFolderFullPath)
			}
		}
	}

	var hm HybridMap
	// 如果是内存模式或混合模式，初始化内存缓存
	if options.Type == Memory || options.Type == Hybrid {
		// 创建一个新的内存缓存，设置项目过期时间和清理间隔
		hm.memorymap = cache.New(options.MemoryExpirationTime, options.JanitorTime)
	}

	// 如果是磁盘模式或混合模式，初始化磁盘存储
	if options.Type == Disk || options.Type == Hybrid {
		diskmapPathm := options.Path
		if diskmapPathm == "" {
			var err error
			// 如果未指定路径，创建一个临时目录用于存储数据
			diskmapPathm, err = os.MkdirTemp("", executableName)
			if err != nil {
				return nil, err
			}
		}

		hm.diskmapPath = diskmapPathm
		// 根据指定的数据库类型初始化相应的磁盘存储
		switch options.DBType {
		case PogrebDB:
			if disk.OpenPogrebDB == nil {
				return nil, disk.ErrNotSupported
			}
			db, err := disk.OpenPogrebDB(diskmapPathm)
			if err != nil {
				return nil, err
			}
			hm.diskmap = db
		case BBoltDB:
			db, err := disk.OpenBoltDBB(filepath.Join(diskmapPathm, "bb"))
			if err != nil {
				return nil, err
			}
			db.BucketName = options.Name
			hm.diskmap = db
		case BuntDB:
			db, err := disk.OpenBuntDB(filepath.Join(diskmapPathm, "bunt"))
			if err != nil {
				return nil, err
			}
			hm.diskmap = db
		case LevelDB:
			fallthrough
		default:
			// 默认使用LevelDB
			db, err := disk.OpenLevelDB(diskmapPathm)
			if err != nil {
				return nil, err
			}
			hm.diskmap = db
		}
	}

	// 在混合模式下，设置内存缓存的淘汰回调函数
	if options.Type == Hybrid {
		// 在混合模式下，当内存中的项被淘汰时，自动将其保存到磁盘
		// 这确保即使数据从内存中移除，仍然可以从磁盘中恢复
		hm.memorymap.OnEvicted(func(k string, v interface{}) {
			_ = hm.diskmap.Set(k, v.([]byte), 0)
		})
	}

	// 如果启用了内存保护，初始化并启动内存监控
	if options.MemoryGuard {
		// 启动内存监控，定期检查内存使用情况
		runMemoryGuard(&hm, options.MemoryGuardTime)
		// 设置终结器，确保在垃圾回收时停止内存监控
		runtime.SetFinalizer(&hm, stopMemoryGuard)
	}

	hm.options = &options

	return &hm, nil
}

// Close 关闭HybridMap并清理资源
// @receiver hm
// @return error error: 闭过程中可能发生的错误，如磁盘写入失败或文件删除失败
func (hm *HybridMap) Close() error {
	// 如果磁盘存储已初始化，关闭它
	if hm.diskmap != (disk.DB)(nil) {
		hm.diskmap.Close()
	}
	// 如果配置了需要清理且磁盘路径非空，删除磁盘存储目录
	if hm.diskmapPath != "" && hm.options.Cleanup {
		return os.RemoveAll(hm.diskmapPath)
	}
	return nil
}

// Set 在HybridMap中设置键值对
// @receiver hm
// @param k string: 键名，用于唯一标识存储的数据
// @param v []byte: 值（字节数组），要存储的实际数据内容
// @return error error: 设置过程中可能发生的错误，如磁盘写入失败
func (hm *HybridMap) Set(k string, v []byte) error {
	var err error
	switch hm.options.Type {
	case Hybrid:
		// 混合模式，根据内存保护状态决定存储位置
		fallthrough
	case Memory:
		// 内存监控强制使用磁盘时，将数据写入磁盘
		if hm.options.MemoryGuardForceDisk {
			err = hm.diskmap.Set(k, v, hm.options.DiskExpirationTime)
		} else {
			hm.memorymap.Set(k, v)
		}
	case Disk:
		// 纯磁盘模式，直接写入磁盘存储
		err = hm.diskmap.Set(k, v, hm.options.DiskExpirationTime)
	}

	return err
}

// Get 从HybridMap中获取指定键的值
// @receiver hm 键名，要检索的数据的标识符
// @param k string: 
// @return []byte []byte: 
// @return bool bool: 
func (hm *HybridMap) Get(k string) ([]byte, bool) {
	switch hm.options.Type {
	case Memory:
		// 纯内存模式，只从内存缓存中检索数据
		v, ok := hm.memorymap.Get(k)
		if ok {
			return v.([]byte), ok
		}
		return []byte{}, ok
	case Hybrid:
		// 混合模式，首先尝试从内存获取（快速路径）
		v, ok := hm.memorymap.Get(k)
		if ok {
			return v.([]byte), ok
		}
		// 内存中不存在，尝试从磁盘获取（慢速路径）
		vm, err := hm.diskmap.Get(k)
		// 如果从磁盘获取成功，将其加载到内存中（因为最近被使用）
		// 这实现了一种"缓存预热"策略，提高后续访问的速度
		if err == nil {
			hm.memorymap.Set(k, vm)
		}
		return vm, err == nil
	case Disk:
		// 纯磁盘模式，只从磁盘中检索数据
		v, err := hm.diskmap.Get(k)
		return v, err == nil
	}

	return []byte{}, false
}

// Del 从HybridMap中删除指定键的值
// @receiver hm 
// @param key string:  要删除的键名
// @return error error: 删除过程中可能发生的错误，如磁盘访问失败
func (hm *HybridMap) Del(key string) error {
	switch hm.options.Type {
	case Memory:
		hm.memorymap.Delete(key)
	case Hybrid:
		// 混合模式，需要同时从内存和磁盘中删除
		hm.memorymap.Delete(key)
		return hm.diskmap.Del(key)
	case Disk:
		return hm.diskmap.Del(key)
	}

	return nil
}

// Scan 遍历HybridMap中的所有键值对
// @receiver hm 
// @param f func([]byte, []byte) error: 处理每个键值对的回调函数，接收键和值作为参数
func (hm *HybridMap) Scan(f func([]byte, []byte) error) {
	switch hm.options.Type {
	case Memory:
		hm.memorymap.Scan(f)
	case Hybrid:
		// 混合模式，同时遍历内存和磁盘
		hm.memorymap.Scan(f)
		_ = hm.diskmap.Scan(disk.ScannerOptions{Handler: f})
	case Disk:
		_ = hm.diskmap.Scan(disk.ScannerOptions{Handler: f})
	}
}

// Size 返回HybridMap中存储的键值对总数
// @receiver hm 
// @return int64 int64:  键值对总数（或在混合模式下的粗略估计）
func (hm *HybridMap) Size() int64 {
	var count int64
	if hm.memorymap != nil {
		count += int64(hm.memorymap.ItemCount())
	}
	if hm.diskmap != (disk.DB)(nil) {
		count += hm.diskmap.Size()
	}
	return count
}

// TuneMemory 调整内存使用策略
// @receiver hm 
func (hm *HybridMap) TuneMemory() {
	// 获取当前运行时内存统计信息
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	// 如果当前内存分配量超过设定的最大值，强制使用磁盘存储
	if m.Alloc >= uint64(hm.options.MaxMemorySize) {
		hm.options.MemoryGuardForceDisk = true
	} else {
		hm.options.MemoryGuardForceDisk = false
	}
}
