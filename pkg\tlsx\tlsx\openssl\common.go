// Author: chenjb
// Version: V1.0
// Date: 2025-06-23 19:55:58
// FilePath: /yaml_scan/pkg/tlsx/tlsx/openssl/common.go
// Description:
package openssl

import (
	"context"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"yaml_scan/pkg/gologger"
	errorutils "yaml_scan/utils/errors"
)

// 预定义的错误类型，用于标识不同类型的OpenSSL操作错误
var (
	ErrParse          = errorutils.NewWithTag("openssl", "failed to parse openssl response")
	ErrCertParse      = errorutils.NewWithTag("openssl", "failed to parse server certificate")
	ErrNotImplemented = errorutils.NewWithTag("openssl", "feature not implemented")
	ErrNotAvailable   = errorutils.NewWithTag("openssl", "executable not installed or in PATH")
	ErrNoSession      = errorutils.NewWithTag("openssl", "session not created/found")
)

var (
	BinaryPath   = ""    // OpenSSL二进制文件的完整路径
	OPENSSL_CONF = ""    // 自定义OpenSSL配置文件路径
	IsLibreSSL   = false // 标识是否使用LibreSSL而非OpenSSL
	PkgTag       = ""    // 包标签，包含OpenSSL/LibreSSL的版本信息，用于错误报告
)

// openSSLConfig 定义了自定义的OpenSSL配置内容
// 某些Linux发行版（如Ubuntu 18/19/20等）的OpenSSL配置文件设置了较高的最小协议版本
// 这会阻止对旧版TLS协议的测试，因此需要临时覆盖配置
// 注意：此配置覆盖仅适用于OpenSSL，不适用于LibreSSL（由于兼容性问题）
var openSSLConfig string = `openssl_conf = default_conf

[ default_conf ]
ssl_conf = ssl_sect

[ssl_sect]
system_default = system_default_sect

[system_default_sect]
MinProtocol = SSLv3
CipherString = DEFAULT:@SECLEVEL=0
`

func init() {
	// 根据操作系统类型查找OpenSSL二进制文件
	if runtime.GOOS == "windows" {
		BinaryPath, _ = exec.LookPath("openssl.exe")
	} else {
		// Unix-like系统查找openssl
		BinaryPath, _ = exec.LookPath("openssl")
	}
	// 检查是否成功找到OpenSSL可执行文件
	if BinaryPath == "" {
		gologger.Debug().Label("openssl").Msg("openssl binary not found skipping")
		return
	}
	// 执行OpenSSL的详细设置和配置
	if err := openSSLSetup(); err != nil {
		gologger.Debug().Label("openssl").Msg(err.Error())
	}
}

// openSSLSetup 获取OpenSSL版本信息并进行必要的配置设置
// 识别OpenSSL或LibreSSL的版本，并为OpenSSL创建自定义配置文件
// @return errorutils.Error errorutils.Error:
func openSSLSetup() errorutils.Error {
	// 执行OpenSSL version命令获取版本信息
	result, err := execOpenSSL(context.TODO(), []string{"version"})
	if err != nil {
		return errorutils.NewWithErr(err).WithTag("openssl").Msgf(result.Stderr)
	}
	// 解析版本输出，格式通常为："OpenSSL 1.1.1f  31 Mar 2020" 或 "LibreSSL 2.8.3"
	arr := strings.Fields(result.Stdout)
	if len(arr) < 2 {
		return errorutils.NewWithTag("openssl", "failed to parse openssl version got %v", result.Stdout)
	}

	// 检查是否为LibreSSL
	if arr[0] == "LibreSSL" {
		IsLibreSSL = true
	}
	// 提取版本号
	OpenSSLVersion := arr[1]
	// 仅为OpenSSL创建自定义配置文件，LibreSSL不需要
	if !IsLibreSSL {
		// 在临时目录创建自定义OpenSSL配置文件
		OPENSSL_CONF = filepath.Join(os.TempDir(), "openssl.cnf")
		err := os.WriteFile(OPENSSL_CONF, []byte(openSSLConfig), 0600)
		if err != nil {
			// 配置文件创建失败，记录日志并清空配置路径
			gologger.Debug().Label("openssl").Msg("Failed to create openssl.cnf file")
			OPENSSL_CONF = ""
		}
		PkgTag = "OpenSSL" + OpenSSLVersion // 设置OpenSSL包标签
	} else {
		PkgTag = "LibreSSL" + OpenSSLVersion // 设置LibreSSL包标签
	}

	return nil
}

// IsAvailable 检查OpenSSL是否可用
// @return bool bool:
func IsAvailable() bool {
	return BinaryPath != ""
}

// UseOpenSSLBinary From Path
func UseOpenSSLBinary(binpath string) {
	BinaryPath = binpath
	if err := openSSLSetup(); err != nil {
		// do not fallback
		gologger.Fatal().Label("openssl").Msg(err.Error())
	}
}
