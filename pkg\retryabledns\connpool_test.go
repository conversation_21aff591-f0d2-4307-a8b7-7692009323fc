//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-17 11:13:09
//FilePath: /yaml_scan/pkg/retryabledns/connpool_test.go
//Description:

package retryabledns

import (
	"context"
	"testing"
	"time"

	"github.com/miekg/dns"
	"github.com/stretchr/testify/require"
)

// TestNewConnPool 测试 NewConnPool 函数的正确性
func TestNewConnPool(t *testing.T) {
	resolver := NetworkResolver{
		Protocol: "udp",
		Host:     "*******",
		Port:     "53",
	}

	poolSize := 3 // 设置连接池大小
	pool, err := NewConnPool(resolver, poolSize)
	require.NoError(t, err)

	// 连接池的 items 字典大小与 poolSize 相等
	require.Equal(t, poolSize, len(pool.items))

	// 连接池的 clients 队列为空
	require.Equal(t, 0, len(pool.clients))
}

// TestGetConnection 测试 getConnection 方法
func TestGetConnection(t *testing.T) {
	resolver := NetworkResolver{
		Protocol: "udp",
		Host:     "*******",
		Port:     "53",
	}

	poolSize := 3 // 设置连接池大小
	pool, err := NewConnPool(resolver, poolSize)
	require.NoError(t, err)

	// 创建一个上下文
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
	defer cancel()

	// 启动一个 goroutine 模拟连接的返回
	go func() {
		time.Sleep(100 * time.Millisecond) // 模拟一些延迟
		client := <-pool.newArrival        // 从新到达通道获取客户端
		client.returnCh <- &dns.Conn{}     // 返回一个新的连接
	}()

	// 测试获取连接
	conn, err := pool.getConnection(ctx)
	require.NoError(t, err, "应该成功获取连接")
	require.NotNil(t, conn, "返回的连接不应该为 nil")
}

// TestReleaseConnection 测试 releaseConnection 方法
func TestReleaseConnection(t *testing.T) {
	// 创建一个新的连接池，假设大小为 1
	resolver := NetworkResolver{
		Protocol: "udp",
		Host:     "*******",
		Port:     "53",
	}

	poolSize := 1 // 设置连接池大小
	cp, err := NewConnPool(resolver, poolSize)
	require.NoError(t, err)

	// 创建一个连接
	conn := &dns.Conn{}

	// 启动一个 goroutine 来接收连接
	go func() {
		// 等待一段时间以确保连接被释放
		time.Sleep(100 * time.Millisecond)
		cp.releaseConnection(conn) // 释放连接
	}()

	// 从 finished 通道接收连接
	select {
	case receivedConn := <-cp.finished:
		require.Equal(t, conn, receivedConn, "接收到的连接应该与释放的连接相同")
	case <-time.After(1 * time.Second):
		t.Fatal("未能在规定时间内接收到连接")
	}
}

// TestExchange 测试 Exchange 方法
func TestExchange(t *testing.T) {
	// 创建一个新的连接池
	resolver := NetworkResolver{
		Protocol: "udp",
		Host:     "*******",
		Port:     "53",
	}

	poolSize := 1 // 设置连接池大小
	cp, err := NewConnPool(resolver, poolSize)
	require.NoError(t, err)

	// 创建一个模拟的 DNS 客户端
	client := &dns.Client{Net: "udp", Timeout: time.Second}

	// 创建一个上下文
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
	defer cancel()

	// 测试 Exchange 方法
	msg := &dns.Msg{} // 创建一个 DNS 消息
	msg.SetQuestion("www.baidu.com.", dns.TypeA)
	response, rtt, err := cp.Exchange(ctx, client, msg)

	// 断言结果
	require.NoError(t, err, "应该成功交换消息")
	require.NotNil(t, response, "返回的响应不应该为 nil")
	require.True(t, rtt > 0)
}

func TestConnPoolLocalAddrs(t *testing.T) {
	// 创建一个新的连接池
	resolver := NetworkResolver{
		Protocol: "udp",
		Host:     "*******",
		Port:     "53",
	}

	poolSize := 1 // 设置连接池大小
	cp, err := NewConnPool(resolver, poolSize)
	require.NoError(t, err)

	addrs := cp.LocalAddrs()
	require.Equal(t, poolSize, len(addrs))
	for _, addr := range addrs {
		require.NotNil(t, addr)
		require.Equal(t, "udp", addr.Network())
	}

	cp.Close()
}
