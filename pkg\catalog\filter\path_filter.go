package filter

import "yaml_scan/pkg/catalog"

// PathFilter is a path based template filter
type PathFilter struct {
	excludedTemplates          []string
	alwaysIncludedTemplatesMap map[string]struct{}
}

// PathFilterConfig contains configuration options for Path based templates Filter
type PathFilterConfig struct {
	IncludedTemplates []string
	ExcludedTemplates []string
}

// NewPathFilter creates a new path filter from provided config
func NewPathFilter(config *PathFilterConfig, catalogClient catalog.Catalog) *PathFilter {
	paths, _ := catalogClient.GetTemplatesPath(config.ExcludedTemplates)
	filter := &PathFilter{
		excludedTemplates:          paths,
		alwaysIncludedTemplatesMap: make(map[string]struct{}),
	}

	alwaysIncludeTemplates, _ := catalogClient.GetTemplatesPath(config.IncludedTemplates)
	for _, tpl := range alwaysIncludeTemplates {
		filter.alwaysIncludedTemplatesMap[tpl] = struct{}{}
	}
	return filter
}