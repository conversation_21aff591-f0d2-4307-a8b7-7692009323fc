// 
// Author: chenjb
// Version: V1.0
// Date: 2025-05-21 16:48:08
// FilePath: /yaml_scan/utils/ptr/ptr.go
// Description: 
package ptr

// Safe dereferences safely a pointer
// - if the pointer is nil => returns the zero value of the type of the pointer if nil
// - if the pointer is not nil => returns the dereferenced pointer
//
// Example:
//
//	var v *int
//	var x = ptr.Safe(v)
func Safe[T any](v *T) T {
	if v == nil {
		return *new(T)
	}
	return *v
}