// Author: chenjb
// Version: V1.0
// Date: 2025-06-30 11:23:21
// FilePath: /yaml_scan/pkg/tlsx/tlsx/ztls/util_test.go
// Description:
package ztls

import (
	"testing"

	"github.com/stretchr/testify/require"
)


// TestAllCiphersConsistency 测试密码套件数组的一致性
// 验证AllCiphers和AllCiphersNames的长度一致性
func TestAllCiphersConsistency(t *testing.T) {
	require.Equal(t, len(AllCiphers), len(AllCiphersNames),
		"AllCiphers和AllCiphersNames的长度应该一致")

	t.Logf("密码套件数组一致性验证通过：%d个密码套件", len(AllCiphers))
}

// TestAllCiphersUniqueness 测试密码套件的唯一性
// 验证密码套件数组中没有重复项
func TestAllCiphersUniqueness(t *testing.T) {
	// 测试密码套件数值的唯一性
	t.Run("密码套件数值唯一性", func(t *testing.T) {
		seen := make(map[uint16]bool)
		duplicates := []uint16{}

		for _, cipher := range AllCiphers {
			if seen[cipher] {
				duplicates = append(duplicates, cipher)
			}
			seen[cipher] = true
		}

		require.Empty(t, duplicates, "密码套件数值中不应该有重复项: %v", duplicates)
		t.Logf("验证通过：%d个密码套件数值都是唯一的", len(AllCiphers))
	})

	// 测试密码套件名称的唯一性
	t.Run("密码套件名称唯一性", func(t *testing.T) {
		seen := make(map[string]bool)
		duplicates := []string{}

		for _, cipher := range AllCiphersNames {
			if seen[cipher] {
				duplicates = append(duplicates, cipher)
			}
			seen[cipher] = true
		}

		require.Empty(t, duplicates, "密码套件名称中不应该有重复项: %v", duplicates)
		t.Logf("验证通过：%d个密码套件名称都是唯一的", len(AllCiphersNames))
	})
}

// TestSupportedTlsVersionsUniqueness 测试TLS版本的唯一性
// 验证TLS版本数组中没有重复项
func TestSupportedTlsVersionsUniqueness(t *testing.T) {
	seen := make(map[string]bool)
	duplicates := []string{}

	for _, version := range SupportedTlsVersions {
		if seen[version] {
			duplicates = append(duplicates, version)
		}
		seen[version] = true
	}

	require.Empty(t, duplicates, "TLS版本中不应该有重复项: %v", duplicates)
	require.Equal(t, len(SupportedTlsVersions), len(seen), "TLS版本数组长度应该等于唯一项数量")

	t.Logf("验证通过：%d个TLS版本都是唯一的", len(SupportedTlsVersions))
}

// TestCipherNamesContent 测试密码套件名称的内容
// 验证包含预期的常见密码套件
func TestCipherNamesContent(t *testing.T) {
	// 一些常见的密码套件，应该在列表中
	expectedCiphers := []string{
		"TLS_RSA_WITH_AES_128_CBC_SHA",
		"TLS_RSA_WITH_AES_256_CBC_SHA",
		"TLS_RSA_WITH_AES_128_GCM_SHA256",
		"TLS_RSA_WITH_AES_256_GCM_SHA384",
	}

	// 将AllCiphersNames转换为map以便快速查找
	cipherMap := make(map[string]bool)
	for _, cipher := range AllCiphersNames {
		cipherMap[cipher] = true
	}

	// 检查常见密码套件是否存在
	foundCount := 0
	for _, expected := range expectedCiphers {
		if cipherMap[expected] {
			foundCount++
			t.Logf("找到预期的密码套件: %s", expected)
		}
	}

	// ZMap支持更多密码套件，至少应该找到一些常见的
	require.Greater(t, foundCount, 0, "应该至少包含一些常见的密码套件")
	t.Logf("在 %d 个预期密码套件中找到了 %d 个", len(expectedCiphers), foundCount)
}


// TestGlobalVariablesNotEmpty 测试全局变量非空性
// 确保初始化过程产生了有意义的结果
func TestGlobalVariablesNotEmpty(t *testing.T) {
	// 测试密码套件列表
	require.Greater(t, len(AllCiphersNames), 0, "密码套件列表应该包含至少一个元素")
	require.Greater(t, len(AllCiphersNames), 10, "密码套件列表应该包含足够多的元素")

	// 测试TLS版本列表
	require.Greater(t, len(SupportedTlsVersions), 0, "TLS版本列表应该包含至少一个元素")
	require.GreaterOrEqual(t, len(SupportedTlsVersions), 2, "TLS版本列表应该包含至少2个版本")

	t.Logf("初始化结果统计 - 密码套件: %d个, TLS版本: %d个",
		len(AllCiphersNames), len(SupportedTlsVersions))
}

// TestCipherNamesFormat 测试密码套件名称格式的一致性
// 验证所有密码套件名称都符合预期的格式规范
func TestCipherNamesFormat(t *testing.T) {
	invalidCiphers := []string{}

	for _, cipher := range AllCiphersNames {
		// 检查基本格式要求
		if len(cipher) < 4 {
			invalidCiphers = append(invalidCiphers, cipher)
			continue
		}

		// 检查前缀（ZMap支持SSL_和TLS_前缀）
		if cipher[:4] != "TLS_" && cipher[:4] != "SSL_" {
			invalidCiphers = append(invalidCiphers, cipher)
			continue
		}

		// 检查是否包含非法字符（密码套件名称应该只包含字母、数字和下划线）
		for _, char := range cipher {
			if !((char >= 'A' && char <= 'Z') ||
				(char >= 'a' && char <= 'z') ||
				(char >= '0' && char <= '9') ||
				char == '_') {
				invalidCiphers = append(invalidCiphers, cipher)
				break
			}
		}
	}

	require.Empty(t, invalidCiphers, "发现格式不正确的密码套件: %v", invalidCiphers)
	t.Logf("所有 %d 个密码套件的格式都正确", len(AllCiphersNames))
}

// TestToZTLSCiphers 测试密码套件名称转换功能
// 验证toZTLSCiphers函数的正确性
func TestToZTLSCiphers(t *testing.T) {
	// 测试有效的密码套件转换
	t.Run("有效密码套件转换", func(t *testing.T) {
		validCiphers := []string{
			"TLS_RSA_WITH_AES_128_CBC_SHA",
			"TLS_RSA_WITH_AES_256_CBC_SHA",
			"TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256",
		}

		result, err := toZTLSCiphers(validCiphers)
		require.NoError(t, err, "转换有效密码套件应该成功")
		require.Len(t, result, len(validCiphers), "转换结果长度应该与输入一致")

		// 验证转换结果都是有效的数值
		for _, cipher := range result {
			require.Greater(t, cipher, uint16(0), "密码套件数值应该大于0")
		}

		t.Logf("成功转换了 %d 个密码套件", len(result))
	})

	// 测试无效的密码套件
	t.Run("无效密码套件处理", func(t *testing.T) {
		invalidCiphers := []string{
			"INVALID_CIPHER_SUITE",
			"TLS_NONEXISTENT_CIPHER",
		}

		result, err := toZTLSCiphers(invalidCiphers)
		require.Error(t, err, "转换无效密码套件应该返回错误")
		require.Nil(t, result, "转换失败时结果应该为nil")
		require.Contains(t, err.Error(), "not supported", "错误信息应该包含'not supported'")
	})

	// 测试空列表
	t.Run("空密码套件列表", func(t *testing.T) {
		result, err := toZTLSCiphers([]string{})
		require.NoError(t, err, "转换空列表应该成功")
		require.Empty(t, result, "空列表转换结果应该为空")
	})

	// 测试混合有效和无效密码套件
	t.Run("混合有效无效密码套件", func(t *testing.T) {
		mixedCiphers := []string{
			"TLS_RSA_WITH_AES_128_CBC_SHA", // 有效
			"INVALID_CIPHER",               // 无效
		}

		result, err := toZTLSCiphers(mixedCiphers)
		require.Error(t, err, "包含无效密码套件应该返回错误")
		require.Nil(t, result, "转换失败时结果应该为nil")
	})
}

// TestConvertCertificateToResponse 测试证书转换功能
// 验证ConvertCertificateToResponse函数的正确性
func TestConvertCertificateToResponse(t *testing.T) {
	// 测试nil证书处理
	t.Run("nil证书处理", func(t *testing.T) {
		result := ConvertCertificateToResponse(nil, "example.com", nil)
		require.Nil(t, result, "nil证书应该返回nil")
	})

	// 由于需要真实的x509.Certificate对象，这里创建一个模拟测试
	// 在实际环境中，可以使用测试证书文件
	t.Run("证书转换基本功能", func(t *testing.T) {
		// 这个测试需要真实的证书数据，暂时跳过
		// 在实际使用中，可以加载测试证书文件进行完整测试
		t.Skip("需要真实证书数据进行测试")
	})
}

// TestParseSimpleTLSCertificate 测试简单TLS证书解析功能
// 验证ParseSimpleTLSCertificate函数的正确性
func TestParseSimpleTLSCertificate(t *testing.T) {
	// 由于需要真实的tls.SimpleCertificate对象，这里创建一个基本测试
	t.Run("证书解析基本功能", func(t *testing.T) {
		// 这个测试需要真实的证书数据，暂时跳过
		// 在实际使用中，可以使用测试证书数据进行完整测试
		t.Skip("需要真实证书数据进行测试")
	})
}
