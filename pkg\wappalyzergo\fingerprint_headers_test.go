// Author: chenjb
// Version: V1.0
// Date: 2025-07-18 11:28:17
// FilePath: /yaml_scan/pkg/wappalyzergo/fingerprint_headers_test.go
// Description: fingerprint_headers.go 的单元测试文件，测试基于HTTP响应头的技术指纹识别功能

package wappalyzergo

import (
    "testing"

    "github.com/stretchr/testify/require"
)

// TestCheckHeaders 测试HTTP响应头指纹检查功能
func TestCheckHeaders(t *testing.T) {
    wappalyzer, err := New()
    require.NoError(t, err, "创建实例不应失败")

    t.Run("检测Nginx服务器", func(t *testing.T) {
        headers := map[string]string{
            "server": "nginx/1.18.0",
        }

        results := wappalyzer.checkHeaders(headers)
        require.NotNil(t, results, "结果不应为nil")
    })

    t.Run("检测Apache服务器", func(t *testing.T) {
        headers := map[string]string{
            "server": "apache/2.4.41 (ubuntu)",
        }

        results := wappalyzer.checkHeaders(headers)
        require.NotNil(t, results, "结果不应为nil")
    })

    t.Run("检测PHP技术栈", func(t *testing.T) {
        headers := map[string]string{
            "x-powered-by": "php/7.4.0",
        }

        results := wappalyzer.checkHeaders(headers)
        require.NotNil(t, results, "结果不应为nil")
    })

    t.Run("检测ASP.NET技术栈", func(t *testing.T) {
        headers := map[string]string{
            "x-powered-by":     "asp.net",
            "x-aspnet-version": "4.0.30319",
        }

        results := wappalyzer.checkHeaders(headers)
        require.NotNil(t, results, "结果不应为nil")
    })

    t.Run("检测IIS服务器", func(t *testing.T) {
        headers := map[string]string{
            "server": "microsoft-iis/10.0",
        }

        results := wappalyzer.checkHeaders(headers)
        require.NotNil(t, results, "结果不应为nil")
    })

    t.Run("检测WordPress CMS", func(t *testing.T) {
        headers := map[string]string{
            "x-generator": "wordpress 5.8",
        }

        results := wappalyzer.checkHeaders(headers)
        require.NotNil(t, results, "结果不应为nil")
    })

    t.Run("检测Drupal CMS", func(t *testing.T) {
        headers := map[string]string{
            "x-drupal-cache": "hit",
            "x-generator":    "drupal 9.0",
        }

        results := wappalyzer.checkHeaders(headers)
        require.NotNil(t, results, "结果不应为nil")
    })

    t.Run("检测Laravel框架", func(t *testing.T) {
        headers := map[string]string{
            "x-powered-by": "php/8.0.0",
            "x-framework":  "laravel",
        }

        results := wappalyzer.checkHeaders(headers)
        require.NotNil(t, results, "结果不应为nil")
    })

    t.Run("混合技术栈检测", func(t *testing.T) {
        headers := map[string]string{
            "server":       "nginx/1.18.0",
            "x-powered-by": "php/7.4.0",
            "x-generator":  "wordpress 5.8",
        }

        results := wappalyzer.checkHeaders(headers)
        require.NotNil(t, results, "结果不应为nil")
        t.Logf("检测到 %d 个技术指纹", len(results))
    })

    t.Run("空头部映射", func(t *testing.T) {
        headers := map[string]string{}

        results := wappalyzer.checkHeaders(headers)
        require.NotNil(t, results, "空头部结果不应为nil")
        require.Empty(t, results, "空头部应该返回空结果")
    })

    t.Run("无关头部", func(t *testing.T) {
        headers := map[string]string{
            "content-type":   "text/html",
            "content-length": "1234",
            "date":           "Mon, 01 Jan 2024 00:00:00 GMT",
        }

        results := wappalyzer.checkHeaders(headers)
        require.NotNil(t, results, "无关头部结果不应为nil")
    })

    t.Run("包含特殊字符的头部", func(t *testing.T) {
        headers := map[string]string{
            "server":      "nginx/1.18.0 (ubuntu; ssl)",
            "x-custom":    "value with spaces & symbols!",
            "x-encoding":  "utf-8; charset=utf-8",
        }

        results := wappalyzer.checkHeaders(headers)
        require.NotNil(t, results, "特殊字符头部结果不应为nil")
    })

    t.Run("大小写混合头部", func(t *testing.T) {
        headers := map[string]string{
            "Server":       "NGINX/1.18.0",
            "X-Powered-By": "PHP/7.4.0",
            "Content-Type": "TEXT/HTML",
        }

        results := wappalyzer.checkHeaders(headers)
        require.NotNil(t, results, "大小写混合头部结果不应为nil")
    })
}

// TestGetHeadersMap 测试HTTP头部数组转换为映射功能
func TestGetHeadersMap(t *testing.T) {
    t.Run("转换基本头部", func(t *testing.T) {
        headersArray := map[string][]string{
            "Server":       {"nginx/1.18.0"},
            "Content-Type": {"text/html; charset=utf-8"},
        }

        headers := getHeadersMap(headersArray)
        require.NotNil(t, headers, "转换结果不应为nil")
        require.Len(t, headers, 2, "应该有2个头部")
        require.Equal(t, "nginx/1.18.0", headers["Server"], "Server头部应该匹配")
        require.Equal(t, "text/html; charset=utf-8", headers["Content-Type"], "Content-Type头部应该匹配")
    })

    t.Run("转换多值头部", func(t *testing.T) {
        headersArray := map[string][]string{
            "Set-Cookie": {"sessionid=abc123; Path=/", "csrftoken=xyz789; HttpOnly"},
            "Accept":     {"text/html", "application/json"},
        }

        headers := getHeadersMap(headersArray)
        require.NotNil(t, headers, "转换结果不应为nil")
        require.Len(t, headers, 2, "应该有2个头部")
        require.Equal(t, "sessionid=abc123; Path=/, csrftoken=xyz789; HttpOnly", headers["Set-Cookie"], "Set-Cookie应该用逗号连接")
        require.Equal(t, "text/html, application/json", headers["Accept"], "Accept应该用逗号连接")
    })

    t.Run("转换单值头部", func(t *testing.T) {
        headersArray := map[string][]string{
            "Server":         {"nginx/1.18.0"},
            "Content-Length": {"1234"},
        }

        headers := getHeadersMap(headersArray)
        require.NotNil(t, headers, "转换结果不应为nil")
        require.Len(t, headers, 2, "应该有2个头部")
        require.Equal(t, "nginx/1.18.0", headers["Server"], "单值头部应该直接转换")
        require.Equal(t, "1234", headers["Content-Length"], "数字值应该保持原样")
    })

    t.Run("转换空值头部", func(t *testing.T) {
        headersArray := map[string][]string{
            "Empty-Header": {""},
            "Normal":       {"value"},
        }

        headers := getHeadersMap(headersArray)
        require.NotNil(t, headers, "转换结果不应为nil")
        require.Len(t, headers, 2, "应该有2个头部")
        require.Equal(t, "", headers["Empty-Header"], "空值头部应该为空字符串")
        require.Equal(t, "value", headers["Normal"], "正常值应该匹配")
    })

    t.Run("转换空数组头部", func(t *testing.T) {
        headersArray := map[string][]string{
            "Empty-Array": {},
            "Normal":      {"value"},
        }

        headers := getHeadersMap(headersArray)
        require.NotNil(t, headers, "转换结果不应为nil")
        require.Len(t, headers, 2, "应该有2个头部")
        require.Equal(t, "", headers["Empty-Array"], "空数组头部应该为空字符串")
        require.Equal(t, "value", headers["Normal"], "正常值应该匹配")
    })

    t.Run("转换空映射", func(t *testing.T) {
        headersArray := map[string][]string{}

        headers := getHeadersMap(headersArray)
        require.NotNil(t, headers, "空映射结果不应为nil")
        require.Empty(t, headers, "空映射应该返回空结果")
    })

    t.Run("转换包含特殊字符的头部", func(t *testing.T) {
        headersArray := map[string][]string{
            "X-Custom": {"value with spaces", "symbols!@#$%", "unicode测试"},
        }

        headers := getHeadersMap(headersArray)
        require.NotNil(t, headers, "特殊字符转换结果不应为nil")
        require.Len(t, headers, 1, "应该有1个头部")
        require.Equal(t, "value with spaces, symbols!@#$%, unicode测试", headers["X-Custom"], "特殊字符应该正确连接")
    })

    t.Run("转换大量头部值", func(t *testing.T) {
        // 创建包含大量值的头部
        values := make([]string, 100)
        for i := 0; i < 100; i++ {
            values[i] = "value" + string(rune('0'+i%10))
        }

        headersArray := map[string][]string{
            "X-Many-Values": values,
        }

        headers := getHeadersMap(headersArray)
        require.NotNil(t, headers, "大量值转换结果不应为nil")
        require.Len(t, headers, 1, "应该有1个头部")
        require.Contains(t, headers["X-Many-Values"], "value0", "应该包含第一个值")
        require.Contains(t, headers["X-Many-Values"], "value9", "应该包含最后一个值")
    })
}

// TestNormalizeHeaders 测试HTTP头部标准化功能
func TestNormalizeHeaders(t *testing.T) {
    wappalyzer, err := New()
    require.NoError(t, err, "创建实例不应失败")

    t.Run("标准化基本头部", func(t *testing.T) {
        headers := map[string][]string{
            "Server":       {"Nginx/1.18.0"},
            "Content-Type": {"Text/HTML; charset=UTF-8"},
        }

        normalized := wappalyzer.normalizeHeaders(headers)
        require.NotNil(t, normalized, "标准化结果不应为nil")
        require.Len(t, normalized, 2, "应该有2个头部")
        require.Equal(t, "nginx/1.18.0", normalized["server"], "Server应该转换为小写")
        require.Equal(t, "text/html; charset=utf-8", normalized["content-type"], "Content-Type应该转换为小写")
    })

    t.Run("标准化混合大小写头部", func(t *testing.T) {
        headers := map[string][]string{
            "X-Powered-By": {"PHP/7.4.0"},
            "X-GENERATOR":  {"WordPress 5.8"},
            "server":       {"apache/2.4.41"},
        }

        normalized := wappalyzer.normalizeHeaders(headers)
        require.NotNil(t, normalized, "标准化结果不应为nil")
        require.Len(t, normalized, 3, "应该有3个头部")
        require.Equal(t, "php/7.4.0", normalized["x-powered-by"], "X-Powered-By应该转换为小写")
        require.Equal(t, "wordpress 5.8", normalized["x-generator"], "X-GENERATOR应该转换为小写")
        require.Equal(t, "apache/2.4.41", normalized["server"], "已经小写的应该保持不变")
    })

    t.Run("标准化多值头部", func(t *testing.T) {
        headers := map[string][]string{
            "Set-Cookie": {"SessionID=ABC123; Path=/", "CSRFToken=XYZ789; HttpOnly"},
            "Accept":     {"Text/HTML", "Application/JSON"},
        }

        normalized := wappalyzer.normalizeHeaders(headers)
        require.NotNil(t, normalized, "标准化结果不应为nil")
        require.Len(t, normalized, 2, "应该有2个头部")
        require.Equal(t, "sessionid=abc123; path=/, csrftoken=xyz789; httponly", normalized["set-cookie"], "Set-Cookie应该全部小写")
        require.Equal(t, "text/html, application/json", normalized["accept"], "Accept应该全部小写")
    })

    t.Run("标准化特殊字符头部", func(t *testing.T) {
        headers := map[string][]string{
            "X-Custom": {"Value With SPACES & Symbols!"},
            "X-Unicode": {"测试VALUE"},
        }

        normalized := wappalyzer.normalizeHeaders(headers)
        require.NotNil(t, normalized, "特殊字符标准化结果不应为nil")
        require.Len(t, normalized, 2, "应该有2个头部")
        require.Equal(t, "value with spaces & symbols!", normalized["x-custom"], "特殊字符应该正确处理")
        require.Equal(t, "测试value", normalized["x-unicode"], "Unicode字符应该正确处理")
    })

    t.Run("标准化空头部", func(t *testing.T) {
        headers := map[string][]string{}

        normalized := wappalyzer.normalizeHeaders(headers)
        require.NotNil(t, normalized, "空头部标准化结果不应为nil")
        require.Empty(t, normalized, "空头部应该返回空结果")
    })

    t.Run("标准化包含空值的头部", func(t *testing.T) {
        headers := map[string][]string{
            "Empty-Header": {""},
            "Normal":       {"Value"},
        }

        normalized := wappalyzer.normalizeHeaders(headers)
        require.NotNil(t, normalized, "包含空值的标准化结果不应为nil")
        require.Len(t, normalized, 2, "应该有2个头部")
        require.Equal(t, "", normalized["empty-header"], "空值应该保持为空")
        require.Equal(t, "value", normalized["normal"], "正常值应该转换为小写")
    })
}

// TestFindSetCookie 测试从标准化头部提取Set-Cookie值
func TestFindSetCookie(t *testing.T) {
    wappalyzer, err := New()
    require.NoError(t, err, "创建实例不应失败")

    t.Run("提取基本Set-Cookie", func(t *testing.T) {
        headers := map[string]string{
            "set-cookie": "phpsessid=abc123; path=/",
        }

        cookies := wappalyzer.findSetCookie(headers)
        require.NotNil(t, cookies, "Cookie列表不应为nil")
        require.NotEmpty(t, cookies, "Cookie列表不应为空")
        require.Contains(t, cookies, "phpsessid=abc123;", "应该包含Cookie名值对")
    })

    t.Run("提取多个Cookie", func(t *testing.T) {
        headers := map[string]string{
            "set-cookie": "phpsessid=abc123; path=/, csrftoken=xyz789; httponly",
        }

        cookies := wappalyzer.findSetCookie(headers)
        require.NotNil(t, cookies, "Cookie列表不应为nil")
        require.NotEmpty(t, cookies, "Cookie列表不应为空")
    })

    t.Run("无Set-Cookie头部", func(t *testing.T) {
        headers := map[string]string{
            "server":       "nginx/1.18.0",
            "content-type": "text/html",
        }

        cookies := wappalyzer.findSetCookie(headers)
        require.Empty(t, cookies, "无Set-Cookie应该返回空列表")
    })

    t.Run("空Set-Cookie值", func(t *testing.T) {
        headers := map[string]string{
            "set-cookie": "",
        }

        cookies := wappalyzer.findSetCookie(headers)
        require.Empty(t, cookies, "空Set-Cookie应该返回空列表")
    })

    t.Run("复杂Set-Cookie格式", func(t *testing.T) {
        headers := map[string]string{
            "set-cookie": "sessionid=abc123; path=/; httponly, csrftoken=xyz789; secure; samesite=strict",
        }

        cookies := wappalyzer.findSetCookie(headers)
        require.NotNil(t, cookies, "复杂格式Cookie列表不应为nil")
        require.NotEmpty(t, cookies, "复杂格式Cookie列表不应为空")
    })
}

// TestAnalyzeHeaderPatterns 测试HTTP头部模式分析功能
func TestAnalyzeHeaderPatterns(t *testing.T) {
    wappalyzer, err := New()
    require.NoError(t, err, "创建实例不应失败")

    t.Run("分析基本服务器模式", func(t *testing.T) {
        headers := map[string]string{
            "server": "nginx/1.18.0",
        }

        results := wappalyzer.analyzeHeaderPatterns(headers)
        require.NotNil(t, results, "分析结果不应为nil")
        require.Contains(t, results, "Nginx", "应该识别Nginx")
        require.Greater(t, results["Nginx"], 80.0, "Nginx置信度应该较高")
    })

    t.Run("分析Apache服务器模式", func(t *testing.T) {
        headers := map[string]string{
            "server": "apache/2.4.41 (ubuntu)",
        }

        results := wappalyzer.analyzeHeaderPatterns(headers)
        require.NotNil(t, results, "分析结果不应为nil")
        require.Contains(t, results, "Apache", "应该识别Apache")
        require.Greater(t, results["Apache"], 80.0, "Apache置信度应该较高")
    })

    t.Run("分析IIS服务器模式", func(t *testing.T) {
        headers := map[string]string{
            "server": "microsoft-iis/10.0",
        }

        results := wappalyzer.analyzeHeaderPatterns(headers)
        require.NotNil(t, results, "分析结果不应为nil")
        require.Contains(t, results, "IIS", "应该识别IIS")
        require.Greater(t, results["IIS"], 80.0, "IIS置信度应该较高")
    })

    t.Run("分析ASP.NET模式", func(t *testing.T) {
        headers := map[string]string{
            "x-aspnet-version": "4.0.30319",
        }

        results := wappalyzer.analyzeHeaderPatterns(headers)
        require.NotNil(t, results, "分析结果不应为nil")
        require.Contains(t, results, "ASP.NET", "应该识别ASP.NET")
        require.Greater(t, results["ASP.NET"], 90.0, "ASP.NET置信度应该很高")
    })

    t.Run("分析Drupal模式", func(t *testing.T) {
        headers := map[string]string{
            "x-drupal-cache": "hit",
        }

        results := wappalyzer.analyzeHeaderPatterns(headers)
        require.NotNil(t, results, "分析结果不应为nil")
        require.Contains(t, results, "Drupal", "应该识别Drupal")
        require.Greater(t, results["Drupal"], 80.0, "Drupal置信度应该较高")
    })

    t.Run("分析安全头部模式", func(t *testing.T) {
        headers := map[string]string{
            "x-xss-protection":         "1; mode=block",
            "content-security-policy": "default-src 'self'",
        }

        results := wappalyzer.analyzeHeaderPatterns(headers)
        require.NotNil(t, results, "分析结果不应为nil")
        require.Contains(t, results, "Security Headers", "应该识别安全头部")
        require.Greater(t, results["Security Headers"], 70.0, "安全头部置信度应该较高")
    })

    t.Run("分析组合模式", func(t *testing.T) {
        headers := map[string]string{
            "server":           "microsoft-iis/10.0",
            "x-aspnet-version": "4.0.30319",
        }

        results := wappalyzer.analyzeHeaderPatterns(headers)
        require.NotNil(t, results, "组合分析结果不应为nil")
        require.Contains(t, results, "IIS", "应该识别IIS")
        require.Contains(t, results, "ASP.NET", "应该识别ASP.NET")
        require.Equal(t, 100.0, results["ASP.NET"], "组合模式下ASP.NET置信度应该是100%")
    })

    t.Run("分析通用Web技术模式", func(t *testing.T) {
        headers := map[string]string{
            "x-powered-by": "php/7.4.0",
        }

        results := wappalyzer.analyzeHeaderPatterns(headers)
        require.NotNil(t, results, "通用技术分析结果不应为nil")
        require.Contains(t, results, "Generic Web Technology", "应该识别通用Web技术")
        require.Greater(t, results["Generic Web Technology"], 60.0, "通用技术置信度应该适中")
    })

    t.Run("分析空头部", func(t *testing.T) {
        headers := map[string]string{}

        results := wappalyzer.analyzeHeaderPatterns(headers)
        require.NotNil(t, results, "空头部分析结果不应为nil")
        require.Empty(t, results, "空头部应该返回空结果")
    })

    t.Run("分析无关头部", func(t *testing.T) {
        headers := map[string]string{
            "content-type":   "text/html",
            "content-length": "1234",
            "date":           "Mon, 01 Jan 2024 00:00:00 GMT",
        }

        results := wappalyzer.analyzeHeaderPatterns(headers)
        require.NotNil(t, results, "无关头部分析结果不应为nil")
        require.Empty(t, results, "无关头部应该返回空结果")
    })
}

// TestHeadersIntegration 测试HTTP头部处理的完整流程
func TestHeadersIntegration(t *testing.T) {
    wappalyzer, err := New()
    require.NoError(t, err, "创建实例不应失败")

    t.Run("完整头部处理流程", func(t *testing.T) {
        // 模拟原始HTTP响应头
        rawHeaders := map[string][]string{
            "Server":         {"Nginx/1.18.0"},
            "X-Powered-By":   {"PHP/7.4.0"},
            "X-Generator":    {"WordPress 5.8"},
            "Set-Cookie":     {"PHPSESSID=abc123; Path=/", "wp_session=xyz789"},
            "Content-Type":   {"text/html; charset=UTF-8"},
        }

        // 1. 标准化头部
        normalized := wappalyzer.normalizeHeaders(rawHeaders)
        require.NotNil(t, normalized, "标准化头部不应为nil")
        require.NotEmpty(t, normalized, "标准化头部不应为空")

        // 2. 进行指纹识别
        results := wappalyzer.checkHeaders(normalized)
        require.NotNil(t, results, "指纹识别结果不应为nil")

        // 3. 分析头部模式
        patterns := wappalyzer.analyzeHeaderPatterns(normalized)
        require.NotNil(t, patterns, "模式分析结果不应为nil")

        // 4. 提取Cookie
        cookies := wappalyzer.findSetCookie(normalized)
        require.NotNil(t, cookies, "Cookie提取结果不应为nil")

        t.Logf("标准化头部数量: %d", len(normalized))
        t.Logf("指纹识别结果数量: %d", len(results))
        t.Logf("模式分析结果数量: %d", len(patterns))
        t.Logf("提取Cookie数量: %d", len(cookies))
    })

    t.Run("真实场景WordPress网站", func(t *testing.T) {
        headers := map[string][]string{
            "Server":       {"nginx/1.18.0"},
            "X-Powered-By": {"PHP/7.4.0"},
            "X-Pingback":   {"https://example.com/xmlrpc.php"},
            "Link":         {"<https://example.com/wp-json/>; rel=\"https://api.w.org/\""},
        }

        normalized := wappalyzer.normalizeHeaders(headers)
        results := wappalyzer.checkHeaders(normalized)
        require.NotNil(t, results, "WordPress网站结果不应为nil")
    })

    t.Run("真实场景ASP.NET网站", func(t *testing.T) {
        headers := map[string][]string{
            "Server":           {"Microsoft-IIS/10.0"},
            "X-Powered-By":     {"ASP.NET"},
            "X-AspNet-Version": {"4.0.30319"},
        }

        normalized := wappalyzer.normalizeHeaders(headers)
        results := wappalyzer.checkHeaders(normalized)
        patterns := wappalyzer.analyzeHeaderPatterns(normalized)
        
        require.NotNil(t, results, "ASP.NET网站结果不应为nil")
        require.NotNil(t, patterns, "ASP.NET模式分析不应为nil")
    })

    t.Run("真实场景静态网站", func(t *testing.T) {
        headers := map[string][]string{
            "Server":       {"nginx/1.18.0"},
            "Content-Type": {"text/html"},
            "Cache-Control": {"max-age=3600"},
        }

        normalized := wappalyzer.normalizeHeaders(headers)
        results := wappalyzer.checkHeaders(normalized)
        require.NotNil(t, results, "静态网站结果不应为nil")
    })
}