package goflags


import (
	"strings"

	fileutil "yaml_scan/utils/file"

	"github.com/pkg/errors"
)


// Options 结构体用于定义与字符串处理相关的选项。
type Options struct {
	// IsFromFile 是一个函数，接受一个字符串参数并返回一个布尔值。
	// 它用于判断给定的值是否来自文件。
	IsFromFile func(string) bool

	// IsEmpty 是一个函数，接受一个字符串参数并返回一个布尔值。
	// 它用于判断给定的值是否为空。
	IsEmpty func(string) bool

	// Normalize 是一个函数，接受一个字符串参数并返回一个字符串。
	// 它用于对值进行规范化处理，例如去除尾部空格。
	Normalize func(string) string

	// IsRaw 是一个函数，接受一个字符串参数并返回一个布尔值。
	// 它用于判断给定的值是否应被视为原始字符串。
	IsRaw func(string) bool
}


// isEmpty 判断给定的字符串是否为空或仅包含空白字符。
// 如果字符串为空或仅包含空格、制表符等空白字符，则返回 true；否则返回 false。
func isEmpty(s string) bool {
	return strings.TrimSpace(s) == ""
}


var quotes = []rune{'"', '\'', '`'}


// isQuote 检查给定的字符是否是引号字符
//
// char: 要检查的字符
// 返回值: 如果字符是引号，返回 true 和对应的引号字符；否则返回 false 和 0。
func isQuote(char rune) (bool, rune) {
	for _, quote := range quotes {
		if quote == char {
			return true, quote
		}
	}
	return false, 0
}


// searchPart 在给定字符串中查找指定字符，并返回指定字符之前的所有字符
//
// value: 要搜索的字符串
// stop: 停止字符 指定的字符
// 返回值: 如果找到停止字符，返回 true 和停止字符之前的字符串；否则返回 false 和结果字符串。
func searchPart(value string, stop rune) (bool, string) {
	var result string
	for _, char := range value {
		if char != stop {
			result += string(char)
		} else {
			return true, result
		}
	}
	return false, result
}

// normalizeLowercase 将输入字符串转换为小写，去除两端的空白字符和引号。
func normalizeLowercase(s string) string {
	return strings.TrimSpace(strings.Trim(strings.TrimSpace(strings.ToLower(s)), string(quotes)))
}


//isFromFile 检查给定的字符串是否表示一个有效的文件路径。这里返回true就行 调用有具体实现
func isFromFile(_ string) bool {
	return true
}

// normalizeTrailingParts 用于去除字符串两端的空白字符
func normalizeTrailingParts(s string) string {
	return strings.TrimSpace(s)
}


// ToStringSlice 将值转换为字符串切片，基于选项进行处理
//
// value: 要转换的字符串
// options: 转换选项
// 返回值: 转换后的字符串切片和可能发生的错误
func ToStringSlice(value string, options Options) ([]string, error) {
	// 用于存储结果字符串切片
	var result []string
	// 如果没有提供任何选项，直接返回包含原始值的切片
	if options.IsEmpty == nil && options.IsFromFile == nil && options.Normalize == nil {
		return []string{value}, nil
	}

	// 定义一个内部函数，用于将部分字符串添加到结果中
	addPartToResult := func(part string) {
		if !options.IsEmpty(part) {
			if options.Normalize != nil {
				part = options.Normalize(part)
			}
			result = append(result, part)
		}
	}
	// 检查值是否是文件路径，并且选项中指定了 IsFromFile 函数
	if fileutil.FileExists(value) && options.IsFromFile != nil && options.IsFromFile(value) {
		// 从文件中读取行
		linesChan, err := fileutil.ReadFile(value)
		if err != nil {
			return nil, err
		}
		// 遍历从文件中读取的每一行 将每一行添加到结果中
		for line := range linesChan {
			addPartToResult(line)
		}
	} else if options.IsRaw != nil && options.IsRaw(value) {
		// 如果值是原始字符串，直接添加到结果中
		addPartToResult(value)
	} else {
		// 处理普通字符串，按逗号分割
		index := 0
		for index < len(value) {
			// 获取当前字符
			char := rune(value[index])
			if isQuote, quote := isQuote(char); isQuote {
				// 如果当前字符是引号，查找引号内的部分
				quoteFound, part := searchPart(value[index+1:], quote)

				if !quoteFound {
					return nil, errors.New("Unclosed quote in path")
				}
				// 更新索引，跳过引号和内容
				index += len(part) + 2

				addPartToResult(part)
			} else {
				// 查找下一个逗号分隔的部分
				commaFound, part := searchPart(value[index:], ',')

				if commaFound {
					 // 更新索引，跳过逗号
					index += len(part) + 1
				} else {
					// 更新索引，跳过最后一部分
					index += len(part)	
				}

				addPartToResult(part)
			}
		}
	}
	return result, nil
}


// ToString 将一个字符串切片转换为格式化的字符串。
// 该字符串的格式为 JSON 风格的数组，例如 ["item1", "item2", "item3"]。
//
// slice: 要转换的字符串切片。
// 返回值: 返回格式化后的字符串表示。
func ToString(slice []string) string {
	 // 创建一个字符串构建器，用于高效地构建字符串
	defaultBuilder := &strings.Builder{}
	// 开始构建字符串，添加开头的方括号
	defaultBuilder.WriteString("[")
	for i, k := range slice {
		 // 为每个元素添加双引号
		defaultBuilder.WriteString("\"")
		defaultBuilder.WriteString(k)
		defaultBuilder.WriteString("\"")
		// 如果不是最后一个元素，则添加逗号和空格
		if i != len(slice)-1 {
			defaultBuilder.WriteString(", ")
		}
	}
	 // 添加结尾的方括号
	defaultBuilder.WriteString("]")
	
	return defaultBuilder.String()
}