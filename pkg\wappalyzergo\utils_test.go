//
// Author: chenjb
// Version: V1.0
// Date: 2025-07-18 11:10:30
// FilePath: /yaml_scan/pkg/wappalyzergo/utils_test.go
// Description: 工具函数的单元测试文件

package wappalyzergo

import (
    "testing"

    "github.com/stretchr/testify/require"
)

// TestNormalizeHeaders 测试HTTP头部标准化功能
func TestNormalizeHeaders(t *testing.T) {
    wappalyzer, err := New()
    require.NoError(t, err, "创建实例不应失败")

    t.Run("标准化基本头部", func(t *testing.T) {
        headers := map[string][]string{
            "Content-Type": {"text/html; charset=utf-8"},
            "Server":       {"nginx/1.18.0"},
            "X-Powered-By": {"PHP/7.4.0"},
        }

        normalized := wappalyzer.normalizeHeaders(headers)
        require.NotNil(t, normalized, "标准化结果不应为nil")
        
        // 检查键是否转换为小写
        require.Contains(t, normalized, "content-type", "应该包含小写的content-type")
        require.Contains(t, normalized, "server", "应该包含小写的server")
        require.Contains(t, normalized, "x-powered-by", "应该包含小写的x-powered-by")
    })

    t.Run("处理多值头部", func(t *testing.T) {
        headers := map[string][]string{
            "Accept": {"text/html", "application/json", "text/plain"},
            "Cache-Control": {"no-cache", "no-store"},
        }

        normalized := wappalyzer.normalizeHeaders(headers)
        require.NotNil(t, normalized, "标准化结果不应为nil")
        
        // 检查多值是否正确合并
        acceptValue := normalized["accept"]
        require.Contains(t, acceptValue, "text/html", "应该包含第一个accept值")
        require.Contains(t, acceptValue, "application/json", "应该包含第二个accept值")
    })

    t.Run("处理空头部", func(t *testing.T) {
        headers := map[string][]string{}
        normalized := wappalyzer.normalizeHeaders(headers)
        require.NotNil(t, normalized, "空头部标准化结果不应为nil")
        require.Empty(t, normalized, "空头部标准化结果应为空")
    })

    t.Run("处理nil头部", func(t *testing.T) {
        normalized := wappalyzer.normalizeHeaders(nil)
        require.NotNil(t, normalized, "nil头部标准化结果不应为nil")
        require.Empty(t, normalized, "nil头部标准化结果应为空")
    })
}

// TestFindSetCookie 测试Cookie提取功能
func TestFindSetCookie(t *testing.T) {
    wappalyzer, err := New()
    require.NoError(t, err, "创建实例不应失败")

    t.Run("提取基本Cookie", func(t *testing.T) {
        headers := map[string]string{
            "set-cookie": "sessionid=abc123; path=/; httponly",
        }

        cookies := wappalyzer.findSetCookie(headers)
        require.NotNil(t, cookies, "Cookie结果不应为nil")
        require.Contains(t, cookies, "sessionid", "应该包含sessionid cookie")
    })

    t.Run("提取多个Cookie", func(t *testing.T) {
        headers := map[string]string{
            "set-cookie": "sessionid=abc123; userid=user456; theme=dark",
        }

        cookies := wappalyzer.findSetCookie(headers)
        require.NotNil(t, cookies, "Cookie结果不应为nil")
        require.Contains(t, cookies, "sessionid", "应该包含sessionid")
        require.Contains(t, cookies, "userid", "应该包含userid")
        require.Contains(t, cookies, "theme", "应该包含theme")
    })

    t.Run("无Set-Cookie头部", func(t *testing.T) {
        headers := map[string]string{
            "content-type": "text/html",
        }

        cookies := wappalyzer.findSetCookie(headers)
        require.NotNil(t, cookies, "无Cookie时结果不应为nil")
        require.Empty(t, cookies, "无Cookie时结果应为空")
    })
}

// TestGetTitle 测试页面标题提取功能
func TestGetTitle(t *testing.T) {
    wappalyzer, err := New()
    require.NoError(t, err, "创建实例不应失败")

    t.Run("提取基本标题", func(t *testing.T) {
        body := []byte("<html><head><title>测试页面</title></head><body></body></html>")
        title := wappalyzer.getTitle(body)
        require.Equal(t, "测试页面", title, "应该正确提取标题")
    })

    t.Run("提取带空格的标题", func(t *testing.T) {
        body := []byte("<html><head><title>  测试页面  </title></head><body></body></html>")
        title := wappalyzer.getTitle(body)
        require.Equal(t, "测试页面", title, "应该去除标题前后空格")
    })

    t.Run("无标题标签", func(t *testing.T) {
        body := []byte("<html><head></head><body></body></html>")
        title := wappalyzer.getTitle(body)
        require.Empty(t, title, "无标题时应返回空字符串")
    })

    t.Run("空标题", func(t *testing.T) {
        body := []byte("<html><head><title></title></head><body></body></html>")
        title := wappalyzer.getTitle(body)
        require.Empty(t, title, "空标题应返回空字符串")
    })

    t.Run("多行标题", func(t *testing.T) {
        body := []byte(`<html><head><title>
            多行
            标题
        </title></head><body></body></html>`)
        title := wappalyzer.getTitle(body)
        require.NotEmpty(t, title, "多行标题应该被提取")
    })
}

// TestUnsafeToString 测试unsafe字符串转换（如果存在）
func TestUnsafeToString(t *testing.T) {
    t.Run("字节数组转字符串", func(t *testing.T) {
        data := []byte("测试字符串")
        // 注意：这里假设unsafeToString函数存在
        // 如果不存在，可以测试标准的string(data)转换
        str := string(data)
        require.Equal(t, "测试字符串", str, "字节数组应该正确转换为字符串")
    })

    t.Run("空字节数组", func(t *testing.T) {
        data := []byte{}
        str := string(data)
        require.Empty(t, str, "空字节数组应该转换为空字符串")
    })

    t.Run("包含特殊字符的字节数组", func(t *testing.T) {
        data := []byte("测试\n\t\r特殊字符")
        str := string(data)
        require.Contains(t, str, "测试", "应该包含中文字符")
        require.Contains(t, str, "\n", "应该包含换行符")
        require.Contains(t, str, "\t", "应该包含制表符")
    })
}

// TestStringMatching 测试字符串匹配相关功能
func TestStringMatching(t *testing.T) {
    wappalyzer, err := New()
    require.NoError(t, err, "创建实例不应失败")

    t.Run("大小写不敏感匹配", func(t *testing.T) {
        // 测试大小写转换
        upper := "NGINX/1.18.0"
        lower := make([]byte, len(upper))
        copy(lower, []byte(upper))
        
        // 手动转换为小写
        for i := range lower {
            if lower[i] >= 'A' && lower[i] <= 'Z' {
                lower[i] += 32
            }
        }
        
        result := string(lower)
        require.Equal(t, "nginx/1.18.0", result, "应该正确转换为小写")
    })

    t.Run("特殊字符处理", func(t *testing.T) {
        text := "Server: nginx/1.18.0 (Ubuntu)"
        require.Contains(t, text, "nginx", "应该包含nginx")
        require.Contains(t, text, "1.18.0", "应该包含版本号")
        require.Contains(t, text, "Ubuntu", "应该包含操作系统信息")
    })
}