// Author: chenjb
// Version: V1.0
// Date: 2025-05-21 17:20:10
// FilePath: /yaml_scan/pkg/fastdialer/ja3/ja3.go
// Description:提供JA3指纹解析和TLS客户端模拟功能的实现
package ja3

import (
	"crypto/sha256"
	"fmt"
	"strconv"
	"strings"
	"yaml_scan/utils/errkit"

	utls "github.com/refraction-networking/utls"
	"golang.org/x/exp/maps"
)

// ErrExtensionNotExist 当请求的扩展不受支持时返回的错误
type ErrExtensionNotExist string

// Error 返回扩展不存在的错误信息
func (e ErrExtensionNotExist) Error() string {
	return fmt.Sprintf("Extension does not exist: %s\n", string(e))
}

// extMap 将扩展值映射到与该编号关联的TLSExtension对象
// 某些值没有放在这里，因为它们必须以特殊方式应用
// 例如，"10"是SupportedCurves扩展，也用于计算JA3签名
// 这些依赖于JA3的值在创建映射实例后应用
var defaultExtensionMap = map[string]utls.TLSExtension{
	// 服务器名称指示扩展
	"0": &utls.SNIExtension{},
	// OCSP状态请求扩展
	"5": &utls.StatusRequestExtension{},
	// 这些扩展会在后面应用
	// "10": &tls.SupportedCurvesExtension{...}  // 支持的椭圆曲线扩展
	// "11": &tls.SupportedPointsExtension{...}  // 支持的椭圆曲线点格式扩展
	// 签名算法扩展
	"13": &utls.SignatureAlgorithmsExtension{
		SupportedSignatureAlgorithms: []utls.SignatureScheme{
			utls.ECDSAWithP256AndSHA256, // ECDSA与P-256和SHA-256
			utls.PSSWithSHA256,          // PSS与SHA-256
			utls.PKCS1WithSHA256,        // PKCS#1与SHA-256
			utls.ECDSAWithP384AndSHA384, // ECDSA与P-384和SHA-384
			utls.PSSWithSHA384,          // PSS与SHA-384
			utls.PKCS1WithSHA384,        // PKCS#1与SHA-384
			utls.PSSWithSHA512,          // PSS与SHA-512
			utls.PKCS1WithSHA512,        // PKCS#1与SHA-512
			utls.PKCS1WithSHA1,          // PKCS#1与SHA-1
		},
	},
	// 应用层协议协商扩展
	"16": &utls.ALPNExtension{
		AlpnProtocols: []string{"h2", "http/1.1"}, // 支持HTTP/2和HTTP/1.1
	},
	// 签名证书时间戳扩展
	"18": &utls.SCTExtension{},
	// 填充扩展，使用BoringSSL风格
	"21": &utls.UtlsPaddingExtension{GetPaddingLen: utls.BoringPaddingStyle},
	// 扩展主密钥扩展
	"23": &utls.ExtendedMasterSecretExtension{},
	// 假记录大小限制扩展
	"28": &utls.FakeRecordSizeLimitExtension{},
	// 会话票据扩展
	"35": &utls.SessionTicketExtension{},
	// 支持的TLS版本扩展
	"43": &utls.SupportedVersionsExtension{Versions: []uint16{
		utls.GREASE_PLACEHOLDER,
		utls.VersionTLS13,
		utls.VersionTLS12,
		utls.VersionTLS11,
		utls.VersionTLS10}},
	// Cookie扩展
	"44": &utls.CookieExtension{},
	// PSK密钥交换模式扩展
	"45": &utls.PSKKeyExchangeModesExtension{
		Modes: []uint8{
			utls.PskModeDHE,
		}},
	// 密钥共享扩展
	"51":    &utls.KeyShareExtension{KeyShares: []utls.KeyShare{}},
	// 下一代协议协商扩展
	"13172": &utls.NPNExtension{},
	 // 重新协商信息扩展
	"65281": &utls.RenegotiationInfoExtension{
		Renegotiation: utls.RenegotiateOnceAsClient,
	},
}

// getExtensionMap  返回默认扩展映射的副本
// @return map map: 
func getExtensionMap() map[string]utls.TLSExtension {
	return maps.Clone(defaultExtensionMap)
}

// ParseWithJa3 解析JA3字符串并返回ClientHelloSpec
// ja3字符串格式: TLS版本,密码套件列表,扩展列表,支持的曲线列表,支持的点格式列表
// @param ja3 string: 符合JA3格式的字符串
// @return *utls.ClientHelloSpec *utls.ClientHelloSpec: 客户端Hello规范，用于TLS指纹模拟
// @return error error:  解析过程中可能发生的错误
func ParseWithJa3(ja3 string) (*utls.ClientHelloSpec, error) {
	ja3tokens := strings.Split(ja3, ",")
	// JA3格式必须有5个部分
	if len(ja3tokens) != 5 {
		return nil, fmt.Errorf("invalid ja3 string: %s", ja3)
	}

	// 解析TLS版本
	vid, err := parseVersion(ja3tokens[0])
	if err != nil {
		return nil, err
	}

	// 解析密码套件列表
	cipherSuites, err := parseCipherSuites(ja3tokens[1])
	if err != nil {
		return nil, err
	}

	// 解析扩展列表
	extensions, err := parseExtensions(ja3tokens[2])
	if err != nil {
		return nil, err
	}

	// 解析支持的曲线列表
	supportedCurves, err := parseSupportedCurves(ja3tokens[3])
	if err != nil {
		return nil, err
	}

	// 解析支持的点格式列表
	supportedPoints, err := parseSupportedPoints(ja3tokens[4])
	if err != nil {
		return nil, err
	}

	// 创建扩展映射并添加特殊处理的扩展
	extMap := getExtensionMap()
	// 添加支持的曲线
	extMap["10"] = &utls.SupportedCurvesExtension{Curves: supportedCurves}
	 // 添加支持的点格式
	extMap["11"] = &utls.SupportedPointsExtension{SupportedPoints: supportedPoints}

	// 创建并返回完整的ClientHelloSpec
	return &utls.ClientHelloSpec{
		TLSVersMin:         vid,
		TLSVersMax:         vid,
		CipherSuites:       cipherSuites,
		CompressionMethods: []byte{0}, // 压缩方法（0表示无压缩）
		Extensions:         extensions,
		GetSessionID:       sha256.Sum256,  // 使用SHA-256生成会话ID
	}, nil
}

// cleanup 清理字符串，去除首尾空格
// @param s string: 
// @return string string: 
func cleanup(s string) string {
	return strings.TrimSpace(s)
}

// parseVersion 解析TLS版本号
// @param version string: 版本号字符串
// @return uint16 uint16: 解析后的TLS版本号
// @return error error: 可能得错误
func parseVersion(version string) (uint16, error) {
	vid64, err := strconv.ParseUint(version, 10, 16)
	if err != nil {
		return 0, err
	}
	return uint16(vid64), nil
}

// parseCipherSuites 解析密码套件列表
// @param cipherToken string: 以-分隔的密码套件ID列表
// @return []uint16 []uint16: 解析后的密码套件ID列表
// @return error error: 可能得错误
func parseCipherSuites(cipherToken string) ([]uint16, error) {
	cipherToken = cleanup(cipherToken)
	if cipherToken == "" {
		return nil, errkit.New("no cipher suites provided")
	}
	// 按-分割密码套件列表
	ciphers := strings.Split(cipherToken, "-")
	var cipherSuites []uint16
	for _, cipher := range ciphers {
		cid, err := strconv.ParseUint(cipher, 10, 16)
		if err != nil {
			return nil, err
		}
		cipherSuites = append(cipherSuites, uint16(cid))
	}
	return cipherSuites, nil
}

// parseExtensions 解析扩展列表
// @param extensionToken string:  以-分隔的扩展ID列表
// @return []utls.TLSExtension []utls.TLSExtension: 解析后的TLS扩展列表
// @return error error: 可能得错误
func parseExtensions(extensionToken string) ([]utls.TLSExtension, error) {
	var extensions []utls.TLSExtension
	extensionToken = cleanup(extensionToken)
	if extensionToken == "" {
		return nil, errkit.New("no extensions provided")
	}
	exts := strings.Split(extensionToken, "-")
	for _, ext := range exts {
		te, ok := defaultExtensionMap[ext]
		if !ok {
			return nil, ErrExtensionNotExist(ext)
		}
		extensions = append(extensions, te)
	}
	return extensions, nil
}

// parseSupportedCurves 解析支持的椭圆曲线列表
// @param supportedCurvesToken string: 以-分隔的曲线ID列表
// @return []utls.CurveID []utls.CurveID:  解析后的曲线ID列表
// @return error error: 
func parseSupportedCurves(supportedCurvesToken string) ([]utls.CurveID, error) {
	var supportedCurves []utls.CurveID
	supportedCurvesToken = cleanup(supportedCurvesToken)
	if supportedCurvesToken == "" {
		return supportedCurves, nil
	}
	curves := strings.Split(supportedCurvesToken, "-")
	for _, c := range curves {
		cid, err := strconv.ParseUint(c, 10, 16)
		if err != nil {
			return nil, err
		}
		supportedCurves = append(supportedCurves, utls.CurveID(cid))
	}
	return supportedCurves, nil
}

// parseSupportedPoints 解析支持的点格式列表
// @param supportedPointsToken string:  以-分隔的点格式列表
// @return []byte []byte: 解析后的点格式列表
// @return error error: 
func parseSupportedPoints(supportedPointsToken string) ([]byte, error) {
	var supportedPoints []byte
	supportedPointsToken = cleanup(supportedPointsToken)
	if supportedPointsToken == "" {
		return supportedPoints, nil
	}
	points := strings.Split(supportedPointsToken, "-")
	for _, p := range points {
		pid, err := strconv.ParseUint(p, 10, 8)
		if err != nil {
			return nil, err
		}
		supportedPoints = append(supportedPoints, byte(pid))
	}
	return supportedPoints, nil
}

// ParseWithRaw 
// @param rawClientHello []byte: 
// @return *utls.ClientHelloSpec *utls.ClientHelloSpec: 
// @return error error: 
func ParseWithRaw(rawClientHello []byte) (*utls.ClientHelloSpec, error) {
	fingerprinter := &utls.Fingerprinter{}
	return fingerprinter.FingerprintClientHello(rawClientHello)
}
