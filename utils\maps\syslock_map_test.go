//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-04 17:11:00
//FilePath: /yaml_scan/utils/maps/syslock_map_test.go
//Description:

package mapsutil

import (
	"testing"

	"github.com/stretchr/testify/require"
)

// TestSyncLockMap 测试 SyncLockMap 的基本功能。
func TestSyncLockMap(t *testing.T) {
	// 创建一个新的 SyncLockMap 实例
	syncMap := NewSyncLockMap[string, int]()

	// 测试设置和获取值
	syncMap.Set("one", 1)
	syncMap.Set("two", 2)

	// 测试获取存在的键
	if value, ok := syncMap.Get("one"); !ok || value != 1 {
		t.<PERSON>rrorf("Expected value 1 for key 'one', got %v (ok: %v)", value, ok)
	}

	if value, ok := syncMap.Get("two"); !ok || value != 2 {
		t.<PERSON><PERSON><PERSON>("Expected value 2 for key 'two', got %v (ok: %v)", value, ok)
	}

	// 测试获取不存在的键
	if _, ok := syncMap.Get("three"); ok {
		t.Error("Expected key 'three' to not exist")
	}

	// 测试删除键
	syncMap.Delete("one")

	// 测试删除后的键
	if _, ok := syncMap.Get("one"); ok {
		t.Error("Expected key 'one' to be deleted")
	}
	// 测试并发
	for i := 0; i < 100; i++ {
		value, ok := syncMap.Get("two")
		require.True(t, ok, "failed to get item from map")
		require.Equal(t, 2, value, "failed to get item from map")
	}
}
