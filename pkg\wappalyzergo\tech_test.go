//
// Author: chenjb
// Version: V1.0
// Date: 2025-07-18 11:10:30
// FilePath: /yaml_scan/pkg/wappalyzergo/tech_test.go
// Description: tech.go 的单元测试文件

package wappalyzergo

import (
    "os"
    "testing"

    "github.com/stretchr/testify/require"
)

// TestNew 测试创建新的技术检测实例
func TestNew(t *testing.T) {
    t.Run("成功创建实例", func(t *testing.T) {
        wappalyzer, err := New()
        require.NoError(t, err, "创建Wappalyze实例不应返回错误")
        require.NotNil(t, wappalyzer, "Wappalyze实例不应为nil")
        require.NotNil(t, wappalyzer.fingerprints, "编译指纹不应为nil")
        require.NotNil(t, wappalyzer.fingerprints.Apps, "应用指纹映射不应为nil")
        require.NotNil(t, wappalyzer.original, "原始指纹不应为nil")
        require.NotEmpty(t, wappalyzer.fingerprints.Apps, "应用指纹映射不应为空")
    })

    t.Run("验证内嵌指纹数据加载", func(t *testing.T) {
        wappalyzer, err := New()
        require.NoError(t, err, "创建实例不应失败")
        
        // 验证原始指纹和编译指纹数量一致
        require.Equal(t, len(wappalyzer.original.Apps), len(wappalyzer.fingerprints.Apps), 
            "原始指纹和编译指纹数量应该一致")
    })
}

// TestNewFromFile 测试从文件创建技术检测实例
func TestNewFromFile(t *testing.T) {
    t.Run("文件不存在时返回错误", func(t *testing.T) {
        wappalyzer, err := NewFromFile("nonexistent.json", false, false)
        require.Error(t, err, "不存在的文件应返回错误")
        require.Nil(t, wappalyzer, "错误时实例应为nil")
    })

    t.Run("仅加载文件指纹", func(t *testing.T) {
        // 创建临时的JSON文件
        tempFile := "test_apps.json"
        content := `{
            "technologies": {
                "TestApp": {
                    "description": "测试应用",
                    "website": "https://test.com",
                    "cats": [1],
                    "headers": {
                        "X-Test": "test"
                    }
                }
            }
        }`
        err := createTempJSONFile(tempFile, content)
        require.NoError(t, err, "创建临时文件不应失败")
        defer removeTempFile(tempFile)

        wappalyzer, err := NewFromFile(tempFile, false, false)
        require.NoError(t, err, "仅加载文件指纹不应返回错误")
        require.NotNil(t, wappalyzer, "实例不应为nil")
        require.Contains(t, wappalyzer.original.Apps, "TestApp", "应该包含测试应用")
    })

    t.Run("加载内嵌指纹并合并文件指纹", func(t *testing.T) {
        tempFile := "test_merge.json"
        content := `{
            "technologies": {
                "NewApp": {
                    "description": "新应用",
                    "website": "https://new.com",
                    "cats": [2]
                }
            }
        }`
        err := createTempJSONFile(tempFile, content)
        require.NoError(t, err, "创建临时文件不应失败")
        defer removeTempFile(tempFile)

        wappalyzer, err := NewFromFile(tempFile, true, false)
        require.NoError(t, err, "合并指纹不应返回错误")
        require.NotNil(t, wappalyzer, "实例不应为nil")
        require.Contains(t, wappalyzer.original.Apps, "NewApp", "应该包含新应用")
    })

    t.Run("覆盖模式测试", func(t *testing.T) {
        tempFile := "test_supersede.json"
        content := `{
            "technologies": {
                "Nginx": {
                    "description": "覆盖的Nginx描述",
                    "website": "https://override.com",
                    "cats": [99]
                }
            }
        }`
        err := createTempJSONFile(tempFile, content)
        require.NoError(t, err, "创建临时文件不应失败")
        defer removeTempFile(tempFile)

        wappalyzer, err := NewFromFile(tempFile, true, true)
        require.NoError(t, err, "覆盖模式不应返回错误")
        require.NotNil(t, wappalyzer, "实例不应为nil")
    })

    t.Run("空文件处理", func(t *testing.T) {
        tempFile := "test_empty.json"
        content := `{"technologies": {}}`
        err := createTempJSONFile(tempFile, content)
        require.NoError(t, err, "创建临时文件不应失败")
        defer removeTempFile(tempFile)

        wappalyzer, err := NewFromFile(tempFile, false, false)
        require.Error(t, err, "空指纹文件应返回错误")
        require.Nil(t, wappalyzer, "空文件时实例应为nil")
    })

    t.Run("无效JSON文件", func(t *testing.T) {
        tempFile := "test_invalid.json"
        content := `{invalid json}`
        err := createTempJSONFile(tempFile, content)
        require.NoError(t, err, "创建临时文件不应失败")
        defer removeTempFile(tempFile)

        wappalyzer, err := NewFromFile(tempFile, false, false)
        require.Error(t, err, "无效JSON应返回错误")
        require.Nil(t, wappalyzer, "无效JSON时实例应为nil")
    })
}

// TestGetFingerprints 测试获取原始指纹数据
func TestGetFingerprints(t *testing.T) {
    wappalyzer, err := New()
    require.NoError(t, err, "创建实例不应失败")

    fingerprints := wappalyzer.GetFingerprints()
    require.NotNil(t, fingerprints, "原始指纹不应为nil")
    require.NotNil(t, fingerprints.Apps, "应用映射不应为nil")
    require.NotEmpty(t, fingerprints.Apps, "应用映射不应为空")
}

// TestGetCompiledFingerprints 测试获取编译后的指纹数据
func TestGetCompiledFingerprints(t *testing.T) {
    wappalyzer, err := New()
    require.NoError(t, err, "创建实例不应失败")

    compiled := wappalyzer.GetCompiledFingerprints()
    require.NotNil(t, compiled, "编译指纹不应为nil")
    require.NotNil(t, compiled.Apps, "应用映射不应为nil")
    require.NotEmpty(t, compiled.Apps, "编译指纹映射不应为空")
}

// TestFingerprint 测试基础指纹识别功能
func TestFingerprint(t *testing.T) {
    wappalyzer, err := New()
    require.NoError(t, err, "创建实例不应失败")

    t.Run("识别HTTP头部技术", func(t *testing.T) {
        headers := map[string][]string{
            "Server":       {"nginx/1.18.0"},
            "X-Powered-By": {"PHP/7.4.0"},
        }
        body := []byte("<html><head><title>Test</title></head><body></body></html>")

        technologies := wappalyzer.Fingerprint(headers, body)
        require.NotNil(t, technologies, "结果不应为nil")
        require.IsType(t, map[string]struct{}{}, technologies, "应该返回正确的类型")
    })

    t.Run("识别Cookie技术", func(t *testing.T) {
        headers := map[string][]string{
            "Set-Cookie": {"PHPSESSID=abc123; path=/", "laravel_session=xyz789"},
        }
        body := []byte("<html></html>")

        technologies := wappalyzer.Fingerprint(headers, body)
        require.NotNil(t, technologies, "结果不应为nil")
    })

    t.Run("识别HTML内容技术", func(t *testing.T) {
        headers := map[string][]string{}
        body := []byte(`
            <html>
            <head>
                <meta name="generator" content="WordPress 5.8">
                <script src="jquery-3.6.0.min.js"></script>
            </head>
            <body></body>
            </html>
        `)

        technologies := wappalyzer.Fingerprint(headers, body)
        require.NotNil(t, technologies, "结果不应为nil")
    })

    t.Run("空输入处理", func(t *testing.T) {
        headers := map[string][]string{}
        body := []byte("")

        technologies := wappalyzer.Fingerprint(headers, body)
        require.NotNil(t, technologies, "结果不应为nil")
    })

    t.Run("nil输入处理", func(t *testing.T) {
        technologies := wappalyzer.Fingerprint(nil, nil)
        require.NotNil(t, technologies, "nil输入结果不应为nil")
    })

    t.Run("大小写不敏感测试", func(t *testing.T) {
        headers := map[string][]string{
            "SERVER":       {"NGINX/1.18.0"},
            "Content-Type": {"TEXT/HTML"},
        }
        body := []byte("<HTML><HEAD><TITLE>TEST</TITLE></HEAD></HTML>")

        technologies := wappalyzer.Fingerprint(headers, body)
        require.NotNil(t, technologies, "大小写不敏感结果不应为nil")
    })
}

// TestFingerprintWithTitle 测试带标题的指纹识别
func TestFingerprintWithTitle(t *testing.T) {
    wappalyzer, err := New()
    require.NoError(t, err, "创建实例不应失败")

    t.Run("HTML页面提取标题", func(t *testing.T) {
        headers := map[string][]string{
            "Content-Type": {"text/html; charset=utf-8"},
        }
        body := []byte("<html><head><title>测试页面</title></head><body></body></html>")

        technologies, title := wappalyzer.FingerprintWithTitle(headers, body)
        require.NotNil(t, technologies, "技术列表不应为nil")
        require.Equal(t, "测试页面", title, "应该正确提取标题")
    })

    t.Run("非HTML内容返回空标题", func(t *testing.T) {
        headers := map[string][]string{
            "Content-Type": {"application/json"},
        }
        body := []byte(`{"key": "value"}`)

        technologies, title := wappalyzer.FingerprintWithTitle(headers, body)
        require.NotNil(t, technologies, "技术列表不应为nil")
        require.Empty(t, title, "非HTML内容应返回空标题")
    })

    t.Run("无Content-Type头部", func(t *testing.T) {
        headers := map[string][]string{}
        body := []byte("<html><head><title>无类型标题</title></head></html>")

        technologies, title := wappalyzer.FingerprintWithTitle(headers, body)
        require.NotNil(t, technologies, "技术列表不应为nil")
        require.Empty(t, title, "无Content-Type应返回空标题")
    })

    t.Run("复杂HTML页面标题提取", func(t *testing.T) {
        headers := map[string][]string{
            "Content-Type": {"text/html"},
        }
        body := []byte(`
            <html>
            <head>
                <meta charset="utf-8">
                <title>复杂页面 - 包含特殊字符 & 符号</title>
                <script src="app.js"></script>
            </head>
            <body>
                <h1>内容</h1>
            </body>
            </html>
        `)

        technologies, title := wappalyzer.FingerprintWithTitle(headers, body)
        require.NotNil(t, technologies, "技术列表不应为nil")
        require.Equal(t, "复杂页面 - 包含特殊字符 & 符号", title, "应该正确提取复杂标题")
    })
}

// TestFingerprintWithInfo 测试带详细信息的指纹识别
func TestFingerprintWithInfo(t *testing.T) {
    wappalyzer, err := New()
    require.NoError(t, err, "创建实例不应失败")

    t.Run("基本信息提取", func(t *testing.T) {
        headers := map[string][]string{
            "Server": {"nginx/1.18.0"},
        }
        body := []byte("<html></html>")

        result := wappalyzer.FingerprintWithInfo(headers, body)
        require.NotNil(t, result, "结果不应为nil")
        require.IsType(t, map[string]AppInfo{}, result, "应该返回正确的类型")
        
        // 检查返回的信息结构
        for appName, info := range result {
            require.NotEmpty(t, appName, "应用名不应为空")
            require.IsType(t, AppInfo{}, info, "应该返回AppInfo类型")
        }
    })

    t.Run("带版本信息的技术", func(t *testing.T) {
        headers := map[string][]string{
            "X-Powered-By": {"PHP/7.4.0"},
        }
        body := []byte("")

        result := wappalyzer.FingerprintWithInfo(headers, body)
        require.NotNil(t, result, "结果不应为nil")
        
        // 检查是否包含版本信息
        for appName, info := range result {
            if appName != "" {
                require.IsType(t, AppInfo{}, info, "应该返回AppInfo类型")
                // 验证AppInfo字段
                require.IsType(t, "", info.Description, "Description应该是字符串")
                require.IsType(t, "", info.Website, "Website应该是字符串")
                require.IsType(t, "", info.Icon, "Icon应该是字符串")
                require.IsType(t, "", info.CPE, "CPE应该是字符串")
                require.IsType(t, []string{}, info.Categories, "Categories应该是字符串切片")
            }
        }
    })

    t.Run("空输入处理", func(t *testing.T) {
        result := wappalyzer.FingerprintWithInfo(nil, nil)
        require.NotNil(t, result, "空输入结果不应为nil")
        require.IsType(t, map[string]AppInfo{}, result, "应该返回正确的类型")
    })
}

// TestAppInfoFromFingerprint 测试从指纹创建应用信息
func TestAppInfoFromFingerprint(t *testing.T) {
    t.Run("完整指纹信息", func(t *testing.T) {
        fingerprint := &CompiledFingerprint{
            description: "测试应用",
            website:     "https://example.com",
            icon:        "test.png",
            cpe:         "cpe:2.3:a:test:app:*:*:*:*:*:*:*:*",
            cats:        []int{1, 2},
        }

        appInfo := AppInfoFromFingerprint(fingerprint)
        require.Equal(t, "测试应用", appInfo.Description, "描述应该匹配")
        require.Equal(t, "https://example.com", appInfo.Website, "网站应该匹配")
        require.Equal(t, "test.png", appInfo.Icon, "图标应该匹配")
        require.Equal(t, "cpe:2.3:a:test:app:*:*:*:*:*:*:*:*", appInfo.CPE, "CPE应该匹配")
        require.NotNil(t, appInfo.Categories, "分类不应为nil")
    })

    t.Run("空指纹信息", func(t *testing.T) {
        fingerprint := &CompiledFingerprint{}

        appInfo := AppInfoFromFingerprint(fingerprint)
        require.Empty(t, appInfo.Description, "空描述应该为空")
        require.Empty(t, appInfo.Website, "空网站应该为空")
        require.Empty(t, appInfo.Icon, "空图标应该为空")
        require.Empty(t, appInfo.CPE, "空CPE应该为空")
        require.NotNil(t, appInfo.Categories, "分类不应为nil")
        require.Empty(t, appInfo.Categories, "空分类应该为空切片")
    })

    t.Run("部分指纹信息", func(t *testing.T) {
        fingerprint := &CompiledFingerprint{
            description: "部分应用",
            cats:        []int{5},
        }

        appInfo := AppInfoFromFingerprint(fingerprint)
        require.Equal(t, "部分应用", appInfo.Description, "描述应该匹配")
        require.Empty(t, appInfo.Website, "未设置的网站应该为空")
        require.NotNil(t, appInfo.Categories, "分类不应为nil")
    })
}

// TestFingerprintWithCats 测试带分类信息的指纹识别
func TestFingerprintWithCats(t *testing.T) {
    wappalyzer, err := New()
    require.NoError(t, err, "创建实例不应失败")

    t.Run("基本分类信息提取", func(t *testing.T) {
        headers := map[string][]string{
            "Server": {"nginx/1.18.0"},
        }
        body := []byte("<html></html>")

        result := wappalyzer.FingerprintWithCats(headers, body)
        require.NotNil(t, result, "结果不应为nil")
        require.IsType(t, map[string]CatsInfo{}, result, "应该返回正确的类型")
        
        // 检查返回的分类信息结构
        for appName, catsInfo := range result {
            require.NotEmpty(t, appName, "应用名不应为空")
            require.IsType(t, CatsInfo{}, catsInfo, "应该返回CatsInfo类型")
            require.NotNil(t, catsInfo.Cats, "分类列表不应为nil")
            require.IsType(t, []int{}, catsInfo.Cats, "分类应该是整数切片")
        }
    })

    t.Run("多技术分类信息", func(t *testing.T) {
        headers := map[string][]string{
            "Server":       {"nginx/1.18.0"},
            "X-Powered-By": {"PHP/7.4.0"},
        }
        body := []byte(`<html><head><meta name="generator" content="WordPress"></head></html>`)

        result := wappalyzer.FingerprintWithCats(headers, body)
        require.NotNil(t, result, "结果不应为nil")
        
        // 验证每个技术都有分类信息
        for appName, catsInfo := range result {
            require.NotEmpty(t, appName, "应用名不应为空")
            require.IsType(t, CatsInfo{}, catsInfo, "应该返回CatsInfo类型")
            require.NotNil(t, catsInfo.Cats, "分类列表不应为nil")
            
            // 验证分类ID都是有效的正整数
            for _, catID := range catsInfo.Cats {
                require.Greater(t, catID, 0, "分类ID应该大于0")
            }
        }
    })

    t.Run("空输入分类处理", func(t *testing.T) {
        result := wappalyzer.FingerprintWithCats(nil, nil)
        require.NotNil(t, result, "空输入结果不应为nil")
        require.IsType(t, map[string]CatsInfo{}, result, "应该返回正确的类型")
    })

    t.Run("无匹配技术的分类", func(t *testing.T) {
        headers := map[string][]string{
            "Content-Type": {"text/plain"},
        }
        body := []byte("纯文本内容")

        result := wappalyzer.FingerprintWithCats(headers, body)
        require.NotNil(t, result, "结果不应为nil")
        require.IsType(t, map[string]CatsInfo{}, result, "应该返回正确的类型")
    })
}

// TestUniqueFingerprints 测试唯一指纹管理器
func TestUniqueFingerprints(t *testing.T) {
    t.Run("创建新的唯一指纹管理器", func(t *testing.T) {
        uf := NewUniqueFingerprints()
        require.NotNil(t, uf, "唯一指纹管理器不应为nil")
        
        values := uf.GetValues()
        require.NotNil(t, values, "值集合不应为nil")
        require.Empty(t, values, "初始值集合应为空")
    })

    t.Run("设置和获取指纹", func(t *testing.T) {
        uf := NewUniqueFingerprints()
        
        uf.SetIfNotExists("nginx", "1.18.0", 95)
        values := uf.GetValues()
        
        require.Len(t, values, 1, "应该有一个指纹")
        require.Contains(t, values, "nginx:1.18.0", "应该包含nginx指纹")
    })

    t.Run("置信度累加测试", func(t *testing.T) {
        uf := NewUniqueFingerprints()
        
        uf.SetIfNotExists("nginx", "1.18.0", 50)
        uf.SetIfNotExists("nginx", "1.18.0", 40) // 相同技术，置信度应该累加
        
        values := uf.GetValues()
        require.Len(t, values, 1, "重复技术应该只有一个")
        require.Contains(t, values, "nginx:1.18.0", "应该包含nginx指纹")
    })

    t.Run("置信度上限测试", func(t *testing.T) {
        uf := NewUniqueFingerprints()
        
        uf.SetIfNotExists("nginx", "1.18.0", 80)
        uf.SetIfNotExists("nginx", "1.18.0", 50) // 累加后超过100
        
        values := uf.GetValues()
        require.Len(t, values, 1, "应该有一个指纹")
    })

    t.Run("版本更新测试", func(t *testing.T) {
        uf := NewUniqueFingerprints()
        
        uf.SetIfNotExists("nginx", "", 50)      // 先设置无版本
        uf.SetIfNotExists("nginx", "1.18.0", 40) // 后设置有版本
        
        values := uf.GetValues()
        require.Len(t, values, 1, "应该有一个指纹")
        require.Contains(t, values, "nginx:1.18.0", "应该更新为有版本的指纹")
    })
}

// TestMatchPartResult 测试匹配结果结构体
func TestMatchPartResult(t *testing.T) {
    t.Run("创建匹配结果", func(t *testing.T) {
        result := matchPartResult{
            application: "nginx",
            version:     "1.18.0",
            confidence:  95,
        }

        require.Equal(t, "nginx", result.application, "应用名应该匹配")
        require.Equal(t, "1.18.0", result.version, "版本应该匹配")
        require.Equal(t, 95, result.confidence, "置信度应该匹配")
    })

    t.Run("零值匹配结果", func(t *testing.T) {
        result := matchPartResult{}

        require.Empty(t, result.application, "默认应用名应该为空")
        require.Empty(t, result.version, "默认版本应该为空")
        require.Zero(t, result.confidence, "默认置信度应该为0")
    })
}

// TestVersionSeparator 测试版本分隔符
func TestVersionSeparator(t *testing.T) {
    t.Run("版本分隔符常量", func(t *testing.T) {
        require.Equal(t, ":", versionSeparator, "版本分隔符应该是冒号")
    })

    t.Run("FormatAppVersion函数测试", func(t *testing.T) {
        // 测试有版本的情况
        formatted := FormatAppVersion("nginx", "1.18.0")
        require.Equal(t, "nginx:1.18.0", formatted, "有版本时应该包含分隔符")
        
        // 测试无版本的情况
        formatted = FormatAppVersion("nginx", "")
        require.Equal(t, "nginx", formatted, "无版本时不应该包含分隔符")
    })
}

// 辅助函数：创建临时JSON文件
func createTempJSONFile(filename, content string) error {
    return os.WriteFile(filename, []byte(content), 0644)
}

// 辅助函数：删除临时文件
func removeTempFile(filename string) {
    os.Remove(filename)
}
