//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-20 15:06:20
//FilePath: /yaml_scan/pkg/goflags/runtime_map.go
//Description:

package goflags

import (
	"fmt"
	"strings"

	stringsutil "yaml_scan/utils/strings"
)

const (
	// 键值对的分隔符
	kvSep = "="
)

// RuntimeMap 是一个仅在运行时使用的接口映射
type RuntimeMap struct {
	kv map[string]interface{}
}

// Set: 将一个键值对插入到映射中。格式为: key=value
//
//	@receiver runtimeMap *RuntimeMap:
//	@param value string: 包含键值对的字符串，格式为 "key=value"。
//	@return error error: 如果插入过程中出现错误，返回错误信息。
//
// 注意：多次插入相同的键会覆盖之前的值，空字符串是合法的值。
func (runtimeMap *RuntimeMap) Set(value string) error {
	if runtimeMap.kv == nil {
		runtimeMap.kv = make(map[string]interface{})
	}
	var k, v string
	// 查找分隔符 "=" 的位置，将字符串分割为键和值。
	if idxSep := strings.Index(value, kvSep); idxSep > 0 {
		k = value[:idxSep]
		v = value[idxSep+1:]
	}
	if k != "" {
		runtimeMap.kv[k] = v
	}
	return nil
}

// String: 返回 RuntimeMap 的字符串表示
//  @receiver runtimeMap RuntimeMap: 
//  @return string string: 以字符串形式返回映射内容，格式为 {"key"="value", ...}。
func (runtimeMap RuntimeMap) String() string {
	defaultBuilder := &strings.Builder{}
	defaultBuilder.WriteString("{")

	var items string
	for k, v := range runtimeMap.kv {
		items += fmt.Sprintf("\"%s\"=\"%s\"%s", k, v, kvSep)
	}
	// 移除末尾多余的分隔符。
	defaultBuilder.WriteString(stringsutil.TrimSuffixAny(items, ",", "="))
	defaultBuilder.WriteString("}")
	return defaultBuilder.String()
}
