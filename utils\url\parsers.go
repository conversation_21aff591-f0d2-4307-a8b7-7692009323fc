//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-11 17:49:07
//FilePath: /yaml_scan/utils/url/parsers.go
//Description: url解析相关

package urlutil

import (
	"errors"
	"fmt"
	"net/url"
	"strings"

	stringsutil "yaml_scan/utils/strings"
)

// relativePathParser: 用于解析相对路径，避免代码重复
//
//	@param u *URL:
//	@return *URL *URL:
//	@return error error:
func relativePathParser(u *URL) (*URL, error) {
	// 获取查询参数
	u.fetchParams()
	// 尝试解析原始 URL
	urlparse, parseErr := url.Parse(u.Original)
	if parseErr != nil {
		if !u.Unsafe {
			// 如果不是不安全的 URL，则返回解析错误
			return nil, errors.New("failed to parse input url")
		} else {
			// 如果是安全的，则不依赖 net/url.Parse，直接将原始路径赋值给 u.Path
			u.Path = u.Original
		}
	}
	// 如果成功解析 URL
	if urlparse != nil {
		// 清空主机部分
		urlparse.Host = ""
		// 复制解析后的 URL 数据到 u.URL
		copy(u.URL, urlparse)
	}
	// 解析相对路径
	u.parseUnsafeRelativePath()
	// 检查是否有主机部分
	if u.Host != "" {
		return nil, fmt.Errorf("expected relative path but got absolute path with host=%v, input=%v", u.Host, u.Original)
	}
	return u, nil
}

// ParseRawRelativePath: 解析原始相对路径
//
//	@param inputURL string:
//	@param unsafe bool:
//	@return *URL *URL:
//	@return error error:
func ParseRawRelativePath(inputURL string, unsafe bool) (*URL, error) {
	u := &URL{
		URL:                &url.URL{},
		Original:           inputURL,
		Unsafe:             unsafe,
		IsRelative:         true, // 标记为相对路径
		disableAutoCorrect: true, // 禁用自动更正
	}
	return relativePathParser(u)
}

const (
	HTTP  = "http"
	HTTPS = "https"

	// Deny all protocols
	// Allow:
	// websocket + websocket over ssl
	WEBSOCKET     = "ws"
	WEBSOCKET_SSL = "wss"
	FTP           = "ftp"

	SchemeSeparator  = "://"
	DefaultHTTPPort  = "80"
	DefaultHTTPSPort = "443"
)

// ParseRelativePath: 解析并返回相对路径。
// 当输入已知为相对路径时应优先使用此函数，
// 这可以减少与绝对路径相关的任何规范化和自动更正，
// 如果输入是绝对路径则返回错误。
//
//	@param inputURL string:  输入的 URL 字符串，应该是相对路径。
//	@param unsafe bool: 是否允许不安全的 URL（例如包含无效字符）。
//	@return *URL *URL:  返回解析后的 *URL
//	@return error error:可能的错误。如果输入是绝对路径，则返回错误
func ParseRelativePath(inputURL string, unsafe bool) (*URL, error) {
	u := &URL{
		URL:        &url.URL{},
		Original:   inputURL,
		Unsafe:     unsafe,
		IsRelative: true,
	}
	return relativePathParser(u)
}

// parseUnsafeFullURL: 解析无效（不安全）URL（例如 https://scanme.sh/%invalid）。
// 由于这些 URL 不符合 RFC 标准，url.Parse 会失败，因此需要手动处理。

// @param urlx string: 待解析的 URL 字符串，可能包含无效字符。
// @return *url.URL *url.URL: 返回解析后的 *url.URL 结构体，如果解析失败则返回 nil。
func parseUnsafeFullURL(urlx string) *url.URL {

	// 临时替换 `//` 协议分隔符以避免冲突
	temp := strings.Replace(urlx, "//", "", 1)
	// 查找路径分隔符 `/` 的索引
	index := strings.IndexRune(temp, '/')
	if index == -1 {
		// 如果没有找到路径分隔符，返回 nil
		return nil
	}
	// 提取路径部分
	urlPath := temp[index:]
	// 提取主机部分
	urlHost := strings.TrimSuffix(urlx, urlPath)
	// 尝试解析主机部分
	parseURL, parseErr := url.Parse(urlHost)
	if parseErr != nil {
		// 如果解析失败，返回 nil
		return nil
	}
	// 尝试解析路径部分
	if relpath, err := ParseRelativePath(urlPath, true); err == nil {
		// 如果路径解析成功，将路径赋值给解析后的 URL
		parseURL.Path = relpath.Path
		return parseURL
	}
	return nil
}

// absoluteURLParser: 是通用的绝对 URL 解析逻辑，用于避免代码重复。
// 该函数处理绝对 URL 的解析，包括验证和提取主机、路径等信息。
//
//	@param u *URL: 待解析的 *URL 结构体，包含原始 URL 字符串和其他相关信息。
//	@return *URL *URL: 返回解析后的 *URL 结构体
//	@return error error: 可能的错误。如果输入无效或解析失败，则返回错误。
func absoluteURLParser(u *URL) (*URL, error) {
	// 获取查询参数
	u.fetchParams()
	// 检查输入是否为空
	if u.Original == "" {
		return nil, errors.New("failed to parse url got empty input")
	}

	// 检查是否为相对路径
	//scanme.sh是有效的（因为所有浏览器都接受这个<script src="//ajax.googleapis.com/ajax/xx">）
	if strings.HasPrefix(u.Original, "/") && !strings.HasPrefix(u.Original, "//") {
		// 标记为相对路径
		u.IsRelative = true
		u.Path = u.Original
		return u, nil
	}
	// 定义允许的协议
	allowedSchemes := []string{
		HTTP + SchemeSeparator,
		HTTPS + SchemeSeparator,
		WEBSOCKET + SchemeSeparator,
		WEBSOCKET_SSL + SchemeSeparator,
		FTP + SchemeSeparator,
		"//",
	}
	// 检查输入是否包含协议分隔符或以 `//` 开头
	if strings.Contains(u.Original, SchemeSeparator) || strings.HasPrefix(u.Original, "//") {
		// 检查协议是否有效
		if !strings.HasPrefix(u.Original, "//") && !stringsutil.HasPrefixAny(u.Original, allowedSchemes...) {
			return nil, fmt.Errorf("failed to parse url got invalid scheme input=%v", u.Original)
		}
		// 标记为绝对路径
		u.IsRelative = false
		// 尝试解析 URL
		urlparse, parseErr := url.Parse(u.Original)
		if parseErr != nil {
			// 如果解析失败且允许不安全的 URL，尝试使用不安全的解析方法
			if u.Unsafe {
				urlparse = parseUnsafeFullURL(u.Original)
				if urlparse != nil {
					parseErr = nil
				}
			}
			if parseErr != nil {
				return nil, errors.New("failed to parse url")
			}
		}
		// 复制解析后的 URL
		copy(u.URL, urlparse)
	} else {

		// 处理没有协议的情况
		parsed, err := url.Parse(HTTPS + SchemeSeparator + u.Original)
		if err != nil {
			if !strings.Contains(err.Error(), "invalid URL escape") {
				// 如果解析失败且不是无效 URL 转义错误，标记为相对路径
				u.IsRelative = true
				return u, nil
			}
		} else {
			// 成功解析绝对 URL
			parsed.Scheme = "" // 移除新添加的协议
			copy(u.URL, parsed)
			return u, nil
		}

		// 查找路径分隔符 `/` 的索引
		pathIndex := strings.IndexRune(u.Original, '/')
		if pathIndex == -1 {
			// 如果没有找到路径分隔符，尝试解析为 HTTPS URL
			urlparse, parseErr := url.Parse(HTTPS + SchemeSeparator + u.Original)
			if parseErr != nil {
				// 标记为相对路径
				u.IsRelative = true
			} else {
				urlparse.Scheme = "" // 移除新添加的协议
				copy(u.URL, urlparse)
			}
			return u, nil
		}
		// 尝试解析主机部分
		urlparse, parseErr := url.Parse(HTTPS + SchemeSeparator + u.Original[:pathIndex])
		if parseErr != nil {
			// 标记为相对路径
			u.IsRelative = true
		} else {
			urlparse.Path = u.Original[pathIndex:]
			urlparse.Scheme = "" // 移除新添加的协议
			copy(u.URL, urlparse)
		}
	}
	return u, nil
}

// ParseAbsoluteURL: 解析并返回绝对 URL。
// 当输入已知为绝对 URL 时应优先使用此函数，
// 这可以减少与相对路径相关的任何规范化和自动更正，
// 如果输入是相对路径则返回错误。
//
//	@param inputURL string: 输入的 URL 字符串，应该是绝对路径。
//	@param unsafe bool: 指示是否允许不安全的 URL（例如包含无效字符）。
//	@return *URL *URL: 返回解析后的 *URL 结构体
//	@return error error: 可能的错误。如果输入是相对路径，则返回错误。
func ParseAbsoluteURL(inputURL string, unsafe bool) (*URL, error) {
	u := &URL{
		URL:      &url.URL{},
		Original: inputURL,
		Unsafe:   unsafe,
		Params:   NewOrderedParams(),
	}
	var err error
	// 调用绝对url解析器
	u, err = absoluteURLParser(u)
	if err != nil {
		return nil, err
	}
	// 检查是否为相对路径
	if u.IsRelative {
		return nil, fmt.Errorf("expected absolute url but got relative url input=%v,path=%v", inputURL, u.Path)
	}
	// 检查主机部分是否为空
	if u.URL.Host == "" {
		return nil, fmt.Errorf("something went wrong got empty host for absolute url=%v", inputURL)
	}
	return u, nil
}

// Parse: 解析输入的 URL（可以是相对 URL 或绝对 URL）。
//
//	@param inputURL string: 要解析的 URL 字符串。
//	@return *URL *URL:  解析后的 URL 结构体
//	@return error error: 解析过程中可能返回的错误。
func Parse(inputURL string) (*URL, error) {
	return ParseURL(inputURL, false)
}

// ParseURL: 解析输入的 URL（可以是相对 URL 或绝对 URL）。
//
//	@param inputURL string: 要解析的 URL 字符串。
//	@param unsafe bool: 是否允许解析不安全的 URL（如包含潜在危险的路径）。
//	@return *URL *URL: 解析后的 URL 结构体。
//	@return error error: 解析过程中可能返回的错误。
func ParseURL(inputURL string, unsafe bool) (*URL, error) {
	u := &URL{
		URL:      &url.URL{},
		Original: inputURL,
		Unsafe:   unsafe,
		Params:   NewOrderedParams(),
	}

	var err error
	// 解析绝对url路径
	u, err = absoluteURLParser(u)
	if err != nil {
		return nil, err
	}
	// 解析相对url路径
	if u.IsRelative {
		return ParseRelativePath(inputURL, unsafe)
	}

	// 逻辑错误：URL 不是相对的，但 Host 为空
	if u.Host == "" {
		return nil, fmt.Errorf("Urlutil failed to parse url %v got empty host when url is not relative", inputURL)
	}

	// # Normalization 1: 如果 u.Host 的值看起来不像常见的域名
	// （例如没有点号或冒号），则认为它是一个相对路径被错误地解析为 Host。
	// 这是由于 url.Parse 的模糊性导致的。
	// 例如，解析类似 `scanme.sh/my/path` 的 URL 时，url.Parse() 会将 `scanme.sh/my/path` 作为 Path，而 Host 为空。
	// 为了避免这种情况，我们始终在缺少模式（如 `https://`）时为输入 URL 添加一个虚拟模式，以避免解析错误。
	// 如果 Host 不符合预期，则认为 URL 是相对的，并重新设置 Path 和 Host。
	if !strings.Contains(u.Host, ".") && !strings.Contains(u.Host, ":") && u.Host != "localhost" {
		// 当前逻辑认为没有点号的 Host 不是有效的域名，因此将其视为相对路径。
		u.IsRelative = true
		u.Path = inputURL
		u.Host = ""
	}

	return u, nil
}
