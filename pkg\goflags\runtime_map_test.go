//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-20 15:43:08
//FilePath: /yaml_scan/pkg/goflags/runtime_map_test.go
//Description:

package goflags

import "testing"

func TestRuntimeMapSetAndString(t *testing.T) {
	runtimeMap := &RuntimeMap{}

	// 测试插入键值对
	err := runtimeMap.Set("key1=value1")
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}
	err = runtimeMap.Set("key2=value2")
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}
	err = runtimeMap.Set("key3=") // 测试空值
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}

	// 测试覆盖值
	err = runtimeMap.Set("key1=newvalue")
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}

	// 检查字符串表示
	expectedString := "{\"key1\"=\"newvalue\"=\"key2\"=\"value2\"=\"key3\"=\"\"}"
	resultString := runtimeMap.String()
	if resultString != expectedString {
		t.Errorf("Expected string %s, but got %s", expectedString, resultString)
	}
}

func TestRuntimeMapSetInvalid(t *testing.T) {
	runtimeMap := &RuntimeMap{}

	// 测试无效输入（没有等号）
	err := runtimeMap.Set("invalid")
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}

	// 检查字符串表示
	expectedString := "{}"
	resultString := runtimeMap.String()
	if resultString != expectedString {
		t.Errorf("Expected string %s, but got %s", expectedString, resultString)
	}
}
