//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-14 20:08:18
//FilePath: /yaml_scan/pkg/input/types/http_test.go
//Description: http单测

package types

import (
	"net/url"
	"testing"

	mapsutil "yaml_scan/utils/maps"
	urlutil "yaml_scan/utils/url"
)

// TestHttpRequest 测试 HttpRequest 结构体的解析
func TestParseRawRequest(t *testing.T) {
	headersMap := mapsutil.NewOrderedMap[string, string]()
	headersMap.Set("Host", "example.com")
	headersMap.Set("Content-Type", "application/json")
	tests := []struct {
		raw      string
		expected *RequestResponse
		isError  bool
	}{
		{
			raw: "GET /path HTTP/1.1\r\nHost: example.com\r\nContent-Type: application/json\r\n\r\n{\"key\":\"value\"}",
			expected: &RequestResponse{
				URL: urlutil.URL{
					URL: &url.URL{
						Host: "example.com",
						Path: "/path",
					},
				},
				Request: &HttpRequest{
					Method:  "GET",
					Headers: headersMap,
					Body:    "{\"key\":\"value\"}",
					Raw:     "GET /path HTTP/1.1\r\nHost: example.com\r\nContent-Type: application/json\r\n\r\n{\"key\":\"value\"}",
				},
			},
			isError: false,
		},
		{
			raw: "POST /submit HTTP/1.1\r\nHost: example.com\r\nContent-Length: 27\r\n\r\n{\"name\":\"test\"}",
			expected: &RequestResponse{
				URL: urlutil.URL{
					URL: &url.URL{
						Path: "/submit",
						Host: "example.com",
					},
				},
				Request: &HttpRequest{
					Method: "POST",
					Headers: func() mapsutil.OrderedMap[string, string] {
						headers := mapsutil.NewOrderedMap[string, string]()
						headers.Set("Host", "example.com")
						headers.Set("Content-Length", "27")
						return headers
					}(),
					Body: "{\"name\":\"test\"}",
					Raw:  "POST /submit HTTP/1.1\r\nHost: example.com\r\nContent-Length: 27\r\n\r\n{\"name\":\"test\"}",
				},
			},
			isError: false,
		},
		{
			raw: "GET /invalid HTTP/1.1\r\nHost: example.com\r\n\r\n",
			expected: &RequestResponse{
				URL: urlutil.URL{
					URL: &url.URL{
						Path: "/invalid",
						Host: "example.com",
					},
				},
				Request: &HttpRequest{
					Method: "GET",
					Headers: func() mapsutil.OrderedMap[string, string] {
						headers := mapsutil.NewOrderedMap[string, string]()
						headers.Set("Host", "example.com")
						return headers
					}(),
					Body: "",
					Raw:  "GET /invalid HTTP/1.1\r\nHost: example.com\r\n\r\n",
				},
			},
			isError: false,
		},
		{
			raw:      "INVALID HTTP/1.1\r\nHost: example.com\r\n\r\n",
			expected: nil,
			isError:  true,
		},
	}

	for _, test := range tests {
		result, err := ParseRawRequest(test.raw)
		if (err != nil) != test.isError {
			t.Errorf("ParseRawRequest(%q) error = %v; expected error = %v", test.raw, err, test.isError)
		}
		if test.expected != nil && result != nil {
			if result.Request.Method != test.expected.Request.Method {
				t.Errorf("ParseRawRequest(%q) = %v; expected %v", test.raw, result.Request.Method, test.expected.Request.Method)
			}
			if result.URL.Path != test.expected.URL.Path {
				t.Errorf("ParseRawRequest(%q) = %v; expected %v", test.raw, result.URL.Path, test.expected.URL.Path)
			}

			if result.Request.Body != test.expected.Request.Body {
				t.Errorf("ParseRawRequest(%q) = %v; expected %v", test.raw, result.Request.Body, test.expected.Request.Body)
			}
		} else if test.expected != nil && result == nil {
			t.Errorf("ParseRawRequest(%q) = nil; expected %v", test.raw, test.expected)
		}
	}
}

// TestParseRawRequestWithURL 测试 ParseRawRequestWithURL 函数
func TestParseRawRequestWithURL(t *testing.T) {
	tests := []struct {
		raw      string
		url      string
		expected *RequestResponse
		isError  bool
	}{
		{
			raw: "GET /path HTTP/1.1\r\nHost: example.com\r\n\r\n",
			url: "http://example.com/path",
			expected: &RequestResponse{
				URL: urlutil.URL{
					URL: &url.URL{
						Host: "example.com",
						Path: "/path",
					},
				},
				Request: &HttpRequest{
					Method: "GET",
					Headers: func() mapsutil.OrderedMap[string, string] {
						headers := mapsutil.NewOrderedMap[string, string]()
						headers.Set("Host", "example.com")
						return headers
					}(),
					Body: "",
					Raw:  "GET /path HTTP/1.1\r\nHost: example.com\r\n\r\n",
				},
			},
			isError: false,
		},

		
	}

	for _, test := range tests {
		result, err := ParseRawRequestWithURL(test.raw, test.url)
		if (err != nil) != test.isError {
			t.Errorf("ParseRawRequestWithURL(%q, %q) error = %v; expected error = %v", test.raw, test.url, err, test.isError)
		}
		if test.expected != nil && result != nil {
			if result.Request.Method != test.expected.Request.Method {
				t.Errorf("ParseRawRequestWithURL(%q, %q) = %v; expected %v", test.raw, test.url, result.Request.Method, test.expected.Request.Method)
			}
			if result.URL.Path != test.expected.URL.Path {
				t.Errorf("ParseRawRequestWithURL(%q, %q) = %v; expected %v", test.raw, test.url, result.URL.Path, test.expected.URL.Path)
			}

			if result.Request.Body != test.expected.Request.Body {
				t.Errorf("ParseRawRequestWithURL(%q, %q) = %v; expected %v", test.raw, test.url, result.Request.Body, test.expected.Request.Body)
			}
		} else if test.expected != nil && result == nil {
			t.Errorf("ParseRawRequestWithURL(%q, %q) = nil; expected %v", test.raw, test.url, test.expected)
		}
	}
}
