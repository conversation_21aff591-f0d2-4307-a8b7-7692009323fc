//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-09 16:43:36
//FilePath: /yaml_scan/pkg/retryabledns/doh/doh_client.go
//Description:

package doh

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"errors"
	"io"
	"net/http"

	"github.com/miekg/dns"
)

// Client Doh客户端
type Client struct {
	DefaultResolver Resolver     // 默认解析器
	httpClient      *http.Client // 内部使用的 http.Client
}

// NewWithOptions: 创建一个新的 Client 实例，使用提供的选项进行配置。
//
//	@param options Options:包含配置 Client 的选项。
//	@return *Client *Client:
func NewWithOptions(options Options) *Client {
	return &Client{DefaultResolver: options.DefaultResolver, httpClient: options.HttpClient}
}

// New:创建一个新的 Client 实例，使用默认配置。
//
//	@return *Client *Client: 返回一个使用默认配置的 Client 实例，默认使用 Cloudflare 解析器和配置了超时的 http.Client。
func New() *Client {
	httpClient := NewHttpClient(
		WithTimeout(DefaultTimeout),
		WithInsecureSkipVerify(),
	)
	// 使用 Google 作为默认解析器创建 Client 实例
	return NewWithOptions(Options{DefaultResolver: Google, HttpClient: httpClient})
}

// Query: 使用默认解析器进行 DNS 查询。
//
//	@receiver c *Client:
//	@param name string:  要查询的域名，例如 "example.com"。
//	@param question QuestionType: 查询类型，例如 A、AAAA 等。
//	@return *Response *Response: DNS 查询的响应，包含查询结果。
//	@return error error: 如果查询失败，返回具体的错误信息。
func (c *Client) Query(name string, question QuestionType) (*Response, error) {
	return c.QueryWithResolver(c.DefaultResolver, name, question)
}

// QueryWithResolver:使用指定的解析器进行 DNS 查询。
//
//	@receiver c *Client:
//	@param r Resolver: 指定的 DNS 解析器 例如 Cloudflare 或 Google。
//	@param name string: 要查询的域名，例如 "example.com"。
//	@param question QuestionType: 查询类型，例如 A、AAAA 等。
//	@return *Response *Response: DNS 查询的响应，包含查询结果。
//	@return error error: 如果查询失败，返回具体的错误信息。
func (c *Client) QueryWithResolver(r Resolver, name string, question QuestionType) (*Response, error) {
	return c.QueryWithJsonAPI(r, name, question)
}

// QueryWithJsonAPI: 使用 JSON API 进行 DNS 查询。
//
//	@receiver c *Client:
//	@param r Resolver:  DNS 解析器
//	@param name string: 要查询的域名，例如 "example.com"。
//	@param question QuestionType: 查询类型，例如 A、AAAA 等。
//	@return *Response *Response: DNS 查询的响应，包含查询结果。
//	@return error error: 如果查询失败，返回具体的错误信息。
func (c *Client) QueryWithJsonAPI(r Resolver, name string, question QuestionType) (*Response, error) {
	// 创建一个新的 HTTP GET 请求
	req, err := http.NewRequest(http.MethodGet, r.URL, nil)
	if err != nil {
		return nil, err
	}
	// 设置请求头
	req.Header.Set("Accept", "application/dns-json")

	// 添加查询参数到 URL
	q := req.URL.Query()
	// 设置查询的域名
	q.Add("name", name)
	// 设置查询类型（转换为字符串）
	q.Add("type", question.ToString())
	req.URL.RawQuery = q.Encode()

	// 使用 httpClient 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.Body == nil {
		return nil, errors.New("empty response body")
	}

	var response Response

	// 将响应体解码为 Response 结构体
	err = json.NewDecoder(resp.Body).Decode(&response)
	if err != nil {
		return nil, err
	}

	return &response, nil
}

// QueryWithDOH: 通过 DoH 协议发送查询
//
//	@receiver c *Client:
//	@param method Method: HTTP 方法
//	@param r Resolver:解析器
//	@param name string:查询的域名
//	@param question uint16: DNS 查询类型
//	@return *dns.Msg *dns.Msg:从 DoH 服务器接收到的 DNS 响应消息
//	@return error error: 如果查询失败，返回具体的错误信息
func (c *Client) QueryWithDOH(method Method, r Resolver, name string, question uint16) (*dns.Msg, error) {
	// 创建一个新的 DNS 消息
	msg := &dns.Msg{}
	// 设置消息 ID 为 0，通常由客户端自动生成
	msg.Id = 0
	// 分配一个问题切片
	msg.Question = make([]dns.Question, 1)
	// 设置查询问题
	msg.Question[0] = dns.Question{
		Name:   dns.Fqdn(name),
		Qtype:  question,
		Qclass: dns.ClassINET,
	}
	return c.QueryWithDOHMsg(method, r, msg)
}

// QueryWithDOHMsg: 通过 DNS over HTTPS (DoH) 协议发送 DNS 查询
//
//	@receiver c *Client:
//	@param method Method: HTTP 方法，支持 MethodPost 和 MethodGet。
//	@param r Resolver: 解析器，包含 DoH 服务器的 URL。
//	@param msg *dns.Msg:要发送的 DNS 消息。
//	@return *dns.Msg *dns.Msg: 从 DoH 服务器接收到的 DNS 响应消息；
//	@return error error:如果查询失败，返回具体的错误信息
func (c *Client) QueryWithDOHMsg(method Method, r Resolver, msg *dns.Msg) (*dns.Msg, error) {
	// 打包 DNS 消息
	packedMsg, err := msg.Pack()
	if err != nil {
		return nil, err
	}

	var body []byte
	var dnsParam string

	switch method {
	case MethodPost:
		dnsParam = ""
		// 对于 POST 方法，使用打包的消息作为请求体
		body = packedMsg
	case MethodGet:
		// 对于 GET 方法，使用 Base64 编码的消息作为查询参数
		dnsParam = base64.RawURLEncoding.EncodeToString(packedMsg)
		body = nil
	default:
		return nil, errors.New("unsupported method")
	}

	// 创建 HTTP 请求
	req, err := http.NewRequest(string(method), r.URL, bytes.NewReader(body))
	if err != nil {
		return nil, err
	}
	// 设置请求头
	req.Header.Set("Accept", "application/dns-message")
	// 添加 DNS 参数
	if dnsParam != "" {
		q := req.URL.Query()
		q.Add("dns", dnsParam)
		req.URL.RawQuery = q.Encode()
	} else if len(body) > 0 {
		// 设置内容类型
		req.Header.Set("Content-Type", "application/dns-message")
	}

	// 发送 HTTP 请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.Body == nil {
		return nil, errors.New("empty response body")
	}

	// 读取响应体
	respBodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 解析DNS响应消息
	respMsg := &dns.Msg{}
	if err := respMsg.Unpack(respBodyBytes); err != nil {
		return nil, err
	}
	return respMsg, nil
}
