//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-16 20:20:47
//FilePath: /yaml_scan/utils/slice/sliceutil_test.go
//Description:

package sliceutil

import (
	"testing"

	"github.com/stretchr/testify/require"
)

// TestEqual 测试 Equal 函数
func TestEqual(t *testing.T) {
	tests := []struct {
		s1       []int // 第一个切片
		s2       []int // 第二个切片
		expected bool  // 预期结果
	}{
		// 测试相等的切片
		{[]int{1, 2, 3}, []int{1, 2, 3}, true},
		{[]int{1, 2, 3, 4}, []int{1, 2, 3, 4}, true},
		{[]int{}, []int{}, true}, // 测试空切片

		// 测试不相等的切片
		{[]int{1, 2, 3}, []int{1, 2, 4}, false},
		{[]int{1, 2, 3}, []int{1, 2}, false},    // 不同长度
		{[]int{1, 2, 3}, []int{2, 3, 1}, false}, // 相同元素但顺序不同

		// 测试不同长度的切片
		{[]int{1, 2}, []int{1, 2, 3}, false},
		{[]int{1, 2, 3}, []int{1, 2}, false},
	}

	for _, test := range tests {
		result := Equal(test.s1, test.s2)
		if result != test.expected {
			t.Errorf("Equal(%v, %v) = %v; 预期 %v", test.s1, test.s2, result, test.expected)
		}
	}
}

// TestDedupe 测试 Dedupe 函数的正确性
func TestDedupe(t *testing.T) {
	tests := []struct {
		name     string // 测试用例名称
		input    []int  // 输入的切片
		expected []int  // 预期的去重结果
	}{
		{
			name:     "Basic deduplication",
			input:    []int{1, 2, 2, 3, 4, 4, 5, 1},
			expected: []int{1, 2, 3, 4, 5},
		},
		{
			name:     "All unique",
			input:    []int{1, 2, 3, 4, 5},
			expected: []int{1, 2, 3, 4, 5},
		},
		{
			name:     "All duplicates",
			input:    []int{1, 1, 1, 1, 1},
			expected: []int{1},
		},
	}

	// 遍历每个测试用例
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 调用 Dedupe 函数进行去重
			result := Dedupe(tt.input)
			// 断言去重结果是否符合预期
			require.Equal(t, tt.expected, result)
		})
	}
}
