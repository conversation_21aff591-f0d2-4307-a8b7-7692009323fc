// Author: chenjb
// Version: V1.0
// Date: 2025-06-17 16:22:03
// FilePath: /yaml_scan/internal/httpapi/apiendpoint.go
// Description:
package httpapi

import "yaml_scan/pkg/types"

// Server represents the HTTP server that handles the concurrency settings endpoints.
type Server struct {
	addr   string
	config *types.Options
}



// New creates a new instance of Server.
func New(addr string, config *types.Options) *Server {
	return &Server{
		addr:   addr,
		config: config,
	}
}


// Start initializes the server and its routes, then starts listening on the specified address.
func (s *Server) Start() error {
	http.HandleFunc("/api/concurrency", s.handleConcurrency)
	if err := http.ListenAndServe(s.addr, nil); err != nil {
		return err
	}
	return nil
}