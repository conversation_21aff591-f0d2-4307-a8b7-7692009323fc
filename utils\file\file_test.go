package fileutil

import (
	"os"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestFileExists 测试 FileExists 函数
func TestFileExists(t *testing.T) {
	// 创建一个临时文件用于测试
	tempFile, err := os.CreateTemp("", "testfile")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name()) // 测试结束后删除临时文件

	// 测试文件存在
	if !FileExists(tempFile.Name()) {
		t.Errorf("Expected file %s to exist, but it does not.", tempFile.Name())
	}

	// 测试文件不存在
	if FileExists("non_existent_file.txt") {
		t.Error("Expected non_existent_file.txt to not exist, but it does.")
	}

	// 测试目录存在
	dirName := tempFile.Name() + "_dir"
	if err := os.Mkdir(dirName, 0755); err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(dirName) // 测试结束后删除临时目录

	// 测试目录被认为不存在
	if FileExists(dirName) {
		t.Errorf("Expected directory %s to not be considered a file, but it is.", dirName)
	}
}


// TestReadFile 测试 ReadFile 函数
func TestReadFile(t *testing.T) {
	// 创建一个临时文件用于测试
	tempFile, err := os.CreateTemp("", "testfile")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name()) // 测试结束后删除临时文件

	// 写入测试内容
	content := []byte("line 1\nline 2\nline 3\n")
	if _, err := tempFile.Write(content); err != nil {
		t.Fatalf("Failed to write to temp file: %v", err)
	}
	tempFile.Close() // 关闭文件以便后续读取

	// 测试读取文件
	lines, err := ReadFile(tempFile.Name())
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}

	// 读取并验证内容
	expectedLines := []string{"line 1", "line 2", "line 3"}
	for i, expected := range expectedLines {
		line := <-lines
		if line != expected {
			t.Errorf("Expected line %d to be %q, got %q", i+1, expected, line)
		}
	}

	// 确保通道已关闭
	_, ok := <-lines
	if ok {
		t.Error("Expected lines channel to be closed, but it is still open.")
	}
}

// TestReadFile_NonExistentFile 测试读取不存在的文件
func TestReadFile_NonExistentFile(t *testing.T) {
	_, err := ReadFile("non_existent_file.txt")
	if err == nil {
		t.Error("Expected an error for non-existent file, but got none.")
	}
}

// TestFolderExists 测试 FolderExists 函数
func TestFolderExists(t *testing.T) {
	// 创建一个临时文件夹用于测试
	testDir := "./test_folder"
	os.Mkdir(testDir, 0755) // 创建文件夹
	defer os.RemoveAll(testDir) // 测试结束后删除文件夹

	// 测试文件夹存在的情况
	if !FolderExists(testDir) {
		t.Errorf("Expected folder '%s' to exist, but it does not.", testDir)
	}

	// 测试不存在的文件夹
	if FolderExists("./non_existent_folder") {
		t.Error("Expected folder './non_existent_folder' to not exist, but it does.")
	}
}

// TestCreateFolder 测试 CreateFolder 函数
func TestCreateFolder(t *testing.T) {
	testDir := "./test_create_folder" // 测试文件夹路径

	// 测试创建文件夹
	if err := CreateFolder(testDir); err != nil {
		t.Errorf("Expected to create folder '%s', but got error: %v", testDir, err)
	}

	// 检查文件夹是否存在
	if _, err := os.Stat(testDir); os.IsNotExist(err) {
		t.Errorf("Expected folder '%s' to exist, but it does not.", testDir)
	}

	// 清理测试创建的文件夹
	if err := os.RemoveAll(testDir); err != nil {
		t.Errorf("Failed to remove test folder '%s': %v", testDir, err)
	}
}


// 测试文件是否为空
func TestIsEmpty(t *testing.T) {
    // 测试用例列表
    testCases := []struct {
        name     string // 测试用例名称
        filename string // 测试文件名
        expect   bool   // 预期结果
        expectErr bool   // 是否预期错误
    }{
        {"空文件", "empty.txt", true, false},
        {"非空文件", "non_empty.txt", false, false},
        {"不存在的文件", "nonexistent.txt", false, true},
    }

    // 创建测试文件
    defer func() {
        os.Remove("empty.txt")
        os.Remove("non_empty.txt")
    }()

    // 创建空文件
    emptyFile, err := os.Create("empty.txt")
    if err != nil {
        t.Fatalf("无法创建空文件: %v", err)
    }
    emptyFile.Close()

    // 创建包含内容的文件
    nonEmptyContent := []byte("test content")
    if err := os.WriteFile("non_empty.txt", nonEmptyContent, 0644); err != nil {
        t.Fatalf("无法创建非空文件: %v", err)
    }


    // 运行测试用例
    for _, tc := range testCases {
        t.Run(tc.name, func(t *testing.T) {
            result, err := IsEmpty(tc.filename)
            if (err != nil) != tc.expectErr {
                t.Errorf("预期错误状态为 %v，但实际为 %v", tc.expectErr, err != nil)
            }
            if result != tc.expect {
                t.Errorf("预期结果为 %v，但实际为 %v", tc.expect, result)
            }
        })
    }
}


// 测试文件是否只包含注释行或空行
func TestIsCommentOnly(t *testing.T) {
    testCases := []struct {
        name     string // 测试用例名称
        filePath string // 测试文件路径
        expect   bool   // 预期结果
    }{
        {"文件不存在", "nonexistent.txt", false},
        {"空文件", "empty.txt", true},
        {"只包含注释", "comments_only.txt", true},
        {"包含非注释内容", "non_comments.txt", false},
        {"混合内容", "mixed_content.txt", false},
    }

    // 创建测试文件
    defer func() {
        os.Remove("empty.txt")
        os.Remove("comments_only.txt")
        os.Remove("non_comments.txt")
        os.Remove("mixed_content.txt")
    }()

    // 创建空文件
    emptyFile, err := os.Create("empty.txt")
    if err != nil {
        t.Fatalf("无法创建空文件: %v", err)
    }
    emptyFile.Close()

    // 创建只包含注释的文件
    commentsOnlyContent := []byte("# comment1\n# comment2\n")
    if err := os.WriteFile("comments_only.txt", commentsOnlyContent, 0644); err != nil {
        t.Fatalf("无法创建只包含注释的文件: %v", err)
    }

    // 创建包含非注释内容的文件
    nonCommentsContent := []byte("test content\n# comment\n")
    if err := os.WriteFile("non_comments.txt", nonCommentsContent, 0644); err != nil {
        t.Fatalf("无法创建包含非注释内容的文件: %v", err)
    }

    // 创建混合内容的文件
    mixedContent := []byte("# comment\n\n test\n")
    if err := os.WriteFile("mixed_content.txt", mixedContent, 0644); err != nil {
        t.Fatalf("无法创建混合内容的文件: %v", err)
    }

    // 运行测试用例
    for _, tc := range testCases {
        t.Run(tc.name, func(t *testing.T) {
            result := IsCommentOnly(tc.filePath)
            assert.Equal(t, tc.expect, result, "预期结果与实际结果不一致")
        })
    }
}

// TestGetTempFileNameSuccess 测试 GetTempFileName 的成功场景
func TestGetTempFileNameSuccess(t *testing.T) {
	filename, err := GetTempFileName() // 调用函数生成临时文件名
	require.NoError(t, err)            // 确保无错误返回
	require.NotEmpty(t, filename)       // 确保文件名不为空

	// 验证文件名位于系统临时目录
	tmpDir := os.TempDir()                          // 获取系统临时目录
	require.True(t, strings.HasPrefix(filename, tmpDir)) // 确保文件名以临时目录开头

	// 验证文件已被删除
	_, err = os.Stat(filename)                     // 检查文件是否存在
	require.ErrorIs(t, err, os.ErrNotExist) // 确保文件不存在
}