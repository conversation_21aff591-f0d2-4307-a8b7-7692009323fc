// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-18 11:26:24
// FilePath: /yaml_scan/pkg/protocols/common/uncover/uncover.go
// Description: 
package uncover

// GetUncoverTargetsFromMetadata returns targets from uncover metadata
func GetUncoverTargetsFromMetadata(ctx context.Context, templates []*templates.Template, outputFormat string, opts *uncover.Options) chan string {
	// contains map[engine]queries
	queriesMap := make(map[string][]string)
	for _, template := range templates {
	innerLoop:
		for k, v := range template.Info.Metadata {
			if !strings.HasSuffix(k, "-query") {
				// this is not a query
				// query keys are like shodan-query, fofa-query, etc
				continue innerLoop
			}
			engine := strings.TrimSuffix(k, "-query")
			if queriesMap[engine] == nil {
				queriesMap[engine] = []string{}
			}
			switch v := v.(type) {
			case []interface{}:
				qs := queriesMap[engine]
				for _, vv := range v {
					qs = append(qs, fmt.Sprint(vv))
				}
				queriesMap[engine] = qs
			default:
				queriesMap[engine] = append(queriesMap[engine], fmt.Sprint(v))
			}
		}
	}
	for engine, queries := range queriesMap {
		queriesMap[engine] = sliceutil.Dedupe(queries)
	}
	keys := mapsutil.GetKeys(queriesMap)
	gologger.Info().Msgf("Running uncover queries from template against: %s", strings.Join(keys, ","))
	result := make(chan string, runtime.NumCPU())
	go func() {
		defer close(result)
		// unfortunately uncover doesn't support execution of map[engine]queries
		// if queries are given they are executed against all engines which is not what we want
		// TODO: add support for map[engine]queries in uncover
		// Note below implementation is intentionally sequential to avoid burning all the API keys
		counter := 0
	outerLoop:
		for eng, queries := range queriesMap {
			if opts.Limit > 0 && counter >= opts.Limit {
				break
			}
			// create new uncover options for each engine
			uncoverOpts := &uncover.Options{
				Agents:        []string{eng},
				Queries:       queries,
				Limit:         opts.Limit,
				MaxRetry:      opts.MaxRetry,
				Timeout:       opts.Timeout,
				RateLimit:     opts.RateLimit,
				RateLimitUnit: opts.RateLimitUnit,
			}
			ch, err := GetTargetsFromUncover(ctx, outputFormat, uncoverOpts)
			if err != nil {
				gologger.Error().Msgf("Could not get targets using %v engine from uncover: %s", eng, err)
				return
			}

		innerLoop:
			for {
				select {
				case <-ctx.Done():
					return
				case res, ok := <-ch:
					if !ok {
						continue outerLoop
					}
					result <- res
					counter++
					if opts.Limit > 0 && counter >= opts.Limit {
						break innerLoop
					}
				}
			}
		}
	}()
	return result
}

