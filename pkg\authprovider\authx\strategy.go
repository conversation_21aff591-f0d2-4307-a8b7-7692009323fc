// Author: chenjb
// Version: V1.0
// Date: 2025-06-13 14:39:23
// FilePath: /yaml_scan/pkg/authprovider/authx/strategy.go
// Description:定义了认证策略的接口和动态认证策略的实现，是认证系统的核心部分
package authx

import (
	"net/http"
	"yaml_scan/pkg/retryablehttp"
)

// AuthStrategy 是认证策略的接口
// 支持基本认证、令牌认证、请求头、Cookie、查询参数等多种认证方式
type AuthStrategy interface {
	// Apply 将认证策略应用到HTTP请求上
	// 参数:
	//   - req: 标准的HTTP请求指针
	Apply(*http.Request)
	
	// ApplyOnRR 将认证策略应用到可重试的HTTP请求上
	// 参数:
	//   - req: 可重试的HTTP请求指针
	ApplyOnRR(*retryablehttp.Request)
}


// DynamicAuthStrategy 是动态密钥的认证策略
// 它实现了AuthStrategy接口，可以动态获取并应用认证信息
type DynamicAuthStrategy struct {
	// Dynamic 是要使用的动态密钥
	Dynamic Dynamic
}

// Apply 将动态认证策略应用到HTTP请求上
// 它会获取所有动态生成的认证策略，并依次应用到请求上
// @receiver d 
// @param req *http.Request: 标准的HTTP请求指针
func (d *DynamicAuthStrategy) Apply(req *http.Request) {
	// 获取所有动态生成的认证策略
	strategies := d.Dynamic.GetStrategies()
	if strategies == nil {
		return
	}
	for _, s := range strategies {
		if s == nil {
			continue
		}
		s.Apply(req)
	}
}

// ApplyOnRR 将动态认证策略应用到可重试的HTTP请求上
// 它会获取所有动态生成的认证策略，并依次应用到可重试请求上
// @receiver d 
// @param req *retryablehttp.Request: 
func (d *DynamicAuthStrategy) ApplyOnRR(req *retryablehttp.Request) {
	strategy := d.Dynamic.GetStrategies()
	for _, s := range strategy {
		s.ApplyOnRR(req)
	}
}