// Author: chenjb
// Version: V1.0
// Date: 2025-04-29 20:01:11
// FilePath: /yaml_scan/pkg/gcache/basecache.go
// Description: 基础缓存
package gcache

import (
	"fmt"
	"sync"
	"time"
)

// baseCache 是缓存的基础结构体，包含通用字段和方法
type baseCache[K comparable, V any] struct {
	clock            Clock                                // 时钟接口，用于获取当前时间
	size             int                                  // 缓存的最大容量
	loaderExpireFunc func(k K) (V, *time.Duration, error) // 加载函数，用于在缓存未命中时加载数据
	evictedFunc      func(k K, v V)                       // 逐出回调函数，当条目被移除时调用
	purgeVisitorFunc func(k K, v V)                       // 清除函数，在清除缓存时对每个条目调用
	addedFunc        func(k K, v V)                       // 添加回调函数，当新条目加入缓存时调用
	serializeFunc    func(k K, v V) (V, error)            // 序列化函数，将缓存值转换为可存储格式
	deserializeFunc  func(k K, v V) (V, error)            // 反序列化函数，将存储值转换回原始格式
	expiration       *time.Duration                       // 默认过期时间
	lease            *time.Duration                       // 租约时间
	mu               sync.RWMutex                         // 读写锁，用于并发安全
	loadGroup        Group[K, V]                          // 加载组，用于同步加载操作
	*stats                                                // 统计信息

	nilV V // 用于快速返回泛型nil
}

// buildCache 初始化 baseCache 的字段
// @param c *baseCache: baseCache 实例
// @param cb *CacheBuilder: CacheBuilder 实例
func buildCache[K comparable, V any](c *baseCache[K, V], cb *CacheBuilder[K, V]) {
	c.clock = cb.clock
	c.size = cb.size
	c.loaderExpireFunc = cb.loaderExpireFunc
	c.expiration = cb.expiration
	c.lease = cb.lease
	c.addedFunc = cb.addedFunc
	c.deserializeFunc = cb.deserializeFunc
	c.serializeFunc = cb.serializeFunc
	c.evictedFunc = cb.evictedFunc
	c.purgeVisitorFunc = cb.purgeVisitorFunc
	c.stats = &stats{}
}

// load 使用指定的键加载新值
// @param key: 要加载的键 
// @param cb: 回调函数，用于处理加载结果
// @param isWait: 是否等待加载完成
// @return V: 加载的值
// @return bool: 是否调用了加载函数
//  @return error: 加载过程中的错误
func (c *baseCache[K, V]) load(key K, cb func(V, *time.Duration, error) (V, error), isWait bool) (V, bool, error) {
	v, called, err := c.loadGroup.Do(key, func() (v V, e error) {
		defer func() {
			if r := recover(); r != nil {
				e = fmt.Errorf("Loader panics: %v", r)
			}
		}()
		return cb(c.loaderExpireFunc(key))
	}, isWait)
	if err != nil {
		return c.nilV, called, err
	}
	return v, called, nil
}
