// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-16 14:42:12
// FilePath: /yaml_scan/pkg/authprovider/authx/basic_auth_test.go
// Description: 
package authx

import (
	"net/http"
	"testing"
	"yaml_scan/pkg/retryablehttp"

	"github.com/stretchr/testify/require"
)

// TestBasicAuthStrategy_Apply 测试基本认证策略的Apply方法
// 确保它能正确设置HTTP请求的基本认证头
func TestBasicAuthStrategy_Apply(t *testing.T) {
	// 创建测试用的密钥数据
	secret := &Secret{
		Username: "testuser",
		Password: "testpass",
	}

	// 创建基本认证策略
	strategy := NewBasicAuthStrategy(secret)

	// 创建测试用的HTTP请求
	req, err := http.NewRequest("GET", "https://example.com", nil)
	require.NoError(t, err, "创建HTTP请求不应该返回错误")

	// 应用认证策略
	strategy.Apply(req)

	// 验证请求头是否正确设置
	username, password, ok := req.BasicAuth()
	require.True(t, ok, "应该能从请求中获取基本认证信息")
	require.Equal(t, "testuser", username, "用户名应该被正确设置")
	require.Equal(t, "testpass", password, "密码应该被正确设置")
}

// TestBasicAuthStrategy_ApplyOnRR 测试基本认证策略的ApplyOnRR方法
// 确保它能正确设置可重试HTTP请求的基本认证头
func TestBasicAuthStrategy_ApplyOnRR(t *testing.T) {
	// 创建测试用的密钥数据
	secret := &Secret{
		Username: "testuser",
		Password: "testpass",
	}

	// 创建基本认证策略
	strategy := NewBasicAuthStrategy(secret)

	// 创建测试用的可重试HTTP请求
	httpReq, err := http.NewRequest("GET", "https://example.com", nil)
	require.NoError(t, err, "创建HTTP请求不应该返回错误")
	req, err := retryablehttp.FromRequest(httpReq)
	require.NoError(t, err, "创建可重试HTTP请求不应该返回错误")

	// 应用认证策略
	strategy.ApplyOnRR(req)

	// 验证请求头是否正确设置
	authHeader := req.Header.Get("Authorization")
	require.NotEmpty(t, authHeader, "Authorization头应该被设置")

	// 因为我们不能直接从可重试请求中调用BasicAuth方法，所以我们检查标准请求
	username, password, ok := req.Request.BasicAuth()
	require.True(t, ok, "应该能从请求中获取基本认证信息")
	require.Equal(t, "testuser", username, "用户名应该被正确设置")
	require.Equal(t, "testpass", password, "密码应该被正确设置")
}

// TestNewBasicAuthStrategy 测试创建基本认证策略的函数
// 确保它能正确创建BasicAuthStrategy对象
func TestNewBasicAuthStrategy(t *testing.T) {
	// 创建测试用的密钥数据
	secret := &Secret{
		Username: "testuser",
		Password: "testpass",
	}

	// 创建基本认证策略
	strategy := NewBasicAuthStrategy(secret)

	// 验证策略对象是否正确创建
	require.NotNil(t, strategy, "应该创建非空的策略对象")
	require.Equal(t, secret, strategy.Data, "策略对象应该包含正确的密钥数据")
}


