// Author: chenjb
// Version: V1.0
// Date: 2025-07-18 11:34:59
// FilePath: /yaml_scan/pkg/wappalyzergo/fingerprint_body.go
// Description: 实现基于HTTP响应体的技术指纹识别功能

package wappalyzergo

import (
	"bytes"
	"unsafe"

	"golang.org/x/net/html"
)

// checkBody 检查HTML响应体中的技术指纹
// 该方法通过分析HTML内容的多个维度来识别Web技术，包括：
// 1. 整体HTML字符串匹配
// 2. HTML标签属性分析
// 3. 脚本标签源码分析
// 4. Meta标签信息提取
//
// 参数:
//   - body: 已转换为小写的HTML响应体字节数组，用于大小写不敏感的匹配
//
// 返回值:
//   - []matchPartResult: 匹配到的技术列表，包含技术名称、版本和置信度信息
//
// 处理流程:
//   1. 将字节数组转换为字符串进行整体匹配
//   2. 使用HTML tokenizer解析文档结构
//   3. 分析各种HTML元素的属性和内容
//   4. 收集所有匹配结果并返回
func (s *Wappalyze) checkBody(body []byte) []matchPartResult {
	var technologies []matchPartResult

	// 使用unsafe包将字节数组转换为字符串，避免内存拷贝提高性能
	// 注意：这里假设body在函数执行期间不会被修改
	bodyString := unsafeToString(body)

	// 对整个HTML字符串进行指纹匹配
	// htmlPart表示这是HTML内容类型的匹配
	// 这一步会匹配HTML源码中的特征字符串，如框架标识、版本信息等
	technologies = append(
		technologies,
		s.fingerprints.matchString(bodyString, htmlPart)...,
	)

	// 创建HTML tokenizer来解析文档结构
	// 使用tokenizer可以准确解析HTML标签、属性和内容
	// 相比简单的字符串匹配，tokenizer能够正确处理HTML语法
	tokenizer := html.NewTokenizer(bytes.NewReader(body))

	// 开始解析HTML文档，逐个处理token
	for {
		tt := tokenizer.Next()
		switch tt {
		case html.ErrorToken:
			// 遇到错误或文档结束，返回所有收集到的技术信息
			return technologies
		case html.StartTagToken:
			// 处理开始标签（如<script>, <meta>等）
			token := tokenizer.Token()
			switch token.Data {
			case "script":
				// 处理script标签，检查外部脚本源文件
				// Check if the script tag has a source file to check
				source, found := getScriptSource(token)
				if found {
					// 如果找到外部脚本源，检查脚本URL中的技术指纹
					// 许多框架和库可以通过其JavaScript文件路径识别
					// Check the script tags for script fingerprints
					technologies = append(
						technologies,
						s.fingerprints.matchString(source, scriptPart)...,
					)
					continue
				}

				// 如果没有外部脚本源，检查内联脚本内容
				// Check the text attribute of the tag for javascript based technologies.
				// The next token should be the contents of the script tag
				if tokenType := tokenizer.Next(); tokenType != html.TextToken {
					continue
				}

				// TODO: JS requires a running VM, for checking properties. Only
				// possible with headless for now :(
				// 注意：JavaScript变量检测需要运行时环境，目前只能在无头浏览器中实现
				// 这里暂时注释掉了JavaScript内容的检测代码

				// data := tokenizer.Token().Data
				// technologies = append(
				// 	technologies,
				// 	s.fingerprints.matchString(data, jsPart)...,
				// )
			case "meta":
				// 处理meta标签，提取name和content属性进行技术识别
				// Meta标签通常包含生成器信息、框架版本等重要技术指纹
				// For meta tag, we are only interested in name and content attributes.
				name, content, found := getMetaNameAndContent(token)
				if !found {
					continue
				}
				// 使用键值对匹配方式检查meta标签
				// 这种方式可以同时匹配name和content属性的组合
				technologies = append(
					technologies,
					s.fingerprints.matchKeyValueString(name, content, metaPart)...,
				)
			}
		case html.SelfClosingTagToken:
			// 处理自闭合标签（如<meta />）
			token := tokenizer.Token()
			if token.Data != "meta" {
				continue
			}

			// 解析自闭合的meta标签并检查技术指纹
			// Parse the meta tag and check for tech
			name, content, found := getMetaNameAndContent(token)
			if !found {
				continue
			}
			// 对自闭合meta标签执行相同的指纹匹配逻辑
			technologies = append(
				technologies,
				s.fingerprints.matchKeyValueString(name, content, metaPart)...,
			)
		}
	}
}

// getTitle 从HTML响应体中提取页面标题
// 该方法专门用于提取HTML文档的<title>标签内容
//
// 参数:
//   - body: HTML响应体字节数组
//
// 返回值:
//   - string: 页面标题文本，如果没有找到title标签则返回空字符串
//
// 处理逻辑:
//   1. 使用HTML tokenizer解析文档
//   2. 查找<title>开始标签
//   3. 获取下一个文本token作为标题内容
//   4. 返回提取的标题文本
//
// 使用场景:
//   - 页面信息收集
//   - 网站标识和分类
//   - 技术栈识别的辅助信息
func (s *Wappalyze) getTitle(body []byte) string {
	var title string

	// 使用HTML tokenizer解析文档以查找标题
	// Tokenize the HTML document and check for fingerprints as required
	tokenizer := html.NewTokenizer(bytes.NewReader(body))

	for {
		tt := tokenizer.Next()
		switch tt {
		case html.ErrorToken:
			// 解析完成或遇到错误，返回找到的标题
			return title
		case html.StartTagToken:
			token := tokenizer.Token()
			switch token.Data {
			case "title":
				// 找到title标签，获取其文本内容
				// Next text token will be the actual title of the page
				if tokenType := tokenizer.Next(); tokenType != html.TextToken {
					continue
				}
				// 提取标题文本并存储
				title = tokenizer.Token().Data
			}
		}
	}
}

// getMetaNameAndContent 从meta HTML token中提取name和content属性
// 该函数专门用于解析meta标签的关键属性，这些属性通常包含重要的技术指纹信息
//
// 参数:
//   - token: HTML token，应该是一个meta标签
//
// 返回值:
//   - string: name属性的值
//   - string: content属性的值  
//   - bool: 是否成功提取到有效的name和content属性
//
// 处理逻辑:
//   1. 检查token是否包含足够的属性（至少2个）
//   2. 遍历所有属性，查找name和content
//   3. 返回提取的属性值和成功标志
//
// 常见的meta标签示例:
//   - <meta name="generator" content="WordPress 5.8">
//   - <meta name="framework" content="Laravel">
//   - <meta name="version" content="1.0.0">
func getMetaNameAndContent(token html.Token) (string, string, bool) {
	// 检查属性数量是否足够（至少需要name和content两个属性）
	if len(token.Attr) < keyValuePairLength {
		return "", "", false
	}

	var name, content string
	// 遍历所有属性，查找name和content
	for _, attr := range token.Attr {
		switch attr.Key {
		case "name":
			name = attr.Val
		case "content":
			content = attr.Val
		}
	}
	// 返回提取的属性值，如果name或content为空则认为提取失败
	return name, content, true
}

// getScriptSource 从script标签中提取src属性
// 该函数用于获取外部JavaScript文件的URL，这些URL通常包含框架和库的识别信息
//
// 参数:
//   - token: HTML token，应该是一个script标签
//
// 返回值:
//   - string: src属性的值（JavaScript文件URL）
//   - bool: 是否成功找到src属性
//
// 处理逻辑:
//   1. 检查token是否包含属性
//   2. 遍历所有属性，查找src属性
//   3. 返回src属性值和成功标志
//
// 常见的script src示例:
//   - <script src="/js/jquery-3.6.0.min.js">
//   - <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.0/dist/js/bootstrap.bundle.min.js">
//   - <script src="/assets/js/app.js?v=1.2.3">
//
// 技术识别价值:
//   - 通过JavaScript文件路径识别前端框架和库
//   - 从文件名中提取版本信息
//   - 识别CDN使用情况和第三方服务
func getScriptSource(token html.Token) (string, bool) {
	// 检查是否有属性
	if len(token.Attr) < 1 {
		return "", false
	}

	var source string
	// 遍历所有属性，查找src属性
	for _, attr := range token.Attr {
		switch attr.Key {
		case "src":
			source = attr.Val
		}
	}
	// 返回src属性值，如果source为空则认为没有找到
	return source, true
}

// unsafeToString 将字节切片转换为字符串，零内存分配
// 该函数使用unsafe包直接转换内存指针，避免了数据拷贝，提高性能
//
// 注意事项:
//   - 只有在确定底层数组不会被修改的情况下才应使用此函数
//   - 这是一个性能优化技巧，但需要谨慎使用
//   - 如果底层数据被修改，可能导致不可预期的行为
//
// 参数:
//   - data: 要转换的字节切片
//
// 返回值:
//   - string: 转换后的字符串，与原字节切片共享底层内存
//
// 性能优势:
//   - 避免内存拷贝，特别适用于大型HTML文档
//   - 减少垃圾回收压力
//   - 提高字符串处理速度
//
// 参考资料:
//   - https://github.com/golang/go/issues/25484
func unsafeToString(data []byte) string {
	// 使用unsafe包直接转换指针，实现零拷贝转换
	return *(*string)(unsafe.Pointer(&data))
}



