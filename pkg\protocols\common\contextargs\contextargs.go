// Author: chenjb
// Version: V1.0
// Date: 2025-06-17 20:22:31
// FilePath: /yaml_scan/pkg/protocols/common/contextargs/contextargs.go
// Description:
package contextargs

import "context"

// Context implements a shared context struct to share information across multiple templates within a workflow
type Context struct {
	ctx context.Context

	// Meta is the target for the executor
	MetaInput *MetaInput

	// CookieJar shared within workflow's http templates
	CookieJar *cookiejar.Jar

	// Args is a workflow shared key-value store
	args *mapsutil.SyncLockMap[string, interface{}]
}


// Create a new contextargs instance with input string
func NewWithInput(ctx context.Context, input string) *Context {
	jar, err := cookiejar.New(nil)
	if err != nil {
		gologger.Error().Msgf("contextargs: could not create cookie jar: %s\n", err)
	}
	metaInput := NewMetaInput()
	metaInput.Input = input
	return &Context{
		ctx:       ctx,
		MetaInput: metaInput,
		CookieJar: jar,
		args: &mapsutil.SyncLockMap[string, interface{}]{
			Map:      make(map[string]interface{}),
			ReadOnly: atomic.Bool{},
		},
	}
}

// Add the specific key-value pair
func (ctx *Context) Add(key string, v interface{}) {
	values, ok := ctx.args.Get(key)
	if !ok {
		ctx.Set(key, v)
	}

	// If the key exists, append the value to the existing value
	switch v := v.(type) {
	case []string:
		if values, ok := values.([]string); ok {
			values = append(values, v...)
			ctx.Set(key, values)
		}
	case string:
		if values, ok := values.(string); ok {
			tmp := []string{values, v}
			ctx.Set(key, tmp)
		}
	default:
		values, _ := ctx.Get(key)
		ctx.Set(key, []interface{}{values, v})
	}
}