// Author: chenjb
// Version: V1.0
// Date: 2025-07-25
// FilePath: /yaml_scan/pkg/rawhttp/simple_test.go
// Description: rawhttp包简单测试验证

package rawhttp

import (
	"testing"

	"github.com/stretchr/testify/require"
)

// TestSimple 简单测试验证包的基本功能
func TestSimple(t *testing.T) {
	// 测试默认选项不为nil
	require.NotNil(t, DefaultOptions, "默认选项不应该为nil")
	
	// 测试默认客户端不为nil
	require.NotNil(t, DefaultClient, "默认客户端不应该为nil")
	
	// 测试创建新客户端
	client := NewClient(DefaultOptions)
	require.NotNil(t, client, "新客户端不应该为nil")
}
