package goflags

import (
	"flag"
	"testing"
)


// MockFlagValue 是一个实现了 flag.Value 接口的结构体，用于测试。
type MockFlagValue struct {
    value string
}


// String 方法返回当前值的字符串表示。
func (m *MockFlagValue) String() string {
	return m.value
}

// Set 方法设置当前值。
func (m *MockFlagValue) Set(value string) error {
	m.value = value
	return nil
}



// TestVar 测试 FlagSet 的 Var 方法
func TestVar(t *testing.T) {
    flagSet := &FlagSet{
        CommandLine: flag.NewFlagSet("test", flag.ContinueOnError),
        flagKeys:    newInsertionOrderedMap(),
    }

    mockValue := &MockFlagValue{}
    flagData := flagSet.Var(mockValue, "longFlagOnly", "This is a test flag without short name")

    // 检查 FlagData 是否正确设置
    if flagData.usage != "This is a test flag without short name" {
        t.<PERSON><PERSON>("Expected usage to be 'This is a test flag without short name', got %s", flagData.usage)
    }
    if flagData.long != "longFlagOnly" {
        t.<PERSON><PERSON><PERSON>("Expected long flag to be 'longFlagOnly', got %s", flagData.long)
    }
    if flagData.short != "" {
        t.Errorf("Expected short flag to be empty, got %s", flagData.short)
    }

    // 检查 flagKeys 是否包含长标志
    if len(flagSet.flagKeys.keys) != 1 {
        t.Errorf("Expected flagKeys to have 1 key, got %d", len(flagSet.flagKeys.keys))
    }
    if flagSet.flagKeys.values["longFlagOnly"] != flagData {
        t.Errorf("Expected flagKeys to contain longFlagOnly, got %v", flagSet.flagKeys.values["longFlagOnly"])
    }
}

// TestVarP 测试 FlagSet 的 VarP 方法
func TestVarP(t *testing.T) {
    flagSet := &FlagSet{
        CommandLine: flag.NewFlagSet("test", flag.ContinueOnError),
        flagKeys:    newInsertionOrderedMap(),
    }

    mockValue := &MockFlagValue{}
    flagData := flagSet.VarP(mockValue, "longFlag", "s", "This is a test flag")

    // 检查 FlagData 是否正确设置
    if flagData.usage != "This is a test flag" {
        t.Errorf("Expected usage to be 'This is a test flag', got %s", flagData.usage)
    }
    if flagData.long != "longFlag" {
        t.Errorf("Expected long flag to be 'longFlag', got %s", flagData.long)
    }
    if flagData.short != "s" {
        t.Errorf("Expected short flag to be 's', got %s", flagData.short)
    }

    // 检查 flagKeys 是否包含短标志和长标志
    if len(flagSet.flagKeys.keys) != 2 {
        t.Errorf("Expected flagKeys to have 2 keys, got %d", len(flagSet.flagKeys.keys))
    }
    if flagSet.flagKeys.values["longFlag"] != flagData {
        t.Errorf("Expected flagKeys to contain longFlag, got %v", flagSet.flagKeys.values["longFlag"])
    }
    if flagSet.flagKeys.values["s"] != flagData {
        t.Errorf("Expected flagKeys to contain short flag 's', got %v", flagSet.flagKeys.values["s"])
    }
}


// TestStringSliceVarP 测试 StringSliceVarP 方法
func TestStringSliceVarP(t *testing.T) {
    // 创建一个新的 FlagSet 实例
    flagSet := &FlagSet{
        CommandLine: flag.NewFlagSet("test", flag.ContinueOnError),
        flagKeys:    newInsertionOrderedMap(),
    }

    // 创建一个 StringSlice 实例
    var stringSlice StringSlice

    // 设置默认值
    defaultValue := StringSlice{"default1", "default2"}

    // 添加字符串切片标志
    flagData := flagSet.StringSliceVarP(&stringSlice, "longFlag", "s", defaultValue, "This is a test flag", MockOptions)

    // 检查 FlagData 是否正确设置
	if flagData.usage != "This is a test flag" {
        t.Errorf("Expected usage to be 'This is a test flag', got %s", flagData.usage)
    }
    if flagData.long != "longFlag" {
        t.Errorf("Expected long flag to be 'longFlag', got %s", flagData.long)
    }
    if flagData.short != "s" {
        t.Errorf("Expected short flag to be 's', got %s", flagData.short)
    }

    // 检查 flagKeys 是否包含短标志和长标志
    if len(flagSet.flagKeys.keys) != 2 {
        t.Errorf("Expected flagKeys to have 2 keys, got %d", len(flagSet.flagKeys.keys))
    }
    if flagSet.flagKeys.values["longFlag"] != flagData {
        t.Errorf("Expected flagKeys to contain longFlag, got %v", flagSet.flagKeys.values["longFlag"])
    }
    if flagSet.flagKeys.values["s"] != flagData {
        t.Errorf("Expected flagKeys to contain short flag 's', got %v", flagSet.flagKeys.values["s"])
    }
}


// TestStringVarP 测试 StringVarP 方法
func TestStringVarP(t *testing.T) {
    var testValue string // 用于存储测试标志的值
    flagSet := &FlagSet{
        CommandLine: flag.NewFlagSet("test", flag.ContinueOnError), // 创建新的 FlagSet
        flagKeys:    newInsertionOrderedMap(), // 初始化 flagKeys 映射
    }

    // 添加标志
    flagSet.StringVarP(&testValue, "config", "c", "default.conf", "Path to the config file")


    // 验证 flagKeys 中的元数据
    if flagData, exists := flagSet.flagKeys.values["config"]; !exists || flagData.long != "config" || flagData.defaultValue != "default.conf" {
        t.Error("Expected flagKeys to contain valid metadata for 'config'") // 检查 flagKeys 中的长名称标志元数据
    }
    if flagData, exists := flagSet.flagKeys.values["c"]; !exists || flagData.short != "c" {
        t.Error("Expected flagKeys to contain valid metadata for 'c'") // 检查 flagKeys 中的短名称标志元数据
    }
}


// TestBoolVarP 测试 BoolVarP 方法
func TestBoolVarP(t *testing.T) {
    var verbose bool // 用于存储测试标志的值
    flagSet := &FlagSet{
        CommandLine: flag.NewFlagSet("test", flag.ContinueOnError), // 创建新的 FlagSet
        flagKeys:    newInsertionOrderedMap(), // 初始化 flagKeys 映射
    }

    // 添加布尔标志
    flagSet.BoolVarP(&verbose, "verbose", "v", false, "Enable verbose output")

    // 解析命令行参数，测试长名称
    err := flagSet.CommandLine.Parse([]string{"--verbose"})
    if err != nil {
        t.Fatalf("Failed to parse flags: %v", err) // 如果解析失败，记录错误并终止测试
    }
    if !verbose {
        t.Error("Expected verbose to be true after setting --verbose")
    }

    // 解析命令行参数，测试短名称
    err = flagSet.CommandLine.Parse([]string{"-v"})
    if err != nil {
        t.Fatalf("Failed to parse flags: %v", err) // 如果解析失败，记录错误并终止测试
    }
    if !verbose {
        t.Error("Expected verbose to be true after setting -v")
    }

    // 验证 flagKeys 中的元数据
    if flagData, exists := flagSet.flagKeys.values["verbose"]; !exists || flagData.long != "verbose" || flagData.defaultValue != "false" {
        t.Error("Expected flagKeys to contain valid metadata for 'verbose'")
    }
    if flagData, exists := flagSet.flagKeys.values["v"]; !exists || flagData.short != "v" {
        t.Error("Expected flagKeys to contain valid metadata for 'v'")
    }
}


// TestBoolVar 测试 BoolVar 方法
func TestBoolVar(t *testing.T) {
    var verbose bool // 用于存储测试标志的值
    flagSet := &FlagSet{
        CommandLine: flag.NewFlagSet("test", flag.ContinueOnError), // 创建新的 FlagSet
        flagKeys:    newInsertionOrderedMap(), // 初始化 flagKeys 映射
    }

    // 添加布尔标志
    flagSet.BoolVar(&verbose, "verbose", false, "Enable verbose output")

    // 解析命令行参数，测试长名称
    err := flagSet.CommandLine.Parse([]string{"--verbose"})
    if err != nil {
        t.Fatalf("Failed to parse flags: %v", err) // 如果解析失败，记录错误并终止测试
    }
    if !verbose {
        t.Error("Expected verbose to be true after setting --verbose")
    }

    // 验证 flagKeys 中的元数据
    if flagData, exists := flagSet.flagKeys.values["verbose"]; !exists || flagData.long != "verbose" || flagData.defaultValue != "false" {
        t.Error("Expected flagKeys to contain valid metadata for 'verbose'")
    }
}

// TestStringSliceVarConfigOnly 测试 StringSliceVarConfigOnly 方法
func TestStringSliceVarConfigOnly(t *testing.T) {
    var values StringSlice // 用于存储测试标志的值
    flagSet := &FlagSet{
        configOnlyKeys: newInsertionOrderedMap(), // 初始化 configOnlyKeys 映射
        flagKeys:       newInsertionOrderedMap(), // 初始化 flagKeys 映射
    }

    // 添加字符串切片配置值
    defaultValues := []string{"value1", "value2"}
    flagSet.StringSliceVarConfigOnly(&values, "config", defaultValues, "A list of config values")   

    // 验证 flagKeys 中的元数据
    if flagData, exists := flagSet.flagKeys.values["config"]; !exists || flagData.long != "config" {
        t.Error("Expected flagKeys to contain valid metadata for 'config'")
    }
}

func TestIntVarP(t *testing.T) {
	var value int
	flagSet := NewFlagSet() // 假设有一个构造函数来创建 FlagSet 实例

	// 添加一个标志
	flagData := flagSet.IntVarP(&value, "longname", "s", 42, "usage of flag")

	// 检查 FlagData 是否正确
	if flagData.long != "longname" {
		t.Errorf("Expected long name 'longname', but got '%s'", flagData.long)
	}
	if flagData.short != "s" {
		t.Errorf("Expected short name 's', but got '%s'", flagData.short)
	}
	if flagData.defaultValue != "42" {
		t.Errorf("Expected default value '42', but got '%s'", flagData.defaultValue)
	}
	if flagData.usage != "usage of flag" {
		t.Errorf("Expected usage 'usage of flag', but got '%s'", flagData.usage)
	}

}


func TestRuntimeMapVarP(t *testing.T) {
	runtimeMap := &RuntimeMap{}
	flagSet := NewFlagSet() // 假设有一个构造函数来创建 FlagSet 实例

	// 添加一个标志
	defaultValues := []string{"key1=value1", "key2=value2"}
	flagData := flagSet.RuntimeMapVarP(runtimeMap, "longname", "s", defaultValues, "usage of flag")

	// 检查 FlagData 是否正确
	if flagData.long != "longname" {
		t.Errorf("Expected long name 'longname', but got '%s'", flagData.long)
	}
	if flagData.short != "s" {
		t.Errorf("Expected short name 's', but got '%s'", flagData.short)
	}

	if flagData.usage != "usage of flag" {
		t.Errorf("Expected usage 'usage of flag', but got '%s'", flagData.usage)
	}

	// 检查 RuntimeMap 是否正确设置了默认值
	expectedString := "{\"key1\"=\"value1\"=\"key2\"=\"value2\"}"
	resultString := runtimeMap.String()
	if resultString != expectedString {
		t.Errorf("Expected runtime map string %s, but got %s", expectedString, resultString)
	}
}

func TestEnumVarP(t *testing.T) {
	allowed := AllowdTypes{
		"option1": 0,
		"option2": 1,
		"option3": 2,
	}

	var value string
	flagSet := NewFlagSet() // 假设有一个构造函数来创建 FlagSet 实例

	// 添加一个枚举标志
	flagSet.EnumVarP(&value, "option", "o", 0, "Select an option", allowed)

	// 模拟设置有效值
	if err := flagSet.CommandLine.Set("option", "option2"); err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}
	if value != "option2" {
		t.Errorf("Expected value 'option2', got '%s'", value)
	}

	// 测试设置无效值
	err := flagSet.CommandLine.Set("option", "invalid")
	if err == nil {
		t.Fatal("Expected error for invalid value, got none")
	}
	expectedError := "allowed values are option1, option2, option3"
	if err.Error() != expectedError {
		t.Errorf("Expected error message '%s', got '%s'", expectedError, err.Error())
	}
}
