//
// Author: chenjb
// Version: V1.0
// Date: 2025-05-19 20:08:31
// FilePath: /yaml_scan/pkg/hybridMap/file/options.go
// Description:

// Author: chenjb
// Version: V1.0
// Date: 2025-05-16 10:43:44
// FilePath: /yaml_scan/pkg/hybridMap/file/options.go
// Description:
package filekv

var (
	BufferSize = 50 * 1024 * 1024 // 50Mb，用于文件读写的缓冲区大小
	Separator  = ";;;"            // 键值对之间的分隔符，用于在文件中区分键和值
	NewLine    = "\n"             // 行分隔符，用于在文件中区分不同的键值对
	FpRatio    = 0.0001           // 布隆过滤器的误报率
	MaxItems   = uint(250000)     // 默认的最大项目数
)

// Options 文件数据库的配置选项
type Options struct {
	Path           string                 // 数据库文件的存储路径
	Compress       bool                   // 是否启用压缩存储
	MaxItems       uint                   // 最大项目数，用于初始化内存数据结构的大小
	Cleanup        bool                   // 关闭时是否清理文件，如果为true则在关闭数据库时删除文件
	SkipEmpty      bool                   // 是否跳过空键，如果为true则不存储键为空的项目
	FilterCallback func(k, v []byte) bool // 过滤回调函数，用于自定义过滤逻辑，返回true表示跳过该项
	Dedupe         Strategy               // 去重策略，决定使用哪种方式检测和处理重复项
}

// Stats 数据库操作的统计信息
type Stats struct {
	NumberOfFilteredItems uint // 被过滤的项目数量
	NumberOfAddedItems    uint // 添加到临时数据库的项目数量
	NumberOfDupedItems    uint // 检测到的重复项数量
	NumberOfItems         uint // 最终存储在主数据库的项目数量
}

// DefaultOptions 提供了文件数据库的默认配置
var DefaultOptions Options = Options{
	Compress:  false,     // 默认不压缩
	Cleanup:   true,      // 默认在关闭时清理文件
	Dedupe:    MemoryLRU, // 默认使用内存LRU缓存进行去重
	SkipEmpty: true,      // 默认跳过空键
}
