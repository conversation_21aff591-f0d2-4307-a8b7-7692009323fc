//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-15 16:06:18
//FilePath: /yaml_scan/pkg/input/formats/openapi/openapi.go
//Description:

package openapi

import (
	"yaml_scan/pkg/input/formats"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/pkg/errors"
)

// OpenAPIFormat 是一个用于解析OpenAPI Schema文件的结构体
// 它实现了formats.Format接口，提供了解析OpenAPI文件的功能。
type OpenAPIFormat struct {
	opts formats.InputFormatOptions
}

// New 创建并返回一个新的OpenAPIFormat实例
// 返回一个指向OpenAPIFormat的指针。
func New() *OpenAPIFormat {
	return &OpenAPIFormat{}
}

// 确保OpenAPIFormat实现了formats.Format接口
var _ formats.Format = &OpenAPIFormat{}

// Name 返回格式的名称
// 该方法实现了formats.Format接口中的Name方法，返回一个字符串，表示格式的名称。
func (j *OpenAPIFormat) Name() string {
	return "openapi"
}

// SetOptions 设置OpenAPIFormat的选项
func (j *OpenAPIFormat) SetOptions(options formats.InputFormatOptions) {
	j.opts = options
}

// Parse: 解析输入的OpenAPI文件，并为每个发现的RawRequest调用提供的回调函数
//
//	@receiver j *OpenAPIFormat:
//	@param input string: 表示要解析的OpenAPI文件的路径。
//	@param resultsCb formats.ParseReqRespCallback: 回调函数，用于处理解析出的请求和响应。
//	@return error error: 如果解析过程中出现错误，返回一个error对象，否则返回nil。
//
// 该方法使用kin-openapi库加载并解析OpenAPI 3.0文件，然后调用GenerateRequestsFromSchema函数生成请求。
func (j *OpenAPIFormat) Parse(input string, resultsCb formats.ParseReqRespCallback) error {
	// 创建一个新的OpenAPI 3.0加载器
	loader := openapi3.NewLoader()
	// 使用加载器从文件中加载OpenAPI 3.0 schema
	schema, err := loader.LoadFromFile(input)
	if err != nil {
		return errors.Wrap(err, "could not decode openapi 3.0 schema")
	}
	// 生成请求
	return GenerateRequestsFromSchema(schema, j.opts, resultsCb)
}
