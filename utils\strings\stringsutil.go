//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-13 16:39:03
//FilePath: /yaml_scan/utils/strings/stringsutil.go
//Description: 字符串工具包

package stringsutil

import (
	"strings"
)

// ContainsAny 如果字符串 s 包含任何指定的子字符串。返回 true
func ContainsAny(s string, ss ...string) bool {
	for _, sss := range ss {
		// 检查字符串 s 是否包含当前的子字符串 sss
		if strings.Contains(s, sss) {
			return true
		}
	}
	return false
}

// ContainsAnyI 如果字符串 s 包含任何指定的子字符串（不区分大小写）。返回 true
func ContainsAnyI(s string, ss ...string) bool {
	s = strings.ToLower(s)
	for _, sss := range ss {
		if strings.Contains(s, strings.ToLower(sss)) {
			return true
		}
	}
	return false
}

// EqualFoldAny 如果字符串 s 与任何指定的子字符串相等，返回 true
// 比较时不区分大小写。
func EqualFoldAny(s string, ss ...string) bool {
	for _, sss := range ss {
		if strings.EqualFold(s, sss) {
			return true
		}
	}
	return false
}

// HasPrefixAny: 检查字符串 s 是否以指定的任意前缀开头。
//
//	@param s string: 要检查的字符串。
//	@param prefixes ...string:一个可变参数列表，包含多个前缀字符串。
//	@return bool bool: 如果 s 以任意一个前缀开头，则返回 true；否则返回 false。
func HasPrefixAny(s string, prefixes ...string) bool {
	for _, prefix := range prefixes {
		if strings.HasPrefix(s, prefix) {
			return true
		}
	}
	return false
}

// TrimPrefixAny: 按顺序修剪字符串中的所有前缀
//
//	@param s string: 要处理的字符串。
//	@param prefixes ...string: 要修剪的前缀列表。
//	@return string string: 修剪后的字符串。
func TrimPrefixAny(s string, prefixes ...string) string {
	for _, prefix := range prefixes {
		s = strings.TrimPrefix(s, prefix)
	}
	return s
}

// TrimSuffixAny: 按顺序修剪字符串中的所有后缀
//
//	@param s string: 要处理的字符串。
//	@param suffixes ...string:  要修剪的后缀列表。
//	@return string string: 修剪后的字符串。
func TrimSuffixAny(s string, suffixes ...string) string {
	for _, suffix := range suffixes {
		s = strings.TrimSuffix(s, suffix)
	}
	return s
}

// Reverse: 反转给定的字符串并返回反转后的字符串。
//
//	@param s string:
//	@return string string:
func Reverse(s string) string {
	n := 0
	// 创建一个足够大的 rune 切片以存储字符串中的所有字符
	rune := make([]rune, len(s))
	// 将字符串中的每个字符转换为 rune，并存储在切片中
	for _, r := range s {
		rune[n] = r
		n++
	}
	// 截取切片以去除未使用的部分
	rune = rune[0:n]
	// 反转 rune 切片
	for i := 0; i < n/2; i++ {
		rune[i], rune[n-1-i] = rune[n-1-i], rune[i]
	}
	return string(rune)
}

// SplitAny: 根据多个分隔符将字符串拆分为子字符串。
//
//	@param s string: 输入的字符串。
//	@param seps ...string: 分隔符列表。
//	@return []string []string:拆分后的字符串切片。
func SplitAny(s string, seps ...string) []string {
	sepsStr := strings.Join(seps, "")
	
	splitter := func(r rune) bool {
		return strings.ContainsRune(sepsStr, r)
	}
	return strings.FieldsFunc(s, splitter)
}
