//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-15 16:47:18
//FilePath: /yaml_scan/utils/generic/generic_test.go
//Description:

package generic

import (
	"testing"
)



// TestEqualsAny 测试 EqualsAny 函数，使用不同的类型和值。
func TestEqualsAny(t *testing.T) {
	// 定义测试用例
	tests := []struct {
		base     interface{}   // 基础值
		values   []interface{} // 要比较的值
		expected bool          // 预期结果
	}{
		{base: 1, values: []interface{}{1, 2, 3}, expected: true}, // 测试整数
		{base: 4, values: []interface{}{1, 2, 3}, expected: false}, // 测试整数不匹配
		{base: "hello", values: []interface{}{"hello", "world"}, expected: true}, // 测试字符串
		{base: "golang", values: []interface{}{"hello", "world"}, expected: false}, // 测试字符串不匹配
		{base: 3.14, values: []interface{}{3.14, 2.71}, expected: true}, // 测试浮点数
		{base: 1.618, values: []interface{}{3.14, 2.71}, expected: false}, // 测试浮点数不匹配
	}

	// 遍历每个测试用例
	for _, test := range tests {
		// 调用 EqualsAny 函数并获取结果
		result := EqualsAny(test.base, test.values...)
		// 检查结果是否与预期相符
		if result != test.expected {
			// 如果不符，输出错误信息
			t.Errorf("EqualsAny(%v, %v) = %v; expected %v", test.base, test.values, result, test.expected)
		}
	}
}