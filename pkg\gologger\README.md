# gologger
一个高效、灵活且易于使用的日志记录系统，能够支持多种日志级别、格式化和输出方式。系统应能够处理结构化数据，并允许用户根据需要自定义日志记录行为。

## 主要功能
- 多级别日志记录: 支持不同的日志级别（如 INFO、ERROR、DEBUG 等）。
- 格式化输出: 提供多种格式化方式，允许用户自定义日志的输出格式。
- 灵活的输出方式: 支持将日志输出到不同的目标（如控制台、文件等）。
- 元数据支持: 允许用户在日志中添加附加信息（如时间戳、标签等），以便于后续分析和调试。

## 主要组件
- Logger: 日志记录器，负责管理日志的输出、格式化和级别控制。
- Event: 日志事件，包含日志的具体信息（如消息、级别、元数据等）。
- Formatter: 格式化器，负责将日志事件格式化为字符串。
- Writer: 写入器，负责将格式化后的日志输出到指定的目标（如控制台或文件）。
- Levels: 日志级别，定义了不同的日志严重性

## 组件间关系

- Logger 持有一个 Formatter 和一个 Writer，并根据设置的最大日志级别来决定是否记录某个 Event。
- Event 包含了日志的具体信息，并可以通过 Logger 进行记录。
- Formatter 负责将 Event 格式化为字符串，Writer 则负责将格式化后的字符串输出。

## 流程

```mermaid
graph TD
    A[开始] --> B[创建日志事件]
    B --> C[设置元数据]
    C --> D[记录日志]
    D --> E[检查日志级别]
    
    E -->|是| F[去除换行符]
    E -->|否| Z[结束]
    
    F --> G[格式化日志事件]
    G --> H[判断格式化是否成功]
    
    H -->|成功| I[写入日志]
    H -->|失败| Z[结束]
    
    I --> J[检查致命错误]
    
    J -->|是| K[退出]
    J -->|否| Z[结束]
```
1.创建日志事件:

- 调用 Info()、Error() 等函数创建一个新的日志事件。这些函数会调用 newDefaultEventWithLevel，生成一个新的 Event 实例，并设置默认的日志级别和标签。

2.设置元数据:

- 在创建日志事件后，可以通过 Str() 方法添加附加的元数据，或者通过 Msg()、Msgf() 等方法设置日志消息。

3.记录日志:

- 当调用 Msg() 或 Msgf() 方法时，事件会被传递给 Logger 的 Log() 方法。
- 在 Log() 方法中，首先检查事件的级别是否被启用（即是否小于或等于最大级别）。如果未启用，则直接返回，不记录该事件。

4.格式化日志:

- 如果事件的级别被启用，Log() 方法会调用格式化器的 Format() 方法，将事件格式化为字符串。

5.输出日志:

- 格式化后的日志字符串通过写入器的 Write() 方法输出到指定的目标（如控制台或文件）。

6.处理致命错误:

- 如果事件的级别是 LevelFatal，则调用 os.Exit(1) 退出程序，表示发生了致命错误。

## 使用示例

```go
package main

import (
	"strconv"

	"yaml_scan/pkg/gologger"
	"yaml_scan/pkg/gologger/levels"
)

func main() {
	gologger.DefaultLogger.SetMaxLevel(levels.LevelDebug)
	//	gologger.DefaultLogger.SetFormatter(&formatter.JSON{})
	gologger.Print().Msgf("\tgologger: sample test\t\n")
	gologger.Info().Str("user", "pdteam").Msg("running simulation program")
	for i := 0; i < 10; i++ {
		gologger.Info().Str("count", strconv.Itoa(i)).Msg("running simulation step...")
	}
	gologger.Debug().Str("state", "running").Msg("planner running")
	gologger.Warning().Str("state", "errored").Str("status", "404").Msg("could not run")
	gologger.Fatal().Msg("bye bye")
}
```