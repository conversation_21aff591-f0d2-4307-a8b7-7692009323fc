//
// Author: chenjb
// Version: V1.0
// Date: 2025-07-22 17:48:14
// FilePath: /yaml_scan/pkg/rawhttp/proxy/proxy.go
// Description: 代理功能基础定义，提供代理相关的类型和接口

// Package proxy 提供HTTP和SOCKS5代理支持
// 包含代理连接建立和管理的基础功能
package proxy

import (
	"net" // 网络包
)

// DialFunc 代理拨号函数类型定义
// 用于通过代理建立到目标地址的连接
// 参数:
//   addr: 目标服务器地址（格式：host:port）
// 返回:
//   net.Conn: 建立的网络连接
//   error: 连接错误，成功时为nil
// 功能: 定义了代理拨号的标准接口，支持不同类型的代理实现
type DialFunc func(addr string) (net.Conn, error)