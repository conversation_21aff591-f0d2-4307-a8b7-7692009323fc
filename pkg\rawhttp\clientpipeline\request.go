// Author: chenjb
// Version: V1.0
// Date: 2025-07-22 19:20:20
// FilePath: /yaml_scan/pkg/rawhttp/clientpipeline/request.go
// Description: HTTP请求结构定义和处理功能，支持管道化请求的构建和序列化

// Package clientpipeline HTTP请求处理模块
package clientpipeline

import (
	"bufio"                         // 缓冲I/O包
	"bytes"                         // 字节操作包
	"fmt"                           // 格式化输出包
	"io"                            // 输入输出接口包
	"strings"                       // 字符串处理包
	"yaml_scan/pkg/rawhttp/client"  // HTTP客户端包
)

// 预定义的HTTP版本常量
var (
	HTTP_1_0 = Version{Major: 1, Minor: 0} // HTTP/1.0版本
	HTTP_1_1 = Version{Major: 1, Minor: 1} // HTTP/1.1版本
)

// Version HTTP协议版本结构体
// 表示HTTP协议的主版本号和次版本号
type Version struct {
	Major int // 主版本号
	Minor int // 次版本号
}

// String 返回HTTP版本的字符串表示
// 返回:
//   string: HTTP版本字符串（如"HTTP/1.1"）
// 功能: 实现fmt.Stringer接口，格式化版本信息
func (v *Version) String() string {
	return fmt.Sprintf("HTTP/%d.%d", v.Major, v.Minor)
}

// Header HTTP头部结构体
// 表示单个HTTP头部的键值对
type Header struct {
	Key   string // 头部名称（如"Content-Type"）
	Value string // 头部值（如"application/json"）
}

// Request HTTP请求结构体
// 表示完整的HTTP请求，包含所有必要的组件
type Request struct {
	AutomaticContentLength bool      // 是否自动计算Content-Length头部
	AutomaticHost          bool      // 是否自动添加Host头部
	Method                 string    // HTTP请求方法（GET、POST等）
	Path                   string    // 请求路径
	Query                  []string  // 查询参数数组
	Version                         // 嵌入HTTP版本信息

	Headers []Header  // HTTP头部数组

	Body io.Reader    // 请求体读取器
}

// ContentLength 返回请求体的长度
// 返回:
//   int64: 请求体长度，如果长度未知则返回-1
// 功能: 计算请求体的字节长度，支持bytes.Buffer和strings.Reader类型
func (r *Request) ContentLength() int64 {
	// TODO(dfc) this should support anything with a Len() int64 method.
	// 如果没有请求体，返回-1
	if r.Body == nil {
		return -1
	}
	// 根据请求体类型计算长度
	switch b := r.Body.(type) {
	case *bytes.Buffer:
		return int64(b.Len()) // 返回字节缓冲区的长度
	case *strings.Reader:
		return int64(b.Len()) // 返回字符串读取器的长度
	default:
		return -1 // 其他类型无法确定长度
	}
}

// Write 将HTTP请求写入缓冲写入器
// 参数:
//   w: 缓冲写入器
// 返回:
//   error: 写入错误，成功时为nil
// 功能: 将完整的HTTP请求序列化并写入到写入器中
func (r *Request) Write(w *bufio.Writer) error {
	// 构建查询字符串
	q := strings.Join(r.Query, "&")
	if len(q) > 0 {
		q = "?" + q // 添加查询字符串前缀
	}
	// 写入请求行：方法 路径 版本
	if _, err := fmt.Fprintf(w, "%s %s%s %s\r\n", r.Method, r.Path, q, r.Version.String()); err != nil {
		return err
	}

	// 写入所有请求头部
	for _, h := range r.Headers {
		var err error
		if h.Value != "" {
			// 有值的头部：键: 值
			_, err = fmt.Fprintf(w, "%s: %s\r\n", h.Key, h.Value)
		} else {
			// 无值的头部：仅键
			_, err = fmt.Fprintf(w, "%s\r\n", h.Key)
		}
		if err != nil {
			return err
		}
	}

	// 处理自动Content-Length计算
	l := r.ContentLength()
	if r.AutomaticContentLength {
		if l >= 0 {
			// 如果能确定内容长度，添加Content-Length头部
			if _, err := fmt.Fprintf(w, "Content-Length: %d", l); err != nil {
				return err
			}
		}
	}

	// 如果没有请求体，只发送结束标记
	if r.Body == nil {
		// doesn't actually start the body, just sends the terminating \r\n
		// 不实际开始请求体，只发送终止的\r\n
		_, err := fmt.Fprintf(w, client.NewLine)
		return err
	}

	// TODO(dfc) Version should implement comparable so we can say version >= HTTP_1_1
	// TODO: 版本应该实现可比较接口，这样我们可以说version >= HTTP_1_1
	// if r.Version.Major == 1 && r.Version.Minor == 1 {
	// 	if l < 0 {
	// 		if _, err := fmt.Fprintf(w, "Transfer-Encoding: chunked\r\n"); err != nil {
	// 			return err
	// 		}
	// 		if _, err := fmt.Fprintf(w, client.NewLine); err != nil {
	// 			return err
	// 		}
	// 		cw := httputil.NewChunkedWriter(w)
	// 		_, err := io.Copy(cw, r.Body)
	// 		return err
	// 	}
	// }
	// 写入空行分隔头部和请求体
	if _, err := fmt.Fprintf(w, client.NewLine); err != nil {
		return err
	}
	// 复制请求体内容
	_, err := io.Copy(w, r.Body)
	return err
}

// ToRequest 创建HTTP请求对象
// 参数:
//   method: HTTP请求方法
//   path: 请求路径
//   query: 查询参数数组
//   headers: HTTP头部映射
//   body: 请求体读取器
// 返回:
//   *Request: 创建的HTTP请求对象
// 功能: 根据提供的参数构建完整的HTTP请求对象，默认使用HTTP/1.1版本
func ToRequest(method string, path string, query []string, headers map[string][]string, body io.Reader) *Request {
	return &Request{
		Method:  method,              // 设置请求方法
		Path:    path,                // 设置请求路径
		Query:   query,               // 设置查询参数
		Version: HTTP_1_1,            // 默认使用HTTP/1.1版本
		Headers: toHeaders(headers),  // 转换头部格式
		Body:    body,                // 设置请求体
	}
}

// toHeaders 将映射格式的头部转换为Header数组
// 参数:
//   h: 头部映射，键为头部名称，值为头部值数组
// 返回:
//   []Header: 转换后的头部数组
// 功能: 将标准头部映射格式转换为内部使用的Header数组格式
func toHeaders(h map[string][]string) []Header {
	var r []Header
	// 遍历头部映射
	for k, v := range h {
		// 为每个头部值创建一个Header对象
		for _, v := range v {
			r = append(r, Header{Key: k, Value: v})
		}
	}
	return r // 返回头部数组
}

