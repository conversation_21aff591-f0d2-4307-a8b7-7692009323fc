// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 19:35:07
// FilePath: /yaml_scan/utils/reader/frozen_reader_test.go
// Description: 
package reader

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

func TestFrozenReader(t *testing.T) {
	// 测试FrozenReader的Read方法
	t.Run("FrozenReader应该永不返回", func(t *testing.T) {
		r := require.New(t)

		// 创建一个FrozenReader
		frozenReader := FrozenReader{}

		// 创建一个缓冲区
		buffer := make([]byte, 10)

		// 创建一个带有超时的上下文
		ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
		defer cancel()

		// 在goroutine中调用Read，这应该会阻塞
		readDone := make(chan struct{})
		var bytesRead int
		var readErr error

		go func() {
			bytesRead, readErr = frozenReader.Read(buffer)
			close(readDone)
		}()

		// 检查是否在超时前完成
		select {
		case <-readDone:
			// 如果Read返回，这不是期望的行为
			t.Fatal("FrozenReader.Read unexpectedly returned")
		case <-ctx.Done():
			// 这是期望的行为，Read应该不返回直到超时
			r.Equal(0, bytesRead, "读取的字节数应该为0")
			r.Nil(readErr, "错误应该为nil")
		}
	})
}


