// Author: chenjb
// Version: V1.0
// Date: 2025-06-11 17:14:35
// FilePath: /yaml_scan/pkg/retryablehttp/digest_auth_client/www_authenticate_test.go
// Description:
package digestauthclient

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestNewWwwAuthenticate 测试从WWW-Authenticate头解析认证信息的功能
func TestNewWwwAuthenticate(t *testing.T) {
	// 定义测试用例
	tests := []struct {
		name     string           // 测试用例名称
		header   string           // WWW-Authenticate头值
		expected *wwwAuthenticate // 期望解析结果
	}{
		{
			name: "完整的摘要认证头",
			header: `Digest realm="<EMAIL>",
				qop="auth,auth-int",
				nonce="dcd98b7102dd2f0e8b11d0f600bfb0c093",
				opaque="5ccc069c403ebaf9f0171e9517f40e41",
				algorithm="MD5",
				domain="/",
				charset="UTF-8",
				userhash=true`,
			expected: &wwwAuthenticate{
				Realm:     "<EMAIL>",
				Qop:       "auth,auth-int",
				Nonce:     "dcd98b7102dd2f0e8b11d0f600bfb0c093",
				Opaque:    "5ccc069c403ebaf9f0171e9517f40e41",
				Algorithm: "MD5",
				Domain:    "/",
				Charset:   "UTF-8",
				Userhash:  false,
			},
		},
		{
			name:   "最小摘要认证头",
			header: `Digest realm="<EMAIL>", nonce="dcd98b7102dd2f0e8b11d0f600bfb0c093"`,
			expected: &wwwAuthenticate{
				Realm: "<EMAIL>",
				Nonce: "dcd98b7102dd2f0e8b11d0f600bfb0c093",
			},
		},
		{
			name:   "使用SHA-256算法",
			header: `Digest realm="<EMAIL>", nonce="123456", algorithm="SHA-256"`,
			expected: &wwwAuthenticate{
				Realm:     "<EMAIL>",
				Nonce:     "123456",
				Algorithm: "SHA-256",
			},
		},
		{
			name:   "stale=true参数",
			header: `Digest realm="<EMAIL>", nonce="123456", stale=true"`,
			expected: &wwwAuthenticate{
				Realm: "<EMAIL>",
				Nonce: "123456",
				Stale: false,
			},
		},
		{
			name:   "stale=false参数",
			header: `Digest realm="<EMAIL>", nonce="123456", stale=false"`,
			expected: &wwwAuthenticate{
				Realm: "<EMAIL>",
				Nonce: "123456",
				Stale: false,
			},
		},
		{
			name:   "userhash=false参数",
			header: `Digest realm="<EMAIL>", nonce="123456", userhash=false"`,
			expected: &wwwAuthenticate{
				Realm:    "<EMAIL>",
				Nonce:    "123456",
				Userhash: false,
			},
		},
	}

	// 执行测试用例
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 调用被测函数
			got := newWwwAuthenticate(tt.header)

			// 验证结果
			assert.Equal(t, tt.expected.Realm, got.Realm, "realm不匹配")
			assert.Equal(t, tt.expected.Nonce, got.Nonce, "nonce不匹配")
			assert.Equal(t, tt.expected.Algorithm, got.Algorithm, "algorithm不匹配")
			assert.Equal(t, tt.expected.Opaque, got.Opaque, "opaque不匹配")
			assert.Equal(t, tt.expected.Qop, got.Qop, "qop不匹配")
			assert.Equal(t, tt.expected.Domain, got.Domain, "domain不匹配")
			assert.Equal(t, tt.expected.Charset, got.Charset, "charset不匹配")
			assert.Equal(t, tt.expected.Stale, got.Stale, "stale不匹配")
			assert.Equal(t, tt.expected.Userhash, got.Userhash, "userhash不匹配")
		})
	}
}

// TestNewWwwAuthenticateEmptyHeader 测试空头信息的处理
func TestNewWwwAuthenticateEmptyHeader(t *testing.T) {
	// 调用被测函数
	wa := newWwwAuthenticate("")

	// 验证所有字段都是空值
	assert.Empty(t, wa.Realm)
	assert.Empty(t, wa.Nonce)
	assert.Empty(t, wa.Algorithm)
	assert.Empty(t, wa.Opaque)
	assert.Empty(t, wa.Qop)
	assert.Empty(t, wa.Domain)
	assert.Empty(t, wa.Charset)
	assert.False(t, wa.Stale)
	assert.False(t, wa.Userhash)
}
