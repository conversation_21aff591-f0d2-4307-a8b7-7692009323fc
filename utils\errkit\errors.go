// Author: chenjb
// Version: V1.0
// Date: 2025-05-20 19:13:24
// FilePath: /yaml_scan/utils/errkit/errors.go
// Description: 定义了ErrorX错误类型及其相关方法，提供了丰富的错误处理功能
package errkit

import (
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"runtime"
	"strconv"
	"strings"
	"time"
)

const (
	// DelimArrow 用于连接错误的分隔符
	DelimArrow = "<-"
	// DelimArrowSerialized 是序列化后的箭头分隔符
	DelimArrowSerialized = "\u003c-"
	// DelimSemiColon 是标准的分号分隔符，常用于连接错误
	DelimSemiColon = "; "
	// DelimMultiLine 是用于多行格式连接错误的分隔符
	DelimMultiLine = "\n -  "
	// MultiLinePrefix 是多行错误使用的前缀
	MultiLineErrPrefix = "the following errors occurred:"
	// Space 是用于缩进的标识符
	Space = " "
)

var (
	// MaxErrorDepth 是要展开或维护的最大错误深度
	// 超过此深度的所有错误将被忽略
	MaxErrorDepth = 5
	// FieldSeperator 是错误字段的分隔符
	ErrFieldSeparator = Space
	// ErrChainSeperator 是错误链的分隔符
	ErrChainSeperator = DelimSemiColon
	// EnableTimestamp 控制是否包含错误时间戳
	EnableTimestamp = true
	// EnableTrace 控制是否包含错误堆栈跟踪
	EnableTrace = true
)

// ErrorX 是一种自定义错误类型，可以处理所有已知类型的错误
// 包装和连接策略，包括自定义策略，并支持错误类别
// 可以以更有意义的方式向客户端/用户显示
type ErrorX struct {
	kind   ErrKind      // kind 表示错误的类型
	record *slog.Record //存储错误的属性记录
	source *slog.Source // 存储错误的源代码位置信息
	errs   []error      //存储所有相关的错误
}

// init 初始化ErrorX实例
// @receiver e
// @param skipStack ...int:  参数可选，控制堆栈跟踪的跳过层数
func (e *ErrorX) init(skipStack ...int) {
	// 必要时初始化
	if e.record == nil {
		// 初始化记录
		e.record = &slog.Record{}
		// 如果启用时间戳，记录当前时间
		if EnableTimestamp {
			e.record.Time = time.Now()
		}
		// 如果启用跟踪，获取函数调用信息
		if EnableTrace {
			// 获取函数名
			var pcs [1]uintptr
			// 跳过[runtime.Callers, ErrorX.init, parent]
			skip := 3
			if len(skipStack) > 0 {
				skip = skipStack[0]
			}
			// 获取调用栈
			runtime.Callers(skip, pcs[:])
			pc := pcs[0]
			fs := runtime.CallersFrames([]uintptr{pc})
			f, _ := fs.Next()
			// 记录源信息
			e.source = &slog.Source{
				Function: f.Function,
				File:     f.File,
				Line:     f.Line,
			}
		}
	}
}

// append 是内部方法，用于将给定的错误添加到错误切片中，它会去除重复项
// @receiver e
// @param errs ...error:
func (e *ErrorX) append(errs ...error) {
	// 遍历要添加的每个错误
	for _, nerr := range errs {
		found := false
	new:
		// 检查是否已存在相同错误
		for _, oerr := range e.errs {
			if oerr.Error() == nerr.Error() {
				found = true
				break new
			}
		}
		// 如果没有找到重复项，则添加
		if !found {
			e.errs = append(e.errs, nerr)
		}
	}
}

// MarshalJSON 实现json.Marshaler接口，将ErrorX序列化为JSON
// @receiver e
// @return []byte []byte: 序列化后数据
// @return error error: 可能得错误
func (e ErrorX) MarshalJSON() ([]byte, error) {
	// 创建临时字符串切片存储错误信息
	tmp := []string{}
	for _, err := range e.errs {
		tmp = append(tmp, err.Error())
	}

	// 如果kind未设置，设为未知类型
	if e.kind == nil {
		e.kind = ErrKindUnknown
	}
	// 创建要序列化的map
	m := map[string]interface{}{
		"kind":   e.kind.String(),
		"errors": tmp,
	}
	// 如果有属性，添加到map
	if e.record != nil && e.record.NumAttrs() > 0 {
		m["attrs"] = slog.GroupValue(e.Attrs()...)
	}
	// 如果有源信息，添加到map
	if e.source != nil {
		m["source"] = e.source
	}
	return json.Marshal(m)
}

// Cause  返回导致此错误的原始错误，不包含任何包装
// @receiver e
// @return error error:
func (e *ErrorX) Cause() error {
	if len(e.errs) > 0 {
		return e.errs[0]
	}
	return nil
}

// Errors  返回错误解析的所有错误
// @receiver e
// @return []error []error:
func (e *ErrorX) Errors() []error {
	return e.errs
}

// Attrs 返回与错误关联的所有属性
// @receiver e
// @return []slog.Attr []slog.Attr:
func (e *ErrorX) Attrs() []slog.Attr {
	// 如果没有记录或属性，返回nil
	if e.record == nil || e.record.NumAttrs() == 0 {
		return nil
	}
	values := []slog.Attr{}
	// 遍历所有属性并添加到切片
	e.record.Attrs(func(a slog.Attr) bool {
		values = append(values, a)
		return true
	})
	return values
}


func (e *ErrorX) SetAttr(s ...slog.Attr) *ErrorX {
	e.init()
	for _, attr := range s {
		e.record.Add(attr)
	}
	return e
}

// Build 将对象作为error接口返回
func (e *ErrorX) Build() error {
	return e
}

// Unwrap 返回底层错误数组，实现JoinedError接口
func (e *ErrorX) Unwrap() []error {
	return e.errs
}

// Is 检查当前错误是否包含给定错误，实现ComparableError接口
func (e *ErrorX) Is(err error) bool {
	// 创建新的ErrorX并初始化
	x := &ErrorX{}
	x.init()
	// 解析给定的错误
	parseError(x, err)
	// 只要有一个子错误匹配就足够
	for _, orig := range e.errs {
		for _, match := range x.errs {
			if errors.Is(orig, match) {
				return true
			}
		}
	}
	return false
}

// Error 返回错误字符串，实现error接口
// @receiver e
// @return string string:
func (e *ErrorX) Error() string {
	// 创建字符串构建器
	var sb strings.Builder
	// 添加cause字段
	sb.WriteString("cause=")
	sb.WriteString(strconv.Quote(e.errs[0].Error()))
	// 如果有属性，添加到错误字符串
	if e.record != nil && e.record.NumAttrs() > 0 {
		values := []string{}
		e.record.Attrs(func(a slog.Attr) bool {
			values = append(values, a.String())
			return true
		})
		sb.WriteString(Space)
		sb.WriteString(strings.Join(values, " "))
	}
	// 如果有多个错误，添加错误链
	if len(e.errs) > 1 {
		chain := []string{}
		for _, value := range e.errs[1:] {
			chain = append(chain, strings.TrimSpace(value.Error()))
		}
		sb.WriteString(Space)
		sb.WriteString("chain=" + strconv.Quote(strings.Join(chain, ErrChainSeperator)))
	}
	return sb.String()
}

// Kind  返回与此错误关联的错误类型
// @receiver e
// @return ErrKind ErrKind:
func (e *ErrorX) Kind() ErrKind {
	// 如果kind未设置或为空，设为未知类型
	if e.kind == nil || e.kind.String() == "" {
		e.kind = ErrKindUnknown
	}
	return e.kind
}

// FromError 解析给定的错误以了解错误类别
// 并可选择添加给定的消息以获取更多信息
// @param err error:
// @return *ErrorX *ErrorX:
func FromError(err error) *ErrorX {
	if err == nil {
		return nil
	}
	// 创建新的ErrorX并初始化
	errorx := &ErrorX{}
	errorx.init()
	parseError(errorx, err)
	return errorx
}

// New 创建具有给定消息的新错误
// 它遵循slog模式的添加并以相同的方式期望
//
// 示例:
//
//	这是正确的 (√)
//	errkit.New("this is a test error","address",host)
//
//	这是不可读的/不推荐的 (x)
//	errkit.New("this is a test error",slog.String("address",host))
//
//	这是错误的 (x)
//	errkit.New("this is a test error %s",host)
func New(msg string, args ...interface{}) *ErrorX {
	// 创建新的ErrorX并初始化
	e := &ErrorX{}
	e.init()
	// 如果有参数，添加到记录中
	if len(args) > 0 {
		e.record.Add(args...)
	}
	// 添加错误消息
	e.append(errors.New(msg))
	return e
}

// Msgf 向错误添加消息
// 它遵循slog模式的添加并以相同的方式期望
//
// 示例:
//
//	这是正确的 (√)
//	myError.Msgf("dial error","network","tcp")
//
//	这是不可读的/不推荐的 (x)
//	myError.Msgf(slog.String("address",host))
//
//	这是错误的 (x)
//	myError.Msgf("this is a test error %s",host)
func (e *ErrorX) Msgf(format string, args ...interface{}) {
	if e == nil {
		return
	}
	// 根据是否有参数决定如何添加消息
	if len(args) == 0 {
		e.append(errors.New(format))
	}
	e.append(fmt.Errorf(format, args...))
}

// SetKind 设置错误的类别
// 如果底层错误类别已经设置，则在生成最终错误消息时会优先考虑
//
//	示例:
//
//	这是正确的 (√)
//	myError.SetKind(errkit.ErrKindNetworkPermanent)
func (e *ErrorX) SetKind(kind ErrKind) *ErrorX {
	// 如果kind未设置，直接设置
	if e.kind == nil {
		e.kind = kind
	} else {
		// 否则，合并错误类型
		e.kind = CombineErrKinds(e.kind, kind)
	}
	return e
}

// ResetKind 重置错误的错误类别
//
//	示例:
//
//	myError.ResetKind()
func (e *ErrorX) ResetKind() *ErrorX {
	e.kind = nil
	return e
}

// parseError 递归解析所有已知类型的错误
func parseError(to *ErrorX, err error) {
	// 防止外部库调用中的panic
	defer func() {
		if r := recover(); r != nil {
			// 将panic转换为错误并附加为最后一个错误
			var panicErr error
			switch v := r.(type) {
			case error:
				panicErr = fmt.Errorf("error while unwrapping: %w", v)
			case string:
				panicErr = fmt.Errorf("error while unwrapping: %s", v)
			default:
				panicErr = fmt.Errorf("error while unwrapping: panic: %v", r)
			}
			to.append(panicErr)
		}
	}()

	if err == nil {
		return
	}

	// 如果目标ErrorX为空，创建并初始化
	if to == nil {
		to = &ErrorX{}
		to.init(4)
	}
	// 如果已达到最大错误深度，返回
	if len(to.errs) >= MaxErrorDepth {
		return
	}

	// 根据错误类型进行不同处理
	switch v := err.(type) {
	case *ErrorX:
		// 如果是ErrorX类型，直接添加其错误
		to.append(v.errs...)
		// 合并属性记录
		if v.record != nil {
			if to.record == nil {
				to.record = v.record
			} else {
				v.record.Attrs(func(a slog.Attr) bool {
					to.record.Add(a)
					return true
				})
			}
		}
		// 合并源信息
		if to.source == nil && v.source != nil {
			to.source = v.source
		}
		// 合并错误类型
		to.kind = CombineErrKinds(to.kind, v.kind)
	case JoinedError:
		// 如果是JoinedError类型，展开所有错误
		foundAny := false
		for _, e := range v.Unwrap() {
			to.append(e)
			foundAny = true
		}
		// 如果没有找到任何错误，解析错误消息
		if !foundAny {
			parseError(to, errors.New(err.Error()))
		}
	case WrappedError:
		// 如果是WrappedError类型，展开错误
		if v.Unwrap() != nil {
			parseError(to, v.Unwrap())
		} else {
			parseError(to, errors.New(err.Error()))
		}
	case CauseError:
		// 如果是CauseError类型，添加原因并解析剩余部分
		to.append(v.Cause())
		remaining := strings.Replace(err.Error(), v.Cause().Error(), "", -1)
		parseError(to, errors.New(remaining))
	default:
		// 默认情况，尝试解析错误字符串
		errString := err.Error()
		// 尝试分配给富错误
		if strings.Contains(errString, DelimArrow) {
			// 按箭头分隔符拆分错误
			parts := strings.Split(errString, DelimArrow)
			for i := len(parts) - 1; i >= 0; i-- {
				part := strings.TrimSpace(parts[i])
				parseError(to, errors.New(part))
			}
		} else if strings.Contains(errString, DelimArrowSerialized) {
			// 按序列化箭头分隔符拆分错误
			parts := strings.Split(errString, DelimArrowSerialized)
			for i := len(parts) - 1; i >= 0; i-- {
				part := strings.TrimSpace(parts[i])
				parseError(to, errors.New(part))
			}
		} else if strings.Contains(errString, DelimSemiColon) {
			// 按分号分隔符拆分错误
			parts := strings.Split(errString, DelimSemiColon)
			for _, part := range parts {
				part = strings.TrimSpace(part)
				parseError(to, errors.New(part))
			}
		} else if strings.Contains(errString, MultiLineErrPrefix) {
			// 删除前缀
			msg := strings.ReplaceAll(errString, MultiLineErrPrefix, "")
			// 按多行分隔符拆分错误
			parts := strings.Split(msg, DelimMultiLine)
			for _, part := range parts {
				part = strings.TrimSpace(part)
				parseError(to, errors.New(part))
			}
		} else {
			// 这个不能进一步展开
			to.append(err)
		}
	}
}
