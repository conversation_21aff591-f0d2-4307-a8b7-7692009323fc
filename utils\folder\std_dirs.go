//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-21 20:16:15
//FilePath: /yaml_scan/utils/folder/std_dirs.go
//Description:

package folderutil

import (
	"os"
	"path/filepath"
)

// UserConfigDirOrDefault:  获取用户的配置目录。
//
//	@param defaultConfigDir string: 默认配置目录，当无法获取用户配置目录时返回该值。
//	@return string string: 用户配置目录，如果获取失败则返回默认配置目录。
func UserConfigDirOrDefault(defaultConfigDir string) string {
	userConfigDir, err := os.UserConfigDir()
	if err != nil {
		return defaultConfigDir
	}
	return userConfigDir
}

// AppConfigDirOrDefault: 获取应用程序的配置目录
//
//	@param defaultAppConfigDir string:默认应用程序配置目录。
//	@param toolName string: 工具名称，用于构建应用程序配置目录的路径。
//	@return string string: 用程序配置目录。
func AppConfigDirOrDefault(defaultAppConfigDir string, toolName string) string {
	// 获取用户配置目录，如果失败则返回空字符串
	userConfigDir := UserConfigDirOrDefault("")
	// 如果用户配置目录为空，返回默认应用程序配置目录
	if userConfigDir == "" {
		return filepath.Join(defaultAppConfigDir, toolName)
	}
	// 返回用户配置目录与工具名称的组合路径
	return filepath.Join(userConfigDir, toolName)
}
