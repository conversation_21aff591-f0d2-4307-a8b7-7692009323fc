//
// Author: chenjb
// Version: V1.0
// Date: 2025-07-22 17:21:27
// FilePath: /yaml_scan/pkg/rawhttp/client.go
// Description: rawhttp客户端实现，提供原始HTTP请求功能

// Package rawhttp 提供原始HTTP客户端功能，支持自定义HTTP请求和响应处理
package rawhttp

import (
	"fmt"    // 格式化输出包
	"io"     // 输入输出接口包
	"net/http" // 标准HTTP包
	"strings"  // 字符串处理包
	"time"     // 时间处理包
	"yaml_scan/pkg/fastdialer"    // 快速拨号器包
	"yaml_scan/pkg/gologger"      // 日志记录包
	"yaml_scan/pkg/retryablehttp" // 可重试HTTP包
	urlutil "yaml_scan/utils/url" // URL工具包
)

// Client 是用于发送原始HTTP请求的客户端结构体
// 包含拨号器和配置选项，提供完整的HTTP客户端功能
type Client struct {
	dialer  Dialer   // 网络连接拨号器，用于建立TCP连接
	Options *Options // 客户端配置选项，包含超时、代理等设置
}

// AutomaticHostHeader 设置是否自动为请求添加Host头部
// 参数:
//   enable: true表示启用自动Host头部，false表示禁用
// 功能: 控制默认客户端是否自动设置Host头部字段
func AutomaticHostHeader(enable bool) {
	DefaultClient.Options.AutomaticHostHeader = enable // 设置默认客户端的自动Host头部选项
}

// AutomaticContentLength 设置是否自动计算请求内容长度
// 参数:
//   enable: true表示启用自动计算Content-Length，false表示禁用
// 功能: 控制默认客户端是否自动计算并设置Content-Length头部
func AutomaticContentLength(enable bool) {
	DefaultClient.Options.AutomaticContentLength = enable // 设置默认客户端的自动内容长度计算选项
}


// NewClient 创建一个新的rawhttp客户端实例
// 参数:
//   options: 客户端配置选项，包含超时、代理、拨号器等设置
// 返回:
//   *Client: 新创建的客户端实例
// 功能: 初始化客户端，如果未提供FastDialer则创建默认的快速拨号器
func NewClient(options *Options) *Client {
	client := &Client{
		dialer:  new(dialer), // 创建新的拨号器实例
		Options: options,     // 设置客户端配置选项
	}
	// 如果配置中没有提供FastDialer，则创建默认的快速拨号器
	if options.FastDialer == nil {
		var err error
		opts := fastdialer.DefaultOptions      // 获取默认的快速拨号器选项
		opts.DialerTimeout = options.Timeout   // 设置拨号超时时间
		options.FastDialer, err = fastdialer.NewDialer(opts) // 创建新的快速拨号器
		if err != nil {
			// 如果创建快速拨号器失败，记录错误日志
			gologger.Error().Msgf("Could not create fast dialer: %s\n", err)
		}
	}
	return client // 返回初始化完成的客户端实例
}

// Head 发送HEAD请求到指定URL
// 参数:
//   url: 目标URL地址
// 返回:
//   *http.Response: HTTP响应对象
//   error: 错误信息，如果请求成功则为nil
// 功能: 发送HEAD请求，通常用于获取资源的元信息而不下载内容
func (c *Client) Head(url string) (*http.Response, error) {
	return c.DoRaw("HEAD", url, "", nil, nil) // 调用DoRaw方法发送HEAD请求
}

// Get 发送GET请求到指定URL
// 参数:
//   url: 目标URL地址
// 返回:
//   *http.Response: HTTP响应对象
//   error: 错误信息，如果请求成功则为nil
// 功能: 发送GET请求，用于获取指定URL的资源内容
func (c *Client) Get(url string) (*http.Response, error) {
	return c.DoRaw("GET", url, "", nil, nil) // 调用DoRaw方法发送GET请求
}

// Post 发送POST请求到指定URL
// 参数:
//   url: 目标URL地址
//   mimetype: 请求体的MIME类型，如"application/json"
//   body: 请求体内容的读取器
// 返回:
//   *http.Response: HTTP响应对象
//   error: 错误信息，如果请求成功则为nil
// 功能: 发送POST请求，通常用于提交数据到服务器
func (c *Client) Post(url string, mimetype string, body io.Reader) (*http.Response, error) {
	headers := make(map[string][]string)        // 创建HTTP头部映射
	headers["Content-Type"] = []string{mimetype} // 设置Content-Type头部
	return c.DoRaw("POST", url, "", headers, body) // 调用DoRaw方法发送POST请求
}

// Do 发送标准HTTP请求并返回响应
// 参数:
//   req: 标准的http.Request请求对象
// 返回:
//   *http.Response: HTTP响应对象
//   error: 错误信息，如果请求成功则为nil
// 功能: 将标准HTTP请求转换为原始HTTP请求并发送
func (c *Client) Do(req *http.Request) (*http.Response, error) {
	method := req.Method        // 获取请求方法（GET、POST等）
	headers := req.Header       // 获取请求头部
	url := req.URL.String()     // 获取请求URL字符串
	body := req.Body           // 获取请求体

	return c.DoRaw(method, url, "", headers, body) // 调用DoRaw方法发送请求
}

// Dor 发送可重试HTTP请求并返回响应
// 参数:
//   req: retryablehttp.Request可重试请求对象
// 返回:
//   *http.Response: HTTP响应对象
//   error: 错误信息，如果请求成功则为nil
// 功能: 将可重试HTTP请求转换为原始HTTP请求并发送
func (c *Client) Dor(req *retryablehttp.Request) (*http.Response, error) {
	method := req.Method        // 获取请求方法
	headers := req.Header       // 获取请求头部
	url := req.URL.String()     // 获取请求URL字符串
	body := req.Body           // 获取请求体

	return c.DoRaw(method, url, "", headers, body) // 调用DoRaw方法发送请求
}


// DoRaw 执行原始HTTP请求，使用默认配置
// 参数:
//   method: HTTP请求方法（GET、POST、PUT等）
//   url: 目标URL地址
//   uripath: 自定义URI路径，如果为空则使用URL中的路径
//   headers: HTTP请求头部映射
//   body: 请求体内容的读取器
// 返回:
//   *http.Response: HTTP响应对象
//   error: 错误信息，如果请求成功则为nil
// 功能: 发送原始HTTP请求，支持重定向跟踪
func (c *Client) DoRaw(method, url, uripath string, headers map[string][]string, body io.Reader) (*http.Response, error) {
	redirectstatus := &RedirectStatus{
		FollowRedirects: true,                    // 启用重定向跟踪
		MaxRedirects:    c.Options.MaxRedirects, // 设置最大重定向次数
	}
	return c.do(method, url, uripath, headers, body, redirectstatus, c.Options) // 调用内部do方法执行请求
}

// DoRawWithOptions 使用自定义选项执行原始HTTP请求
// 参数:
//   method: HTTP请求方法
//   url: 目标URL地址
//   uripath: 自定义URI路径
//   headers: HTTP请求头部映射
//   body: 请求体内容的读取器
//   options: 自定义的请求选项
// 返回:
//   *http.Response: HTTP响应对象
//   error: 错误信息，如果请求成功则为nil
// 功能: 使用指定的选项发送原始HTTP请求
func (c *Client) DoRawWithOptions(method, url, uripath string, headers map[string][]string, body io.Reader, options *Options) (*http.Response, error) {
	redirectstatus := &RedirectStatus{
		FollowRedirects: options.FollowRedirects, // 使用选项中的重定向设置
		MaxRedirects:    c.Options.MaxRedirects,  // 使用客户端的最大重定向次数
	}
	return c.do(method, url, uripath, headers, body, redirectstatus, options) // 调用内部do方法执行请求
}

// Close 关闭客户端并释放其持有的所有资源
// 功能: 清理客户端资源，包括关闭快速拨号器等
func (c *Client) Close() {
	// 如果存在快速拨号器，则关闭它以释放资源
	if c.Options.FastDialer != nil {
		c.Options.FastDialer.Close()
	}
}

// getConn 获取到目标主机的网络连接
// 参数:
//   protocol: 协议类型（http或https）
//   host: 目标主机地址（包含端口）
//   options: 连接选项配置
// 返回:
//   Conn: 网络连接接口
//   error: 错误信息，如果连接成功则为nil
// 功能: 根据配置建立到目标主机的连接，支持代理和超时设置
func (c *Client) getConn(protocol, host string, options *Options) (Conn, error) {
	// 如果配置了代理，则通过代理建立连接
	if options.Proxy != "" {
		return c.dialer.DialWithProxy(protocol, host, c.Options.Proxy, c.Options.ProxyDialTimeout, options)
	}
	var conn Conn
	var err error
	// 如果设置了超时时间，则使用带超时的拨号
	if options.Timeout > 0 {
		conn, err = c.dialer.DialTimeout(protocol, host, options.Timeout, options)
	} else {
		// 否则使用普通拨号
		conn, err = c.dialer.Dial(protocol, host, options)
	}
	return conn, err // 返回连接和可能的错误
}

// do 执行HTTP请求的核心实现方法
// 参数:
//   method: HTTP请求方法
//   url: 目标URL地址
//   uripath: 自定义URI路径
//   headers: HTTP请求头部映射
//   body: 请求体内容的读取器
//   redirectstatus: 重定向状态跟踪
//   options: 请求选项配置
// 返回:
//   *http.Response: HTTP响应对象
//   error: 错误信息，如果请求成功则为nil
// 功能: 处理HTTP请求的完整流程，包括连接建立、请求发送、响应接收和重定向处理
func (c *Client) do(method, url, uripath string, headers map[string][]string, body io.Reader, redirectstatus *RedirectStatus, options *Options) (*http.Response, error) {
	protocol := "http" // 默认协议为HTTP
	// 检查URL是否以https://开头，确定协议类型
	if strings.HasPrefix(strings.ToLower(url), "https://") {
		protocol = "https"
	}

	// 如果headers为nil，初始化为空映射
	if headers == nil {
		headers = make(map[string][]string)
	}
	// 解析URL，获取各个组件
	u, err := urlutil.ParseURL(url, true)
	if err != nil {
		return nil, err // 如果URL解析失败，返回错误
	}

	host := u.Host // 获取主机地址
	// 如果启用了自动Host头部，则添加Host头部（带前导空格）
	if options.AutomaticHostHeader {
		// add automatic space
		headers["Host"] = []string{fmt.Sprintf(" %s", host)}
	}

	// 如果主机地址不包含端口号，则添加默认端口
	if !strings.Contains(host, ":") {
		if protocol == "https" {
			host += ":443" // HTTPS默认端口443
		} else {
			host += ":80"  // HTTP默认端口80
		}
	}

	// standard path - 构建标准路径
	path := u.Path
	if path == "" {
		path = "/" // 如果路径为空，设置为根路径
	}
	// 如果存在查询参数，添加到路径中
	if !u.Params.IsEmpty() {
		path += "?" + u.Params.Encode()
	}
	// override if custom one is specified - 如果指定了自定义路径，则覆盖
	if uripath != "" {
		path = uripath
	}

	// 再次检查URL前缀确定协议（冗余检查，确保协议正确）
	if strings.HasPrefix(url, "https://") {
		protocol = "https"
	}

	// 获取到目标主机的连接
	conn, err := c.getConn(protocol, host, options)
	if err != nil {
		return nil, err // 如果连接失败，返回错误
	}

	// 构建HTTP请求对象
	req := toRequest(method, path, nil, headers, body, options)
	req.AutomaticContentLength = options.AutomaticContentLength // 设置自动内容长度计算
	req.AutomaticHost = options.AutomaticHostHeader             // 设置自动Host头部

	// set timeout if any - 如果设置了超时时间，则设置连接截止时间
	if options.Timeout > 0 {
		_ = conn.SetDeadline(time.Now().Add(options.Timeout))
	}

	// 发送HTTP请求到服务器
	if err := conn.WriteRequest(req); err != nil {
		return nil, err // 如果发送请求失败，返回错误
	}
	// 读取服务器响应
	resp, err := conn.ReadResponse(options.ForceReadAllBody)
	if err != nil {
		return nil, err // 如果读取响应失败，返回错误
	}

	// 将原始响应转换为标准HTTP响应对象
	r, err := toHTTPResponse(conn, resp)
	if err != nil {
		return nil, err // 如果转换失败，返回错误
	}

	// 检查是否需要处理重定向
	if resp.Status.IsRedirect() && redirectstatus.FollowRedirects && redirectstatus.Current <= redirectstatus.MaxRedirects {
		// consume the response body - 消费响应体内容
		_, err := io.Copy(io.Discard, r.Body)
		// 确保响应体被正确关闭，处理可能的错误
		if err := firstErr(err, r.Body.Close()); err != nil {
			return nil, err
		}
		// 获取重定向的目标位置
		loc := headerValue(r.Header, "Location")
		// 如果Location是相对路径，转换为绝对URL
		if strings.HasPrefix(loc, "/") {
			loc = fmt.Sprintf("%s://%s%s", protocol, host, loc)
		}
		redirectstatus.Current++ // 增加重定向计数
		// 递归调用do方法处理重定向请求
		return c.do(method, loc, uripath, headers, body, redirectstatus, options)
	}

	return r, err // 返回最终的HTTP响应
}

// RedirectStatus 表示请求的重定向状态信息
// 用于跟踪和控制HTTP重定向的处理过程
type RedirectStatus struct {
	FollowRedirects bool // 是否跟踪重定向，true表示自动跟踪重定向
	MaxRedirects    int  // 最大重定向次数，防止无限重定向循环
	Current         int  // 当前重定向次数，用于计数已经发生的重定向
}