//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-20 16:43:12
//FilePath: /yaml_scan/pkg/goflags/callback_var.go
//Description:

package goflags

import (
	"fmt"
	"strconv"
)

// CallBackFunc 定义了一个回调函数类型
type CallBackFunc func()

// callBackVar 是一个用于存储回调函数的结构体
type callBackVar struct {
	Value CallBackFunc
}

// Set: 解析布尔字符串并在为 true 时执行回调函数
//
//	@receiver c *callBackVar:
//	@param s string: 表示布尔值的字符串（如 "true", "false"）。
//	@return error error: 果解析失败，返回错误信息。
func (c *callBackVar) Set(s string) error {
	v, err := strconv.ParseBool(s)
	if err != nil {
		return fmt.Errorf("failed to parse callback flag")
	}
	if v {
		// 如果标志被设置为 true，则执行回调函数
		c.Value()
	}
	return nil
}

// IsBoolFlag 指示该标志是一个布尔标志
func (c *callBackVar) IsBoolFlag() bool {
	return true
}

// 返回 callBackVar 的字符串表示
func (c *callBackVar) String() string {
	return "false"
}

// CallbackVar: 添加一个具有长名称的回调标志
//
//	@receiver flagSet *FlagSet:
//	@param callback CallBackFunc: 当标志被设置时要执行的回调函数。
//	@param long string: 标志的长名称。
//	@param usage string: 标志的使用说明。
//	@return *FlagData *FlagData: 返回一个 FlagData 指针，包含标志的相关信息。
func (flagSet *FlagSet) CallbackVar(callback CallBackFunc, long string, usage string) *FlagData {
	return flagSet.CallbackVarP(callback, long, "", usage)
}

// CallbackVarP: 添加一个具有短名称和长名称的回调标志
//
//	@receiver flagSet *FlagSet:
//	@param callback CallBackFunc: 当标志被设置时要执行的回调函数。
//	@param long string:标志的长名称。
//	@param short string:标志的短名称。
//	@param usage string:标志的使用说明。
//	@return *FlagData *FlagData:返回一个 FlagData 指针，包含标志的相关信息。
func (flagSet *FlagSet) CallbackVarP(callback CallBackFunc, long, short string, usage string) *FlagData {
	if callback == nil {
		panic(fmt.Errorf("callback cannot be nil for flag -%v", long))
	}
	flagData := &FlagData{
		usage:        usage,
		long:         long,
		defaultValue: strconv.FormatBool(false),
		field:        &callBackVar{Value: callback},
		skipMarshal:  true,
	}
	if short != "" {
		flagData.short = short
		flagSet.CommandLine.Var(flagData.field, short, usage)
		flagSet.flagKeys.Set(short, flagData)
	}
	flagSet.CommandLine.Var(flagData.field, long, usage)
	flagSet.flagKeys.Set(long, flagData)
	return flagData
}
