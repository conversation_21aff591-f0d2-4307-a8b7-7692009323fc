//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-19 20:18:47
//FilePath: /yaml_scan/cmd/scan/main.go
//Description:

package main

import (
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"strings"
	"time"

	"yaml_scan/cmd/runner"
	"yaml_scan/pkg/config"
	"yaml_scan/pkg/goflags"
	"yaml_scan/pkg/gologger"
	"yaml_scan/pkg/input/provider"
	"yaml_scan/pkg/model/types/severity"
	"yaml_scan/pkg/operators/common/dsl"
	"yaml_scan/pkg/protocols/http"
	"yaml_scan/pkg/templates"
	"yaml_scan/pkg/templates/extensions"
	"yaml_scan/pkg/templates/signer"
	templateTypes "yaml_scan/pkg/templates/types"
	"yaml_scan/pkg/types"
	"yaml_scan/pkg/types/scanstrategy"
	fileutil "yaml_scan/utils/file"
	unitutils "yaml_scan/utils/unit"
)

var (
	cfgFile         string
	templateProfile string
	options         = &types.Options{}
	memProfile      string // optional profile file path
)

func main() {
	gologger.Info().Msg("Yaml scan engine start...")

	if err := runner.ConfigureOptions(); err != nil {
		gologger.Fatal().Msgf("Could not initialize options: %s\n", err)
	}
	_ = readConfig()

	// 打印支持的dsl函数
	if options.ListDslSignatures {
		gologger.Info().Msgf("The available custom DSL functions are:")
		fmt.Println(dsl.GetPrintableDslFunctionSignatures())
		return
	}

	//根据请求对模板进行签名 - 仅支持 glob 语法
	if options.SignTemplates {
		// 初始化签名时使用已解析的选项，而不是默认选项
		templates.UseOptionsForSigner(options)
		tsigner, err := signer.NewTemplateSigner(nil, nil) // 从 env 、 config 中读取或生成新密钥
		if err != nil {
			gologger.Fatal().Msgf("couldn't initialize signer crypto engine: %s\n", err)
		}

		successCounter := 0
		errorCounter := 0
		for _, item := range options.Templates {
			err := filepath.WalkDir(item, func(iterItem string, d fs.DirEntry, err error) error {
				if err != nil || d.IsDir() || !strings.HasSuffix(iterItem, extensions.YAML) {
					// skip non yaml files
					return nil
				}

				if err := templates.SignTemplate(tsigner, iterItem); err != nil {
					if err != templates.ErrNotATemplate {
						// skip warnings and errors as given items are not templates
						errorCounter++
						gologger.Error().Msgf("could not sign '%s': %s\n", iterItem, err)
					}
				} else {
					successCounter++
				}

				return nil
			})
			if err != nil {
				gologger.Error().Msgf("%s\n", err)
			}
		}
		gologger.Info().Msgf("All templates signatures were elaborated success=%d failed=%d\n", successCounter, errorCounter)
		return
	}
}

func readConfig() *goflags.FlagSet {

	// 加载命令行默认配置
	defaultFlagConfig := config.GetFlagConfig()

	flagSet := goflags.NewFlagSet()
	// 设置默认区别大小写
	flagSet.CaseSensitive = defaultFlagConfig.CaseSensitive
	// 设置默认描述信息
	flagSet.SetDescription(defaultFlagConfig.Description)
	// 设置配置文件路径
	flagSet.SetConfigFilePath("config.yaml")

	// 扫描目标配置
	flagSet.CreateGroup("input", "扫描目标配置",
		// 扫描目标 -u, -target string[]
		flagSet.StringSliceVarP(&options.Targets, "target", "u", nil, "扫描目标", goflags.StringSliceOptions),
		// 扫描文件目标路径 -l, -list string
		flagSet.StringVarP(&options.TargetsFilePath, "list", "l", "", "包含要扫描的目标url /主机列表的文件路径（每行一个）"),
		// 要排除的扫描目标 -eh, -exclude-hosts string[]
		flagSet.StringSliceVarP(&options.ExcludeTargets, "exclude-hosts", "eh", nil, "输入列表中排除扫描的主机（ip, cidr, hostname）(ip, cidr, hostname)", goflags.FileCommaSeparatedStringSliceOptions),
		// 使用指定的resume.cfg文件恢复扫描（将禁用请求聚类）。 -resume string
		flagSet.StringVar(&options.Resume, "resume", "", "使用指定的resume.cfg文件恢复扫描（将禁用请求聚类）"),
		//扫描由目标解析出来的所有IP（针对域名对应多个IP的情况）-sa, -scan-all-ips
		flagSet.BoolVarP(&options.ScanAllIPs, "scan-all-ips", "sa", false, "扫描由目标解析出来的所有IP（针对域名对应多个IP的情况）"),
		// 要扫描的主机名的IP版本（4,6）-（默认为4） -iv, -ip-version string[]
		flagSet.StringSliceVarP(&options.IPVersion, "ip-version", "iv", nil, "要扫描的主机名的IP版本（4,6）-（默认为4)", goflags.CommaSeparatedStringSliceOptions),
	)

	// 扫描目标格式化配置
	flagSet.CreateGroup("target-format", "扫描目标格式化配置",
		// 输入文件模式 支持多种输入文件的模式（list, burp, json, yaml, openapi, swagger）（默认为“list”） -im, -input-mode string
		flagSet.StringVarP(&options.InputFileMode, "input-mode", "im", "list", fmt.Sprintf("支持的文件模式:(%v)", provider.SupportedInputFormats())),
		// 在生成请求时，只使用输入格式中的必需字段 -ro, -required-only
		flagSet.BoolVarP(&options.FormatUseRequiredOnly, "required-only", "ro", false, "在生成请求时，只使用输入格式中的必需字段"),
		// 解析输入文件时跳过格式验证（如遗漏变量）-sfv, -skip-format-validation
		flagSet.BoolVarP(&options.SkipFormatValidation, "skip-format-validation", "sfv", false, "解析输入文件时跳过格式验证（如遗漏变量）"),
	)

	// 模板配置
	flagSet.CreateGroup("templates", "模板配置",
		// 仅运行最新发布的模板 -nt, -new-templates
		flagSet.BoolVarP(&options.NewTemplates, "new-templates", "nt", false, "仅运行最新发布的模板"),
		// 仅运行特定版本中添加的新模板 -ntv, -new-templates-version string[]
		flagSet.StringSliceVarP(&options.NewTemplatesWithVersion, "new-templates-version", "ntv", nil, "仅运行特定版本中添加的新模板", goflags.CommaSeparatedStringSliceOptions),
		// 基于Wappalyzer技术的标签映射自动扫描 -as, -automatic-scan
		flagSet.BoolVarP(&options.AutomaticScan, "automatic-scan", "as", false, "基于Wappalyzer技术的标签映射自动扫描"),
		// 指定要运行的模板或者模板目录（以逗号分隔或目录形式） -t, -templates string[]
		flagSet.StringSliceVarP(&options.Templates, "templates", "t", nil, "指定要运行的模板或者模板目录（以逗号分隔或目录形式）", goflags.FileCommaSeparatedStringSliceOptions),
		// 指定要运行的模板URL或模板目录URL（以逗号分隔或目录形式）-turl, -template-url string[]
		flagSet.StringSliceVarP(&options.TemplateURLs, "template-url", "turl", nil, "指定要运行的模板URL或模板目录URL（以逗号分隔或目录形式）", goflags.FileCommaSeparatedStringSliceOptions),
		// 指定要运行的工作流或工作流目录（以逗号分隔或目录形式） -w, -workflows string[]
		flagSet.StringSliceVarP(&options.Workflows, "workflows", "w", nil, "指定要运行的工作流或工作流目录（以逗号分隔或目录形式）", goflags.FileCommaSeparatedStringSliceOptions),
		// 指定要运行的工作流URL或工作流目录URL（以逗号分隔或目录形式）-wurl, -workflow-url string[]
		flagSet.StringSliceVarP(&options.WorkflowURLs, "workflow-url", "wurl", nil, "指定要运行的工作流URL或工作流目录URL（以逗号分隔或目录形式）", goflags.FileCommaSeparatedStringSliceOptions),
		// 验证模板有效性 -validate
		flagSet.BoolVar(&options.Validate, "validate", false, "验证模板有效性"),
		// 禁用对模板的严格检查 -nss, -no-strict-syntax
		flagSet.BoolVarP(&options.NoStrictSyntax, "no-strict-syntax", "nss", false, "禁用对模板的严格检查"),
		// 显示模板内容 -td, -template-display
		flagSet.BoolVarP(&options.TemplateDisplay, "template-display", "td", false, "显示模板内容"),
		// 列出所有可用的模板 -tl
		flagSet.BoolVar(&options.TemplateList, "tl", false, "列出所有支持的模板"),
		// 列出所有可用的标签
		flagSet.BoolVar(&options.TagList, "tgl", false, "列出所有可用的标签"),
		// 允许从中加载远程模板的 URL 列表。-remote-template-domain
		flagSet.StringSliceVarConfigOnly(&options.RemoteTemplateDomainList, "remote-template-domain", []string{"cloud.projectdiscovery.io"}, "allowed domain list to load remote templates from"),
		// 使用环境变量中的私钥对模板进行签名 -sign
		flagSet.BoolVar(&options.SignTemplates, "sign", false, "模板签名"),
		// 启用加载基于协议的代码模板 -code
		flagSet.BoolVar(&options.EnableCodeTemplates, "code", false, "启用加载基于协议的代码模板"),
		// 禁用运行未签名模板或签名不匹配的模板 -dut, -disable-unsigned-templates
		flagSet.BoolVarP(&options.DisableUnsignedTemplates, "disable-unsigned-templates", "dut", false, "禁用运行未签名模板或签名不匹配的模板"),
		// 启用加载自包含模板
		flagSet.BoolVarP(&options.EnableSelfContainedTemplates, "enable-self-contained", "esc", false, "启用加载自包含模板"),
		flagSet.BoolVarP(&options.EnableGlobalMatchersTemplates, "enable-global-matchers", "egm", false, "启用加载全局匹配器模板"),
		// 启用文件模板
		flagSet.BoolVar(&options.EnableFileTemplates, "file", false, "启用文件模板"),
	)

	// 模板过滤配置
	flagSet.CreateGroup("filters", "模板过滤配置",
		// 基于作者运行的模板（逗号分隔，文件）-a, -author string[]
		flagSet.StringSliceVarP(&options.Authors, "author", "a", nil, "基于作者运行的模板（逗号分隔，文件）", goflags.FileNormalizedStringSliceOptions),
		// 执行带指定tag的模板（逗号分隔，文件）-tags string[]
		flagSet.StringSliceVar(&options.Tags, "tags", nil, "执行带指定tag的模板（逗号分隔，文件）", goflags.FileNormalizedStringSliceOptions),
		// 排除带指定tag的模板（逗号分隔，文件）-etags, -exclude-tags string[]
		flagSet.StringSliceVarP(&options.ExcludeTags, "exclude-tags", "etags", nil, "排除带指定tag的模板（逗号分隔，文件）", goflags.FileNormalizedStringSliceOptions),
		// 执行带有指定tag的模板，即使是被默认或者配置排除的模板 -itags, -include-tags string[]
		flagSet.StringSliceVarP(&options.IncludeTags, "include-tags", "itags", nil, "执行带有指定tag的模板，即使是被默认或者配置排除的模板", goflags.FileNormalizedStringSliceOptions), // TODO show default deny list
		// 执行指定id的模板（逗号分隔，文件）-id, -template-id string[]
		flagSet.StringSliceVarP(&options.IncludeIds, "template-id", "id", nil, "执行指定id的模板（逗号分隔，文件）", goflags.FileNormalizedStringSliceOptions),
		//排除指定id的模板（逗号分隔，文件）-eid, -exclude-id string[]
		flagSet.StringSliceVarP(&options.ExcludeIds, "exclude-id", "eid", nil, "排除指定id的模板（逗号分隔，文件）", goflags.FileNormalizedStringSliceOptions),
		//执行指定模板，即使是被默认或配置排除的模板 -it, -include-templates string[]
		flagSet.StringSliceVarP(&options.IncludeTemplates, "include-templates", "it", nil, "执行指定模板，即使是被默认或配置排除的模板", goflags.FileCommaSeparatedStringSliceOptions),
		//  排除指定模板或者模板目录（逗号分隔，文件）-et, -exclude-templates string[]
		flagSet.StringSliceVarP(&options.ExcludedTemplates, "exclude-templates", "et", nil, "排除指定模板或者模板目录（逗号分隔，文件）", goflags.FileCommaSeparatedStringSliceOptions),
		// 排除指定模板matcher -em, -exclude-matchers string[]
		flagSet.StringSliceVarP(&options.ExcludeMatchers, "exclude-matchers", "em", nil, "排除指定模板matcher", goflags.FileCommaSeparatedStringSliceOptions),
		// 根据严重程度运行模板，可选值有：info,low,medium,high,critical -s, -severity value[]
		flagSet.VarP(&options.Severities, "severity", "s", fmt.Sprintf("根据严重程度运行模板，可选值有： %s", severity.GetSupportedSeverities().String())),
		// 根据严重程度排除模板，可选值有：info,low,medium,high,critical -es, -exclude-severity value[]
		flagSet.VarP(&options.ExcludeSeverities, "exclude-severity", "es", fmt.Sprintf("根据严重程度排除模板，可选值有: %s", severity.GetSupportedSeverities().String())),
		// 根据类型运行模板，可选值有：dns, file, http, headless, network, workflow, ssl, websocket, whois -pt, -type value[]
		flagSet.VarP(&options.Protocols, "type", "pt", fmt.Sprintf("根据类型运行模板，可选值有: %s", templateTypes.GetSupportedProtocolTypes())),
		// 根据类型排除模板，可选值有：dns, file, http, headless, network, workflow, ssl, websocket, whois -ept, -exclude-type value[]
		flagSet.VarP(&options.ExcludeProtocols, "exclude-type", "ept", fmt.Sprintf("根据类型排除模板，可选值有: %s", templateTypes.GetSupportedProtocolTypes())),
		// 根据表达式运行模板 -tc, -template-condition string[]
		flagSet.StringSliceVarP(&options.IncludeConditions, "template-condition", "tc", nil, "根据表达式运行模板", goflags.StringSliceOptions),
	)

	// 输出配置
	flagSet.CreateGroup("output", "输出配置",
		// 输出发现的问题到文件 -o, -output string
		flagSet.StringVarP(&options.Output, "output", "o", "", "输出发现的问题到文件"),
		// 将所有请求和响应输出到目录 -sresp, -store-resp
		flagSet.BoolVarP(&options.StoreResponse, "store-resp", "sresp", false, "将nuclei的所有请求和响应输出到目录"),
		// 将所有请求和响应输出到指定目录（默认：output） -srd, -store-resp-dir string
		flagSet.StringVarP(&options.StoreResponseDir, "store-resp-dir", "srd", runner.DefaultDumpTrafficOutputFolder, "将所有请求和响应输出到指定目录（默认：output）"),
		// 只显示结果 -silent
		flagSet.BoolVar(&options.Silent, "silent", false, "只显示结果"),
		// 禁用输出内容着色（ANSI转义码） -nc, -no-color
		flagSet.BoolVarP(&options.NoColor, "no-color", "nc", false, "禁用输出内容着色（ANSI转义码）"),
		// 输出格式为jsonL（ines）-j, -jsonl
		flagSet.BoolVarP(&options.JSONL, "jsonl", "j", false, "输出格式为jsonL（ines）"),
		//flagSet.BoolVarP(&options.JSONRequests, "include-rr", "irr", true, "include request/response pairs in the JSON, JSONL, and Markdown outputs (for findings only) [DEPRECATED use `-omit-raw`]"),
		// 在JSON、JSONL和Markdown中不输出请求/响应对 -or, -omit-raw
		flagSet.BoolVarP(&options.OmitRawRequests, "omit-raw", "or", false, "在JSON、JSONL和Markdown中不输出请求/响应对"),
		// 省略JSON、JSONL输出中的编码模板 -ot, -omit-template
		flagSet.BoolVarP(&options.OmitTemplate, "omit-template", "ot", false, "省略JSON、JSONL输出中的编码模板"),
		// 在cli输出中不打印元数据 -nm, -no-meta
		flagSet.BoolVarP(&options.NoMeta, "no-meta", "nm", false, "在cli输出中不打印元数据"),
		// 在cli输出中打印时间戳 -ts, -timestamp
		flagSet.BoolVarP(&options.Timestamp, "timestamp", "ts", false, "在cli输出中打印时间戳"),
		// 本地的结果数据库（始终使用该数据库保存结果）-rdb, -report-db string
		flagSet.StringVarP(&options.ReportingDB, "report-db", "rdb", "", "本地的结果数据库（始终使用该数据库保存结果）"),
		// 显示匹配失败状态 -ms, -matcher-status
		flagSet.BoolVarP(&options.MatcherStatus, "matcher-status", "ms", false, "显示匹配失败状态"),
		// 以markdown格式导出结果 -me, -markdown-export string
		flagSet.StringVarP(&options.MarkdownExportDirectory, "markdown-export", "me", "", "以markdown格式导出结果"),
		// 以SARIF格式导出结果 -se, -sarif-export string
		flagSet.StringVarP(&options.SarifExport, "sarif-export", "se", "", "以SARIF格式导出结果"),
		//  以JSON格式导出结果 -je, -json-export string
		flagSet.StringVarP(&options.JSONExport, "json-export", "je", "", "以JSON格式导出结果"),
		// 以JSONL(ine)格式导出结果 -jle, -jsonl-export string
		flagSet.StringVarP(&options.JSONLExport, "jsonl-export", "jle", "", "以JSONL(ine)格式导出结果"),
		//  从查询参数、请求头和正文中编校给定的键列表 -rd, -redact string[]
		flagSet.StringSliceVarP(&options.Redact, "redact", "rd", nil, "从查询参数、请求头和正文中编校给定的键列表", goflags.CommaSeparatedStringSliceOptions),
	)

	// 扫描配置
	flagSet.CreateGroup("configs", "扫描配置",
		// 指定配置文件 -config string
		flagSet.StringVar(&cfgFile, "config", "", "指定配置文件"),
		// 要运行的模板配置文件 -tp, -profile string
		flagSet.StringVarP(&templateProfile, "profile", "tp", "", "要运行的模板配置文件"),
		// 列出所有可用的模板配置文件 -tpl, -profile-list
		flagSet.BoolVarP(&options.ListTemplateProfiles, "profile-list", "tpl", false, "列出所有可用的模板配置文件"),
		//  为HTTP模板启用重定向 -fr, -follow-redirects
		flagSet.BoolVarP(&options.FollowRedirects, "follow-redirects", "fr", false, "为HTTP模板启用重定向"),
		// 允许在同一主机上重定向 -fhr, -follow-host-redirects
		flagSet.BoolVarP(&options.FollowHostRedirects, "follow-host-redirects", "fhr", false, "允许在同一主机上重定向"),
		// HTTP模板最大重定向次数（默认：10）-mr, -max-redirects int
		flagSet.IntVarP(&options.MaxRedirects, "max-redirects", "mr", defaultFlagConfig.MaxRedirects, "HTTP模板最大重定向次数（默认：10）"),
		// 为HTTP模板禁用重定向 -dr, -disable-redirects
		flagSet.BoolVarP(&options.DisableRedirects, "disable-redirects", "dr", false, "为HTTP模板禁用重定向"),
		// 指定报告模板文件 -rc, -report-config string
		flagSet.StringVarP(&options.ReportingConfig, "report-config", "rc", "", "指定报告模板文件"),
		// 指定在所有http请求中包含的自定义header、cookie，以header:value的格式指定（cli，文件） H, -header string[]
		flagSet.StringSliceVarP(&options.CustomHeaders, "header", "H", nil, "指定在所有http请求中包含的自定义header、cookie，以header:value的格式指定（cli，文件）", goflags.FileStringSliceOptions),
		//  以key=value格式自定义变量 -V, -var value
		flagSet.RuntimeMapVarP(&options.Vars, "var", "V", nil, "以key=value格式自定义变量"),
		// 指定包含DNS解析服务列表的文件 -r, -resolvers string
		flagSet.StringVarP(&options.ResolversFile, "resolvers", "r", "", "指定包含DNS解析服务列表的文件"),
		// 当DNS错误时使用系统DNS解析服务 -sr, -system-resolvers
		flagSet.BoolVarP(&options.SystemResolvers, "system-resolvers", "sr", false, "当DNS错误时使用系统DNS解析服务"),
		// 关闭请求聚类功能 -dc, -disable-clustering
		flagSet.BoolVarP(&options.DisableClustering, "disable-clustering", "dc", false, "关闭请求聚类功能"),
		//启用被动模式处理本地HTTP响应数据 -passive
		flagSet.BoolVar(&options.OfflineHTTP, "passive", false, "启用被动模式处理本地HTTP响应数据"),
		// 强制使用http2连接 -fh2, -force-http2
		flagSet.BoolVarP(&options.ForceAttemptHTTP2, "force-http2", "fh2", false, "强制使用http2连接"),
		// 启用在模板中使用环境变量 -ev, -env-vars
		flagSet.BoolVarP(&options.EnvironmentVariables, "env-vars", "ev", false, "启用在模板中使用环境变量"),
		// 用于对扫描的主机进行身份验证的客户端证书文件（PEM 编码） -cc, -client-cert string
		flagSet.StringVarP(&options.ClientCertFile, "client-cert", "cc", "", "用于对扫描的主机进行身份验证的客户端证书文件（PEM 编码）"),
		// 用于对扫描的主机进行身份验证的客户端密钥文件（PEM 编码） -ck, -client-key string
		flagSet.StringVarP(&options.ClientKeyFile, "client-key", "ck", "", "用于对扫描的主机进行身份验证的客户端密钥文件（PEM 编码）"),
		//  用于对扫描的主机进行身份验证的客户端证书颁发机构文件（PEM 编码） -ca, -client-ca string
		flagSet.StringVarP(&options.ClientCAFile, "client-ca", "ca", "", "用于对扫描的主机进行身份验证的客户端证书颁发机构文件（PEM 编码）"),
		//显示文件模板的匹配值，只适用于提取器 -sml, -show-match-line
		flagSet.BoolVarP(&options.ShowMatchLine, "show-match-line", "sml", false, "显示文件模板的匹配值，只适用于提取器"),
		// 使用ztls库，带有自动回退到标准库tls13 [已弃用] 默认情况下启用对ztls的自动回退 -ztls
		// flagSet.BoolVar(&options.ZTLS, "ztls", false, "使用ztls库，带有自动回退到标准库tls13 [已弃用] 默认情况下启用对ztls的自动回退"), //nolint:all
		// 指定tls sni的主机名（默认为输入的域名） -sni string
		flagSet.StringVar(&options.SNI, "sni", "", "指定tls sni的主机名（默认为输入的域名）"),
		// 设置网络请求的保持活动时间。 -dka, -dialer-keep-alive value
		flagSet.DurationVarP(&options.DialerKeepAlive, "dialer-keep-alive", "dka", 0, "设置网络请求的保持活动时间"),
		// 允许访问本地文件（payload文件）-lfa, -allow-local-file-access
		flagSet.BoolVarP(&options.AllowLocalFileAccess, "allow-local-file-access", "lfa", false, "允许访问本地文件（payload文件）"),
		// 阻止对本地/私有网络的连接 -lna, -restrict-local-network-access
		flagSet.BoolVarP(&options.RestrictLocalNetworkAccess, "restrict-local-network-access", "lna", false, "阻止对本地/私有网络的连接"),
		// 指定用于网络扫描的网卡 -i, -interface string
		flagSet.StringVarP(&options.Interface, "interface", "i", "", "指定用于网络扫描的网卡"),
		// payload的组合模式（batteringram,pitchfork,clusterbomb） -at, -attack-type string
		flagSet.StringVarP(&options.AttackType, "attack-type", "at", "", "payload的组合模式（batteringram,pitchfork,clusterbomb）"),
		// 指定用于网络扫描的源IP -sip, -source-ip string
		flagSet.StringVarP(&options.SourceIP, "source-ip", "sip", "", "指定用于网络扫描的源IP"),
		// 最大读取响应大小（默认：10485760字节） -rsr, -response-size-read int
		flagSet.IntVarP(&options.ResponseReadSize, "response-size-read", "rsr", 0, "最大读取响应大小（默认：10485760字节）"),
		//  最大储存响应大小（默认：1048576字节）-rss, -response-size-save int
		flagSet.IntVarP(&options.ResponseSaveSize, "response-size-save", "rss", unitutils.Mega, "最大储存响应大小（默认：1048576字节）"),
		// 删除所有配置和数据文件（包括templates） -reset
		// flagSet.CallbackVar(resetCallback, "reset", "删除所有配置和数据文件（包括templates）"),
		// 启用实验性的Client Hello（ja3）TLS 随机化功能 -tlsi, -tls-impersonate
		flagSet.BoolVarP(&options.TlsImpersonate, "tls-impersonate", "tlsi", false, "启用实验性的Client Hello（ja3）TLS 随机化功能"),
		// 实验性HTTP API端点 -hae, -http-api-endpoint string
		flagSet.StringVarP(&options.HttpApiEndpoint, "http-api-endpoint", "hae", "", "实验性HTTP API端点"),
	)

	// interactsh设置
	// flagSet.CreateGroup("interactsh", "interactsh",
	// 	//使用interactsh反连检测平台（默认为oast.pro,oast.live,oast.site,oast.online,oast.fun,oast.me）-iserver, -interactsh-server string
	// 	flagSet.StringVarP(&options.InteractshURL, "interactsh-server", "iserver", "", fmt.Sprintf("使用interactsh反连检测平台 (默认为: %s)", client.DefaultOptions.ServerURL)),
	// 	flagSet.StringVarP(&options.InteractshToken, "interactsh-token", "itoken", "", "authentication token for self-hosted interactsh server"),
	// 	flagSet.IntVar(&options.InteractionsCacheSize, "interactions-cache-size", 5000, "number of requests to keep in the interactions cache"),
	// 	flagSet.IntVar(&options.InteractionsEviction, "interactions-eviction", 60, "number of seconds to wait before evicting requests from cache"),
	// 	flagSet.IntVar(&options.InteractionsPollDuration, "interactions-poll-duration", 5, "number of seconds to wait before each interaction poll request"),
	// 	flagSet.IntVar(&options.InteractionsCoolDownPeriod, "interactions-cooldown-period", 5, "extra time for interaction polling before exiting"),
	// 	flagSet.BoolVarP(&options.NoInteractsh, "no-interactsh", "ni", false, "disable interactsh server for OAST testing, exclude OAST based templates"),
	// )
	// fuzz扫描配置
	flagSet.CreateGroup("fuzzing", "fuzz扫描配置",
		//覆盖模板中设置的fuzz类型（replace、prefix、postfix、infix）-ft, -fuzzing-type string
		flagSet.StringVarP(&options.FuzzingType, "fuzzing-type", "ft", "", "覆盖模板中设置的fuzz类型（replace、prefix、postfix、infix）"),
		// 覆盖模板中设置的fuzz模式（multiple、single）-fm, -fuzzing-mode string
		flagSet.StringVarP(&options.FuzzingMode, "fuzzing-mode", "fm", "", "覆盖模板中设置的fuzz模式（multiple、single）"),
		// 启用fuzz模板扫描 -dast
		flagSet.BoolVar(&options.DAST, "dast", false, "启用fuzz模板扫描"),
		// 启用 dast server 模式 -dts
		flagSet.BoolVarP(&options.DASTServer, "dast-server", "dts", false, "启用 dast server 模式 (live fuzzing)"),
		// 写 dast 扫描报告到文件中 -dtr
		flagSet.BoolVarP(&options.DASTReport, "dast-report", "dtr", false, "写 dast 扫描报告"),
		// dast server token -dtst
		flagSet.StringVarP(&options.DASTServerToken, "dast-server-token", "dtst", "", "dast server token (可选)"),
		// dast server地址 -dtsa
		flagSet.StringVarP(&options.DASTServerAddress, "dast-server-address", "dtsa", "localhost:9055", "dast server地址"),
		//显示输出中的fuzz，以便调试 -dfp, -display-fuzz-points
		flagSet.BoolVarP(&options.DisplayFuzzPoints, "display-fuzz-points", "dfp", false, "显示输出中的fuzz，以便调试"),
		// fuzz参数的频率 -fuzz-param-frequency int
		flagSet.IntVar(&options.FuzzParamFrequency, "fuzz-param-frequency", 10, "fuzz参数的频率"),
		// fuzz攻击级别 (low, medium, high) -fa, -fuzz-aggression string
		flagSet.StringVarP(&options.FuzzAggressionLevel, "fuzz-aggression", "fa", "low", "fuzz攻击级别 (low, medium, high)"),
		// 范围内url的正则表达式列表 -cs
		flagSet.StringSliceVarP(&options.Scope, "fuzz-scope", "cs", nil, "范围内url的正则表达式列表", goflags.FileCommaSeparatedStringSliceOptions),
		// 范围外url的正则表达式列表 -cos
		flagSet.StringSliceVarP(&options.OutOfScope, "fuzz-out-scope", "cos", nil, "范围外url的正则表达式列表", goflags.FileCommaSeparatedStringSliceOptions),
	)

	// uncover引擎
	// flagSet.CreateGroup("uncover", "Uncover引擎 ",
	// 	// 启动uncover引擎 -uc
	// 	flagSet.BoolVarP(&options.Uncover, "uncover", "uc", false, "启动uncover引擎"),
	// 	// uncover查询语句 -uq
	// 	flagSet.StringSliceVarP(&options.UncoverQuery, "uncover-query", "uq", nil, "uncover查询语句", goflags.FileStringSliceOptions),
	// 	// 指定uncover查询引擎 -ue
	// 	flagSet.StringSliceVarP(&options.UncoverEngine, "uncover-engine", "ue", nil, fmt.Sprintf("指定uncover查询引擎 (%s) (default shodan)", uncover.GetUncoverSupportedAgents()), goflags.FileStringSliceOptions),
	// 	// 查询字段 （ip,port,host） （默认 "ip:port"） -uf
	// 	flagSet.StringVarP(&options.UncoverField, "uncover-field", "uf", "ip:port", "查询字段 （ip,port,host） （默认 'ip:port'）"),
	// 	// 查询结果数 （默认 100） -ul
	// 	flagSet.IntVarP(&options.UncoverLimit, "uncover-limit", "ul", 100, "查询结果数 （默认 100）"),
	// 	// 查询速率，默认每分钟60个请求（默认 60）
	// 	flagSet.IntVarP(&options.UncoverRateLimit, "uncover-ratelimit", "ur", 60, "查询速率，默认每分钟60个请求（默认 60）"),
	// )

	// 性能配置
	flagSet.CreateGroup("rate-limit", "性能配置",
		// 每秒最大请求量（默认：150） -rl, -rate-limit int
		flagSet.IntVarP(&options.RateLimit, "rate-limit", "rl", 150, "每秒最大请求量（默认：150）"),
		//  每秒发送的最大请求数 -rld, -rate-limit-duration value
		flagSet.DurationVarP(&options.RateLimitDuration, "rate-limit-duration", "rld", time.Second, "每秒发送的最大请求数（默认为15）"),
		// 每个模板最大并行检测数（默认：25） -bs, -bulk-size int
		flagSet.IntVarP(&options.BulkSize, "bulk-size", "bs", 25, "每个模板最大并行检测数（默认：25）"),
		// 并行执行的最大模板数量（默认：25） -c, -concurrency int
		flagSet.IntVarP(&options.TemplateThreads, "concurrency", "c", 25, "并行执行的最大模板数量（默认：25）"),
		// 每个模板并行运行的无头主机最大数量（默认：10）-hbs, -headless-bulk-size int
		flagSet.IntVarP(&options.HeadlessBulkSize, "headless-bulk-size", "hbs", 10, "每个模板并行运行的无头主机最大数量（默认：10）"),
		// 并行指定无头主机最大数量（默认：10）-headc, -headless-concurrency int
		flagSet.IntVarP(&options.HeadlessTemplateThreads, "headless-concurrency", "headc", 10, "并行指定无头主机最大数量（默认：10）"),
		// 并行执行的js运行时的最大数量（默认为120）-jsc, -js-concurrency int
		flagSet.IntVarP(&options.JsConcurrency, "js-concurrency", "jsc", 120, "并行执行的js运行时的最大数量（默认为120）"),
		// 每个模板的最大payload并发数（默认值为 25）-pc, -payload-concurrency int
		flagSet.IntVarP(&options.PayloadConcurrency, "payload-concurrency", "pc", 25, "每个模板的最大payload并发数（默认值为 25)"),
		// 与 httpx 一起的 HTTP 探测并发数（默认值为 50） -prc, -probe-concurrency int
		flagSet.IntVarP(&options.ProbeConcurrency, "probe-concurrency", "prc", 50, "与 httpx 一起的 HTTP 探测并发数（默认值为 50）"),
	)

	// 优化配置
	flagSet.CreateGroup("optimization", "优化配置",
		// 超时时间（默认为10秒）-timeout int
		flagSet.IntVar(&options.Timeout, "timeout", 10, "超时时间（默认为10秒）"),
		//  重试次数（默认：1）-retries int
		flagSet.IntVar(&options.Retries, "retries", 1, "重试次数（默认：1）"),
		//指定HTTP/HTTPS默认端口（例如：host:80，host:443） -ldp, -leave-default-ports
		flagSet.BoolVarP(&options.LeaveDefaultPorts, "leave-default-ports", "ldp", false, "指定HTTP/HTTPS默认端口（例如：host:80，host:443）"),
		// 某主机扫描失败次数，跳过该主机（默认：30）-mhe, -max-host-error int
		flagSet.IntVarP(&options.MaxHostError, "max-host-error", "mhe", 30, "某主机扫描失败次数，跳过该主机（默认：30）"),
		// 将给定错误添加到最大主机错误监视列表（标准、文件）-te, -track-error string[]
		flagSet.StringSliceVarP(&options.TrackError, "track-error", "te", nil, "将给定错误添加到最大主机错误监视列表（标准、文件）", goflags.FileStringSliceOptions),
		// 禁用基于错误跳过主机扫描-nmhe, -no-mhe
		flagSet.BoolVarP(&options.NoHostErrors, "no-mhe", "nmhe", false, "禁用基于错误跳过主机扫描"),
		// 使用项目文件夹避免多次发送同一请求 -project
		flagSet.BoolVar(&options.Project, "project", false, "使用项目文件夹避免多次发送同一请求"),
		// 设置特定的项目文件夹 -project-path string
		flagSet.StringVar(&options.ProjectPath, "project-path", os.TempDir(), "设置特定的项目文件夹"),
		//得到一个结果后停止（或许会中断模板和工作流的逻辑）-spm, -stop-at-first-match
		flagSet.BoolVarP(&options.StopAtFirstMatch, "stop-at-first-match", "spm", false, "得到一个结果后停止（或许会中断模板和工作流的逻辑）"),
		// 流模式 - 在不整理输入的情况下详细描述 -stream
		flagSet.BoolVar(&options.Stream, "stream", false, "流模式 - 在不整理输入的情况下详细描述"),
		// 扫描时使用的策略（auto/host-spray/template-spray） （默认 auto） -ss, -scan-strategy value
		flagSet.EnumVarP(&options.ScanStrategy, "scan-strategy", "ss", goflags.EnumVariable(0), "扫描时使用的策略（auto/host-spray/template-spray） （默认 auto）", goflags.AllowdTypes{
			scanstrategy.Auto.String():          goflags.EnumVariable(0),
			scanstrategy.HostSpray.String():     goflags.EnumVariable(1),
			scanstrategy.TemplateSpray.String(): goflags.EnumVariable(2),
		}),
		//  输入读取超时时间（默认：3分钟）-irt, -input-read-timeout value
		flagSet.DurationVarP(&options.InputReadTimeout, "input-read-timeout", "irt", time.Duration(3*time.Minute), "输入读取超时时间（默认：3分钟）"),
		// 禁用对非URL输入进行httpx探测 -nh, -no-httpx
		flagSet.BoolVarP(&options.DisableHTTPProbe, "no-httpx", "nh", false, "禁用对非URL输入进行httpx探测"),
		//  禁用标准输入 -no-stdin
		flagSet.BoolVar(&options.DisableStdin, "no-stdin", false, "禁用标准输入"),
	)

	// 无界面浏览器配置
	flagSet.CreateGroup("headless", "无界面浏览器配置",
		//启用需要无界面浏览器的模板 -headless
		flagSet.BoolVar(&options.Headless, "headless", false, "启用需要无界面浏览器的模板"),
		// 在无界面下超时秒数（默认：20） -page-timeout int
		flagSet.IntVar(&options.PageTimeout, "page-timeout", 20, "在无界面下超时秒数（默认：20"),
		// 在无界面浏览器运行模板时，显示浏览器 -sb, -show-browser
		flagSet.BoolVarP(&options.ShowBrowser, "show-browser", "sb", false, "在无界面浏览器运行模板时，显示浏览器"),
		// 使用附加选项启动无界面浏览器 -ho, -headless-options string[]
		flagSet.StringSliceVarP(&options.HeadlessOptionalArguments, "headless-options", "ho", nil, "使用附加选项启动无界面浏览器", goflags.FileCommaSeparatedStringSliceOptions),
		// 不使用自带的浏览器，使用本地浏览器-sc, -system-chrome
		flagSet.BoolVarP(&options.UseInstalledChrome, "system-chrome", "sc", false, "不使用自带的浏览器，使用本地浏览器"),
		// 列出可用的无界面操作 -lha, -list-headless-action
		flagSet.BoolVarP(&options.ShowActions, "list-headless-action", "lha", false, "列出可用的无界面操作"),
	)

	// Debug配置
	flagSet.CreateGroup("debug", "Debug配置",
		// 显示所有请求和响应-debug
		flagSet.BoolVar(&options.Debug, "debug", false, "显示所有请求和响应"),
		// 显示所有请求 -dreq, -debug-req
		flagSet.BoolVarP(&options.DebugRequests, "debug-req", "dreq", false, "显示所有请求"),
		// 显示所有响应 -dresp, -debug-resp
		flagSet.BoolVarP(&options.DebugResponse, "debug-resp", "dresp", false, "显示所有响应"),
		// 使用http/socks5代理（逗号分隔，文件） -p, -proxy string[]
		flagSet.StringSliceVarP(&options.Proxy, "proxy", "p", nil, "使用http/socks5代理（逗号分隔，文件）", goflags.FileCommaSeparatedStringSliceOptions),
		// 代理所有请求 -pi, -proxy-internal
		flagSet.BoolVarP(&options.ProxyInternal, "proxy-internal", "pi", false, "代理所有请求"),
		// 列出所有支持的DSL函数签名 -ldf, -list-dsl-function
		flagSet.BoolVarP(&options.ListDslSignatures, "list-dsl-function", "ldf", false, "列出所有支持的DSL函数签名"),
		// 写入跟踪日志到文件 -tlog, -trace-log string
		flagSet.StringVarP(&options.TraceLogFile, "trace-log", "tlog", "", "写入跟踪日志到文件"),
		// 写入错误日志到文件 -elog, -error-log string
		flagSet.StringVarP(&options.ErrorLogFile, "error-log", "elog", "", "写入错误日志到文件"),
		// 显示版本信息 -version
		// flagSet.CallbackVar(printVersion, "version", "显示版本信息"),
		//启用挂起协程的监控-hm, -hang-monitor
		flagSet.BoolVarP(&options.HangMonitor, "hang-monitor", "hm", false, "启用挂起协程的监控"),
		// 显示详细信息 -v, -verbose
		flagSet.BoolVarP(&options.Verbose, "verbose", "v", false, "显示详细信息"),
		//将内存转储成文件 -profile-mem string
		flagSet.StringVar(&memProfile, "profile-mem", "", "将内存转储成文件"),
		//  显示额外的详细信息 -vv
		flagSet.BoolVar(&options.VerboseVerbose, "vv", false, "显示额外的详细信息"),
		// 显示用于调试的变量输出 -svd, -show-var-dump
		flagSet.BoolVarP(&options.ShowVarDump, "show-var-dump", "svd", false, "显示用于调试的变量输出"),
		//限制var dump中显示的字符数 -vdl, -var-dump-limit int
		flagSet.IntVarP(&options.VarDumpLimit, "var-dump-limit", "vdl", 255, "限制var dump中显示的字符数"),
		// 启用pprof调试服务器 -ep, -enable-pprof
		flagSet.BoolVarP(&options.EnablePprof, "enable-pprof", "ep", false, "启用pprof调试服务器"),
		// 显示已安装的模板版本-tv, -templates-version
		// flagSet.CallbackVarP(printTemplateVersion, "templates-version", "tv", "显示已安装的模板版本"),
		// 运行诊断检查 -hc, -health-check
		flagSet.BoolVarP(&options.HealthCheck, "health-check", "hc", false, "运行诊断检查"),
	)

	// 扫描统计配置
	flagSet.CreateGroup("stats", "扫描统计配置",
		//显示正在扫描的统计信息-stats
		flagSet.BoolVar(&options.EnableProgressBar, "stats", false, "显示正在扫描的统计信息"),
		// 将统计信息以JSONL格式输出到文件-sj, -stats-json
		flagSet.BoolVarP(&options.StatsJSON, "stats-json", "sj", false, "将统计信息以JSONL格式输出到文件"),
		//显示统计信息更新的间隔秒数（默认：5） -si, -stats-interval int
		flagSet.IntVarP(&options.StatsInterval, "stats-interval", "si", 5, "显示统计信息更新的间隔秒数（默认：5）"),
		// 更改metrics服务的端口（默认：9092） -mp, -metrics-port int
		flagSet.IntVarP(&options.MetricsPort, "metrics-port", "mp", 9092, "更改metrics服务的端口（默认：9092）"),
		// 启用HTTP状态捕获
		flagSet.BoolVarP(&options.HTTPStats, "http-stats", "hps", false, "启用HTTP统计信息跟踪和显示。"),
	)

	// 解析配置
	_ = flagSet.Parse()

	if options.EnableCodeTemplates {
		options.EnableSelfContainedTemplates = true
	}
	// 是否跳过默认端口
	if options.LeaveDefaultPorts {
		http.LeaveDefaultPorts = true
	}

	// 使用外部配置
	if cfgFile != "" {
		if !fileutil.FileExists(cfgFile) {
			gologger.Fatal().Msgf("given config file '%s' does not exist", cfgFile)
		}
		// 合并配置文件与标志
		if err := flagSet.MergeConfigFile(cfgFile); err != nil {
			gologger.Fatal().Msgf("Could not read config: %s\n", err)
		}
	}

	// 加载模板配置文件
	if templateProfile != "" {
		LoadTemplateProfile(templateProfile)

		if err := flagSet.MergeConfigFile(templateProfile); err != nil {
			gologger.Fatal().Msgf("Could not read template profile: %s\n", err)
		}
	}

	return flagSet
}

// LoadTemplateProfile: 加载模板配置文件
//
//	@param templateProfile string: 要加载的模板配置文件的路径或 ID。
//	@return string string: 加载的模板文件的完整路径。
//	@return error error:如果加载过程中发生错误，则返回相应的错误信息。
func LoadTemplateProfile(templateProfile string) {
	// 默认配置文件路径
	defaultProfilesPath := filepath.Join(config.DefaultConfig.GetTemplateDir(), "profiles")
	// 检查模板配置文件名是否有扩展名
	if filepath.Ext(templateProfile) == "" {
		// 尝试通过 ID 查找配置文件路径
		if tp := findProfilePathById(templateProfile, defaultProfilesPath); tp != "" {
			templateProfile = tp
		} else {
			gologger.Fatal().Msgf("'%s' is not a profile-id or profile path", templateProfile)
		}
	}
	// 检查模板文件路径是否为绝对路径
	if !filepath.IsAbs(templateProfile) {
		if filepath.Dir(templateProfile) == "profiles" {
			defaultProfilesPath = filepath.Join(config.DefaultConfig.GetTemplateDir())
		}
		currentDir, err := os.Getwd()
		if err == nil && fileutil.FileExists(filepath.Join(currentDir, templateProfile)) {
			templateProfile = filepath.Join(currentDir, templateProfile)
		} else {
			templateProfile = filepath.Join(defaultProfilesPath, templateProfile)
		}
	}
	// 检查模板文件是否存在
	if !fileutil.FileExists(templateProfile) {
		gologger.Fatal().Msgf("given template profile file '%s' does not exist", templateProfile)
	}
}

// findProfilePathById:  根据给定的配置文件 ID 在指定的模板目录中查找 YAML 文件的路径。
//
//	@param profileId string:要查找的配置文件 ID（不带扩展名）。
//	@param templatesDir string: 模板目录的路径。
//	@return string string: 找到的配置文件的完整路径，如果未找到则返回空字符串。
func findProfilePathById(profileId, templatesDir string) string {
	var profilePath string
	// 遍历模板目录
	err := filepath.WalkDir(templatesDir, func(iterItem string, d fs.DirEntry, err error) error {
		// 获取当前文件的扩展名
		ext := filepath.Ext(iterItem)
		// 检查文件是否为 YAML 文件
		isYaml := ext == extensions.YAML || ext == extensions.YML
		// 如果发生错误、是目录或不是 YAML 文件，则跳过
		if err != nil || d.IsDir() || !isYaml {
			return nil
		}
		// 检查文件名（去掉扩展名）是否与 profileId 匹配
		if strings.TrimSuffix(filepath.Base(iterItem), ext) == profileId {
			profilePath = iterItem
			return fmt.Errorf("FOUND")
		}
		return nil
	})
	if err != nil && err.Error() != "FOUND" {
		gologger.Error().Msgf("%s\n", err)
	}
	return profilePath
}
