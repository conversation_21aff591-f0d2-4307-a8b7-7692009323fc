//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-10 19:53:53
//FilePath: /yaml_scan/pkg/networkpolicy/networkpolicy_test.go
//Description:

package networkpolicy

import (
	"fmt"
	"net"
	"net/netip"
	"testing"

	"github.com/gaissmai/bart"
	"github.com/stretchr/testify/require"
)

// TestAsCidr 测试 asCidr 函数的功能
func TestAsCidr(t *testing.T) {
	tests := []struct {
		input    string
		expected netip.Prefix
		hasError bool
	}{
		// 测试有效的 IPv4 地址
		{"***********", netip.PrefixFrom(netip.MustParseAddr("***********"), 32), false},
		{"********", netip.PrefixFrom(netip.MustParseAddr("********"), 32), false},
		{"**********", netip.PrefixFrom(netip.MustParseAddr("**********"), 32), false},

		// 测试有效的 IPv6 地址
		{"::1", netip.PrefixFrom(netip.MustParseAddr("::1"), 128), false},
		{"2001:0db8:85a3:0000:0000:8a2e:0370:7334", netip.PrefixFrom(netip.MustParseAddr("2001:0db8:85a3:0000:0000:8a2e:0370:7334"), 128), false},

		// 测试有效的 CIDR 表示法
		{"***********/24", netip.MustParsePrefix("***********/24"), false},
		{"2001:0db8::/32", netip.MustParsePrefix("2001:0db8::/32"), false},

		// 测试无效的输入
		{"invalid_ip", netip.Prefix{}, true},
		{"256.256.256.256", netip.Prefix{}, true},
		{"", netip.Prefix{}, true},
	}

	for _, test := range tests {
		t.Run(test.input, func(t *testing.T) {
			result, err := asCidr(test.input)
			if test.hasError {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, test.expected, result)
			}
		})
	}
}

func TestNew(t *testing.T) {
	tests := []struct {
		name     string
		options  Options
		hasError bool
	}{
		{
			name: "Valid options with allow and deny lists",
			options: Options{
				DenyList:        []string{"***********/24"},
				AllowList:       []string{"10.0.0.0/8"},
				AllowSchemeList: []string{"http", "https"},
				DenySchemeList:  []string{"ftp"},
				AllowPortList:   []int{80, 443},
				DenyPortList:    []int{21, 25},
			},
			hasError: false,
		},
		{
			name: "Invalid regex in allow list",
			options: Options{
				DenyList:        []string{"***********/24"},
				AllowList:       []string{"[invalid_regex"},
				AllowSchemeList: []string{"http", "https"},
				DenySchemeList:  []string{"ftp"},
				AllowPortList:   []int{80, 443},
				DenyPortList:    []int{21, 25},
			},
			hasError: true,
		},
		{
			name: "Empty options",
			options: Options{
				DenyList:        []string{},
				AllowList:       []string{},
				AllowSchemeList: []string{},
				DenySchemeList:  []string{},
				AllowPortList:   []int{},
				DenyPortList:    []int{},
			},
			hasError: false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			policy, err := New(test.options)
			if test.hasError {
				require.Error(t, err)
				require.Nil(t, policy)
			} else {
				require.NoError(t, err)
				require.NotNil(t, policy)
				// 进一步检查 policy 的内容是否符合预期
				require.Equal(t, test.options.DenyList, policy.Options.DenyList)
				require.Equal(t, test.options.AllowList, policy.Options.AllowList)
			}
		})
	}
}

// 测试 rangerContains 函数
func TestRangerContains(t *testing.T) {
	// 初始化测试表
	table := new(bart.Table[net.IP])
	// 添加测试 IP
	ip1 := netip.MustParsePrefix("***********/16")
	ip2 := netip.MustParsePrefix("*******/16")
	table.Insert(ip1, nil)

	// 测试用例
	testCases := []struct {
		name     string
		ranger   *bart.Table[net.IP]
		ip       netip.Addr
		expected bool
	}{
		{
			name:     "IP 存在于表中",
			ranger:   table,
			ip:       ip1.Addr(),
			expected: true,
		},
		{
			name:     "IP 不存在于表中",
			ranger:   table,
			ip:       ip2.Addr(),
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 测试正常情况
			require.Equal(t, tc.expected, rangerContains(tc.ranger, tc.ip))
		})
	}
}

// 测试 NetworkPolicy.Validate 函数
func TestValidate(t *testing.T) {
	var testCases = []struct {
		address       string
		expectedValid bool
	}{
		{"projectdiscovery.io", false},
		{"projectdiscovery.io:80", false},
		{"http://scanme.sh", false},
		{"scanme.sh:8080", true},
	}

	var npOptions Options
	npOptions.DenyList = append(npOptions.DenyList,
		"projectdiscovery.io",
		"projectdiscovery.io:80",
		"http://scanm.\\.sh",
		"honey\\.scanme\\.sh",
	)

	np, err := New(npOptions)
	if err != nil {
		fmt.Println(err)
	}

	for _, tc := range testCases {
		ok := np.Validate(tc.address)
		require.Equal(t, tc.expectedValid, ok, "Unexpected result for address: "+tc.address)
	}
}

func TestValidateAddress(t *testing.T) {
	var npOptions Options
	npOptions.DenyList = append(npOptions.DenyList, "*********/8")
	np, err := New(npOptions)
	if err != nil {
		fmt.Println(err)
	}
	ok := np.ValidateAddress("127.0.0.1")
	require.Equal(t, false, ok, "Unexpected positive result")
	ok = np.ValidateAddress("***********")
	require.Equal(t, true, ok, "Unexpected negative result")
}

// 测试 NetworkPolicy.ValidateURLWithIP 函数
func TestValidateURLWithIP(t *testing.T) {
	var npOptions Options
	npOptions.DenyList = append(npOptions.DenyList, "*********/8")
	np, err := New(npOptions)
	if err != nil {
		fmt.Println(err)
	}
	ok := np.ValidateURLWithIP("https://example.com", "127.0.0.1")
	require.Equal(t, false, ok, "Unexpected positive result")
	ok = np.ValidateURLWithIP("https://example.com", "********")
	require.Equal(t, true, ok, "Unexpected negative result")
}

// 测试 portIsListed 函数
func TestPortIsListed(t *testing.T) {
	testCases := []struct {
		name     string
		list     map[int]struct{}
		port     int
		expected bool
	}{
		{
			name:     "端口存在于列表中",
			list:     map[int]struct{}{8080: {}},
			port:     8080,
			expected: true,
		},
		{
			name:     "端口不存在于列表中",
			list:     map[int]struct{}{8080: {}, 8081: {}},
			port:     8082,
			expected: false,
		},
		{
			name:     "空列表",
			list:     map[int]struct{}{},
			port:     8080,
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 调用 portIsListed 函数
			result := portIsListed(tc.list, tc.port)

			// 验证结果
			require.Equal(t, tc.expected, result)
		})
	}
}

// 测试 NetworkPolicy.ValidatePort 函数
func TestValidatePort(t *testing.T) {
	var npOptions Options
	npOptions.DenyPortList = append(npOptions.DenyPortList, 100)
	np, err := New(npOptions)
	if err != nil {
		fmt.Println(err)
	}
	ok := np.ValidatePort(100)
	require.Equal(t, false, ok, "Unexpected positive result")
	ok = np.ValidatePort(800)
	require.Equal(t, true, ok, "Unexpected negative result")
}

// 测试 NetworkPolicy.ValidateAddressWithPort 函数
func TestValidateAddressWithPort(t *testing.T) {
	var npOptions Options
	npOptions.DenyPortList = append(npOptions.DenyPortList, 100)
	npOptions.DenyList = append(npOptions.DenyList, "127.0.0.1")
	np, err := New(npOptions)
	if err != nil {
		fmt.Println(err)
	}
	ok := np.ValidateAddressWithPort("127.0.0.1", 100)
	require.Equal(t, false, ok, "Unexpected positive result")
	ok = np.ValidateAddressWithPort("*************", 800)
	require.Equal(t, true, ok, "Unexpected negative result")
}

// 测试 NetworkPolicy.ValidateHost 函数
func TestValidateHost(t *testing.T) {
	var npOptions Options
	npOptions.DenyList = append(npOptions.DenyList, "127.0.0.1")
	np, err := New(npOptions)
	if err != nil {
		fmt.Println(err)
	}
	ip, ok := np.ValidateHost("127.0.0.1")
	require.Equal(t, false, ok, "Unexpected positive result")
	require.Equal(t, "127.0.0.1", ip, "Unexpected positive result")
	
	ip, ok = np.ValidateHost("*************")
	require.Equal(t, true, ok, "Unexpected negative result")
	require.Equal(t, "*************", ip, "Unexpected negative result")
}
