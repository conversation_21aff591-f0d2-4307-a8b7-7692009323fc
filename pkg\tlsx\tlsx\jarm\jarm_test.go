// 
// Author: chenjb
// Version: V1.0
// Date: 2025-07-01 11:46:49
// FilePath: /yaml_scan/pkg/tlsx/tlsx/jarm/jarm_test.go
// Description: 
package jarm

import (
	"testing"
	"yaml_scan/pkg/fastdialer"

	"github.com/stretchr/testify/require"
)

// TestHashWithDialer 测试JARM指纹识别功能
// 使用真实的网络地址进行JARM哈希计算测试
func TestHashWithDialer(t *testing.T) {
	// 创建快速拨号器
	dialer, err := fastdialer.NewDialer(fastdialer.DefaultOptions)
	require.NoError(t, err, "创建拨号器不应该出错")

	tests := []struct {
		name        string // 测试用例名称
		host        string // 目标主机
		port        int    // 目标端口
		duration    int    // 超时时间（秒）
		expectError bool   // 是否期望出现错误
		description string // 测试用例描述
	}{
		{
			name:        "百度HTTPS JARM指纹",
			host:        "www.baidu.com",
			port:        443,
			duration:    15,
			expectError: false,
			description: "计算百度HTTPS服务的JARM指纹应该成功",
		},
		{
			name:        "指定IP地址JARM指纹",
			host:        "**************",
			port:        443,
			duration:    15,
			expectError: false,
			description: "使用IP地址计算JARM指纹应该成功",
		},
		{
			name:        "短超时时间",
			host:        "www.baidu.com",
			port:        443,
			duration:    5,
			expectError: false,
			description: "使用较短的超时时间应该仍能成功",
		},
		{
			name:        "不存在的端口",
			host:        "www.baidu.com",
			port:        9999,
			duration:    10,
			expectError: true,
			description: "连接不存在的端口应该失败",
		},
		{
			name:        "无效的主机名",
			host:        "invalid-host-that-does-not-exist.com",
			port:        443,
			duration:    10,
			expectError: true,
			description: "连接无效主机名应该失败",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行JARM指纹计算
			jarmHash, err := HashWithDialer(dialer, tt.host, tt.port, tt.duration)

			if tt.expectError {
				// 期望出现错误的情况
				require.Error(t, err, "应该返回错误: %s", tt.description)
				require.Empty(t, jarmHash, "出错时JARM哈希应该为空")
			} else {
				// 期望成功的情况
				require.NoError(t, err, "不应该返回错误: %s", tt.description)
				require.NotEmpty(t, jarmHash, "JARM哈希不应该为空")
				
				// 验证JARM哈希的格式
				require.Len(t, jarmHash, 62, "JARM哈希应该是62个字符")
				
				// 验证JARM哈希只包含有效字符（十六进制字符）
				for _, char := range jarmHash {
					require.True(t, 
						(char >= '0' && char <= '9') || 
						(char >= 'a' && char <= 'f') || 
						(char >= 'A' && char <= 'F'), 
						"JARM哈希应该只包含十六进制字符: %c", char)
				}
				
				t.Logf("JARM指纹计算成功 - 主机: %s:%d, 哈希: %s", tt.host, tt.port, jarmHash)
			}
		})
	}
}

// TestHashWithDialerConsistency 测试JARM指纹的一致性
// 验证对同一目标多次计算JARM指纹的结果是否一致
func TestHashWithDialerConsistency(t *testing.T) {
	// 创建快速拨号器
	dialer, err := fastdialer.NewDialer(fastdialer.DefaultOptions)
	require.NoError(t, err, "创建拨号器不应该出错")

	host := "www.baidu.com"
	port := 443
	duration := 15

	// 计算第一次JARM指纹
	jarmHash1, err1 := HashWithDialer(dialer, host, port, duration)
	require.NoError(t, err1, "第一次JARM计算不应该出错")
	require.NotEmpty(t, jarmHash1, "第一次JARM哈希不应该为空")

	// 计算第二次JARM指纹
	jarmHash2, err2 := HashWithDialer(dialer, host, port, duration)
	require.NoError(t, err2, "第二次JARM计算不应该出错")
	require.NotEmpty(t, jarmHash2, "第二次JARM哈希不应该为空")

	// 验证两次计算的结果是否一致
	require.Equal(t, jarmHash1, jarmHash2, "对同一目标的JARM指纹应该保持一致")
	
	t.Logf("JARM指纹一致性验证通过 - 哈希: %s", jarmHash1)
}

// TestHashWithDialerDifferentHosts 测试不同主机的JARM指纹差异
// 验证不同主机的JARM指纹应该不同（在大多数情况下）
func TestHashWithDialerDifferentHosts(t *testing.T) {
	// 创建快速拨号器
	dialer, err := fastdialer.NewDialer(fastdialer.DefaultOptions)
	require.NoError(t, err, "创建拨号器不应该出错")

	// 测试不同的主机
	hosts := []struct {
		name string
		host string
		port int
	}{
		{"百度", "www.baidu.com", 443},
		{"指定IP", "**************", 443},
	}

	jarmHashes := make(map[string]string)
	duration := 15

	for _, hostInfo := range hosts {
		t.Run(hostInfo.name, func(t *testing.T) {
			jarmHash, err := HashWithDialer(dialer, hostInfo.host, hostInfo.port, duration)
			
			// 允许某些主机连接失败（网络问题等）
			if err != nil {
				t.Logf("主机 %s 连接失败，跳过: %v", hostInfo.host, err)
				return
			}
			
			require.NotEmpty(t, jarmHash, "JARM哈希不应该为空")
			require.Len(t, jarmHash, 62, "JARM哈希应该是62个字符")
			
			jarmHashes[hostInfo.name] = jarmHash
			t.Logf("主机 %s 的JARM指纹: %s", hostInfo.host, jarmHash)
		})
	}

	// 如果有多个成功的结果，验证它们是否不同
	if len(jarmHashes) >= 2 {
		hashValues := make([]string, 0, len(jarmHashes))
		for _, hash := range jarmHashes {
			hashValues = append(hashValues, hash)
		}
		
		// 注意：不同的主机可能有相同的JARM指纹（如果使用相同的TLS配置）
		// 这里只是记录结果，不强制要求不同
		t.Logf("收集到 %d 个JARM指纹用于比较", len(jarmHashes))
	}
}

// TestHashWithDialerTimeout 测试不同超时时间的影响
// 验证超时时间对JARM指纹计算的影响
func TestHashWithDialerTimeout(t *testing.T) {
	// 创建快速拨号器
	dialer, err := fastdialer.NewDialer(fastdialer.DefaultOptions)
	require.NoError(t, err, "创建拨号器不应该出错")

	host := "www.baidu.com"
	port := 443

	tests := []struct {
		name        string // 测试用例名称
		duration    int    // 超时时间
		description string // 测试描述
	}{
		{
			name:        "短超时",
			duration:    5,
			description: "5秒超时应该足够完成JARM计算",
		},
		{
			name:        "中等超时",
			duration:    10,
			description: "10秒超时应该能稳定完成JARM计算",
		},
		{
			name:        "长超时",
			duration:    20,
			description: "20秒超时应该能处理较慢的网络情况",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			jarmHash, err := HashWithDialer(dialer, host, port, tt.duration)
			
			// 对于较短的超时，允许失败
			if tt.duration <= 5 && err != nil {
				t.Logf("短超时可能导致失败，这是正常的: %v", err)
				return
			}
			
			require.NoError(t, err, tt.description)
			require.NotEmpty(t, jarmHash, "JARM哈希不应该为空")
			require.Len(t, jarmHash, 62, "JARM哈希应该是62个字符")
			
			t.Logf("超时 %d 秒的JARM指纹: %s", tt.duration, jarmHash)
		})
	}
}
