// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 16:50:45
// FilePath: /yaml_scan/pkg/retryablehttp/options.go
// Description: 定义了retryablehttp客户端的配置选项结构和预设配置

package retryablehttp

import (
	"net/http"
	"time"
)

// Options 包含客户端的配置选项
type Options struct {
	// RetryWaitMin 是重试之间的最小等待时间
	// 指定了在重试请求之前要等待的最短时间
	RetryWaitMin time.Duration
	// RetryWaitMax 是重试之间的最大等待时间
	// 指定了在重试请求之前要等待的最长时间
	RetryWaitMax time.Duration
	// Timeout 是请求的最大等待时间
	// 指定了整个HTTP请求（包括可能的重试）的总超时时间
	Timeout time.Duration
	// RetryMax 是最大重试次数
	// 指定了在放弃之前最多尝试多少次请求（总尝试次数 = 1 + RetryMax）
	RetryMax int
	// RespReadLimit 是为了连接复用而要读取的最大HTTP响应大小
	// 当需要排空响应体以重用连接时，这个值限制了要读取的最大字节数
	RespReadLimit int64
	// Verbose 指定是否应打印调试信息
	// 设置为true时，会启用详细的日志记录
	Verbose bool
	// KillIdleConn 指定是否终止所有保持活动的连接
	// 设置为true时，客户端将定期关闭空闲连接，防止长时间运行时的连接泄漏
	KillIdleConn bool
	// CheckRetry 是自定义的重试策略函数
	// 允许指定一个函数来决定是否应该重试失败的请求
	CheckRetry CheckRetry
	// Backoff 是自定义的退避策略函数
	// 允许指定一个函数来决定重试之间应该等待多长时间
	Backoff Backoff
	// NoAdjustTimeout 禁用自动调整HTTP请求超时
	// 默认情况下，客户端会根据总超时时间和重试次数调整单个请求的超时时间
	// 设置为true时，将使用相同的超时时间用于所有请求
	NoAdjustTimeout bool
	// HttpClient 是自定义的http客户端
	// 允许使用预先配置的http.Client，而不是使用默认创建的客户端
	HttpClient *http.Client
	// Trace 启用HTTP请求的跟踪功能
	// 设置为true时，会收集请求的详细跟踪信息，如DNS查询、连接建立和TLS握手时间
	Trace bool
}

// DefaultOptionsSpraying 包含了主机喷洒场景的默认选项
// 主机喷洒场景是指需要向不同主机发送大量请求的情况
var DefaultOptionsSpraying = Options{
	// 最小重试等待时间为1秒
	RetryWaitMin:    1 * time.Second,
	// 最大重试等待时间为30秒
	RetryWaitMax:    30 * time.Second,
	// 总超时时间为60秒
	Timeout:         60 * time.Second,
	// 最多重试5次（总共尝试6次）
	RetryMax:        5,
	// 排空响应体时最多读取4KB
	RespReadLimit:   4096,
	// 启用空闲连接终止，避免资源泄漏
	KillIdleConn:    true,
	// 禁用超时调整，对所有请求使用相同的超时时间
	NoAdjustTimeout: true,
	Trace: true,
}

// DefaultOptionsSingle 包含了主机暴力破解场景的默认选项
// 主机暴力破解场景是指需要向单个主机发送大量请求的情况
var DefaultOptionsSingle = Options{
	// 最小重试等待时间为1秒
	RetryWaitMin:    1 * time.Second,
	// 最大重试等待时间为30秒
	RetryWaitMax:    30 * time.Second,
	// 总超时时间为60秒
	Timeout:         60 * time.Second,
	// 最多重试5次（总共尝试6次）
	RetryMax:        5,
	// 排空响应体时最多读取4KB
	RespReadLimit:   4096,
	// 禁用空闲连接终止，提高对单个主机请求的性能
	KillIdleConn:    false,
	// 禁用超时调整，对所有请求使用相同的超时时间
	NoAdjustTimeout: true,
	Trace: true,
}

