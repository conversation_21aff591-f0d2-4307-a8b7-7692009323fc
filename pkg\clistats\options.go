// Author: chenjb
// Version: V1.0
// Date: 2025-06-18 17:01:33
// FilePath: /yaml_scan/pkg/clistats/options.go
// Description: 统计客户端的配置选项，用于自定义统计客户端的行为
package clistats

// DefaultOptions 统计客户端的默认配置选项
//
// 默认配置使用63636端口，并启用Web接口
var DefaultOptions = Options{
	ListenPort: 63636,
	Web:        true,
}

// Options 自定义统计客户端行为的配置选项
type Options struct {
	// ListenPort 是HTTP服务器监听的端口号
	// 如果指定的端口不可用，将自动选择一个可用端口
	ListenPort int 

	// Web 表示是否启用Web接口提供指标数据
	// 如果为true，将启动HTTP服务器提供/metrics端点
	Web        bool
}
