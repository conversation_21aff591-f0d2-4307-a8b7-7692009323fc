// Author: chenjb
// Version: V1.0
// Date: 2024-12-09 15:45:59
// FilePath: /yaml_scan/pkg/retryabledns/client_queue.go
// Description: 客户端队列 基于heap实现
package retryabledns

import (
	"time"

	"github.com/miekg/dns"
)

// waitingClient 用于表示一个等待 DNS 响应的客户端连接。
type waitingClient struct {
	returnCh    chan *dns.Conn  // 用于接收DNS响应的通道
	doneCh      <-chan struct{} // 用于指示操作完成的通道
	arrivalTime time.Time       // 客户端请求到达的时间
	index       int             // 用于管理优先级
}

// clientQueue 实现了 heap.Interface
type clientQueue []*waitingClient

// Len: 返回队列中元素的数量
//
//	@receiver pq clientQueue:
//	@return int int:
func (pq clientQueue) Len() int { return len(pq) }

// Less: 比较队列中两个元素的优先级
// 这里使用 arrivalTime 来决定优先级，较早到达的请求优先级更高
//
//	@receiver pq clientQueue:
//	@param i int:
//	@param j int:
//	@return bool bool:
func (pq clientQueue) Less(i, j int) bool {
	return pq[i].arrivalTime.Before(pq[j].arrivalTime)
}

// Swap: 交换队列中两个元素的位置
//
//	@receiver pq clientQueue:
//	@param i int:
//	@param j int:
func (pq clientQueue) Swap(i, j int) {
	pq[i], pq[j] = pq[j], pq[i]
	pq[i].index = i
	pq[j].index = j
}

// Push:将一个新元素添加到队列中
//
//	@receiver pq *clientQueue:
//	@param x any:
func (pq *clientQueue) Push(x any) {
	n := len(*pq)
	item := x.(*waitingClient)
	item.index = n
	*pq = append(*pq, item)
}

// Pop: 从队列中移除并返回优先级最高的元素
//
//	@receiver pq *clientQueue:
//	@return any any:
func (pq *clientQueue) Pop() any {
	old := *pq
	n := len(old)
	item := old[n-1]
	old[n-1] = nil  // avoid memory leak
	item.index = -1 // for safety
	*pq = old[0 : n-1]
	return item
}
