package stringsutil

import (
	"testing"

	"github.com/stretchr/testify/require"
)


func TestContainsAny(t *testing.T) {
    tests := []struct {
        input    string
        substrs  []string
        expected bool
    }{
        {"Hello, world!", []string{"world", "golang"}, true},  // 包含 "world"
        {"Hello, world!", []string{"golang", "python"}, false}, // 不包含 "golang" 和 "python"
        {"Go is awesome", []string{"Go", "is"}, true},         // 包含 "Go" 和 "is"
        {"Go is awesome", []string{"java", "ruby"}, false},    // 不包含 "java" 和 "ruby"
        {"", []string{"empty"}, false},                          // 空字符串，不包含任何子字符串
        {"Hello, world!", []string{}, false},                   // 没有子字符串，返回 false
    }

    for _, test := range tests {
        result := ContainsAny(test.input, test.substrs...)
        if result != test.expected {
            t.<PERSON>rrorf("ContainsAny(%q, %v) = %v; want %v", test.input, test.substrs, result, test.expected)
        }
    }
}

func TestContainsAnyI(t *testing.T) {
	tests := []struct {
		input    string
		substrs  []string
		expected bool
	}{
		{"Hello World", []string{"hello", "world"}, true}, // 不区分大小写匹配
		{"GoLang", []string{"golang", "python"}, true},    // 不区分大小写匹配
		{"Test String", []string{"test", "string"}, true}, // 不区分大小写匹配
		{"Example", []string{"sample", "test"}, false},     // 不匹配
		{"", []string{"empty"}, false},                      // 空字符串，不匹配
		{"CaseSensitive", []string{"casesensitive"}, true}, // 完全匹配
		{"MixedCase", []string{"mixedcase"}, true},         // 完全匹配
		{"NoMatchHere", []string{"match", "here"}, true},   // 匹配
		{"NoMatch", []string{"yes", "no"}, true},          // 匹配
	}

	for _, test := range tests {
		result := ContainsAnyI(test.input, test.substrs...)
		if result != test.expected {
			t.Errorf("ContainsAnyI(%q, %v) = %v; want %v", test.input, test.substrs, result, test.expected)
		}
	}
}


func TestEqualFoldAny(t *testing.T) {
    tests := []struct {
        input    string
        substrs  []string
        expected bool
    }{
        {"hello", []string{"hello", "world"}, true},         // 完全匹配
        {"Hello", []string{"hello", "world"}, true},         // 不区分大小写匹配
        {"HELLO", []string{"hello", "world"}, true},         // 不区分大小写匹配
        {"GoLang", []string{"golang", "python"}, true},      // 不区分大小写匹配
        {"Go", []string{"java", "ruby"}, false},             // 不匹配
        {"", []string{"empty"}, false},                       // 空字符串，不匹配
        {"test", []string{}, false},                          // 没有子字符串，返回 false
    }

    for _, test := range tests {
        result := EqualFoldAny(test.input, test.substrs...)
        if result != test.expected {
            t.Errorf("EqualFoldAny(%q, %v) = %v; want %v", test.input, test.substrs, result, test.expected)
        }
    }
}

// TestHasPrefixAny 测试 HasPrefixAny 函数
func TestHasPrefixAny(t *testing.T) {
	tests := []struct {
		input    string
		prefixes []string
		expected bool
	}{
		{
			input:    "hello world",
			prefixes: []string{"he", "hi", "ho"},
			expected: true, // "hello world" 以 "he" 开头
		},
		{
			input:    "goodbye world",
			prefixes: []string{"he", "hi", "ho"},
			expected: false, // "goodbye world" 不以任何给定前缀开头
		},
		{
			input:    "test string",
			prefixes: []string{"te", "test", "t"},
			expected: true, // "test string" 以 "test" 开头
		},
		{
			input:    "another test",
			prefixes: []string{"an", "another"},
			expected: true, // "another test" 以 "another" 开头
		},
		{
			input:    "example",
			prefixes: []string{"ex", "ample"},
			expected: true, // "example" 以 "ex" 开头
		},
		{
			input:    "example",
			prefixes: []string{"ample", "x"},
			expected: false, // "example" 不以 "ample" 或 "x" 开头
		},
		{
			input:    "",
			prefixes: []string{"he", "hi"},
			expected: false, // 空字符串不以任何前缀开头
		},
		{
			input:    "test",
			prefixes: []string{},
			expected: false, // 没有前缀时，返回 false
		},
	}

	for _, test := range tests {
		result := HasPrefixAny(test.input, test.prefixes...)
		if result != test.expected {
			t.Errorf("HasPrefixAny(%q, %v) = %v; expected %v", test.input, test.prefixes, result, test.expected)
		}
	}
}


func TestTrimPrefixAny(t *testing.T) {
	tests := []struct {
		input    string
		prefixes []string
		expected string
	}{
		{"foobar", []string{"foo"}, "bar"},
		{"foobar", []string{"bar"}, "foobar"},
		{"foobar", []string{"foo", "bar"}, ""},
		{"foobar", []string{"fo", "o"}, "bar"},
		{"foobar", []string{"baz"}, "foobar"},
		{"foobarbaz", []string{"foo", "bar"}, "baz"},
		{"foobarbaz", []string{"foo", "baz"}, "barbaz"},
	}

	for _, test := range tests {
		result := TrimPrefixAny(test.input, test.prefixes...)
		if result != test.expected {
			t.Errorf("TrimPrefixAny(%q, %v) = %q; expected %q", test.input, test.prefixes, result, test.expected)
		}
	}
}

func TestTrimSuffixAny(t *testing.T) {
	tests := []struct {
		input    string
		suffixes []string
		expected string
	}{
		{"foobar", []string{"bar"}, "foo"},
		{"foobar", []string{"foo"}, "foobar"},
		{"foobar", []string{"bar", "foo"}, ""},
		{"foobar", []string{"ar", "b"}, "foo"},
		{"foobar", []string{"baz"}, "foobar"},
		{"foobarbaz", []string{"baz", "bar"}, "foo"},
		{"foobarbaz", []string{"baz", "foo"}, "foobar"},
	}

	for _, test := range tests {
		result := TrimSuffixAny(test.input, test.suffixes...)
		if result != test.expected {
			t.Errorf("TrimSuffixAny(%q, %v) = %q; expected %q", test.input, test.suffixes, result, test.expected)
		}
	}
}

// TestReverse 测试 Reverse 函数的正确性
func TestReverse(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"Hello, World!", "!dlroW ,olleH"}, // 普通字符串
		{"", ""},                            // 空字符串
		{"a", "a"},                          // 单字符字符串
		{"abc", "cba"},                      // 多字符字符串
		{"Hello, 世界", "界世 ,olleH"},      // 包含 Unicode 字符
		{"Go 是一个很棒的语言", "言语的棒很个一是 oG"}, // 中文字符串
	}

	for _, test := range tests {
		result := Reverse(test.input)
		if result != test.expected {
			t.Errorf("Reverse(%q) = %q; want %q", test.input, result, test.expected)
		}
	}
}

// TestSplitAny 测试 SplitAny 函数的基本情况。
func TestSplitAny(t *testing.T) {
    tests := []struct {
        input    string   // 输入字符串
        seps     []string // 分隔符列表
        expected []string // 预期的输出
    }{
        {
            input:    "a,b;c:d",
            seps:     []string{",", ";", ":"},
            expected: []string{"a", "b", "c", "d"},
        },
        {
            input:    "hello world",
            seps:     []string{" "},
            expected: []string{"hello", "world"},
        },
        {
            input:    "one,two;three:four",
            seps:     []string{",", ";", ":"},
            expected: []string{"one", "two", "three", "four"},
        },
        {
            input:    "a--b--c",
            seps:     []string{"-"},
            expected: []string{"a", "b", "c"},
        },
        {
            input:    "a b c",
            seps:     []string{" ", ","},
            expected: []string{"a", "b", "c"},
        },
        {
            input:    "",
            seps:     []string{","},
            expected: []string{}, // 空字符串的特殊情况
        },
    }

    for _, test := range tests {
        result := SplitAny(test.input, test.seps...)
        require.Equal(t, test.expected, result, "For input %q with separators %v, expected %v but got %v", test.input, test.seps, test.expected, result)
    }
}