// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 19:11:32
// FilePath: /yaml_scan/utils/reader/rawmode/raw_mode_posix.go
// Description: 提供POSIX系统下终端原始模式的实现，包含终端属性获取和设置的相关函数
package rawmode

import (
	"errors"
	"os"
	"syscall"
	"unsafe"
)

func init() {
	// 为GetMode赋值，调用本地的getMode函数获取终端模式
	GetMode = func(std *os.File) (interface{}, error) {
		return getMode(std)
	}

	// 为SetMode赋值，将interface{}类型转换为*syscall.Termios后调用本地setMode函数
	SetMode = func(std *os.File, mode interface{}) error {
		m, ok := mode.(*syscall.Termios)
		if !ok {
			return errors.New("invalid syscall.Termios")
		}
		return setMode(std, m)
	}

	// 为SetRawMode赋值，将interface{}类型转换为*syscall.Termios后调用本地setRawMode函数
	SetRawMode = func(std *os.File, mode interface{}) error {
		m, ok := mode.(*syscall.Termios)
		if !ok {
			return errors.New("invalid syscall.Termios")
		}
		return setRawMode(std, m)
	}

	// 为Read赋值，调用本地的read函数从文件描述符读取数据
	Read = func(std *os.File, buf []byte) (int, error) {
		return read(std, buf)
	}
}

// getTermios 从文件描述符获取终端属性结构体
// @param fd uintptr: 文件描述符
// @return *syscall.Termios *syscall.Termios: 终端属性结构体指针
// @return error error: 可能的错误
func getTermios(fd uintptr) (*syscall.Termios, error) {
	var t syscall.Termios
	// 使用系统调用SYS_IOCTL获取终端属性
	// TCGETS是获取终端属性的ioctl命令
	// 使用unsafe.Pointer将t的地址转换为uintptr，以便传递给系统调用
	_, _, err := syscall.Syscall6(
		syscall.SYS_IOCTL,
		os.Stdin.Fd(),
		TCGETS,
		uintptr(unsafe.Pointer(&t)),
		0, 0, 0)

	return &t, err
}

// setTermios 设置文件描述符的终端属性
// @param fd uintptr: 文件描述符
// @param term *syscall.Termios: 要设置的终端属性结构体指针
// @return error error: 可能的错误
func setTermios(fd uintptr, term *syscall.Termios) error {
	// 使用系统调用SYS_IOCTL设置终端属性
	// TCSETS是设置终端属性的ioctl命令
	// 使用unsafe.Pointer将term的地址转换为uintptr，以便传递给系统调用
	_, _, err := syscall.Syscall6(
		syscall.SYS_IOCTL,
		os.Stdin.Fd(),
		TCSETS,
		uintptr(unsafe.Pointer(term)),
		0, 0, 0)
	return err
}

// setRaw 将终端属性结构体修改为原始模式
// 这种模式下，终端将逐字符处理输入，不会对特殊字符（如Ctrl+C）进行特殊处理
//
// @param term *syscall.Termios: 要修改的终端属性结构体指针
func setRaw(term *syscall.Termios) {
	// 这里尝试复制termios(3)手册页中为cfmakeraw记录的行为
	
	// 清除输入标志位
	// IGNBRK: 忽略BREAK条件
	// BRKINT: 在BREAK时发送中断信号
	// PARMRK: 标记奇偶校验错误
	// ISTRIP: 剥离第8位
	// INLCR: 将NL转换为CR
	// IGNCR: 忽略CR
	// ICRNL: 将CR转换为NL
	// IXON: 启用输出流控制
	term.Iflag &^= syscall.IGNBRK | syscall.BRKINT | syscall.PARMRK | syscall.ISTRIP | syscall.INLCR | syscall.IGNCR | syscall.ICRNL | syscall.IXON
	// 清除本地标志位
	// ECHO: 回显输入的字符
	// ECHONL: 回显NL字符
	// ICANON: 使用标准行编辑功能
	// ISIG: 当收到INTR、QUIT、SUSP或DSUSP字符时，生成相应信号
	// IEXTEN: 启用实现定义的输入处理
	term.Lflag &^= syscall.ECHO | syscall.ECHONL | syscall.ICANON | syscall.ISIG | syscall.IEXTEN
	// 清除控制标志位中的字符大小位和奇偶校验位
	term.Cflag &^= syscall.CSIZE | syscall.PARENB
	// 设置控制标志位中的字符大小为8位
	term.Cflag |= syscall.CS8
	// 设置等待字符的最小数量为1，即每次至少读取一个字符
	term.Cc[syscall.VMIN] = 1
	// 设置等待超时为0，表示不超时
	term.Cc[syscall.VTIME] = 0
}

func getMode(std *os.File) (*syscall.Termios, error) {
	return getTermios(os.Stdin.Fd())
}

func setMode(std *os.File, mode *syscall.Termios) error {
	return setTermios(os.Stdin.Fd(), mode)
}


func setRawMode(std *os.File, mode *syscall.Termios) error {
	setRaw(mode)
	return SetMode(std, mode)
}

// read 从指定文件读取数据到缓冲区
// @param std *os.File: 
// @param buf []byte: 
// @return int int: 
// @return error error: 
func read(std *os.File, buf []byte) (int, error) {
	return syscall.Read(int(os.Stdin.Fd()), buf)
}

