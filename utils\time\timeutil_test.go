//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-20 16:21:23
//FilePath: /yaml_scan/utils/time/timeutil_test.go
//Description:

package timeutil

import (
	"testing"
	"time"
)

func TestParseDuration(t *testing.T) {
	tests := []struct {
		input    string
		expected time.Duration
	}{
		{"10", 10 * time.Second},          // 默认秒
		{"10s", 10 * time.Second},         // 秒
		{"5m", 5 * time.Minute},           // 分钟
		{"2h", 2 * time.Hour},             // 小时
		{"1d", 24 * time.Hour},            // 天
		{"300ms", 300 * time.Millisecond}, // 毫秒
	}

	for _, test := range tests {
		result, err := ParseDuration(test.input)
		if err != nil {
			t.Errorf("Unexpected error for input %q: %v", test.input, err)
		}
		if result != test.expected {
			t.Errorf("For input %q, expected %v, but got %v", test.input, test.expected, result)
		}
	}
}