//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-21 16:30:28
//FilePath: /yaml_scan/pkg/goflags/usage.go
//Description:

package goflags

import (
	"flag"
	"fmt"
	"io"
	"os"
	"reflect"
	"strings"
	"text/tabwriter"
)

// createUsageFlagNames: 生成命令行标志的使用说明字符串
//
//	@param data *FlagData: 指向 FlagData 结构的指针，包含标志的短名称和长名称。
//	@return string string: 返回格式化的标志名称字符串。
//
// 注意: 如果短名称和长名称都为空，则该函数会引发 panic。
func createUsageFlagNames(data *FlagData) string {
	// 初始化标志名称字符串，前面加上两个空格和一个制表符
	flagNames := strings.Repeat(" ", 2) + "\t"

	// 用于存储有效的标志名称
	var validFlags []string
	// 定义一个内部函数，用于添加有效的标志参数
	addValidParam := func(value string) {
		if !isEmpty(value) {
			validFlags = append(validFlags, fmt.Sprintf("-%s", value))
		}
	}

	// 添加短标志和长标志
	addValidParam(data.short)
	addValidParam(data.long)

	// 如果没有有效的标志，抛出 panic
	if len(validFlags) == 0 {
		panic("CLI arguments cannot be empty.")
	}

	// 将有效的标志名称连接成字符串，并返回
	flagNames += strings.Join(validFlags, ", ")
	return flagNames
}

// createUsageTypeAndDescription:生成命令行标志的类型和描述信息
//
//	@param currentFlag *flag.Flag:指向 flag.Flag 结构的指针，表示当前的命令行标志。
//	@param valueType reflect.Type:反射类型，表示与标志关联的值的类型。
//	@return string string: 返回格式化的标志类型和描述信息。
func createUsageTypeAndDescription(currentFlag *flag.Flag, valueType reflect.Type) string {
	// 用于存储最终的结果字符串
	var result string

	// 获取标志的显示类型和使用说明
	flagDisplayType, usage := flag.UnquoteUsage(currentFlag)
	// 如果标志的显示类型不为空
	if len(flagDisplayType) > 0 {
		// 检查标志的显示类型是否为 "value"（在 goflags 库中硬编码）
		if flagDisplayType == "value" {
			// 根据值的类型进行处理
			switch valueType.Kind() {
			case reflect.Ptr:
				// 如果值是指针类型

				// 获取指针指向的元素类型
				pointerTypeElement := valueType.Elem()
				switch pointerTypeElement.Kind() {
				case reflect.Slice, reflect.Array: // 如果元素类型是切片或数组
					switch pointerTypeElement.Elem().Kind() {
					case reflect.String: // 如果元素类型是字符串
						flagDisplayType = "string[]" // 设置显示类型为 "string[]"
					default:
						flagDisplayType = "value[]" // 其他类型设置为 "value[]"
					}
				}
			}
		}
		// 将标志的显示类型添加到结果字符串中
		result += " " + flagDisplayType
	}
	// 添加使用说明到结果字符串中
	result += "\t\t"
	// 格式化使用说明
	result += strings.ReplaceAll(usage, "\n", "\n"+strings.Repeat(" ", 4)+"\t")
	return result
}

// isZeroValue: 判断给定的字符串值是否表示标志的零值。
//
//	@param f *flag.Flag:  指向 flag.Flag 结构的指针，表示当前的命令行标志。
//	@param value string: 要检查的字符串值。
//	@return bool bool: 如果 value 是标志的零值，则返回 true；否则返回 false。
func isZeroValue(f *flag.Flag, value string) bool {
	// 构建标志值类型的零值，并检查调用其 String 方法的结果是否等于传入的值。
	// 这在值类型本身不是接口类型时有效。
	// 获取标志值的类型
	valueType := reflect.TypeOf(f.Value)
	// 声明一个反射值用于存储零值
	var zeroValue reflect.Value
	// 如果值类型是指针类型
	if valueType.Kind() == reflect.Ptr {
		// 创建指向元素类型的零值指针
		zeroValue = reflect.New(valueType.Elem())
	} else {
		// 创建值类型的零值
		zeroValue = reflect.Zero(valueType)
	}
	// 返回传入的值是否等于零值的字符串表示
	return value == zeroValue.Interface().(flag.Value).String()
}

// createUsageDefaultValue: 生成包含默认值的使用说明字符串。
//
//	@param data *FlagData:指向 FlagData 结构的指针，包含标志的默认值。
//	@param currentFlag *flag.Flag:指向 flag.Flag 结构的指针，表示当前的命令行标志。
//	@param valueType reflect.Type:反射类型，表示与标志关联的值的类型。
//	@return string string: 返回格式化的默认值字符串，如果默认值是零值则返回空字符串。
func createUsageDefaultValue(data *FlagData, currentFlag *flag.Flag, valueType reflect.Type) string {
	// 检查当前标志的默认值是否为零值
	if !isZeroValue(currentFlag, currentFlag.DefValue) {
		// 初始化默认值模板
		defaultValueTemplate := " (default "
		switch valueType.String() {
		case "*flag.stringValue":
			defaultValueTemplate += "%q" // 如果是字符串类型，使用双引号格式化
		default:
			defaultValueTemplate += "%v" // 其他类型使用默认格式化
		}
		defaultValueTemplate += ")"

		return fmt.Sprintf(defaultValueTemplate, data.defaultValue)
	}
	return ""
}

// createUsageString: 生成命令行标志的完整使用说明字符串。
//
//	@param data *FlagData: 指向 FlagData 结构的指针，包含标志的相关信息。
//	@param currentFlag *flag.Flag: 指向 flag.Flag 结构的指针，表示当前的命令行标志。
//	@return string string: 返回格式化的使用说明字符串，包含标志名称、类型、描述和默认值。
func createUsageString(data *FlagData, currentFlag *flag.Flag) string {
	// 获取当前标志值的反射类型
	valueType := reflect.TypeOf(currentFlag.Value)
	// 生成标志名称的使用说明
	result := createUsageFlagNames(data)
	// 生成标志类型和描述的使用说明
	result += createUsageTypeAndDescription(currentFlag, valueType)
	// 生成默认值的使用说明
	result += createUsageDefaultValue(data, currentFlag, valueType)

	return result
}

// displayGroupUsageFunc: 用于显示特定标志组的使用说明。它会输出组的描述，并列出该组中的所有标志及其使用说明。
//
//	@receiver flagSet *FlagSet:
//	@param uniqueDeduper *uniqueDeduper: 指向 uniqueDeduper 结构的指针，用于确保标志的唯一性。
//	@param group groupData: groupData 结构，表示当前的标志组。
//	@param cliOutput io.Writer: io.Writer 接口，用于输出到命令行。
//	@param writer *tabwriter.Writer: 指向 tabwriter.Writer 结构的指针，用于格式化输出。
//	@return []string []string: 返回未分组的标志的使用说明字符串列表。
func (flagSet *FlagSet) displayGroupUsageFunc(uniqueDeduper *uniqueDeduper, group groupData, cliOutput io.Writer, writer *tabwriter.Writer) []string {
	// 输出组的描述
	fmt.Fprintf(cliOutput, "%s:\n", normalizeGroupDescription(group.description))
	// 用于存储未分组的标志的使用说明
	var otherOptions []string
	// 遍历标志集中的所有标志
	flagSet.flagKeys.forEach(func(key string, data *FlagData) {
		// 查找当前标志
		if currentFlag := flagSet.CommandLine.Lookup(key); currentFlag != nil {
			// 如果标志没有分组
			if data.group == "" {
				// 检查标志是否唯一
				if !uniqueDeduper.isUnique(data) {
					return
				}
				// 生成未分组标志的使用说明并添加到列表
				otherOptions = append(otherOptions, createUsageString(data, currentFlag))
				return
			}
			// 如果标志不在当前组中，跳过
			if !strings.EqualFold(data.group, group.name) {
				return
			}
			// 检查标志是否唯一
			if !uniqueDeduper.isUnique(data) {
				return
			}
			// 生成当前组标志的使用说明并输出
			result := createUsageString(data, currentFlag)
			fmt.Fprint(writer, result, "\n")
		}
	})
	// 刷新输出
	writer.Flush()
	fmt.Printf("\n")
	return otherOptions
}

// displaySingleFlagUsageFunc: 显示单个命令行标志的使用说明。
//
//	@receiver flagSet *FlagSet:
//	@param name string: 要显示使用说明的标志名称，可以是短标志或长标志。
//	@param data *FlagData:指向 FlagData 结构的指针，包含标志的相关信息。
//	@param cliOutput io.Writer: io.Writer 接口，用于输出到命令行。
//	@param writer *tabwriter.Writer:指向 tabwriter.Writer 结构的指针，用于格式化输出。
func (flagSet *FlagSet) displaySingleFlagUsageFunc(name string, data *FlagData, cliOutput io.Writer, writer *tabwriter.Writer) {
	// 查找当前标志
	if currentFlag := flagSet.CommandLine.Lookup(name); currentFlag != nil {
		// 生成标志的使用说明
		result := createUsageString(data, currentFlag)
		// 输出使用说明
		fmt.Fprint(writer, result, "\n")
		// 刷新输出
		writer.Flush()
	}
}

// getFlagByName: 根据标志名称查找并返回相应的 FlagData。
//
//	@receiver flagSet *FlagSet:
//	@param name string: 要查找的标志名称，可以是短标志或长标志。
//	@return *FlagData *FlagData: 返回指向 FlagData 结构的指针，如果未找到则返回 nil。
func (flagSet *FlagSet) getFlagByName(name string) *FlagData {
	// 声明一个指向 FlagData 的指针，用于存储找到的标志数据
	var flagData *FlagData
	// 遍历 flagKeys 中的所有标志
	flagSet.flagKeys.forEach(func(key string, data *FlagData) {
		// 检查标志名称是否匹配
		// - 区分大小写
		equal := flagSet.CaseSensitive && (data.long == name || data.short == name)
		// - 不区分大小写
		equalFold := !flagSet.CaseSensitive && (strings.EqualFold(data.long, name) || strings.EqualFold(data.short, name))
		if equal || equalFold {
			flagData = data
			return
		}
	})
	return flagData
}

// usageFuncForGroups: 用于打印带有分组功能的命令行标志的使用说明。
//
//	@receiver flagSet *FlagSet:
//	@param cliOutput io.Writer: io.Writer 接口，用于输出到命令行。
//	@param writer *tabwriter.Writer: 指向 tabwriter.Writer 结构的指针，用于格式化输出。
func (flagSet *FlagSet) usageFuncForGroups(cliOutput io.Writer, writer *tabwriter.Writer) {
	// 创建一个 uniqueDeduper 实例，用于确保标志的唯一性
	uniqueDeduper := newUniqueDeduper()

	// 用于存储未分组的标志的使用说明
	var otherOptions []string

	for _, group := range flagSet.groups {
		// 调用 displayGroupUsageFunc 显示每个组的使用说明，并收集未分组的标志
		otherOptions = append(otherOptions, flagSet.displayGroupUsageFunc(uniqueDeduper, group, cliOutput, writer)...)
	}

	// 打印任何可能未分组的额外标志
	if len(otherOptions) > 0 {
		fmt.Fprintf(cliOutput, "%s:\n", normalizeGroupDescription(flagSet.OtherOptionsGroupName))
		// 输出未分组标志的使用说明
		for _, option := range otherOptions {
			fmt.Fprint(writer, option, "\n")
		}
		// 刷新输出
		writer.Flush()
	}
}

// usageFuncInternal:用于打印命令行标志的使用说明。
//
//	@receiver flagSet *FlagSet:
//	@param writer *tabwriter.Writer: 指向 tabwriter.Writer 结构的指针，用于格式化输出。
func (flagSet *FlagSet) usageFuncInternal(writer *tabwriter.Writer) {
	// 创建一个 uniqueDeduper 实例，用于确保标志的唯一性
	uniqueDeduper := newUniqueDeduper()

	flagSet.flagKeys.forEach(func(key string, data *FlagData) {
		if currentFlag := flagSet.CommandLine.Lookup(key); currentFlag != nil {
			// 检查标志是否唯一
			if !uniqueDeduper.isUnique(data) {
				return
			}
			// 生成标志的使用说明
			result := createUsageString(data, currentFlag)
			fmt.Fprint(writer, result, "\n")
		}
	})
	writer.Flush()
}

// usageFunc:用于处理命令行标志的帮助信息显示。
//
//	@receiver flagSet *FlagSet: 指向 FlagSet 结构的指针，表示当前的标志集。
func (flagSet *FlagSet) usageFunc() {
	// 标志用户是否请求帮助
	var helpAsked bool

	// 仅在用户请求帮助时显示帮助信息
	for _, arg := range os.Args {
		// 去掉前导的短横线
		argStripped := strings.Trim(arg, "-")
		if argStripped == "h" || argStripped == "help" {
			helpAsked = true
		}
	}
	if !helpAsked {
		return
	}

	// 获取命令行输出
	cliOutput := flagSet.CommandLine.Output()
	// 输出描述
	fmt.Fprintf(cliOutput, "%s\n\n", flagSet.description)
	// // 输出用法说明
	fmt.Fprintf(cliOutput, "使用说明:\n\n")

	// 创建 tabwriter 用于格式化输出
	writer := tabwriter.NewWriter(cliOutput, 0, 0, 1, ' ', 0)

	// 如果用户指定了帮助的组，并且我们有组，返回工具的使用函数
	if len(flagSet.groups) > 0 && len(os.Args) == 3 {
		// 获取指定的组
		group := flagSet.getGroupbyName(strings.ToLower(os.Args[2]))
		if group.name != "" {
			// 显示组的使用说明
			flagSet.displayGroupUsageFunc(newUniqueDeduper(), group, cliOutput, writer)
			return
		}
		// 查找指定的标志
		flag := flagSet.getFlagByName(os.Args[2])
		// 显示单个标志的使用说明
		if flag != nil {
			flagSet.displaySingleFlagUsageFunc(os.Args[2], flag, cliOutput, writer)
			return
		}
	}

	// 如果有组，显示所有组的使用说明
	if len(flagSet.groups) > 0 {
		flagSet.usageFuncForGroups(cliOutput, writer)
	} else {
		flagSet.usageFuncInternal(writer) // 否则显示所有标志的使用说明
	}

	// 如果指定了自定义帮助文本，打印它
	if !isEmpty(flagSet.customHelpText) {
		fmt.Fprintf(cliOutput, "\n%s\n", flagSet.customHelpText)
	}
}
