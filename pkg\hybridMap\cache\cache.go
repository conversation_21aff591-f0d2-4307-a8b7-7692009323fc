// Author: chenjb
// Version: V1.0
// Date: 2025-05-16 09:50:37
// FilePath: /yaml_scan/pkg/hybridMap/cache/cache.go
// Description: 内存缓存实现，提供了带过期时间的键值存储能力，支持清理过期项
package cache

import (
	"runtime"
	"sync"
	"time"
)

const (
	// NoExpiration 表示缓存项永不过期
	NoExpiration time.Duration = -1
	// DefaultExpiration 表示使用缓存创建时指定的默认过期时间
	DefaultExpiration time.Duration = 0
)

// keyAndValue 结构体用于在删除过期项时保存键值对
type keyAndValue struct {
	key   string
	value interface{}
}

// Item 表示缓存中的一个项目
// 包含存储的值和过期时间
type Item struct {
	Object     interface{} // 存储的实际对象
	Expiration int64       // 过期时间戳（纳秒）
}

// Expired 检查缓存项是否已过期
// @receiver item
// @return bool bool: 	 true: 已过期 false: 未过期
func (item Item) Expired() bool {
	if item.Expiration == 0 {
		return false
	}
	return time.Now().UnixNano() > item.Expiration
}

// Cache 定义了缓存的基本接口
type Cache interface {
	// SetWithExpiration 设置带过期时间的缓存项
	SetWithExpiration(string, interface{}, time.Duration)

	// Set 使用默认过期时间设置缓存项
	Set(string, interface{})

	// Get 获取缓存项，并返回是否找到
	Get(string) (interface{}, bool)

	// Delete 删除指定键的缓存项
	Delete(string)

	// DeleteExpired 删除所有过期的缓存项
	DeleteExpired()

	// OnEvicted 设置删除缓存项时的回调函数
	OnEvicted(func(string, interface{}))

	// CloneItems 克隆当前所有缓存项（非过期的）
	CloneItems() map[string]Item

	// Scan 使用指定的处理函数遍历缓存
	Scan(func([]byte, []byte) error)

	// ItemCount 返回缓存中的项目数量
	ItemCount() int
}

// CacheMemory 是内存缓存的公开结构体
type CacheMemory struct {
	*cacheMemory
}

// cacheMemory 是内存缓存的内部实现
type cacheMemory struct {
	DefaultExpiration time.Duration
	Items             map[string]Item
	mu                sync.RWMutex
	onEvicted         func(string, interface{})
	janitor           *janitor
}

// SetWithExpiration 设置带过期时间的缓存项
// @receiver c
// @param k string: 键名
// @param x interface{}: 要存储的值
// @param d time.Duration:  过期时间，0表示使用默认过期时间，-1表示永不过期
func (c *cacheMemory) SetWithExpiration(k string, x interface{}, d time.Duration) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.set(k, x, d)
}

// set 是设置缓存项的内部实现（已加锁）
// @receiver c
// @param k string: 键名
// @param x interface{}: 要存储的值
// @param d time.Duration:  过期时间，0表示使用默认过期时间，-1表示永不过期
func (c *cacheMemory) set(k string, x interface{}, d time.Duration) {
	var e int64
	if d == DefaultExpiration {
		d = c.DefaultExpiration
	}
	if d > 0 {
		e = time.Now().Add(d).UnixNano()
	}
	c.Items[k] = Item{
		Object:     x,
		Expiration: e,
	}
}

// set 使用默认过期时间设置缓存项
// @receiver c
// @param k string: 键名
// @param x interface{}: 要存储的值
func (c *cacheMemory) Set(k string, x interface{}) {
	c.SetWithExpiration(k, x, c.DefaultExpiration)
}

// Get 获取缓存项，并返回是否找到
// @receiver c
// @param k string: 键名
// @return interface{} interface{}: 存储的值（如果找到且未过期）
// @return bool bool: 是否找到
func (c *cacheMemory) Get(k string) (interface{}, bool) {
	c.mu.RLock()
	item, found := c.Items[k]
	if !found {
		c.mu.RUnlock()
		return nil, false
	}
	if item.Expiration > 0 {
		if time.Now().UnixNano() > item.Expiration {
			c.mu.RUnlock()
			return nil, false
		}
	}
	c.mu.RUnlock()
	c.refresh(k)
	return item.Object, true
}

// refresh 刷新缓存项的过期时间（延长其生命周期）
// @receiver c
// @param k string: 键名
// @return bool bool: 是否成功刷新
func (c *cacheMemory) refresh(k string) bool {
	c.mu.Lock()
	defer c.mu.Unlock()

	item, found := c.Items[k]
	if !found {
		return false
	}
	item.Expiration = time.Now().Add(c.DefaultExpiration).UnixNano()
	return true
}

// Delete 删除指定键的缓存项
// @receiver c
// @param k string: 键名
func (c *cacheMemory) Delete(k string) {
	c.mu.Lock()
	v, evicted := c.delete(k)
	c.mu.Unlock()
	if evicted {
		c.onEvicted(k, v)
	}
}

// delete 是删除缓存项的内部实现（已加锁）
// @receiver c
// @param k string: 键名
// @return interface{} interface{}: 删除的值
// @return bool bool: 是否成功
func (c *cacheMemory) delete(k string) (interface{}, bool) {
	if c.onEvicted != nil {
		if v, found := c.Items[k]; found {
			delete(c.Items, k)
			return v.Object, true
		}
	}
	delete(c.Items, k)
	return nil, false
}

// DeleteExpired 删除所有过期的缓存项
func (c *cacheMemory) DeleteExpired() {
	var evictedItems []keyAndValue
	now := time.Now().UnixNano()
	c.mu.Lock()
	for k, v := range c.Items {
		// "Inlining" of expired
		if v.Expiration > 0 && now > v.Expiration {
			ov, evicted := c.delete(k)
			if evicted {
				evictedItems = append(evictedItems, keyAndValue{k, ov})
			}
		}
	}
	c.mu.Unlock()
	for _, v := range evictedItems {
		c.onEvicted(v.key, v.value)
	}
}

// OnEvicted 设置删除缓存项时的回调函数
// @receiver c
// @param f func(string, interface{}): 删除项目时调用的函数，接收被删除的键和值
func (c *cacheMemory) OnEvicted(f func(string, interface{})) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.onEvicted = f
}

// Scan 使用指定的处理函数遍历缓存
// @receiver c
// @param f func([]byte, []byte) error: 处理函数，接收键和值作为字节数组
func (c *cacheMemory) Scan(f func([]byte, []byte) error) {
	c.mu.Lock()
	defer c.mu.Unlock()

	for k, item := range c.Items {
		if f([]byte(k), item.Object.([]byte)) != nil {
			break
		}
	}
}

// CloneItems 克隆当前所有缓存项（非过期的）
// @receiver c
// @return map map: 包含所有非过期缓存项的映射表
func (c *cacheMemory) CloneItems() map[string]Item {
	c.mu.RLock()
	defer c.mu.RUnlock()
	m := make(map[string]Item, len(c.Items))
	now := time.Now().UnixNano()
	for k, v := range c.Items {
		// "Inlining" of Expired
		if v.Expiration > 0 {
			if now > v.Expiration {
				continue
			}
		}
		m[k] = v
	}
	return m
}

// ItemCount 返回缓存中的项目数量
// @receiver c
// @return int int: 缓存项数量
func (c *cacheMemory) ItemCount() int {
	c.mu.RLock()
	defer c.mu.RUnlock()
	n := len(c.Items)

	return n
}

// Empty 清空缓存中的所有项目
// @receiver c
func (c *cacheMemory) Empty() {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.Items = map[string]Item{}
}

// newCache 创建一个新的缓存实例
// @param de time.Duration: 默认过期时间
// @param m map[string]Item: 初始缓存项
// @return *cacheMemory *cacheMemory: 缓存实例
func newCache(de time.Duration, m map[string]Item) *cacheMemory {
	if de == 0 {
		de = -1
	}
	c := &cacheMemory{
		DefaultExpiration: de,
		Items:             m,
	}
	return c
}

// newCacheWithJanitor 创建带清理器的缓存实例
// @param de time.Duration: 默认过期时间
// @param ci time.Duration: 清理间隔时间
// @param m map[string]Item: 初始缓存项
// @return *CacheMemory *CacheMemory: 缓存实例
func newCacheWithJanitor(de time.Duration, ci time.Duration, m map[string]Item) *CacheMemory {
	c := newCache(de, m)
	w := &CacheMemory{
		cacheMemory: c,
	}
	if ci > 0 {
		runJanitor(c, ci)
		runtime.SetFinalizer(w, func(c *CacheMemory) {
			stopJanitor(c.cacheMemory)
		})
	}
	return w
}

// New 创建一个新的缓存实例
//
// @param defaultExpiration time.Duration: 默认过期时间
// @param cleanupInterval time.Duration: 清理间隔时间，为0表示不进行自动清理
//
// @return *CacheMemory *CacheMemory: 缓存实例
func New(defaultExpiration, cleanupInterval time.Duration) *CacheMemory {
	items := make(map[string]Item)
	return newCacheWithJanitor(defaultExpiration, cleanupInterval, items)
}

// NewFrom 从已有的项目创建缓存实例
//
// 参数:
//   - defaultExpiration: 默认过期时间
//   - cleanupInterval: 清理间隔时间，为0表示不进行自动清理
//   - items: 初始缓存项
//
// 返回:
//   - 缓存实例
func NewFrom(defaultExpiration, cleanupInterval time.Duration, items map[string]Item) *CacheMemory {
	return newCacheWithJanitor(defaultExpiration, cleanupInterval, items)
}
