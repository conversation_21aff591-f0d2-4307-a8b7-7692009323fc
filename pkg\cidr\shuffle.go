// Author: chenjb
// Version: V1.0
// Date: 2025-06-04 10:38:51
// FilePath: /yaml_scan/pkg/cidr/shuffle.go
// Description: 提供IP地址和端口的随机化访问功能，使用BlackRock算法对CIDR块中的IP地址进行随机排列
package cidr

import (
	"fmt"
	"net"
	iputil "yaml_scan/utils/ip"
)

// Item 表示IP地址和端口的组合
// 用于在扫描过程中表示目标服务器端点
type Item struct {
	IP   string // 目标IP地址
	Port int    // 目标端口号
}

// String  返回格式化的 "ip:port" 字符串表示
// @receiver i
// @return string string: 格式为"IP:Port"的字符串
func (i Item) String() string {
	return net.JoinHostPort(i.IP, fmt.Sprintf("%d", i.Port))
}

// ShuffleCidrsWithSeed 使用BlackRock算法随机访问CIDR范围内的所有IP地址
// @param cidrs []*net.IPNet:  要随机访问的CIDR网络列表
// @param seed int64: 随机化种子，确保可重现性
// @return chan chan:  返回包含随机排列的IP地址的通道
func ShuffleCidrsWithSeed(cidrs []*net.IPNet, seed int64) chan Item {
	// 合并和压缩CIDR列表，减少重叠和提高效率
	cidrs, _ = CoalesceCIDRs(cidrs)
	// 创建用于返回结果的通道
	out := make(chan Item)
	// 启动goroutine执行异步处理
	go func(out chan Item, cidrs []*net.IPNet) {
		defer close(out)
		// 计算所有CIDR中包含的IP地址总数
		targetsCount := int64(TotalIPSInCidrs(cidrs))
		Range := targetsCount
		// 创建BlackRock实例用于随机化
		br := New(Range, seed)
		for index := int64(0); index < Range; index++ {
			// 使用BlackRock算法将顺序索引转换为随机索引
			ipIndex := br.Shuffle(index)
			// 根据随机索引选择IP地址
			ip := PickIP(cidrs, ipIndex)
			if ip == "" {
				continue
			}
			out <- Item{IP: ip}
		}
	}(out, cidrs)
	return out
}

// ShuffleCidrsWithPortsAndSeed 使用BlackRock算法随机访问CIDR范围内所有IP地址和端口的组合
// @param cidrs []*net.IPNet: 要随机访问的CIDR网络列表
// @param ports []int:  要扫描的端口列表
// @param seed int64:  随机化种子，确保可重现性
// @return chan chan:  返回包含随机排列的IP:端口组合的通道
func ShuffleCidrsWithPortsAndSeed(cidrs []*net.IPNet, ports []int, seed int64) chan Item {
	// 合并和压缩CIDR列表，减少重叠和提高效率
	cidrs, _ = CoalesceCIDRs(cidrs)
	out := make(chan Item)
	// 启动goroutine执行异步处理
	go func(out chan Item, cidrs []*net.IPNet) {
		defer close(out)
		// 计算所有CIDR中包含的IP地址总数
		targetsCount := int64(TotalIPSInCidrs(cidrs))
		// 计算端口数量
		portsCount := int64(len(ports))
		// 计算总的组合数：IP地址数 * 端口数
		Range := targetsCount * portsCount
		// 创建BlackRock实例用于随机化
		br := New(Range, seed)
		// 遍历所有可能的组合索引
		for index := int64(0); index < Range; index++ {
			// 使用BlackRock算法将顺序索引转换为随机索引
			xxx := br.Shuffle(index)
			ipIndex := xxx / portsCount
			portIndex := int(xxx % portsCount)
			// 根据索引选择具体的IP地址和端口
			ip := PickIP(cidrs, ipIndex)
			port := PickPort(ports, portIndex)

			// 如果IP为空或端口小于等于0，跳过此组合
			if ip == "" || port <= 0 {
				continue
			}
			out <- Item{IP: ip, Port: port}
		}
	}(out, cidrs)
	return out
}

// PickIP 从CIDR列表中选择指定索引的IP地址
// @param cidrs []*net.IPNet: CIDR网络列表
// @param index int64: 要选择的IP索引
// @return string string: 选择的IP地址字符串，如果索引超出范围则返回空字符串
func PickIP(cidrs []*net.IPNet, index int64) string {
	for _, target := range cidrs {
		// 计算当前CIDR网络中包含的IP地址数量
		subnetIpsCount := int64(AddressCountIpnet(target))
		// 如果索引小于当前CIDR中的IP数量，则索引指向当前CIDR中的某个IP
		if index < subnetIpsCount {
			// 调用PickSubnetIP从当前CIDR中选择特定索引的IP
			return PickSubnetIP(target, index)
		}
		index -= subnetIpsCount
	}

	return ""
}

// PickSubnetIP 从单个CIDR网络中选择指定索引的IP地址
// @param network *net.IPNet:  CIDR网络
// @param index int64:  子网内的IP索引
// @return string string: 选择的IP地址字符串
func PickSubnetIP(network *net.IPNet, index int64) string {
	return Inet_ntoa(Inet_aton(network.IP) + index).String()
}

// PickPort 从端口列表中选择指定索引的端口
// @param ports []int: 端口列表
// @param index int: 端口索引
// @return int int: 选择的端口号
func PickPort(ports []int, index int) int {
	return ports[index]
}

// CIDRsAsIPNET 将CIDR字符串列表转换为IPNet对象列表
// @param cidrs []string:  CIDR字符串列表
// @return ipnets []*net.IPNet:IPNet对象列表
func CIDRsAsIPNET(cidrs []string) (ipnets []*net.IPNet) {
	for _, cidr := range cidrs {
		// 将每个CIDR字符串转换为IPNet对象并添加到结果列表
		ipnets = append(ipnets, AsIPV4CIDR(cidr))
	}
	return
}

// AsIPV4CIDR 将IPv4地址转换为CIDR表示
// @param ipv4 string: IPv4地址或CIDR字符串
// @return *net.IPNet *net.IPNet: 解析后的IPNet对象，解析失败则返回nil
func AsIPV4CIDR(ipv4 string) *net.IPNet {
	if iputil.IsIPv4(net.ParseIP(ipv4)) {
		ipv4 += "/32"
	}
	_, network, err := net.ParseCIDR(ipv4)
	if err != nil {
		return nil
	}
	return network
}
