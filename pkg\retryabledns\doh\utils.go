//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-17 09:53:49
//FilePath: /yaml_scan/pkg/retryabledns/doh/utils.go
//Description:

package doh

import (
	"crypto/tls"
	"net/http"
	"net/url"
	"time"
)

// ClientOption 定义了一个函数类型，用于配置 http.Client。
type ClientOption func(*http.Client)

// NewHttpClient:  创建一个新的 http.Client 实例，并应用给定的配置选项。
//
//	@param opts ...ClientOption:  可变参数，传入的配置选项函数。
//	@return *http.Client *http.Client: 配置好的 http.Client 实例。
func NewHttpClient(opts ...ClientOption) *http.Client {
	client := &http.Client{}
	for _, opt := range opts {
		opt(client)
	}
	return client
}

// WithTimeout:设置 http.Client 的超时时间。
//
//	@param timeout time.Duration: 超时时间。
//	@return ClientOption ClientOption: 返回一个配置函数，用于设置客户端的超时时间。
func WithTimeout(timeout time.Duration) ClientOption {
	return func(c *http.Client) {
		c.Timeout = timeout
	}
}

// WithInsecureSkipVerify:配置 http.Client 在请求时跳过证书验证。
//
//	@return ClientOption ClientOption: 返回一个配置函数，用于设置客户端跳过证书验证。
func WithInsecureSkipVerify() ClientOption {
	return func(c *http.Client) {
		// 检查 Transport 是否为 *http.Transport 类型
		transport, ok := c.Transport.(*http.Transport)
		if !ok {
			transport = &http.Transport{}
			c.Transport = transport
		}
		if transport.TLSClientConfig == nil {
			transport.TLSClientConfig = &tls.Config{}
		}
		// 设置 InsecureSkipVerify 为 true，跳过证书验证
		transport.TLSClientConfig.InsecureSkipVerify = true
	}
}

// WithProxy: 设置 http.Client 的代理。
//
//	@param proxyURL string:代理的 URL。
//	@return ClientOption ClientOption: 返回一个配置函数，用于设置客户端的代理。
func WithProxy(proxyURL string) ClientOption {
	return func(c *http.Client) {
		if proxyURL == "" {
			return
		}
		// 解析代理 URL
		proxyURL, err := url.Parse(proxyURL)
		if err != nil {
			return
		}

		transport, ok := c.Transport.(*http.Transport)
		if !ok {
			transport = &http.Transport{}
			c.Transport = transport
		}

		// 设置代理
		transport.Proxy = http.ProxyURL(proxyURL)
	}
}
