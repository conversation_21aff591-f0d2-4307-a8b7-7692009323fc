// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 19:11:09
// FilePath: /yaml_scan/utils/reader/rawmode/ramode.go
// Description: 定义终端原始模式操作的接口函数，这些函数将在特定平台的实现文件中被赋值
package rawmode

import (
	"os"
)

var (
	// GetMode 从文件描述符获取终端模式设置
	//
	// 参数:
	//   - std: 标准输入/输出文件指针
	//
	// 返回:
	//   - interface{}: 特定于平台的终端模式设置对象
	//   - error: 获取过程中可能发生的错误
	GetMode func(std *os.File) (interface{}, error)
	
	// SetMode 将终端模式设置应用到文件描述符
	//
	// 参数:
	//   - std: 标准输入/输出文件指针
	//   - mode: 特定于平台的终端模式设置对象
	//
	// 返回:
	//   - error: 设置过程中可能发生的错误
	SetMode func(std *os.File, mode interface{}) error
	
	// SetRawMode 将终端设置为原始模式，在现有模式基础上增加原始控制台标志
	//
	// 参数:
	//   - std: 标准输入/输出文件指针
	//   - mode: 特定于平台的终端模式设置对象
	//
	// 返回:
	//   - error: 设置过程中可能发生的错误
	SetRawMode func(std *os.File, mode interface{}) error
	
	// Read 从文件描述符读取数据到缓冲区
	//
	// 参数:
	//   - std: 标准输入/输出文件指针
	//   - buf: 用于存储读取数据的字节切片
	//
	// 返回:
	//   - int: 读取的字节数
	//   - error: 读取过程中可能发生的错误
	Read func(std *os.File, buf []byte) (int, error)

	// TCSETS 是用于设置终端属性的ioctl命令常量
	// 具体值在平台特定实现中定义
	TCSETS uintptr
	// TCGETS 是用于获取终端属性的ioctl命令常量
	// 具体值在平台特定实现中定义
	TCGETS uintptr
)

