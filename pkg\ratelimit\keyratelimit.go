// Author: chenjb
// Version: V1.0
// Date: 2025-06-12 11:34:53
// FilePath: /yaml_scan/pkg/ratelimit/keyratelimit.go
// Description:实现了基于键的多实例限流器，可以根据不同的键创建和管理多个限流器实例
package ratelimit

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"
)

// Options 包含MultiLimiter的配置选项
type Options struct {
	Key         string        // 唯一标识符，用于区分不同的限流器实例
	IsUnlimited bool          // 是否为无限制模式，无限制模式下不进行限流
	MaxCount    uint          // 最大令牌数量
	Duration    time.Duration // 令牌填充间隔时间
}

// Validate 验证MultiLimiter的配置选项是否有效
// @receiver o
// @return error error: 如果配置无效则返回错误，否则返回nil
func (o *Options) Validate() error {
	// 如果不是无限制模式，则需要验证其他参数
	if !o.IsUnlimited {
		if o.Key == "" {
			return errors.New("MultiLimiter: empty keys not allowed")
		}
		if o.MaxCount == 0 {
			return errors.New("MultiLimiter: maxcount cannot be zero")
		}
		if o.Duration == 0 {
			return errors.New("MultiLimiter: time duration not set")
		}
	}
	return nil
}

// MultiLimiter 是Limiter的包装器，可以基于不同的键管理多个限流器实例
type MultiLimiter struct {
	limiters sync.Map        // 限流器映射表，键是字符串，值是*Limiter
	ctx      context.Context // 上下文，用于控制限流器的生命周期
}

// Add 添加一个新的限流器实例
// @receiver m 
// @param opts *Options:  限流器配置选项
// @return error error: 错误
func (m *MultiLimiter) Add(opts *Options) error {
	 // 验证配置选项
	if err := opts.Validate(); err != nil {
		return err
	}
	var rlimiter *Limiter
	// 创建无限制限流器
	if opts.IsUnlimited {
		rlimiter = NewUnlimited(m.ctx)
	} else {
		rlimiter = New(m.ctx, opts.MaxCount, opts.Duration)
	}
	// ok为true表示键已存在
	_, ok := m.limiters.LoadOrStore(opts.Key, rlimiter)
	if ok {
		return fmt.Errorf("key: %v", opts.Key)
	}
	return nil
}

// GetLimit 返回指定键的限流器当前的限流速率
// @receiver m 
// @param key string: 限流器的键
// @return uint uint: 限流器的最大令牌数
// @return error error: 可能的错误
func (m *MultiLimiter) GetLimit(key string) (uint, error) {
	// 获取指定键的限流器
	limiter, err := m.get(key)
	if err != nil {
		return 0, err
	}
	return limiter.GetLimit(), nil
}

// Take 从指定键的限流器中获取一个令牌
// @receiver m 
// @param key string: 限流器的键
// @return error error: 
func (m *MultiLimiter) Take(key string) error {
	limiter, err := m.get(key)
	if err != nil {
		return err
	}
	limiter.Take()
	return nil
}

// CanTake 检查指定键的限流器是否有可用的令牌
// @receiver m 
// @param key string: 
// @return bool bool: 
func (m *MultiLimiter) CanTake(key string) bool {
	limiter, err := m.get(key)
	if err != nil {
		return false
	}
	return limiter.CanTake()
}

// AddAndTake 如果键不存在则添加一个新的限流器实例，然后获取一个令牌
// @receiver m 
// @param opts *Options: 限流器配置选项
func (m *MultiLimiter) AddAndTake(opts *Options) {
	if limiter, err := m.get(opts.Key); err == nil {
		limiter.Take()
		return
	}
	_ = m.Add(opts)
	_ = m.Take(opts.Key)
}

// Stop 停止指定键的限流器或所有限流器
// @receiver m 
// @param keys ...string: 停止的限流器的键，如果为空则停止所有限流器
func (m *MultiLimiter) Stop(keys ...string) {
	if len(keys) == 0 {
		m.limiters.Range(func(key, value any) bool {
			if limiter, ok := value.(*Limiter); ok {
				limiter.Stop()
			}
			return true
		})
		return
	}
	for _, v := range keys {
		if limiter, err := m.get(v); err == nil {
			limiter.Stop()
		}
	}
}

// get 获取指定键的限流器实例
// @receiver m 
// @param key string: 限流器的键
// @return *Limiter *Limiter: 限流器实例
// @return error error: 可能的错误
func (m *MultiLimiter) get(key string) (*Limiter, error) {
	val, _ := m.limiters.Load(key)
	if val == nil {
		return nil, fmt.Errorf("key: %v", key)
	}
	if limiter, ok := val.(*Limiter); ok {
		return limiter, nil
	}
	return nil, errors.New("MultiLimiter: type assertion of rateLimiter failed in multiLimiter")
}

// NewMultiLimiter 创建一个新的MultiLimiter实例
// @param ctx context.Context:  上下文，用于控制限流器的生命周期
// @param opts *Options: 限流器配置选项
// @return *MultiLimiter *MultiLimiter: 新创建的MultiLimiter实例
// @return error error: 可能的错误
func NewMultiLimiter(ctx context.Context, opts *Options) (*MultiLimiter, error) {
	if err := opts.Validate(); err != nil {
		return nil, err
	}
	multilimiter := &MultiLimiter{
		ctx:      ctx,
		limiters: sync.Map{},
	}
	 // 添加初始限流器实例并返回
	return multilimiter, multilimiter.Add(opts)
}
