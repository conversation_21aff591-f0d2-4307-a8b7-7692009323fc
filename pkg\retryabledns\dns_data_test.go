package retryabledns

import (
	"encoding/json"
	"net"
	"testing"

	"github.com/miekg/dns"
	"github.com/stretchr/testify/require"
)

// TestTrimChars 测试trimChars函数
func TestTrimChars(t *testing.T) {
	result := trimChars("example.com.")
	require.Equal(t, "example.com", result, "trimChars应移除末尾的点号")
	result = trimChars("example.com")
	require.Equal(t, "example.com", result, "trimChars应保持无点号的字符串不变")
}

// TestParseFromRR 测试ParseFromRR方法
func TestParseFromRR(t *testing.T) {
	d := &DNSData{}
	rrs := []dns.RR{
		&dns.A{Hdr: dns.RR_Header{Name: "example.com.", Ttl: 3600}, A: net.ParseIP("*************")},
		&dns.NS{Hdr: dns.RR_Header{Name: "example.com."}, Ns: "ns1.example.com."},
		&dns.CNAME{Hdr: dns.RR_Header{Name: "www.example.com."}, Target: "example.com."},
		&dns.SOA{Hdr: dns.RR_Header{Name: "example.com."}, Ns: "ns1.example.com.", Mbox: "admin.example.com.", Serial: 2023010101, Refresh: 3600, Retry: 600, Expire: 86400, Minttl: 3600},
		&dns.PTR{Hdr: dns.RR_Header{Name: "*************.in-addr.arpa."}, Ptr: "example.com."},
		&dns.MX{Hdr: dns.RR_Header{Name: "example.com."}, Mx: "mail.example.com."},
		&dns.CAA{Hdr: dns.RR_Header{Name: "example.com."}, Value: "issue \"ca.example.com\""},
		&dns.TXT{Hdr: dns.RR_Header{Name: "example.com."}, Txt: []string{"text", "record"}},
		&dns.SRV{Hdr: dns.RR_Header{Name: "_service._tcp.example.com."}, Target: "target.example.com."},
		&dns.AAAA{Hdr: dns.RR_Header{Name: "example.com."}, AAAA: net.ParseIP("2606:2800:220:1:248:1893:25c8:1946")},
	}

	err := d.ParseFromRR(rrs)
	require.NoError(t, err, "ParseFromRR不应返回错误")
	require.Equal(t, uint32(3600), d.TTL, "TTL应设置为3600")
	require.Len(t, d.A, 1, "应有1条A记录")
	require.Equal(t, "*************", d.A[0], "A记录应为*************")
	require.Len(t, d.NS, 1, "应有1条NS记录")
	require.Equal(t, "ns1.example.com", d.NS[0], "NS记录应为ns1.example.com")
	require.Len(t, d.CNAME, 1, "应有1条CNAME记录")
	require.Equal(t, "example.com", d.CNAME[0], "CNAME记录应为example.com")
	require.Len(t, d.SOA, 1, "应有1条SOA记录")
	require.Equal(t, "example.com", d.SOA[0].Name, "SOA Name应为example.com")
	require.Len(t, d.PTR, 1, "应有1条PTR记录")
	require.Equal(t, "example.com", d.PTR[0], "PTR记录应为example.com")
	require.Len(t, d.MX, 1, "应有1条MX记录")
	require.Equal(t, "mail.example.com", d.MX[0], "MX记录应为mail.example.com")
	require.Len(t, d.CAA, 1, "应有1条CAA记录")
	require.Equal(t, "issue \"ca.example.com\"", d.CAA[0], "CAA记录应为issue \"ca.example.com\"")
	require.Len(t, d.TXT, 1, "应有1条TXT记录")
	require.Equal(t, "textrecord", d.TXT[0], "TXT记录应为textrecord")
	require.Len(t, d.SRV, 1, "应有1条SRV记录")
	require.Equal(t, "target.example.com", d.SRV[0], "SRV记录应为target.example.com")
	require.Len(t, d.AAAA, 1, "应有1条AAAA记录")
	require.Equal(t, "2606:2800:220:1:248:1893:25c8:1946", d.AAAA[0], "AAAA记录应为2606:2800:220:1:248:1893:25c8:1946")
	require.Len(t, d.AllRecords, 10, "应有10条AllRecords记录")
}

// TestParseFromMsg 测试ParseFromMsg方法
func TestParseFromMsg(t *testing.T) {
	d := &DNSData{}
	msg := &dns.Msg{
		Answer: []dns.RR{
			&dns.A{Hdr: dns.RR_Header{Name: "example.com.", Ttl: 3600}, A: net.ParseIP("*************")},
		},
		Ns: []dns.RR{
			&dns.NS{Hdr: dns.RR_Header{Name: "example.com."}, Ns: "ns1.example.com."},
		},
		Extra: []dns.RR{
			&dns.AAAA{Hdr: dns.RR_Header{Name: "example.com."}, AAAA: net.ParseIP("2606:2800:220:1:248:1893:25c8:1946")},
		},
	}

	err := d.ParseFromMsg(msg)
	require.NoError(t, err, "ParseFromMsg不应返回错误")
	require.Equal(t, uint32(3600), d.TTL, "TTL应设置为3600")
	require.Len(t, d.A, 1, "应有1条A记录")
	require.Equal(t, "*************", d.A[0], "A记录应为*************")
	require.Len(t, d.NS, 1, "应有1条NS记录")
	require.Equal(t, "ns1.example.com", d.NS[0], "NS记录应为ns1.example.com")
	require.Len(t, d.AAAA, 1, "应有1条AAAA记录")
	require.Equal(t, "2606:2800:220:1:248:1893:25c8:1946", d.AAAA[0], "AAAA记录应为2606:2800:220:1:248:1893:25c8:1946")
	require.Len(t, d.AllRecords, 3, "应有3条AllRecords记录")
}

// TestParseFromEnvelopeChan 测试ParseFromEnvelopeChan方法
func TestParseFromEnvelopeChan(t *testing.T) {
	d := &DNSData{}
	envChan := make(chan *dns.Envelope, 2)
	env1 := &dns.Envelope{
		RR: []dns.RR{
			&dns.A{Hdr: dns.RR_Header{Name: "example.com.", Ttl: 3600}, A: net.ParseIP("*************")},
		},
	}
	env2 := &dns.Envelope{
		RR: []dns.RR{
			&dns.AAAA{Hdr: dns.RR_Header{Name: "example.com."}, AAAA: net.ParseIP("2606:2800:220:1:248:1893:25c8:1946")},
		},
	}

	go func() {
		envChan <- env1
		envChan <- env2
		close(envChan)
	}()

	err := d.ParseFromEnvelopeChan(envChan)
	require.NoError(t, err, "ParseFromEnvelopeChan不应返回错误")
	require.Equal(t, uint32(3600), d.TTL, "TTL应设置为3600")
	require.Len(t, d.A, 1, "应有1条A记录")
	require.Equal(t, "*************", d.A[0], "A记录应为*************")
	require.Len(t, d.AAAA, 1, "应有1条AAAA记录")
	require.Equal(t, "2606:2800:220:1:248:1893:25c8:1946", d.AAAA[0], "AAAA记录应为2606:2800:220:1:248:1893:25c8:1946")
	require.Len(t, d.AllRecords, 2, "应有2条AllRecords记录")
}


// TestContains 测试 contains 方法的正确性
func TestContains(t *testing.T) {
	// 场景 1: 空记录
	t.Run("空记录", func(t *testing.T) {
		d := &DNSData{}
		require.False(t, d.contains(), "空记录应返回 false")
	})

	// 场景 2: 包含 A 记录
	t.Run("包含 A 记录", func(t *testing.T) {
		d := &DNSData{A: []string{"*********"}}
		require.True(t, d.contains(), "包含 A 记录应返回 true")
	})

	// 场景 3: 包含 AAAA 记录
	t.Run("包含 AAAA 记录", func(t *testing.T) {
		d := &DNSData{AAAA: []string{"2001:db8::1"}}
		require.True(t, d.contains(), "包含 AAAA 记录应返回 true")
	})

	// 场景 4: 包含 CNAME 记录
	t.Run("包含 CNAME 记录", func(t *testing.T) {
		d := &DNSData{CNAME: []string{"www.example.com"}}
		require.True(t, d.contains(), "包含 CNAME 记录应返回 true")
	})

	// 场景 5: 包含 MX 记录
	t.Run("包含 MX 记录", func(t *testing.T) {
		d := &DNSData{MX: []string{"mail.example.com"}}
		require.True(t, d.contains(), "包含 MX 记录应返回 true")
	})

	// 场景 6: 包含 NS 记录
	t.Run("包含 NS 记录", func(t *testing.T) {
		d := &DNSData{NS: []string{"ns1.example.com"}}
		require.True(t, d.contains(), "包含 NS 记录应返回 true")
	})

	// 场景 7: 包含 PTR 记录
	t.Run("包含 PTR 记录", func(t *testing.T) {
		d := &DNSData{PTR: []string{"ptr.example.com"}}
		require.True(t, d.contains(), "包含 PTR 记录应返回 true")
	})

	// 场景 8: 包含 TXT 记录
	t.Run("包含 TXT 记录", func(t *testing.T) {
		d := &DNSData{TXT: []string{"text record"}}
		require.True(t, d.contains(), "包含 TXT 记录应返回 true")
	})

	// 场景 9: 包含 SRV 记录
	t.Run("包含 SRV 记录", func(t *testing.T) {
		d := &DNSData{SRV: []string{"_service._tcp.example.com"}}
		require.True(t, d.contains(), "包含 SRV 记录应返回 true")
	})

	// 场景 10: 包含 SOA 记录
	t.Run("包含 SOA 记录", func(t *testing.T) {
		d := &DNSData{SOA: []SOA{{Name: "example.com"}}}
		require.True(t, d.contains(), "包含 SOA 记录应返回 true")
	})

	// 场景 11: 包含 CAA 记录
	t.Run("包含 CAA 记录", func(t *testing.T) {
		d := &DNSData{CAA: []string{"issue \"ca.example.com\""}}
		require.True(t, d.contains(), "包含 CAA 记录应返回 true")
	})
}

// TestDedupe 测试 dedupe 方法的正确性
func TestDedupe(t *testing.T) {
	// 场景 1: 去除重复项
	t.Run("去除重复项", func(t *testing.T) {
		d := &DNSData{
			Resolver:   []string{"*******", "*******", "*******"},
			A:          []string{"*********", "*********"},
			AAAA:       []string{"2001:db8::1", "2001:db8::1"},
			CNAME:      []string{"www.example.com", "www.example.com"},
			MX:         []string{"mail.example.com", "mail.example.com"},
			PTR:        []string{"ptr.example.com", "ptr.example.com"},
			NS:         []string{"ns1.example.com", "ns1.example.com"},
			TXT:        []string{"text record", "text record"},
			SRV:        []string{"_service._tcp.example.com", "_service._tcp.example.com"},
			CAA:        []string{"issue \"ca.example.com\"", "issue \"ca.example.com\""},
			AllRecords: []string{"A:*********", "A:*********"},
		}
		d.dedupe()
		require.Equal(t, []string{"*******", "*******"}, d.Resolver, "Resolver 应去重")
		require.Equal(t, []string{"*********"}, d.A, "A 记录应去重")
		require.Equal(t, []string{"2001:db8::1"}, d.AAAA, "AAAA 记录应去重")
		require.Equal(t, []string{"www.example.com"}, d.CNAME, "CNAME 记录应去重")
		require.Equal(t, []string{"mail.example.com"}, d.MX, "MX 记录应去重")
		require.Equal(t, []string{"ptr.example.com"}, d.PTR, "PTR 记录应去重")
		require.Equal(t, []string{"ns1.example.com"}, d.NS, "NS 记录应去重")
		require.Equal(t, []string{"text record"}, d.TXT, "TXT 记录应去重")
		require.Equal(t, []string{"_service._tcp.example.com"}, d.SRV, "SRV 记录应去重")
		require.Equal(t, []string{"issue \"ca.example.com\""}, d.CAA, "CAA 记录应去重")
		require.Equal(t, []string{"A:*********"}, d.AllRecords, "AllRecords 应去重")
	})

	// 场景 2: 无重复项
	t.Run("无重复项", func(t *testing.T) {
		d := &DNSData{
			Resolver:   []string{"*******", "*******"},
			A:          []string{"*********", "19*******"},
			AAAA:       []string{"2001:db8::1", "2001:db8::2"},
			CNAME:      []string{"www.example.com", "www2.example.com"},
			MX:         []string{"mail.example.com", "mail2.example.com"},
			PTR:        []string{"ptr.example.com", "ptr2.example.com"},
			NS:         []string{"ns1.example.com", "ns2.example.com"},
			TXT:        []string{"text record", "another text"},
			SRV:        []string{"_service._tcp.example.com", "_service2._tcp.example.com"},
			CAA:        []string{"issue \"ca.example.com\"", "issue \"ca2.example.com\""},
			AllRecords: []string{"A:*********", "A:19*******"},
		}
		d.dedupe()
		require.Equal(t, []string{"*******", "*******"}, d.Resolver, "Resolver 不应改变")
		require.Equal(t, []string{"*********", "19*******"}, d.A, "A 记录不应改变")
		require.Equal(t, []string{"2001:db8::1", "2001:db8::2"}, d.AAAA, "AAAA 记录不应改变")
		require.Equal(t, []string{"www.example.com", "www2.example.com"}, d.CNAME, "CNAME 记录不应改变")
		require.Equal(t, []string{"mail.example.com", "mail2.example.com"}, d.MX, "MX 记录不应改变")
		require.Equal(t, []string{"ptr.example.com", "ptr2.example.com"}, d.PTR, "PTR 记录不应改变")
		require.Equal(t, []string{"ns1.example.com", "ns2.example.com"}, d.NS, "NS 记录不应改变")
		require.Equal(t, []string{"text record", "another text"}, d.TXT, "TXT 记录不应改变")
		require.Equal(t, []string{"_service._tcp.example.com", "_service2._tcp.example.com"}, d.SRV, "SRV 记录不应改变")
		require.Equal(t, []string{"issue \"ca.example.com\"", "issue \"ca2.example.com\""}, d.CAA, "CAA 记录不应改变")
		require.Equal(t, []string{"A:*********", "A:19*******"}, d.AllRecords, "AllRecords 不应改变")
	})

	// 场景 3: 空字段
	t.Run("空字段", func(t *testing.T) {
		d := &DNSData{}
		d.dedupe()
		require.Empty(t, d.Resolver, "Resolver 应为空")
		require.Empty(t, d.A, "A 记录应为空")
		require.Empty(t, d.AAAA, "AAAA 记录应为空")
		require.Empty(t, d.CNAME, "CNAME 记录应为空")
		require.Empty(t, d.MX, "MX 记录应为空")
		require.Empty(t, d.PTR, "PTR 记录应为空")
		require.Empty(t, d.NS, "NS 记录应为空")
		require.Empty(t, d.TXT, "TXT 记录应为空")
		require.Empty(t, d.SRV, "SRV 记录应为空")
		require.Empty(t, d.CAA, "CAA 记录应为空")
		require.Empty(t, d.AllRecords, "AllRecords 应为空")
	})
}

// TestJSON 测试 JSON 方法的功能。
func TestJSON(t *testing.T) {
	// 测试用例 1：成功序列化有效的 DNSData
	t.Run("成功序列化", func(t *testing.T) {
		// 创建一个 DNSData 实例，填充示例数据
		dnsData := &DNSData{
			Resolver:   []string{"*******", "*******"},
			A:          []string{"*********", "19*******"},
			AAAA:       []string{"2001:db8::1", "2001:db8::2"},
			CNAME:      []string{"www.example.com", "www2.example.com"},
			MX:         []string{"mail.example.com", "mail2.example.com"},
			PTR:        []string{"ptr.example.com", "ptr2.example.com"},
			NS:         []string{"ns1.example.com", "ns2.example.com"},
			TXT:        []string{"text record", "another text"},
			SRV:        []string{"_service._tcp.example.com", "_service2._tcp.example.com"},
			CAA:        []string{"issue \"ca.example.com\"", "issue \"ca2.example.com\""},
			AllRecords: []string{"A:*********", "A:19*******"},
		}

		// 调用 JSON 方法
		jsonStr, err := dnsData.JSON()

		// 断言结果
		require.NoError(t, err, "JSON 方法不应返回错误")
		require.NotEmpty(t, jsonStr, "JSON 字符串不应为空")

		// 反序列化 JSON 字符串以验证内容
		var decoded DNSData
		err = json.Unmarshal([]byte(jsonStr), &decoded)
		require.NoError(t, err, "JSON 字符串应能反序列化")
		require.Equal(t, dnsData.Host, decoded.Host, "主机名应一致")
		require.Equal(t, dnsData.A, decoded.A, "A 记录应一致")
		require.Equal(t, dnsData.AAAA, decoded.AAAA, "AAAA 记录应一致")
		require.Equal(t, dnsData.StatusCode, decoded.StatusCode, "状态码应一致")
	})
}


// TestMarshalUnmarshal 测试 Marshal 和 Unmarshal 方法的功能。
func TestMarshalUnmarshal(t *testing.T) {
	// 测试用例 1：成功序列化和反序列化
	t.Run("成功序列化和反序列化", func(t *testing.T) {
		// 创建一个 DNSData 实例，填充示例数据
		original := &DNSData{
			Resolver:   []string{"*******", "*******"},
			A:          []string{"*********", "19*******"},
			AAAA:       []string{"2001:db8::1", "2001:db8::2"},
			CNAME:      []string{"www.example.com", "www2.example.com"},
			MX:         []string{"mail.example.com", "mail2.example.com"},
			PTR:        []string{"ptr.example.com", "ptr2.example.com"},
			NS:         []string{"ns1.example.com", "ns2.example.com"},
			TXT:        []string{"text record", "another text"},
			SRV:        []string{"_service._tcp.example.com", "_service2._tcp.example.com"},
			CAA:        []string{"issue \"ca.example.com\"", "issue \"ca2.example.com\""},
			AllRecords: []string{"A:*********", "A:19*******"},
		}

		// 调用 Marshal 方法序列化
		data, err := original.Marshal()
		require.NoError(t, err, "Marshal 方法不应返回错误")
		require.NotEmpty(t, data, "序列化后的数据不应为空")

		// 创建新的 DNSData 实例用于反序列化
		decoded := &DNSData{}
		// 调用 Unmarshal 方法反序列化
		err = decoded.Unmarshal(data)
		require.NoError(t, err, "Unmarshal 方法不应返回错误")

		// 验证反序列化后的数据与原始数据一致
		require.Equal(t, original.Host, decoded.Host, "主机名应一致")
		require.Equal(t, original.A, decoded.A, "A 记录应一致")
		require.Equal(t, original.AAAA, decoded.AAAA, "AAAA 记录应一致")
		require.Equal(t, original.StatusCode, decoded.StatusCode, "状态码应一致")
		require.Equal(t, original.Timestamp, decoded.Timestamp, "时间戳应一致")
		require.Equal(t, original.Resolver, decoded.Resolver, "解析器列表应一致")
		require.Equal(t, original.Raw, decoded.Raw, "原始响应字符串应一致")
		require.Equal(t, original.HostsFile, decoded.HostsFile, "HostsFile 标志应一致")
		require.Equal(t, original.HasInternalIPs, decoded.HasInternalIPs, "HasInternalIPs 标志应一致")
	})
}

// TestGetSOARecords 测试 GetSOARecords 方法的功能。
func TestGetSOARecords(t *testing.T) {
	// 测试用例 1：单个 SOA 记录
	t.Run("单个SOA记录", func(t *testing.T) {
		// 创建 DNSData 实例，包含一个 SOA 记录
		dnsData := &DNSData{
			SOA: []SOA{
				{
					NS:   "ns1.example.com",
					Mbox: "admin.example.com",
				},
			},
		}

		// 调用 GetSOARecords 方法
		records := dnsData.GetSOARecords()

		// 断言结果
		require.Equal(t, []string{"ns1.example.com", "admin.example.com"}, records,
			"应返回单个 SOA 记录的 NS 和 Mbox")
	})

	// 测试用例 2：多个 SOA 记录
	t.Run("多个SOA记录", func(t *testing.T) {
		// 创建 DNSData 实例，包含多个 SOA 记录
		dnsData := &DNSData{
			SOA: []SOA{
				{
					NS:   "ns1.example.com",
					Mbox: "admin1.example.com",
				},
				{
					NS:   "ns2.example.com",
					Mbox: "admin2.example.com",
				},
			},
		}

		// 调用 GetSOARecords 方法
		records := dnsData.GetSOARecords()

		// 断言结果
		require.Equal(t, []string{
			"ns1.example.com",
			"admin1.example.com",
			"ns2.example.com",
			"admin2.example.com",
		}, records, "应返回多个 SOA 记录的 NS 和 Mbox")
	})

}