// Author: chenjb
// Version: V1.0
// Date: 2025-06-12 11:30:21
// FilePath: /yaml_scan/pkg/ratelimit/ratelimit.go
// Description: 实现了不同策略的限流器，包括基于令牌桶和漏桶算法的实现
package ratelimit

import (
	"context"
	"math"
	"sync/atomic"
	"time"

	"golang.org/x/time/rate"
)

// minusOne 表示无符号整数的最大值，等同于-1，用于原子操作中的递减
var minusOne = ^uint32(0)

type Strategy uint8

const (
	// None 表示使用默认的令牌桶限流策略
	None Strategy = iota
	// LeakyBucket 表示使用漏桶算法进行限流
	LeakyBucket
)

// Limiter 允许在定义的时间间隔内处理一定数量的请求
type Limiter struct {
	strategy   Strategy           // 限流策略类型
	maxCount   atomic.Uint32      // 最大令牌数量，使用原子操作确保并发安全
	interval   time.Duration      // 令牌填充间隔时间
	count      atomic.Uint32      // 当前可用令牌数量，使用原子操作确保并发安全
	ticker     *time.Ticker       // 定时器，用于定时填充令牌
	tokens     chan struct{}      // 令牌通道，用于在限流时阻塞调用者
	ctx        context.Context    // 上下文，用于控制限流器的生命周期
	cancelFunc context.CancelFunc // 用于取消内部goroutine的函数

	// 封装了uber的漏桶限流器，按照期望的速率进行限流
	leakyBucketLimiter *rate.Limiter
}

// run 在后台运行限流器的核心逻辑
// 负责令牌的填充和分发，以及处理上下文取消事件
// @receiver limiter 
// @param ctx context.Context: 上下文，用于控制goroutine的生命周期
func (limiter *Limiter) run(ctx context.Context) {
	defer close(limiter.tokens)
	for {
		// 如果没有可用令牌，等待下一个填充周期
		if limiter.count.Load() == 0 {
			<-limiter.ticker.C
			limiter.count.Store(limiter.maxCount.Load())
		}
		select {
		case <-ctx.Done():
			// 内部上下文被取消
			// 停止定时器
			limiter.ticker.Stop()
			return
		case <-limiter.ctx.Done():
			// 外部上下文被取消
			limiter.ticker.Stop()
			return
		case limiter.tokens <- struct{}{}:
			// 向令牌通道发送一个令牌
			limiter.count.Add(minusOne)
		case <-limiter.ticker.C:
			 // 定时器触发 重新填满
			limiter.count.Store(limiter.maxCount.Load())
		}
	}
}

// Take 从限流器中获取一个令牌，如果没有可用令牌则阻塞等待
// 根据不同的限流策略采取不同的实现方式
// @receiver limiter 
func (limiter *Limiter) Take() {
	switch limiter.strategy {
	case LeakyBucket:
		_ = limiter.leakyBucketLimiter.Wait(context.TODO())
	default:
		<-limiter.tokens
	}
}

// CanTake 检查限流器是否有可用的令牌
// @receiver limiter 
// @return bool bool: 如果有可用令牌返回true，否则返回false
func (limiter *Limiter) CanTake() bool {
	switch limiter.strategy {
	case LeakyBucket:
		return limiter.leakyBucketLimiter.Tokens() > 0
	default:
		return limiter.count.Load() > 0
	}
}

// GetLimit  返回限流器当前的令牌生成速率
// @receiver limiter 
// @return uint uint: 限流器的最大令牌数
func (limiter *Limiter) GetLimit() uint {
	return uint(limiter.maxCount.Load())
}

// SetLimit 设置限流器的最大令牌数
// @receiver limiter 
// @param max uint:  新的最大令牌数
func (limiter *Limiter) SetLimit(max uint) {
	limiter.maxCount.Store(uint32(max))
	switch limiter.strategy {
	case LeakyBucket:
		limiter.leakyBucketLimiter.SetBurst(int(max))
	default:
	}
}

// SetDuration 设置限流器的令牌填充间隔时间
// @receiver limiter 
// @param d time.Duration: 新的间隔时间
func (limiter *Limiter) SetDuration(d time.Duration) {
	limiter.interval = d
	switch limiter.strategy {
	case LeakyBucket:
		limiter.leakyBucketLimiter.SetLimit(rate.Every(d))
	default:
		limiter.ticker.Reset(d)
	}
}

// Stop 停止限流器，取消内部上下文
func (limiter *Limiter) Stop() {
	switch limiter.strategy {
	case LeakyBucket:  // 漏桶策略，无需额外操作
	default:
		if limiter.cancelFunc != nil {
			limiter.cancelFunc()
		}
	}
}

// New 创建一个新的限流器实例，指定最大令牌数和填充间隔
// @param ctx context.Context: 上下文，用于控制限流器的生命周期
// @param max uint: 最大令牌数
// @param duration time.Duration: 令牌填充间隔时间
// @return *Limiter *Limiter: 新创建的限流器实例
func New(ctx context.Context, max uint, duration time.Duration) *Limiter {
	// 创建内部上下文
	internalctx, cancel := context.WithCancel(context.TODO())

	// 创建原子计数器
	maxCount := &atomic.Uint32{}
	// 设置最大令牌数
	maxCount.Store(uint32(max))
	limiter := &Limiter{
		ticker:     time.NewTicker(duration), // 创建定时器
		tokens:     make(chan struct{}),      // 创建令牌通道
		ctx:        ctx,                      // 存储外部上下文
		cancelFunc: cancel,                   // 存储取消函数
		strategy:   None,                     // 默认使用令牌桶策略
		interval:   duration,                 // 存储时间间隔
	}
	limiter.maxCount.Store(uint32(max))
	limiter.count.Store(uint32(max))
	// 启动后台goroutine
	go limiter.run(internalctx)

	return limiter
}

// NewUnlimited 创建一个近似无限制令牌的限流器
// @param ctx context.Context: 上下文，用于控制限流器的生命周期
// @return *Limiter *Limiter: 新创建的无限制限流器实例
func NewUnlimited(ctx context.Context) *Limiter {
	internalctx, cancel := context.WithCancel(context.TODO())
	limiter := &Limiter{
		ticker:     time.NewTicker(time.Millisecond),
		tokens:     make(chan struct{}),
		ctx:        ctx,
		cancelFunc: cancel,
	}
	limiter.maxCount.Store(math.MaxUint32)
	limiter.count.Store(math.MaxUint32)
	go limiter.run(internalctx)

	return limiter
}

// NewLeakyBucket 创建一个基于漏桶算法的限流器
// @param ctx context.Context: 上下文，用于控制限流器的生命周期
// @param max uint:  最大令牌数
// @param duration time.Duration: 令牌填充间隔时间
// @return *Limiter *Limiter: 新创建的漏桶限流器实例
func NewLeakyBucket(ctx context.Context, max uint, duration time.Duration) *Limiter {
	limiter := &Limiter{
		strategy:           LeakyBucket,
		leakyBucketLimiter: rate.NewLimiter(rate.Every(duration), int(max)),
	}
	limiter.maxCount.Store(uint32(max))
	limiter.interval = duration
	return limiter
}
