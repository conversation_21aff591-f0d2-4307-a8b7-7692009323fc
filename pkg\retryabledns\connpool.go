//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-09 15:45:15
//FilePath: /yaml_scan/pkg/retryabledns/connpool.go
//Description: 连接池，用于管理多个 DNS 连接，实现连接的复用和分配

package retryabledns

import (
	"container/heap"
	"context"
	"fmt"
	"net"
	"time"

	"github.com/miekg/dns"
)

// ConnPool 连接池，用于管理多个 DNS 连接，实现连接的复用和分配
type ConnPool struct {
	items      map[*dns.Conn]bool  // 保存 DNS 连接及其使用状态的集合
	newArrival chan *waitingClient // 新到达的客户端等待连接的通道
	finished   chan *dns.Conn      // 完成的连接返回连接池的通道
	clients    clientQueue         // 等待连接的客户端队列，使用最小堆实现优先级队列
	cancel     context.CancelFunc  // 用于取消连接池协调 goroutine 的函数
	resolver   NetworkResolver     // DNS 解析器
}

// coordinate:处理连接池的协调工作，分配空闲连接给等待的客户端
// 该方法在 goroutine 中运行，持续监听 newArrival 和 finished 通道
// 当有新客户端到达时，将其加入等待队列；当有连接完成时，标记该连接为未使用

// @receiver cp *ConnPool:
// @param ctx context.Context:
func (cp *ConnPool) coordinate(ctx context.Context) {
	for {
		select {
		// 如果上下文被取消，退出循环
		case <-ctx.Done():
			return
			// 处理新到达的客户端
		case client := <-cp.newArrival:
			heap.Push(&cp.clients, client)
			// 处理完成的连接
		case conn := <-cp.finished:
			cp.items[conn] = false
		}

		// 遍历所有连接，分配连接给等待的客户端
		for conn, inUse := range cp.items {
			// 如果连接未使用且有等待的客户端
			if !inUse && len(cp.clients) > 0 {
				// 标记连接为正在使用
				cp.items[conn] = true
				// 从队列中取出等待的客户端
				client := heap.Pop(&cp.clients).(*waitingClient)
				select {
				// 将连接返回给客户端
				case client.returnCh <- conn:
					// 如果客户端完成，标记连接为未使用
				case <-client.doneCh:
					cp.items[conn] = false
				case <-ctx.Done():
					return
				}
			}
		}
	}
}

// NewConnPool:创建一个新的连接池
//
//	@param resolver NetworkResolver:DNS 解析器。
//	@param poolSize int: 连接池大小。
//	@return *ConnPool *ConnPool:返回创建的连接池实例。
//	@return error error:返回可能发生的错误。
func NewConnPool(resolver NetworkResolver, poolSize int) (*ConnPool, error) {
	// 创建一个可取消的上下文
	ctx, cancel := context.WithCancel(context.Background())

	// 初始化连接池
	pool := &ConnPool{
		items:      make(map[*dns.Conn]bool, poolSize),
		newArrival: make(chan *waitingClient),
		finished:   make(chan *dns.Conn),
		cancel:     cancel,
		resolver:   resolver,
	}
	// 初始化客户端队列
	heap.Init(&pool.clients)

	// 创建指定数量的 DNS 连接
	for i := 0; i < poolSize; i++ {
		conn, err := dns.Dial(resolver.Protocol.String(), resolver.String())
		if err != nil {
			return nil, fmt.Errorf("unable to create conn to %s: %w", resolver.String(), err)
		}
		// 将连接标记为未使用
		pool.items[conn] = false
	}
	// 启动连接池的协调工作
	go pool.coordinate(ctx)
	return pool, nil
}

// getConnection:从连接池中获取一个连接
//
//	@receiver cp *ConnPool:
//	@param ctx context.Context:上下文，用于控制请求的生命周期。
//	@return *dns.Conn *dns.Conn:成功获取的 DNS 连接指针；
//	@return error error:
func (cp *ConnPool) getConnection(ctx context.Context) (*dns.Conn, error) {
	// 创建一个新的等待客户端，包含到达时间、返回通道和完成通道
	client := &waitingClient{
		arrivalTime: time.Now(),
		returnCh:    make(chan *dns.Conn),
		doneCh:      ctx.Done(),
	}
	select {
	// 将客户端添加到连接池的等待队列
	case cp.newArrival <- client:
	case <-ctx.Done():
		// 返回上下文错误
		return nil, ctx.Err()
	}

	select {
	// 从返回通道获取连接
	case conn := <-client.returnCh:
		return conn, nil
		// 如果上下文被取消
	case <-ctx.Done():
		return nil, ctx.Err()
	}
}

// releaseConnection: 将给定的 DNS 连接放回连接池
//
//	@receiver cp *ConnPool:
//	@param conn *dns.Conn: 要释放的 DNS 连接
func (cp *ConnPool) releaseConnection(conn *dns.Conn) {
	// 将连接发送到 finished 通道
	cp.finished <- conn
}

// Exchange: 使用连接池中的连接与 DNS 服务器交换消息
//
//	@receiver cp *ConnPool:
//	@param ctx context.Context: 上下文，用于控制请求的生命周期。
//	@param client *dns.Client:  用于与 DNS 服务器进行通信的 DNS 客户端。
//	@param msg *dns.Msg: 要发送的 DNS 消息。
//	@return r *dns.Msg: 从 DNS 服务器接收到的响应消息；
//	@return rtt time.Duration: 往返时间（RTT）；
//	@return err error:
func (cp *ConnPool) Exchange(ctx context.Context, client *dns.Client, msg *dns.Msg) (r *dns.Msg, rtt time.Duration, err error) {
	// 从连接池获取连接
	conn, err := cp.getConnection(ctx)
	if err != nil {
		return nil, time.Duration(0), err
	}
	// 确保在函数结束时释放连接
	defer cp.releaseConnection(conn)
	// 使用连接交换消息
	return client.ExchangeWithConn(msg, conn)
}

// Close: 关闭连接池
//
//	@receiver cp *ConnPool:
func (cp *ConnPool) Close() {
	cp.cancel()
	for conn := range cp.items {
		conn.Close()
	}
}

// Resolver: 获取连接池中的解析器
//
//	@receiver cp *ConnPool:
//	@return NetworkResolver NetworkResolver:
func (cp *ConnPool) Resolver() NetworkResolver {
	return cp.resolver
}

// LocalAddrs: 获取连接池中所有 DNS 连接的本地 UDP 地址
//
//	@receiver cp *ConnPool:
//	@return []*net.UDPAddr []*net.UDPAddr:返回一个包含所有本地地址的切片
func (cp *ConnPool) LocalAddrs() []*net.UDPAddr {
	retval := make([]*net.UDPAddr, len(cp.items))
	i := 0
	for conn := range cp.items {
		retval[i] = conn.LocalAddr().(*net.UDPAddr)
		i++
	}
	return retval
}
