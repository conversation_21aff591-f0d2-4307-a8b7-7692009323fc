// Author: chenjb
// Version: V1.0
// Date: 2025-07-25
// FilePath: /yaml_scan/pkg/rawhttp/client_test.go
// Description: rawhttp客户端模块单元测试

package rawhttp

import (
	"io"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"yaml_scan/pkg/fastdialer"
)

// TestAutomaticHostHeader 测试AutomaticHostHeader函数
func TestAutomaticHostHeader(t *testing.T) {
	// 保存原始值
	originalValue := DefaultClient.Options.AutomaticHostHeader
	defer func() {
		DefaultClient.Options.AutomaticHostHeader = originalValue
	}()
	
	// 测试启用自动Host头部
	AutomaticHostHeader(true)
	require.True(t, DefaultClient.Options.AutomaticHostHeader, "应该启用自动Host头部")
	
	// 测试禁用自动Host头部
	AutomaticHostHeader(false)
	require.False(t, DefaultClient.Options.AutomaticHostHeader, "应该禁用自动Host头部")
}

// TestAutomaticContentLength 测试AutomaticContentLength函数
func TestAutomaticContentLength(t *testing.T) {
	// 保存原始值
	originalValue := DefaultClient.Options.AutomaticContentLength
	defer func() {
		DefaultClient.Options.AutomaticContentLength = originalValue
	}()
	
	// 测试启用自动Content-Length
	AutomaticContentLength(true)
	require.True(t, DefaultClient.Options.AutomaticContentLength, "应该启用自动Content-Length")
	
	// 测试禁用自动Content-Length
	AutomaticContentLength(false)
	require.False(t, DefaultClient.Options.AutomaticContentLength, "应该禁用自动Content-Length")
}

// TestNewClient 测试NewClient函数
func TestNewClient(t *testing.T) {
	// 测试使用默认选项创建客户端
	options := &Options{
		Timeout:         30 * time.Second,
		FollowRedirects: true,
		MaxRedirects:    10,
	}
	
	client := NewClient(options)
	require.NotNil(t, client, "客户端应该被创建")
	require.Equal(t, options, client.Options, "选项应该正确设置")
	require.NotNil(t, client.dialer, "拨号器应该被创建")
	
	// 测试FastDialer被自动创建
	require.NotNil(t, options.FastDialer, "FastDialer应该被自动创建")
}

// TestNewClient_WithFastDialer 测试使用预设FastDialer创建客户端
func TestNewClient_WithFastDialer(t *testing.T) {
	// 创建自定义FastDialer
	opts := fastdialer.DefaultOptions
	customDialer, err := fastdialer.NewDialer(opts)
	require.NoError(t, err, "创建FastDialer应该成功")
	
	options := &Options{
		Timeout:     30 * time.Second,
		FastDialer:  customDialer,
	}
	
	client := NewClient(options)
	require.NotNil(t, client, "客户端应该被创建")
	require.Equal(t, customDialer, options.FastDialer, "应该使用预设的FastDialer")
}

// TestClient_Head 测试Head方法
func TestClient_Head(t *testing.T) {
	// 创建模拟客户端
	client := &Client{
		dialer:  &mockDialer{},
		Options: DefaultOptions,
	}
	
	// 由于Head方法调用DoRaw，我们需要模拟整个流程
	// 这里只测试方法调用不会panic
	_, err := client.Head("http://example.com")
	// 由于我们使用的是模拟拨号器，预期会有连接错误
	require.Error(t, err, "模拟环境下应该有连接错误")
}

// TestClient_Get 测试Get方法
func TestClient_Get(t *testing.T) {
	client := &Client{
		dialer:  &mockDialer{},
		Options: DefaultOptions,
	}
	
	_, err := client.Get("http://example.com")
	require.Error(t, err, "模拟环境下应该有连接错误")
}

// TestClient_Post 测试Post方法
func TestClient_Post(t *testing.T) {
	client := &Client{
		dialer:  &mockDialer{},
		Options: DefaultOptions,
	}
	
	body := strings.NewReader("test body")
	_, err := client.Post("http://example.com", "application/json", body)
	require.Error(t, err, "模拟环境下应该有连接错误")
}

// TestClient_Do 测试Do方法
func TestClient_Do(t *testing.T) {
	client := &Client{
		dialer:  &mockDialer{},
		Options: DefaultOptions,
	}
	
	req, err := http.NewRequest("GET", "http://example.com", nil)
	require.NoError(t, err, "创建HTTP请求应该成功")
	
	_, err = client.Do(req)
	require.Error(t, err, "模拟环境下应该有连接错误")
}

// TestClient_Close 测试Close方法
func TestClient_Close(t *testing.T) {
	// 创建带FastDialer的客户端
	opts := fastdialer.DefaultOptions
	fastDialer, err := fastdialer.NewDialer(opts)
	require.NoError(t, err, "创建FastDialer应该成功")
	
	options := &Options{
		FastDialer: fastDialer,
	}
	
	client := NewClient(options)
	
	// 测试关闭客户端
	client.Close()
	// Close方法应该正常执行，不会panic
}

// TestClient_Close_NoFastDialer 测试没有FastDialer时的Close方法
func TestClient_Close_NoFastDialer(t *testing.T) {
	client := &Client{
		dialer:  &mockDialer{},
		Options: &Options{}, // 没有FastDialer
	}
	
	// 测试关闭客户端
	client.Close()
	// Close方法应该正常执行，不会panic
}

// TestRedirectStatus 测试RedirectStatus结构体
func TestRedirectStatus(t *testing.T) {
	redirectStatus := &RedirectStatus{
		FollowRedirects: true,
		MaxRedirects:    5,
		Current:         0,
	}
	
	require.True(t, redirectStatus.FollowRedirects, "应该跟踪重定向")
	require.Equal(t, 5, redirectStatus.MaxRedirects, "最大重定向次数应该正确")
	require.Equal(t, 0, redirectStatus.Current, "当前重定向次数应该为0")
	
	// 测试增加重定向计数
	redirectStatus.Current++
	require.Equal(t, 1, redirectStatus.Current, "重定向计数应该增加")
}

// mockDialer 模拟拨号器实现
type mockDialer struct{}

func (m *mockDialer) Dial(protocol, addr string, options *Options) (Conn, error) {
	return nil, io.EOF // 返回连接错误
}

func (m *mockDialer) DialWithProxy(protocol, addr, proxyURL string, timeout time.Duration, options *Options) (Conn, error) {
	return nil, io.EOF // 返回连接错误
}

func (m *mockDialer) DialTimeout(protocol, addr string, timeout time.Duration, options *Options) (Conn, error) {
	return nil, io.EOF // 返回连接错误
}
