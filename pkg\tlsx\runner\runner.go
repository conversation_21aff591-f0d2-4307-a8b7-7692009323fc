// Author: chenjb
// Version: V1.0
// Date: 2025-06-18 15:21:11
// FilePath: /yaml_scan/pkg/tlsx/runner/runner.go
// Description:TLS扫描器的核心运行器，负责任务调度和执行管理
package runner

import (
	"bufio"
	"net"
	"net/url"
	"os"
	"strings"
	"sync"
	"time"
	"yaml_scan/pkg/cidr"
	"yaml_scan/pkg/dnsx/libs/dnsx"
	"yaml_scan/pkg/fastdialer"
	"yaml_scan/pkg/gologger"
	"yaml_scan/pkg/gologger/formatter"
	"yaml_scan/pkg/gologger/levels"
	"yaml_scan/pkg/tlsx/output"
	"yaml_scan/pkg/tlsx/output/stats"
	"yaml_scan/pkg/tlsx/tlsx"
	"yaml_scan/pkg/tlsx/tlsx/clients"
	"yaml_scan/pkg/tlsx/tlsx/openssl"
	errorutil "yaml_scan/utils/errors"
	fileutil "yaml_scan/utils/file"
	iputil "yaml_scan/utils/ip"
	sliceutil "yaml_scan/utils/slice"

	"github.com/miekg/dns"
)

// Runner TLS扫描器的核心运行器
type Runner struct {
	hasStdin     bool               // 是否有标准输入数据
	outputWriter output.Writer      // 输出写入器，负责结果格式化和输出
	fastDialer   *fastdialer.Dialer // 快速拨号器，用于网络连接
	options      *clients.Options   // 扫描配置选项
	dnsclient    *dnsx.DNSX         // DNS客户端，用于域名解析
}

// New 根据提供的配置选项创建新的TLS扫描运行器
// 该函数负责初始化运行器的所有组件，包括日志、网络拨号器、DNS客户端和输出写入器
// @param options *clients.Options: TLS扫描的配置选项，包含所有扫描参数和行为控制
// @return *Runner *Runner: 初始化完成的运行器实例
// @return error error:
func New(options *clients.Options) (*Runner, error) {
	// 配置日志输出格式：如果用户要求禁用颜色，则使用无颜色格式
	if options.NoColor {
		gologger.DefaultLogger.SetFormatter(formatter.NewCLI(true))
	}
	// 配置日志级别：如果启用静默模式，则设置为静默级别
	if options.Silent {
		gologger.DefaultLogger.SetMaxLevel(levels.LevelSilent)
	}

	// 配置OpenSSL二进制文件路径：如果用户指定了自定义OpenSSL路径
	if options.OpenSSLBinary != "" {
		openssl.UseOpenSSLBinary(options.OpenSSLBinary)
	}

	// 处理密码套件枚举的依赖：密码套件枚举需要同时启用TLS版本枚举
	if options.TlsCiphersEnum {
		options.TlsVersionsEnum = true
	}

	// 创建运行器实例并验证配置选项
	runner := &Runner{options: options}
	if err := runner.validateOptions(); err != nil {
		return nil, errorutil.NewWithErr(err).Msgf("could not validate options")
	}

	// 配置快速拨号器选项
	dialerOpts := fastdialer.DefaultOptions
	// 启用拨号历史记录
	dialerOpts.WithDialerHistory = true
	// 设置最大重试次数
	dialerOpts.MaxRetries = 3
	// 设置拨号超时时间
	dialerOpts.DialerTimeout = time.Duration(options.Timeout) * time.Second

	// 如果用户指定了自定义DNS解析器，则使用用户配置
	if len(options.Resolvers) > 0 {
		dialerOpts.BaseResolvers = options.Resolvers
	}

	// 创建快速拨号器实例
	fastDialer, err := fastdialer.NewDialer(dialerOpts)
	if err != nil {
		return nil, errorutil.NewWithErr(err).Msgf("could not create dialer")
	}
	runner.fastDialer = fastDialer
	runner.options.Fastdialer = fastDialer

	// 配置DNS客户端选项
	dnsOptions := dnsx.DefaultOptions
	// 设置DNS查询重试次数
	dnsOptions.MaxRetries = runner.options.Retries
	// 启用hosts文件支持
	dnsOptions.Hostsfile = true
	// 如果指定了IPv6版本，则添加AAAA记录查询类型
	if sliceutil.Contains(options.IPVersion, "6") {
		dnsOptions.QuestionTypes = append(dnsOptions.QuestionTypes, dns.TypeAAAA)
	}
	// 创建DNS客户端实例
	dnsclient, err := dnsx.New(dnsOptions)
	if err != nil {
		return nil, err
	}
	runner.dnsclient = dnsclient

	// 创建输出写入器，负责结果的格式化和输出
	outputWriter, err := output.New(options)
	if err != nil {
		return nil, errorutil.NewWithErr(err).Msgf("could not create output writer")
	}
	runner.outputWriter = outputWriter

	// 如果启用了密码套件枚举且非静默模式，则显示提示信息
	if options.TlsCiphersEnum && !options.Silent {
		gologger.Info().Msgf("Enumerating TLS Ciphers in %s mode", options.ScanMode)
	}

	return runner, nil
}

// Close 关闭运行器并释放所有相关资源
func (r *Runner) Close() error {
	_ = r.outputWriter.Close()
	r.fastDialer.Close()
	return nil
}

type taskInput struct {
	host string
	ip   string
	port string
	sni  string
}

// Address 返回任务的网络地址字符串
// 将主机名和端口组合成标准的网络地址格式
// @receiver t
// @return string string: 格式为"host:port"的网络地址字符串
func (t taskInput) Address() string {
	return net.JoinHostPort(t.host, t.port)
}

// Execute 执行主要的数据收集循环
// 该方法是TLS扫描器的核心执行入口，负责协调整个扫描过程
// @receiver r
// @return error error:
// 执行流程:
//  1. 创建任务输入通道，缓冲大小等于并发数
//  2. 启动指定数量的工作协程处理扫描任务
//  3. 规范化并排队所有输入目标
//  4. 等待所有工作协程完成
//  5. 显示统计信息（如果使用auto模式）
//
// 并发模型:
//   - 使用生产者-消费者模式
//   - 主协程负责生产任务（输入处理）
//   - 工作协程负责消费任务（TLS扫描）
//   - 通过WaitGroup确保所有任务完成
func (r *Runner) Execute() error {
	// 创建任务输入通道，缓冲大小等于并发数以优化性能
	inputs := make(chan taskInput, r.options.Concurrency)

	// 用于等待所有工作协程完成
	wg := &sync.WaitGroup{}

	// 启动工作协程池处理扫描任务
	for i := 0; i < r.options.Concurrency; i++ {
		wg.Add(1)
		go r.processInputElementWorker(inputs, wg)
	}

	// 规范化并排队所有输入目标
	// 将各种格式的输入（域名、IP、CIDR等）转换为统一的任务格式
	if err := r.normalizeAndQueueInputs(inputs); err != nil {
		gologger.Error().Msgf("Could not normalize queue inputs: %s", err)
	}

	close(inputs) // 关闭输入通道，通知工作协程没有更多任务
	wg.Wait()     // 等待所有工作协程完成

	// 如果使用auto模式，显示各种TLS实现的连接统计
	if r.options.ScanMode == "auto" {
		gologger.Info().Msgf("Connections made using crypto/tls: %d, zcrypto/tls: %d, openssl: %d",
			stats.LoadCryptoTLSConnections(),
			stats.LoadZcryptoTLSConnections(),
			stats.LoadOpensslTLSConnections())
	}
	return nil
}

// processInputElementWorker 处理来自输入通道的扫描任务
// 该方法是工作协程的主要逻辑，负责执行实际的TLS扫描操作
// @receiver r 
// @param inputs chan taskInput: 任务输入通道，接收待处理的扫描任务
// @param wg *sync.WaitGroup: 用于通知主协程当前工作协程已完成
// 工作流程:
//   1. 创建TLS扫描服务实例
//   2. 循环处理通道中的每个任务
//   3. 应用延迟设置（如果配置）
//   4. 执行TLS连接和扫描
//   5. 将结果写入输出
func (r *Runner) processInputElementWorker(inputs chan taskInput, wg *sync.WaitGroup) {
	defer wg.Done()

	// 为当前工作协程创建TLS扫描服务实例
	tlsxService, err := tlsx.New(r.options)
	if err != nil {
		gologger.Fatal().Msgf("could not create tlsx client: %s", err)
		return
	}

	// 循环处理通道中的每个扫描任务
	for task := range inputs {
		// 应用延迟设置（如果配置了延迟）
		if r.options.Delay != "" {
			duration, err := time.ParseDuration(r.options.Delay)
			if err != nil {
				gologger.Error().Msgf("error parsing delay %s: %s", r.options.Delay, err)
			}
			time.Sleep(duration)
		}

		// 详细模式下显示当前处理的任务信息
		if r.options.Verbose {
			gologger.Info().Msgf("Processing input %s:%s", task.host, task.port)
		}

		// 执行TLS连接和扫描
		response, err := tlsxService.ConnectWithOptions(task.host, task.ip, task.port, clients.ConnectOptions{SNI: task.sni})
		if err != nil {
			gologger.Warning().Msgf("Could not connect input %s: %s", task.Address(), err)
		}

		if response == nil {
			continue
		}

		// 将扫描结果写入输出
		if err := r.outputWriter.Write(response); err != nil {
			gologger.Warning().Msgf("Could not write output %s: %s", task.Address(), err)
			continue
		}
	}
}

// normalizeAndQueueInputs 规范化输入并将其排队等待执行
// 该方法处理来自多个源的输入（命令行参数、文件、标准输入），并将其转换为统一的任务格式
// @receiver r 
// @param inputs chan taskInput: 任务输入通道，用于发送处理后的扫描任务
// @return error error: 输入处理过程中的错误，成功时为nil
// 输入源处理顺序:
//   1. 命令行直接指定的输入参数
//   2. 输入文件中的目标列表
//   3. 标准输入中的目标数据
//
// 支持的输入格式:
//   - 域名：example.com
//   - IP地址：***********
//   - CIDR网段：***********/24
//   - URL：https://example.com:8443
//   - 带端口的地址：example.com:8080
func (r *Runner) normalizeAndQueueInputs(inputs chan taskInput) error {
	// 处理命令行直接指定的输入参数
	for _, text := range r.options.Inputs {
		r.processInputItem(text, inputs)
	}

	// 处理输入文件中的目标列表
	if r.options.InputList != "" {
		file, err := os.Open(r.options.InputList)
		if err != nil {
			return errorutil.NewWithErr(err).Msgf("could not open input file")
		}
		defer file.Close()

		// 逐行读取文件内容
		scanner := bufio.NewScanner(file)
		for scanner.Scan() {
			text := scanner.Text()
			// 跳过空行
			if text != "" {
				r.processInputItem(text, inputs)
			}
		}
	}

	// 处理标准输入中的目标数据
	if r.hasStdin {
		scanner := bufio.NewScanner(os.Stdin)
		for scanner.Scan() {
			text := scanner.Text()
			// 跳过空行
			if text != "" {
				r.processInputItem(text, inputs)
			}
		}
	}
	return nil
}

// resolveFQDN 解析完全限定域名（FQDN）并返回IP地址列表
// 该方法负责将域名转换为IP地址，支持IPv4和IPv6解析
// @receiver r 
// @param target string: 目标主机名或IP地址
// @return []string []string: 解析得到的IP地址列表
// @return error error: 
// 解析逻辑:
//   - 如果目标已经是IP地址，直接返回
//   - 如果是域名，执行DNS查询获取IP地址
//   - 根据IPVersion配置决定返回IPv4、IPv6或两者
//   - 默认情况下只返回IPv4地址（A记录）
func (r *Runner) resolveFQDN(target string) ([]string, error) {
	var hostIPs []string

	// 检查目标是否已经是IP地址
	if !iputil.IsIP(target) {
		// 目标是域名，执行DNS查询
		dnsData, err := r.dnsclient.QueryMultiple(target)
		if err != nil || dnsData == nil {
			gologger.Warning().Msgf("Could not get IP for host: %s\n", target)
			return nil, err
		}

		// 根据IP版本配置选择返回的IP地址类型
		if len(r.options.IPVersion) > 0 {
			if sliceutil.Contains(r.options.IPVersion, "4") {
				hostIPs = append(hostIPs, dnsData.A...)
			}
			if sliceutil.Contains(r.options.IPVersion, "6") {
				hostIPs = append(hostIPs, dnsData.AAAA...)
			}
		} else {
			// 默认情况下只返回IPv4地址
			hostIPs = append(hostIPs, dnsData.A...)
		}
	} else {
		hostIPs = append(hostIPs, target)
	}
	return hostIPs, nil
}

// processInputItem 处理单个输入项并生成相应的扫描任务
// 该方法根据输入的格式和类型，将其转换为一个或多个扫描任务
// @receiver r 
// @param input string: 单个输入字符串，可以是域名、IP、CIDR、URL等格式
// @param inputs chan taskInput: 任务输入通道，用于发送生成的扫描任务
// 输入类型识别和处理:
//   1. CIDR网段：如***********/24，展开为多个IP地址
//   2. 多IP扫描：当启用ScanAllIPs或指定IPVersion时，解析域名的所有IP
//   3. 普通输入：域名、IP地址或URL，提取主机名和端口
func (r *Runner) processInputItem(input string, inputs chan taskInput) {

	// CIDR网段输入处理
	if _, ipRange, _ := net.ParseCIDR(input); ipRange != nil {
		r.processInputCIDR(input, inputs)
		return
	}

	// 多IP扫描模式：扫描域名的所有IP地址或指定IP版本
	if r.options.ScanAllIPs || len(r.options.IPVersion) > 0 {
		r.processInputForMultipleIPs(input, inputs)
		return
	}
	
	// 普通输入处理：域名、IP地址或URL
	host, customPort := r.getHostPortFromInput(input)
	if customPort == "" {
		// 输入不包含端口，使用配置的所有端口
		for _, port := range r.options.Ports {
			r.processInputItemWithSni(taskInput{host: host, port: port}, inputs)
		}
	} else {
		// 输入包含端口，使用指定端口
		r.processInputItemWithSni(taskInput{host: host, port: customPort}, inputs)
	}
}


// processInputItemWithSni 处理带SNI配置的输入项
// 该方法根据SNI配置生成一个或多个扫描任务
// @receiver r 
// @param task taskInput: 基础任务信息，包含主机名、IP和端口
// @param inputs chan taskInput: 任务输入通道，用于发送生成的扫描任务
func (r *Runner) processInputItemWithSni(task taskInput, inputs chan taskInput) {
	if len(r.options.ServerName) > 0 {
		// 为每个配置的SNI生成一个扫描任务
		for _, serverName := range r.options.ServerName {
			task.sni = serverName
			inputs <- task
		}
	} else {
		inputs <- task
	}
}

// getHostPortFromInput 从输入字符串中提取主机名和端口
// 该方法支持多种输入格式，包括URL、带端口的地址和纯主机名
// @receiver r 
// @param input string: 输入字符串，可能包含协议、主机名、端口等信息
// @return string string: 提取的主机名或IP地址
// @return string string: 提取的端口号，如果没有端口则返回空字符串
// 支持的输入格式:
//   - URL格式：https://example.com:8443/path
//   - 带端口地址：example.com:8080
//   - 纯主机名：example.com
//   - IP地址：***********
//   - 带端口IP：***********:8080
func (r *Runner) getHostPortFromInput(input string) (string, string) {
	host := input

	// 处理URL格式输入
	if strings.Contains(input, "://") {
		if parsed, err := url.Parse(input); err != nil {
			return "", ""
		} else {
			// 提取URL中的主机部分
			host = parsed.Host
			 // 如果Host没有端口，根据协议补充默认端口
			 if !strings.Contains(host, ":") {
				switch parsed.Scheme {
				case "http":
					return host, "80"
				case "https":
					return host, "443"
				}
			}
		}
	}

	// 处理带端口的地址
	if strings.Contains(host, ":") {
		if host, port, err := net.SplitHostPort(host); err != nil {
			return "", ""
		} else {
			// 返回分离后的主机名和端口
			return host, port
		}
	}
	return host, ""
}


// processInputCIDR 处理CIDR网段输入
// 该方法将CIDR网段展开为所有包含的IP地址，并为每个IP生成扫描任务
// @receiver r 
// @param input string: CIDR格式的网段，如"***********/24"
// @param inputs chan taskInput: 任务输入通道，用于发送生成的扫描任务
func (r *Runner) processInputCIDR(input string, inputs chan taskInput) {
	// 将CIDR网段转换为IP地址流
	cidrInputs, err := cidr.IPAddressesAsStream(input)
	if err != nil {
		gologger.Error().Msgf("Could not parse cidr %s: %s", input, err)
		return
	}

	// 为每个IP地址和每个端口生成扫描任务
	for cidr := range cidrInputs {
		for _, port := range r.options.Ports {
			r.processInputItemWithSni(taskInput{host: cidr, port: port}, inputs)
		}
	}
}

// processInputForMultipleIPs 处理多IP扫描模式的输入
// 当启用ScanAllIPs或指定IPVersion时，该方法会解析域名的所有IP地址并生成相应任务
// @receiver r 
// @param input string: 输入字符串，通常是域名或带端口的域名
// @param inputs chan taskInput: 任务输入通道，用于发送生成的扫描任务
// 多IP扫描场景:
//   - ScanAllIPs启用：扫描域名解析到的所有IP地址
//   - IPVersion指定：根据版本配置扫描IPv4和/或IPv6地址
//   - 负载均衡检测：发现域名背后的多个服务器
//   - CDN分析：识别CDN节点的不同IP地址
func (r *Runner) processInputForMultipleIPs(input string, inputs chan taskInput) {
	// 从输入中提取主机名和端口
	host, customPort := r.getHostPortFromInput(input)
	
	// 解析域名获取所有IP地址
	ipList, err := r.resolveFQDN(host)
	if err != nil {
		gologger.Warning().Msgf("Could not resolve %s: %s", host, err)
		return
	}
	for _, ip := range ipList {
		if customPort == "" {
			// 输入不包含端口，使用配置的所有端口
			for _, port := range r.options.Ports {
				r.processInputItemWithSni(taskInput{host: host, ip: ip, port: port}, inputs)
			}
		} else {
			// 输入包含端口，使用指定端口
			r.processInputItemWithSni(taskInput{host: host, ip: ip, port: customPort}, inputs)
		}
	}
}

// validateOptions 验证提供的扫描选项配置
// 该方法检查选项的有效性、兼容性，并设置默认值
// @receiver r
// @return error error:
func (r *Runner) validateOptions() error {
	// 检查是否有标准输入数据
	r.hasStdin = fileutil.HasStdin()

	// 设置重试次数默认值
	if r.options.Retries == 0 {
		r.options.Retries = 3
	}

	// 检查是否指定了探测相关的选项
	probeSpecified := r.options.SO || r.options.TLSVersion || r.options.Cipher || r.options.Expired || r.options.SelfSigned || r.options.Hash != "" || r.options.Jarm || r.options.MisMatched || r.options.Revoked || r.options.WildcardCertCheck

	// 验证resp-only标志的使用限制
	if r.options.RespOnly && probeSpecified {
		return errorutil.New("resp-only flag can only be used with san and cn flags")
	}

	// 验证san/cn标志与其他探测标志的互斥性
	if (r.options.SAN || r.options.CN) && probeSpecified {
		return errorutil.New("san or cn flag cannot be used with other probes")
	}

	// 验证输入源：必须至少有一个输入源
	if !r.hasStdin && len(r.options.Inputs) == 0 && r.options.InputList == "" {
		return errorutil.New("no input provided for enumeration")
	}

	// 设置默认端口：如果没有指定端口，使用443作为默认端口
	if len(r.options.Ports) == 0 {
		r.options.Ports = append(r.options.Ports, "443")
	}

	// 验证certs-only选项的扫描模式要求
	if r.options.CertsOnly && !(r.options.ScanMode == "ztls" || r.options.ScanMode == "auto") {
		return errorutil.New("scan-mode must be ztls or auto with certs-only option")
	}

	// 强制设置ztls模式：某些功能需要ztls库的特殊支持
	if r.options.CertsOnly || r.options.Ja3 || r.options.Ja3s {
		r.options.ScanMode = "ztls" // force setting ztls when using certs-only
	}

	// 设置详细日志级别
	if r.options.Verbose {
		gologger.DefaultLogger.SetMaxLevel(levels.LevelVerbose)
	}

	// JARM哈希计算与延迟的兼容性警告
	if r.options.Jarm && r.options.Delay != "" {
		gologger.Info().Label("WRN").Msg("Using connection pooling for jarm hash calculation, delay will not work as expected")
	}
	return nil
}
