# main config file
# generated by goflags

# 扫描目标
#target: []

# 包含要扫描的目标url /主机列表的文件路径（每行一个）
#list: 

# 输入列表中排除扫描的主机（ip, cidr, hostname）(ip, cidr, hostname)
#exclude-hosts: []

# 使用指定的resume.cfg文件恢复扫描（将禁用请求聚类）
#resume: 

# 扫描由目标解析出来的所有ip（针对域名对应多个ip的情况）
#scan-all-ips: false

# 要扫描的主机名的ip版本（4,6）-（默认为4)
#ip-version: []

# 支持的文件模式:(list, burp, jsonl, yaml, openapi, swagger)
#input-mode: list

# 在生成请求时，只使用输入格式中的必需字段
#required-only: false

# 解析输入文件时跳过格式验证（如遗漏变量）
#skip-format-validation: false

# 仅运行最新发布的模板
#new-templates: false

# 仅运行特定版本中添加的新模板
#new-templates-version: []

# 基于wappalyzer技术的标签映射自动扫描
#automatic-scan: false

# 指定要运行的模板或者模板目录（以逗号分隔或目录形式）
#templates: []

# 指定要运行的模板url或模板目录url（以逗号分隔或目录形式）
#template-url: []

# 指定要运行的工作流或工作流目录（以逗号分隔或目录形式）
#workflows: []

# 指定要运行的工作流url或工作流目录url（以逗号分隔或目录形式）
#workflow-url: []

# 验证模板有效性
#validate: false

# 禁用对模板的严格检查
#no-strict-syntax: false

# 显示模板内容
#template-display: false

# 列出所有支持的模板
#tl: false

# allowed domain list to load remote templates from
#remote-template-domain: 

# 模板签名
#sign: false

# 启用加载基于协议的代码模板
#code: false

# 禁用运行未签名模板或签名不匹配的模板
#disable-unsigned-templates: false

# 启用加载自包含模板
#enable-self-contained: false

# 启用文件模板
#file: false

# 基于作者运行的模板（逗号分隔，文件）
#author: []

# 执行带指定tag的模板（逗号分隔，文件）
#tags: []

# 排除带指定tag的模板（逗号分隔，文件）
#exclude-tags: []

# 执行带有指定tag的模板，即使是被默认或者配置排除的模板
#include-tags: []

# 执行指定id的模板（逗号分隔，文件）
#template-id: []

# 排除指定id的模板（逗号分隔，文件）
#exclude-id: []

# 执行指定模板，即使是被默认或配置排除的模板
#include-templates: []

# 排除指定模板或者模板目录（逗号分隔，文件）
#exclude-templates: []

# 排除指定模板matcher
#exclude-matchers: []

# 根据严重程度运行模板，可选值有： info, low, medium, high, critical, unknown
#severity: 

# 根据严重程度排除模板，可选值有: info, low, medium, high, critical, unknown
#exclude-severity: 

# 根据类型运行模板，可选值有: dns, file, http, headless, tcp, workflow, ssl, websocket, whois, code, javascript
#type: 

# 根据类型排除模板，可选值有: dns, file, http, headless, tcp, workflow, ssl, websocket, whois, code, javascript
#exclude-type: 

# 根据表达式运行模板
#template-condition: []

# 输出发现的问题到文件
#output: 

# 将nuclei的所有请求和响应输出到目录
#store-resp: false

# 将所有请求和响应输出到指定目录（默认：output）
#store-resp-dir: output

# 只显示结果
#silent: false

# 禁用输出内容着色（ansi转义码）
#no-color: false

# 输出格式为jsonl（ines）
#jsonl: false

# 在json、jsonl和markdown中不输出请求/响应对
#omit-raw: false

# 省略json、jsonl输出中的编码模板
#omit-template: false

# 在cli输出中不打印元数据
#no-meta: false

# 在cli输出中打印时间戳
#timestamp: false

# 本地的结果数据库（始终使用该数据库保存结果）
#report-db: 

# 显示匹配失败状态
#matcher-status: false

# 以markdown格式导出结果
#markdown-export: 

# 以sarif格式导出结果
#sarif-export: 

# 以json格式导出结果
#json-export: 

# 以jsonl(ine)格式导出结果
#jsonl-export: 

# 从查询参数、请求头和正文中编校给定的键列表
#redact: []

# 指定配置文件
#config: 

# 要运行的模板配置文件
#profile: 

# 列出所有可用的模板配置文件
#profile-list: false

# 为http模板启用重定向
#follow-redirects: false

# 允许在同一主机上重定向
#follow-host-redirects: false

# http模板最大重定向次数（默认：10）
#max-redirects: 10

# 为http模板禁用重定向
#disable-redirects: false

# 指定报告模板文件
#report-config: 

# 指定在所有http请求中包含的自定义header、cookie，以header:value的格式指定（cli，文件）
#header: []

# 以key=value格式自定义变量
#var: 

# 指定包含dns解析服务列表的文件
#resolvers: 

# 当dns错误时使用系统dns解析服务
#system-resolvers: false

# 关闭请求聚类功能
#disable-clustering: false

# 启用被动模式处理本地http响应数据
#passive: false

# 强制使用http2连接
#force-http2: false

# 启用在模板中使用环境变量
#env-vars: false

# 用于对扫描的主机进行身份验证的客户端证书文件（pem 编码）
#client-cert: 

# 用于对扫描的主机进行身份验证的客户端密钥文件（pem 编码）
#client-key: 

# 用于对扫描的主机进行身份验证的客户端证书颁发机构文件（pem 编码）
#client-ca: 

# 显示文件模板的匹配值，只适用于提取器
#show-match-line: false

# 指定tls sni的主机名（默认为输入的域名）
#sni: 

# 设置网络请求的保持活动时间
#dialer-keep-alive: 

# 允许访问本地文件（payload文件）
#allow-local-file-access: false

# 阻止对本地/私有网络的连接
#restrict-local-network-access: false

# 指定用于网络扫描的网卡
#interface: 

# payload的组合模式（batteringram,pitchfork,clusterbomb）
#attack-type: 

# 指定用于网络扫描的源ip
#source-ip: 

# 最大读取响应大小（默认：10485760字节）
#response-size-read: 0

# 最大储存响应大小（默认：1048576字节）
#response-size-save: 1048576

# 启用实验性的client hello（ja3）tls 随机化功能
#tls-impersonate: false

# 实验性http api端点
#http-api-endpoint: 

# 覆盖模板中设置的fuzz类型（replace、prefix、postfix、infix）
#fuzzing-type: 

# 覆盖模板中设置的fuzz模式（multiple、single）
#fuzzing-mode: 

# 启用fuzz模板扫描
#dast: false

# 显示输出中的fuzz，以便调试
#display-fuzz-points: false

# fuzz参数的频率
#fuzz-param-frequency: 10

# fuzz攻击级别 (low, medium, high)
#fuzz-aggression: low

# 每秒最大请求量（默认：150）
#rate-limit: 150

# 每秒发送的最大请求数（默认为15）
#rate-limit-duration: 

# 每个模板最大并行检测数（默认：25）
#bulk-size: 25

# 并行执行的最大模板数量（默认：25）
#concurrency: 25

# 每个模板并行运行的无头主机最大数量（默认：10）
#headless-bulk-size: 10

# 并行指定无头主机最大数量（默认：10）
#headless-concurrency: 10

# 并行执行的js运行时的最大数量（默认为120）
#js-concurrency: 120

# 每个模板的最大payload并发数（默认值为 25)
#payload-concurrency: 25

# 与 httpx 一起的 http 探测并发数（默认值为 50）
#probe-concurrency: 50

# 超时时间（默认为10秒）
#timeout: 10

# 重试次数（默认：1）
#retries: 1

# 指定http/https默认端口（例如：host:80，host:443）
#leave-default-ports: false

# 某主机扫描失败次数，跳过该主机（默认：30）
#max-host-error: 30

# 将给定错误添加到最大主机错误监视列表（标准、文件）
#track-error: []

# 禁用基于错误跳过主机扫描
#no-mhe: false

# 使用项目文件夹避免多次发送同一请求
#project: false

# 设置特定的项目文件夹
#project-path: /tmp

# 得到一个结果后停止（或许会中断模板和工作流的逻辑）
#stop-at-first-match: false

# 流模式 - 在不整理输入的情况下详细描述
#stream: false

# 扫描时使用的策略（auto/host-spray/template-spray） （默认 auto）
#scan-strategy: auto

# 输入读取超时时间（默认：3分钟）
#input-read-timeout: 

# 禁用对非url输入进行httpx探测
#no-httpx: false

# 禁用标准输入
#no-stdin: false

# 启用需要无界面浏览器的模板
#headless: false

# 在无界面下超时秒数（默认：20
#page-timeout: 20

# 在无界面浏览器运行模板时，显示浏览器
#show-browser: false

# 使用附加选项启动无界面浏览器
#headless-options: []

# 不使用自带的浏览器，使用本地浏览器
#system-chrome: false

# 列出可用的无界面操作
#list-headless-action: false

# 显示所有请求和响应
#debug: false

# 显示所有请求
#debug-req: false

# 显示所有响应
#debug-resp: false

# 使用http/socks5代理（逗号分隔，文件）
#proxy: []

# 代理所有请求
#proxy-internal: false

# 列出所有支持的dsl函数签名
#list-dsl-function: false

# 写入跟踪日志到文件
#trace-log: 

# 写入错误日志到文件
#error-log: 

# 启用挂起协程的监控
#hang-monitor: false

# 显示详细信息
#verbose: false

# 将内存转储成文件
#profile-mem: 

# 显示额外的详细信息
#vv: false

# 显示用于调试的变量输出
#show-var-dump: false

# 限制var dump中显示的字符数
#var-dump-limit: 255

# 启用pprof调试服务器
#enable-pprof: false

# 运行诊断检查
#health-check: false

# 显示正在扫描的统计信息
#stats: false

# 将统计信息以jsonl格式输出到文件
#stats-json: false

# 显示统计信息更新的间隔秒数（默认：5）
#stats-interval: 5

# 更改metrics服务的端口（默认：9092）
#metrics-port: 9092