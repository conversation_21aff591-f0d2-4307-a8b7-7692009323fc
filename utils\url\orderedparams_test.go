//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-12 19:31:53
//FilePath: /yaml_scan/utils/url/orderedparams_test.go
//Description: orderedparams单测

package urlutil

import (
	"testing"
)

// 测试NewOrderedParams
func TestNewOrderedParams(t *testing.T) {
	// 创建一个新的 OrderedParams
	op := NewOrderedParams()

	// 检查 OrderedParams 是否初始化正确
	if op == nil {
		t.Fatal("Expected NewOrderedParams to return a non-nil pointer")
	}
	if op.om.IsEmpty() {
		t.Log("OrderedParams initialized correctly with empty OrderedMap")
	} else {
		t.Error("Expected OrderedParams to be empty")
	}
}

// 测试 Encode 方法，验证编码的参数字符串是否符合预期
func TestOrderedParamsEncode(t *testing.T) {
	op := NewOrderedParams()

	// 测试编码空的 OrderedParams
	empty_result := op.Encode()
	if empty_result != "" {
		t.Error("Expected empty string for empty OrderedParams, got:", empty_result)
	}

	// 添加一些参数
	op.om.Set("param1", []string{"value1", "value2"})
	op.om.Set("param2", []string{"value3"})

	// 测试编码
	expected := "param1=value1&param1=value2&param2=value3"
	result := op.Encode()
	if result != expected {
		t.Errorf("Expected %q, got %q", expected, result)
	}

	// 测试 IncludeEquals 为 true 的情况
	op.IncludeEquals = true
	expectedWithEquals := "param1=value1&param1=value2&param2=value3"
	resultWithEquals := op.Encode()
	if resultWithEquals != expectedWithEquals {
		t.Errorf("Expected %q, got %q", expectedWithEquals, resultWithEquals)
	}

	// 测试没有值的情况
	op.om.Set("param3", []string{""})
	expectedWithEmptyValue := "param1=value1&param1=value2&param2=value3&param3="
	resultWithEmptyValue := op.Encode()
	if resultWithEmptyValue != expectedWithEmptyValue {
		t.Errorf("Expected %q, got %q", expectedWithEmptyValue, resultWithEmptyValue)
	}
}

func TestOrderedParamsAdd(t *testing.T) {
	op := NewOrderedParams()

	// 测试添加新键
	op.Add("key1", "value1")
	if values, ok := op.om.Get("key1"); !ok || len(values) != 1 || values[0] != "value1" {
		t.Errorf("Expected key1 to have value [value1], got %v", values)
	}

	// 测试添加相同键的新值
	op.Add("key1", "value2")
	if values, ok := op.om.Get("key1"); !ok || len(values) != 2 || values[0] != "value1" || values[1] != "value2" {
		t.Errorf("Expected key1 to have values [value1, value2], got %v", values)
	}

	// 测试添加不同键
	op.Add("key2", "value3")
	if values, ok := op.om.Get("key2"); !ok || len(values) != 1 || values[0] != "value3" {
		t.Errorf("Expected key2 to have value [value3], got %v", values)
	}

	// 测试添加空值
	op.Add("key1")
	if values, ok := op.om.Get("key1"); !ok || len(values) != 2 || values[0] != "value1" || values[1] != "value2" {
		t.Errorf("Expected key1 to have values [value1, 'value2'], got %v", values)
	}
}

func TestOrderedParamsDecode(t *testing.T) {
	op := NewOrderedParams()

	// 测试基本的键值对解析
	raw := "bar=baz&foo=quux"
	op.Decode(raw)

	if values, ok := op.om.Get("bar"); !ok || len(values) != 1 || values[0] != "baz" {
		t.Errorf("Expected bar to have value [baz], got %v", values)
	}
	if values, ok := op.om.Get("foo"); !ok || len(values) != 1 || values[0] != "quux" {
		t.Errorf("Expected foo to have value [quux], got %v", values)
	}

	// 测试没有值的键
	raw = "key1&key2=value2"
	op.Decode(raw)

	if values, ok := op.om.Get("key1"); !ok || len(values) != 1 || values[0] != "" {
		t.Errorf("Expected key1 to have empty value, got %v", values)
	}
	if values, ok := op.om.Get("key2"); !ok || len(values) != 1 || values[0] != "value2" {
		t.Errorf("Expected key2 to have value [value2], got %v", values)
	}

	// // 测试使用分号作为分隔符
	// raw = "param1=value1;param2=value2"
	// op.Decode(raw)

	// if values, ok := op.om.Get("param1"); !ok || len(values) != 1 || values[0] != "value1" {
	// 	t.Errorf("Expected param1 to have value [value1], got %v", values)
	// }
	// if values, ok := op.om.Get("param2"); !ok || len(values) != 1 || values[0] != "value2" {
	// 	t.Errorf("Expected param2 to have value [value2], got %v", values)
	// }

	// 测试多个相同的键
	raw = "key=value1&key=value2&key=value3"
	op.Decode(raw)

	if values, ok := op.om.Get("key"); !ok || len(values) != 3 {
		t.Errorf("Expected key to have 3 values, got %v", values)
	} else {
		if values[0] != "value1" || values[1] != "value2" || values[2] != "value3" {
			t.Errorf("Expected key values to be [value1, value2, value3], got %v", values)
		}
	}
}
