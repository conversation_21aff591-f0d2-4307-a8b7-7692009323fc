// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 16:57:35
// FilePath: /yaml_scan/pkg/retryablehttp/util.go
// Description:
package retryablehttp

import (
	"io"
	"net/http"

	readerutil "yaml_scan/utils/reader"
)

// Discard 是一个辅助函数，用于丢弃响应体并关闭底层连接
// @param req *Request: HTTP请求对象，用于更新指标统计
// @param resp *http.Response: HTTP响应对象，包含需要丢弃的响应体
// @param RespReadLimit int64:  读取的最大字节数，防止读取过大的响应 
func Discard(req *Request, resp *http.Response, RespReadLimit int64) {
	// 使用io.Copy将响应体内容复制到io.Discard（丢弃写入的所有数据）
	// LimitReader确保最多只读取RespReadLimit字节，防止占用过多内存
	_, err := io.Copy(io.Discard, io.LimitReader(resp.Body, RespReadLimit))
	if err != nil {
		req.Metrics.DrainErrors++
	}
	// 无论是否出错，都关闭响应体，确保资源被释放
	resp.Body.Close()
}

// getLength 高效地返回Reader的内容长度
// @param x io.Reader: 需要计算长度的Reader接口
// @return int64 int64: Reader中的字节数
// @return error error: 读取过程中可能发生的错误
func getLength(x io.Reader) (int64, error) {
	// 将Reader的全部内容复制到io.Discard，返回复制的字节数
	// io.Discard丢弃所有写入的数据，但会计数
	len, err := io.Copy(io.Discard, x)
	return len, err
}

// getReusableBodyandContentLength 获取请求体的可重用读取器和内容长度
// @param rawBody interface{}: 原始请求体，可以是多种类型（如字符串、[]byte、io.Reader等）
// @return *readerutil.ReusableReadCloser *readerutil.ReusableReadCloser:  包装原始请求体的可重用读取器 
// @return int64 int64: 请求体的内容长度（字节数）
// @return error error: 处理过程中可能发生的错误
func getReusableBodyandContentLength(rawBody interface{}) (*readerutil.ReusableReadCloser, int64, error) {

	// 声明变量用于存储可重用的读取器和内容长度
	var bodyReader *readerutil.ReusableReadCloser
	var contentLength int64

	if rawBody != nil {
		switch body := rawBody.(type) {
			// 如果已经是可重用读取器类型，直接使用它
		case readerutil.ReusableReadCloser:
			bodyReader = &body
		case *readerutil.ReusableReadCloser:
			bodyReader = body
		// 如果是生成读取器的函数，调用它获取读取器，然后创建可重用读取器
		case func() (io.Reader, error):
			tmp, err := body()
			if err != nil {
				return nil, 0, err
			}
			// 将获得的Reader转换为可重用读取器
			bodyReader, err = readerutil.NewReusableReadCloser(tmp)
			if err != nil {
				return nil, 0, err
			}
		// 如果是其他类型，尝试创建新的可重用读取器
		default:
			var err error
			bodyReader, err = readerutil.NewReusableReadCloser(body)
			if err != nil {
				return nil, 0, err
			}
		}
	}

	// 如果成功创建了可重用读取器，计算内容长度
	if bodyReader != nil {
		var err error
		contentLength, err = getLength(bodyReader)
		if err != nil {
			return nil, 0, err
		}
	}

	return bodyReader, contentLength, nil
}

