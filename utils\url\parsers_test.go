//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-13 15:59:09
//FilePath: /yaml_scan/utils/url/parsers_test.go
//Description: parsers单测

package urlutil

import (
	"net/url"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestRelativePathParser 测试 relativePathParser 函数
func TestRelativePathParser(t *testing.T) {
	tests := []struct {
		input         *URL
		expectedPath  string
		expectedError string
	}{
		{
			input: &URL{
				URL:      &url.URL{}, // 确保 URL 字段被初始化
				Original: "test/path",
				Unsafe:   false,
			},
			expectedPath:  "/test/path", // 期望的路径
			expectedError: "",           // 期望没有错误
		},
		{
			input: &URL{
				URL:      &url.URL{}, // 确保 URL 字段被初始化
				Original: "http://example.com/test/path",
				Unsafe:   false,
			},
			expectedPath:  "/http://example.com/test/path",
			expectedError: "",
		},

		{
			input: &URL{
				URL:      &url.URL{}, // 确保 URL 字段被初始化
				Original: "invalid-url",
				Unsafe:   true,
			},
			expectedPath:  "/invalid-url", // 期望的路径为原始输入
			expectedError: "",             // 期望没有错误
		},
	}

	for _, test := range tests {
		result, err := relativePathParser(test.input)
		if err != nil && err.Error() != test.expectedError {
			t.Errorf("relativePathParser() error = %v; expected %v", err, test.expectedError)
		}
		if result != nil && result.Path != test.expectedPath {
			t.Errorf("relativePathParser() = %q; expected %q", result.Path, test.expectedPath)
		}
	}
}

// TestParseRawRelativePath 测试 ParseRawRelativePath 函数
func TestParseRawRelativePath(t *testing.T) {
	tests := []struct {
		inputURL      string
		unsafe        bool
		expectedPath  string
		expectedError string
	}{
		{
			inputURL:     "test/path",
			unsafe:       false,
			expectedPath: "test/path", // 期望的路径
		},
		{
			inputURL:     "http://example.com/test/path",
			unsafe:       false,
			expectedPath: "http://example.com/test/path",
		},
		{
			inputURL:     "invalid-url",
			unsafe:       true,
			expectedPath: "invalid-url", // 期望的路径为原始输入
		},
	}

	for _, test := range tests {
		result, err := ParseRawRelativePath(test.inputURL, test.unsafe)
		if err != nil && err.Error() != test.expectedError {
			t.Errorf("ParseRawRelativePath() error = %v; expected %v", err, test.expectedError)
		}
		if result != nil && result.Path != test.expectedPath {
			t.Errorf("ParseRawRelativePath() = %q; expected %q", result.Path, test.expectedPath)
		}
	}
}

// TestParseUnsafeFullURL 测试 parseUnsafeFullURL 函数
func TestParseUnsafeFullURL(t *testing.T) {
	tests := []struct {
		input    string
		expected *url.URL
	}{
		{
			input:    "https://scanme.sh/%invalid",
			expected: &url.URL{Scheme: "https", Host: "scanme.sh", Path: "/%invalid"},
		},
		{
			input:    "http://example.com/%20invalid",
			expected: &url.URL{Scheme: "http", Host: "example.com", Path: "/%20invalid"},
		},
		{
			input:    "ftp://example.com/%invalid",
			expected: &url.URL{Scheme: "ftp", Host: "example.com", Path: "/%invalid"},
		},
		{
			input:    "invalid-url",
			expected: nil, // 无法解析的 URL
		},
		{
			input:    "http://example.com/",
			expected: &url.URL{Scheme: "http", Host: "example.com", Path: "/"},
		},
	}

	for _, test := range tests {
		result := parseUnsafeFullURL(test.input)
		if test.expected == nil {
			if result != nil {
				t.Errorf("parseUnsafeFullURL(%q) = %v; expected nil", test.input, result)
			}
		} else {
			if result == nil {
				t.Errorf("parseUnsafeFullURL(%q) = nil; expected %v", test.input, test.expected)
			} else if result.Scheme != test.expected.Scheme || result.Host != test.expected.Host || result.Path != test.expected.Path {
				t.Errorf("parseUnsafeFullURL(%q) = %v; expected %v", test.input, result, test.expected)
			}
		}
	}
}

// TestAbsoluteURLParser 测试 absoluteURLParser 函数
func TestAbsoluteURLParser(t *testing.T) {
	tests := []struct {
		input    *URL
		expected *URL
		isError  bool
	}{
		{
			input: &URL{URL: &url.URL{}, Original: "http://example.com/path", Unsafe: false},
			expected: &URL{
				URL:        &url.URL{Scheme: "http", Host: "example.com", Path: "/path"},
				Original:   "http://example.com/path",
				IsRelative: false,
			},
			isError: false,
		},
		{
			input: &URL{URL: &url.URL{}, Original: "https://example.com/path", Unsafe: false},
			expected: &URL{
				URL:        &url.URL{Scheme: "https", Host: "example.com", Path: "/path"},
				Original:   "https://example.com/path",
				IsRelative: false,
			},
			isError: false,
		},
		{
			input: &URL{URL: &url.URL{}, Original: "http://example.com/%invalid", Unsafe: true},
			expected: &URL{
				URL:        &url.URL{Scheme: "http", Host: "example.com", Path: "/%invalid"},
				Original:   "http://example.com/%invalid",
				IsRelative: false,
			},
			isError: false,
		},
		{
			input: &URL{URL: &url.URL{}, Original: "/relative/path", Unsafe: false},
			expected: &URL{
				URL:        &url.URL{Path: "/relative/path"},
				Original:   "/relative/path",
				IsRelative: true,
			},
			isError: false,
		},
	}

	for _, test := range tests {
		result, err := absoluteURLParser(test.input)
		if (err != nil) != test.isError {
			t.Errorf("absoluteURLParser(%v) error = %v; expected error = %v", test.input, err, test.isError)
		}
		if test.expected != nil && result != nil {
			if result.URL.String() != test.expected.URL.String() {
				t.Errorf("absoluteURLParser(%v) = %v; expected %v", test.input, result.URL, test.expected.URL)
			}
		} else if test.expected != nil && result == nil {
			t.Errorf("absoluteURLParser(%v) = nil; expected %v", test.input, test.expected)
		}
	}
}

// 测试 Parse 和 ParseURL 函数
func TestParseURL(t *testing.T) {
	testCases := []struct {
		name        string
		inputURL    string
		unsafe      bool
		expected    *URL
		expectedErr error
	}{
		{
			name:     "绝对 URL 解析",
			inputURL: "https://example.com/path",
			unsafe:   false,
			expected: &URL{
				URL:        &url.URL{Scheme: "https", Host: "example.com", Path: "/path"},
				Original:   "https://example.com/path",
				Unsafe:     false,
				IsRelative: false,
				Params:     NewOrderedParams(),
			},
			expectedErr: nil,
		},
		{
			name:     "相对 URL 解析",
			inputURL: "/path/to/resource",
			unsafe:   false,
			expected: &URL{
				URL:        &url.URL{Path: "/path/to/resource"},
				Original:   "/path/to/resource",
				Unsafe:     false,
				IsRelative: true,
				Params:     NewOrderedParams(),
			},
			expectedErr: nil,
		},
		
		{
			name:     "特殊情况：localhost",
			inputURL: "http://localhost/path",
			unsafe:   false,
			expected: &URL{
				URL:        &url.URL{Scheme: "http", Host: "localhost", Path: "/path"},
				Original:   "http://localhost/path",
				Unsafe:     false,
				IsRelative: false,
				Params:     NewOrderedParams(),
			},
			expectedErr: nil,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			u, err := ParseURL(tc.inputURL, tc.unsafe)
			if tc.expectedErr != nil {
				require.Equal(t, tc.expectedErr, err)
				return
			}

			require.NoError(t, err)
			require.Equal(t, tc.expected.IsRelative, u.IsRelative)
			require.Equal(t, tc.expected.Host, u.Host)
			require.Equal(t, tc.expected.Path, u.Path)
			require.Equal(t, tc.expected.Original, u.Original)
			require.Equal(t, tc.expected.Unsafe, u.Unsafe)
			require.Equal(t, tc.expected.URL.Scheme, u.URL.Scheme)
			require.Equal(t, tc.expected.URL.Host, u.URL.Host)
			require.Equal(t, tc.expected.URL.Path, u.URL.Path)
		})
	}
}
