package goflags

import (
	"bytes"
	"flag"
	"os"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	fileutil "yaml_scan/utils/file"
	permissionutil "yaml_scan/utils/permission"

	"github.com/cnf/structhash"
	"gopkg.in/yaml.v2"
)

// FlagData 结构体用于存储命令行标志的相关信息
type FlagData struct {
	usage        string  // 用法说明，描述该标志的用途
	short        string  // 短标志，例如 -f
	long         string  // 长标志，例如 --flag
	group        string  // 组名，未使用，除非稍后设置
	defaultValue interface{}  // 默认值，标志未提供时使用的值
	skipMarshal  bool  // 是否跳过序列化，通常用于控制标志的输出
	field        flag.Value  // 实现了 flag.Value 接口的字段，用于存储标志的值
}


// FlagSet 是一个应用程序的标志列表
type FlagSet struct {
	CaseSensitive  bool  // 是否区分大小写
	Marshal        bool  // 是否允许序列化标志
	description    string  // 标志集的描述
	customHelpText string    // 自定义帮助文本
	flagKeys       InsertionOrderedMap  // 存储标志的键，保持插入顺序
	groups         []groupData
	CommandLine    *flag.FlagSet // Go 的 flag 包的 FlagSet，用于解析命令行标志
	configFilePath string // 配置文件的路径
	OtherOptionsGroupName string   // 用于标识所有未分组的标志
	configOnlyKeys        InsertionOrderedMap  // 存储仅用于配置的标志键
}

// uniqueDeduper 定义了一个用于去重的结构体
type uniqueDeduper struct {
	hashes map[string]interface{}
}

// newUniqueDeduper 创建并返回一个新的 uniqueDeduper 实例
// 用于创建一个唯一性去重器，能够存储唯一的值。
func newUniqueDeduper() *uniqueDeduper {
	return &uniqueDeduper{hashes: make(map[string]interface{})}
}


// Hash: flagData 结构的唯一哈希值
//  当结构无法被哈希时，Hash 会引发 panic。
//  @receiver flagData *FlagData: 
//  @return string string: 
func (flagData *FlagData) Hash() string {
	hash, _ := structhash.Hash(flagData, 1)
	return hash
}

// isUnique: 在迭代过程中返回标志是否唯一
//  @receiver u *uniqueDeduper: 
//  @param data *FlagData:  指向 FlagData 结构的指针，用于检查唯一性。
//  @return bool bool: 果标志是唯一的，则返回 true；否则返回 false。
func (u *uniqueDeduper) isUnique(data *FlagData) bool {
	// 计算 FlagData 的哈希值
	dataHash := data.Hash()
	// 如果哈希值已存在，表示该标志之前已打印过，返回 false
	if _, ok := u.hashes[dataHash]; ok {
		return false 
	}
	 // 将哈希值存储到 hashes 中，表示该标志已被处理
	u.hashes[dataHash] = struct{}{}
	return true
}


// NewFlagSet 创建并返回一个新的 FlagSet 实例
func NewFlagSet() *FlagSet {
	// 设置命令行标志的错误处理方式
	flag.CommandLine.ErrorHandling()
	return &FlagSet{
		flagKeys:              newInsertionOrderedMap(),  // 初始化 flagKeys
		OtherOptionsGroupName: "other options",   // 设置未分组标志的名称
		CommandLine:           flag.NewFlagSet(os.Args[0], flag.ExitOnError),  // 创建新的 FlagSet
		configOnlyKeys:        newInsertionOrderedMap(),  // 初始化 configOnlyKeys
	}
}


// SetDescription 设置 flagSet 的 description 字段为指定的值
func (flagSet *FlagSet) SetDescription(description string) {
	flagSet.description = description
}


// SetConfigFilePath 设置 flagSet 的 ConfigFilePath 字段为指定的值
func (flagSet *FlagSet) SetConfigFilePath(filePath string) {
	flagSet.configFilePath = filePath
}


// generateDefaultConfig: 生成一个默认的YAML配置文件，包含FlagSet中的所有标志及其默认值。
//  @receiver flagSet *FlagSet: 
//  @return []byte []byte:  生成的默认配置文件的字节切片。
func (flagSet *FlagSet) generateDefaultConfig() []byte {
	// 用于存储已处理标志的哈希值
	hashes := make(map[string]struct{})
	// 创建一个字节缓冲区以构建配置文件内容
	configBuffer := &bytes.Buffer{}
	configBuffer.WriteString("# ")
	configBuffer.WriteString(path.Base(os.Args[0]))
	configBuffer.WriteString(" config file\n# generated by goflags\n\n")

	// 尝试如果设置了适当的标志则原生序列化，如果发生错误则回退到正常机制
	if flagSet.Marshal {
		 // 创建一个映射以存储要序列化的标志及其默认值
		flagsToMarshall := make(map[string]interface{})

		// 遍历所有标志
		flagSet.flagKeys.forEach(func(key string, data *FlagData) {
			if !data.skipMarshal {
				flagsToMarshall[key] = data.defaultValue
			}
		})

		// 尝试将标志映射序列化为YAML
		flagSetBytes, err := yaml.Marshal(flagsToMarshall)
		if err == nil {
			configBuffer.Write(flagSetBytes)
			return configBuffer.Bytes()
		}
	}

	// 遍历所有标志以生成默认配置
	flagSet.flagKeys.forEach(func(key string, data *FlagData) {
		// 计算标志的哈希值
		dataHash := data.Hash()
		if _, ok := hashes[dataHash]; ok {
			return
		}
		// 记录已处理的标志
		hashes[dataHash] = struct{}{}

		// 写入标志的使用说明和默认值
		configBuffer.WriteString("# ")
		configBuffer.WriteString(strings.ToLower(data.usage))
		configBuffer.WriteString("\n")
		configBuffer.WriteString("#")
		configBuffer.WriteString(data.long)
		configBuffer.WriteString(": ")
		// 根据默认值的类型写入相应的值
		switch dv := data.defaultValue.(type) {
		case string:
			configBuffer.WriteString(dv)
		case flag.Value:
			configBuffer.WriteString(dv.String())
		case StringSlice:
			configBuffer.WriteString(dv.String())
		}

		configBuffer.WriteString("\n\n")
	})
	
	 // 返回生成的配置内容，去掉尾部多余的换行
	return bytes.TrimSuffix(configBuffer.Bytes(), []byte("\n\n"))
}


// GetToolConfigDir returns the config dir path of the tool
func (flagset *FlagSet) GetToolConfigDir() string {
	cfgFilePath, _ := flagset.GetConfigFilePath()
	return filepath.Dir(cfgFilePath)
}


// readConfigFile: 读取指定路径的配置文件，并根据文件中的设置更新命令行标志。
//  @receiver flagSet *FlagSet: 
//  @param filePath string: 文件路径
//  @return error error: 如果读取或解析配置文件时发生错误，则返回相应的错误信息；如果成功，则返回nil。
func (flagSet *FlagSet) readConfigFile(filePath string) error {
	// 检测文件是否为空
	if empty, err := fileutil.IsEmpty(filePath); err == nil && empty {
		return nil
	}

	// 检测文件是否包含注释
	if fileutil.IsCommentOnly(filePath) {
		return nil
	}

	// 打开配置文件
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	// 创建一个映射以存储配置文件中的数据
	data := make(map[string]interface{})

	// 解析YAML文件
	err = yaml.NewDecoder(file).Decode(&data)
	if err != nil {
		return err
	}

	// 遍历所有命令行标志
	flagSet.CommandLine.VisitAll(func(fl *flag.Flag) {
		// 检查配置文件中是否有对应的项
		item, ok := data[fl.Name]
		// 获取当前标志的值
		value := fl.Value.String()
		// 如果当前值等于默认值且配置文件中有该项
		if strings.EqualFold(fl.DefValue, value) && ok {
			switch itemValue := item.(type) {
			case string:
				_ = fl.Value.Set(itemValue)
			case bool:
				_ = fl.Value.Set(strconv.FormatBool(itemValue))
			case int:
				_ = fl.Value.Set(strconv.Itoa(itemValue))
			case time.Duration:
				_ = fl.Value.Set(itemValue.String())
			case []interface{}:
				for _, v := range itemValue {
					vStr, ok := v.(string)
					if ok {
						_ = fl.Value.Set(vStr)
					}
				}
			}
		}
	})

	// 处理仅在配置文件中定义的标志
	flagSet.configOnlyKeys.forEach(func(key string, flagData *FlagData) {
		item, ok := data[key]
		if ok {
			fl := flag.Lookup(key)
			if fl == nil {
				// 如果标志不存在，则创建它
				flag.Var(flagData.field, key, flagData.usage)
				fl = flag.Lookup(key)
			}

			switch data := item.(type) {
			case string:
				_ = fl.Value.Set(data)
			case bool:
				_ = fl.Value.Set(strconv.FormatBool(data))
			case int:
				_ = fl.Value.Set(strconv.Itoa(data))
			case []interface{}:
				for _, v := range data {
					vStr, ok := v.(string)
					if ok {
						_ = fl.Value.Set(vStr)
					}
				}
			}
		}
	})
	return nil
}


// MergeConfigFile: 用于读取指定路径的配置文件，并将文件中的值合并到当前的标志集中。
//  @receiver flagSet *FlagSet: 
//  @param file string: 要读取的配置文件路径。
//  @return error error: 如果读取配置文件时发生错误，返回相应的错误信息；如果成功则返回 nil。
func (flagSet *FlagSet) MergeConfigFile(file string) error {
	return flagSet.readConfigFile(file)
}

// Parse: 解析令行标志，并处理配置文件的读取和创建。
//  @receiver flagSet *FlagSet: 
//  @param args ...string: 可选的命令行参数，如果未提供，则使用 os.Args[1:]。
//  @return error error: 如果解析标志或处理配置文件时发生错误，返回相应的错误信息；如果成功则返回 nil。
func (flagSet *FlagSet) Parse(args ...string) error {
	 // 设置命令行输出为标准输出
	flagSet.CommandLine.SetOutput(os.Stdout)
	 // 设置使用说明函数
	flagSet.CommandLine.Usage = flagSet.usageFunc
	// 默认解析 os.Args[1:] 中的参数 如果提供了参数，则使用提供的参数
	toParse := os.Args[1:]
	
	if len(args) > 0 {
		toParse = args
	}

	// 解析命令行参数
	_ = flagSet.CommandLine.Parse(toParse)
	// 获取配置文件路径
	configFilePath, _ := flagSet.GetConfigFilePath()

	// 如果配置文件不存在，则创建一个
	if !fileutil.FileExists(configFilePath) {
		// 生成默认配置
		configData := flagSet.generateDefaultConfig()
		// 获取工具配置目录
		configFileDir := flagSet.GetToolConfigDir()
		// 创建配置目录
		if !fileutil.FolderExists(configFileDir) {
			_ = fileutil.CreateFolder(configFileDir)
		}
		// 写入默认配置文件
		return os.WriteFile(configFilePath, configData, permissionutil.ConfigFilePermission)
	}

	// 尝试在解析标志后读取默认配置
	 _ = flagSet.MergeConfigFile(configFilePath)

	return nil
}