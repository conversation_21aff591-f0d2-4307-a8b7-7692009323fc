// Author: chenjb
// Version: V1.0
// Date: 2025-05-16 09:53:40
// FilePath: /yaml_scan/pkg/hybridMap/disk/bboltdb.go
// Description: 基于 BBolt 的磁盘存储引擎，BBolt 是一个嵌入式键值数据库
package disk

import (
	"bytes"
	"strconv"
	"sync"
	"time"

	"github.com/pkg/errors"

	bolt "go.etcd.io/bbolt"
)

// BBoltDB - 表示基于 BBolt 的数据库实现
// 提供了键值存储的基本操作和事务支持
type BBoltDB struct {
	db           *bolt.DB // 底层 BBolt 数据库实例
	sync.RWMutex          // 读写锁，保证并发安全
	BucketName   string   // 存储桶名称，BBolt 使用桶来组织数据
}

// OpenBoltDBB 打开指定路径的 BBolt 数据库
// @param path string: 数据库文件路径
// @return *BBoltDB *BBoltDB: 数据库实例
// @return error error: 可能出现的错误
func OpenBoltDBB(path string) (*BBoltDB, error) {
	db, err := bolt.Open(path, 0600, nil)
	if err != nil {
		return nil, err
	}

	bbdb := new(BBoltDB)
	bbdb.db = db

	return bbdb, nil
}

// Size - 返回数据库在磁盘上的大小（字节数）
func (b *BBoltDB) Size() int64 {
	// not implemented
	return 0
}

// Close 关闭数据库连接并释放相关资源
func (b *BBoltDB) Close() {
	b.db.Close()
}

// GC - 运行垃圾回收以回收空间 未实现
func (b *BBoltDB) GC() error {
	return ErrNotImplemented
}

// Incr 将指定键的值增加指定数量
// @receiver ldb 
// @param k string: 要增加值的键
// @param by int64: 要增加的数量
// @return int64 int64: 增加后的新值
// @return error error: 可能的错误
// 未实现
func (b *BBoltDB) Incr(k string, by int64) (int64, error) {
	return 0, ErrNotImplemented
}

// set 内部方法，设置键值对并处理过期时间
// @receiver ldb 
// @param k []byte:  键名字节数组
// @param v []byte:  值字节数组
// @param ttl time.Duration:  过期时间，-1表示永不过期
// @return error error: 可能的错误
func (b *BBoltDB) set(k, v []byte, ttl time.Duration) error {
	return b.db.Update(func(tx *bolt.Tx) error {
		var expires int64
		if ttl > 0 {
			expires = time.Now().Add(ttl).Unix()
		}
		b, err := tx.CreateBucketIfNotExists([]byte(b.BucketName))
		if err != nil {
			return err
		}
		expiresBytes := append(intToByteSlice(expires), expSeparator[:]...)
		v = append(expiresBytes, v...)
		return b.Put(k, v)
	})
}

// Set 设置键值对并处理过期时间
// @receiver ldb 
// @param k string:  键名
// @param v []byte:  值字节数组
// @param ttl time.Duration:  过期时间，-1表示永不过期
// @return error error: 可能的错误
func (b *BBoltDB) Set(k string, v []byte, ttl time.Duration) error {
	return b.set([]byte(k), v, ttl)
}

// MSet - 为实现
func (b *BBoltDB) MSet(data map[string][]byte) error {
	return ErrNotImplemented
}

// get 内部方法，获取指定键的值并处理过期逻辑
// @receiver ldb 
// @param k string: 要获取的键名
// @return []byte []byte: 键对应的值（如果存在且未过期）
// @return error error: 可能的错误，如键不存在或已过期
func (b *BBoltDB) get(k string) ([]byte, error) {
	var data []byte
	delete := false

	return data, b.db.Update(func(tx *bolt.Tx) error {
		b, err := tx.CreateBucketIfNotExists([]byte(b.BucketName))
		if err != nil {
			return err
		}
		data = b.Get([]byte(k))
		if data == nil {
			return ErrNoData
		}
		parts := bytes.SplitN(data, []byte(expSeparator), 2)
		expires, actual := parts[0], parts[1]
		if exp, _ := strconv.Atoi(string(expires)); exp > 0 && int(time.Now().Unix()) >= exp {
			delete = true
		}
		data = actual

		if delete {
			return b.Delete([]byte(k))
		}

		return nil
	})
}


// Get 获取指定键的值
// @receiver ldb 
// @param k string: 要获取的键名
// @return []byte []byte: 键对应的值（如果存在且未过期）
// @return error error: 可能的错误，如键不存在或已过期
func (b *BBoltDB) Get(k string) ([]byte, error) {
	return b.get(k)
}

// MGet 批量获取多个键的值
// @receiver ldb 
// @param keys []string:  要获取的键名列表
// @return [] []: 对应的值列表，返回的数组内项的顺序与传入的keys一致 
// 如果某个键不存在或已过期，对应位置为空字节数组
func (b *BBoltDB) MGet(keys []string) [][]byte {
	var data [][]byte
	for _, key := range keys {
		val, err := b.get(key)
		if err != nil {
			data = append(data, []byte{})
			continue
		}
		data = append(data, val)
	}
	return data
}

// TTL 返回指定键值对的剩余生存时间（秒）
// @receiver ldb 
// @param key string:  要查询的键名
// @return int64 int64: 剩余生存时间（秒），-1表示永不过期，-2表示键不存在或已过期
func (b *BBoltDB) TTL(key string) int64 {
	item, err := b.Get(key)
	if err != nil {
		return -2
	}
	parts := bytes.SplitN(item, []byte(expSeparator), 2)
	exp, _ := strconv.Atoi(string(parts[0]))
	if exp == 0 {
		return -1
	}

	now := time.Now().Unix()
	if now >= int64(exp) {
		return -2
	}

	return int64(exp) - now
}

// MDel - 未实现
func (b *BBoltDB) MDel(keys []string) error {
	return ErrNotImplemented
}

// Del 删除指定的键
// @receiver ldb 
// @param key string: 要删除的键名
// @return error error: 可能得错误
func (b *BBoltDB) Del(key string) error {
	return b.db.Update(func(tx *bolt.Tx) error {
		b, err := tx.CreateBucketIfNotExists([]byte(b.BucketName))
		if err != nil {
			return err
		}
		return b.Delete([]byte(key))
	})
}

// Scan 使用指定的处理函数遍历整个存储
// @receiver ldb 
// @param scannerOpt ScannerOptions: 扫描选项，包含偏移量、前缀过滤、处理函数等
// @return error error: 可能的错误
func (b *BBoltDB) Scan(scannerOpt ScannerOptions) error {
	valid := func(k []byte) bool {
		if k == nil {
			return false
		}

		if scannerOpt.Prefix != "" && !bytes.HasPrefix(k, []byte(scannerOpt.Prefix)) {
			return false
		}

		return true
	}
	return b.db.View(func(tx *bolt.Tx) error {
		b := tx.Bucket([]byte(b.BucketName))
		if b == nil {
			return errors.New("bucket not found")
		}
		c := b.Cursor()
		for key, val := c.First(); key != nil; key, val = c.Next() {
			parts := bytes.SplitN(val, []byte(expSeparator), 2)
			data := val
			if len(parts) == 2 {
				data = parts[1]
			}
			if !valid(key) {
				continue
			} 
			if scannerOpt.Handler(key, data) != nil {
				break
			}
		}
		return nil
	})
}
