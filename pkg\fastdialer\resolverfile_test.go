// 
// Author: chenjb
// Version: V1.0
// Date: 2025-05-26 19:38:50
// FilePath: /yaml_scan/pkg/fastdialer/resolverfile_test.go
// Description: 
package fastdialer


import (
	"os"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestHandleResolverLine 测试解析resolv.conf文件中的一行
func TestHandleResolverLine(t *testing.T) {
	testCases := []struct {
		name     string
		line     string
		expected string
	}{
		{
			name:     "有效的nameserver行",
			line:     "nameserver *******",
			expected: "*******",
		},
		{
			name:     "带注释的nameserver行",
			line:     "nameserver ******* # Google DNS",
			expected: "*******",
		},
		{
			name:     "纯注释行",
			line:     "# 这是一个注释",
			expected: "",
		},
		{
			name:     "空行",
			line:     "",
			expected: "",
		},
		{
			name:     "非nameserver行",
			line:     "options timeout:2",
			expected: "",
		},
		{
			name:     "IPv6地址",
			line:     "nameserver 2001:4860:4860::8888",
			expected: "2001:4860:4860::8888",
		},
		{
			name:     "带前导空格的nameserver行",
			line:     "  nameserver *******",
			expected: "*******",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := HandleResolverLine(tc.line)
			require.Equal(t, tc.expected, result, "HandleResolverLine返回结果与预期不符")
		})
	}
}

// TestLoadResolverFile 测试加载resolv.conf文件
func TestLoadResolverFile(t *testing.T) {
	// 创建临时测试文件
	tempDir := t.TempDir()
	tempFile := filepath.Join(tempDir, "resolv.conf")
	
	content := `# 测试resolv.conf文件
nameserver *******
nameserver *******
options ndots:1 timeout:5
# nameserver *******
nameserver 2001:4860:4860::8888
invalid line
`
	
	err := os.WriteFile(tempFile, []byte(content), 0644)
	require.NoError(t, err, "写入临时测试文件失败")
	
	// 保存原始环境变量和常量值，测试后恢复
	originalMaxEntries := MaxResolverEntries
	
	// 使用RESOLVERS_PATH环境变量指向测试文件
	os.Setenv("RESOLVERS_PATH", tempFile)
	
	defer func() {
		os.Unsetenv("RESOLVERS_PATH") // 恢复环境
		
		// Go语言中不能修改常量，但本例中ResolverFilePath实际上是个变量，所以我们可以在测试结束后恢复它
		// 注意：在实际代码中应避免这样做，因为这会使测试变得不稳定
		// 这里这样做只是为了完成单元测试
		MaxResolverEntries = originalMaxEntries
	}()
	
	// 测试正常加载
	resolvers, err := loadResolverFile()
	require.NoError(t, err, "加载解析器文件应成功")
	
	expectedResolvers := []string{
		"*******:53",
		"*******:53",
		"[2001:4860:4860::8888]:53", // IPv6地址会被中括号包围
	}
	
	require.Equal(t, expectedResolvers, resolvers, "解析出的DNS服务器列表不符合预期")
	
	// 测试条目限制
	MaxResolverEntries = 1
	resolversLimited, err := loadResolverFile()
	require.NoError(t, err, "加载解析器文件应成功")
	require.Len(t, resolversLimited, 1, "应只加载一个解析器")
	require.Equal(t, "*******:53", resolversLimited[0], "加载的解析器应为第一个")
	
	// 测试文件不存在
	os.Setenv("RESOLVERS_PATH", "/non/existent/file")
	_, err = loadResolverFile()
	require.Error(t, err, "加载不存在的文件应返回错误")
}

// TestMaxResolverEntries 测试最大解析器条目数限制
func TestMaxResolverEntries(t *testing.T) {
	// 验证默认限制
	require.Equal(t, 4096, MaxResolverEntries, "默认最大解析器条目数应为4096")
} 

