// Author: chenjb
// Version: V1.0
// Date: 2025-06-18 16:10:33
// FilePath: /yaml_scan/pkg/clistats/callbacks.go
// Description:  提供统计回调函数的实现，用于计算动态统计数据如每秒请求数
package clistats

import "time"

// RequestPerSecondCallbackOptions 包含生成每秒请求数指标回调函数所需的配置选项
//
// 此结构体保存了创建计算每秒请求数所需的字段ID信息，包括开始时间和请求计数器
type RequestPerSecondCallbackOptions struct {
	// StartTimeFieldID 是客户端开始时间字段的ID
	StartTimeFieldID string
	// RequestsCounterID 是发送请求计数器的ID
	RequestsCounterID string
}

// NewRequestsPerSecondCallback 创建一个计算每秒请求数的回调函数
//
// 此函数返回一个动态回调，用于计算每秒处理的请求数量。
// 计算方法是将请求总数除以从开始时间到现在的秒数。
//
// @param options RequestPerSecondCallbackOptions: 包含必要字段ID的配置选项
// @return DynamicCallback DynamicCallback: 用于计算每秒请求数的回调函数
func NewRequestsPerSecondCallback(options RequestPerSecondCallbackOptions) DynamicCallback {
	return func(client StatisticsClient) interface{} {
		// 获取开始时间
		start, ok := client.GetStatic(options.StartTimeFieldID)
		if !ok {
			return nil
		}
		// 确保开始时间是time.Time类型
		startTime, ok := start.(time.Time)
		if !ok {
			return nil
		}

		// 获取请求计数
		requests, ok := client.GetCounter(options.RequestsCounterID)
		if !ok {
			return nil
		}
		// 计算并返回每秒请求数
		return uint64(float64(requests) / time.Since(startTime).Seconds())
	}
}
