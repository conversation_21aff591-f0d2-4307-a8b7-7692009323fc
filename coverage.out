mode: set
yaml_scan/pkg/gologger/formatter/cli.go:22.36,24.2 1 1
yaml_scan/pkg/gologger/formatter/cli.go:28.55,38.23 5 1
yaml_scan/pkg/gologger/formatter/cli.go:38.23,44.3 5 1
yaml_scan/pkg/gologger/formatter/cli.go:47.2,48.27 2 1
yaml_scan/pkg/gologger/formatter/cli.go:48.27,54.3 5 1
yaml_scan/pkg/gologger/formatter/cli.go:57.2,59.32 3 1
yaml_scan/pkg/gologger/formatter/cli.go:59.32,66.3 6 0
yaml_scan/pkg/gologger/formatter/cli.go:69.2,72.35 2 1
yaml_scan/pkg/gologger/formatter/cli.go:72.35,77.3 4 1
yaml_scan/pkg/gologger/formatter/cli.go:79.2,80.18 2 1
yaml_scan/pkg/gologger/formatter/cli.go:85.46,86.19 1 1
yaml_scan/pkg/gologger/formatter/cli.go:86.19,88.3 1 0
yaml_scan/pkg/gologger/formatter/cli.go:89.2,89.36 1 1
yaml_scan/pkg/gologger/formatter/cli.go:94.46,97.34 2 1
yaml_scan/pkg/gologger/formatter/cli.go:97.34,99.3 1 1
yaml_scan/pkg/gologger/formatter/cli.go:100.2,100.21 1 1
yaml_scan/pkg/gologger/formatter/cli.go:101.26,102.9 1 0
yaml_scan/pkg/gologger/formatter/cli.go:103.45,104.58 1 1
yaml_scan/pkg/gologger/formatter/cli.go:105.25,106.70 1 0
yaml_scan/pkg/gologger/formatter/cli.go:107.25,108.57 1 0
yaml_scan/pkg/gologger/formatter/cli.go:109.25,110.61 1 1
yaml_scan/pkg/gologger/formatter/cli.go:111.27,112.60 1 0
yaml_scan/pkg/gologger/formatter/json.go:21.13,23.2 1 1
yaml_scan/pkg/gologger/formatter/json.go:25.22,27.2 1 1
yaml_scan/pkg/gologger/formatter/json.go:30.56,36.46 2 1
yaml_scan/pkg/gologger/formatter/json.go:36.46,37.18 1 1
yaml_scan/pkg/gologger/formatter/json.go:37.18,40.4 2 1
yaml_scan/pkg/gologger/formatter/json.go:43.2,43.35 1 1
yaml_scan/pkg/gologger/formatter/json.go:43.35,45.3 1 1
yaml_scan/pkg/gologger/formatter/json.go:48.2,53.34 3 1
yaml_scan/pkg/gologger/formatter/tee.go:17.57,24.2 3 1
yaml_scan/pkg/gologger/formatter/tee.go:27.65,28.18 1 1
yaml_scan/pkg/gologger/formatter/tee.go:28.18,30.3 1 0
yaml_scan/pkg/gologger/formatter/tee.go:32.2,37.17 3 1
yaml_scan/pkg/gologger/formatter/tee.go:37.17,39.3 1 1
yaml_scan/pkg/gologger/formatter/tee.go:40.2,40.16 1 1
yaml_scan/pkg/gologger/formatter/tee.go:40.16,42.3 1 0
yaml_scan/pkg/gologger/formatter/tee.go:45.2,47.34 2 1
yaml_scan/utils/os/os.go:7.23,9.2 1 1
yaml_scan/pkg/gologger/levels/levels.go:24.32,26.2 1 1
yaml_scan/pkg/gologger/levels/levels.go:30.49,32.46 2 1
yaml_scan/pkg/gologger/levels/levels.go:32.46,33.29 1 1
yaml_scan/pkg/gologger/levels/levels.go:33.29,35.4 1 1
yaml_scan/pkg/gologger/levels/levels.go:37.2,37.64 1 1
yaml_scan/pkg/gologger/writer/cli.go:20.20,22.2 1 1
yaml_scan/pkg/gologger/writer/cli.go:25.54,31.15 3 1
yaml_scan/pkg/gologger/writer/cli.go:32.26,34.33 2 1
yaml_scan/pkg/gologger/writer/cli.go:35.10,37.33 2 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:40.13,42.40 1 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:42.40,44.3 1 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:47.2,52.53 4 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:66.45,67.19 1 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:67.19,69.3 1 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:74.36,77.2 2 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:81.52,90.30 5 0
yaml_scan/pkg/gologger/writer/filewithrotation.go:90.30,92.3 1 0
yaml_scan/pkg/gologger/writer/filewithrotation.go:92.8,92.36 1 0
yaml_scan/pkg/gologger/writer/filewithrotation.go:92.36,94.3 1 0
yaml_scan/pkg/gologger/writer/filewithrotation.go:96.2,100.24 3 0
yaml_scan/pkg/gologger/writer/filewithrotation.go:100.24,102.28 1 0
yaml_scan/pkg/gologger/writer/filewithrotation.go:102.28,104.18 2 0
yaml_scan/pkg/gologger/writer/filewithrotation.go:104.18,107.5 1 0
yaml_scan/pkg/gologger/writer/filewithrotation.go:113.74,115.16 2 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:115.16,117.3 1 0
yaml_scan/pkg/gologger/writer/filewithrotation.go:118.2,118.15 1 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:122.56,125.16 3 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:125.16,127.3 1 0
yaml_scan/pkg/gologger/writer/filewithrotation.go:129.2,129.23 1 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:129.23,131.3 1 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:133.2,133.46 1 0
yaml_scan/pkg/gologger/writer/filewithrotation.go:137.52,140.16 3 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:140.16,142.3 1 0
yaml_scan/pkg/gologger/writer/filewithrotation.go:143.2,147.16 3 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:147.16,149.3 1 0
yaml_scan/pkg/gologger/writer/filewithrotation.go:151.2,151.12 1 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:155.56,160.2 3 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:164.45,168.16 3 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:168.16,170.3 1 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:172.2,181.87 5 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:181.87,190.3 5 0
yaml_scan/pkg/gologger/writer/filewithrotation.go:195.87,201.24 2 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:201.24,203.3 1 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:206.2,207.16 2 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:207.16,209.3 1 0
yaml_scan/pkg/gologger/writer/filewithrotation.go:212.2,213.16 2 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:213.16,215.3 1 0
yaml_scan/pkg/gologger/writer/filewithrotation.go:217.2,217.17 1 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:221.67,225.15 3 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:226.26,228.17 2 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:228.17,230.4 1 0
yaml_scan/pkg/gologger/writer/filewithrotation.go:232.3,233.17 2 1
yaml_scan/pkg/gologger/writer/filewithrotation.go:233.17,235.4 1 0
yaml_scan/pkg/gologger/writer/filewithrotation.go:237.10,239.17 2 0
yaml_scan/pkg/gologger/writer/filewithrotation.go:239.17,241.4 1 0
yaml_scan/pkg/gologger/writer/filewithrotation.go:242.3,243.17 2 0
yaml_scan/pkg/gologger/writer/filewithrotation.go:243.17,245.4 1 0
yaml_scan/pkg/gologger/writer/newline.go:8.13,9.24 1 1
yaml_scan/pkg/gologger/writer/newline.go:9.24,11.3 1 0
yaml_scan/pkg/gologger/writer/newline.go:11.8,13.3 1 1
