// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 19:12:48
// FilePath: /yaml_scan/utils/reader/timeout_reader.go
// Description: 提供一个带有超时功能的读取器包装器，可在指定时间后停止等待读取操作
package reader

import (
	"context"
	"io"
	"time"
)

// TimeoutReader 是一个读取器包装器，在超时时间后停止等待
// 它可以包装任何io.Reader，为其添加超时功能
// 当读取操作超过指定的超时时间后，将返回超时错误
type TimeoutReader struct {
	Timeout  time.Duration // 读取操作的超时时间
	Reader   io.Reader     // 被包装的原始读取器
	datachan chan struct{} // 用于通知读取完成的通道
}

// Read 实现了io.Reader接口的Read方法
// 从被包装的Reader中读取数据到提供的缓冲区，如果超过超时时间则返回超时错误
// @receiver reader 
// @param p []byte: 用于存储读取数据的字节切片
// @return n int:  读取的字节数
// @return err error: 读取过程中可能发生的错误，包括超时错误
func (reader TimeoutReader) Read(p []byte) (n int, err error) {
	var (
		ctx    context.Context
		cancel context.CancelFunc
	)
	// 如果设置了超时时间，创建一个带有超时的上下文
	if reader.Timeout > 0 {
		ctx, cancel = context.WithTimeout(context.Background(), time.Duration(reader.Timeout))
		defer cancel()
	}

	// 如果数据通道尚未初始化，则创建它
	if reader.datachan == nil {
		reader.datachan = make(chan struct{})
	}

	// 在后台协程中进行实际的读取操作
	go func() {
		n, err = reader.Reader.Read(p)
		// 通过发送空结构体到通道来通知读取完成
		reader.datachan <- struct{}{}
	}()

	// 使用select语句处理超时和数据读取
	select {
	case <-ctx.Done():
		err = ErrTimeout
		return
	case <-reader.datachan:
		return
	}
}

