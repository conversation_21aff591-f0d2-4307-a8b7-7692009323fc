// Author: chenjb
// Version: V1.0
// Date: 2025-06-17 20:23:56
// FilePath: /yaml_scan/pkg/protocols/common/generators/options.go
// Description:
package generators

import "yaml_scan/pkg/types"

// BuildPayloadFromOptions returns a map with the payloads provided via CLI
func BuildPayloadFromOptions(options *types.Options) map[string]interface{} {
	m := make(map[string]interface{})
	// merge with vars
	if !options.Vars.IsEmpty() {
		m = MergeMaps(m, options.Vars.AsMap())
	}

	// merge with env vars
	if options.EnvironmentVariables {
		m = MergeMaps(EnvVars(), m)
	}
	return m
}

