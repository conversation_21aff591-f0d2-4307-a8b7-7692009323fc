// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 16:44:23
// FilePath: /yaml_scan/pkg/retryablehttp/request.go
// Description:定义了支持重试功能的HTTP请求结构和相关辅助函数
package retryablehttp

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"net/http/httptrace"
	"net/http/httputil"
	"net/url"
	"os"

	urlutil "yaml_scan/utils/url"
	readerutil "yaml_scan/utils/reader"
)

// PreferHTTP 当为true时，请求使用`http`作为协议而不是`https`
// 影响请求的默认协议选择
var PreferHTTP bool

// RequestLogHook 允许在每次重试之前运行特定函数
// 参数包括将要发送的HTTP请求和重试次数（初始请求为0）
// 可以用于记录请求日志、监控或调试目的
type RequestLogHook func(*http.Request, int)



// ResponseLogHook 类似于RequestLogHook，但允许在每个HTTP响应上运行函数
// 无论是否需要执行后续重试，此函数都将在执行的每个HTTP请求结束时被调用
// 注意：如果在此方法中读取或关闭响应体，这将影响从Do()返回的响应
type ResponseLogHook func(*http.Response)


// ErrorHandler 在重试次数用尽时被调用，包含来自http库的最后状态
// 如果未指定，库的默认行为是关闭响应体并返回一个错误，指示尝试了多少次
// 如果覆盖此函数，请确保在需要时关闭响应体
type ErrorHandler func(resp *http.Response, err error, numTries int) (*http.Response, error)

// Metrics 包含关于每个请求的指标数据
type Metrics struct {
	// Failures 表示失败的请求数
	Failures int
	// Retries 表示请求的重试次数
	Retries int
	// DrainErrors 表示在排空响应体时发生的错误数
	DrainErrors int
}

// AuthType 表示身份验证类型的枚举
type AuthType uint8

const (
	// DigestAuth 表示摘要认证类型
	DigestAuth AuthType = iota
)


// Auth 包含特定的认证信息
type Auth struct {
	// Type 认证类型（如DigestAuth）
	Type     AuthType
	// Username 用户名
	Username string
	// Password 密码
	Password string
}

// Request  封装了创建HTTP请求所需的元数据
type Request struct {
	// 内嵌一个HTTP请求。这使得*Request的行为与*http.Request完全一致，
	// 所有元方法都能被支持
	*http.Request

	// URL 是经过自定义处理的URL结构，提供比标准url.URL更多的功能
	*urlutil.URL

	// Metrics 包含请求的指标数据
	Metrics Metrics

	// Auth 认证信息
	Auth *Auth

	// TraceInfo 包含请求的跟踪信息
	TraceInfo *TraceInfo
}

// WithContext 返回一个包装的Request，其中包含底层*http.Request的一个浅拷贝，
// 且其上下文已更改为ctx。提供的ctx必须是非nil的。
// @receiver r 
// @param ctx context.Context: 要使用的上下文
// @return *Request *Request: 更新了上下文的请求对象
func (r *Request) WithContext(ctx context.Context) *Request {
	r.Request = r.Request.WithContext(ctx)
	return r
}

// BodyBytes 允许访问请求的主体内容
// 它类似于http.Request的Body变量，但它返回底层数据的副本而不是直接消耗它
// @receiver r 
// @return []byte []byte: 请求体的字节数据
// @return error error: 可能的错误
func (r *Request) BodyBytes() ([]byte, error) {
	// 如果请求体为空，直接返回nil
	if r.Request.Body == nil {
		return nil, nil
	}
		// 创建一个新的缓冲区
	buf := new(bytes.Buffer)
	// 从请求体中读取所有数据到缓冲区
	_, err := buf.ReadFrom(r.Body)
	if err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}

// Update  使用参数的新变化更新请求URL（如果有的话）
// @receiver r 
func (r *Request) Update() {
	r.URL.Update()
	updateScheme(r.URL.URL)
}

// SetURL  使用给定的URL更新请求URL（即http.Request.URL）
// @receiver r 
// @param u *urlutil.URL: 
func (r *Request) SetURL(u *urlutil.URL) {
	r.URL = u
	r.Request.URL = u.URL
	r.Update()
}

// Clone 复制并返回一个新的Request对象
// @receiver r 
// @param ctx context.Context:  新请求使用的上下文
// @return *Request *Request: 复制的请求对象
func (r *Request) Clone(ctx context.Context) *Request {
	// 首先更新当前请求的URL
	r.Update()
	// 克隆URL对象
	ux := r.URL.Clone()
	// 克隆原始http.Request
	req := r.Request.Clone(ctx)
	// 更新克隆后的请求的URL
	req.URL = ux.URL
	ux.Update()
	// 处理认证信息
	var auth *Auth
	if r.hasAuth() {
		auth = &Auth{
			Type:     r.Auth.Type,
			Username: r.Auth.Username,
			Password: r.Auth.Password,
		}
	}
	return &Request{
		Request: req,
		URL:     ux,
		Metrics: Metrics{},  // 指标数据不应该被克隆
		Auth:    auth,
	}
}

// Dump 返回请求的二进制转储
// @receiver r 
// @return []byte []byte: 请求的二进制转储数据
// @return error error: 可能的错误
func (r *Request) Dump() ([]byte, error) {
	resplen := int64(0)
	dumpbody := true
	// 克隆请求以避免修改原始请求
	clone := r.Clone(context.TODO())

	// 克隆请求以避免修改原始请求
	if clone.Body != nil {
		resplen, _ = getLength(clone.Body)
	}

		// 如果请求体为空，不需要转储请求体
	if resplen == 0 {
		dumpbody = false
		clone.ContentLength = 0
		clone.Body = nil
		delete(clone.Header, "Content-length")
	} else {
		clone.ContentLength = resplen
	}
	// 使用httputil.DumpRequestOut转储请求
	dumpBytes, err := httputil.DumpRequestOut(clone.Request, dumpbody)
	if err != nil {
		return nil, err
	}
	return dumpBytes, nil
}

// hasAuth 检查请求是否具有身份验证信息
// @receiver request 
// @return bool bool: 如果请求具有身份验证信息则返回true
func (request *Request) hasAuth() bool {
	return request.Auth != nil
}

// FromRequest 将http.Request包装成retryablehttp.Request
// @param r *http.Request: 要包装的标准http.Request
// @return *Request *Request: 包装后的可重试请求
// @return error error: 可能的错误
func FromRequest(r *http.Request) (*Request, error) {
	// 创建Request结构
	req := Request{
		Request: r,
		Metrics: Metrics{},
		Auth:    nil,
	}

	// 处理URL
	if r.URL != nil {
		// 解析URL字符串为自定义URL对象
		urlx, err := urlutil.Parse(r.URL.String())
		if err != nil {
			return nil, err
		}
		req.URL = urlx
	}

	// 处理请求体
	if r.Body != nil {
		// 创建可重用的读取器，允许请求体被多次读取
		body, err := readerutil.NewReusableReadCloser(r.Body)
		if err != nil {
			return nil, err
		}
		r.Body = body
		// 获取内容长度
		req.ContentLength, err = getLength(body)
		if err != nil {
			return nil, err
		}
	}

	return &req, nil
}

// FromRequestWithTrace  将http.Request包装成启用了跟踪的retryablehttp.Request
// @param r *http.Request: 要包装的标准http.Request
// @return *Request *Request: 包装后的可跟踪、可重试请求
// @return error error: 可能的错误
func FromRequestWithTrace(r *http.Request) (*Request, error) {
	// 创建HTTP跟踪客户端
	trace := &httptrace.ClientTrace{
		// 当获取到连接时的回调
		GotConn: func(connInfo httptrace.GotConnInfo) {
			fmt.Fprintf(os.Stderr, "Got connection\tReused: %v\tWas Idle: %v\tIdle Time: %v\n", connInfo.Reused, connInfo.WasIdle, connInfo.IdleTime)
		},
		// 当开始连接时的回调
		ConnectStart: func(network, addr string) {
			fmt.Fprintf(os.Stderr, "Dial start\tnetwork: %s\taddress: %s\n", network, addr)
		},
		// 当连接完成时的回调
		ConnectDone: func(network, addr string, err error) {
			fmt.Fprintf(os.Stderr, "Dial done\tnetwork: %s\taddress: %s\terr: %v\n", network, addr, err)
		},
		// 当收到第一个响应字节时的回调
		GotFirstResponseByte: func() {
			fmt.Fprintf(os.Stderr, "Got response's first byte\n")
		},
		// 当写入请求头时的回调
		WroteHeaders: func() {
			fmt.Fprintf(os.Stderr, "Wrote request headers\n")
		},
		// 当写入请求完成时的回调
		WroteRequest: func(wr httptrace.WroteRequestInfo) {
			fmt.Fprintf(os.Stderr, "Wrote request, err: %v\n", wr.Err)
		},
	}

	// 添加跟踪到请求上下文
	r = r.WithContext(httptrace.WithClientTrace(r.Context(), trace))

	return FromRequest(r)
}

// NewRequestFromURLWithContext 使用上下文创建一个新的包装请求
// @param ctx :请求的上下文
// @param method string: HTTP方法（GET、POST等）
// @param urlx *urlutil.URL: 自定义URL对象
// @param body interface{}: 请求体内容
// @return *Request *Request: 创建的请求
// @return error error: 创建过程中可能发生的错误
func NewRequestFromURLWithContext(ctx context.Context, method string, urlx *urlutil.URL, body interface{}) (*Request, error) {
	// 获取可重用的请求体和内容长度
	bodyReader, contentLength, err := getReusableBodyandContentLength(body)
	if err != nil {
		return nil, err
	}

	// 我们在开始时提供一个没有路径的URL给http.NewRequest，然后直接替换url实例，
	// 因为`http.NewRequest()`内部使用`url.Parse()`解析，这会删除/覆盖urlutil.URL
	// 在不安全模式下所做的任何修补（例如：https://scanme.sh/%invalid）
	// 注意：这对发送请求时的实际路径没有影响
	// `http.NewRequestxxx`内部只使用`u.Host`，所有其他数据都存储在`url.URL`实例中
	httpReq, err := http.NewRequestWithContext(ctx, method, "https://"+urlx.Host, nil)
	if err != nil {
		return nil, err
	}
	// 更新URL状态
	urlx.Update()
	httpReq.URL = urlx.URL
	updateScheme(httpReq.URL)
	
	// 检查是否有请求体
	if bodyReader != nil {
		httpReq.ContentLength = contentLength
		httpReq.Body = bodyReader
	}

	request := &Request{
		Request: httpReq,
		URL:     urlx,
		Metrics: Metrics{},
	}

	return request, nil
}



// NewRequestFromURL 创建一个新的请求，使用给定的自定义URL
// @param method string: HTTP方法（GET、POST等）
// @param urlx *urlutil.URL: 自定义URL对象
// @param body interface{}: 请求体内容
// @return *Request *Request: 创建的请求
// @return error error: 创建过程中可能发生的错误
func NewRequestFromURL(method string, urlx *urlutil.URL, body interface{}) (*Request, error) {
	return NewRequestFromURLWithContext(context.Background(), method, urlx, body)
}

// NewRequest  创建一个新的请求，使用给定的URL字符串
// @param method string:  HTTP方法（GET、POST等）
// @param url string: URL字符串
// @param body interface{}: 请求体内容
// @return *Request *Request: 创建的请求
// @return error error: 创建过程中可能发生的错误
func NewRequest(method, url string, body interface{}) (*Request, error) {
	urlx, err := urlutil.Parse(url)
	if err != nil {
		return nil, err
	}
	return NewRequestFromURL(method, urlx, body)
}

// NewRequestWithContext 使用上下文创建一个新的请求，使用给定的URL字符串
// @param ctx context.Context: 请求的上下文
// @param method string:  HTTP方法（GET、POST等）
// @param url string:  URL字符串
// @param body interface{}: 请求体内容
// @return *Request *Request: 创建的请求
// @return error error: 创建过程中可能发生的错误
func NewRequestWithContext(ctx context.Context, method, url string, body interface{}) (*Request, error) {
	urlx, err := urlutil.Parse(url)
	if err != nil {
		return nil, err
	}
	return NewRequestFromURLWithContext(ctx, method, urlx, body)
}

// updateScheme 根据全局设置更新URL的协议方案
// @param u *url.URL: 要更新的URL
func updateScheme(u *url.URL) {
	if u.Host != "" && u.Scheme == "" {
		if PreferHTTP {
			u.Scheme = "http"
		} else {
			u.Scheme = "https"
		}
	}
}