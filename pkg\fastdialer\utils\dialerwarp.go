// Author: chenjb
// Version: V1.0
// Date: 2025-05-20 17:22:51
// FilePath: /yaml_scan/pkg/fastdialer/utils/dialerwarp.go
// Description: 提供网络拨号器的包装功能，优化多IP场景下的连接性能
// 该包主要关注网络连接优化，尤其是在多IP环境中提高拨号效率和成功率。
// 核心组件是DialWrap结构体，它实现了一种智能的拨号策略，包括：
//   - 首次并行拨号（与所有IP同时尝试连接）
//   - 快乐眼球算法（优先IPv6，延迟后尝试IPv4）
//   - 连接复用和缓存机制
//   - 智能错误处理和分类
package utils

import (
	"context"
	"net"
	"sync"
	"sync/atomic"
	"time"

	"yaml_scan/utils/errkit"
	iputil "yaml_scan/utils/ip"
)

// == DialWrap =====
// DialWrap 是优化用于拨号具有多个IP的主机名的网络拨号器包装器。
// 它并行尝试第一次拨号，阻塞所有其他拨号，直到第一次拨号完成。
// 如果在第一次并行拨号期间建立了多个连接，它们会随机分配给所有暂停的拨号。
// 如果第一次拨号成功或由于临时原因（如上下文取消）而失败，则根据需要创建新连接。
// 如果是永久性错误（如端口被过滤），则不创建新连接并返回该永久性错误。
//
// 第一次拨号后，创建新连接时使用快乐眼球算法
// 快乐眼球算法 =
// IP被分为IPv4和IPv6
// 首先串行拨号IPv6，在延迟回退时间（300ms）后，并行拨号IPv4
// 无论哪个先返回，都使用它，并取消另一个
// 标准库拨号器使用相同的方法
//
// 注意：
// 我们曾尝试通过始终并行拨号来重用连接，但这导致了"使用已关闭的网络连接"错误。
// 当TCP保持活跃被禁用/不支持，或者在连接过期（保持活跃超时）时使用连接时，就会发生这种情况。
// 这就是我们回退到使用快乐眼球算法的原因。

// DialWrap 包装网络拨号器，仅拨号到给定的IP。
// 此实现保留了原始的快乐眼球算法和双栈支持。

// Error constants
var (
	// ErrInflightCancel 表示连接建立前上下文已被取消的错误
	ErrInflightCancel = errkit.New("context cancelled before establishing connection")
	// ErrNoIPs 表示在创建DialWrap时未提供有效的IP地址
	ErrNoIPs = errkit.New("no ips provided in dialWrap")
	// ExpireConnAfter 定义连接的默认过期时间
	ExpireConnAfter = time.Duration(5) * time.Second
	// ErrPortClosedOrFiltered 表示目标端口关闭或被防火墙过滤
	ErrPortClosedOrFiltered = errkit.New("port closed or filtered").SetKind(errkit.ErrKindNetworkPermanent)
)

// DialWrap 网络拨号包装结构体
type DialWrap struct {
	dialer  *net.Dialer // 基础网络拨号器
	ipv4    []net.IP    // IPv4地址列表
	ipv6    []net.IP    // IPv6地址列表
	ips     []net.IP    // 所有IP地址列表
	network string      // 网络类型（如"tcp"）
	address string      // 目标地址
	port    string      // 目标端口

	// 所有连接在第一个连接建立之前都会阻塞
	// 后续调用将根据第一个结果的行为方式
	busyFirstConnection      *atomic.Bool  // 指示第一个连接是否正在建立中
	completedFirstConnection *atomic.Bool  // 指示第一个连接是否已完成
	firstConnectionDuration  time.Duration // 第一个连接建立所需时间
	mu                       sync.RWMutex  // 保护共享状态的
	err                      error         // 第一个连接返回的错误

	firstConnCond *sync.Cond // 条件变量，用于通知第一个连接的完成
}

// dialResult 表示单个拨号操作的结果
type dialResult struct {
	net.Conn           // 嵌入的网络连接，如果拨号成功则非nil
	error              // 嵌入的错误信息，如果拨号失败则非nil
	primary  bool      // 指示这是否是主要连接（在双栈情况下通常是IPv6）
	done     bool      // 指示此拨号操作是否已完成
	expiry   time.Time // 连接的过期时间，超过此时间连接将被视为无效并重新建立
}

// NewDialWrap 创建新的拨号包装实例并返回
// @param dialer *net.Dialer:  基础网络拨号器
// @param ips []string:  IP地址列表
// @param network string:  网络类型（如"tcp"）
// @param address string: 目标地址
// @param port string: 目标端口
// @return *DialWrap *DialWrap:  新创建的拨号包装器
// @return error error: 创建过程中可能发生的错误
func NewDialWrap(dialer *net.Dialer, ips []string, network, address, port string) (*DialWrap, error) {
	var ipv4, valid, ipv6 []net.IP
	for _, ip := range ips {
		if iputil.IsIP(ip) {
			// 添加有效IP
			valid = append(valid, net.ParseIP(ip))
			// 添加IPv4地址
			if iputil.IsIPv4(ip) {
				ipv4 = append(ipv4, net.ParseIP(ip))
			} else {
				// 添加IPv6地址
				ipv6 = append(ipv6, net.ParseIP(ip))
			}
		}
	}
	// 如果没有有效IP，返回错误
	if len(valid) == 0 {
		return nil, ErrNoIPs
	}

	return &DialWrap{
		dialer:                   dialer,                      // 使用提供的拨号器
		ipv4:                     ipv4,                        // 设置IPv4地址列表
		ipv6:                     ipv6,                        // 设置IPv6地址列表
		ips:                      valid,                       // 设置所有有效IP地址列表
		completedFirstConnection: &atomic.Bool{},              // 初始化首次连接完成标志
		busyFirstConnection:      &atomic.Bool{},              // 初始化首次连接忙碌标志
		network:                  network,                     // 设置网络类型
		address:                  address,                     // 设置原始地址
		port:                     port,                        // 设置端口
		firstConnCond:            sync.NewCond(&sync.Mutex{}), // 创建条件变量，用于协调多个goroutine
	}, nil
}

// Address 返回目标的IP和端口
// 如果存在多个IP，则返回第一个
// @receiver d
// @return string string: IP地址
// @return string string: 端口
func (d *DialWrap) Address() (string, string) {
	if len(d.ips) == 0 {
		return "", ""
	}
	return d.ips[0].String(), d.port
}

// FirstConnectionTook 获取首次连接建立所需的时间
// @receiver d 
// @return time.Duration time.Duration: 首次连接建立所需的时间，如果尚未建立连接则为零值
func (d *DialWrap) FirstConnectionTook() time.Duration {
	d.mu.RLock()
	defer d.mu.RUnlock()

	// 返回存储的首次连接时间
	return d.firstConnectionDuration
}

// deadline 计算连接操作的最终截止时间
// @receiver d 
// @param ctx context.Context:  可能包含截止时间的上下文
// @param now time.Time: 当前时间，用作计算的基准点
// @return earliest time.Time: 计算出的最终截止时间，如果没有有效限制则为零值
func (d *DialWrap) deadline(ctx context.Context, now time.Time) (earliest time.Time) {
	// 检查拨号器是否设置了超时
	if d.dialer.Timeout != 0 {
		earliest = now.Add(d.dialer.Timeout)
	}
	// 检查上下文是否设置了截止时间
	if de, ok := ctx.Deadline(); ok {
		// 如果上下文有截止时间，同样加上首次连接时间进行调整
		// 使用minNonzeroTime选择最早的非零截止时间
		earliest = minNonzeroTime(earliest, de)
	}
	return earliest
}

// SetFirstConnectionDuration 设置首次连接建立所需的时间
// @receiver d 
// @param dur time.Duration: 
func (d *DialWrap) SetFirstConnectionDuration(dur time.Duration) {
	d.mu.Lock()
	defer d.mu.Unlock()

	// 设置首次连接持续时间字段
	d.firstConnectionDuration = dur
}

// dialAllParallel 并行地连接到所有给定的IP地址
// @receiver d 
// @param ctx context.Context: 控制连接过程的上下文，用于超时和取消
// @return []*dialResult []*dialResult: 成功建立的连接列表，如果没有成功则为nil
// @return error error: 如果所有连接都失败，返回合并后的错误；否则为nil
func (d *DialWrap) dialAllParallel(ctx context.Context) ([]*dialResult, error) {
	// 计算连接操作的截止时间
	deadline := d.deadline(ctx, time.Now())
	// 如果有截止时间且早于上下文中的截止时间，创建带有新截止时间的子上下文
	if !deadline.IsZero() {
		if d, ok := ctx.Deadline(); !ok || deadline.Before(d) {
			subCtx, cancel := context.WithDeadline(ctx, deadline)
			defer cancel()
			ctx = subCtx
		}
	}
	// 创建用于接收拨号结果的通道
	rec := make(chan *dialResult, len(d.ipv4)+len(d.ipv6))

	wg := &sync.WaitGroup{}
	// 启动协程处理并行拨号操作
	go func() {
		defer close(rec)
		defer wg.Wait()
		// 对每个IP地址启动一个并行拨号操作
		for _, ip := range d.ips {
			wg.Add(1)
			go func(ipx net.IP) {
				defer wg.Done()
				select {
				case <-ctx.Done():
					// 上下文已取消，发送取消错误
					rec <- &dialResult{error: errkit.Append(ErrInflightCancel, ctx.Err())}
				default:
					// 尝试建立连接
					c, err := d.dialer.DialContext(ctx, d.network, net.JoinHostPort(ipx.String(), d.port))
					// 发送结果，包括成功的连接或错误
					rec <- &dialResult{Conn: c, error: err, expiry: time.Now().Add(ExpireConnAfter)}
				}
			}(ip)
		}
	}()
	
	// 用于收集成功连接和错误的切片
	conns := []*dialResult{}
	errs := []*dialResult{}

	// 从通道接收所有拨号结果
	for result := range rec {
		if result.Conn != nil {
			conns = append(conns, result)
		} else {
			if !errkit.Is(result.error, ErrInflightCancel) {
				errs = append(errs, result)
			}
		}
	}

	if len(conns) > 0 {
		return conns, nil
	}
	// 如果没有连接也没有错误，说明所有连接都被取消
	if len(conns) == 0 && len(errs) == 0 {
		return nil, ErrInflightCancel
	}

	// 处理错误情况：合并所有错误
	var finalErr error
	for _, v := range errs {
		finalErr = errkit.Append(finalErr, v.error)
	}
	// 确定最终错误类型
	// 如果不是取消错误，则认为是永久性错误（如端口关闭或被过滤）
	if !errkit.Is(finalErr, ErrInflightCancel) {
		// 返回永久性错误，表明目标可能不可达
		return nil, errkit.Append(ErrPortClosedOrFiltered, finalErr)
	}
	return nil, finalErr
}

// doFirstConnection 执行首次连接并返回结果通道
// 这是DialWrap首次连接策略的核心实现。该方法负责初始化并执行首次并行拨号尝试，
// 并建立一个通道，通过它可以获取连接结果。这个方法有几个重要特性：
//
// 1. 单例模式：确保在任何时刻只有一个首次连接过程在运行
// 2. 非阻塞：返回一个通道，允许调用者异步处理结果
// 3. 状态跟踪：记录连接持续时间并通知等待的goroutine
// @receiver d 
// @param ctx context.Context: 控制连接操作的上下文，用于超时控制和取消
// @return chan chan: 结果通道，用于接收连接结果；如果首次连接已在进行中，返回nil
func (d *DialWrap) doFirstConnection(ctx context.Context) chan *dialResult {
	// 检查是否已经有首次连接正在进行
	// 如果是，返回nil表示不需要开始新的首次连接
	if d.busyFirstConnection.Load() {
		return nil
	}
	// 标记首次连接正在进行
	d.busyFirstConnection.Store(true)
	now := time.Now()
	// 计算需要连接的IP地址总数
	size := len(d.ipv4) + len(d.ipv6)
	ch := make(chan *dialResult, size)

	// 启动异步goroutine执行并行拨号
	go func() {
		defer close(ch)

		conns, err := d.dialAllParallel(ctx)

		// 更新连接状态
		// 获取条件变量的锁，以保护共享状态的修改
		d.firstConnCond.L.Lock()
		// 记录首次连接所需的时间
		d.SetFirstConnectionDuration(time.Since(now))
		// 标记首次连接已完成
		d.completedFirstConnection.Store(true)
		// 通知所有等待的goroutine首次连接已完成
		d.firstConnCond.Broadcast()
		d.err = err
		d.firstConnCond.L.Unlock()

		// 如果有错误，通过通道发送错误并返回
		if err != nil {
			ch <- &dialResult{error: err}
			return
		}
		for _, conn := range conns {
			ch <- conn
		}
	}()
	return ch
}

// dualStack 判断拨号器是否配置为双栈模式
// @receiver d 
// @return bool bool: 如果启用双栈功能则返回true，否则返回false
func (d *DialWrap) dualStack() bool { return d.dialer.FallbackDelay >= 0 }

// dial 执行主要的拨号功能
// 它根据网络类型和双栈设置选择合适的拨号策略。
// @receiver d 
// @param ctx context.Context: 控制拨号操作的上下文
// @return net.Conn net.Conn: 建立的网络连接
// @return error error: 连接过程中可能发生的错误
func (d *DialWrap) dial(ctx context.Context) (net.Conn, error) {
	// 计算连接超时
	deadline := d.deadline(ctx, time.Now())
	// 如果有设置截止时间且早于上下文中的截止时间，创建新的子上下文
	if !deadline.IsZero() {
		if d, ok := ctx.Deadline(); !ok || deadline.Before(d) {
			subCtx, cancel := context.WithDeadline(ctx, deadline)
			defer cancel()
			ctx = subCtx
		}
	}

	// 根据网络类型和双栈支持选择连接策略
	if d.network == "tcp" && d.dualStack() {
		return d.dialParallel(ctx, d.ipv4, d.ipv6, d.network, d.port)
	}
	// 对其他网络类型或非双栈模式，直接使用所有IP
	return d.dialParallel(ctx, d.ips, nil, d.network, d.port)
}

// dialSerial 按顺序尝试连接到地址列表中的每个地址
// @receiver d 
// @param ctx context.Context: 控制连接操作的上下文
// @param ras []net.IP: 要尝试连接的IP地址列表
// @param network string:  网络类型（如"tcp"）
// @param port string:  目标端口
// @return net.Conn net.Conn:  建立的网络连接，如果所有尝试都失败则为nil
// @return error error: 如果所有连接尝试都失败，返回第一个错误（最相关）
func (d *DialWrap) dialSerial(ctx context.Context, ras []net.IP, network, port string) (net.Conn, error) {
	// 保存第一个错误，因为它最具相关性
	var firstErr error

	// 依次尝试每个IP地址
	for _, ra := range ras {
		select {
		case <-ctx.Done():
			// 上下文已取消，返回上下文错误
			return nil, ctx.Err()
		default:
		}

		// 尝试连接到当前IP地址
		c, err := d.dialer.DialContext(ctx, network, net.JoinHostPort(ra.String(), port))
		if err == nil {
			// 连接成功，返回连接
			return c, nil
		}
		if firstErr == nil {
			firstErr = err
		}
	}

	// 如果没有任何错误（这表示IP列表为空）
	// 创建一个网络未知错误
	if firstErr == nil {
		firstErr = errkit.Wrap(net.UnknownNetworkError(network), "dialSerial")
	}
	return nil, firstErr
}

// fallbackDelay 获取双栈回退延迟时间
// 该方法返回在IPv6连接尝试失败后，等待多长时间再尝试IPv4连接。
// 这是快乐眼球算法的关键参数，控制IPv4和IPv6连接尝试之间的平衡
// @receiver d 
// @return time.Duration time.Duration: 双栈回退延迟时间
func (d *DialWrap) fallbackDelay() time.Duration {
	// 优先使用拨号器配置的回退延迟
	if d.dialer.FallbackDelay > 0 {
		return d.dialer.FallbackDelay
	} else {
		return 300 * time.Millisecond
	}
}

// dialParallel 实现"快乐眼球"算法连接策略
// 该方法运行两组并行连接尝试，但给主要组（通常是IPv6）一个先发优势。
// 这是Go标准库中"快乐眼球"算法的实现，具有以下特点：
// 1. 首先尝试主要组（如IPv6）中的地址
// 2. 经过短暂延迟后，如果主要组尚未成功，尝试备用组（如IPv4）
// 3. 返回最先成功建立的连接，关闭其他连接
// @receiver d 
// @param ctx context.Context: 控制连接操作的上下文
// @param primaries []net.IP:  主要地址组（优先尝试，通常是IPv6）
// @param fallbacks []net.IP: 备用地址组（延迟尝试，通常是IPv4）
// @param network string:  网络类型（如"tcp"）
// @param port string:  目标端口
// @return net.Conn net.Conn: 建立的网络连接
// @return error error: 连接过程中可能发生的错误
func (d *DialWrap) dialParallel(ctx context.Context, primaries, fallbacks []net.IP, network string, port string) (net.Conn, error) {
	// 如果没有备用地址，直接使用串行连接
	if len(fallbacks) == 0 {
		return d.dialSerial(ctx, primaries, network, port)
	}

	// 创建通道，用于通知函数已返回
	returned := make(chan struct{})
	defer close(returned)

	// 创建无缓冲的结果通道，确保只接收第一个结果
	results := make(chan dialResult)

	// 定义拨号竞争者函数
	// 该函数将尝试连接到一组IP地址
	startRacer := func(ctx context.Context, primary bool) {
		// 选择要使用的地址组
		ras := primaries
		if !primary {
			ras = fallbacks
		}
		// 串行尝试连接到选定的地址组
		c, err := d.dialSerial(ctx, ras, network, port)
		// 发送结果或关闭连接（如果已返回）
		select {
		case results <- dialResult{Conn: c, error: err, primary: primary, done: true}:
		case <-returned:
			if c != nil {
				c.Close()
			}
		}
	}

	var primary, fallback dialResult

	// 启动主要连接尝试
	primaryCtx, primaryCancel := context.WithCancel(ctx)
	defer primaryCancel()
	go startRacer(primaryCtx, true)

	// 启动备用连接的定时器
	fallbackTimer := time.NewTimer(d.fallbackDelay())
	defer fallbackTimer.Stop()

	// 等待结果循环
	for {
		select {
		case <-fallbackTimer.C:
			// 备用延迟时间已过，启动备用连接尝试
			fallbackCtx, fallbackCancel := context.WithCancel(ctx)
			defer fallbackCancel()
			go startRacer(fallbackCtx, false)

		case res := <-results:
			// 收到连接结果
			if res.error == nil {
				return res.Conn, nil
			}
			// 根据连接类型保存错误
			if res.primary {
				primary = res
			} else {
				fallback = res
			}
			// 如果两种连接尝试都完成且都失败，返回主要错误
			if primary.done && fallback.done {
				return nil, primary.error
			}
			// 如果主要连接失败且备用连接尚未启动，立即启动备用连接
			if res.primary && fallbackTimer.Stop() {
				// 重置定时器为0，立即触发备用连接
				fallbackTimer.Reset(0)
			}
		}
	}
}

// hasCompletedFirstConnection 检查首次连接是否已完成并等待完成
// @receiver d 
// @param ctx context.Context:  控制等待的上下文，可用于取消等待
// @return chan chan: 当首次连接完成时关闭的通道
func (d *DialWrap) hasCompletedFirstConnection(ctx context.Context) chan struct{} {
	// 创建通道，用于通知首次连接已完成
	ch := make(chan struct{}, 1)

	go func() {
		defer close(ch)

		// 立即检查首次连接是否已完成
		if d.completedFirstConnection.Load() {
			return
		}

		d.firstConnCond.L.Lock()
		defer d.firstConnCond.L.Unlock()

		// 循环等待，直到首次连接完成或上下文取消
		for !d.completedFirstConnection.Load() {
			if ctx.Err() != nil {
				return
			}
			// 等待条件变量通知
			d.firstConnCond.Wait()
		}
	}()
	return ch
}

// DialContext 实现拨号器的连接建立接口
// 该方法是DialWrap的主要入口点，实现了标准的DialContext接口。
// 它协调首次连接和后续连接，处理连接共享和错误传播。
// @receiver d 
// @param ctx context.Context: 控制连接操作的上下文
// @param _ string: 网络类型（未使用，使用初始化时提供的值）
// @param _ string: 目标地址（未使用，使用初始化时提供的值）
// @return net.Conn net.Conn: 建立的网络连接
// @return error error: 连接过程中可能发生的错误
func (d *DialWrap) DialContext(ctx context.Context, _ string, _ string) (net.Conn, error) {
	// 使用select监听多个通道，采用先到先服务原则
	select {
	case res, ok := <-d.doFirstConnection(ctx):
		// 检查是否正在执行首次连接
		if !ok {
			// 通道已关闭，表示首次连接已完成
			// 根据首次连接的结果决定后续行为
			if d.err == nil {
				// 首次连接成功，建立新连接
				return d.dial(ctx)
			}
			return nil, d.err
		}
		if res.Conn != nil {
			// 连接成功，检查是否已过期
			if res.expiry.Before(time.Now()) {
				// 连接已过期，关闭并创建新连接
				res.Conn.Close()
				return d.dial(ctx)
			}
			return res.Conn, nil
		}
		if d.err != nil {
			return nil, d.err
		}
		return nil, res.error
	case <-d.hasCompletedFirstConnection(ctx):
		// 首次连接已完成，检查结果
		// 获取首次连接的错误
		d.firstConnCond.L.Lock()
		err := d.err
		d.firstConnCond.L.Unlock()

		if err != nil && !errkit.Is(err, ErrInflightCancel) && !errkit.Is(err, context.Canceled) {
			return nil, err
		}
		return d.dial(ctx)
	case <-ctx.Done():
		return nil, errkit.Append(ErrInflightCancel, ctx.Err())
	}
}

// minNonzeroTime 返回两个时间点中较早的非零时间
// @param a time.Time:  第一个时间点
// @param b time.Time: 第二个时间点
// @return time.Time time.Time: 两个时间中较早的非零时间点
func minNonzeroTime(a, b time.Time) time.Time {
	// 如果a是零值，返回b
	if a.IsZero() {
		return b
	}
	// 如果b是零值或a早于b，返回a
	if b.IsZero() || a.Before(b) {
		return a
	}
	return b
}
