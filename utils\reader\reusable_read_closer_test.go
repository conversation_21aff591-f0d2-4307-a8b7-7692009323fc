// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 20:02:58
// FilePath: /yaml_scan/utils/reader/reusable_read_closer_test.go
// Description: 
package reader

import (
	"bytes"
	"io"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestNewReusableReadCloser(t *testing.T) {
	// 测试NewReusableReadCloser函数
	t.Run("使用[]byte创建", func(t *testing.T) {
		r := require.New(t)

		// 创建一个字节数组
		input := []byte("测试数据")

		// 使用字节数组创建ReusableReadCloser
		reusableReader, err := NewReusableReadCloser(input)

		// 验证结果
		r.NoError(err)
		r.NotNil(reusableReader)

		// 读取数据并验证
		buffer := make([]byte, 100)
		n, err := reusableReader.Read(buffer)

		r.NoError(err)
		r.Equal(len(input), n)
		r.Equal(input, buffer[:n])
	})

	t.Run("使用*[]byte创建", func(t *testing.T) {
		r := require.New(t)

		// 创建一个字节数组指针
		input := []byte("测试数据")

		// 使用字节数组指针创建ReusableReadCloser
		reusableReader, err := NewReusableReadCloser(&input)

		// 验证结果
		r.NoError(err)
		r.NotNil(reusableReader)

		// 读取数据并验证
		buffer := make([]byte, 100)
		n, err := reusableReader.Read(buffer)

		r.NoError(err)
		r.Equal(len(input), n)
		r.Equal(input, buffer[:n])
	})

	t.Run("使用string创建", func(t *testing.T) {
		r := require.New(t)

		// 创建一个字符串
		input := "测试数据"

		// 使用字符串创建ReusableReadCloser
		reusableReader, err := NewReusableReadCloser(input)

		// 验证结果
		r.NoError(err)
		r.NotNil(reusableReader)

		// 读取数据并验证
		buffer := make([]byte, 100)
		n, err := reusableReader.Read(buffer)

		r.NoError(err)
		r.Equal(len([]byte(input)), n)
		r.Equal([]byte(input), buffer[:n])
	})

	t.Run("使用*bytes.Buffer创建", func(t *testing.T) {
		r := require.New(t)

		// 创建一个字节缓冲区
		input := bytes.NewBufferString("测试数据")

		// 使用字节缓冲区创建ReusableReadCloser
		reusableReader, err := NewReusableReadCloser(input)

		// 验证结果
		r.NoError(err)
		r.NotNil(reusableReader)

		// 读取数据并验证
		buffer := make([]byte, 100)
		n, err := reusableReader.Read(buffer)

		r.NoError(err)
		r.Equal(len([]byte("测试数据")), n)
		r.Equal([]byte("测试数据"), buffer[:n])
	})

	t.Run("使用*bytes.Reader创建", func(t *testing.T) {
		r := require.New(t)

		// 创建一个字节读取器
		input := bytes.NewReader([]byte("测试数据"))

		// 使用字节读取器创建ReusableReadCloser
		reusableReader, err := NewReusableReadCloser(input)

		// 验证结果
		r.NoError(err)
		r.NotNil(reusableReader)

		// 读取数据并验证
		buffer := make([]byte, 100)
		n, err := reusableReader.Read(buffer)

		r.NoError(err)
		r.Equal(len([]byte("测试数据")), n)
		r.Equal([]byte("测试数据"), buffer[:n])
	})

	t.Run("使用*strings.Reader创建", func(t *testing.T) {
		r := require.New(t)

		// 创建一个字符串读取器
		input := strings.NewReader("测试数据")

		// 使用字符串读取器创建ReusableReadCloser
		reusableReader, err := NewReusableReadCloser(input)

		// 验证结果
		r.NoError(err)
		r.NotNil(reusableReader)

		// 读取数据并验证
		buffer := make([]byte, 100)
		n, err := reusableReader.Read(buffer)

		r.NoError(err)
		r.Equal(len([]byte("测试数据")), n)
		r.Equal([]byte("测试数据"), buffer[:n])
	})

	t.Run("使用不支持的类型创建", func(t *testing.T) {
		r := require.New(t)

		// 使用不支持的类型(整数)创建ReusableReadCloser
		_, err := NewReusableReadCloser(123)

		// 验证结果
		r.Error(err)
		r.Contains(err.Error(), "cannot handle type")
	})
}

func TestReusableReadCloser_Read(t *testing.T) {
	// 测试ReusableReadCloser的Read方法
	t.Run("多次读取相同内容", func(t *testing.T) {
		r := require.New(t)

		// 创建一个ReusableReadCloser
		input := "测试可重用读取器"
		reusableReader, err := NewReusableReadCloser(input)
		r.NoError(err)

		// 准备缓冲区
		buffer := make([]byte, 100)

		// 第一次读取
		n1, err1 := reusableReader.Read(buffer)
		r.NoError(err1)
		r.Equal([]byte(input), buffer[:n1])

		// 检查是否到达EOF
		n2, err2 := reusableReader.Read(buffer)
		r.Equal(0, n2)
		r.Equal(io.EOF, err2)

		// EOF后应该重置并允许再次读取
		n3, err3 := reusableReader.Read(buffer)
		r.NoError(err3)
		r.Equal([]byte(input), buffer[:n3])
	})
}

func TestReusableReadCloser_Close(t *testing.T) {
	// 测试ReusableReadCloser的Close方法
	t.Run("关闭操作是空操作", func(t *testing.T) {
		r := require.New(t)

		// 创建一个ReusableReadCloser
		reusableReader, err := NewReusableReadCloser("测试数据")
		r.NoError(err)

		// 关闭读取器
		err = reusableReader.Close()

		// 验证关闭不会返回错误
		r.NoError(err)

		// 验证关闭后仍然可以读取数据
		buffer := make([]byte, 100)
		n, err := reusableReader.Read(buffer)

		r.NoError(err)
		r.Equal([]byte("测试数据"), buffer[:n])
	})
}


