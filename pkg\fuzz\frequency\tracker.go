// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-17 16:14:53
// FilePath: /yaml_scan/pkg/fuzz/frequency/tracker.go
// Description: 
package frequency

const (
	DefaultMaxTrackCount           = 10000
	DefaultParamOccurenceThreshold = 10
)

// Tracker implements a frequency tracker for a given input
// which is used to determine uninteresting input parameters
// which are not that interesting from fuzzing perspective for a template
// and target combination.
//
// This is used to reduce the number of requests made during fuzzing
// for parameters that are less likely to give results for a rule.
type Tracker struct {
	frequencies             gcache.Cache
	paramOccurenceThreshold int

	isDebug bool
}

// New creates a new frequency tracker with a given maximum
// number of params to track in LRU fashion with a max error threshold
func New(maxTrackCount, paramOccurenceThreshold int) *Tracker {
	gc := gcache.New(maxTrackCount).ARC().Build()

	var isDebug bool
	if os.Getenv("FREQ_DEBUG") != "" {
		isDebug = true
	}
	return &Tracker{
		isDebug:                 isDebug,
		frequencies:             gc,
		paramOccurenceThreshold: paramOccurenceThreshold,
	}
}