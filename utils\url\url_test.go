//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-12 20:21:52
//FilePath: /yaml_scan/utils/url/url_test.go
//Description: url单测

package urlutil

import (
	"net/url"
	"testing"
)

func TestURLUpdate(t *testing.T) {
	u := &URL{
		URL:      &url.URL{},
		Params:   NewOrderedParams(),
		Original: "http://example.com",
	}

	// 添加查询参数
	u.Params.Add("foo", "bar")
	u.Update()

	// 验证 RawQuery 是否正确
	expected := "foo=bar"
	if u.RawQuery != expected {
		t.<PERSON><PERSON>("Expected RawQuery %q, got %q", expected, u.RawQuery)
	}
}

func TestURLfetchParams(t *testing.T) {
	rawURL := "http://example.com?foo=bar&baz=qux#fragment"
	u := &URL{
		URL:      &url.URL{},
		Original: rawURL,
		Params:   NewOrderedParams(),
	}

	u.fetchParams()

	// 验证 URL 的各个部分
	if u.Params.om.Len() != 2 {
		t.<PERSON><PERSON><PERSON>("Expected 2 query parameters, got %d", u.Params.om.Len())
	}
	if values, ok := u.Params.om.Get("foo"); !ok || values[0] != "bar" {
		t.Errorf("Expected foo to be bar, got %v", values)
	}
	if values, ok := u.Params.om.Get("baz"); !ok || values[0] != "qux" {
		t.Errorf("Expected baz to be qux, got %v", values)
	}
	if u.URL.Fragment != "fragment" {
		t.Errorf("Expected fragment to be 'fragment', got %q", u.URL.Fragment)
	}
}

func TestShouldEscape(t *testing.T) {
	tests := []struct {
		input    string
		expected bool
	}{
		{"hello world", false}, // 不需要转义
		{"hello/world", false}, // 不需要转义
		{"hello&world", true},  // 需要转义
		{"hello%world", true},  // 需要转义
		{"hello😊world", true},  // 需要转义（非 ASCII 字符）
		{"hello!world", true},  // 需要转义（包含 !）
		{"hello*world", true},  // 需要转义（包含 *）
		{"hello(world)", true}, // 需要转义（包含 ( 和 )）
		{"hello;world", true},  // 需要转义（包含 ;）
		{"hello:world", true},  // 需要转义（包含 :）
		{"hello@world", true},  // 需要转义（包含 @）
		{"hello&world", true},  // 需要转义（包含 &）
		{"hello=world", true},  // 需要转义（包含 =）
		{"hello+world", true},  // 需要转义（包含 +）
		{"hello$world", true},  // 需要转义（包含 $）
		{"hello,world", true},  // 需要转义（包含 ,）
		{"hello?world", true},  // 需要转义（包含 ?）
		{"hello#world", true},  // 需要转义（包含 #）
		{"hello[world]", true}, // 需要转义（包含 [ 和 ]）
		{"hello world", false}, // 不需要转义（空格）
		{"", false},            // 空字符串不需要转义
	}

	for _, test := range tests {
		result := shouldEscape(test.input)
		if result != test.expected {
			t.Errorf("shouldEscape(%q) = %v; expected %v", test.input, result, test.expected)
		}
	}
}

// 测试 parseUnsafeRelativePath 方法
func TestParseUnsafeRelativePath(t *testing.T) {
	tests := []struct {
		input        URL
		expectedPath string
	}{
		{
			input: URL{
				Original: "http://example.com/path/to/resource",
				URL: &url.URL{
					Host: "example.com",
					Path: "/path/to/resource",
				},
			},
			expectedPath: "/path/to/resource",
		},
		{
			input: URL{
				Original: "/test/path",
				URL: &url.URL{
					Host: "",
					Path: "/test/path",
				},
			},
			expectedPath: "/test/path", // Host 为空，Path 应该保持不变
		},
		{
			input: URL{
				Original: "/%20test%0a",
				URL: &url.URL{
					Host: "example.com",
					Path: "/%20test%0a",
				},
			},
			expectedPath: "/%20test%0a", // 保持原样，因为有编码字符
		},
		{
			input: URL{
				Original: "test/path",
				URL: &url.URL{
					Host: "example.com",
					Path: "test/path",
				},
			},
			expectedPath: "/test/path", // 自动添加前缀 /
		},
		{
			input: URL{
				Original: "test/path",
				URL: &url.URL{
					Host: "example.com",
					Path: "test/path",
				},
				disableAutoCorrect: true,
			},
			expectedPath: "test/path", // 不自动添加前缀 /
		},
		{
			input: URL{
				Original: "http://example.com/test/path",
				URL: &url.URL{
					Host: "example.com",
					Path: "http://example.com/test/path",
				},
			},
			expectedPath: "/test/path", // 正确解析相对路径
		},
	}

	for _, test := range tests {
		u := &test.input
		u.parseUnsafeRelativePath()
		if u.Path != test.expectedPath {
			t.Errorf("parseUnsafeRelativePath() = %q; expected %q", u.Path, test.expectedPath)
		}
	}
}

// TestCopy 测试 copy 函数的功能
func TestCopy(t *testing.T) {
	// 创建源 URL，初始化其各个字段
	src := &url.URL{
		Scheme:  "http",               // 设置协议为 http
		Host:    "example.com",        // 设置主机为 example.com
		Path:    "/path/to/resource",  // 设置路径为 /path/to/resource
		RawPath: "/path/to/resource",  // 设置原始路径为 /path/to/resource
		Opaque:  "opaque",             // 设置 Opaque 字段
		User:    url.User("username"), // 设置用户信息
	}

	// 创建一个空的目标 URL
	dst := &url.URL{}

	// 调用 copy 函数，将 src 的数据复制到 dst
	copy(dst, src)

	// 验证目标 URL 的各个字段是否正确复制
	if dst.Scheme != src.Scheme {
		t.Errorf("expected dst.Scheme to be %q, got %q", src.Scheme, dst.Scheme)
	}
	if dst.Host != src.Host {
		t.Errorf("expected dst.Host to be %q, got %q", src.Host, dst.Host)
	}
	if dst.Path != src.Path {
		t.Errorf("expected dst.Path to be %q, got %q", src.Path, dst.Path)
	}
	if dst.RawPath != src.RawPath {
		t.Errorf("expected dst.RawPath to be %q, got %q", src.RawPath, dst.RawPath)
	}
	if dst.Opaque != src.Opaque {
		t.Errorf("expected dst.Opaque to be %q, got %q", src.Opaque, dst.Opaque)
	}
	if dst.User.String() != src.User.String() {
		t.Errorf("expected dst.User to be %q, got %q", src.User.String(), dst.User.String())
	}
}
