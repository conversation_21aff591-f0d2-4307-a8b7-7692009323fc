// Author: chenjb
// Version: V1.0
// Date: 2025-05-20 16:17:15
// FilePath: /yaml_scan/pkg/fastdialer/dialer.go
// Description: 提供快速、可定制的网络拨号器实现，支持DNS缓存、TLS连接和各种网络策略
package fastdialer

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"net"
	"strings"
	"sync/atomic"
	"time"

	"golang.org/x/sync/singleflight"

	"yaml_scan/pkg/fastdialer/ja3"
	"yaml_scan/pkg/fastdialer/metafiles"
	"yaml_scan/pkg/fastdialer/utils"
	"yaml_scan/pkg/gcache"
	"yaml_scan/pkg/hybridMap/hybrid"
	"yaml_scan/pkg/networkpolicy"
	"yaml_scan/pkg/retryabledns"
	cryptoutil "yaml_scan/utils/crypto"
	"yaml_scan/utils/errkit"

	"github.com/pkg/errors"
	"github.com/zmap/zcrypto/encoding/asn1"
	ztls "github.com/zmap/zcrypto/tls"
	"golang.org/x/net/proxy"
)

var (
	// 控制是否禁用ZTLS降级功能
	// 当TLS握手失败时，可选择降级到ZTLS
	disableZTLSFallback = false

	// MaxDNSCacheSize DNS缓存的最大大小（10MB）
	MaxDNSCacheSize = 1024 * 1024 * 10

	// MaxDNSItems DNS缓存的最大项目数
	MaxDNSItems = 1024

	// MaxDialCacheSize 拨号缓存的最大项目数
	MaxDialCacheSize = 10000

	//  dns查询超时时间
	dnsTimeOut = 10 * time.Second
)

func init() {
	// 启用ztls的宽松解析模式
	// 允许对X509证书进行宽松解析，提高兼容性
	asn1.AllowPermissiveParsing = true
}

// Dialer 网络拨号器结构体
type Dialer struct {
	options           *Options                                    // 拨号器配置选项
	dnsclient         *retryabledns.Client                        // DNS解析客户端
	mDnsCache         gcache.Cache[string, *retryabledns.DNSData] // 内存类型缓存，用于存储DNS解析结果
	hmDnsCache        *hybrid.HybridMap                           // 混合DNS缓存
	hostsFileData     *hybrid.HybridMap                           // hosts文件数据
	dialerHistory     *hybrid.HybridMap                           // 拨号历史记录
	dialerTLSData     *hybrid.HybridMap                           // TLS连接数据
	dialer            *net.Dialer                                 // 标准网络拨号器
	proxyDialer       *proxy.Dialer                               // 代理拨号器
	networkpolicy     *networkpolicy.NetworkPolicy                // 网络策略
	dialCache         gcache.Cache[string, *utils.DialWrap]       // 拨号结果缓存
	dialTimeoutErrors gcache.Cache[string, *atomic.Uint32]        // 超时错误计数缓存

	resolutionsGroup *singleflight.Group // 解析组，避免重复DNS解析
}

// getHMAPDBType 根据选项返回混合映射数据库类型
// @param options Options:  拨号器配置选项
// @return hybrid.DBType hybrid.DBType: 数据库类型（LevelDB或Pogreb）
func getHMAPDBType(options Options) hybrid.DBType {
	switch options.DiskDbType {
	case Pogreb:
		return hybrid.PogrebDB
	default:
		return hybrid.LevelDB
	}
}

// NewDialer 创建新的拨号器实例
// @param options Options:  拨号器配置选项
// @return *Dialer *Dialer: 新创建的拨号器实例
// @return error error: 创建过程中可能发生的错误
func NewDialer(options Options) (*Dialer, error) {
	var resolvers []string
	// 首先添加系统解析器，优先尝试使用
	if options.ResolversFile {
		systemResolvers, err := loadResolverFile()
		if err == nil && len(systemResolvers) > 0 {
			resolvers = systemResolvers
		}
	}
	// 追加基础解析器列表
	resolvers = append(resolvers, options.BaseResolvers...)
	var err error
	var dialerHistory *hybrid.HybridMap
	// 初始化拨号历史记录（如果启用）
	if options.WithDialerHistory {
		// 使用磁盘存储所有拨号过的IP
		dialerHistoryCacheOptions := hybrid.DefaultDiskOptions
		dialerHistoryCacheOptions.DBType = getHMAPDBType(options)
		dialerHistory, err = hybrid.New(dialerHistoryCacheOptions)
		if err != nil {
			return nil, err
		}
	}
	// 初始化DNS缓存
	var (
		hmDnsCache *hybrid.HybridMap
		dnsCache   gcache.Cache[string, *retryabledns.DNSData]
	)
	// 使用内存缓存
	if options.CacheType == Memory {
		dnsCache = gcache.New[string, *retryabledns.DNSData](MaxDNSItems).Build()
	} else {
		// 使用混合缓存
		hmDnsCache, err = hybrid.New(hybrid.DefaultHybridOptions)
		if err != nil {
			return nil, err
		}
	}

	// 初始化TLS数据存储（如果启用）
	var dialerTLSData *hybrid.HybridMap
	if options.WithTLSData {
		dialerTLSData, err = hybrid.New(hybrid.DefaultDiskOptions)
		if err != nil {
			return nil, err
		}
	}

	// 初始化标准拨号器
	var dialer *net.Dialer
	if options.Dialer != nil {
		// 使用提供的自定义拨号器
		dialer = options.Dialer
	} else {
		// 创建默认拨号器
		dialer = &net.Dialer{
			Timeout:   options.DialerTimeout,   // 设置拨号超时
			KeepAlive: options.DialerKeepAlive, // 设置保持活跃时间
			DualStack: true,                    // 启用双栈（IPv4和IPv6）
		}
	}

	// 加载hosts文件数据
	var hostsFileData *hybrid.HybridMap
	if options.HostsFile {
		var err error
		if options.CacheType == Memory {
			// 使用内存模式加载hosts文件
			hostsFileData, err = metafiles.GetHostsFileDnsData(metafiles.InMemory)
		} else {
			// 使用混合模式加载hosts文件
			hostsFileData, err = metafiles.GetHostsFileDnsData(metafiles.Hybrid)
		}
		if options.Logger != nil && err != nil {
			options.Logger.Printf("could not load hosts file: %s\n", err)
		}
	}

	// 创建可重试DNS客户端
	dnsclient, err := retryabledns.NewWithOptions(retryabledns.Options{
		BaseResolvers: resolvers,          // 设置解析服务器列表
		MaxRetries:    options.MaxRetries, // 设置最大重试次数
		Timeout:       dnsTimeOut,         // 设置超时时间
	})
	if err != nil {
		return nil, err
	}

	// 初始化网络策略
	var np *networkpolicy.NetworkPolicy
	if options.NetworkPolicy != nil {
		np = options.NetworkPolicy
	} else {
		// 创建新的网络策略
		np, err = createNetworkPolicy(options)
		if err != nil {
			return nil, errors.Wrap(err, "could not create network policy")
		}
	}

	// 创建并返回完整的拨号器实例
	d := &Dialer{
		dnsclient:        dnsclient,                                                     // DNS客户端
		mDnsCache:        dnsCache,                                                      // 内存DNS缓存
		hmDnsCache:       hmDnsCache,                                                    // 混合DNS缓存
		hostsFileData:    hostsFileData,                                                 // hosts文件数据
		dialerHistory:    dialerHistory,                                                 // 拨号历史记录
		dialerTLSData:    dialerTLSData,                                                 // TLS数据存储
		dialer:           dialer,                                                        // 标准拨号器
		proxyDialer:      options.ProxyDialer,                                           // 代理拨号器
		options:          &options,                                                      // 配置选项
		networkpolicy:    np,                                                            // 网络策略
		dialCache:        gcache.New[string, *utils.DialWrap](MaxDialCacheSize).Build(), // 拨号缓存
		resolutionsGroup: &singleflight.Group{},                                         // 解析请求去重组
	}

	// 如果配置了临时错误转永久错误的参数，初始化对应的缓存
	if options.MaxTemporaryErrors > 0 && options.MaxTemporaryToPermanentDuration > 0 {
		d.dialTimeoutErrors = gcache.New[string, *atomic.Uint32](MaxDialCacheSize).Expiration(options.MaxTemporaryToPermanentDuration).Build()
	}

	return d, nil
}

// createNetworkPolicy 根据配置选项创建网络策略
// @param options Options: 拨号器配置选项
// @return *networkpolicy.NetworkPolicy *networkpolicy.NetworkPolicy: 创建的网络策略
// @return error error: 建过程中可能发生的错误
func createNetworkPolicy(options Options) (*networkpolicy.NetworkPolicy, error) {
	var npOptions networkpolicy.Options
	// 使用提供的网络策略选项
	if options.WithNetworkPolicyOptions != nil {
		npOptions = *options.WithNetworkPolicyOptions
	}

	// 填充黑名单（拒绝列表）
	npOptions.DenyList = append(npOptions.DenyList, options.Deny...)
	// 填充白名单（允许列表）
	npOptions.AllowList = append(npOptions.AllowList, options.Allow...)

	// 添加端口限制
	npOptions.AllowPortList = append(npOptions.AllowPortList, options.AllowPortList...)
	npOptions.DenyPortList = append(npOptions.DenyPortList, options.DenyPortList...)

	// 添加协议方案限制
	npOptions.AllowSchemeList = append(npOptions.AllowSchemeList, options.AllowSchemeList...)
	npOptions.DenySchemeList = append(npOptions.DenySchemeList, options.DenySchemeList...)

	// 创建网络策略
	np, err := networkpolicy.New(npOptions)
	return np, err
}

// Dial 创建到指定地址的网络连接
// @receiver d
// @param ctx context.Context: 上下文，用于超时和取消控制
// @param network string:  网络类型，如"tcp"
// @param address string: 目标地址，格式为"host:port"
// @return conn net.Conn: 建立的网络连接
// @return err error: 连接过程中可能发生的错误
func (d *Dialer) Dial(ctx context.Context, network, address string) (conn net.Conn, err error) {
	return d.dial(ctx, &dialOptions{
		network:             network,
		address:             address,
		shouldUseTLS:        false,    // 不使用TLS
		shouldUseZTLS:       false,    // 不使用ZTLS
		tlsconfig:           nil,      // 无TLS配置
		ztlsconfig:          nil,      // 无ZTLS配置
		impersonateStrategy: ja3.None, // 不使用JA3指纹模拟
		impersonateIdentity: nil,      // 无JA3身份
	})
}

// DialTLS 创建加密的TLS连接
// @receiver d
// @param ctx context.Context: 上下文，用于超时和取消控制
// @param network string: 网络类型，如"tcp"
// @param address string: : 目标地址，格式为"host:port"
// @return conn net.Conn: 建立的TLS连接
// @return err error: 连接过程中可能发生的错误
func (d *Dialer) DialTLS(ctx context.Context, network, address string) (conn net.Conn, err error) {
	// 如果启用了ZTLS，使用ZTLS连接
	if d.options.WithZTLS {
		return d.DialZTLSWithConfig(ctx, network, address, DefaultZTLSConfig)
	}
	return d.DialTLSWithConfig(ctx, network, address, DefaultTLSConfig)
}

// DialZTLS 使用ZTLS库创建加密连接
// @receiver d
// @param ctx context.Context: 上下文，用于超时和取消控制
// @param network string: 网络类型，如"tcp"
// @param address string: 目标地址，格式为"host:port"
// @return conn net.Conn: 建立的ZTLS连接
// @return err error: 连接过程中可能发生的错误
func (d *Dialer) DialZTLS(ctx context.Context, network, address string) (conn net.Conn, err error) {
	return d.DialZTLSWithConfig(ctx, network, address, DefaultZTLSConfig)
}

// DialTLSWithConfig 使用指定配置创建TLS连接
// @receiver d
// @param ctx context.Context: 上下文，用于超时和取消控制
// @param network string: 网络类型，如"tcp"
// @param address string: 目标地址，格式为"host:port"
// @param config *tls.Config: TLS配置
// @return conn net.Conn: 建立的TLS连接
// @return err error:
func (d *Dialer) DialTLSWithConfig(ctx context.Context, network, address string, config *tls.Config) (conn net.Conn, err error) {
	return d.dial(ctx, &dialOptions{
		network:             network,
		address:             address,
		shouldUseTLS:        true,
		shouldUseZTLS:       false,
		tlsconfig:           config,
		ztlsconfig:          nil,
		impersonateStrategy: ja3.None,
		impersonateIdentity: nil,
	})
}

// DialZTLSWithConfig 使用指定配置创建ZTLS连接
// @receiver d
// @param ctx context.Context: 上下文，用于超时和取消控制
// @param network string: 网络类型，如"tcp"
// @param address string: 目标地址，格式为"host:port"
// @param config *ztls.Config: ZTLS配置
// @return conn net.Conn: 建立的ZTLS连接
// @return err error: 连接过程中可能发生的错误
func (d *Dialer) DialZTLSWithConfig(ctx context.Context, network, address string, config *ztls.Config) (conn net.Conn, err error) {
	// ZTLS不支持TLS 1.3
	if IsTLS13(config) {
		// 如果配置为TLS 1.3，转换为标准TLS配置
		stdTLSConfig, err := AsTLSConfig(config)
		if err != nil {
			return nil, errkit.Wrap(err, "could not convert ztls config to tls config")
		}
		return d.dial(ctx, &dialOptions{
			network:             network,
			address:             address,
			shouldUseTLS:        true,         // 使用标准TLS
			shouldUseZTLS:       false,        // 不使用ZTLS
			tlsconfig:           stdTLSConfig, // 使用转换后的TLS配置
			ztlsconfig:          nil,          // 无ZTLS配置
			impersonateStrategy: ja3.None,     // 不使用JA3指纹模拟
			impersonateIdentity: nil,          // 无JA3身份
		})
	}
	return d.dial(ctx, &dialOptions{
		network:             network,
		address:             address,
		shouldUseTLS:        false,    // 不使用标准TLS
		shouldUseZTLS:       true,     // 使用ZTLS
		tlsconfig:           nil,      // 无标准TLS配置
		ztlsconfig:          config,   // 使用提供的ZTLS配置
		impersonateStrategy: ja3.None, // 不使用JA3指纹模拟
		impersonateIdentity: nil,      // 无JA3身份
	})
}

// DialTLSWithConfigImpersonate  创建带有JA3指纹模拟功能的TLS连接
// @receiver d
// @param ctx context.Context: 上下文，用于超时和取消控制
// @param network string:  网络类型，如"tcp"
// @param address string: 目标地址，格式为"host:port"
// @param config *tls.Config: TLS配置
// @param impersonate ja3.Strategy: JA3指纹模拟策略
// @param identity *ja3.Identity: 要模拟的JA3客户端身份
// @return conn net.Conn: 建立的TLS连接
// @return err error: 连接过程中可能发生的错误
func (d *Dialer) DialTLSWithConfigImpersonate(ctx context.Context, network, address string, config *tls.Config, impersonate ja3.Strategy, identity *ja3.Identity) (conn net.Conn, err error) {
	return d.dial(ctx, &dialOptions{
		network:             network,
		address:             address,
		shouldUseTLS:        true,        // 使用TLS
		shouldUseZTLS:       false,       // 不使用ZTLS
		tlsconfig:           config,      // 使用提供的TLS配置
		ztlsconfig:          nil,         // 无ZTLS配置
		impersonateStrategy: impersonate, // 使用提供的JA3指纹模拟策略
		impersonateIdentity: identity,    // 使用提供的JA3身份
	})
}

// Close 关闭拨号器实例并清理资源
// @receiver d
func (d *Dialer) Close() {
	// 清理内存DNS缓存
	if d.mDnsCache != nil {
		d.mDnsCache.Purge()
	}
	// 关闭混合DNS缓存
	if d.hmDnsCache != nil {
		d.hmDnsCache.Close()
	}
	// 关闭拨号历史记录
	if d.options.WithDialerHistory && d.dialerHistory != nil {
		d.dialerHistory.Close()
	}
	// 关闭TLS数据存储
	if d.options.WithTLSData {
		d.dialerTLSData.Close()
	}
	// 清理拨号缓存
	if d.dialCache != nil {
		d.dialCache.Purge()
	}
	// 清理超时错误计数缓存
	if d.dialTimeoutErrors != nil {
		d.dialTimeoutErrors.Purge()
	}
	// 不关闭hosts文件数据，因为它可能被共享使用
}

// GetDialedIP 返回HTTP客户端拨号的IP地址
// @receiver d
// @param hostname string: 主机名
// @return string string: 拨号使用的IP地址，如果未启用历史记录或未找到记录则返回空字符串
func (d *Dialer) GetDialedIP(hostname string) string {
	// 如果没有启用拨号历史记录，则返回空
	if !d.options.WithDialerHistory || d.dialerHistory == nil {
		return ""
	}
	// 将主机名转换为ASCII编码
	hostname = asAscii(hostname)
	// 从历史记录中查找
	v, ok := d.dialerHistory.Get(hostname)
	if ok {
		return string(v)
	}

	return ""
}

// GetTLSData 获取主机名对应的TLS连接数据
// @receiver d
// @param hostname string: 主机名
// @return *cryptoutil.TLSData *cryptoutil.TLSData: TLS数据结构
// @return error error: 可能的错误，如未启用TLS数据收集或未找到记录
func (d *Dialer) GetTLSData(hostname string) (*cryptoutil.TLSData, error) {
	// 将主机名转换为ASCII编码
	hostname = asAscii(hostname)
	// 检查是否启用了TLS数据收集
	if !d.options.WithTLSData {
		return nil, NoTLSHistoryError
	}
	v, ok := d.dialerTLSData.Get(hostname)
	if !ok {
		return nil, NoTLSDataError
	}

	// 将二进制数据解码为TLS数据结构
	var tlsData cryptoutil.TLSData
	err := json.NewDecoder(bytes.NewReader(v)).Decode(&tlsData)
	if err != nil {
		return nil, err
	}

	return &tlsData, nil
}

// GetDNSDataFromCache 从缓存中获取DNS解析数据
// @receiver d
// @param hostname string:  要查询的主机名
// @return *retryabledns.DNSData *retryabledns.DNSData:  DNS数据结构
// @return error error: 获取过程中可能发生的错误，如未找到记录
func (d *Dialer) GetDNSDataFromCache(hostname string) (*retryabledns.DNSData, error) {
	// 将主机名转换为ASCII编码
	hostname = asAscii(hostname)
	var data retryabledns.DNSData
	var dataBytes []byte
	var ok bool
	// 首先尝试从hosts文件获取DNS数据
	if d.hostsFileData != nil {
		dataBytes, ok = d.hostsFileData.Get(hostname)
	}
	if !ok {
		// 从hosts文件中未找到，尝试从内存缓存获取
		if d.mDnsCache != nil {
			return d.mDnsCache.GetIFPresent(hostname)
		}

		// 从混合缓存获取
		dataBytes, ok = d.hmDnsCache.Get(hostname)
		if !ok {
			return nil, NoDNSDataError
		}
	}
	// 解析二进制数据为DNS数据结构
	err := data.Unmarshal(dataBytes)
	return &data, err
}

// GetDNSData 获取指定主机名的DNS解析数据
// 先尝试从缓存获取，如果没有则进行实时解析
// @receiver d
// @param hostname string: 要解析的主机名
// @return *retryabledns.DNSData *retryabledns.DNSData: 包含A和AAAA记录的DNS数据结构
// @return error error:
func (d *Dialer) GetDNSData(hostname string) (*retryabledns.DNSData, error) {
	// 将主机名转换为ASCII编码 幂等的
	hostname = asAscii(hostname)
	// 支持 http://[::1] 和 http://[::1]:8080 这样的IPv6格式
	// 参考 https://datatracker.ietf.org/doc/html/rfc2732
	// 这定义了IPv6地址语法，并允许在URI中明确使用"["和"]"
	if strings.HasPrefix(hostname, "[") && strings.HasSuffix(hostname, "]") {
		ipv6host := hostname[1:strings.LastIndex(hostname, "]")]
		if ip := net.ParseIP(ipv6host); ip != nil {
			if ip.To16() != nil {
				// 返回IPv6地址记录
				return &retryabledns.DNSData{AAAA: []string{ip.To16().String()}}, nil
			}
		}
	}
	
	// 检查是否是IP地址
	if ip := net.ParseIP(hostname); ip != nil {
		if ip.To4() != nil {
			// 返回IPv4地址记录
			return &retryabledns.DNSData{A: []string{hostname}}, nil
		}
		if ip.To16() != nil {
			// 返回IPv6地址记录
			return &retryabledns.DNSData{AAAA: []string{hostname}}, nil
		}
	}
	var (
		data *retryabledns.DNSData
		err  error
	)
	// 尝试从缓存获取DNS数据
	data, err = d.GetDNSDataFromCache(hostname)
	if err != nil {
		// 缓存中未找到，进行实时解析
		data, err = d.dnsclient.Resolve(hostname)
		if err != nil && d.options.EnableFallback {
			// 如果解析失败且启用了备用方案，使用系统调用解析
			data, err = d.dnsclient.ResolveWithSyscall(hostname)
		}
		if err != nil {
			return nil, err
		}
		if data == nil {
			return nil, ResolveHostError
		}
		// 如果解析出了IP地址，将结果存入缓存
		if len(data.A)+len(data.AAAA) > 0 {
			if d.mDnsCache != nil {
				// 存入内存缓存
				err := d.mDnsCache.Set(hostname, data)
				if err != nil {
					return nil, err
				}
			}

			// 存入混合缓存
			if d.hmDnsCache != nil {
				b, errX := data.Marshal()
				if errX != nil {
					return nil, errX
				}
				err := d.hmDnsCache.Set(hostname, b)
				if err != nil {
					return nil, err
				}
			}

		}
		return data, nil
	}
	
	return data, nil
}
