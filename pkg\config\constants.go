//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-19 14:49:24
//FilePath: /yaml_scan/pkg/config/constants.go
//Description: 常量定义

package config

import "time"

type AppMode string

const (
	AppModeLibrary AppMode = "library"
	AppModeCLI     AppMode = "cli"
	// 签名模板的证书过期时间默认4年
	SignTemplateCertExpire = 4 * 365 * 24 * time.Hour
)

var (
	// Global Var to control behaviours specific to cli or library
	// maybe this should be moved to utils ??
	// this is overwritten in cmd/nuclei/main.go
	CurrentAppMode = AppModeLibrary
	// 配置文件目录
	ConfigDirEnv = "CONFIG_DIR"
)
