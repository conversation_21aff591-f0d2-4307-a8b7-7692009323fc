package goflags


import (
	"testing"
	"os"
)


func TestIsEmpty(t *testing.T) {
	tests := []struct {
		input    string
		expected bool
	}{
		{"", true},              // 空字符串
		{"   ", true},          // 仅包含空格
		{"\t\n", true},         // 仅包含制表符和换行符
		{"Hello", false},       // 包含非空白字符
		{" Hello ", false},     // 包含非空白字符
		{"\nHello\n", false},   // 包含非空白字符
		{"\tHello\t", false},   // 包含非空白字符
	}

	for _, test := range tests {
		result := isEmpty(test.input)
		if result != test.expected {
			t.Errorf("isEmpty(%q) = %v; want %v", test.input, result, test.expected)
		}
	}
}


// TestIsQuote 测试 isQuote 函数
func TestIsQuote(t *testing.T) {
	tests := []struct {
		input    rune
		expected bool
		quote    rune
	}{
		{'"', true, '"'},
		{'\'', true, '\''},
		{'`', true, '`'},
		{'a', false, 0},
		{' ', false, 0},
		{'#', false, 0},
	}

	for _, test := range tests {
		result, quote := isQuote(test.input)
		if result != test.expected || quote != test.quote {
			t.Errorf("isQuote(%q) = (%v, %q), expected (%v, %q)", test.input, result, quote, test.expected, test.quote)
		}
	}
}


// TestSearchPart 测试 searchPart 函数
func TestSearchPart(t *testing.T) {
	tests := []struct {
		value    string
		stop     rune
		expected bool
		result   string
	}{
		{"Hello, World!", ',', true, "Hello"},
		{"Hello, World!", '!', true, "Hello, World"},
		{"Hello, World!", 'x', false, "Hello, World!"},
		{"", ',', false, ""},
		{"No stop character here", '!', false, "No stop character here"},
		{"Just a test", ' ', true, "Just"},
		{"Leading space ", ' ', true, "Leading"},
		{"Trailing space", ' ', true, "Trailing"},
	}

	for _, tt := range tests {
		result, found := searchPart(tt.value, tt.stop)
		if found != tt.result || result != tt.expected {
			t.Errorf("searchPart(%q, %q) = (%v, %v), expected (%v, %v)", tt.value, tt.stop, found, result, tt.expected, tt.result)
		}
	}
}

func TestNormalizeLowercase(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"", ""},                          // 空字符串
		{"   ", ""},                       // 仅包含空格
		{"  Hello World  ", "hello world"}, // 前后有空格
		{"'Hello'", "hello"},              // 包含单引号
		{`"Hello"`, "hello"},              // 包含双引号
		{"`Hello`", "hello"},              // 包含反引号
		{" 'Hello' ", "hello"},            // 前后有空格和单引号
		{" \"Hello\" ", "hello"},          // 前后有空格和双引号
		{" `Hello` ", "hello"},            // 前后有空格和反引号
		{"Hello", "hello"},                 // 不包含空格或引号
		{"HeLLo WoRLD", "hello world"},     // 大小写混合
		{"'  Hello  World  '", "hello  world"}, // 前后有空格和单引号
		{"\"  Hello  World  \"", "hello  world"}, // 前后有空格和双引号
		{"`  Hello  World  `", "hello  world"}, // 前后有空格和反引号
	}

	for _, test := range tests {
		result := normalizeLowercase(test.input)
		if result != test.expected {
			t.Errorf("normalizeLowercase(%q) = %q; want %q", test.input, result, test.expected)
		}
	}
}


func TestNormalizeTrailingParts(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"", ""},                          // 空字符串
		{"   ", ""},                       // 仅包含空格
		{"  Hello World  ", "Hello World"}, // 前后有空格
		{"Hello Wor ld", "Hello Wor ld"},   // 中间有空格
	}

	for _, test := range tests {
		result := normalizeTrailingParts(test.input)
		if result != test.expected {
			t.Errorf("normalizeTrailingParts(%q) = %q; want %q", test.input, result, test.expected)
		}
	}
}


// TestToStringSlice 测试 ToStringSlice 函数
func TestToStringSlice(t *testing.T) {
	// 创建临时文件用于测试
	tmpFile, err := os.CreateTemp("", "testfile")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer os.Remove(tmpFile.Name()) // 清理临时文件

	// 写入测试内容到临时文件
	content := "line 1\nline 2\nline 3\n"
	if _, err := tmpFile.WriteString(content); err != nil {
		t.Fatalf("Failed to write to temp file: %v", err)
	}
	tmpFile.Close() // 关闭文件以确保写入完成

	tests := []struct {
		value    string
		options  Options
		expected []string
		err      bool
	}{
		{
			value: "Hello, World!",
			options: Options{
				IsEmpty: isEmpty,
				Normalize: func(s string) string { return s },
			},
			expected: []string{"Hello", " World!"},
			err:      false,
		},
		{
			value: "  Hello, World!  ",
			options: Options{
				IsEmpty: isEmpty,
				Normalize: normalizeTrailingParts,
			},
			expected: []string{"Hello", "World!"},
			err:      false,
		},
		{
			value: tmpFile.Name(),
			options: Options{
				IsEmpty: isEmpty,
				IsFromFile: func(s string) bool { return s == tmpFile.Name() },
			},
			expected: []string{"line 1", "line 2", "line 3"},
			err:      false,
		},
		{
			value: "raw string",
			options: Options{
				IsEmpty: isEmpty,
				IsRaw: func(s string) bool { return s == "raw string" },
			},
			expected: []string{"raw string"},
			err:      false,
		},
		{
			value: `"Hello, World!"`,
			options: Options{
				IsEmpty: func(s string) bool { return s == "" },
				Normalize: normalizeTrailingParts,
			},
			expected: []string{"Hello, World!"},
			err:      false,
		},
		{
			value: `"Hello, World!`,
			options: Options{
				IsEmpty: isEmpty,
			},
			expected: nil,
			err:      true, // Expecting an error due to unclosed quote
		},
		{
			value: "Hello,, World!",
			options: Options{
				IsEmpty: isEmpty,
				Normalize: normalizeTrailingParts,
			},
			expected: []string{"Hello", "World!"},
			err:      false,
		},
		{
			value: "Hello, World! This is a test.",
			options: Options{
				IsEmpty: isEmpty,
				Normalize: normalizeTrailingParts,
			},
			expected: []string{"Hello", "World! This is a test."},
			err:      false,
		},
		{
			value: "  ",
			options: Options{
				IsEmpty: isEmpty,
				Normalize: normalizeTrailingParts,
			},
			expected: nil,
			err:      false, // No error, but empty result
		},
		{
			value: "Hello, \"World!\"",
			options: Options{
				IsEmpty: isEmpty,
				Normalize: normalizeTrailingParts,
			},
			expected: []string{"Hello", "\"World!\""},
			err:      false,
		},
	}

	for _, test := range tests {

		result, err := ToStringSlice(test.value, test.options)

		if (err != nil) != test.err {
			t.Errorf("For value %q, expected error: %v, got: %v", test.value, test.err, err)
		}

		if !equalSlices(result, test.expected) {
			t.Errorf("For value %q, expected result: %v, got: %v", test.value, test.expected, result)
		}
	}
}

// equalSlices 比较两个字符串切片是否相等
func equalSlices(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}
	for i := range a {
		if a[i] != b[i] {
			return false
		}
	}
	return true
}


// TestToString 测试 ToString 函数
func TestToString(t *testing.T) {
    // 测试空切片
    result := ToString([]string{})
    expected := "[]"
    if result != expected {
        t.Errorf("Expected %q, got %q", expected, result)
    }

    // 测试单个元素的切片
    result = ToString([]string{"item1"})
    expected = "[\"item1\"]"
    if result != expected {
        t.Errorf("Expected %q, got %q", expected, result)
    }

    // 测试多个元素的切片
    result = ToString([]string{"item1", "item2", "item3"})
    expected = "[\"item1\", \"item2\", \"item3\"]"
    if result != expected {
        t.Errorf("Expected %q, got %q", expected, result)
    }

    // 测试包含空字符串的切片
    result = ToString([]string{"item1", "", "item3"})
    expected = "[\"item1\", \"\", \"item3\"]"
    if result != expected {
        t.Errorf("Expected %q, got %q", expected, result)
    }
}