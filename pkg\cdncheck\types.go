// Author: chenjb
// Version: V1.0
// Date: 2025-06-19 15:52:23
// FilePath: /yaml_scan/pkg/cdncheck/types.go
// Description: 定义CDN检测包中使用的数据类型和结构
package cdncheck

import (
	"net"
	"net/netip"

	"github.com/gaissmai/bart"
)

// InputCompiled 包含已编译的输入结构列表
type InputCompiled struct {
	// CDN 包含CDN提供商的CIDR范围列表
	CDN map[string][]string `yaml:"cdn,omitempty" json:"cdn,omitempty"`
		// WAF 包含WAF提供商的CIDR范围列表
	WAF map[string][]string `yaml:"waf,omitempty" json:"waf,omitempty"`
	// Cloud 包含云服务提供商的CIDR范围列表
	Cloud map[string][]string `yaml:"cloud,omitempty" json:"cloud,omitempty"`
	// Common 包含主要提供商的域名后缀列表
	Common map[string][]string `yaml:"common,omitempty" json:"common,omitempty"`
}


// providerScraper 是用于检测IP是否属于某提供商的结构
type providerScraper struct {
	rangers map[string]*bart.Table[net.IP]  // 提供商名称到IP范围查找表的映射
}

// newProviderScraper 返回一个新的提供商检测器实例
// @param ranges map[string][]string:  提供商名称到CIDR范围列表的映射
// @return *providerScraper *providerScraper:  初始化后的提供商检测器
func newProviderScraper(ranges map[string][]string) *providerScraper {
	 // 创建新的检测器实例
	scraper := &providerScraper{rangers: make(map[string]*bart.Table[net.IP])}

	for provider, items := range ranges {
		ranger := new(bart.Table[net.IP])
		for _, cidr := range items {
			if network, err := netip.ParsePrefix(cidr); err == nil {
				ranger.Insert(network, nil)
			}
		}
		scraper.rangers[provider] = ranger
	}
	return scraper
}



// Match 检查IP是否匹配提供的CIDR范围
//
// @receiver p 
// @param ip net.IP: 要检查的IP地址
// @return bool bool:  如果IP匹配任何CIDR范围则为true
// @return string string:  匹配的提供商名称，如果不匹配则为空字符串
// @return error error:  检查过程中可能出现的错误
func (p *providerScraper) Match(ip net.IP) (bool, string, error) {
	// 将net.IP转换为netip.Addr
	parsed, err := netip.ParseAddr(ip.String())
	if err != nil {
		return false, "", err
	}

	 // 遍历所有提供及其IP范围查找表
	for provider, ranger := range p.rangers {
		 // 检查IP是否在范围内
		if _, contains := ranger.Lookup(parsed); contains {
			return true, provider, err
		}
	}
	return false, "", nil
}