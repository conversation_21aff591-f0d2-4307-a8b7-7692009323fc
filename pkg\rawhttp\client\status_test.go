// Author: chenjb
// Version: V1.0
// Date: 2025-07-25
// FilePath: /yaml_scan/pkg/rawhttp/client/status_test.go
// Description: rawhttp状态码模块单元测试

package client

import (
	"testing"

	"github.com/stretchr/testify/require"
)

// TestStatus_String 测试Status的String方法
func TestStatus_String(t *testing.T) {
	// 测试正常状态
	status := Status{Code: 200, Reason: "OK"}
	require.Equal(t, "200 OK", status.String(), "状态字符串应该正确格式化")
	
	// 测试错误状态
	status2 := Status{Code: 404, Reason: "Not Found"}
	require.Equal(t, "404 Not Found", status2.String(), "错误状态字符串应该正确格式化")
	
	// 测试空原因短语
	status3 := Status{Code: 500, Reason: ""}
	require.Equal(t, "500 ", status3.String(), "空原因短语应该正确处理")
}

// TestStatus_IsInformational 测试IsInformational方法
func TestStatus_IsInformational(t *testing.T) {
	// 测试1xx状态码
	testCases := []struct {
		code     int
		expected bool
		desc     string
	}{
		{INFO_CONTINUE, true, "100 Continue应该是信息性状态码"},
		{INFO_SWITCHING_PROTOCOL, true, "101 Switching Protocol应该是信息性状态码"},
		{INFO_PROCESSING, true, "102 Processing应该是信息性状态码"},
		{199, true, "199应该是信息性状态码"},
		{SUCCESS_OK, false, "200 OK不应该是信息性状态码"},
		{CLIENT_ERROR_BAD_REQUEST, false, "400 Bad Request不应该是信息性状态码"},
	}
	
	for _, tc := range testCases {
		status := Status{Code: tc.code}
		require.Equal(t, tc.expected, status.IsInformational(), tc.desc)
	}
}

// TestStatus_IsSuccess 测试IsSuccess方法
func TestStatus_IsSuccess(t *testing.T) {
	// 测试2xx状态码
	testCases := []struct {
		code     int
		expected bool
		desc     string
	}{
		{SUCCESS_OK, true, "200 OK应该是成功状态码"},
		{SUCCESS_CREATED, true, "201 Created应该是成功状态码"},
		{SUCCESS_ACCEPTED, true, "202 Accepted应该是成功状态码"},
		{SUCCESS_NO_CONTENT, true, "204 No Content应该是成功状态码"},
		{SUCCESS_PARTIAL_CONTENT, true, "206 Partial Content应该是成功状态码"},
		{299, true, "299应该是成功状态码"},
		{INFO_CONTINUE, false, "100 Continue不应该是成功状态码"},
		{REDIRECTION_MOVED_PERMANENTLY, false, "301 Moved Permanently不应该是成功状态码"},
		{CLIENT_ERROR_BAD_REQUEST, false, "400 Bad Request不应该是成功状态码"},
	}
	
	for _, tc := range testCases {
		status := Status{Code: tc.code}
		require.Equal(t, tc.expected, status.IsSuccess(), tc.desc)
	}
}

// TestStatus_IsRedirect 测试IsRedirect方法
func TestStatus_IsRedirect(t *testing.T) {
	// 测试3xx状态码
	testCases := []struct {
		code     int
		expected bool
		desc     string
	}{
		{REDIRECTION_MULTIPLE_CHOICES, true, "300 Multiple Choices应该是重定向状态码"},
		{REDIRECTION_MOVED_PERMANENTLY, true, "301 Moved Permanently应该是重定向状态码"},
		{REDIRECTION_MOVED_TEMPORARILY, true, "302 Found应该是重定向状态码"},
		{REDIRECTION_SEE_OTHER, true, "303 See Other应该是重定向状态码"},
		{REDIRECTION_NOT_MODIFIED, false, "304 Not Modified不应该是重定向状态码（特殊情况）"},
		{REDIRECTION_USE_PROXY, true, "305 Use Proxy应该是重定向状态码"},
		{REDIRECTION_TEMPORARY_REDIRECT, true, "307 Temporary Redirect应该是重定向状态码"},
		{399, true, "399应该是重定向状态码"},
		{SUCCESS_OK, false, "200 OK不应该是重定向状态码"},
		{CLIENT_ERROR_BAD_REQUEST, false, "400 Bad Request不应该是重定向状态码"},
	}
	
	for _, tc := range testCases {
		status := Status{Code: tc.code}
		require.Equal(t, tc.expected, status.IsRedirect(), tc.desc)
	}
}

// TestStatus_IsError 测试IsError方法
func TestStatus_IsError(t *testing.T) {
	// 测试4xx和5xx状态码
	testCases := []struct {
		code     int
		expected bool
		desc     string
	}{
		{CLIENT_ERROR_BAD_REQUEST, true, "400 Bad Request应该是错误状态码"},
		{CLIENT_ERROR_UNAUTHORIZED, true, "401 Unauthorized应该是错误状态码"},
		{CLIENT_ERROR_NOT_FOUND, true, "404 Not Found应该是错误状态码"},
		{SERVER_ERROR_INTERNAL, true, "500 Internal Server Error应该是错误状态码"},
		{SERVER_ERROR_NOT_IMPLEMENTED, true, "501 Not Implemented应该是错误状态码"},
		{599, true, "599应该是错误状态码"},
		{SUCCESS_OK, false, "200 OK不应该是错误状态码"},
		{REDIRECTION_MOVED_PERMANENTLY, false, "301 Moved Permanently不应该是错误状态码"},
	}
	
	for _, tc := range testCases {
		status := Status{Code: tc.code}
		require.Equal(t, tc.expected, status.IsError(), tc.desc)
	}
}

// TestStatus_IsClientError 测试IsClientError方法
func TestStatus_IsClientError(t *testing.T) {
	// 测试4xx状态码
	testCases := []struct {
		code     int
		expected bool
		desc     string
	}{
		{CLIENT_ERROR_BAD_REQUEST, true, "400 Bad Request应该是客户端错误"},
		{CLIENT_ERROR_UNAUTHORIZED, true, "401 Unauthorized应该是客户端错误"},
		{CLIENT_ERROR_FORBIDDEN, true, "403 Forbidden应该是客户端错误"},
		{CLIENT_ERROR_NOT_FOUND, true, "404 Not Found应该是客户端错误"},
		{CLIENT_ERROR_METHOD_NOT_ALLOWED, true, "405 Method Not Allowed应该是客户端错误"},
		{499, true, "499应该是客户端错误"},
		{SUCCESS_OK, false, "200 OK不应该是客户端错误"},
		{REDIRECTION_MOVED_PERMANENTLY, false, "301 Moved Permanently不应该是客户端错误"},
		{SERVER_ERROR_INTERNAL, false, "500 Internal Server Error不应该是客户端错误"},
	}
	
	for _, tc := range testCases {
		status := Status{Code: tc.code}
		require.Equal(t, tc.expected, status.IsClientError(), tc.desc)
	}
}

// TestStatus_IsServerError 测试IsServerError方法
func TestStatus_IsServerError(t *testing.T) {
	// 测试5xx状态码
	testCases := []struct {
		code     int
		expected bool
		desc     string
	}{
		{SERVER_ERROR_INTERNAL, true, "500 Internal Server Error应该是服务器错误"},
		{SERVER_ERROR_NOT_IMPLEMENTED, true, "501 Not Implemented应该是服务器错误"},
		{SERVER_ERROR_BAD_GATEWAY, true, "502 Bad Gateway应该是服务器错误"},
		{SERVER_ERROR_SERVICE_UNAVAILABLE, true, "503 Service Unavailable应该是服务器错误"},
		{SERVER_ERROR_GATEWAY_TIMEOUT, true, "504 Gateway Timeout应该是服务器错误"},
		{599, true, "599应该是服务器错误"},
		{SUCCESS_OK, false, "200 OK不应该是服务器错误"},
		{CLIENT_ERROR_BAD_REQUEST, false, "400 Bad Request不应该是服务器错误"},
	}
	
	for _, tc := range testCases {
		status := Status{Code: tc.code}
		require.Equal(t, tc.expected, status.IsServerError(), tc.desc)
	}
}

// TestStatusConstants 测试状态码常量的值
func TestStatusConstants(t *testing.T) {
	// 测试1xx信息性状态码
	require.Equal(t, 100, INFO_CONTINUE, "INFO_CONTINUE应该是100")
	require.Equal(t, 101, INFO_SWITCHING_PROTOCOL, "INFO_SWITCHING_PROTOCOL应该是101")
	require.Equal(t, 102, INFO_PROCESSING, "INFO_PROCESSING应该是102")
	
	// 测试2xx成功状态码
	require.Equal(t, 200, SUCCESS_OK, "SUCCESS_OK应该是200")
	require.Equal(t, 201, SUCCESS_CREATED, "SUCCESS_CREATED应该是201")
	require.Equal(t, 202, SUCCESS_ACCEPTED, "SUCCESS_ACCEPTED应该是202")
	require.Equal(t, 204, SUCCESS_NO_CONTENT, "SUCCESS_NO_CONTENT应该是204")
	require.Equal(t, 206, SUCCESS_PARTIAL_CONTENT, "SUCCESS_PARTIAL_CONTENT应该是206")
	
	// 测试3xx重定向状态码
	require.Equal(t, 300, REDIRECTION_MULTIPLE_CHOICES, "REDIRECTION_MULTIPLE_CHOICES应该是300")
	require.Equal(t, 301, REDIRECTION_MOVED_PERMANENTLY, "REDIRECTION_MOVED_PERMANENTLY应该是301")
	require.Equal(t, 302, REDIRECTION_MOVED_TEMPORARILY, "REDIRECTION_MOVED_TEMPORARILY应该是302")
	require.Equal(t, 304, REDIRECTION_NOT_MODIFIED, "REDIRECTION_NOT_MODIFIED应该是304")
	require.Equal(t, 307, REDIRECTION_TEMPORARY_REDIRECT, "REDIRECTION_TEMPORARY_REDIRECT应该是307")
	
	// 测试4xx客户端错误状态码
	require.Equal(t, 400, CLIENT_ERROR_BAD_REQUEST, "CLIENT_ERROR_BAD_REQUEST应该是400")
	require.Equal(t, 401, CLIENT_ERROR_UNAUTHORIZED, "CLIENT_ERROR_UNAUTHORIZED应该是401")
	require.Equal(t, 403, CLIENT_ERROR_FORBIDDEN, "CLIENT_ERROR_FORBIDDEN应该是403")
	require.Equal(t, 404, CLIENT_ERROR_NOT_FOUND, "CLIENT_ERROR_NOT_FOUND应该是404")
	require.Equal(t, 405, CLIENT_ERROR_METHOD_NOT_ALLOWED, "CLIENT_ERROR_METHOD_NOT_ALLOWED应该是405")
	
	// 测试5xx服务器错误状态码
	require.Equal(t, 500, SERVER_ERROR_INTERNAL, "SERVER_ERROR_INTERNAL应该是500")
	require.Equal(t, 501, SERVER_ERROR_NOT_IMPLEMENTED, "SERVER_ERROR_NOT_IMPLEMENTED应该是501")
	require.Equal(t, 502, SERVER_ERROR_BAD_GATEWAY, "SERVER_ERROR_BAD_GATEWAY应该是502")
	require.Equal(t, 503, SERVER_ERROR_SERVICE_UNAVAILABLE, "SERVER_ERROR_SERVICE_UNAVAILABLE应该是503")
	require.Equal(t, 504, SERVER_ERROR_GATEWAY_TIMEOUT, "SERVER_ERROR_GATEWAY_TIMEOUT应该是504")
}

// TestStatus_EdgeCases 测试边界情况
func TestStatus_EdgeCases(t *testing.T) {
	// 测试边界值
	testCases := []struct {
		code                int
		isInformational     bool
		isSuccess           bool
		isRedirect          bool
		isError             bool
		isClientError       bool
		isServerError       bool
	}{
		{99, false, false, false, false, false, false},   // 小于100
		{100, true, false, false, false, false, false},   // 1xx边界
		{199, true, false, false, false, false, false},   // 1xx边界
		{200, false, true, false, false, false, false},   // 2xx边界
		{299, false, true, false, false, false, false},   // 2xx边界
		{300, false, false, true, false, false, false},   // 3xx边界
		{304, false, false, false, false, false, false},  // 304特殊情况
		{399, false, false, true, false, false, false},   // 3xx边界
		{400, false, false, false, true, true, false},    // 4xx边界
		{499, false, false, false, true, true, false},    // 4xx边界
		{500, false, false, false, true, false, true},    // 5xx边界
		{999, false, false, false, true, false, true},    // 大于500
	}
	
	for _, tc := range testCases {
		status := Status{Code: tc.code}
		require.Equal(t, tc.isInformational, status.IsInformational(), "IsInformational for code %d", tc.code)
		require.Equal(t, tc.isSuccess, status.IsSuccess(), "IsSuccess for code %d", tc.code)
		require.Equal(t, tc.isRedirect, status.IsRedirect(), "IsRedirect for code %d", tc.code)
		require.Equal(t, tc.isError, status.IsError(), "IsError for code %d", tc.code)
		require.Equal(t, tc.isClientError, status.IsClientError(), "IsClientError for code %d", tc.code)
		require.Equal(t, tc.isServerError, status.IsServerError(), "IsServerError for code %d", tc.code)
	}
}
