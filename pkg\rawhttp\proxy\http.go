// Author: chenjb
// Version: V1.0
// Date: 2025-07-22 17:48:32
// FilePath: /yaml_scan/pkg/rawhttp/proxy/http.go
// Description: HTTP代理实现，支持通过HTTP CONNECT方法建立代理连接

// Package proxy HTTP代理功能模块
package proxy

import (
	"context"                       // 上下文包
	"encoding/base64"               // Base64编码包
	"fmt"                           // 格式化输出包
	"net"                           // 网络包
	"net/url"                       // URL解析包
	"strings"                       // 字符串处理包
	"time"                          // 时间处理包
	"yaml_scan/pkg/fastdialer"      // 快速拨号器包
	"yaml_scan/pkg/rawhttp/client"  // HTTP客户端包
)

// httpDialer 创建HTTP代理拨号函数的内部实现
// 参数:
//   proxyAddr: 代理服务器地址
//   timeout: 连接超时时间
//   fd: 快速拨号器实例，可为nil
// 返回:
//   DialFunc: 代理拨号函数
// 功能: 通过HTTP CONNECT方法建立代理连接，支持基本身份验证
func httpDialer(proxyAddr string, timeout time.Duration, fd *fastdialer.Dialer) DialFunc {
	return func(addr string) (net.Conn, error) {
		var netConn net.Conn
		var err error
		var auth string
		// close the connection when an error occurs - 发生错误时关闭连接
		defer func() {
			if err != nil && netConn != nil {
				netConn.Close()
			}
		}()
		// 解析代理地址URL
		u, err := url.Parse(proxyAddr)
		if err != nil {
			return nil, err
		}
		// 检查是否包含身份验证信息（格式：user:pass@proxy）
		if strings.Contains(proxyAddr, "@") {
			split := strings.Split(proxyAddr, "@")
			auth = base64.StdEncoding.EncodeToString([]byte(split[0])) // Base64编码认证信息
			proxyAddr = split[1] // 提取代理地址部分
		}
		// 建立到代理服务器的连接
		if fd != nil {
			// 使用快速拨号器
			netConn, err = fd.Dial(context.TODO(), "tcp", u.Host)
		} else {
			// 使用标准拨号器
			netConn, err = net.DialTimeout("tcp", u.Host, timeout)
		}

		if err != nil {
			return nil, err
		}
		// 创建HTTP客户端
		conn := client.NewClient(netConn)

		// 构建CONNECT请求
		req := "CONNECT " + addr + " HTTP/1.1\r\n"
		if auth != "" {
			// 添加代理身份验证头部
			req += "Proxy-Authorization: Basic " + auth + "\r\n"
		}
		req += "\r\n" // 请求结束标记
		// 创建原始请求对象
		clientReq := &client.Request{
			RawBytes: []byte(req),
		}
		// 发送CONNECT请求
		if err = conn.WriteRequest(clientReq); err != nil {
			return nil, err
		}
		// 读取代理服务器响应
		resp, err := conn.ReadResponse(false)
		if err != nil {
			return nil, err
		}
		// 检查响应状态码，200表示连接成功
		if resp.Status.Code != 200 {
			return nil, fmt.Errorf("could not connect to proxy: %s status code: %d", proxyAddr, resp.Status.Code)
		}

		return netConn, nil // 返回建立的连接
	}
}

// HTTPDialer 创建标准HTTP代理拨号函数
// 参数:
//   proxyAddr: 代理服务器地址（格式：http://[user:pass@]host:port）
//   timeout: 连接超时时间
// 返回:
//   DialFunc: HTTP代理拨号函数
// 功能: 使用标准网络拨号器创建HTTP代理连接
func HTTPDialer(proxyAddr string, timeout time.Duration) DialFunc {
	return httpDialer(proxyAddr, timeout, nil) // 不使用快速拨号器
}

// HTTPFastDialer 创建使用快速拨号器的HTTP代理拨号函数
// 参数:
//   proxyAddr: 代理服务器地址
//   timeout: 连接超时时间
//   fd: 快速拨号器实例
// 返回:
//   DialFunc: HTTP代理拨号函数
// 功能: 使用快速拨号器创建HTTP代理连接，提供更好的性能
func HTTPFastDialer(proxyAddr string, timeout time.Duration, fd *fastdialer.Dialer) DialFunc {
	return httpDialer(proxyAddr, timeout, fd) // 使用快速拨号器
}

