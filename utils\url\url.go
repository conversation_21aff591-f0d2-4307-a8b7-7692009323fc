//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-11 17:51:31
//FilePath: /yaml_scan/utils/url/url.go
//Description: url封装

package urlutil

import (
	"net/url"
	"strings"
)

// URL 是 net/url.URL 的一个封装
type URL struct {
	*url.URL // 嵌入 net/url.URL 以便使用其功能

	Original           string         // original 原始或给定的 URL（如果有参数则不包含）
	Unsafe             bool           // Unsafe 如果请求不安全（跳过验证）
	IsRelative         bool           // URL 是否相对路径
	Params             *OrderedParams // 查询参数
	disableAutoCorrect bool           // 是否禁用任何类型的自动更正
}

// Update: 是一个自定义的 URL 结构体，封装了标准库中的 url.URL
//
//	@receiver u *URL:
func (u *URL) Update() {
	// 如果 Params 不为 nil，则将其编码为查询字符串
	if u.Params != nil {
		u.RawQuery = u.Params.Encode()
	}
}

// fetchParams: 从 Original 字段中提取查询参数和片段（fragment）
//
//	@receiver u *URL:
func (u *URL) fetchParams() {
	if u.Params == nil {
		u.Params = NewOrderedParams()
	}
	// 解析 URL 中的片段（如果存在 #后面的内容），并将其存储在 Fragment 字段中。
	if i := strings.IndexRune(u.Original, '#'); i != -1 {
		// 例如 ?param=value#highlight
		u.Fragment = u.Original[i+1:]
		u.Original = u.Original[:i]
	}
	if index := strings.IndexRune(u.Original, '?'); index == -1 {
		return
	} else {
		encodedParams := u.Original[index+1:]
		u.Params.Decode(encodedParams)
		u.Original = u.Original[:index]
	}
	u.Update()
}

// parseUnsafeRelativePath: 解析 Original 路径中的相对路径
//
//	@receiver u *URL:
func (u *URL) parseUnsafeRelativePath() {
	// 自动修正路径前缀
	defer func() {
		if !u.disableAutoCorrect && !strings.HasPrefix(u.Path, "/") && u.Path != "" {
			u.Path = "/" + u.Path
		}
	}()

	// 如果 Original 和 Path 不相同，则将 Path 设置为 Original。这用于确保路径的完整性
	if u.Original != u.Path {
		u.Path = u.Original
	}

	// 如果 Host 为空或长度小于 4，检查 Original 是否需要转义。如果需要，则将 Path 设置为 Original，并返回
	if u.Host == "" || len(u.Host) < 4 {
		if shouldEscape(u.Original) {
			u.Path = u.Original
		}
		return
	}
	// 分割路径  将 Original 按 Host 分割成两部分
	expectedPath := strings.SplitN(u.Original, u.Host, 2)

	if len(expectedPath) != 2 {
		return
	}
	// 将 Path 设置为分割后的第二部分
	u.Path = expectedPath[1]
}

// shouldEscape: 判断给定的字符串中是否包含需要被转义的字符  不包括分隔符
//
//	@param ss string:
//	@return bool bool:
func shouldEscape(ss string) bool {
	rmap := getrunemap(RFCEscapeCharSet)
	for _, v := range ss {
		switch {
		case v == '/':
			continue
		case v > rune(127):
			// 非 ASCII 字符 需要被转义
			return true
		default:
			// 如果在RFC保留字符中，需要被转义
			if _, ok := rmap[v]; ok {
				return true
			}
		}
	}
	return false
}

// copy: // copy 将 src 中解析的数据复制到 dst 中，
// 该函数不包括片段（fragment）或查询参数（params）。
//
//	@param dst *url.URL:
//	@param src *url.URL:
func copy(dst *url.URL, src *url.URL) {
	dst.Host = src.Host
	dst.Opaque = src.Opaque
	dst.Path = src.Path
	dst.RawPath = src.RawPath
	dst.Scheme = src.Scheme
	dst.User = src.User
}

// Clone
func (u *URL) Clone() *URL {
	var userinfo *url.Userinfo
	if u.User != nil {
		// userinfo is immutable so this is the only way
		tempurl := HTTPS + SchemeSeparator + u.User.String() + "@" + "scanme.sh/"
		turl, _ := url.Parse(tempurl)
		if turl != nil {
			userinfo = turl.User
		}
	}
	ux := &url.URL{
		Scheme:      u.Scheme,
		Opaque:      u.Opaque,
		User:        userinfo,
		Host:        u.Host,
		Path:        u.Path,
		RawPath:     u.RawPath,
		RawQuery:    u.RawQuery,
		Fragment:    u.Fragment,
		OmitHost:    u.OmitHost, // only supported in 1.19
		ForceQuery:  u.ForceQuery,
		RawFragment: u.RawFragment,
	}
	params := u.Params.Clone()
	return &URL{
		URL:        ux,
		Params:     params,
		Original:   u.Original,
		Unsafe:     u.Unsafe,
		IsRelative: u.IsRelative,
	}
}