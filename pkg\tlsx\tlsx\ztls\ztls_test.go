// Author: chenjb
// Version: V1.0
// Date: 2025-06-27 16:49:59
// FilePath: /yaml_scan/pkg/tlsx/tlsx/ztls/ztls_test.go
// Description:
package ztls

import (
	"testing"
	"yaml_scan/pkg/fastdialer"
	"yaml_scan/pkg/tlsx/tlsx/clients"

	"github.com/stretchr/testify/require"
)

// TestNew 测试ZMap TLS客户端的创建功能
// 验证不同配置下ZMap TLS客户端的正确创建
func TestNew(t *testing.T) {
	tests := []struct {
		name        string           // 测试用例名称
		options     *clients.Options // 输入的配置选项
		expectError bool             // 是否期望出现错误
		description string           // 测试用例描述
	}{
		{
			name: "默认配置创建ZMap TLS客户端",
			options: &clients.Options{
				Timeout: 5,
				Retries: 3,
			},
			expectError: false,
			description: "使用默认配置创建ZMap TLS客户端应该成功",
		},
		{
			name: "启用证书验证",
			options: &clients.Options{
				Timeout:                 5,
				Retries:                 3,
				VerifyServerCertificate: true,
			},
			expectError: false,
			description: "启用证书验证的ZMap TLS客户端应该成功创建",
		},
		{
			name: "启用JA3指纹",
			options: &clients.Options{
				Timeout: 5,
				Retries: 3,
				Ja3:     true,
			},
			expectError: false,
			description: "启用JA3指纹的ZMap TLS客户端应该成功创建",
		},
		{
			name: "启用协议详细信息",
			options: &clients.Options{
				Timeout:     5,
				Retries:     3,
				ClientHello: true,
				ServerHello: true,
			},
			expectError: false,
			description: "启用协议详细信息的ZMap TLS客户端应该成功创建",
		},
		{
			name: "指定最小TLS版本",
			options: &clients.Options{
				Timeout:    5,
				Retries:    3,
				MinVersion: "tls12",
			},
			expectError: false,
			description: "指定最小TLS版本应该成功创建客户端",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行ZMap TLS客户端创建
			client, err := New(tt.options)

			if tt.expectError {
				// 期望出现错误的情况
				require.Error(t, err, "应该返回错误: %s", tt.description)
				require.Nil(t, client, "出错时client应该为nil")
			} else {
				// 期望成功的情况
				require.NoError(t, err, "不应该返回错误: %s", tt.description)
				require.NotNil(t, client, "client不应该为nil")
				require.NotNil(t, client.dialer, "dialer不应该为nil")
				require.NotNil(t, client.tlsConfig, "tlsConfig不应该为nil")
				require.NotNil(t, client.options, "options不应该为nil")

				// 验证TLS配置
				if tt.options.VerifyServerCertificate {
					require.False(t, client.tlsConfig.InsecureSkipVerify, "启用证书验证时不应该跳过验证")
				} else {
					require.True(t, client.tlsConfig.InsecureSkipVerify, "默认应该跳过证书验证")
				}

				t.Logf("ZMap TLS客户端创建成功 - 证书验证: %v", !client.tlsConfig.InsecureSkipVerify)
			}
		})
	}
}

// TestConnectWithOptions 测试ZMap TLS连接功能
// 使用真实的网络地址进行连接测试
func TestConnectWithOptions(t *testing.T) {
	// 创建ZMap TLS客户端
	client, err := New(&clients.Options{
		Timeout: 60,
		Retries: 3,
	})
	require.NoError(t, err, "创建ZMap TLS客户端不应该出错")

	tests := []struct {
		name        string                  // 测试用例名称
		hostname    string                  // 目标主机名
		ip          string                  // 目标IP地址
		port        string                  // 目标端口
		options     clients.ConnectOptions  // 连接选项
		expectError bool                    // 是否期望出现错误
		description string                  // 测试用例描述
	}{
		{
			name:        "连接百度HTTPS",
			hostname:    "www.baidu.com",
			ip:          "",
			port:        "443",
			options:     clients.ConnectOptions{},
			expectError: false,
			description: "连接百度的HTTPS服务应该成功",
		},
		{
			name:     "使用SNI连接",
			hostname: "www.baidu.com",
			ip:       "",
			port:     "443",
			options: clients.ConnectOptions{
				SNI: "www.baidu.com",
			},
			expectError: false,
			description: "使用SNI连接应该成功",
		},
		{
			name:     "指定TLS版本连接",
			hostname: "www.baidu.com",
			ip:       "",
			port:     "443",
			options: clients.ConnectOptions{
				VersionTLS: "tls12",
			},
			expectError: false,
			description: "指定TLS 1.2版本连接应该成功",
		},
		{
			name:        "连接不存在的端口",
			hostname:    "www.baidu.com",
			ip:          "",
			port:        "9999",
			options:     clients.ConnectOptions{},
			expectError: true,
			description: "连接不存在的端口应该失败",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行连接
			client.options.Fastdialer, _ = fastdialer.NewDialer(fastdialer.DefaultOptions)
			response, err := client.ConnectWithOptions(tt.hostname, tt.ip, tt.port, tt.options)

			if tt.expectError {
				// 期望出现错误的情况
				require.Error(t, err, "应该返回错误: %s", tt.description)
			} else {
				// 期望成功的情况
				require.NoError(t, err, "不应该返回错误: %s", tt.description)
				require.NotNil(t, response, "响应不应该为nil")
				require.Equal(t, tt.port, response.Port, "端口应该匹配")
				require.Equal(t, tt.hostname, response.Host, "主机名应该匹配")
				
				// 验证TLS版本和密码套件
				require.NotEmpty(t, response.Version, "TLS版本不应该为空")
				require.NotEmpty(t, response.Cipher, "密码套件不应该为空")
				
				// 验证证书信息
				require.NotNil(t, response.CertificateResponse, "证书响应不应该为nil")
				
				t.Logf("连接成功 - TLS版本: %s, 密码套件: %s", response.Version, response.Cipher)
			}
		})
	}
}

// TestConnectWithJA3 测试JA3指纹功能
// 验证ZMap TLS客户端的JA3指纹计算能力
func TestConnectWithJA3(t *testing.T) {
	// 创建启用JA3的ZMap TLS客户端
	client, err := New(&clients.Options{
		Timeout: 20,
		Retries: 2,
		Ja3:     true,
	})
	require.NoError(t, err, "创建启用JA3的ZMap TLS客户端不应该出错")

	// 测试JA3指纹计算
	client.options.Fastdialer, _ = fastdialer.NewDialer(fastdialer.DefaultOptions)
	response, err := client.ConnectWithOptions("www.baidu.com", "", "443", clients.ConnectOptions{})
	require.NoError(t, err, "JA3连接不应该出错")
	require.NotNil(t, response, "响应不应该为nil")
	
	// 验证JA3指纹
	require.NotEmpty(t, response.Ja3Hash, "JA3哈希不应该为空")
	
	t.Logf("JA3指纹: %s", response.Ja3Hash)
}

// TestConnectWithProtocolDetails 测试协议详细信息功能
// 验证ZMap TLS客户端的协议详细信息收集能力
func TestConnectWithProtocolDetails(t *testing.T) {
	// 创建启用协议详细信息的ZMap TLS客户端
	client, err := New(&clients.Options{
		Timeout:     30,
		Retries:     2,
		ClientHello: true,
		ServerHello: true,
	})
	require.NoError(t, err, "创建启用协议详细信息的ZMap TLS客户端不应该出错")

	// 测试协议详细信息收集
	client.options.Fastdialer, _ = fastdialer.NewDialer(fastdialer.DefaultOptions)
	response, err := client.ConnectWithOptions("www.baidu.com", "", "443", clients.ConnectOptions{})
	require.NoError(t, err, "协议详细信息连接不应该出错")
	require.NotNil(t, response, "响应不应该为nil")
	
	// 验证协议详细信息
	require.NotNil(t, response.ClientHello, "ClientHello不应该为nil")
	require.NotNil(t, response.ServerHello, "ServerHello不应该为nil")
	
	t.Logf("协议详细信息收集成功 - ClientHello: %v, ServerHello: %v", 
		response.ClientHello != nil, response.ServerHello != nil)
}

// TestEnumerateCiphers 测试密码套件枚举功能
// 验证ZMap TLS客户端的密码套件枚举能力
func TestEnumerateCiphers(t *testing.T) {
	// 创建ZMap TLS客户端
	client, err := New(&clients.Options{
		Timeout: 20,
		Retries: 2,
	})
	require.NoError(t, err, "创建ZMap TLS客户端不应该出错")

	// 测试密码套件枚举
	client.options.Fastdialer, _ = fastdialer.NewDialer(fastdialer.DefaultOptions)
	ciphers, err := client.EnumerateCiphers("www.baidu.com", "", "443", clients.ConnectOptions{
		VersionTLS: "tls12",
		CipherLevel: []clients.CipherSecLevel{clients.All},
	})

	require.NoError(t, err, "密码套件枚举不应该出错")
	require.NotNil(t, ciphers, "密码套件列表不应该为nil")
	
	// 验证枚举结果
	if len(ciphers) > 0 {
		t.Logf("枚举到 %d 个密码套件", len(ciphers))
		
		// 验证密码套件名称格式
		for _, cipher := range ciphers {
			require.NotEmpty(t, cipher, "密码套件名称不应该为空")
		}
		
		// 显示前几个密码套件作为示例
		maxShow := 5
		if len(ciphers) < maxShow {
			maxShow = len(ciphers)
		}
		t.Logf("示例密码套件: %v", ciphers[:maxShow])
	} else {
		t.Log("未枚举到密码套件（可能是网络或服务器配置原因）")
	}
}

// TestSupportedTLSVersions 测试支持的TLS版本获取功能
// 验证ZMap TLS客户端的版本支持信息
func TestSupportedTLSVersions(t *testing.T) {
	// 创建ZMap TLS客户端
	client, err := New(&clients.Options{
		Timeout: 5,
		Retries: 1,
	})
	require.NoError(t, err, "创建ZMap TLS客户端不应该出错")

	// 获取支持的TLS版本
	versions, err := client.SupportedTLSVersions()
	require.NoError(t, err, "获取支持的TLS版本不应该出错")
	require.NotNil(t, versions, "TLS版本列表不应该为nil")
	require.NotEmpty(t, versions, "TLS版本列表不应该为空")

	// 验证版本内容（ZMap支持更多版本，包括SSL 3.0）
	expectedVersions := []string{"tls1", "tls12", "tls13"}
	foundCount := 0
	for _, expected := range expectedVersions {
		if contains(versions, expected) {
			foundCount++
		}
	}
	require.Greater(t, foundCount, 0, "应该至少支持一些标准TLS版本")

	t.Logf("支持的TLS版本: %v", versions)
}

// TestSupportedTLSCiphers 测试支持的密码套件获取功能
// 验证ZMap TLS客户端的密码套件支持信息
func TestSupportedTLSCiphers(t *testing.T) {
	// 创建ZMap TLS客户端
	client, err := New(&clients.Options{
		Timeout: 5,
		Retries: 1,
	})
	require.NoError(t, err, "创建ZMap TLS客户端不应该出错")

	// 获取支持的密码套件
	ciphers, err := client.SupportedTLSCiphers()
	require.NoError(t, err, "获取支持的密码套件不应该出错")
	require.NotNil(t, ciphers, "密码套件列表不应该为nil")
	require.NotEmpty(t, ciphers, "密码套件列表不应该为空")

	// 验证密码套件格式
	for _, cipher := range ciphers {
		require.NotEmpty(t, cipher, "密码套件名称不应该为空")
	}

	t.Logf("支持的密码套件总数: %d", len(ciphers))
	
	// 显示前几个密码套件作为示例
	maxShow := 10
	if len(ciphers) < maxShow {
		maxShow = len(ciphers)
	}
	t.Logf("示例密码套件: %v", ciphers[:maxShow])
}

// contains 检查字符串切片是否包含指定字符串
// 辅助函数，用于版本检查
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}


