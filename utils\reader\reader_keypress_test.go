// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 19:41:01
// FilePath: /yaml_scan/utils/reader/reader_keypress_test.go
// Description: 
package reader

import (
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestKeyPressReader 测试键盘按键读取器
func TestKeyPressReader(t *testing.T) {
	t.Run("创建KeyPressReader", func(t *testing.T) {
		r := require.New(t)

		// 创建一个KeyPressReader
		kpr := &KeyPressReader{
			Once:    &sync.Once{},
			Raw:     false,
			Timeout: 100 * time.Millisecond,
		}

		r.NotNil(kpr)
		r.Equal(100*time.Millisecond, kpr.Timeout)
		r.<PERSON>alse(kpr.Raw)
	})

	// 由于KeyPressReader依赖于实际的标准输入，不易在单元测试中模拟
	// 这里我们只测试部分行为，完整测试可能需要集成测试或手动测试

	t.Run("读取超时", func(t *testing.T) {
		r := require.New(t)

		// 创建一个KeyPressReader，设置超时
		kpr := &KeyPressReader{
			Once:    &sync.Once{},
			Raw:     false,
			Timeout: 10 * time.Millisecond,
		}

		// 不调用Start()，这样不会启动实际的读取线程
		// 但我们可以测试Read方法的超时行为

		// 尝试读取数据，应该超时
		buffer := make([]byte, 10)
		_, err := kpr.Read(buffer)

		// 验证超时错误
		r.Error(err)
		r.Equal(ErrTimeout, err)
	})
}

// TestKeyPressReaderMockDatachan 使用模拟数据通道测试KeyPressReader
func TestKeyPressReaderMockDatachan(t *testing.T) {
	t.Run("从数据通道读取数据", func(t *testing.T) {
		r := require.New(t)

		// 创建一个KeyPressReader
		kpr := &KeyPressReader{
			Once:     &sync.Once{},
			Raw:      false,
			datachan: make(chan []byte, 1), // 创建带缓冲的通道
			Timeout:  1 * time.Second, 
		}

		// 向数据通道发送模拟数据
		testData := []byte("测试键盘输入")
		kpr.datachan <- testData

		// 读取数据
		buffer := make([]byte, 100)
		n, err := kpr.Read(buffer)

		// 验证结果
		r.NoError(err)
		r.Equal(len(testData), n)
		r.Equal(testData, buffer[:n])
	})
}

// 以下测试将检查Start和Stop方法的基本功能
// 注意：由于这些方法涉及到实际的终端操作，我们只进行基本的功能测试

func TestKeyPressReader_StartStop(t *testing.T) {
	// 这个测试可能在某些环境中不稳定，因为它依赖于终端功能
	// 如果测试失败，可以考虑跳过
	if testing.Short() {
		t.Skip("跳过需要终端支持的测试")
	}

	t.Run("Start和Stop基本功能", func(t *testing.T) {
		r := require.New(t)

		// 创建一个非Raw模式的KeyPressReader
		kpr := &KeyPressReader{
			Once: &sync.Once{},
			Raw:  false,
		}

		// 尝试启动
		err := kpr.Start()
		r.NoError(err)

		// 尝试停止
		err = kpr.Stop()
		r.NoError(err)
	})

	// Raw模式的测试可能在某些环境中不适用
	// 所以我们不测试Raw模式的Start和Stop
}


