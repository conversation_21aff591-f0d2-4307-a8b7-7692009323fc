// Author: chenjb
// Version: V1.0
// Date: 2025-07-25
// FilePath: /yaml_scan/pkg/rawhttp/util_test.go
// Description: rawhttp工具函数模块单元测试

package rawhttp

import (
	"bytes"
	"compress/gzip"
	"errors"
	"io"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
	"yaml_scan/pkg/rawhttp/client"
)

// TestStatusError_Error 测试StatusError的Error方法
func TestStatusError_Error(t *testing.T) {
	// 测试正常状态错误
	status := client.Status{Code: 404, Reason: "Not Found"}
	statusErr := &StatusError{Status: status}
	
	result := statusErr.Error()
	require.Equal(t, "404 Not Found", result, "状态错误字符串应该正确格式化")
	
	// 测试空原因短语
	status2 := client.Status{Code: 500, Reason: ""}
	statusErr2 := &StatusError{Status: status2}
	
	result2 := statusErr2.Error()
	require.Equal(t, "500 ", result2, "空原因短语应该正确处理")
}

// TestReadCloser 测试readCloser结构体
func TestReadCloser(t *testing.T) {
	// 创建测试数据
	testData := "测试数据"
	reader := strings.NewReader(testData)
	
	// 创建模拟的Closer
	mockCloser := &mockCloser{closed: false}
	
	// 创建readCloser实例
	rc := &readCloser{
		Reader: reader,
		Closer: mockCloser,
	}
	
	// 测试读取功能
	buf := make([]byte, len(testData))
	n, err := rc.Read(buf)
	require.NoError(t, err, "读取应该成功")
	require.Equal(t, len(testData), n, "读取字节数应该正确")
	require.Equal(t, testData, string(buf), "读取内容应该正确")
	
	// 测试关闭功能
	err = rc.Close()
	require.NoError(t, err, "关闭应该成功")
	require.True(t, mockCloser.closed, "Closer应该被调用")
}

// mockCloser 模拟的Closer实现
type mockCloser struct {
	closed bool
}

func (m *mockCloser) Close() error {
	m.closed = true
	return nil
}

// TestToRequest 测试toRequest函数
func TestToRequest(t *testing.T) {
	// 测试使用自定义原始字节
	customBytes := []byte("GET / HTTP/1.1\r\n\r\n")
	options := &Options{CustomRawBytes: customBytes}
	
	req := toRequest("GET", "/", nil, nil, nil, options)
	require.Equal(t, customBytes, req.RawBytes, "应该使用自定义原始字节")
	
	// 测试正常请求构建
	options2 := &Options{}
	headers := map[string][]string{
		"Content-Type": {"application/json"},
		"User-Agent":   {"test-agent"},
	}
	body := strings.NewReader("test body")
	
	req2 := toRequest("POST", "/api/test", []string{"param=value"}, headers, body, options2)
	require.Equal(t, "POST", req2.Method, "请求方法应该正确")
	require.Equal(t, "/api/test", req2.Path, "请求路径应该正确")
	require.Equal(t, []string{"param=value"}, req2.Query, "查询参数应该正确")
	require.Equal(t, client.HTTP_1_1, req2.Version, "HTTP版本应该是1.1")
	require.Equal(t, body, req2.Body, "请求体应该正确")
	
	// 验证头部转换
	expectedHeaders := []client.Header{
		{Key: "Content-Type", Value: "application/json"},
		{Key: "User-Agent", Value: "test-agent"},
	}
	require.ElementsMatch(t, expectedHeaders, req2.Headers, "头部应该正确转换")
	
	// 测试使用自定义头部
	customHeaders := []client.Header{
		{Key: "Custom-Header", Value: "custom-value"},
	}
	options3 := &Options{CustomHeaders: customHeaders}
	
	req3 := toRequest("GET", "/", nil, headers, nil, options3)
	require.Equal(t, customHeaders, req3.Headers, "应该使用自定义头部")
}

// TestToHTTPResponse 测试toHTTPResponse函数
func TestToHTTPResponse(t *testing.T) {
	// 创建模拟连接
	mockConn := &mockConn{}
	
	// 测试普通响应
	clientResp := &client.Response{
		Version: client.HTTP_1_1,
		Status:  client.Status{Code: 200, Reason: "OK"},
		Headers: []client.Header{
			{Key: "Content-Type", Value: "text/plain"},
			{Key: "Content-Length", Value: "11"},
		},
		Body: strings.NewReader("Hello World"),
	}
	
	httpResp, err := toHTTPResponse(mockConn, clientResp)
	require.NoError(t, err, "转换应该成功")
	require.Equal(t, 1, httpResp.ProtoMajor, "主版本号应该正确")
	require.Equal(t, 1, httpResp.ProtoMinor, "次版本号应该正确")
	require.Equal(t, "200 OK", httpResp.Status, "状态应该正确")
	require.Equal(t, 200, httpResp.StatusCode, "状态码应该正确")
	require.Equal(t, int64(11), httpResp.ContentLength, "内容长度应该正确")
	
	// 验证头部转换
	require.Equal(t, "text/plain", httpResp.Header.Get("Content-Type"), "Content-Type头部应该正确")
	require.Equal(t, "11", httpResp.Header.Get("Content-Length"), "Content-Length头部应该正确")
	
	// 验证响应体
	body, err := io.ReadAll(httpResp.Body)
	require.NoError(t, err, "读取响应体应该成功")
	require.Equal(t, "Hello World", string(body), "响应体内容应该正确")
}

// TestToHTTPResponse_Gzip 测试gzip压缩响应的处理
func TestToHTTPResponse_Gzip(t *testing.T) {
	// 创建gzip压缩的数据
	originalData := "这是一个测试数据，用于验证gzip解压缩功能"
	var buf bytes.Buffer
	gzipWriter := gzip.NewWriter(&buf)
	_, err := gzipWriter.Write([]byte(originalData))
	require.NoError(t, err, "写入gzip数据应该成功")
	err = gzipWriter.Close()
	require.NoError(t, err, "关闭gzip写入器应该成功")

	// 创建模拟连接
	mockConn := &mockConn{}

	// 创建带gzip编码的响应
	clientResp := &client.Response{
		Version: client.HTTP_1_1,
		Status:  client.Status{Code: 200, Reason: "OK"},
		Headers: []client.Header{
			{Key: "Content-Encoding", Value: "gzip"},
			{Key: "Content-Type", Value: "text/plain"},
		},
		Body: bytes.NewReader(buf.Bytes()),
	}

	httpResp, err := toHTTPResponse(mockConn, clientResp)
	require.NoError(t, err, "转换gzip响应应该成功")

	// 验证解压缩后的内容
	body, err := io.ReadAll(httpResp.Body)
	require.NoError(t, err, "读取解压缩响应体应该成功")
	require.Equal(t, originalData, string(body), "解压缩后的内容应该正确")
}

// TestToHTTPResponse_GzipError 测试gzip解压缩错误处理
func TestToHTTPResponse_GzipError(t *testing.T) {
	// 创建模拟连接
	mockConn := &mockConn{}

	// 创建无效的gzip数据
	clientResp := &client.Response{
		Version: client.HTTP_1_1,
		Status:  client.Status{Code: 200, Reason: "OK"},
		Headers: []client.Header{
			{Key: "Content-Encoding", Value: "gzip"},
		},
		Body: strings.NewReader("invalid gzip data"),
	}

	_, err := toHTTPResponse(mockConn, clientResp)
	require.Error(t, err, "无效gzip数据应该返回错误")
}

// TestToHeaders 测试toHeaders函数
func TestToHeaders(t *testing.T) {
	// 测试空映射
	result := toHeaders(nil)
	require.Nil(t, result, "空映射应该返回nil")

	// 测试正常头部映射
	headers := map[string][]string{
		"Content-Type": {"application/json"},
		"Accept":       {"text/html", "application/xml"},
		"User-Agent":   {"test-agent"},
	}

	result = toHeaders(headers)
	require.Len(t, result, 4, "应该有4个头部项")

	// 验证头部转换正确性
	expectedHeaders := []client.Header{
		{Key: "Content-Type", Value: "application/json"},
		{Key: "Accept", Value: "text/html"},
		{Key: "Accept", Value: "application/xml"},
		{Key: "User-Agent", Value: "test-agent"},
	}
	require.ElementsMatch(t, expectedHeaders, result, "头部转换应该正确")
}

// TestFromHeaders 测试fromHeaders函数
func TestFromHeaders(t *testing.T) {
	// 测试nil输入
	result := fromHeaders(nil)
	require.Nil(t, result, "nil输入应该返回nil")

	// 测试正常头部数组
	headers := []client.Header{
		{Key: "Content-Type", Value: "application/json"},
		{Key: "Accept", Value: "text/html"},
		{Key: "Accept", Value: "application/xml"},
		{Key: "User-Agent", Value: "test-agent"},
	}

	result = fromHeaders(headers)
	require.Len(t, result, 3, "应该有3个不同的头部键")

	// 验证转换结果
	require.Equal(t, []string{"application/json"}, result["Content-Type"], "Content-Type应该正确")
	require.Equal(t, []string{"text/html", "application/xml"}, result["Accept"], "Accept应该包含多个值")
	require.Equal(t, []string{"test-agent"}, result["User-Agent"], "User-Agent应该正确")
}

// TestHeaderValue 测试headerValue函数
func TestHeaderValue(t *testing.T) {
	headers := map[string][]string{
		"Content-Type": {"application/json"},
		"Accept":       {"text/html", "application/xml"},
		"Empty":        {},
	}

	// 测试单个值
	result := headerValue(headers, "Content-Type")
	require.Equal(t, "application/json", result, "单个值应该正确返回")

	// 测试多个值
	result = headerValue(headers, "Accept")
	require.Equal(t, "text/html application/xml", result, "多个值应该用空格连接")

	// 测试不存在的头部
	result = headerValue(headers, "NonExistent")
	require.Equal(t, "", result, "不存在的头部应该返回空字符串")

	// 测试空值数组
	result = headerValue(headers, "Empty")
	require.Equal(t, "", result, "空值数组应该返回空字符串")
}

// TestFirstErr 测试firstErr函数
func TestFirstErr(t *testing.T) {
	err1 := errors.New("第一个错误")
	err2 := errors.New("第二个错误")

	// 测试第一个错误不为nil
	result := firstErr(err1, err2)
	require.Equal(t, err1, result, "应该返回第一个错误")

	// 测试第一个错误为nil，第二个不为nil
	result = firstErr(nil, err2)
	require.Equal(t, err2, result, "应该返回第二个错误")

	// 测试两个错误都为nil
	result = firstErr(nil, nil)
	require.Nil(t, result, "两个错误都为nil时应该返回nil")

	// 测试两个错误都不为nil
	result = firstErr(err1, err2)
	require.Equal(t, err1, result, "应该优先返回第一个错误")
}

// TestDumpRequestRaw 测试DumpRequestRaw函数
func TestDumpRequestRaw(t *testing.T) {
	// 测试使用自定义原始字节
	customBytes := []byte("GET / HTTP/1.1\r\nHost: example.com\r\n\r\n")
	options := &Options{CustomRawBytes: customBytes}

	result, err := DumpRequestRaw("GET", "http://example.com", "", nil, nil, options)
	require.NoError(t, err, "使用自定义原始字节应该成功")
	require.Equal(t, customBytes, result, "应该返回自定义原始字节")

	// 测试基本GET请求
	options2 := &Options{}
	result, err = DumpRequestRaw("GET", "http://example.com/path", "", nil, nil, options2)
	require.NoError(t, err, "基本GET请求应该成功")

	expected := "GET /path HTTP/1.1\r\nHost: example.com\r\n\r\n"
	require.Equal(t, expected, string(result), "基本GET请求格式应该正确")

	// 测试带查询参数的请求
	result, err = DumpRequestRaw("GET", "http://example.com/path?param1=value1&param2=value2", "", nil, nil, options2)
	require.NoError(t, err, "带查询参数的请求应该成功")

	expected = "GET /path?param1=value1&param2=value2 HTTP/1.1\r\nHost: example.com\r\n\r\n"
	require.Equal(t, expected, string(result), "查询参数应该正确包含")

	// 测试带自定义头部的请求
	headers := map[string][]string{
		"Content-Type": {"application/json"},
		"User-Agent":   {"test-agent"},
	}
	result, err = DumpRequestRaw("POST", "http://example.com/api", "", headers, nil, options2)
	require.NoError(t, err, "带头部的请求应该成功")

	resultStr := string(result)
	require.Contains(t, resultStr, "POST /api HTTP/1.1", "请求行应该正确")
	require.Contains(t, resultStr, "Host: example.com", "Host头部应该存在")
	require.Contains(t, resultStr, "Content-Type: application/json", "Content-Type头部应该存在")
	require.Contains(t, resultStr, "User-Agent: test-agent", "User-Agent头部应该存在")

	// 测试带请求体的请求
	body := strings.NewReader("test request body")
	result, err = DumpRequestRaw("POST", "http://example.com/api", "", headers, body, options2)
	require.NoError(t, err, "带请求体的请求应该成功")

	resultStr = string(result)
	require.Contains(t, resultStr, "test request body", "请求体应该包含在结果中")

	// 测试自定义URI路径
	result, err = DumpRequestRaw("GET", "http://example.com/original", "/custom/path", nil, nil, options2)
	require.NoError(t, err, "自定义URI路径应该成功")

	expected = "GET /custom/path HTTP/1.1\r\nHost: example.com\r\n\r\n"
	require.Equal(t, expected, string(result), "应该使用自定义URI路径")

	// 测试已存在Host头部的情况
	headersWithHost := map[string][]string{
		"Host": {"custom.host.com"},
	}
	result, err = DumpRequestRaw("GET", "http://example.com/", "", headersWithHost, nil, options2)
	require.NoError(t, err, "已存在Host头部的请求应该成功")

	expected = "GET / HTTP/1.1\r\nHost: custom.host.com\r\n\r\n"
	require.Equal(t, expected, string(result), "应该保持现有的Host头部")
}

// TestDumpRequestRaw_InvalidURL 测试DumpRequestRaw函数处理无效URL
func TestDumpRequestRaw_InvalidURL(t *testing.T) {
	options := &Options{}

	_, err := DumpRequestRaw("GET", "invalid-url", "", nil, nil, options)
	require.Error(t, err, "无效URL应该返回错误")
}

// TestDumpRequestRaw_EmptyPath 测试DumpRequestRaw函数处理空路径
func TestDumpRequestRaw_EmptyPath(t *testing.T) {
	options := &Options{}

	result, err := DumpRequestRaw("GET", "http://example.com", "", nil, nil, options)
	require.NoError(t, err, "空路径应该成功处理")

	expected := "GET / HTTP/1.1\r\nHost: example.com\r\n\r\n"
	require.Equal(t, expected, string(result), "空路径应该默认为根路径")
}

// TestDumpRequestRaw_AutomaticContentLength 测试自动Content-Length计算
func TestDumpRequestRaw_AutomaticContentLength(t *testing.T) {
	options := &Options{}
	body := strings.NewReader("test body")

	// 创建一个支持自动Content-Length的请求
	headers := map[string][]string{
		"Content-Type": {"text/plain"},
	}

	result, err := DumpRequestRaw("POST", "http://example.com/", "", headers, body, options)
	require.NoError(t, err, "带请求体的请求应该成功")

	resultStr := string(result)
	require.Contains(t, resultStr, "test body", "请求体应该包含在结果中")
}

// mockConn 模拟连接实现
type mockConn struct{}

func (m *mockConn) Close() error {
	return nil
}
