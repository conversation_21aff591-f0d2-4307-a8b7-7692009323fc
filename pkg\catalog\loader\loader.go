// Author: chenjb
// Version: V1.0
// Date: 2025-06-17 14:11:25
// FilePath: /yaml_scan/pkg/catalog/loader/loader.go
// Description:
package loader

import (
	"fmt"
	"os"
	"yaml_scan/pkg/catalog"
	"yaml_scan/pkg/model/types/severity"
	"yaml_scan/pkg/protocols"
	"yaml_scan/pkg/templates"
	"yaml_scan/pkg/catalog/filter"
	"yaml_scan/pkg/types"
	templateTypes "yaml_scan/pkg/templates/types"
	stringsutil "yaml_scan/utils/strings"
	urlutil "yaml_scan/utils/url"
)

const (
	httpPrefix  = "http://"
	httpsPrefix = "https://"
	AuthStoreId = "auth_store"
)

// Config contains the configuration options for the loader
type Config struct {
	StoreId                  string // used to set store id (optional)
	Templates                []string
	TemplateURLs             []string
	Workflows                []string
	WorkflowURLs             []string
	ExcludeTemplates         []string
	IncludeTemplates         []string
	RemoteTemplateDomainList []string
	AITemplatePrompt         string

	Tags              []string
	ExcludeTags       []string
	Protocols         templateTypes.ProtocolTypes
	ExcludeProtocols  templateTypes.ProtocolTypes
	Authors           []string
	Severities        severity.Severities
	ExcludeSeverities severity.Severities
	IncludeTags       []string
	IncludeIds        []string
	ExcludeIds        []string
	IncludeConditions []string

	Catalog         catalog.Catalog
	ExecutorOptions protocols.ExecutorOptions
}

// Store is a storage for loaded nuclei templates
type Store struct {
	id             string // id of the store (optional)
	tagFilter      *templates.TagFilter
	pathFilter     *filter.PathFilter
	config         *Config
	finalTemplates []string
	finalWorkflows []string

	templates []*templates.Template
	workflows []*templates.Template

	preprocessor templates.Preprocessor

	// NotFoundCallback is called for each not found template
	// This overrides error handling for not found templates
	NotFoundCallback func(template string) bool
}


// New creates a new template store based on provided configuration
func New(cfg *Config) (*Store, error) {
	tagFilter, err := templates.NewTagFilter(&templates.TagFilterConfig{
		Tags:              cfg.Tags,
		ExcludeTags:       cfg.ExcludeTags,
		Authors: <AUTHORS>
		Severities:        cfg.Severities,
		ExcludeSeverities: cfg.ExcludeSeverities,
		IncludeTags:       cfg.IncludeTags,
		IncludeIds:        cfg.IncludeIds,
		ExcludeIds:        cfg.ExcludeIds,
		Protocols:         cfg.Protocols,
		ExcludeProtocols:  cfg.ExcludeProtocols,
		IncludeConditions: cfg.IncludeConditions,
	})
	if err != nil {
		return nil, err
	}

	store := &Store{
		id:        cfg.StoreId,
		config:    cfg,
		tagFilter: tagFilter,
		pathFilter: filter.NewPathFilter(&filter.PathFilterConfig{
			IncludedTemplates: cfg.IncludeTemplates,
			ExcludedTemplates: cfg.ExcludeTemplates,
		}, cfg.Catalog),
		finalTemplates: cfg.Templates,
		finalWorkflows: cfg.Workflows,
	}

	// Do a check to see if we have URLs in templates flag, if so
	// we need to processs them separately and remove them from the initial list
	var templatesFinal []string
	for _, template := range cfg.Templates {
		// TODO: Add and replace this with urlutil.IsURL() helper
		if stringsutil.HasPrefixAny(template, httpPrefix, httpsPrefix) {
			cfg.TemplateURLs = append(cfg.TemplateURLs, template)
		} else {
			templatesFinal = append(templatesFinal, template)
		}
	}

	// fix editor paths
	remoteTemplates := []string{}
	for _, v := range cfg.TemplateURLs {
		if _, err := urlutil.Parse(v); err == nil {
			remoteTemplates = append(remoteTemplates, handleTemplatesEditorURLs(v))
		} else {
			templatesFinal = append(templatesFinal, v) // something went wrong, treat it as a file
		}
	}
	cfg.TemplateURLs = remoteTemplates
	store.finalTemplates = templatesFinal

	urlBasedTemplatesProvided := len(cfg.TemplateURLs) > 0 || len(cfg.WorkflowURLs) > 0
	if urlBasedTemplatesProvided {
		remoteTemplates, remoteWorkflows, err := getRemoteTemplatesAndWorkflows(cfg.TemplateURLs, cfg.WorkflowURLs, cfg.RemoteTemplateDomainList)
		if err != nil {
			return store, err
		}
		store.finalTemplates = append(store.finalTemplates, remoteTemplates...)
		store.finalWorkflows = append(store.finalWorkflows, remoteWorkflows...)
	}

	// Handle AI template generation if prompt is provided
	if len(cfg.AITemplatePrompt) > 0 {
		aiTemplates, err := getAIGeneratedTemplates(cfg.AITemplatePrompt, cfg.ExecutorOptions.Options)
		if err != nil {
			return nil, err
		}
		store.finalTemplates = append(store.finalTemplates, aiTemplates...)
	}

	// Handle a dot as the current working directory
	if len(store.finalTemplates) == 1 && store.finalTemplates[0] == "." {
		currentDirectory, err := os.Getwd()
		if err != nil {
			return nil, fmt.Errorf("could not get current directory %s", err)
		}
		store.finalTemplates = []string{currentDirectory}
	}

	// Handle a case with no templates or workflows, where we use base directory
	if len(store.finalTemplates) == 0 && len(store.finalWorkflows) == 0 && !urlBasedTemplatesProvided {
		store.finalTemplates = []string{config.DefaultConfig.TemplatesDirectory}
	}

	return store, nil
}

// NewConfig returns a new loader config
func NewConfig(options *types.Options, catalog catalog.Catalog, executerOpts protocols.ExecutorOptions) *Config {
	loaderConfig := Config{
		Templates:                options.Templates,
		Workflows:                options.Workflows,
		RemoteTemplateDomainList: options.RemoteTemplateDomainList,
		TemplateURLs:             options.TemplateURLs,
		WorkflowURLs:             options.WorkflowURLs,
		ExcludeTemplates:         options.ExcludedTemplates,
		Tags:                     options.Tags,
		ExcludeTags:              options.ExcludeTags,
		IncludeTemplates:         options.IncludeTemplates,
		Authors: <AUTHORS>
		Severities:               options.Severities,
		ExcludeSeverities:        options.ExcludeSeverities,
		IncludeTags:              options.IncludeTags,
		IncludeIds:               options.IncludeIds,
		ExcludeIds:               options.ExcludeIds,
		Protocols:                options.Protocols,
		ExcludeProtocols:         options.ExcludeProtocols,
		IncludeConditions:        options.IncludeConditions,
		Catalog:                  catalog,
		ExecutorOptions:          executerOpts,
		AITemplatePrompt:         options.AITemplatePrompt,
	}
	loaderConfig.RemoteTemplateDomainList = append(loaderConfig.RemoteTemplateDomainList, TrustedTemplateDomains...)
	return &loaderConfig
}

// LoadTemplates takes a list of templates and returns paths for them
func (store *Store) LoadTemplates(templatesList []string) []*templates.Template {
	return store.LoadTemplatesWithTags(templatesList, nil)
}

// LoadTemplatesOnlyMetadata loads only the metadata of the templates
func (store *Store) LoadTemplatesOnlyMetadata() error {
	templatePaths, errs := store.config.Catalog.GetTemplatesPath(store.finalTemplates)
	store.logErroredTemplates(errs)

	filteredTemplatePaths := store.pathFilter.Match(templatePaths)

	validPaths := make(map[string]struct{})
	for templatePath := range filteredTemplatePaths {
		loaded, err := store.config.ExecutorOptions.Parser.LoadTemplate(templatePath, store.tagFilter, nil, store.config.Catalog)
		if loaded || store.pathFilter.MatchIncluded(templatePath) {
			validPaths[templatePath] = struct{}{}
		}
		if err != nil {
			if strings.Contains(err.Error(), templates.ErrExcluded.Error()) {
				stats.Increment(templates.TemplatesExcludedStats)
				if config.DefaultConfig.LogAllEvents {
					gologger.Print().Msgf("[%v] %v\n", aurora.Yellow("WRN").String(), err.Error())
				}
				continue
			}
			gologger.Warning().Msg(err.Error())
		}
	}
	parserItem, ok := store.config.ExecutorOptions.Parser.(*templates.Parser)
	if !ok {
		return errors.New("invalid parser")
	}
	templatesCache := parserItem.Cache()

	for templatePath := range validPaths {
		template, _, _ := templatesCache.Has(templatePath)

		if len(template.RequestsHeadless) > 0 && !store.config.ExecutorOptions.Options.Headless {
			continue
		}

		if len(template.RequestsCode) > 0 && !store.config.ExecutorOptions.Options.EnableCodeTemplates {
			continue
		}

		if template.IsFuzzing() && !store.config.ExecutorOptions.Options.DAST {
			continue
		}

		if template.SelfContained && !store.config.ExecutorOptions.Options.EnableSelfContainedTemplates {
			continue
		}

		if template.HasFileProtocol() && !store.config.ExecutorOptions.Options.EnableFileTemplates {
			continue
		}

		if template != nil {
			template.Path = templatePath
			store.templates = append(store.templates, template)
		}
	}
	return nil
}


// Templates returns all the templates in the store
func (store *Store) Templates() []*templates.Template {
	return store.templates
}


// ReadTemplateFromURI should only be used for viewing templates
// and should not be used anywhere else like loading and executing templates
// there is no sandbox restriction here
func (store *Store) ReadTemplateFromURI(uri string, remote bool) ([]byte, error) {
	if stringsutil.HasPrefixAny(uri, httpPrefix, httpsPrefix) && remote {
		uri = handleTemplatesEditorURLs(uri)
		remoteTemplates, _, err := getRemoteTemplatesAndWorkflows([]string{uri}, nil, store.config.RemoteTemplateDomainList)
		if err != nil || len(remoteTemplates) == 0 {
			return nil, errorutil.NewWithErr(err).Msgf("Could not load template %s: got %v", uri, remoteTemplates)
		}
		resp, err := retryablehttp.Get(remoteTemplates[0])
		if err != nil {
			return nil, err
		}
		defer resp.Body.Close()
		return io.ReadAll(resp.Body)
	} else {
		return os.ReadFile(uri)
	}
}


// ValidateTemplates takes a list of templates and validates them
// erroring out on discovering any faulty templates.
func (store *Store) ValidateTemplates() error {
	templatePaths, errs := store.config.Catalog.GetTemplatesPath(store.finalTemplates)
	store.logErroredTemplates(errs)
	workflowPaths, errs := store.config.Catalog.GetTemplatesPath(store.finalWorkflows)
	store.logErroredTemplates(errs)

	filteredTemplatePaths := store.pathFilter.Match(templatePaths)
	filteredWorkflowPaths := store.pathFilter.Match(workflowPaths)

	if store.areTemplatesValid(filteredTemplatePaths) && store.areWorkflowsValid(filteredWorkflowPaths) {
		return nil
	}
	return errors.New("errors occurred during template validation")
}

// Load loads all the templates from a store, performs filtering and returns
// the complete compiled templates for a nuclei execution configuration.
func (store *Store) Load() {
	store.templates = store.LoadTemplates(store.finalTemplates)
	store.workflows = store.LoadWorkflows(store.finalWorkflows)
}

// IsHTTPBasedProtocolUsed returns true if http/headless protocol is being used for
// any templates.
func IsHTTPBasedProtocolUsed(store *Store) bool {
	templates := append(store.Templates(), store.Workflows()...)

	for _, template := range templates {
		if len(template.RequestsHTTP) > 0 || len(template.RequestsHeadless) > 0 {
			return true
		}
		if len(template.Workflows) > 0 {
			if workflowContainsProtocol(template.Workflows) {
				return true
			}
		}
	}
	return false
}