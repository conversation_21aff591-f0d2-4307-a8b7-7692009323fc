// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-18 17:55:22
// FilePath: /yaml_scan/pkg/clistats/clistats_test.go
// Description: 
package clistats

import (
	"context"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestNew 测试使用默认选项创建新的统计客户端
func TestNew(t *testing.T) {
	// 创建新的统计客户端
	stats, err := New()
	
	// 验证创建成功且无错误
	require.NoError(t, err, "创建统计客户端不应返回错误")
	require.NotNil(t, stats, "统计客户端不应为空")
	
	// 验证默认选项设置正确
	require.Equal(t, &DefaultOptions, stats.Options, "应使用默认选项")
	require.Equal(t, 63636, stats.Options.ListenPort, "默认端口应为63636")
	require.True(t, stats.Options.Web, "默认应启用Web接口")
	
	// 验证内部映射已初始化
	require.NotNil(t, stats.counters, "计数器映射应已初始化")
	require.NotNil(t, stats.static, "静态字段映射应已初始化")
	require.NotNil(t, stats.dynamic, "动态字段映射应已初始化")
}

// TestNewWithOptions 测试使用自定义选项创建新的统计客户端
func TestNewWithOptions(t *testing.T) {
	// 创建自定义选项
	customOptions := &Options{
		ListenPort: 8080,
		Web:        false,
	}
	
	// 使用自定义选项创建客户端
	stats, err := NewWithOptions(context.Background(), customOptions)
	
	// 验证创建成功且无错误
	require.NoError(t, err, "使用自定义选项创建统计客户端不应返回错误")
	require.NotNil(t, stats, "统计客户端不应为空")
	
	// 验证自定义选项设置正确
	require.Equal(t, customOptions, stats.Options, "应使用自定义选项")
	require.Equal(t, 8080, stats.Options.ListenPort, "端口应为8080")
	require.False(t, stats.Options.Web, "Web接口应禁用")
}

// TestStartStop 测试启动和停止统计客户端
func TestStartStop(t *testing.T) {
	// 创建自定义选项，启用Web但使用高端口号以避免冲突
	customOptions := &Options{
		ListenPort: 54321,
		Web:        true,
	}
	
	// 创建统计客户端
	stats, err := NewWithOptions(context.Background(), customOptions)
	require.NoError(t, err, "创建统计客户端不应返回错误")
	
	// 启动客户端
	err = stats.Start()
	require.NoError(t, err, "启动统计客户端不应返回错误")
	require.NotNil(t, stats.httpServer, "HTTP服务器应已创建")
	
	// 确保启动成功后HTTP服务器已运行
	// 等待短暂时间确保服务器启动
	time.Sleep(100 * time.Millisecond)
	
	// 测试重复启动
	err = stats.Start()
	require.Error(t, err, "重复启动应返回错误")
	require.Contains(t, err.Error(), "already started", "错误消息应包含'already started'")
	
	// 停止客户端
	err = stats.Stop()
	require.NoError(t, err, "停止统计客户端不应返回错误")
	require.Nil(t, stats.httpServer, "HTTP服务器应已关闭")
}

// TestMetricsHandler 测试指标处理器生成正确的JSON响应
func TestMetricsHandler(t *testing.T) {
	// 创建统计客户端
	stats, err := NewWithOptions(context.Background(), &Options{Web: false})
	require.NoError(t, err, "创建统计客户端不应返回错误")
	
	// 添加一些测试数据
	stats.AddCounter("requests", 100)
	stats.AddCounter("total", 500)
	startTime := time.Now().Add(-10 * time.Second) // 10秒前
	stats.AddStatic("startedAt", startTime)
	
	// 创建响应记录器和测试请求
	rec := &testResponseRecorder{
		headers: make(http.Header),
	}
	req, err := http.NewRequest("GET", "/metrics", nil)
	require.NoError(t, err, "创建HTTP请求不应返回错误")
	
	// 调用处理器
	stats.metricsHandler(rec, req)
	// 验证响应
	require.NotEmpty(t, rec.body, "响应体不应为空")
	require.Contains(t, rec.body, `"requests":100`, "响应应包含请求计数")
	require.Contains(t, rec.body, `"total":500`, "响应应包含总计数")
	//require.Contains(t, rec.body, `"percent":20`, "响应应包含正确的百分比")
	require.Contains(t, rec.body, `"duration":"0:00:10"`, "响应应包含正确的持续时间")
	// require.Contains(t, rec.body, `"rps":9`, "响应应包含正确的每秒请求数")
}

// testResponseRecorder 是一个简单的测试用HTTP响应记录器
type testResponseRecorder struct {
	statusCode int
	body       string
	headers    http.Header
}

func (r *testResponseRecorder) Header() http.Header {
	return r.headers
}

func (r *testResponseRecorder) Write(b []byte) (int, error) {
	r.body += string(b)
	return len(b), nil
}

func (r *testResponseRecorder) WriteHeader(statusCode int) {
	r.statusCode = statusCode
} 

