//
// Author: chenjb
// Version: V1.0
// Date: 2025-07-22 17:30:39
// FilePath: /yaml_scan/pkg/rawhttp/client/reader.go
// Description: HTTP响应读取器实现，提供HTTP协议解析功能

// Package client HTTP响应读取模块
package client

import (
	"bufio"   // 缓冲I/O包
	"bytes"   // 字节操作包
	"fmt"     // 格式化输出包
	"io"      // 输入输出接口包
	"strconv" // 字符串转换包
)

// reader HTTP响应读取器结构体
// 封装了bufio.Reader，提供HTTP协议特定的读取方法
type reader struct {
	*bufio.Reader // 嵌入缓冲读取器，提供基础的缓冲读取功能
}

// ReadVersion 从网络读取HTTP版本字符串
// 返回:
//   Version: 解析得到的HTTP版本对象
//   error: 读取或解析错误，成功时为nil
// 功能: 解析HTTP版本字符串（如"HTTP/1.1 "），支持HTTP/1.x、HTTP/2、HTTP/3
func (r *reader) ReadVersion() (Version, error) {
	var major, minor int
	// 逐字节读取并解析HTTP版本字符串
	for pos := 0; pos < len("HTTP/x.x "); pos++ {
		c, err := r.ReadByte()
		if err != nil {
			return invalidVersion, err // 读取失败返回无效版本
		}
		switch pos {
		case 0: // 期望字符'H'
			if c != 'H' {
				return readVersionErr(pos, 'H', c)
			}
		case 1, 2: // 期望字符'T'
			if c != 'T' {
				return readVersionErr(pos, 'T', c)
			}
		case 3: // 期望字符'P'
			if c != 'P' {
				return readVersionErr(pos, 'P', c)
			}
		case 4: // 期望字符'/'
			if c != '/' {
				return readVersionErr(pos, '/', c)
			}
		case 5: // 主版本号（0-9）
			switch c {
			case '0', '1', '2', '3', '4', '5', '6', '7', '8', '9':
				major = int(int(c) - 0x30) // 将ASCII字符转换为数字
			}
		case 6: // HTTP/1.x的'.'或HTTP/2+的空格
			// For HTTP/2 and HTTP/3 there is no any '.', just do nothing
			// 对于HTTP/2和HTTP/3没有'.'，直接处理
			if c != '.' && major == 1 {
				return readVersionErr(pos, '.', c)
			}
			if c != ' ' && (major == 2 || major == 3) {
				return readVersionErr(pos, ' ', c)
			}
			if c == ' ' && (major == 2 || major == 3) {
				return Version{Major: major, Minor: minor}, nil // HTTP/2+版本解析完成
			}
		case 7: // 次版本号或空格
			switch c {
			case '0', '1', '2', '3', '4', '5', '6', '7', '8', '9':
				minor = int(int(c) - 0x30) // 将ASCII字符转换为数字
			case ' ':
				// HTTP/2 case - HTTP/2情况
				minor = 0
			}
		case 8: // 期望空格结束
			if c != ' ' {
				return readVersionErr(pos, ' ', c)
			}
		}
	}
	return Version{Major: major, Minor: minor}, nil // 返回解析得到的版本
}

// invalidVersion 无效版本常量，用于错误情况
var invalidVersion Version

// readVersionErr 生成版本读取错误
// 参数:
//   pos: 错误发生的位置
//   expected: 期望的字节
//   got: 实际读取到的字节
// 返回:
//   Version: 无效版本对象
//   error: 格式化的错误信息
// 功能: 创建详细的版本解析错误信息
func readVersionErr(pos int, expected, got byte) (Version, error) {
	return invalidVersion, fmt.Errorf("ReadVersion: expected %q, got %q at position %v", expected, got, pos)
}

// maxStatusCodeLength 状态码的最大长度限制（25字符）
// 用于防止读取过长的状态码导致内存问题
const maxStatusCodeLength = 25

// ReadStatusCode 从网络读取HTTP状态码
// 返回:
//   int: 解析得到的状态码数字
//   error: 读取或解析错误，成功时为nil
// 功能: 读取HTTP状态码，支持任意大小的数字和非RFC标准状态码
// 特性:
//   - 接受任意大小的数字
//   - 支持非RFC标准状态码
func (r *reader) ReadStatusCode() (int, error) {
	var pattern string
	// read until: space, \r - 读取直到遇到空格或回车符
	for p := 0; p < maxStatusCodeLength; p++ {
		c, err := r.ReadByte()
		if err != nil {
			return 0, err // 读取失败返回错误
		}
		if c == ' ' || c == '\r' {
			break // 遇到分隔符停止读取
		}
		pattern += string(c) // 累积状态码字符
	}
	return strconv.Atoi(pattern) // 将字符串转换为整数
}

// ReadStatusLine 读取HTTP状态行
// 返回:
//   Version: HTTP版本对象
//   int: 状态码
//   string: 状态消息
//   error: 读取或解析错误，成功时为nil
// 功能: 完整读取HTTP响应的状态行，包括版本、状态码和状态消息
func (r *reader) ReadStatusLine() (Version, int, string, error) {
	// 读取HTTP版本
	version, err := r.ReadVersion()
	if err != nil {
		return Version{}, 0, "", err
	}
	// 读取状态码
	code, err := r.ReadStatusCode()
	if err != nil {
		return Version{}, 0, "", err
	}
	// 读取状态消息（行的剩余部分）
	msg, _, err := r.ReadLine()
	return version, code, string(msg), err
}

// ReadHeader 读取HTTP头部
// 返回:
//   string: 头部名称（键）
//   string: 头部值
//   bool: 是否已读取完所有头部（遇到空行时为true）
//   error: 读取或解析错误，成功时为nil
// 功能: 读取单个HTTP头部，解析键值对格式
func (r *reader) ReadHeader() (string, string, bool, error) {
	// 读取一行数据
	line, err := r.readLine()
	if err != nil {
		return "", "", false, err
	}
	// 检查是否为空行（头部结束标志）
	if line := string(line); line == "\r\n" || line == "\n" {
		return "", "", true, nil // 返回true表示头部读取完成
	}
	// 按冒号分割头部行，最多分割为2部分
	v := bytes.SplitN(line, []byte(":"), 2)
	if len(v) != 2 {
		return "", "", false, fmt.Errorf("invalid header line: %q", line)
	}
	// 返回去除空白的头部名称和值
	return string(bytes.TrimSpace(v[0])), string(bytes.TrimSpace(v[1])), false, nil
}

// ReadBody 返回响应体读取器
// 返回:
//   io.Reader: 响应体读取器（返回自身）
// 功能: 提供响应体的读取接口，直接返回reader自身
func (r *reader) ReadBody() io.Reader {
	return r // 返回自身作为响应体读取器
}

// readLine 读取以\r\n结尾的一行数据
// 返回:
//   []byte: 读取到的行数据（包含换行符）
//   error: 读取错误，成功时为nil
// 功能: 内部辅助方法，读取一行数据直到遇到换行符
func (r *reader) readLine() ([]byte, error) {
	return r.ReadBytes('\n') // 读取直到遇到换行符
}

