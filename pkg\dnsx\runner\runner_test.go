// Author: chenjb
// Version: V1.0
// Date: 2025-06-23 17:24:05
// FilePath: /yaml_scan/pkg/dnsx/runner/runner_test.go
// Description:
package runner

import (
	"io"
	"os"
	"testing"
	"yaml_scan/pkg/gologger"
	"yaml_scan/pkg/retryabledns"

	"github.com/stretchr/testify/require"
)

// setupTestRunner 创建用于测试的Runner实例
func setupTestRunner(t *testing.T) *Runner {
	// 设置基本选项
	options := &Options{
		Threads:           2,
		Retries:           2,
		HostsFile:         false,
		Silent:            true,
		WildcardThreshold: 2,
	}

	// 创建Runner实例
	runner, err := New(options)
	require.NoError(t, err, "创建Runner实例不应有错误")
	require.NotNil(t, runner, "Runner实例不应为nil")

	return runner
}

// TestNew 测试创建新的Runner实例
func TestNew(t *testing.T) {
	// 测试基本选项
	options := &Options{
		Threads:           10,
		Retries:           3,
		HostsFile:         true,
		WildcardThreshold: 5,
		Resolvers:         "*******,*******",
		A:                 true,
		AAAA:              true,
	}

	runner, err := New(options)
	require.NoError(t, err, "创建Runner实例不应有错误")
	require.NotNil(t, runner, "Runner实例不应为nil")
	require.Equal(t, options, runner.options, "Runner应存储传入的选项")
	require.NotNil(t, runner.dnsx, "DNS客户端不应为nil")
	require.NotNil(t, runner.hm, "混合映射不应为nil")
	require.NotNil(t, runner.limiter, "速率限制器不应为nil")

	// 测试自定义解析器
	resolversOption := &Options{
		Resolvers: "*******,*******",
		Threads:   1,
		Retries:   3,
	}
	runnerWithResolvers, err := New(resolversOption)
	require.NoError(t, err, "使用解析器创建Runner不应有错误")
	require.NotNil(t, runnerWithResolvers.dnsx, "DNS客户端不应为nil")
	require.Equal(t, 2, len(runnerWithResolvers.dnsx.Options.BaseResolvers), "应有2个解析器")

	// 测试解析器文件
	tmpFile, err := os.CreateTemp("", "resolvers-*.txt")
	require.NoError(t, err, "创建临时文件不应有错误")
	defer os.Remove(tmpFile.Name())
	_, err = io.WriteString(tmpFile, "*******\n*******\n")
	require.NoError(t, err, "写入临时文件不应有错误")
	tmpFile.Close()

	resolversFileOption := &Options{
		Resolvers: tmpFile.Name(),
		Threads:   1,
		Retries:   3,
	}
	runnerWithResolversFile, err := New(resolversFileOption)
	require.NoError(t, err, "使用解析器文件创建Runner不应有错误")
	require.NotNil(t, runnerWithResolversFile.dnsx, "DNS客户端不应为nil")
	require.Equal(t, 2, len(runnerWithResolversFile.dnsx.Options.BaseResolvers), "应有2个解析器")
}

// TestAddHostsToHMapFromList 测试从列表添加主机到混合映射
func TestAddHostsToHMapFromList(t *testing.T) {
	runner := setupTestRunner(t)

	// 测试添加主机列表
	hosts := []string{"example.com", "test.com", "example.com"} // 故意重复
	count := runner.addHostsToHMapFromList(hosts)
	require.Equal(t, 2, count, "应只添加2个唯一主机")

	// 验证主机已添加到映射
	_, ok := runner.hm.Get("example.com")
	require.True(t, ok, "example.com应该在映射中")
	_, ok = runner.hm.Get("test.com")
	require.True(t, ok, "test.com应该在映射中")
}

// TestNormalize 测试字符串标准化
func TestNormalize(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"example.com", "example.com"},
		{"  example.com  ", "example.com"},
		{"", ""},
		{"\texample.com\n", "example.com"},
	}

	for _, test := range tests {
		result := normalize(test.input)
		require.Equal(t, test.expected, result, "normalize(%q)应返回%q", test.input, test.expected)
	}
}

// TestPreProcessArgument 测试参数预处理
func TestPreProcessArgument(t *testing.T) {
	runner := setupTestRunner(t)

	// 测试内联内容
	inlineArg := "example.com,test.com"
	inlineCh, err := runner.preProcessArgument(inlineArg)
	require.NoError(t, err, "处理内联内容不应有错误")

	var inlineResults []string
	for item := range inlineCh {
		inlineResults = append(inlineResults, item)
	}
	require.Equal(t, 2, len(inlineResults), "应解析2个项目")
	require.Contains(t, inlineResults, "example.com", "结果应包含example.com")
	require.Contains(t, inlineResults, "test.com", "结果应包含test.com")

	// 测试文件内容
	tmpFile, err := os.CreateTemp("", "domains-*.txt")
	require.NoError(t, err, "创建临时文件不应有错误")
	defer os.Remove(tmpFile.Name())
	_, err = io.WriteString(tmpFile, "file1.com\nfile2.com\n")
	require.NoError(t, err, "写入临时文件不应有错误")
	tmpFile.Close()

	fileCh, err := runner.preProcessArgument(tmpFile.Name())
	require.NoError(t, err, "处理文件不应有错误")

	var fileResults []string
	for item := range fileCh {
		fileResults = append(fileResults, item)
	}
	require.Equal(t, 2, len(fileResults), "应解析2个项目")
	require.Contains(t, fileResults, "file1.com", "结果应包含file1.com")
	require.Contains(t, fileResults, "file2.com", "结果应包含file2.com")

	// 测试空参数
	_, err = runner.preProcessArgument("")
	require.Error(t, err, "空参数应返回错误")
}

// TestOutputRecordType 测试记录类型输出
func TestOutputRecordType(t *testing.T) {
	// 设置选项并创建Runner
	options := &Options{
		Silent:       true,
		ResponseOnly: false,
		Response:     false,
		Retries: 3,
	}
	runner, err := New(options)
	require.NoError(t, err, "创建Runner实例不应有错误")

	// 创建输出通道
	runner.outputchan = make(chan string, 10) // 缓冲通道以避免阻塞

	// 测试普通输出
	records := []string{"***********", "***********"}
	runner.outputRecordType("example.com", records, "A")

	// 读取并验证输出
	output := <-runner.outputchan
	require.Equal(t, "example.com", output, "输出应为域名")

	// 测试ResponseOnly选项
	runner.options.Response = false
	runner.options.ResponseOnly = true
	runner.outputRecordType("example.com", records, "A")

	// 读取并验证输出
	output = <-runner.outputchan
	require.Equal(t, "***********", output, "输出应为IP地址")

	// 测试Response选项
	runner.options.Response = true
	runner.options.ResponseOnly = false
	runner.outputRecordType("example.com", records, "A")

	// 读取并验证输出
	output = <-runner.outputchan
	require.Contains(t, output, "***********", "输出应包含IP地址")

}

// TestStoreDNSData 测试DNS数据存储
func TestStoreDNSData(t *testing.T) {
	runner := setupTestRunner(t)

	// 创建测试DNS数据
	dnsdata := &retryabledns.DNSData{
		Host: "example.com",
		A:    []string{"***********"},
	}

	// 存储数据
	err := runner.storeDNSData(dnsdata)
	require.NoError(t, err, "存储DNS数据不应有错误")

	// 验证数据已存储
	data, ok := runner.hm.Get("example.com")
	require.True(t, ok, "应能从混合映射获取数据")
	require.NotNil(t, data, "存储的数据不应为nil")

	// 验证可以解析回原始数据
	var retrievedData retryabledns.DNSData
	err = retrievedData.Unmarshal(data)
	require.NoError(t, err, "解析存储的数据不应有错误")
	require.Equal(t, "example.com", retrievedData.Host, "主机名应匹配")
	require.Equal(t, []string{"***********"}, retrievedData.A, "A记录应匹配")
}

// TestClose 测试关闭Runner
func TestClose(t *testing.T) {
	runner := setupTestRunner(t)

	// 关闭Runner不应引发错误
	runner.Close()
}

// 禁用日志输出以保持测试输出清晰
func init() {
	gologger.DefaultLogger.SetMaxLevel(0)
}
