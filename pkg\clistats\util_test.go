// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-18 17:54:30
// FilePath: /yaml_scan/pkg/clistats/util_test.go
// Description: 
package clistats

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestString 测试String函数的字符串转换功能
func TestString(t *testing.T) {
	// 测试nil值
	result := String(nil)
	require.Equal(t, "n/a", result, "nil值应转换为'n/a'")
	
	// 测试字符串类型
	result = String("test")
	require.Equal(t, "test", result, "字符串应保持不变")
	
	// 测试整数类型
	result = String(42)
	require.Equal(t, "42", result, "整数应转换为十进制字符串")
	
	// 测试int32类型
	result = String(int32(123))
	require.Equal(t, "123", result, "int32应转换为十进制字符串")
	
	// 测试int64类型
	result = String(int64(9876543210))
	require.Equal(t, "9876543210", result, "int64应转换为十进制字符串")
	
	// 测试uint32类型
	result = String(uint32(456))
	require.Equal(t, "456", result, "uint32应转换为十进制字符串")
	
	// 测试uint64类型
	result = String(uint64(9876543210))
	require.Equal(t, "9876543210", result, "uint64应转换为十进制字符串")
	
	// 测试布尔类型
	result = String(true)
	require.Equal(t, "true", result, "true应转换为'true'字符串")
	result = String(false)
	require.Equal(t, "false", result, "false应转换为'false'字符串")
	
	// 测试浮点数类型
	result = String(float32(3.14))
	require.Contains(t, result, "3.14", "float32应包含正确的数值")
	
	result = String(float64(2.71828))
	require.Contains(t, result, "2.71828", "float64应包含正确的数值")
	
	// 测试字节切片
	result = String([]byte("hello"))
	require.Equal(t, "hello", result, "字节切片应转换为字符串")
	
	// 测试字节切片指针
	bytes := []byte("world")
	result = String(&bytes)
	require.Equal(t, "world", result, "字节切片指针应转换为字符串")
	
	// 测试字符串指针
	str := "pointer"
	result = String(&str)
	require.Equal(t, "pointer", result, "字符串指针应解引用")
	
	// 测试结构体（使用默认的fmt.Sprint）
	type testStruct struct {
		Name string
	}
	result = String(testStruct{Name: "test"})
	require.Contains(t, result, "test", "结构体应使用fmt.Sprint格式化")
}

// TestFmtDuration 测试FmtDuration函数的时间格式化功能
func TestFmtDuration(t *testing.T) {
	// 测试零时间
	result := FmtDuration(0)
	require.Equal(t, "0:00:00", result, "零时间应格式化为0:00:00")
	
	// 测试只有秒的时间
	result = FmtDuration(30 * time.Second)
	require.Equal(t, "0:00:30", result, "30秒应格式化为0:00:30")
	
	// 测试只有分钟的时间
	result = FmtDuration(5 * time.Minute)
	require.Equal(t, "0:05:00", result, "5分钟应格式化为0:05:00")
	
	// 测试只有小时的时间
	result = FmtDuration(3 * time.Hour)
	require.Equal(t, "3:00:00", result, "3小时应格式化为3:00:00")
	
	// 测试包含小时、分钟和秒的时间
	duration := 2*time.Hour + 34*time.Minute + 56*time.Second
	result = FmtDuration(duration)
	require.Equal(t, "2:34:56", result, "2小时34分56秒应格式化为2:34:56")
	
	// 测试大于24小时的时间
	duration = 25*time.Hour + 12*time.Minute + 5*time.Second
	result = FmtDuration(duration)
	require.Equal(t, "25:12:05", result, "25小时12分5秒应格式化为25:12:05")
	
	// 测试四舍五入
	duration = 1*time.Hour + 23*time.Minute + 45*time.Second + 500*time.Millisecond
	result = FmtDuration(duration)
	require.Equal(t, "1:23:46", result, "1:23:45.5应四舍五入为1:23:46")
} 

