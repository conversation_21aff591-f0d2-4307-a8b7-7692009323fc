// Author: chenjb
// Version: V1.0
// Date: 2025-05-21 16:48:59
// FilePath: /yaml_scan/pkg/fastdialer/util.go
// Description:
package fastdialer

import (
	"crypto/tls"

	ztls "github.com/zmap/zcrypto/tls"
	"golang.org/x/net/idna"
)

// asAscii 将主机名转换为ASCII编码
// 用于处理国际化域名，确保域名可以被正确解析
// @param hostname string: 需要转换的主机名
// @return string string: 转换后的ASCII编码的主机名
func asAscii(hostname string) string {
	hostnameAscii, _ := idna.ToASCII(hostname)
	return hostnameAscii
}

// AsZTLSConfig  将标准库tls.Config转换为zcrypto的ztls.Config
// @param tlsConfig *tls.Config:  标准库的TLS配置
// @return *ztls.Config *ztls.Config: 转换后的zcrypto TLS配置
// @return error error: 可能的错误，当前实现始终返回nil
func AsZTLSConfig(tlsConfig *tls.Config) (*ztls.Config, error) {
	ztlsConfig := &ztls.Config{
		NextProtos:             tlsConfig.NextProtos,                      // 复制协议列表
		ServerName:             tlsConfig.ServerName,                      // 复制服务器名称
		ClientAuth:             ztls.ClientAuthType(tlsConfig.ClientAuth), // 转换客户端认证类型
		InsecureSkipVerify:     tlsConfig.InsecureSkipVerify,              // 复制是否跳过证书验证
		CipherSuites:           tlsConfig.CipherSuites,                    // 复制密码套件列表
		SessionTicketsDisabled: tlsConfig.SessionTicketsDisabled,          // 复制是否禁用会话票证
		MinVersion:             tlsConfig.MinVersion,                      // 复制最低TLS版本
		MaxVersion:             tlsConfig.MaxVersion,                      // 复制最高TLS版本
	}
	return ztlsConfig, nil
}

// AsTLSConfig 将zcrypto的ztls.Config转换为标准库的tls.Config
// @param ztlsConfig *ztls.Config: zcrypto的TLS配置
// @return *tls.Config *tls.Config: 转换后的标准库TLS配置
// @return error error: 可能的错误，当前实现始终返回nil
func AsTLSConfig(ztlsConfig *ztls.Config) (*tls.Config, error) {
	tlsConfig := &tls.Config{
		NextProtos:             ztlsConfig.NextProtos,                     // 复制协议列表
		ServerName:             ztlsConfig.ServerName,                     // 复制服务器名称
		ClientAuth:             tls.ClientAuthType(ztlsConfig.ClientAuth), // 转换客户端认证类型
		InsecureSkipVerify:     ztlsConfig.InsecureSkipVerify,             // 复制是否跳过证书验证
		CipherSuites:           ztlsConfig.CipherSuites,                   // 复制密码套件列表
		SessionTicketsDisabled: ztlsConfig.SessionTicketsDisabled,         // 复制是否禁用会话票证
		MinVersion:             ztlsConfig.MinVersion,                     // 复制最低TLS版本
		MaxVersion:             ztlsConfig.MaxVersion,                     // 复制最高TLS版本
	}
	return tlsConfig, nil
}

// IsTLS13 检查给定的TLS配置是否使用TLS 1.3版本
// 支持标准库和zcrypto的TLS配置
// @param config interface{}: TLS配置，可以是*tls.Config或*ztls.Config
// @return bool bool: 如果最低版本设置为TLS 1.3则返回true，否则返回false
func IsTLS13(config interface{}) bool {
	switch c := config.(type) {
	case *tls.Config:
		return c.MinVersion == tls.VersionTLS13
	case *ztls.Config:
		return c.MinVersion == tls.VersionTLS13
	}

	return false
}
