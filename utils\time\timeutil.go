//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-20 16:17:20
//FilePath: /yaml_scan/utils/time/timeutil.go
//Description:

package timeutil

import (
	"strconv"
	"strings"
	"time"
)

// ParseDuration: 用于解析表示时间间隔的字符串
// 类似于 time.ParseDuration，但也支持天单位 如果没有指定单位，则默认为秒
//
//	@param s string:  表示持续时间的字符串，可以包含单位（如 "300ms", "2h", "1d"）。
//	@return time.Duration time.Duration: 解析后的持续时间。
//	@return error error:  如果解析失败，返回错误信息。
func ParseDuration(s string) (time.Duration, error) {
	s = strings.ToLower(s)

	//如果字符串可以直接转换为整数，则默认单位为秒，添加 "s" 后缀。
	if _, err := strconv.Atoi(s); err == nil {
		s = s + "s"
	}
	//  如果字符串以 "d" 结尾，表示天单位，将其转换为小时（1天=24小时）。
	if strings.HasSuffix(s, "d") {
		s = strings.TrimSuffix(s, "d")
		if days, err := strconv.Atoi(s); err == nil {
			s = strconv.Itoa(days*24) + "h"
		}
	}
	return time.ParseDuration(s)
}
