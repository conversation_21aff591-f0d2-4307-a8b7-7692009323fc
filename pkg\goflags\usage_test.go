//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-21 16:32:48
//FilePath: /yaml_scan/pkg/goflags/usage_test.go
//Description:

package goflags

import (
	"bytes"
	"flag"
	"os"
	"reflect"
	"strings"
	"testing"
	"text/tabwriter"
)

func TestCreateUsageFlagNames(t *testing.T) {
	tests := []struct {
		data     *FlagData
		expected string
	}{
		{&FlagData{short: "f", long: "flag"}, "  \t-f, -flag"},
		{&FlagData{short: "", long: "flag"}, "  \t-flag"},
		{&FlagData{short: "f", long: ""}, "  \t-f"},
	}

	for _, test := range tests {
		got := createUsageFlagNames(test.data)
		if got != test.expected {
			t.Errorf("createUsageFlagNames(%v) = %q; want %q", test.data, got, test.expected)
		}
	}

	// 测试空标志引发 panic
	defer func() {
		if r := recover(); r == nil {
			t.<PERSON><PERSON><PERSON>("Expected panic for empty flags, but did not panic")
		}
	}()
	createUsageFlagNames(&FlagData{short: "", long: ""}) // 应该引发 panic
}

func TestCreateUsageTypeAndDescription(t *testing.T) {
	// 创建一个示例 flag.Flag
	currentFlag := &flag.Flag{
		Name:  "test",
		Usage: "This is a test flag.",
	}

	// 测试指向字符串的指针
	valueType := reflect.TypeOf(new(string))
	expected := " value\t\tThis is a test flag."

	got := createUsageTypeAndDescription(currentFlag, valueType)
	if got != expected {
		t.Errorf("createUsageTypeAndDescription() = %q; want %q", got, expected)
	}

	// 测试指向字符串切片的指针
	valueTypeSlice := reflect.TypeOf(new([]string))
	expectedSlice := " string[]\t\tThis is a test flag."

	gotSlice := createUsageTypeAndDescription(currentFlag, valueTypeSlice)
	if gotSlice != expectedSlice {
		t.Errorf("createUsageTypeAndDescription() = %q; want %q", gotSlice, expectedSlice)
	}
}

func TestIsZeroValue(t *testing.T) {
	// 创建一个示例 flag.Flag
	currentFlag := &flag.Flag{
		Name:     "test",
		DefValue: "default",
		Usage:    "This is a test flag.",
		Value:    &MockFlagValue{}, // 初始化 Value
	}

	// 测试非零值
	if isZeroValue(currentFlag, "default") {
		t.Error("Expected 'default' to not be zero value")
	}

	// 测试零值
	if !isZeroValue(currentFlag, "") {
		t.Error("Expected '' to be zero value")
	}
}

func TestCreateUsageDefaultValue(t *testing.T) {
	// 创建一个示例 flag.Flag
	currentFlag := &flag.Flag{
		Name:     "test",
		DefValue: "default",
		Usage:    "This is a test flag.",
		Value:    &MockFlagValue{}, // 初始化 Value
	}

	// 测试指向字符串的指针
	valueType := reflect.TypeOf(new(string))
	data := &FlagData{defaultValue: currentFlag.DefValue}

	// 生成默认值的使用说明
	got := createUsageDefaultValue(data, currentFlag, valueType)
	expected := " (default default)"

	if got != expected {
		t.Errorf("createUsageDefaultValue() = %q; want %q", got, expected)
	}

	// 测试零值情况
	currentFlag.DefValue = ""
	gotZero := createUsageDefaultValue(data, currentFlag, valueType)
	if gotZero != "" {
		t.Errorf("Expected empty string for zero default value, got %q", gotZero)
	}
}

func TestDisplayGroupUsageFunc(t *testing.T) {

	// 创建 FlagSet 实例
	flagSet := &FlagSet{
		CommandLine: flag.NewFlagSet("test", flag.ExitOnError),
		flagKeys:    newInsertionOrderedMap(),
	}

	// 创建 uniqueDeduper 实例
	uniqueDeduper := newUniqueDeduper()

	// 创建 tabwriter
	var output bytes.Buffer
	writer := tabwriter.NewWriter(&output, 0, 0, 1, ' ', 0)

	// 创建组数据
	group := groupData{
		name:        "group1",
		description: "This is group 1",
	}

	// 调用 displayGroupUsageFunc
	otherOptions := flagSet.displayGroupUsageFunc(uniqueDeduper, group, &output, writer)

	// 检查输出
	expectedOutput := "THIS IS GROUP 1:\n"
	if strings.TrimSpace(output.String()) != strings.TrimSpace(expectedOutput) {
		t.Errorf("Expected output:\n%s\nGot:\n%s", expectedOutput, output.String())
	}

	// 检查未分组选项
	if len(otherOptions) != 0 {
		t.Errorf("Expected no other options, got %v", otherOptions)
	}
}

func TestGetFlagByName(t *testing.T) {
	var testValue string // 用于存储测试标志的值
	// 创建 FlagSet 实例并添加标志数据
	flagSet := &FlagSet{
		CommandLine:   flag.NewFlagSet("test", flag.ContinueOnError), // 创建新的 FlagSet
		flagKeys:      newInsertionOrderedMap(),
		CaseSensitive: false, // 设置为不区分大小写
	}
	flagSet.StringVarP(&testValue, "example", "e", "default.conf", "This is an example flag")

	// 测试查找长标志
	flag := flagSet.getFlagByName("example")
	if flag == nil || flag.short != "e" {
		t.Errorf("Expected to find flag 'example', got %v", flag)
	}

	// 测试查找短标志
	flag = flagSet.getFlagByName("e")
	if flag == nil || flag.long != "example" {
		t.Errorf("Expected to find flag 'e', got %v", flag)
	}

	// 测试查找不存在的标志
	flag = flagSet.getFlagByName("nonexistent")
	if flag != nil {
		t.Errorf("Expected to not find flag 'nonexistent', got %v", flag)
	}

	// 测试区分大小写
	flagSet.CaseSensitive = true
	flag = flagSet.getFlagByName("E") // 测试短标志
	if flag != nil {
		t.Errorf("Expected to not find flag 'E' in case-sensitive mode, got %v", flag)
	}
}

func TestDisplaySingleFlagUsageFunc(t *testing.T) {
	var testValue string
	// 创建 FlagSet 实例并添加标志数据
	flagSet := &FlagSet{
		CommandLine: flag.NewFlagSet("test", flag.ExitOnError),
		flagKeys:    newInsertionOrderedMap(),
	}

	// 添加标志
	flagSet.StringVarP(&testValue, "test", "t", "default.conf", "This is a test flag.")

	// 创建 tabwriter
	var output bytes.Buffer
	writer := tabwriter.NewWriter(&output, 0, 0, 1, ' ', 0)

	// 调用 displaySingleFlagUsageFunc
	flagSet.displaySingleFlagUsageFunc("test", flagSet.flagKeys.values["test"], &output, writer)

	// 检查输出
	expectedOutput := "-t, -test string  This is a test flag. (default \"default.conf\")"
	if strings.TrimSpace(output.String()) != strings.TrimSpace(expectedOutput) {
		t.Errorf("Expected output:\n%s\nGot:\n%s", expectedOutput, output.String())
	}
}

func TestUsageFuncForGroups(t *testing.T) {
	var testValue string
	// 创建 FlagSet 实例并添加标志数据
	flagSet := &FlagSet{
		CommandLine:           flag.NewFlagSet("test", flag.ExitOnError),
		flagKeys:              newInsertionOrderedMap(),
		OtherOptionsGroupName: "Other Options",
	}
	flagSet.CreateGroup(
		"group1", "This is group 1",
		flagSet.StringVarP(&testValue, "example", "e", "default.conf", "This is an example flag."),
	)
	// 创建 tabwriter
	var output bytes.Buffer
	writer := tabwriter.NewWriter(&output, 0, 0, 1, ' ', 0)

	// 调用 usageFuncForGroups
	flagSet.usageFuncForGroups(&output, writer)

	// 检查输出
	expectedOutput := "THIS IS GROUP 1:\n   -e, -example string  This is an example flag. (default \"default.conf\")"
	if strings.TrimSpace(output.String()) != strings.TrimSpace(expectedOutput) {
		t.Errorf("Expected output:\n%s\nGot:\n%s", expectedOutput, output.String())
	}
}

func TestUsageFunc(t *testing.T) {
	// 创建 FlagSet 实例并添加标志数据
	flagSet := &FlagSet{
		CommandLine:    flag.NewFlagSet("test", flag.ExitOnError),
		flagKeys:       newInsertionOrderedMap(),
		description:    "This is a sample application.",
	}

	// 添加标志
	flagSet.CommandLine.String("example", "default", "This is an example flag.")
	flagSet.CommandLine.String("test", "default", "This is a test flag.")

	// 创建一个缓冲区来捕获输出
	var output bytes.Buffer
	flagSet.CommandLine.SetOutput(&output)

	// 模拟命令行参数
	os.Args = []string{"cmd", "help"}

	// 调用 usageFunc
	flagSet.usageFunc()

	// 检查输出
	expectedOutput := "This is a sample application.\n\n使用说明:\n  cmd [flags]\n\nFlags:"
	if !strings.HasPrefix(output.String(), expectedOutput) {
		t.Errorf("Expected output to start with:\n%s\nGot:\n%s", expectedOutput, output.String())
	}
}
