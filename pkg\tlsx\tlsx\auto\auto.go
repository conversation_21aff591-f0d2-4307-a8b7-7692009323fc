//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-24 16:12:42
// FilePath: /yaml_scan/pkg/tlsx/tlsx/auto/auto.go
// Description:自动回退TLS客户端实现

package auto

import (
	"sync"
	"yaml_scan/pkg/tlsx/output/stats"
	"yaml_scan/pkg/tlsx/tlsx/clients"
	"yaml_scan/pkg/tlsx/tlsx/openssl"
	"yaml_scan/pkg/tlsx/tlsx/tls"
	"yaml_scan/pkg/tlsx/tlsx/ztls"

	errorutils "yaml_scan/utils/errors"
	sliceutil "yaml_scan/utils/slice"

	"go.uber.org/multierr"
)

// Client 使用自动回退机制的TLS抓取客户端
type Client struct {
	tlsClient     *tls.Client      // 原生Go TLS客户端实现
	ztlsClient    *ztls.Client     // ZMap TLS客户端实现，提供更详细的TLS信息
	opensslClient *openssl.Client  // OpenSSL TLS客户端实现，兼容性最好
	options       *clients.Options // 共享的配置选项
}

// New 创建一个使用自动回退机制的TLS抓取客户端
// 尝试初始化所有可用的TLS实现，至少需要一个实现成功才能创建客户端
// @param options *clients.Options: TLS连接配置选项，将传递给所有底层实现
// @return *Client *Client: 自动回退TLS客户端实例
// @return error error: 如果所有TLS实现都初始化失败则返回组合错误
func New(options *clients.Options) (*Client, error) {
	tlsClient, tlsErr := tls.New(options)
	ztlsClient, ztlsErr := ztls.New(options)
	opensslClient, opensslErr := openssl.New(options)

	// 检查是否至少有一个实现成功初始化
	// OpenSSL不可用错误不算作失败，因为系统可能未安装OpenSSL
	if tlsErr != nil && ztlsErr != nil && (opensslErr != nil && !errorutils.IsAny(opensslErr, openssl.ErrNotAvailable)) {
		return nil, multierr.Combine(tlsErr, ztlsErr, opensslErr)
	}
	return &Client{tlsClient: tlsClient, ztlsClient: ztlsClient, opensslClient: opensslClient, options: options}, nil
}

// ConnectWithOptions 使用自动回退机制连接到目标主机
// 按优先级顺序尝试不同的TLS实现，直到成功或达到最大重试次数
// @receiver c
// @param hostname string: 目标主机名或域名
// @param ip string: 目标IP地址
// @param port string: 目标端口号
// @param options clients.ConnectOptions: 接选项配置
// @return *clients.Response *clients.Response: TLS连接响应，包含使用的实现类型标识
// @return error error: 所有实现都失败时返回组合错误
// 回退策略:
//   - 优先级: ctls -> ztls -> openssl
//   - 每个实现失败后立即尝试下一个
//   - 最少重试3次，确保有足够的尝试机会
//   - 记录成功连接的统计信息
//   - 在响应中标记使用的TLS实现类型
func (c *Client) ConnectWithOptions(hostname, ip, port string, options clients.ConnectOptions) (*clients.Response, error) {
	var response *clients.Response
	var err, ztlsErr, opensslErr error

	// 确定最大重试次数，至少为3次
	maxRetries := c.options.Retries
	if maxRetries < 3 {
		maxRetries = 3
	}
	retryCounter := 0

	// 安全检查：确保至少有一个可用的TLS实现
	if c.tlsClient == nil && c.ztlsClient == nil && c.opensslClient == nil {
		return nil, errorutils.New("no tls client available available for auto mode")
	}
	var errStack error

	for retryCounter < maxRetries {

		// 第一优先级：尝试原生Go TLS实现
		if c.tlsClient != nil {
			if response, err = c.tlsClient.ConnectWithOptions(hostname, ip, port, options); err == nil {
				response.TLSConnection = "ctls"
				// 更新统计信息
				stats.IncrementCryptoTLSConnections()
				return response, nil
			}
			retryCounter++
		}

		// 第二优先级：尝试ZMap TLS实现
		if c.ztlsClient != nil {
			if response, ztlsErr = c.ztlsClient.ConnectWithOptions(hostname, ip, port, options); ztlsErr == nil {
				response.TLSConnection = "ztls"
				stats.IncrementZcryptoTLSConnections()
				return response, nil
			}
			retryCounter++
		}

		// 第三优先级：尝试OpenSSL实现
		if c.opensslClient != nil {
			if response, opensslErr = c.opensslClient.ConnectWithOptions(hostname, ip, port, options); opensslErr == nil {
				response.TLSConnection = "openssl"
				stats.IncrementOpensslTLSConnections()
				return response, nil
			}
			if errorutils.IsAny(opensslErr, openssl.ErrNotAvailable) {
				opensslErr = nil
			}
			retryCounter++
		}
		errStack = multierr.Combine(errStack, err, ztlsErr, opensslErr)
	}
	return nil, errStack
}

// EnumerateCiphers 使用所有可用的TLS实现并发枚举密码套件
// 通过并行执行多个实现的密码套件枚举，获得最全面的结果
// @receiver c
// @param hostname string: 目标主机名或域名
// @param ip string: 目标IP地址
// @param port string: 目标端口号
// @param options clients.ConnectOptions:  连接选项，包含TLS版本和密码套件级别配置
// @return []string []string: 去重后的密码套件名称列表，包含所有实现发现的密码套件
// @return error error:
func (c *Client) EnumerateCiphers(hostname, ip, port string, options clients.ConnectOptions) ([]string, error) {
	// 等待组，用于同步所有goroutine
	wg := &sync.WaitGroup{}
	ciphersFound := []string{}
	// 互斥锁，保护共享数据
	cipherMutex := &sync.Mutex{}
	// 可用的TLS实现列表
	allClients := []clients.Implementation{}

	// 按优先级顺序收集可用的TLS实现
	// OpenSSL优先，因为它通常能发现最多的密码套件
	if c.opensslClient != nil {
		allClients = append(allClients, c.opensslClient)
	}
	if c.ztlsClient != nil {
		allClients = append(allClients, c.ztlsClient)
	}
	if c.tlsClient != nil {
		allClients = append(allClients, c.tlsClient)
	}

	// 为每个可用的TLS实现启动并发的密码套件枚举
	for _, v := range allClients {
		wg.Add(1)
		go func(clientx clients.Implementation) {
			defer wg.Done()
			// 调用具体实现的密码套件枚举方法
			if res, _ := clientx.EnumerateCiphers(hostname, ip, port, options); len(res) > 0 {
				// 使用互斥锁保护共享数据的并发访问
				cipherMutex.Lock()
				ciphersFound = append(ciphersFound, res...)
				cipherMutex.Unlock()
			}
		}(v)
	}
	// 等待所有goroutine完成
	wg.Wait()
	// 去重并返回最终结果
	return sliceutil.Dedupe(ciphersFound), nil
}

// SupportedTLSVersions 返回所有TLS实现支持的TLS版本列表
func (c *Client) SupportedTLSVersions() ([]string, error) {
	return supportedTlsVersions, nil
}

// SupportedTLSCiphers 返回所有TLS实现支持的密码套件列表
func (c *Client) SupportedTLSCiphers() ([]string, error) {
	return allCiphersNames, nil
}
