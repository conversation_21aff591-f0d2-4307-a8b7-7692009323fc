package formatter

import (
	"bytes"
	"testing"

	"yaml_scan/pkg/gologger/levels"
)

func TestCliFormat(t *testing.T){
	tests := []struct {
		name        string
		event       *LogEvent
		noUseColors bool
		expected    string
	}{
		{
			name: "Info level with label and timestamp",
			event: &LogEvent{
				Level:   levels.LevelInfo,
				Message: "This is a test message",
				Metadata: map[string]string{
					"label":     "INFO",
					"timestamp": "2024-10-30T12:34:56Z",
				},
			},
			noUseColors: false,
			expected:    "[\x1b[34mINFO\x1b[0m] [2024-10-30T12:34:56Z] This is a test message",
		},
		{
			name: "Error level without colors",
			event: &LogEvent{
				Level:   levels.LevelError,
				Message: "An error occurred",
				Metadata: map[string]string{
					"label":     "ERROR",
					"timestamp": "2024-10-30T12:34:56Z",
				},
			},
			noUseColors: true,
			expected:    "[ERROR] [2024-10-30T12:34:56Z] An error occurred",
		},
		{
			name: "Debug level with additional metadata",
			event: &LogEvent{
				Level:   levels.LevelDebug,
				Message: "Debugging",
				Metadata: map[string]string{
					"label":     "DEBUG",
					"timestamp": "2024-10-30T12:34:56Z",
					"user":      "admin",
					"session":   "xyz123",
				},
			},
			noUseColors: false,
			expected:    "[\x1b[35mDEBUG\x1b[0m] [2024-10-30T12:34:56Z] Debugging \x1b[1muser\x1b[0m=admin \x1b[1msession\x1b[0m=xyz123",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cli := NewCLI(tt.noUseColors)
			
			result, err := cli.Format(tt.event)
			if err != nil {
				t.Fatalf("Format() returned an error: %v", err)
			}

			if !bytes.Equal(result, []byte(tt.expected)) {
				t.Errorf("Format() = %s, want %s", result, tt.expected)
			}
		})
	}


}