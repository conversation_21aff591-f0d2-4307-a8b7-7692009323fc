//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-21 10:47:38
//FilePath: /yaml_scan/pkg/goflags/enum_var.go
//Description:

package goflags

import (
	"fmt"
	"strings"
)

// EnumVariable 定义一个枚举变量类型
type EnumVariable int8

// String 返回 EnumVariable 的字符串表示
func (e *EnumVariable) String() string {
	return fmt.Sprintf("%v", *e)
}

// AllowdTypes 定义一个映射，表示允许的类型
type AllowdTypes map[string]EnumVariable

// String 返回 AllowdTypes 的字符串表示
func (a AllowdTypes) String() string {
	var str string
	for k := range a {
		str += fmt.Sprintf("%s, ", k)
	}
	return strings.TrimSuffix(str, ", ")
}

// EnumVar 定义一个枚举变量结构体
type EnumVar struct {
	allowedTypes AllowdTypes // 允许的类型
	value        *string     // 指向存储值的指针
}

// String 返回 EnumVar 的字符串表示
func (e *EnumVar) String() string {
	if e.value != nil {
		return *e.value
	}
	return ""
}

// Set: 设置 EnumVar 的值
//
//	@receiver e *EnumVar:
//	@param value string:要设置的值，必须是允许的类型之一。
//	@return error error:如果值不在允许的类型中，返回错误信息。
func (e *EnumVar) Set(value string) error {
	_, ok := e.allowedTypes[value]
	if !ok {
		return fmt.Errorf("allowed values are %v", e.allowedTypes.String())
	}
	*e.value = value
	return nil
}
