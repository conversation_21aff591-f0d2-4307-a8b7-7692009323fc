package levels

import (
	"strings"
	"errors"
)


// Level defines all the available levels we can log at
type Level int

// Available logging levels
const (
	LevelFatal Level = iota  // 致命错误级别
	LevelSilent  // 不输出任何日志
	LevelError // 错误级别
	LevelInfo // 信息级别
	LevelWarning // 警告级别
	LevelDebug // 调试级别
	LevelVerbose // 详细信息级别
)

// String returns the string representation of a log level
func (l Level) String() string {
	return [...]string{"fatal", "silent", "error", "info", "warning", "debug", "verbose"}[l]
}


// ParseLevel converts a string to a Level type
func ParseLevel(levelStr string) (Level, error) {
	levelStr = strings.ToLower(levelStr)
	for i := LevelFatal; i <= LevelVerbose; i++ {
		if levelStr == i.String() {
			return i, nil
		}
	}
	return LevelInfo, errors.New("invalid log level: " + levelStr) // 返回默认级别和错误
}