//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-09 15:39:20
//FilePath: /yaml_scan/pkg/retryabledns/resolver.go
//Description: 解析器

package retryabledns

import (
	"net"
	"strings"
	stringsutil "yaml_scan/utils/strings"
)

// Resolver 接口定义了一个方法，用于返回解析器的字符串表示
type Resolver interface {
	String() string
}

// NetworkResolver 结构体实现了 Resolver 接口
type NetworkResolver struct {
	Protocol Protocol // 协议
	Host     string   // 主机名
	Port     string   // 端口号
}

// String: 返辉DNS解析器的字符串表示，格式为"host:port"
//
//	@receiver r NetworkResolver:
//	@return string string:
func (r NetworkResolver) String() string {
	return net.JoinHostPort(r.Host, r.Port)
}

// DohResolver 结构体实现了 Resolver 接口，表示DOH解析器
type DohResolver struct {
	Protocol DohProtocol // DOH协议类型
	URL      string      // url地址
}

// Method: 返回根据DOH协议方式选择的HTTP方法字符串
//  @receiver r DohResolver: 
//  @return string string: 
func (r DohResolver) Method() string {
	if r.Protocol == POST {
		return POST.String()
	}

	return GET.String()
}

// String: 返回DOH解析器的URL字符串表示
//  @receiver r DohResolver: 
//  @return string string: 
func (r DohResolver) String() string {
	return r.URL
}

// trimProtocol: 去除resolver字符串中的协议前缀
//
//	@param resolver string:
//	@return string string:
func trimProtocol(resolver string) string {
	return stringsutil.TrimPrefixAny(resolver, TCP.StringWithSemicolon(), UDP.StringWithSemicolon(), DOH.StringWithSemicolon(), DOT.StringWithSemicolon())
}

// trimDohProtocol: 去除resolver字符串中的doh协议后缀
//
//	@param resolver string:
//	@return string string:
func trimDohProtocol(resolver string) string {
	return stringsutil.TrimSuffixAny(resolver, GET.StringWithSemicolon(), POST.StringWithSemicolon(), JsonAPI.StringWithSemicolon())
}

// hasDohProtocol:检查是否为doh协议
//
//	@param resolver string:
//	@param protocol string:
//	@return bool bool:
func hasDohProtocol(resolver, protocol string) bool {
	return strings.HasSuffix(resolver, protocol)
}

// parseHostPort: 解析给定的主机和端口字符串，并将结果存储在 NetworkResolver 结构体中。
//
//	@param networkResolver *NetworkResolver: 指向 NetworkResolver 结构体的指针，用于存储解析后的主机和端口信息。
//	@param r string: 字符串，表示要解析的主机和端口信息，格式通常为 "host:port"。
func parseHostPort(networkResolver *NetworkResolver, r string) {
	// 尝试从字符串 r 中分离主机和端口
	if host, port, err := net.SplitHostPort(r); err == nil {
		networkResolver.Host = host
		networkResolver.Port = port
	} else {
		// 如果解析失败，说明字符串不包含端口
		networkResolver.Host = r
		// 对于 DOT 协议，默认端口为 853
		if networkResolver.Protocol == DOT {
			networkResolver.Port = "853"
		} else {
			// 对于其他协议，默认端口为 53
			networkResolver.Port = "53"
		}
	}
}

// parseResolver: 解析给定的解析器字符串 r，并返回相应的 Resolver 实例。
//
//	@param r string:，表示要解析的解析器信息，格式通常为 "protocol:host:port"。
//	@return resolver Resolver:
func parseResolver(r string) (resolver Resolver) {
	// 去除协议前缀，获取主机和端口部分
	rNetworkTokens := trimProtocol(r)

	// 默认协议为 UDP
	protocol := UDP

	// 检查字符串长度和协议分隔符
	// 58 是字符 ":" 的 ASCII 值
	if len(r) >= 4 && r[3] == 58 {
		switch r[0:3] {
		case "udp":
			// 使用默认的 UDP 协议
		case "tcp":
			// 设置协议为 TCP
			protocol = TCP
		case "dot":
			// 设置协议为 DOT
			protocol = DOT
		case "doh":
			// 设置协议为 DOH
			protocol = DOH
			// 检查是否为 JSON API 或 GET 请求
			isJsonApi, isGet := hasDohProtocol(r, JsonAPI.StringWithSemicolon()), hasDohProtocol(r, GET.StringWithSemicolon())
			// 去除 DOH 协议前缀，获取 URL
			URL := trimDohProtocol(rNetworkTokens)
			// 创建 DOH 解析器
			dohResolver := &DohResolver{URL: URL, Protocol: POST}
			// 根据请求类型设置 DOH 解析器的协议
			if isJsonApi {
				dohResolver.Protocol = JsonAPI
			} else if isGet {
				dohResolver.Protocol = GET
			}
			resolver = dohResolver
		default:

		}
	}
	// 如果协议不是 DOH，则创建 NetworkResolver
	if protocol != DOH {
		networkResolver := &NetworkResolver{Protocol: protocol}
		parseHostPort(networkResolver, rNetworkTokens)
		resolver = networkResolver
	}

	return
}

// parseResolvers:解析给定的解析器字符串切片，并返回相应的 Resolver 实例切片。
//
//	@param resolvers []string:
//	@return []Resolver []Resolver:
func parseResolvers(resolvers []string) []Resolver {
	var parsedResolvers []Resolver
	for _, resolver := range resolvers {
		parsedResolvers = append(parsedResolvers, parseResolver(resolver))
	}
	return parsedResolvers
}
