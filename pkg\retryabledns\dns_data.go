//
//Author: chenjb
//Version: V1.0
//Date: 2025-04-24 10:22:04
//FilePath: /yaml_scan/pkg/retryabledns/dns_data.go
//Description:

package retryabledns

import (
	"bytes"
	"encoding/gob"
	"encoding/json"
	"strings"
	"time"

	sliceutil "yaml_scan/utils/slice"

	"github.com/miekg/dns"
)

func init() {
	gob.Register(&dns.A{})
    gob.Register(&dns.AAAA{})
    gob.Register(&dns.CNAME{})
    gob.Register(&dns.MX{})
    gob.Register(&dns.NS{})
    gob.Register(&dns.SOA{})
    gob.Register(&dns.PTR{})
    gob.Register(&dns.TXT{})
    gob.Register(&dns.SRV{})
    gob.Register(&dns.CAA{})
	gob.Register(&dns.OPT{})
}

// SOA 结构体表示 DNS SOA（Start of Authority）记录的字段
type SOA struct {
	Name    string `json:"name,omitempty"`    // SOA 记录的域名
	NS      string `json:"ns,omitempty"`      // 主名称服务器的域名
	Mbox    string `json:"mailbox,omitempty"` // 负责人的电子邮件地址（通常将 @ 替换为 .）
	Serial  uint32 `json:"serial,omitempty"`  // 序列号，用于区域传输
	Refresh uint32 `json:"refresh,omitempty"` // 刷新时间，指示辅助服务器检查更新的频率
	Retry   uint32 `json:"retry,omitempty"`   // 重试时间，指示失败后重试的间隔
	Expire  uint32 `json:"expire,omitempty"`  // 过期时间，指示辅助服务器在无法联系主服务器时保留数据的时长
	Minttl  uint32 `json:"minttl,omitempty"`  // 最小 TTL，指示记录的默认 TTL
}

// TraceData 结构体包含 DNS 查询的跟踪信息
type TraceData struct {
	Host    string     `json:"host,omitempty"`  // 查询的主机名
	DNSData []*DNSData `json:"chain,omitempty"` // 查询链中每个步骤的 DNS 数据
}

// AXFRData 结构体包含 DNS AXFR（区域传输）请求的数据
type AXFRData struct {
	Host    string     `json:"host,omitempty"`  // 查询的主机名
	DNSData []*DNSData `json:"chain,omitempty"` // AXFR 请求返回的 DNS 数据
}

// DNSData 结构体表示 DNS 请求响应的数据
type DNSData struct {
	Host           string     `json:"host,omitempty"`             // 查询的主机名
	TTL            uint32     `json:"ttl,omitempty"`              // 记录的 TTL（生存时间）
	Resolver       []string   `json:"resolver,omitempty"`         // 用于解析的 DNS 服务器列表
	A              []string   `json:"a,omitempty"`                // A 记录（IPv4 地址）
	AAAA           []string   `json:"aaaa,omitempty"`             // AAAA 记录（IPv6 地址）
	CNAME          []string   `json:"cname,omitempty"`            // CNAME 记录（别名）
	MX             []string   `json:"mx,omitempty"`               // MX 记录（邮件交换）
	PTR            []string   `json:"ptr,omitempty"`              // PTR 记录（反向 DNS）
	SOA            []SOA      `json:"soa,omitempty"`              // SOA 记录
	NS             []string   `json:"ns,omitempty"`               // NS 记录（名称服务器）
	TXT            []string   `json:"txt,omitempty"`              // TXT 记录（文本）
	SRV            []string   `json:"srv,omitempty"`              // SRV 记录（服务）
	CAA            []string   `json:"caa,omitempty"`              // CAA 记录（认证机构授权）
	AllRecords     []string   `json:"all,omitempty"`              // 所有记录的字符串表示
	Raw            string     `json:"raw,omitempty"`              // 原始 DNS 响应
	HasInternalIPs bool       `json:"has_internal_ips,omitempty"` // 是否包含内部 IP 地址
	InternalIPs    []string   `json:"internal_ips,omitempty"`     // 内部 IP 地址列表
	StatusCode     string     `json:"status_code,omitempty"`      // DNS 状态码的字符串表示
	StatusCodeRaw  int        `json:"status_code_raw,omitempty"`  // DNS 状态码的原始整数值
	TraceData      *TraceData `json:"trace,omitempty"`            // 查询的跟踪数据
	AXFRData       *AXFRData  `json:"axfr,omitempty"`             // AXFR 请求的数据
	RawResp        *dns.Msg   `json:"raw_resp,omitempty"`         // 原始 DNS 响应消息
	Timestamp      time.Time  `json:"timestamp,omitempty"`        // 查询时间戳
	HostsFile      bool       `json:"hosts_file,omitempty"`       // 是否从 hosts 文件解析
}

var internalRangeCheckerInstance *internalRangeChecker

// trimChars: 移除字符串末尾的点号。
//
//	@param s string: 需要处理的字符串
//	@return string string:移除末尾点号后的字符串
func trimChars(s string) string {
	return strings.TrimRight(s, ".")
}

// ParseFromRR:解析DNS资源记录（RR）并填充DNSData结构体。
//
//	@receiver d *DNSData:
//	@param rrs []dns.RR: DNS资源记录的切片
//	@return error error: 如果解析过程中出错，返回错误；否则返回nil
func (d *DNSData) ParseFromRR(rrs []dns.RR) error {
	for _, record := range rrs {
		// 如果TTL尚未设置且记录有TTL，则设置TTL
		if d.TTL == 0 && record.Header().Ttl > 0 {
			d.TTL = record.Header().Ttl
		}
		switch recordType := record.(type) {
		case *dns.A:
			// 检查IP是否为内部IP，如果是则添加到InternalIPs
			if CheckInternalIPs && internalRangeCheckerInstance != nil && internalRangeCheckerInstance.ContainsIPv4(recordType.A) {
				d.HasInternalIPs = true
				d.InternalIPs = append(d.InternalIPs, trimChars(recordType.A.String()))
			}
			// 将A记录添加到DNSData
			d.A = append(d.A, trimChars(recordType.A.String()))
		case *dns.NS:
			// 将NS记录添加到DNSData
			d.NS = append(d.NS, trimChars(recordType.Ns))
		case *dns.CNAME:
			// 将CNAME记录添加到DNSData
			d.CNAME = append(d.CNAME, trimChars(recordType.Target))
		case *dns.SOA:
			// 将SOA记录添加到DNSData
			d.SOA = append(d.SOA, SOA{
				Name:    trimChars(recordType.Hdr.Name),
				NS:      trimChars(recordType.Ns),
				Mbox:    trimChars(recordType.Mbox),
				Serial:  recordType.Serial,
				Refresh: recordType.Refresh,
				Retry:   recordType.Retry,
				Expire:  recordType.Expire,
				Minttl:  recordType.Minttl,
			},
			)
		case *dns.PTR:
			// 将PTR记录添加到DNSData
			d.PTR = append(d.PTR, trimChars(recordType.Ptr))
		case *dns.MX:
			// 将MX记录添加到DNSData
			d.MX = append(d.MX, trimChars(recordType.Mx))
		case *dns.CAA:
			// 将CAA记录添加到DNSData
			d.CAA = append(d.CAA, trimChars(recordType.Value))
		case *dns.TXT:
			// 根据RFC 7208，单个TXT记录可能被分成多个部分，需将它们连接起来，不添加空格
			d.TXT = append(d.TXT, strings.Join(recordType.Txt, ""))
		case *dns.SRV:
			// 将SRV记录添加到DNSData
			d.SRV = append(d.SRV, trimChars(recordType.Target))
		case *dns.AAAA:
			// 检查IPv6是否为内部IP，如果是则添加到InternalIPs
			if CheckInternalIPs && internalRangeCheckerInstance.ContainsIPv6(recordType.AAAA) {
				d.HasInternalIPs = true
				d.InternalIPs = append(d.InternalIPs, trimChars(recordType.AAAA.String()))
			}
			// 将AAAA记录添加到DNSData
			d.AAAA = append(d.AAAA, trimChars(recordType.AAAA.String()))
		}
		// 将记录的字符串表示添加到AllRecords
		d.AllRecords = append(d.AllRecords, record.String())
	}
	return nil
}

// ParseFromMsg: 解析DNS消息并填充DNSData结构体。
//
//	@receiver d *DNSData:
//	@param msg *dns.Msg: DNS消息结构体指针
//	@return error error:如果解析过程中出错，返回错误；否则返回nil
func (d *DNSData) ParseFromMsg(msg *dns.Msg) error {
	// 合并Answer、Extra和Ns部分的记录
	allRecords := append(msg.Answer, msg.Extra...)
	allRecords = append(allRecords, msg.Ns...)
	return d.ParseFromRR(allRecords)
}

// ParseFromEnvelopeChan: 从通道解析DNS传输层并填充DNSData结构体。
//
//	@receiver d *DNSData:
//	@param envChan chan *dns.Envelope: DNS传输层的通道
//	@return error error:如果解析过程中出错，返回错误；否则返回nil
func (d *DNSData) ParseFromEnvelopeChan(envChan chan *dns.Envelope) error {
	var allRecords []dns.RR
	for env := range envChan {
		if env.Error != nil {
			return env.Error
		}
		allRecords = append(allRecords, env.RR...)
	}
	return d.ParseFromRR(allRecords)
}

// contains:检查 DNSData 是否包含任何记录。
// （A、AAAA、CNAME、MX、NS、PTR、TXT、SRV、SOA、CAA）
//
//	@receiver d *DNSData:
//	@return bool bool: 如果任何记录字段不为空，返回 true；否则返回 false
func (d *DNSData) contains() bool {
	return len(d.A) > 0 || len(d.AAAA) > 0 || len(d.CNAME) > 0 || len(d.MX) > 0 || len(d.NS) > 0 || len(d.PTR) > 0 || len(d.TXT) > 0 || len(d.SRV) > 0 || len(d.SOA) > 0 || len(d.CAA) > 0
}

// dedupe: 去除 DNSData 中各个记录字段中的重复项。
//
//	@receiver d *DNSData:
func (d *DNSData) dedupe() {
	d.Resolver = sliceutil.Dedupe(d.Resolver)
	d.A = sliceutil.Dedupe(d.A)
	d.AAAA = sliceutil.Dedupe(d.AAAA)
	d.CNAME = sliceutil.Dedupe(d.CNAME)
	d.MX = sliceutil.Dedupe(d.MX)
	d.PTR = sliceutil.Dedupe(d.PTR)
	d.NS = sliceutil.Dedupe(d.NS)
	d.TXT = sliceutil.Dedupe(d.TXT)
	d.SRV = sliceutil.Dedupe(d.SRV)
	d.CAA = sliceutil.Dedupe(d.CAA)
	d.AllRecords = sliceutil.Dedupe(d.AllRecords)
}

// JSON:将 DNSData 对象序列化为 JSON 字符串。
//
//	@receiver d *DNSData:
//	@return string string: 序列化后的 JSON 字符串。
//	@return error error: 如果序列化失败，返回具体的错误信息；成功时返回 nil。
func (d *DNSData) JSON() (string, error) {
	b, err := json.Marshal(&d)
	return string(b), err
}

// Marshal:将 DNSData 对象编码为二进制表示。
//
//	@receiver d *DNSData:
//	@return []byte []byte: 序列化后的二进制数据。
//	@return error error: 如果序列化失败，返回具体的错误信息；成功时返回 nil。
func (d *DNSData) Marshal() ([]byte, error) {
	// 初始化字节缓冲区，用于存储序列化结果
	var b bytes.Buffer
	// 创建 gob 编码器，绑定到缓冲区
	enc := gob.NewEncoder(&b)
	// 编码 DNSData 结构体
	err := enc.Encode(d)
	if err != nil {
		return nil, err
	}
	return b.Bytes(), nil
}

// Unmarshal: 从二进制表示解码到 DNSData 对象。
//
//	@receiver d *DNSData:
//	@param b []byte: 要解码的二进制数据。
//	@return error error: 如果解码失败，返回具体的错误信息；成功时返回 nil。
func (d *DNSData) Unmarshal(b []byte) error {
	dec := gob.NewDecoder(bytes.NewBuffer(b))
	return dec.Decode(&d)
}

// GetSOARecords: 返回所有 SOA 记录的 NS 和 Mbox 字段作为字符串切片。
//
//	@receiver d *DNSData:
//	@return []string []string: 包含所有 SOA 记录的 NS 和 Mbox 字段的字符串切片。
func (d *DNSData) GetSOARecords() []string {
	var soaRecords []string
	// 遍历 SOA 记录列表
	for _, soa := range d.SOA {
		soaRecords = append(soaRecords, soa.NS, soa.Mbox)
	}
	return soaRecords
}
