//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-11 19:45:14
//FilePath: /yaml_scan/utils/url/rawparam_test.go
//Description: rawparam 单测

package urlutil

import (
	"testing"
)

// 测试 getasciihex 函数，验证不同 ASCII 字符的十六进制表示是否正确。
func TestGetASCIIHex(t *testing.T) {
	tests := []struct {
		input    rune
		expected string
	}{
		{'A', "41"},    // ASCII 'A'
		{'a', "61"},    // ASCII 'a'
		{'0', "30"},    // ASCII '0'
		{' ', "20"},    // 空格
		{'\n', "0A"},   // 换行符
		{'\x1F', "1F"}, // 控制字符
	}

	for _, test := range tests {
		result := getasciihex(test.input)
		if result != test.expected {
			t.Errorf("getasciihex(%q) = %q; want %q", test.input, result, test.expected)
		}
	}
}

// 测试 getrunemap 函数，验证将 rune 切片转换为映射的功能。
func TestGetRuneMap(t *testing.T) {
	tests := []struct {
		input    []rune
		expected map[rune]struct{}
	}{
		{[]rune{'a', 'b', 'c'}, map[rune]struct{}{
			'a': {},
			'b': {},
			'c': {},
		}},
		{[]rune{'中', '文'}, map[rune]struct{}{
			'中': {},
			'文': {},
		}},
		{[]rune{}, map[rune]struct{}{}}, // 空切片
	}

	for _, test := range tests {
		result := getrunemap(test.input)
		if len(result) != len(test.expected) {
			t.Errorf("getrunemap(%q) has length %d; want %d", test.input, len(result), len(test.expected))
			continue
		}
		for k := range test.expected {
			if _, ok := result[k]; !ok {
				t.Errorf("getrunemap(%q) missing key %q", test.input, k)
			}
		}
	}
}

// 测试 getutf8hex 函数，验证不同字符（包括中文和表情符号）的十六进制表示是否正确
func TestGetUTF8Hex(t *testing.T) {
	tests := []struct {
		input    rune
		expected string
	}{
		{'中', "e4%b8%ad"},    // 中文字符 '中'
		{'文', "e6%96%87"},    // 中文字符 '文'
		{'😊', "f0%9f%98%8a"}, // 表情符号 '😊'
		{'a', "61"},          // ASCII 字符 'a'
		{' ', "20"},          // 空格
	}

	for _, test := range tests {
		result := getutf8hex(test.input)
		if result != test.expected {
			t.Errorf("getutf8hex(%q) = %q; want %q", test.input, result, test.expected)
		}
	}
}

// 测试 URLEncodeWithEscapes 函数
func TestURLEncodeWithEscapes(t *testing.T) {
	tests := []struct {
		input    string
		charset  []rune
		expected string
	}{
		{"hello world", nil, "hello+world"},                      // 空格转为 +
		{"hello;world", nil, "hello;world"},                      // 分号不转换
		{"hello&world", nil, "hello&world"},                      // & 不转换
		{"hello=world", nil, "hello=world"},                      // = 不转换
		{"hello\x00world", nil, "hello%00world"},                 // 控制字符转为 %00
		{"中文", nil, "%e4%b8%ad%e6%96%87"},                        // 中文字符转为 UTF-8 编码
		{"hello", []rune{'h', 'e', 'l', 'o'}, "%68%65%6C%6C%6F"}, // 保留字符转义
		{"hello!@#$%^&*()", nil, "hello!@#$%^&*()"},              // 特殊字符不转义
	}

	for _, test := range tests {
		result := URLEncodeWithEscapes(test.input, test.charset...)
		if result != test.expected {
			t.Errorf("URLEncodeWithEscapes(%q) = %q; want %q", test.input, result, test.expected)
		}
	}
}

// 测试 ParamEncode 函数
func TestParamEncode(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"hello world", "hello+world"}, // 空格转为 +
		{"hello;world", "hello;world"}, // 分号不转
		{"hello&world", "hello&world"}, // &不转
		{"hello=world", "hello=world"}, // = 不转
		{"中文", "%e4%b8%ad%e6%96%87"},   // 中文字符转为 UTF-8 编码
	}

	for _, test := range tests {
		result := ParamEncode(test.input)
		if result != test.expected {
			t.Errorf("ParamEncode(%q) = %q; want %q", test.input, result, test.expected)
		}
	}
}

// 测试 Params 的 Encode 方法，验证多个键值对的编码结果
func TestEncode(t *testing.T) {
	p := Params{
		"foo":   {"bar", "baz"},
		"hello": {"world"},
		"key":   {},
	}

	expected := "foo=bar&foo=baz&hello=world"
	result := p.Encode()
	if result != expected {
		t.Errorf("Params.Encode() = %q; want %q", result, expected)
	}
}

// 测试 Params 的 Has 方法，检查键是否存在
func TestHas(t *testing.T) {
	p := Params{
		"foo": {"bar"},
	}

	if !p.Has("foo") {
		t.Error("Expected key 'foo' to exist")
	}
	if p.Has("nonexistent") {
		t.Error("Expected key 'nonexistent' to not exist")
	}
}

// 测试 Params 的 Add 方法，确保可以正确添加键值对。
func TestAdd(t *testing.T) {
	p := Params{}
	p.Add("foo", "bar")
	p.Add("foo", "baz")

	expected := Params{
		"foo": {"bar", "baz"},
	}

	if len(p["foo"]) != len(expected["foo"]) || p["foo"][0] != expected["foo"][0] || p["foo"][1] != expected["foo"][1] {
		t.Errorf("Expected %v, got %v", expected, p)
	}
}

// 测试 Params 的 Decode 方法，验证解析的结果是否符合预期
func TestDecode(t *testing.T) {
	tests := []struct {
		raw      string
		expected Params
	}{
		{"foo=bar&hello=world", Params{"foo": {"bar"}, "hello": {"world"}}},
		{"key1=value1&key2=value2", Params{"key1": {"value1"}, "key2": {"value2"}}},
		{"key1=value1&key1=value2", Params{"key1": {"value1", "value2"}}},
		{"key1=", Params{"key1": {""}}},                    // 测试空值
		{"key1&key2=", Params{"key1": {""}, "key2": {""}}}, // 测试多个空值
		// {"key1=value1;key2=value2", Params{"key1": {"value1"}, "key2": {"value2"}}}, // 测试分号
	}

	for _, test := range tests {
		p := Params{}
		p.Decode(test.raw)
		if !equalParams(p, test.expected) {
			t.Errorf("Params.Decode(%q) = %v; want %v", test.raw, p, test.expected)
		}
	}
}

// 辅助函数：比较两个 Params 是否相等
func equalParams(a, b Params) bool {
	if len(a) != len(b) {
		return false
	}
	for k, v := range a {
		if len(v) != len(b[k]) {
			return false
		}
		for i, val := range v {
			if val != b[k][i] {
				return false
			}
		}
	}
	return true
}
