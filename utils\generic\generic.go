//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-15 16:43:14
//FilePath: /yaml_scan/utils/generic/generic.go
//Description:

package generic

// EqualsAny :是一个泛型函数，用于检查给定的基础值是否等于提供的任意其他值
// 检查类型 T 的基础值是否等于提供的任意其他类型 T 的值。
// T 是一个类型参数，必须是可比较的，意味着该类型的值可以使用 == 运算符进行比较。
//
//	@param base T: 要与其他值进行比较的基础值。
//	@param all ...T: 一个可变参数列表，包含要与基础值进行比较的其他值。
//	@return bool bool: 如果 base 等于 all 中的任何一个值，则返回 true；否则返回 false。
func EqualsAny[T comparable](base T, all ...T) bool {
	for _, v := range all {
		if v == base {
			return true
		}
	}
	return false
}
