package formatter

import (
	"testing"
	"time"

	jsoniter "github.com/json-iterator/go"
)


func TestJSONFortmat(t *testing.T) {

	tests := []struct {
		name     string
		event    *LogEvent
		expected string
	}{
		{
			name: "Basic log event",
			event: &LogEvent{
				Message: "This is a test message",
				Metadata: map[string]string{
					"label": "INFO",
				},
			},
			expected: `{"level":"INFO","msg":"This is a test message","timestamp":"` + time.Now().Format("2006-01-02 15:04:05") + `"}`,
		},
		{
			name: "Log event with additional metadata",
			event: &LogEvent{
				Message: "Another test message",
				Metadata: map[string]string{
					"label": "ERROR",
					"user":  "admin",
				},
			},
			expected: `{"level":"ERROR","msg":"Another test message","timestamp":"` + time.Now().Format("2006-01-02 15:04:05") + `","user":"admin"}`,
		},
	}


	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := NewJSON().Format(tt.event)
			if err != nil {
				t.Fatalf("Format() returned an error: %v", err)
			}

			// 将结果转换为字符串进行比较
			resultStr := string(result)

			// 由于时间戳是动态生成的，使用 jsoniter 解析并比较
			var resultMap, expectedMap map[string]interface{}
			if err := jsoniter.Unmarshal([]byte(resultStr), &resultMap); err != nil {
				t.Fatalf("Failed to unmarshal result: %v", err)
			}
			if err := jsoniter.Unmarshal([]byte(tt.expected), &expectedMap); err != nil {
				t.Fatalf("Failed to unmarshal expected: %v", err)
			}

			// 比较结果中的时间戳
			if resultMap["timestamp"] == expectedMap["timestamp"] {
				delete(resultMap, "timestamp")
				delete(expectedMap, "timestamp")
			}

			// 比较结果和期望值
			if !equal(resultMap, expectedMap) {
				t.Errorf("Format() = %v, want %v", resultMap, expectedMap)
			}
		})
	}

}


// equal 比较两个 map 是否相等
func equal(a, b map[string]interface{}) bool {
	if len(a) != len(b) {
		return false
	}
	for k, v := range a {
		if b[k] != v {
			return false
		}
	}
	return true
}