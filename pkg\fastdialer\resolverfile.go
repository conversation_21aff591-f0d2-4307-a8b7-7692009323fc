// Author: chenjb
// Version: V1.0
// Date: 2025-05-20 16:46:23
// FilePath: /yaml_scan/pkg/fastdialer/resolverfile.go
// Description:
package fastdialer

import (
	"bufio"
	"net"
	"os"
	"path/filepath"
	"strings"

	"yaml_scan/pkg/fastdialer/metafiles"

	"github.com/dimchansky/utfbom"

)

var (
	// MaxResolverEntries 限制从解析器文件中读取的最大条目数量
	MaxResolverEntries = 4096
)

const ResolverFilePath = "/etc/resolv.conf"


// loadResolverFile 加载系统DNS解析服务器配置文件
// @return []string []string: DNS解析服务器地址列表，格式为"IP:53"
// @return error error: 可能的错误，如文件不存在或读取失败
func loadResolverFile() ([]string, error) {
	// 展开环境变量并转换路径分隔符
	osResolversFilePath := os.ExpandEnv(filepath.FromSlash(ResolverFilePath))

	// 如果设置了RESOLVERS_PATH环境变量，则使用它指定的路径
	if env, isset := os.LookupEnv("RESOLVERS_PATH"); isset && len(env) > 0 {
		osResolversFilePath = os.ExpandEnv(filepath.FromSlash(env))
	}

	// 打开解析器配置文件
	file, err := os.Open(osResolversFilePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	// 存储解析到的DNS服务器地址
	var systemResolvers []string

	// 创建扫描器，跳过UTF字节顺序标记
	scanner := bufio.NewScanner(utfbom.SkipOnly(file))
	for scanner.Scan() {
		// 检查是否超过最大条目限制，-1表示无限制
		if MaxResolverEntries != -1 && len(systemResolvers) >= MaxResolverEntries {
			break
		}
		// 处理每一行，提取解析器IP
		resolverIP := HandleResolverLine(scanner.Text())
		if resolverIP == "" {
			continue
		}
		// 将IP和标准DNS端口（53）组合成"IP:53"格式
		systemResolvers = append(systemResolvers, net.JoinHostPort(resolverIP, "53"))
	}
	return systemResolvers, nil
}


// HandleResolverLine  处理resolv.conf文件中的一行
// @param raw string:  配置文件中的一行文本
// @return ip string: 解析出的DNS服务器IP地址，如果行无效则返回空字符串
func HandleResolverLine(raw string) (ip string) {
	// 忽略注释行
	if metafiles.IsComment(raw) {
		return
	}

	// 修剪行内注释
	if metafiles.HasComment(raw) {
		commentSplit := strings.Split(raw, metafiles.CommentChar)
		raw = commentSplit[0]
	}

	// 分割为字段
	fields := strings.Fields(raw)
	if len(fields) == 0 {
		return
	}

	// 检查是否为nameserver行
	nameserverPrefix := fields[0]
	if nameserverPrefix != "nameserver" {
		return
	}

	// 获取IP地址并验证有效性
	ip = fields[1]
	if net.ParseIP(ip) == nil {
		return
	}

	return ip
}