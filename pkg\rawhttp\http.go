//
// Author: chenjb
// Version: V1.0
// Date: 2025-07-22 17:43:47
// FilePath: /yaml_scan/pkg/rawhttp/http.go
// Description: HTTP默认客户端定义，提供全局可用的原始HTTP客户端实例

// Package rawhttp HTTP默认客户端模块
package rawhttp


// DefaultClient 是用于执行原始HTTP请求的默认HTTP客户端
// 这是一个全局可用的客户端实例，使用默认配置和新的拨号器
// 可以直接使用此客户端进行HTTP请求，无需手动创建客户端实例
var DefaultClient = Client{
	dialer:  new(dialer),    // 创建新的拨号器实例，用于管理网络连接
	Options: DefaultOptions, // 使用默认的客户端选项配置
}


