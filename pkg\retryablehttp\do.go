// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 17:01:27
// FilePath: /yaml_scan/pkg/retryablehttp/do.go
// Description: 实现了HTTP请求的执行和重试逻辑，包括请求发送、响应处理和重试策略的应用
package retryablehttp

import (
	"context"
	"crypto/tls"
	"fmt"
	"io"
	"net/http"
	"net/http/httptrace"
	"time"

	stringsutil "yaml_scan/utils/strings"

	dac "yaml_scan/pkg/retryablehttp/digest_auth_client"
)

type ContextOverride string

const (
	// RETRY_MAX 是在上下文中指定最大重试次数的键
	RETRY_MAX               ContextOverride = "retry-max"
	// closeConnectionsCounter 是在关闭空闲连接前的请求计数器阈值
	closeConnectionsCounter                 = 100
)

// Do 封装了调用HTTP方法并处理重试的逻辑
//
// Do方法发送HTTP请求并根据配置的重试策略处理重试。
// 它会跟踪请求指标、应用钩子函数，并根据重试策略决定何时重试。
// @receiver c
// @param req *Request: 要执行的HTTP请求，包含所有元数据
// @return *http.Response *http.Response: HTTP响应，如果请求成功
// @return error error: 可能的错误
func (c *Client) Do(req *Request) (*http.Response, error) {
	var resp *http.Response
	var err error

	// 创建一个主上下文，用作主要超时控制
	// 这个上下文会在函数结束时被取消
	mainCtx, cancel := context.WithTimeout(context.Background(), c.options.Timeout)
	defer cancel()

	// 确定最大重试次数，优先使用请求上下文中的值（如果存在）
	retryMax := c.options.RetryMax
	if ctxRetryMax := req.Context().Value(RETRY_MAX); ctxRetryMax != nil {
		if maxRetriesParsed, ok := ctxRetryMax.(int); ok {
			retryMax = maxRetriesParsed
		}
	}

	// 开始重试循环
	for i := 0; ; i++ {
		// 请求体可以被多次读取，因此无需回退
		// 如果设置了请求日志钩子，则在每次请求前调用
		if c.RequestLogHook != nil {
			c.RequestLogHook(req.Request, i)
		}

		// 如果启用了跟踪功能，则为请求添加跟踪信息
		if c.options.Trace {
			c.wrapContextWithTrace(req)
		}

		// 根据认证类型选择不同的请求发送方式
		if req.hasAuth() && req.Auth.Type == DigestAuth {
			digestTransport := dac.NewTransport(req.Auth.Username, req.Auth.Password)
			digestTransport.HTTPClient = c.HTTPClient
			resp, err = digestTransport.RoundTrip(req.Request)
		} else {
			// 使用标准方式发送请求
			resp, err = c.HTTPClient.Do(req.Request)
		}

		// 检查是否应该继续重试
		checkOK, checkErr := c.CheckRetry(req.Context(), resp, err)

		// 如果错误与HTTP协议版本不兼容相关，尝试使用HTTP/2客户端重试
		// 这处理了服务器返回HTTP/2响应但客户端使用HTTP/1.x的情况
		if err != nil && stringsutil.ContainsAny(err.Error(), "net/http: HTTP/1.x transport connection broken: malformed HTTP version \"HTTP/2\"", "net/http: HTTP/1.x transport connection broken: malformed HTTP response") {
			resp, err = c.HTTPClient2.Do(req.Request)
			checkOK, checkErr = c.CheckRetry(req.Context(), resp, err)
		}

		if err != nil {
			// 如果请求失败，增加失败计数器
			req.Metrics.Failures++
		} else {
			// 无论是否继续重试，如果设置了响应日志钩子，都调用它记录响应
			if c.ResponseLogHook != nil {
				c.ResponseLogHook(resp)
			}
		}

		// 基于CheckRetry的结果决定是否继续
		if !checkOK {
			if checkErr != nil {
				err = checkErr
			}
			// 关闭空闲连接并返回结果
			c.closeIdleConnections()
			return resp, err
		}

		// 检查是否已达到最大重试次数
		// 在排空响应体之前进行此检查，避免不必要的I/O操作
		remain := retryMax - i
		if remain <= 0 {
			break
		}

		// 增加重试计数器，因为将要进行一次重试
		req.Metrics.Retries++

		// 准备重试，排空响应体以便重用连接
		if err == nil && resp != nil {
			c.drainBody(req, resp)
		}

		// 根据退避策略计算等待时间，然后重试
		// 如果上下文被取消，则立即返回
		wait := c.Backoff(c.options.RetryWaitMin, c.options.RetryWaitMax, i, resp)

		// 检查上下文是否已完成
		// 否则，等待指定的时间后再次尝试
		// 使用标签明确指定要跳出的部分
	selectstatement:
		select {
		case <-mainCtx.Done():
			// 主上下文已完成，跳出select语句
			break selectstatement
		case <-req.Context().Done():
			// 请求上下文已完成，关闭连接并返回错误
			c.closeIdleConnections()
			return nil, req.Context().Err()
		case <-time.After(wait):
			// 等待时间已过，继续下一次循环
		}
	}

	// 如果设置了错误处理器，则使用它处理最终结果
	if c.ErrorHandler != nil {
		c.closeIdleConnections()
		return c.ErrorHandler(resp, err, retryMax+1)
	}

	// 默认情况下，关闭响应体并返回错误，不返回响应
	if resp != nil {
		resp.Body.Close()
	}
	c.closeIdleConnections()
	return nil, fmt.Errorf("%s %s giving up after %d attempts: %w", req.Method, req.URL, retryMax+1, err)
}

// PassthroughErrorHandler  是一个直接传递最终请求的net/http库值的ErrorHandler
// @param resp *http.Response: HTTP响应
// @param err error: 错误
// @param _ int: 尝试次数（未使用）
// @return *http.Response *http.Response: 原始HTTP响应
// @return error error: 
func PassthroughErrorHandler(resp *http.Response, err error, _ int) (*http.Response, error) {
	return resp, err
}

// drainBody 尝试读取响应体，以便能够重用连接
// @receiver c 
// @param req *Request: HTTP请求，用于更新指标
// @param resp *http.Response: 要排空的HTTP响应
func (c *Client) drainBody(req *Request, resp *http.Response) {
	// 使用io.Copy将响应体内容复制到丢弃器，并限制读取量
	_, err := io.Copy(io.Discard, io.LimitReader(resp.Body, c.options.RespReadLimit))
	if err != nil {
		req.Metrics.DrainErrors++
	}
	resp.Body.Close()
}

// closeIdleConnections 根据设置关闭空闲连接
// @receiver c 
func (c *Client) closeIdleConnections() {
	// 检查是否需要关闭空闲连接
	if c.options.KillIdleConn {
		// 增加请求计数器，如果未达到阈值
		if c.requestCounter.Load() < closeConnectionsCounter {
			c.requestCounter.Add(1)
		} else {
			// 重置计数器并关闭空闲连接
			c.requestCounter.Store(0)
			c.HTTPClient.CloseIdleConnections()
		}
	}
}

// wrapContextWithTrace 为请求上下文添加HTTP跟踪功能
// @receiver c
// @param req *Request: 要添加跟踪的请求
func (c *Client) wrapContextWithTrace(req *Request) {
	// 创建一个新的跟踪信息结构
	traceInfo := &TraceInfo{}
	trace := &httptrace.ClientTrace{
		// 当获取到连接时记录时间和信息
		GotConn: func(connInfo httptrace.GotConnInfo) {
			traceInfo.GotConn = TraceEventInfo{
				Time: time.Now(),
				Info: connInfo,
			}
		},
		// 当DNS解析完成时记录时间和信息
		DNSDone: func(dnsInfo httptrace.DNSDoneInfo) {
			traceInfo.DNSDone = TraceEventInfo{
				Time: time.Now(),
				Info: dnsInfo,
			}
		},
		// 当开始获取连接时记录时间和目标信息
		GetConn: func(hostPort string) {
			traceInfo.GetConn = TraceEventInfo{
				Time: time.Now(),
				Info: hostPort,
			}
		},
		// 当将连接放回空闲池时记录时间和错误信息
		PutIdleConn: func(err error) {
			traceInfo.PutIdleConn = TraceEventInfo{
				Time: time.Now(),
				Info: err,
			}
		},
		// 当收到第一个响应字节时记录时间
		GotFirstResponseByte: func() {
			traceInfo.GotFirstResponseByte = TraceEventInfo{
				Time: time.Now(),
			}
		},
		// 当收到100 Continue响应时记录时间
		Got100Continue: func() {
			traceInfo.Got100Continue = TraceEventInfo{
				Time: time.Now(),
			}
		},
		// 当开始DNS解析时记录时间和信息
		DNSStart: func(di httptrace.DNSStartInfo) {
			traceInfo.DNSStart = TraceEventInfo{
				Time: time.Now(),
				Info: di,
			}
		},
		// 当开始建立连接时记录时间和网络信息
		ConnectStart: func(network, addr string) {
			traceInfo.ConnectStart = TraceEventInfo{
				Time: time.Now(),
				Info: struct {
					Network, Addr string
				}{network, addr},
			}
		},
		// 当连接建立完成时记录时间和结果
		ConnectDone: func(network, addr string, err error) {
			if err == nil {
				traceInfo.ConnectDone = TraceEventInfo{
					Time: time.Now(),
					Info: struct {
						Network, Addr string
						Error         error
					}{network, addr, err},
				}
			}
		},
		// 当开始TLS握手时记录时间
		TLSHandshakeStart: func() {
			traceInfo.TLSHandshakeStart = TraceEventInfo{
				Time: time.Now(),
			}
		},
		// 当TLS握手完成时记录时间和状态
		TLSHandshakeDone: func(cs tls.ConnectionState, err error) {
			if err == nil {
				traceInfo.TLSHandshakeDone = TraceEventInfo{
					Time: time.Now(),
					Info: struct {
						ConnectionState tls.ConnectionState
						Error           error
					}{cs, err},
				}
			}
		},
		// 当写入请求头时记录时间
		WroteHeaders: func() {
			traceInfo.WroteHeaders = TraceEventInfo{
				Time: time.Now(),
			}
		},
		// 当写入请求完成时记录时间和状态
		WroteRequest: func(wri httptrace.WroteRequestInfo) {
			traceInfo.WroteRequest = TraceEventInfo{
				Time: time.Now(),
				Info: wri,
			}
		},
	}
	req.TraceInfo = traceInfo

	// 将跟踪添加到请求上下文中
	req.Request = req.Request.WithContext(httptrace.WithClientTrace(req.Request.Context(), trace))
}
