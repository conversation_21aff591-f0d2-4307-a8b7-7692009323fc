// Author: chenjb
// Version: V1.0
// Date: 2025-05-22 20:07:26
// FilePath: /yaml_scan/pkg/fastdialer/util_test.go
// Description:
// TestAsAsciiIdempotent 验证asAscii函数是幂等的
// 确保对结果应用多次asAscii函数不会改变结果
package fastdialer

import (
	"crypto/tls"
	"testing"

	ztls "github.com/zmap/zcrypto/tls"

	"github.com/stretchr/testify/require"
)

func TestAsAsciiIdempotent(t *testing.T) {
	testCases := []struct {
		name     string
		hostname string
	}{
		{
			name:     "纯ASCII域名",
			hostname: "example.com",
		},
		{
			name:     "国际化域名",
			hostname: "例子.测试", // 中文域名示例
		},
		{
			name:     "中文域名",
			hostname: "北京大学.中国",
		},
		{
			name:     "俄文域名",
			hostname: "пример.рф",
		},
		{
			name:     "混合域名",
			hostname: "test.例子.com",
		},
		{
			name:     "包含特殊字符",
			hostname: "test-123.例子.com",
		},
		{
			name:     "带端口的域名",
			hostname: "例子.测试:8080",
		},
		{
			name:     "空字符串",
			hostname: "",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 应用一次asAscii函数
			once := asAscii(tc.hostname)
			
			// 应用两次asAscii函数
			twice := asAscii(asAscii(tc.hostname))
			
			// 应用三次asAscii函数
			thrice := asAscii(asAscii(asAscii(tc.hostname)))
			
			// 验证所有结果都相同，证明函数是幂等的
			require.Equal(t, once, twice, "第一次和第二次asAscii转换结果应该相同")
			require.Equal(t, twice, thrice, "第二次和第三次asAscii转换结果应该相同")
			
			// 测试直接应用asAscii到结果
			onceAgain := asAscii(once)
			require.Equal(t, once, onceAgain, "对结果再次应用asAscii应该不变")
			
			// 输出调试信息
			t.Logf("原始域名: %s, 转换后: %s", tc.hostname, once)
		})
	}
} 

// TestAsAscii 测试将Unicode主机名转换为ASCII的函数
func TestAsAscii(t *testing.T) {
	testCases := []struct {
		name     string
		hostname string
		expected string
	}{
		{
			name:     "普通ASCII域名",
			hostname: "example.com",
			expected: "example.com",
		},
		{
			name:     "包含中文的域名",
			hostname: "例子.com",
			expected: "xn--fsqu00a.com", // 中文"例子"的Punycode编码
		},
		{
			name:     "包含特殊字符的域名",
			hostname: "exämple.com",
			expected: "xn--exmple-cua.com",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := asAscii(tc.hostname)
			require.Equal(t, tc.expected, result, "转换结果应与预期一致")
		})
	}
}

// TestAsZTLSConfig 测试从标准TLS配置到zcrypto TLS配置的转换
func TestAsZTLSConfig(t *testing.T) {
	// 创建标准TLS配置
	tlsConfig := &tls.Config{
		ServerName:         "example.com",
		InsecureSkipVerify: true,
		MinVersion:         tls.VersionTLS12,
		MaxVersion:         tls.VersionTLS13,
		NextProtos:         []string{"h2", "http/1.1"},
	}

	// 转换为zcrypto配置
	ztlsConfig, err := AsZTLSConfig(tlsConfig)

	// 验证转换成功，没有错误
	require.NoError(t, err, "转换过程不应产生错误")
	require.NotNil(t, ztlsConfig, "转换结果不应为nil")

	// 验证各字段正确转换
	require.Equal(t, tlsConfig.ServerName, ztlsConfig.ServerName, "ServerName字段应正确转换")
	require.Equal(t, tlsConfig.InsecureSkipVerify, ztlsConfig.InsecureSkipVerify, "InsecureSkipVerify字段应正确转换")
	require.Equal(t, tlsConfig.MinVersion, ztlsConfig.MinVersion, "MinVersion字段应正确转换")
	require.Equal(t, tlsConfig.MaxVersion, ztlsConfig.MaxVersion, "MaxVersion字段应正确转换")
	require.Equal(t, tlsConfig.NextProtos, ztlsConfig.NextProtos, "NextProtos字段应正确转换")
}

// TestAsTLSConfig 测试从zcrypto TLS配置到标准TLS配置的转换
func TestAsTLSConfig(t *testing.T) {
	// 创建zcrypto TLS配置
	ztlsConfig := &ztls.Config{
		ServerName:         "example.com",
		InsecureSkipVerify: true,
		MinVersion:         tls.VersionTLS12,
		MaxVersion:         tls.VersionTLS13,
		NextProtos:         []string{"h2", "http/1.1"},
	}

	// 转换为标准配置
	tlsConfig, err := AsTLSConfig(ztlsConfig)

	// 验证转换成功，没有错误
	require.NoError(t, err, "转换过程不应产生错误")
	require.NotNil(t, tlsConfig, "转换结果不应为nil")

	// 验证各字段正确转换
	require.Equal(t, ztlsConfig.ServerName, tlsConfig.ServerName, "ServerName字段应正确转换")
	require.Equal(t, ztlsConfig.InsecureSkipVerify, tlsConfig.InsecureSkipVerify, "InsecureSkipVerify字段应正确转换")
	require.Equal(t, ztlsConfig.MinVersion, tlsConfig.MinVersion, "MinVersion字段应正确转换")
	require.Equal(t, ztlsConfig.MaxVersion, tlsConfig.MaxVersion, "MaxVersion字段应正确转换")
	require.Equal(t, ztlsConfig.NextProtos, tlsConfig.NextProtos, "NextProtos字段应正确转换")
}

// TestIsTLS13 测试是否正确识别TLS 1.3配置
func TestIsTLS13(t *testing.T) {
	// 测试标准库TLS配置
	t.Run("标准库TLS配置-TLS1.3", func(t *testing.T) {
		tlsConfig := &tls.Config{MinVersion: tls.VersionTLS13}
		require.True(t, IsTLS13(tlsConfig), "应识别为TLS 1.3配置")
	})

	t.Run("标准库TLS配置-非TLS1.3", func(t *testing.T) {
		tlsConfig := &tls.Config{MinVersion: tls.VersionTLS12}
		require.False(t, IsTLS13(tlsConfig), "不应识别为TLS 1.3配置")
	})

	// 测试zcrypto TLS配置
	t.Run("zcrypto TLS配置-TLS1.3", func(t *testing.T) {
		ztlsConfig := &ztls.Config{MinVersion: tls.VersionTLS13}
		require.True(t, IsTLS13(ztlsConfig), "应识别为TLS 1.3配置")
	})

	t.Run("zcrypto TLS配置-非TLS1.3", func(t *testing.T) {
		ztlsConfig := &ztls.Config{MinVersion: tls.VersionTLS12}
		require.False(t, IsTLS13(ztlsConfig), "不应识别为TLS 1.3配置")
	})

	// 测试非TLS配置类型
	t.Run("非TLS配置类型", func(t *testing.T) {
		nonTLSConfig := "不是TLS配置"
		require.False(t, IsTLS13(nonTLSConfig), "非TLS配置应返回false")
	})
}
