// Author: chenjb
// Version: V1.0
// Date: 2025-06-19 15:41:09
// FilePath: /yaml_scan/pkg/dnsx/libs/dnsx/dnsx.go
// Description: DNS查询包，提供DNS查询相关功能
package dnsx

import (
	"encoding/json"
	"errors"
	"yaml_scan/pkg/retryabledns"
	iputil "yaml_scan/utils/ip"
	sliceutil "yaml_scan/utils/slice"

	miekgdns "github.com/miekg/dns"
)

// DNSX 是用于执行DNS查询的结构体
type DNSX struct {
	dnsClient *retryabledns.Client // DNS客户端，用于执行实际的DNS查询
	Options   *Options             // DNS查询选项配置
}

// Options 包含DNS查询的配置选项
type Options struct {
	BaseResolvers     []string // 基础DNS解析器列表
	MaxRetries        int      // 最大重试次数
	QuestionTypes     []uint16 // DNS查询类型
	Trace             bool     // 是否开启DNS追踪
	TraceMaxRecursion int      // 追踪最大递归深度
	Hostsfile         bool     // 是否使用本地hosts文件
	QueryAll          bool     // 是否查询所有DNS记录类型
	Proxy             string   // 代理服务器地址
}

// ResponseData 用于显示输出结果的数据结构
type ResponseData struct {
	*retryabledns.DNSData // 嵌入DNS数据
}

// MarshalOption 定义一个函数类型，用于自定义JSON序列化的选项
type MarshalOption func(d *ResponseData)

// WithoutAllRecords 返回一个MarshalOption，用于在JSON序列化时排除AllRecords字段
// @return MarshalOption MarshalOption: 一个自定义序列化选项函数
func WithoutAllRecords() MarshalOption {
	return func(d *ResponseData) {
		d.AllRecords = nil
	}
}

// JSON 将ResponseData序列化为JSON字符串
// @receiver d
// @param options ...MarshalOption: 可变参数，序列化选项函数列表
// @return string string: 序列化后的JSON字符串
// @return error error: 可能的错误
func (d *ResponseData) JSON(options ...MarshalOption) (string, error) {
	dataToMarshal := *d
	for _, option := range options {
		option(d)
	}
	b, err := json.Marshal(dataToMarshal)
	return string(b), err
}

// DefaultOptions 包含默认的DNS查询配置选项
var DefaultOptions = Options{
	BaseResolvers:     DefaultResolvers,
	MaxRetries:        5,
	QuestionTypes:     []uint16{miekgdns.TypeA},
	TraceMaxRecursion: 255,
	Trace:             true,
	Hostsfile:         true,
}

// DefaultResolvers 包含已知可信的DNS解析器列表
var DefaultResolvers = []string{
	"udp:*******:53",         // Cloudflare
	"udp:*******:53",         // Cloudflare
	"udp:*******:53",         // Google
	"udp:*******:53",         // Google
	"udp:*******:53",         // Quad9
	"udp:***************:53", // Quad9
	"udp:**************:53",  // Open DNS
	"udp:**************:53",  // Open DNS
}

// New 创建一个DNS解析器
// @param options Options: DNS查询选项配置
// @return *DNSX *DNSX: 创建的DNS解析器实例
// @return error error: 可能的错误
func New(options Options) (*DNSX, error) {
	// 构建重试DNS选项
	retryablednsOptions := retryabledns.Options{
		BaseResolvers: options.BaseResolvers,
		MaxRetries:    options.MaxRetries,
		Hostsfile:     options.Hostsfile,
		Proxy:         options.Proxy,
	}

	// 创建DNS客户端
	dnsClient, err := retryabledns.NewWithOptions(retryablednsOptions)
	if err != nil {
		return nil, err
	}
	// 开启TCP回退
	dnsClient.TCPFallback = true
	// 创建DNSX实例
	dnsx := &DNSX{dnsClient: dnsClient, Options: &options}
	return dnsx, nil
}

// Lookup 执行DNS A记录查询并返回对应的IP地址
// @receiver d 
// @param hostname string: 要查询的主机名
// @return []string []string:  查询结果的IP地址列表
// @return error error: 可能的错误
func (d *DNSX) Lookup(hostname string) ([]string, error) {
	// 如果输入已经是IP地址，则直接返回
	if iputil.IsIP(hostname) {
		return []string{hostname}, nil
	}

	// 执行DNS解析
	dnsdata, err := d.dnsClient.Resolve(hostname)
	if err != nil {
		return nil, err
	}

	// 检查是否有A记录
	if dnsdata == nil || len(dnsdata.A) == 0 {
		return []string{}, errors.New("no ips found")
	}

	// 返回A记录IP地址列表
	return dnsdata.A, nil
}

// QueryOne 执行指定类型的DNS查询并返回原始响应
// @receiver d 
// @param hostname string: 要查询的主机名
// @return *retryabledns.DNSData *retryabledns.DNSData:  DNS查询结果数据
// @return error error: 可能的错误
func (d *DNSX) QueryOne(hostname string) (*retryabledns.DNSData, error) {
	return d.dnsClient.Query(hostname, d.Options.QuestionTypes[0])
}

// QueryMultiple 执行多种类型的DNS查询并返回原始响应
// @receiver d 
// @param hostname string: 要查询的主机名
// @return *retryabledns.DNSData *retryabledns.DNSData: DNS查询结果数据
// @return error error: 可能的错误
func (d *DNSX) QueryMultiple(hostname string) (*retryabledns.DNSData, error) {
	// 根据输入类型过滤查询类型，避免不必要的PTR查询以减少执行时间
	filteredQuestionTypes := d.Options.QuestionTypes
	if d.Options.QueryAll {
		isIP := iputil.IsIP(hostname)
		if !isIP {
			// 如果不是IP地址，则排除PTR查询
			filteredQuestionTypes = sliceutil.PruneEqual(filteredQuestionTypes, miekgdns.TypePTR)
		} else {
			// 如果是IP地址，则只进行PTR查询
			filteredQuestionTypes = []uint16{miekgdns.TypePTR}
		}
	}
	return d.dnsClient.QueryMultiple(hostname, filteredQuestionTypes)
}

// Trace  执行DNS追踪，返回原始响应
// @receiver d 
// @param hostname string:  要追踪的主机名
// @return *retryabledns.TraceData *retryabledns.TraceData:  DNS追踪结果数据
// @return error error: 可能的错误
func (d *DNSX) Trace(hostname string) (*retryabledns.TraceData, error) {
	return d.dnsClient.Trace(hostname, d.Options.QuestionTypes[0], d.Options.TraceMaxRecursion)
}

// AXFR 执行DNS区域传送查询
// @receiver d 
// @param hostname string: 要查询的主机名
// @return *retryabledns.AXFRData *retryabledns.AXFRData:DNS区域传送结果数据 
// @return error error: 可能的错误
func (d *DNSX) AXFR(hostname string) (*retryabledns.AXFRData, error) {
	return d.dnsClient.AXFR(hostname)
}
