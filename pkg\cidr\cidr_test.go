// Author: chenjb
// Version: V1.0
// Date: 2025-06-04 17:24:16
// FilePath: /yaml_scan/pkg/cidr/cidr_test.go
// Description:
package cidr

import (
	"math/big"
	"net"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestAddressRange 测试AddressRange函数，验证能否正确获取CIDR范围内的第一个和最后一个IP地址
func TestAddressRange(t *testing.T) {
	// 测试用例
	testCases := []struct {
		cidr     string // CIDR表示法字符串
		firstIP  string // 预期的第一个IP地址
		lastIP   string // 预期的最后一个IP地址
		hasError bool   // 是否应该返回错误
	}{
		// IPv4测试用例
		{"***********/24", "***********", "*************", false},
		{"10.0.0.0/8", "10.0.0.0", "**************", false},
		{"**********/16", "**********", "**************", false},
		{"***********/32", "***********", "***********", false}, // 单个IP地址

		// IPv6测试用例
		{"2001:db8::/32", "2001:db8::", "2001:db8:ffff:ffff:ffff:ffff:ffff:ffff", false},
		{"::1/128", "::1", "::1", false}, // IPv6本地环回地址
	}

	for _, tc := range testCases {
		t.Run(tc.cidr, func(t *testing.T) {
			// 解析CIDR字符串
			_, network, err := net.ParseCIDR(tc.cidr)
			require.NoError(t, err, "解析CIDR字符串时不应出错")

			// 调用被测试函数
			first, last, err := AddressRange(network)

			// 验证结果
			if tc.hasError {
				require.Error(t, err, "应该返回错误")
			} else {
				require.NoError(t, err, "不应返回错误")
				require.Equal(t, tc.firstIP, first.String(), "第一个IP地址不匹配")
				require.Equal(t, tc.lastIP, last.String(), "最后一个IP地址不匹配")
			}
		})
	}
}

// TestIPToInteger 测试IPToInteger函数，验证能否正确将IP地址转换为整数
func TestIPToInteger(t *testing.T) {
	// 测试用例
	testCases := []struct {
		ipStr    string   // IP地址字符串
		expected *big.Int // 预期的整数结果
		bits     int      // 预期的比特长度
		hasError bool     // 是否应该返回错误
	}{
		// IPv4测试用例
		{"***********", big.NewInt(0).SetBytes(net.ParseIP("***********").To4()), 32, false},
		{"********", big.NewInt(0).SetBytes(net.ParseIP("********").To4()), 32, false},
		{"0.0.0.0", big.NewInt(0), 32, false},
		{"***************", big.NewInt(0).SetBytes([]byte{255, 255, 255, 255}), 32, false},

		// IPv6测试用例
		{"::1", big.NewInt(1), 128, false}, // IPv6本地环回地址
		{"2001:db8::1", big.NewInt(0).SetBytes(net.ParseIP("2001:db8::1")), 128, false},
	}

	for _, tc := range testCases {
		t.Run(tc.ipStr, func(t *testing.T) {
			// 解析IP地址
			ip := net.ParseIP(tc.ipStr)
			require.NotNil(t, ip, "解析IP地址失败")

			// 调用被测试函数
			result, bits, err := IPToInteger(ip)

			// 验证结果
			if tc.hasError {
				require.Error(t, err, "应该返回错误")
			} else {
				require.NoError(t, err, "不应返回错误")
				require.Equal(t, tc.bits, bits, "比特长度不匹配")
				require.Equal(t, 0, tc.expected.Cmp(result), "整数值不匹配")
			}
		})
	}
}

// TestIntegerToIP 测试IntegerToIP函数，验证能否正确将整数转换为IP地址
func TestIntegerToIP(t *testing.T) {
	// 测试用例
	testCases := []struct {
		ipInt    *big.Int // IP地址的整数表示
		bits     int      // IP地址的比特长度
		expected string   // 预期的IP地址字符串
	}{
		// IPv4测试用例
		{big.NewInt(3232235777), 32, "***********"},     // 3232235777 = ***********
		{big.NewInt(167772161), 32, "********"},         // 167772161 = ********
		{big.NewInt(0), 32, "0.0.0.0"},                  // 0 = 0.0.0.0
		{big.NewInt(4294967295), 32, "***************"}, // 4294967295 = ***************

		// IPv6测试用例
		{big.NewInt(1), 128, "::1"}, // 1 = ::1 (IPv6本地环回地址)
	}

	for _, tc := range testCases {
		t.Run(tc.expected, func(t *testing.T) {
			// 调用被测试函数
			result := IntegerToIP(tc.ipInt, tc.bits)

			// 验证结果
			// 注意：有些IP地址可能有多种字符串表示形式，所以我们先解析回IP，再比较
			expectedIP := net.ParseIP(tc.expected)
			require.NotNil(t, expectedIP, "解析预期IP地址失败")
			require.True(t, result.Equal(expectedIP),
				"IP地址不匹配，预期: %s, 实际: %s", tc.expected, result.String())
		})
	}
}

// TestAddressCount 测试AddressCount函数，验证能否正确计算CIDR范围内的IP地址数量
func TestAddressCount(t *testing.T) {
	// 测试用例
	testCases := []struct {
		cidr     string // CIDR表示法字符串
		count    uint64 // 预期的地址数量
		hasError bool   // 是否应该返回错误
	}{
		// IPv4测试用例
		{"***********/24", 256, false},  // 256个地址
		{"***********/28", 16, false},   // 16个地址
		{"***********/32", 1, false},    // 单个IP地址
		{"10.0.0.0/8", 16777216, false}, // 16,777,216个地址

		// IPv6测试用例
		{"2001:db8::/64", 0x0, false}, // 2^64个地址
		{"::1/128", 1, false},         // 单个IPv6地址

		// 错误测试用例
		{"invalid", 0, true}, // 无效CIDR
	}

	for _, tc := range testCases {
		t.Run(tc.cidr, func(t *testing.T) {
			// 调用被测试函数
			count, err := AddressCount(tc.cidr)

			// 验证结果
			if tc.hasError {
				require.Error(t, err, "应该返回错误")
			} else {
				require.NoError(t, err, "不应返回错误")
				require.Equal(t, tc.count, count, "地址数量不匹配")
			}
		})
	}
}

// TestSplitN 测试SplitN函数，验证能否正确将CIDR划分为N个子网
func TestSplitN(t *testing.T) {
	// 测试用例
	testCases := []struct {
		cidr        string // CIDR表示法字符串
		n           int    // 期望划分的子网数量
		subnetCount int    // 预期的实际子网数量
		hasError    bool   // 是否应该返回错误
	}{
		// IPv4测试用例
		{"***********/24", 4, 4, false},  // 划分为4个子网
		{"10.0.0.0/24", 2, 2, false},     // 划分为2个子网
		{"**********/16", 8, 8, false},   // 划分为8个子网
		{"***********/24", 3, 4, false},  // 划分为3个子网
		{"***********/28", 32, 1, false}, // 子网数量超过可用地址数，返回原子网
		{"***********/24", 0, 1, false},  // n <= 1，返回原子网

		// 错误测试用例
		{"invalid", 4, 0, true}, // 无效CIDR
	}

	for _, tc := range testCases {
		t.Run(tc.cidr, func(t *testing.T) {
			// 调用被测试函数
			subnets, err := SplitN(tc.cidr, tc.n)

			// 验证结果
			if tc.hasError {
				require.Error(t, err, "应该返回错误")
			} else {
				require.NoError(t, err, "不应返回错误")
				require.Len(t, subnets, tc.subnetCount, "子网数量不匹配")

				if tc.subnetCount > 1 {
					// 验证子网地址总数是否等于原始CIDR地址总数
					_, network, _ := net.ParseCIDR(tc.cidr)
					originalCount := AddressCountIpnet(network)

					var totalSubnetCount uint64
					for _, subnet := range subnets {
						totalSubnetCount += AddressCountIpnet(subnet)
					}

					require.Equal(t, originalCount, totalSubnetCount, "子网地址总数不等于原始CIDR地址总数")
				}
			}
		})
	}
}

// TestSplitByNumber 测试SplitByNumber函数，验证能否正确将CIDR划分为多个子网
func TestSplitByNumber(t *testing.T) {
	// 测试用例
	testCases := []struct {
		cidr        string // CIDR表示法字符串
		number      int    // 每个子网中期望的主机数量
		subnetCount int    // 预期的子网数量
		hasError    bool   // 是否应该返回错误
	}{
		// IPv4测试用例
		{"***********/24", 64, 4, false},   // 划分为4个子网，每个64个地址
		{"10.0.0.0/24", 128, 2, false},     // 划分为2个子网，每个128个地址
		{"**********/16", 4096, 16, false}, // 划分为16个子网，每个4096个地址
		{"***********/24", 1000, 1, false}, // 无法精确划分，返回1个子网

		// 错误测试用例
		{"invalid", 64, 0, true}, // 无效CIDR
	}

	for _, tc := range testCases {
		t.Run(tc.cidr, func(t *testing.T) {
			// 调用被测试函数
			subnets, err := SplitByNumber(tc.cidr, tc.number)

			// 验证结果
			if tc.hasError {
				require.Error(t, err, "应该返回错误")
			} else {
				require.NoError(t, err, "不应返回错误")
				require.Len(t, subnets, tc.subnetCount, "子网数量不匹配")

				// 验证每个子网的大小是否符合预期
				if tc.subnetCount > 1 {
					// 获取第一个子网的大小
					firstSubnetSize := AddressCountIpnet(subnets[0])
					// 验证所有子网大小是否相同
					for i := 1; i < len(subnets); i++ {
						subnetSize := AddressCountIpnet(subnets[i])
						require.Equal(t, firstSubnetSize, subnetSize, "子网 %d 大小不一致", i)
					}
				}
			}
		})
	}
}

// TestIPAddresses 测试IPAddresses函数，验证能否正确返回CIDR范围内的所有IP地址
func TestIPAddresses(t *testing.T) {
	// 测试用例
	testCases := []struct {
		cidr     string   // CIDR表示法字符串
		expected []string // 预期的IP地址列表（如果太多只检查数量）
		count    int      // 预期的IP地址数量
		hasError bool     // 是否应该返回错误
	}{
		// 小型网络测试用例
		{"***********/30", []string{"***********", "***********", "***********", "***********"}, 4, false},
		{"***********/32", []string{"***********"}, 1, false},

		// 中型网络测试用例（只检查数量，不检查具体内容）
		{"***********/28", nil, 16, false},

		// 错误测试用例
		{"invalid", nil, 0, true}, // 无效CIDR
	}

	for _, tc := range testCases {
		t.Run(tc.cidr, func(t *testing.T) {
			// 调用被测试函数
			ips, err := IPAddresses(tc.cidr)

			// 验证结果
			if tc.hasError {
				require.Error(t, err, "应该返回错误")
			} else {
				require.NoError(t, err, "不应返回错误")
				require.Len(t, ips, tc.count, "IP地址数量不匹配")

				// 对于小型网络，验证具体IP地址
				if tc.expected != nil {
					require.ElementsMatch(t, tc.expected, ips, "IP地址列表不匹配")
				}
			}
		})
	}
}

// TestIPAddressesAsStream 测试IPAddressesAsStream函数，验证能否正确生成IP地址流
func TestIPAddressesAsStream(t *testing.T) {
	// 测试用例
	testCases := []struct {
		cidr     string // CIDR表示法字符串
		count    int    // 预期的IP地址数量
		hasError bool   // 是否应该返回错误
	}{
		// 小型网络测试用例
		{"***********/30", 4, false},
		{"***********/32", 1, false},

		// 中型网络测试用例
		{"***********/28", 16, false},

		// 中型网络测试用例
		{"***********/24", 256, false},

		// 错误测试用例
		{"invalid", 0, true}, // 无效CIDR
	}

	for _, tc := range testCases {
		t.Run(tc.cidr, func(t *testing.T) {
			// 调用被测试函数
			ipsChan, err := IPAddressesAsStream(tc.cidr)

			// 验证结果
			if tc.hasError {
				require.Error(t, err, "应该返回错误")
				require.Nil(t, ipsChan, "应该返回nil通道")
			} else {
				require.NoError(t, err, "不应返回错误")

				// 收集并计数通道中的所有IP地址
				ips := make([]string, 0, tc.count)
				for ip := range ipsChan {
					ips = append(ips, ip)
				}

				require.Len(t, ips, tc.count, "IP地址数量不匹配")
			}
		})
	}
}
