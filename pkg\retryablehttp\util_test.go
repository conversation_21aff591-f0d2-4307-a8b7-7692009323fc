// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-10 16:25:10
// FilePath: /yaml_scan/pkg/retryablehttp/util_test.go
// Description: 
package retryablehttp


import (
	"bytes"
	"errors"
	"io"
	"net/http"
	"strings"
	"testing"

	readerutil "yaml_scan/utils/reader"

	"github.com/stretchr/testify/require"
)

// TestDiscard 测试丢弃响应体并关闭连接的功能
func TestDiscard(t *testing.T) {

	// 创建测试用例
	t.Run("成功丢弃响应体", func(t *testing.T) {
		r := require.New(t)

		// 模拟请求
		req := &Request{
			Metrics: Metrics{},
		}

		// 模拟包含响应体的响应
		respBody := strings.NewReader("测试响应体内容")
		resp := &http.Response{
			Body: io.NopCloser(respBody),
		}

		// 调用Discard函数
		Discard(req, resp, 1024)

		// 验证排空错误计数未增加
		r.Equal(0, req.Metrics.DrainErrors, "正常丢弃不应增加排空错误计数")
	})

	t.Run("丢弃过程中出错", func(t *testing.T) {
		r := require.New(t)

		// 模拟请求
		req := &Request{
			Metrics: Metrics{},
		}

		// 模拟会出错的响应体
		errorReader := &errorReaderCloser{err: errors.New("读取错误")}
		resp := &http.Response{
			Body: errorReader,
		}

		// 调用Discard函数
		Discard(req, resp, 1024)

		// 验证排空错误计数已增加
		r.Equal(1, req.Metrics.DrainErrors, "读取错误应增加排空错误计数")
		r.True(errorReader.closed, "响应体应被关闭")
	})
}

// errorReaderCloser 是一个模拟会产生错误的读取器和关闭器
type errorReaderCloser struct {
	err    error
	closed bool
}

func (e *errorReaderCloser) Read(p []byte) (n int, err error) {
	return 0, e.err
}

func (e *errorReaderCloser) Close() error {
	e.closed = true
	return nil
}

// TestGetLength 测试获取读取器内容长度的功能
func TestGetLength(t *testing.T) {

	// 测试用例1: 字符串读取器
	t.Run("字符串读取器", func(t *testing.T) {
		r := require.New(t)
		reader := strings.NewReader("测试内容") // 中文字符占用3字节/字符
		length, err := getLength(reader)

		r.NoError(err, "获取长度不应有错误")
		r.Equal(int64(12), length, "长度应正确计算") // "测试内容" = 4个中文字符 = 12字节
	})

	// 测试用例2: 字节读取器
	t.Run("字节读取器", func(t *testing.T) {
		r := require.New(t)
		reader := bytes.NewReader([]byte{1, 2, 3, 4, 5})
		length, err := getLength(reader)

		r.NoError(err, "获取长度不应有错误")
		r.Equal(int64(5), length, "长度应正确计算")
	})

	// 测试用例3: 出错的读取器
	t.Run("出错的读取器", func(t *testing.T) {
		r := require.New(t)
		reader := &errorReaderCloser{err: errors.New("读取错误")}
		length, err := getLength(reader)

		r.Error(err, "应返回读取错误")
		r.Equal(int64(0), length, "出错时长度应为0")
	})
}

// TestGetReusableBodyandContentLength 测试获取可重用请求体和内容长度的功能
func TestGetReusableBodyandContentLength(t *testing.T) {

	// 测试用例1: nil请求体
	t.Run("nil请求体", func(t *testing.T) {
		r := require.New(t)

		body, length, err := getReusableBodyandContentLength(nil)

		r.NoError(err, "处理nil请求体不应有错误")
		r.Nil(body, "nil请求体应返回nil读取器")
		r.Equal(int64(0), length, "nil请求体长度应为0")
	})

	// 测试用例2: 字符串请求体
	t.Run("字符串请求体", func(t *testing.T) {
		r := require.New(t)

		body, length, err := getReusableBodyandContentLength("测试数据")

		r.NoError(err, "处理字符串请求体不应有错误")
		r.NotNil(body, "应返回有效的读取器")
		r.Equal(int64(12), length, "长度应正确计算") // "测试数据" = 4个中文字符 = 12字节

		// 验证读取器可重用
		buf := new(bytes.Buffer)
		n, err := io.Copy(buf, body)
		r.NoError(err, "读取内容不应有错误")
		r.Equal(int64(12), n, "应读取全部内容")
		r.Equal("测试数据", buf.String(), "内容应正确")

		// 再次读取，验证可重用性
		// body.Seek(0, io.SeekStart)
		buf.Reset()
		n, err = io.Copy(buf, body)
		r.NoError(err, "再次读取内容不应有错误")
		r.Equal(int64(12), n, "应再次读取全部内容")
		r.Equal("测试数据", buf.String(), "内容应再次正确")
	})

	// 测试用例3: 已存在的ReusableReadCloser
	t.Run("已存在的ReusableReadCloser", func(t *testing.T) {
		r := require.New(t)

		// 首先创建一个ReusableReadCloser
		original, err := readerutil.NewReusableReadCloser("原始数据")
		r.NoError(err, "创建原始读取器不应有错误")

		// 将其传递给函数
		body, length, err := getReusableBodyandContentLength(original)

		r.NoError(err, "处理已存在的读取器不应有错误")
		r.Equal(original, body, "应返回相同的读取器")
		r.Equal(int64(12), length, "长度应正确计算") // "原始数据" = 4个中文字符 = 12字节
	})

	// 测试用例4: 使用生成器函数
	t.Run("生成器函数", func(t *testing.T) {
		r := require.New(t)

		// 创建一个返回Reader的函数
		generator := func() (io.Reader, error) {
			return strings.NewReader("函数生成的数据"), nil
		}

		// 将函数传递给getReusableBodyandContentLength
		body, length, err := getReusableBodyandContentLength(generator)

		r.NoError(err, "处理生成器函数不应有错误")
		r.NotNil(body, "应返回有效的读取器")
		r.Equal(int64(21), length, "长度应正确计算") // "函数生成的数据" = 7个中文字符 = 21字节

		// 读取内容验证
		buf := new(bytes.Buffer)
		io.Copy(buf, body)
		r.Equal("函数生成的数据", buf.String(), "内容应正确")
	})

	// 测试用例5: 生成器函数出错
	t.Run("生成器函数出错", func(t *testing.T) {
		r := require.New(t)

		// 创建一个返回错误的函数
		generator := func() (io.Reader, error) {
			return nil, errors.New("生成器错误")
		}

		// 将函数传递给getReusableBodyandContentLength
		body, length, err := getReusableBodyandContentLength(generator)

		r.Error(err, "生成器错误应被传递")
		r.Equal("生成器错误", err.Error(), "应返回原始错误消息")
		r.Nil(body, "出错时应返回nil读取器")
		r.Equal(int64(0), length, "出错时长度应为0")
	})

	// 测试用例6: 不支持的请求体类型
	t.Run("不支持的请求体类型", func(t *testing.T) {
		r := require.New(t)

		// 创建一个没有实现io.Reader的类型
		type unsupportedType struct{}
		unsupported := unsupportedType{}

		// 将不支持的类型传递给getReusableBodyandContentLength
		body, length, err := getReusableBodyandContentLength(unsupported)

		r.Error(err, "处理不支持的类型应返回错误")
		r.Nil(body, "出错时应返回nil读取器")
		r.Equal(int64(0), length, "出错时长度应为0")
	})
}


