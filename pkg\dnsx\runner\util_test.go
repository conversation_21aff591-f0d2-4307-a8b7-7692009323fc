// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-23 16:23:47
// FilePath: /yaml_scan/pkg/dnsx/runner/util_test.go
// Description: 
package runner


import (
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestIsURL 测试URL检测函数
func TestIsURL(t *testing.T) {
	// 测试有效的URL
	validURLs := []string{
		"https://www.example.com",
		"http://example.com/path",
		"https://sub.domain.com:8080/path?query=value",
		"ftp://ftp.example.com",
	}

	for _, url := range validURLs {
		result := isURL(url)
		require.True(t, result, "URL '%s' 应该被识别为有效URL", url)
	}

	// 测试无效的URL
	invalidURLs := []string{
		"not a url",
		"www.example.com", // 缺少协议
		"://example.com",  // 缺少协议名称
		"http://",         // 缺少主机名
		"",                // 空字符串
	}

	for _, url := range invalidURLs {
		result := isURL(url)
		require.False(t, result, "URL '%s' 应该被识别为无效URL", url)
	}
}

// TestExtractDomain 测试从URL提取域名的函数
func TestExtractDomain(t *testing.T) {
	tests := []struct {
		url          string
		expectDomain string
	}{
		{"https://www.example.com", "www.example.com"},
		{"http://sub.domain.com/path", "sub.domain.com"},
		{"https://example.com:8080/path?query=value", "example.com"},
		{"http://127.0.0.1:8080", "127.0.0.1"},
		{"invalid url", ""},
		{"", ""},
	}

	for _, test := range tests {
		result := extractDomain(test.url)
		require.Equal(t, test.expectDomain, result, "URL '%s' 应该提取域名为 '%s'，而不是 '%s'",
			test.url, test.expectDomain, result)
	}
}

// TestPrepareResolver 测试解析器地址准备函数
func TestPrepareResolver(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"8.8.8.8", "8.8.8.8:53"},
		{"1.1.1.1:53", "1.1.1.1:53"},
		{"dns.example.com", "dns.example.com:53"},
		{"dns.example.com:5353", "dns.example.com:5353"},
		{"  8.8.4.4  ", "8.8.4.4:53"}, // 测试空格修剪
	}

	for _, test := range tests {
		result := prepareResolver(test.input)
		require.Equal(t, test.expected, result, "解析器 '%s' 应该准备为 '%s'，而不是 '%s'",
			test.input, test.expected, result)
	}
}

// TestFmtDuration 测试持续时间格式化函数
func TestFmtDuration(t *testing.T) {
	tests := []struct {
		input    time.Duration
		expected string
	}{
		{1*time.Hour + 30*time.Minute + 45*time.Second, "1:30:45"},
		{2 * time.Hour, "2:00:00"},
		{59 * time.Minute, "0:59:00"},
		{59 * time.Second, "0:00:59"},
		{90 * time.Second, "0:01:30"},
		{0, "0:00:00"},
	}

	for _, test := range tests {
		result := fmtDuration(test.input)
		require.Equal(t, test.expected, result, "持续时间 %v 应该被格式化为 '%s'，而不是 '%s'",
			test.input, test.expected, result)
	}
}

// TestLinesInFile 测试文件行读取函数
func TestLinesInFile(t *testing.T) {
	// 创建临时测试文件
	tmpFile, err := os.CreateTemp("", "test-lines-*.txt")
	require.NoError(t, err, "应该能够创建临时文件")
	defer os.Remove(tmpFile.Name())

	// 写入测试数据
	content := "line1\nline2\nline3\n"
	_, err = tmpFile.WriteString(content)
	require.NoError(t, err, "应该能够写入文件内容")
	tmpFile.Close()

	// 测试读取行
	lines, err := linesInFile(tmpFile.Name())
	require.NoError(t, err, "从有效文件读取行不应有错误")
	require.Equal(t, 3, len(lines), "应该读取到3行")
	require.Equal(t, "line1", lines[0], "第一行应为'line1'")
	require.Equal(t, "line2", lines[1], "第二行应为'line2'")
	require.Equal(t, "line3", lines[2], "第三行应为'line3'")

	// 测试读取不存在的文件
	_, err = linesInFile("non-existent-file.txt")
	require.Error(t, err, "读取不存在的文件应该返回错误")
}


