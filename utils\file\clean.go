//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-06 17:30:19
//FilePath: /yaml_scan/utils/file/clean.go
//Description:

package fileutil

import (
	"os"
	"path/filepath"
)

// CleanPath: 清理路径以防止任何可能的路径遍历攻击，并始终返回绝对路径。
//
//	@param inputPath string: 输入的路径，可以是相对路径或绝对路径。
//	@return string string: 返回清理后的绝对路径。
//	@return error error:
func CleanPath(inputPath string) (string, error) {
	// 检查路径是否为绝对路径
	if filepath.IsAbs(inputPath) {
		// 使用 filepath.Abs 清理绝对路径
		// Abs 方法会自动调用 Clean，因此不需要再次调用 Clean
		return filepath.Abs(inputPath)
	}
	// 获取当前工作目录
	cwd, err := os.Getwd()
	if err != nil {
		return "", err
	}
	// 将当前工作目录与输入路径连接
	joined := filepath.Join(cwd, inputPath)
	// 使用 filepath.Abs 清理连接后的路径
	return filepath.Abs(joined)
}

// FixMissingDirs: 创建路径中缺失的所有目录。
//  @param path string: 目标文件的完整路径，可能包含缺失的目录。
//  @return error error: 
func FixMissingDirs(path string) error {
	// 清理并获取绝对路径
	abs, err := CleanPath(path)
	if err != nil {
		return err
	}
	// 创建缺失的目录
	return os.MkdirAll(filepath.Dir(abs), os.ModePerm)
}
