//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-02 10:49:28
//FilePath: /yaml_scan/utils/rand/number.go
//Description:

package rand

import (
	"crypto/rand"
	"errors"
	"math/big"
	crand "math/rand"
)

// IntN: 返回一个均匀分布的随机值，范围在 [0, max)。
//
//	@param max int: 生成随机数的上限（不包括该值）。
//	@return int int: 返回生成的随机整数
//	@return error error:可能发生的错误
func IntN(max int) (int, error) {
	if max <= 0 {
		return 0, errors.New("max can't be <= 0")
	}
	// 使用加密安全的随机数生成器生成随机数
	nBig, err := rand.Int(rand.Reader, big.NewInt(int64(max)))
	if err != nil {
		// 如果加密随机数生成器失败，使用非加密的随机数生成器
		return crand.Intn(max), nil
	}
	// 返回生成的随机数
	return int(nBig.Int64()), nil
}
