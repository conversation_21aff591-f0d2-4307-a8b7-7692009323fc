package hostsfile

import (
	"errors"
	"strings"
	fileutil "yaml_scan/utils/file"
)

const (
	localhostName = "localhost"
	hostFilePath = "/etc/hosts"
)

var (
	// MaxLines 定义 Parse 函数将处理的 hosts 文件的最大行数
	MaxLines = 4096
)


// Parse: 解析给定路径的 hosts 文件，并返回 IP 地址与主机名的映射关系。
//  @param p string: 表示 hosts 文件的路径。
//  @return map[string][]string map[string][]string: 
//  @return error error: 
func Parse(p string) (map[string][]string, error) {
	// 检查文件是否存在
	if !fileutil.FileExists(p) {
		return nil, errors.New("hosts file doesn't exist")
	}
	// 读取文件内容
	hostsFileCh, err := fileutil.ReadFile(p)
	if err != nil {
		return nil, err
	}

	// 创建一个映射，用于存储主机名与 IP 地址的关系
	items := make(map[string][]string)
	lineCount := 0

	// 遍历文件中的每一行
	for line := range hostsFileCh {
		lineCount++
		if lineCount > MaxLines {
			break
		}

		line = strings.TrimSpace(line)
		// 跳过空行和注释行
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// 丢弃注释部分
		if idx := strings.Index(line, "#"); idx > 0 {
			line = line[:idx]
		}
		// 将行分割为字段
		tokens := strings.Fields(line)
		if len(tokens) > 1 {
			ip := tokens[0]
			for _, hostname := range tokens[1:] {
				items[hostname] = append(items[hostname], ip)
			}
		}
	}

	return items, nil
}


func ParseDefault() (map[string][]string, error) {
	return Parse(hostFilePath)
}