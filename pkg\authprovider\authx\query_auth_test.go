// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-16 14:54:08
// FilePath: /yaml_scan/pkg/authprovider/authx/query_auth_test.go
// Description: 
package authx

import (
	"net/http"
	"net/url"
	"testing"
	"yaml_scan/pkg/retryablehttp"

	"github.com/stretchr/testify/require"
)

// TestQueryAuthStrategy_Apply 测试查询参数认证策略的Apply方法
// 确保它能正确设置HTTP请求的URL查询参数
func TestQueryAuthStrategy_Apply(t *testing.T) {
	// 创建测试用的密钥数据，包含多个查询参数
	secret := &Secret{
		Params: []KV{
			{Key: "api_key", Value: "test-api-key-123"},
			{Key: "version", Value: "v1"},
		},
	}

	// 创建查询参数认证策略
	strategy := NewQueryAuthStrategy(secret)

	// 创建测试用的HTTP请求，带有一些现有的查询参数
	req, err := http.NewRequest("GET", "https://example.com?existing=value", nil)
	require.NoError(t, err, "创建HTTP请求不应该返回错误")

	// 应用认证策略
	strategy.Apply(req)

	// 解析URL以验证查询参数
	query, err := url.ParseQuery(req.URL.RawQuery)
	require.NoError(t, err, "解析查询参数不应该返回错误")

	// 验证查询参数是否正确设置
	require.Equal(t, "test-api-key-123", query.Get("api_key"), "api_key参数应该被正确设置")
	require.Equal(t, "v1", query.Get("version"), "version参数应该被正确设置")
	require.Equal(t, "value", query.Get("existing"), "现有参数应该被保留")
}

// TestQueryAuthStrategy_ApplyOnRR 测试查询参数认证策略的ApplyOnRR方法
// 确保它能正确设置可重试HTTP请求的URL查询参数
func TestQueryAuthStrategy_ApplyOnRR(t *testing.T) {
	// 创建测试用的密钥数据，包含多个查询参数
	secret := &Secret{
		Params: []KV{
			{Key: "api_key", Value: "test-api-key-123"},
			{Key: "version", Value: "v1"},
		},
	}

	// 创建查询参数认证策略
	strategy := NewQueryAuthStrategy(secret)

	// 创建测试用的可重试HTTP请求，带有一些现有的查询参数
	httpReq, err := http.NewRequest("GET", "https://example.com?existing=value", nil)
	require.NoError(t, err, "创建HTTP请求不应该返回错误")
	req, err := retryablehttp.FromRequest(httpReq)
	require.NoError(t, err, "创建可重试HTTP请求不应该返回错误")

	// 应用认证策略
	strategy.ApplyOnRR(req)

	// 解析URL以验证查询参数
	query, err := url.ParseQuery(req.Request.URL.RawQuery)
	require.NoError(t, err, "解析查询参数不应该返回错误")

	// 验证查询参数是否正确设置
	require.Equal(t, "test-api-key-123", query.Get("api_key"), "api_key参数应该被正确设置")
	require.Equal(t, "v1", query.Get("version"), "version参数应该被正确设置")
	require.Equal(t, "value", query.Get("existing"), "现有参数应该被保留")
}

// TestNewQueryAuthStrategy 测试创建查询参数认证策略的函数
// 确保它能正确创建QueryAuthStrategy对象
func TestNewQueryAuthStrategy(t *testing.T) {
	// 创建测试用的密钥数据
	secret := &Secret{
		Params: []KV{
			{Key: "api_key", Value: "test-api-key-123"},
		},
	}

	// 创建查询参数认证策略
	strategy := NewQueryAuthStrategy(secret)

	// 验证策略对象是否正确创建
	require.NotNil(t, strategy, "应该创建非空的策略对象")
	require.Equal(t, secret, strategy.Data, "策略对象应该包含正确的密钥数据")
}
