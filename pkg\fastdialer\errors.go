// Author: chenjb
// Version: V1.0
// Date: 2025-05-20 17:13:54
// FilePath: /yaml_scan/pkg/fastdialer/errors.go
// Description: 定义fastdialer包中使用的所有错误类型
package fastdialer

import (
	"yaml_scan/utils/errkit"
)

var (
	// CouldNotConnectError 表示无法连接到主机的任何发现地址的错误
	CouldNotConnectError  = errkit.New("could not connect to any address found for host").SetKind(errkit.ErrKindNetworkPermanent)
	// NoAddressFoundError 表示无法找到主机地址的错误
	NoAddressFoundError   = errkit.New("no address found for host").SetKind(errkit.ErrKindNetworkPermanent)
	// NoAddressAllowedError 表示找到的主机地址被拒绝访问的错误
	NoAddressAllowedError = errkit.New("denied address found for host").SetKind(errkit.ErrKindNetworkPermanent)
	// NoPortSpecifiedError 表示未指定端口的错误
	NoPortSpecifiedError  = errkit.New("port was not specified").SetKind(errkit.ErrKindNetworkPermanent)
	// MalformedIP6Error 表示IPv6地址格式错误的错误
	MalformedIP6Error     = errkit.New("malformed IPv6 address").SetKind(errkit.ErrKindNetworkPermanent)
	// ResolveHostError 表示无法解析主机的错误
	ResolveHostError      = errkit.New("could not resolve host").SetKind(errkit.ErrKindNetworkPermanent)
	// NoTLSHistoryError 表示没有可用的TLS数据历史记录的错误
	NoTLSHistoryError     = errkit.New("no tls data history available")
	// NoTLSDataError 表示未找到指定键的TLS数据的错误
	NoTLSDataError        = errkit.New("no tls data found for the key")
	// NoDNSDataError 表示未找到任何数据的错误
	NoDNSDataError        = errkit.New("no data found")
	// AsciiConversionError 表示无法将主机名转换为ASCII的错误
	AsciiConversionError  = errkit.New("could not convert hostname to ASCII")
	// ErrDialTimeout 表示拨号超时的错误
	ErrDialTimeout        = errkit.New("dial timeout").SetKind(errkit.ErrKindNetworkTemporary)
)
