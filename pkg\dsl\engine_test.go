// Author: chenjb
// Version: V1.0
// Date: 2025-05-30 11:30:50
// FilePath: /yaml_scan/pkg/dsl/engine_test.go
// Description:
package dsl

import (
	"regexp"
	"testing"
	mapsutil "yaml_scan/utils/maps"

	"github.com/stretchr/testify/require"
)

// TestEngine_EvalExpr 测试引擎的表达式计算功能
func TestEngine_EvalExpr(t *testing.T) {
	// 初始化引擎
	engine, err := NewEngine()
	require.NoError(t, err, "创建新引擎不应该出错")

	// 测试简单表达式
	t.Run("简单表达式测试", func(t *testing.T) {
		// 数学表达式
		result, err := engine.EvalExpr("1 + 2", nil)
		require.NoError(t, err, "计算'1 + 2'不应该出错")
		require.Equal(t, float64(3), result, "'1 + 2'应该等于3")

		// 带变量的表达式
		vars := map[string]interface{}{"x": 10, "y": 5}
		result, err = engine.EvalExpr("x * y", vars)
		require.NoError(t, err, "计算'x * y'不应该出错")
		require.Equal(t, float64(50), result, "'x * y'应该等于50，其中x=10, y=5")

		// 布尔表达式
		result, err = engine.EvalExpr("x > y", vars)
		require.NoError(t, err, "计算'x > y'不应该出错")
		require.Equal(t, true, result, "'x > y'应该为true，其中x=10, y=5")
	})

	// 测试缓存功能
	t.Run("表达式缓存测试", func(t *testing.T) {
		// 第一次执行表达式
		expr := "3 * 4 + 2"
		result1, err := engine.EvalExprFromCache(expr, nil)
		require.NoError(t, err, "第一次计算表达式不应该出错")
		require.Equal(t, float64(14), result1, "表达式计算结果不正确")

		// 再次执行相同表达式，应该使用缓存
		result2, err := engine.EvalExprFromCache(expr, nil)
		require.NoError(t, err, "从缓存计算表达式不应该出错")
		require.Equal(t, result1, result2, "缓存结果应该与之前计算结果相同")

		// 验证表达式已缓存
		_, exists := engine.ExpressionStore[expr]
		require.True(t, exists, "表达式应该已被缓存")
	})

}

// TestEvalExpr 测试全局EvalExpr函数
func TestEvalExpr(t *testing.T) {
	// 重置全局引擎
	defaultEngine = nil

	// 测试全局函数
	vars := map[string]interface{}{"a": 10, "b": 20}
	result, err := EvalExpr("a + b", vars)
	require.NoError(t, err, "全局EvalExpr计算'a + b'不应该出错")
	require.Equal(t, float64(30), result, "'a + b'应该等于30，其中a=10, b=20")

	// 确保全局引擎已创建
	require.NotNil(t, defaultEngine, "全局引擎应该已被初始化")
}

// TestRegex 测试正则表达式缓存功能
func TestRegex(t *testing.T) {
	// 清空缓存
	RegexStore = &mapsutil.SyncLockMap[string, *regexp.Regexp]{Map: make(mapsutil.Map[string, *regexp.Regexp])}

	// 测试有效正则表达式
	t.Run("有效正则表达式测试", func(t *testing.T) {
		// 简单正则表达式
		pattern := "^[a-z]+$"
		re, err := Regex(pattern)
		require.NoError(t, err, "编译有效正则表达式不应该出错")
		require.True(t, re.MatchString("abc"), "正则表达式应该匹配'abc'")
		require.False(t, re.MatchString("123"), "正则表达式不应该匹配'123'")

		// 验证表达式已缓存
		cached, exists := RegexStore.Get(pattern)
		require.True(t, exists, "正则表达式应该已被缓存")
		require.Equal(t, re, cached, "缓存的正则表达式应该与原始表达式相同")

		// 再次获取相同表达式，应该使用缓存
		re2, err := Regex(pattern)
		require.NoError(t, err, "从缓存获取正则表达式不应该出错")
		require.Equal(t, re, re2, "应该返回相同的正则表达式实例")
	})

	// 测试无效正则表达式
	t.Run("无效正则表达式测试", func(t *testing.T) {
		// 无效的正则表达式
		_, err := Regex("[abc")
		require.Error(t, err, "编译无效正则表达式应该返回错误")
	})
} 

