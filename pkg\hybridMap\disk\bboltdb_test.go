//
// Author: chenjb
// Version: V1.0
// Date: 2025-05-16 16:22:59
// FilePath: /yaml_scan/pkg/hybridMap/disk/bboltdb_test.go
// Description:

package disk

import (
	"os"
	"testing"

	"github.com/stretchr/testify/require"
	bolt "go.etcd.io/bbolt"
)

// 测试前的设置：创建模拟的 BBoltDB 实现
func setupBBoltTest(t *testing.T) (*BBoltDB, string, func()) {
	// 创建临时测试目录
	tempDir, err := os.MkdirTemp("", "bbolt-test")
	require.NoError(t, err, "创建临时测试目录失败")

	// 创建数据库文件路径
	dbPath := tempDir + "/test.db"

	// 打开 BBolt 数据库
	db, err := bolt.Open(dbPath, 0600, nil)
	require.NoError(t, err, "打开 BBolt 数据库应该成功")

	// 创建主事务并创建桶
	err = db.Update(func(tx *bolt.Tx) error {
		_, err := tx.CreateBucketIfNotExists([]byte("test-bucket"))
		return err
	})
	require.NoError(t, err, "创建测试桶应该成功")

	// 创建 BBoltDB 实例
	bbdb := &BBoltDB{
		db:         db,
		BucketName: "test-bucket",
	}

	// 返回清理函数
	cleanupFn := func() {
		bbdb.Close()
		os.RemoveAll(tempDir)
	}

	return bbdb, tempDir, cleanupFn
}


// TestBBoltDBBasicOperations 测试 BBoltDB 的基本操作，包括添加、获取、删除键值对
// 本测试用例测试 BBoltDB 实现的基本增删改查功能
func TestBBoltDBBasicOperations(t *testing.T) {
	// 设置测试环境
	bbdb, _, cleanup := setupBBoltTest(t)
	defer cleanup()

	// 1. 测试设置和获取键值对
	testKey := "test-key"
	testValue := []byte("test-value")

	// 1.1 设置键值对
	err := bbdb.Set(testKey, testValue, 0) // 永不过期
	require.NoError(t, err, "设置键值对应该成功")

	// 1.2 获取键值对
	value, err := bbdb.Get(testKey)
	require.NoError(t, err, "获取存在的键应该成功")
	require.Equal(t, testValue, value, "获取的值应该与设置的值相同")

	// 1.3 获取不存在的键
	_, err = bbdb.Get("non-existent-key")
	require.Error(t, err, "获取不存在的键应该返回错误")

	// 2. 测试删除键
	err = bbdb.Del(testKey)
	require.NoError(t, err, "删除键应该成功")

	// 2.1 验证删除后无法获取
	_, err = bbdb.Get(testKey)
	require.Error(t, err, "获取已删除的键应该返回错误")

	// 3. 测试批量获取
	keys := []string{"key1", "key2", "key3"}
	values := [][]byte{[]byte("value1"), []byte("value2"), []byte("value3")}

	for i, key := range keys {
		err = bbdb.Set(key, values[i], 0)
		require.NoError(t, err, "设置测试数据应该成功")
	}

	// 3.1 批量获取
	results := bbdb.MGet([]string{"key1", "key2", "key3", "nonexistent-key"})
	require.Len(t, results, 4, "应该返回 4 个结果")
	require.Equal(t, []byte("value1"), results[0], "第一个值应该是 value1")
	require.Equal(t, []byte("value2"), results[1], "第二个值应该是 value2")
	require.Equal(t, []byte("value3"), results[2], "第三个值应该是 value3")
	require.Empty(t, results[3], "第四个值应该是空字节数组")
}

// TestBBoltDBScan 测试 BBoltDB 的扫描功能
func TestBBoltDBScan(t *testing.T) {
	// 设置测试环境
	bbdb, _, cleanup := setupBBoltTest(t)
	defer cleanup()

	// 1. 准备测试数据
	testData := map[string][]byte{
		"user:1": []byte("Alice"),
		"user:2": []byte("Bob"),
		"user:3": []byte("Charlie"),
		"post:1": []byte("Hello World"),
		"post:2": []byte("Testing BBolt"),
		"post:3": []byte("Scan Feature"),
	}

	for k, v := range testData {
		err := bbdb.Set(k, v, 0)
		require.NoError(t, err, "设置测试数据应该成功")
	}

	// 2. 测试前缀扫描
	userKeys := make([]string, 0)
	userValues := make([]string, 0)

	err := bbdb.Scan(ScannerOptions{
		Prefix:      "user:",
		FetchValues: true,
		Handler: func(k []byte, v []byte) error {
			userKeys = append(userKeys, string(k))
			userValues = append(userValues, string(v))
			return nil
		},
	})

	require.NoError(t, err, "前缀扫描应该成功")
	require.Len(t, userKeys, 3, "应该有 3 个用户键")
	require.Contains(t, userKeys, "user:1", "应包含 user:1")
	require.Contains(t, userKeys, "user:2", "应包含 user:2")
	require.Contains(t, userKeys, "user:3", "应包含 user:3")
	require.Contains(t, userValues, "Alice", "应包含 Alice")
	require.Contains(t, userValues, "Bob", "应包含 Bob")
	require.Contains(t, userValues, "Charlie", "应包含 Charlie")
}
