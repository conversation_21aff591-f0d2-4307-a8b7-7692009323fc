// 
// Author: chenjb
// Version: V1.0
// Date: 2025-05-29 20:31:20
// FilePath: /yaml_scan/pkg/dsl/func_test.go
// Description: 
package dsl

import (
	"testing"

	"github.com/stretchr/testify/require"
)

// TestDslFunction_GetSignatures 测试dslFunction的GetSignatures方法
func TestDslFunction_GetSignatures(t *testing.T) {
	// 测试基于固定参数数量的函数
	t.Run("固定参数数量函数测试", func(t *testing.T) {
		// 创建一个带有固定参数数量的函数
		fn := dslFunction{
			Name:         "test_func",
			NumberOfArgs: 2,
			ExpressionFunction: func(args ...interface{}) (interface{}, error) {
				return nil, nil
			},
		}

		// 获取签名
		signatures := fn.GetSignatures()
		
		// 验证结果
		require.Len(t, signatures, 1, "应该只有一个签名")
		require.Equal(t, "test_func(arg1, arg2 interface{}) interface{}", signatures[0], "签名格式不正确")
	})

	// 测试基于预定义签名的函数
	t.Run("预定义签名函数测试", func(t *testing.T) {
		// 创建一个带有预定义签名的函数
		fn := dslFunction{
			Name: "multi_sig_func",
			Signatures: []string{
				"(str string) string",
				"(str string, num int) int",
			},
			ExpressionFunction: func(args ...interface{}) (interface{}, error) {
				return nil, nil
			},
		}

		// 获取签名
		signatures := fn.GetSignatures()
		
		// 验证结果
		require.Len(t, signatures, 2, "应该有两个签名")
		require.Equal(t, "multi_sig_func(str string) string", signatures[0], "第一个签名格式不正确")
		require.Equal(t, "multi_sig_func(str string, num int) int", signatures[1], "第二个签名格式不正确")
	})
}

// TestDslFunction_Exec 测试dslFunction的Exec方法
func TestDslFunction_Exec(t *testing.T) {
	// 测试参数验证
	t.Run("参数验证测试", func(t *testing.T) {
		// 创建一个需要2个参数的函数
		fn := dslFunction{
			Name:         "test_func",
			NumberOfArgs: 2,
			ExpressionFunction: func(args ...interface{}) (interface{}, error) {
				return args[0].(string) + args[1].(string), nil
			},
		}

		// 使用正确数量的参数
		result, err := fn.Exec("hello", "world")
		require.NoError(t, err, "使用正确数量的参数不应该出错")
		require.Equal(t, "helloworld", result, "函数应该正确连接两个字符串")
	})

	// 测试结果缓存
	t.Run("结果缓存测试", func(t *testing.T) {
		// 跟踪函数调用次数
		callCount := 0
		
		// 创建一个可缓存的函数
		fn := dslFunction{
			Name:        "cacheable_func",
			IsCacheable: true,
			ExpressionFunction: func(args ...interface{}) (interface{}, error) {
				callCount++
				return args[0].(int) * 2, nil
			},
		}

		// 第一次调用
		result1, err := fn.Exec(5)
		require.NoError(t, err, "第一次调用不应该出错")
		require.Equal(t, 10, result1, "结果应该正确计算")
		require.Equal(t, 1, callCount, "函数应该被调用一次")

		// 第二次使用相同参数调用，应该使用缓存
		result2, err := fn.Exec(5)
		require.NoError(t, err, "第二次调用不应该出错")
		require.Equal(t, 10, result2, "应该返回相同的结果")
		require.Equal(t, 1, callCount, "函数不应该被再次调用")

		// 使用不同参数调用，不应使用缓存
		result3, err := fn.Exec(10)
		require.NoError(t, err, "使用不同参数调用不应该出错")
		require.Equal(t, 20, result3, "应该正确计算新的结果")
		require.Equal(t, 2, callCount, "函数应该被再次调用")
	})

	// 测试不可缓存的函数
	t.Run("不可缓存函数测试", func(t *testing.T) {
		// 跟踪函数调用次数
		callCount := 0
		
		// 创建一个不可缓存的函数
		fn := dslFunction{
			Name:        "non_cacheable_func",
			IsCacheable: false,
			ExpressionFunction: func(args ...interface{}) (interface{}, error) {
				callCount++
				return args[0].(int) * 2, nil
			},
		}

		// 连续调用两次
		_, err1 := fn.Exec(5)
		require.NoError(t, err1, "第一次调用不应该出错")
		
		_, err2 := fn.Exec(5)
		require.NoError(t, err2, "第二次调用不应该出错")
		
		// 验证函数被调用了两次
		require.Equal(t, 2, callCount, "不可缓存的函数应该在每次调用时执行")
	})
}

// TestHelperFunctions 测试HelperFunctions函数
func TestHelperFunctions(t *testing.T) {
	// 清空函数列表
	functions = nil
	
	// 添加一些测试函数
	testFunc1 := NewWithPositionalArgs("test_function", 1, true, func(args ...interface{}) (interface{}, error) {
		return "test1", nil
	})
	testFunc2 := NewWithPositionalArgs("test_with_underscore", 1, true, func(args ...interface{}) (interface{}, error) {
		return "test2", nil
	})
	
	MustAddFunction(testFunc1)
	MustAddFunction(testFunc2)
	
	// 获取辅助函数映射
	helpers := HelperFunctions()
	
	// 验证原始函数名存在
	require.Contains(t, helpers, "test_function", "原始函数名应该存在于辅助函数映射中")
	require.Contains(t, helpers, "test_with_underscore", "原始函数名应该存在于辅助函数映射中")
	
	// 验证无下划线版本也存在（向后兼容）
	require.Contains(t, helpers, "testfunction", "无下划线函数名应该存在于辅助函数映射中")
	require.Contains(t, helpers, "testwithunderscore", "无下划线函数名应该存在于辅助函数映射中")
	
	// 验证函数执行
	result, err := helpers["test_function"]("arg")
	require.NoError(t, err, "辅助函数执行不应该出错")
	require.Equal(t, "test1", result, "辅助函数应该返回正确的结果")
} 
