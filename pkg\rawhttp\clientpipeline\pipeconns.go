//
// Author: chenjb
// Version: V1.0
// Date: 2025-07-22 19:19:58
// FilePath: /yaml_scan/pkg/rawhttp/clientpipeline/pipeconns.go
// Description: 管道连接实现，提供内存中的双向连接对，用于测试和本地通信

// Package clientpipeline 管道连接模块
package clientpipeline

// Original Source: https://github.com/valyala/fasthttp
// 原始来源：基于fasthttp项目的管道连接实现

import (
	"errors" // 错误处理包
	"io"     // 输入输出接口包
	"net"    // 网络包
	"sync"   // 同步原语包
	"time"   // 时间处理包
)

// NewPipeConns 创建新的管道连接对
// 返回:
//   *PipeConns: 新创建的管道连接对象
// 功能: 创建一对相互连接的内存管道，数据从一端写入可从另一端读取
func NewPipeConns() *PipeConns {
	ch1 := make(chan *byteBuffer, 4) // 创建缓冲通道1，容量为4
	ch2 := make(chan *byteBuffer, 4) // 创建缓冲通道2，容量为4

	pc := &PipeConns{
		stopCh: make(chan struct{}), // 创建停止信号通道
	}
	// 设置连接1：读取通道1，写入通道2
	pc.c1.rCh = ch1
	pc.c1.wCh = ch2
	// 设置连接2：读取通道2，写入通道1
	pc.c2.rCh = ch2
	pc.c2.wCh = ch1
	// 设置连接的父对象引用
	pc.c1.pc = pc
	pc.c2.pc = pc
	return pc
}

// PipeConns 管道连接对结构体
// 包含两个相互连接的管道连接和控制通道
type PipeConns struct {
	c1         pipeConn    // 管道连接1
	c2         pipeConn    // 管道连接2
	stopCh     chan struct{} // 停止信号通道
	stopChLock sync.Mutex    // 停止通道的互斥锁
}

// Conn1 获取第一个管道连接
// 返回:
//   net.Conn: 第一个管道连接的网络连接接口
// 功能: 返回管道连接对中的第一个连接
func (pc *PipeConns) Conn1() net.Conn {
	return &pc.c1
}

// Conn2 获取第二个管道连接
// 返回:
//   net.Conn: 第二个管道连接的网络连接接口
// 功能: 返回管道连接对中的第二个连接
func (pc *PipeConns) Conn2() net.Conn {
	return &pc.c2
}

// Close 关闭管道连接
// 返回:
//   error: 关闭错误，总是返回nil
// 功能: 关闭管道连接对，发送停止信号给所有相关的goroutine
func (pc *PipeConns) Close() error {
	pc.stopChLock.Lock() // 加锁保护停止通道
	select {
	case <-pc.stopCh: // 检查是否已经关闭
	default:
		close(pc.stopCh) // 关闭停止信号通道
	}
	pc.stopChLock.Unlock() // 解锁

	return nil // 总是返回nil
}

// pipeConn 管道连接结构体
// 实现了net.Conn接口，提供内存中的连接功能
type pipeConn struct {
	b  *byteBuffer // 当前读取的字节缓冲区
	bb []byte      // 字节缓冲区的字节切片

	rCh chan *byteBuffer // 读取通道，从此通道接收数据
	wCh chan *byteBuffer // 写入通道，向此通道发送数据
	pc  *PipeConns       // 父管道连接对的引用

	readDeadlineTimer  *time.Timer // 读取截止时间定时器
	writeDeadlineTimer *time.Timer // 写入截止时间定时器

	readDeadlineCh  <-chan time.Time // 读取截止时间通道
	writeDeadlineCh <-chan time.Time // 写入截止时间通道

	readDeadlineChLock sync.Mutex // 读取截止时间通道的互斥锁
}

// Write 实现net.Conn接口的Write方法，向管道连接写入数据
// 参数:
//   p: 要写入的字节数据
// 返回:
//   int: 实际写入的字节数
//   error: 写入错误，成功时为nil
// 功能: 将数据写入管道连接，支持超时和连接关闭检测
func (c *pipeConn) Write(p []byte) (int, error) {
	// 从对象池获取字节缓冲区
	b := acquireByteBuffer()
	b.b = append(b.b[:0], p...) // 复制数据到缓冲区

	// 检查连接是否已关闭
	select {
	case <-c.pc.stopCh:
		releaseByteBuffer(b)        // 释放缓冲区
		return 0, errConnectionClosed // 返回连接关闭错误
	default:
	}

	// 尝试发送数据到写入通道
	select {
	case c.wCh <- b: // 快速路径：直接发送
	default:
		// 慢速路径：等待通道可用或超时
		select {
		case c.wCh <- b: // 等待写入通道可用
		case <-c.writeDeadlineCh: // 写入超时
			c.writeDeadlineCh = closedDeadlineCh // 重置超时通道
			return 0, ErrTimeout                 // 返回超时错误
		case <-c.pc.stopCh: // 连接关闭
			releaseByteBuffer(b)        // 释放缓冲区
			return 0, errConnectionClosed // 返回连接关闭错误
		}
	}

	return len(p), nil // 返回写入的字节数
}

// Read 实现net.Conn接口的Read方法，从管道连接读取数据
// 参数:
//   p: 用于存储读取数据的字节切片
// 返回:
//   int: 实际读取的字节数
//   error: 读取错误，成功时为nil
// 功能: 从管道连接读取数据，尽可能填满提供的缓冲区
func (c *pipeConn) Read(p []byte) (int, error) {
	mayBlock := true // 第一次读取允许阻塞
	nn := 0          // 总读取字节数
	// 循环读取直到缓冲区填满或出错
	for len(p) > 0 {
		n, err := c.read(p, mayBlock) // 读取数据
		nn += n                       // 累加读取字节数
		if err != nil {
			// 如果是非阻塞模式的"会阻塞"错误，转换为成功
			if !mayBlock && err == errWouldBlock {
				err = nil
			}
			return nn, err // 返回已读取的字节数和错误
		}
		p = p[n:]        // 移动缓冲区指针
		mayBlock = false // 后续读取不阻塞，避免死锁
	}

	return nn, nil // 返回总读取字节数
}

// read 内部读取方法，支持阻塞和非阻塞模式
// 参数:
//   p: 用于存储读取数据的字节切片
//   mayBlock: 是否允许阻塞等待数据
// 返回:
//   int: 实际读取的字节数
//   error: 读取错误，成功时为nil
// 功能: 从内部缓冲区读取数据，必要时获取新的数据缓冲区
func (c *pipeConn) read(p []byte, mayBlock bool) (int, error) {
	// 如果内部缓冲区为空，尝试读取下一个字节缓冲区
	if len(c.bb) == 0 {
		if err := c.readNextByteBuffer(mayBlock); err != nil {
			return 0, err // 读取缓冲区失败
		}
	}
	// 从内部缓冲区复制数据到目标切片
	n := copy(p, c.bb)
	c.bb = c.bb[n:] // 更新内部缓冲区，移除已读取的数据

	return n, nil // 返回读取的字节数
}

// readNextByteBuffer 读取下一个字节缓冲区
// 参数:
//   mayBlock: 是否允许阻塞等待数据
// 返回:
//   error: 读取错误，成功时为nil
// 功能: 从读取通道获取新的字节缓冲区，支持超时和连接关闭处理
func (c *pipeConn) readNextByteBuffer(mayBlock bool) error {
	// 释放当前字节缓冲区
	releaseByteBuffer(c.b)
	c.b = nil

	// 尝试从读取通道获取数据
	select {
	case c.b = <-c.rCh: // 快速路径：直接获取数据
	default:
		// 如果不允许阻塞，直接返回"会阻塞"错误
		if !mayBlock {
			return errWouldBlock
		}
		// 获取读取截止时间通道（需要加锁保护）
		c.readDeadlineChLock.Lock()
		readDeadlineCh := c.readDeadlineCh
		c.readDeadlineChLock.Unlock()
		// 等待数据、超时或连接关闭
		select {
		case c.b = <-c.rCh: // 获取到数据
		case <-readDeadlineCh: // 读取超时
			c.readDeadlineChLock.Lock()
			c.readDeadlineCh = closedDeadlineCh // 重置超时通道
			c.readDeadlineChLock.Unlock()
			// rCh may contain data when deadline is reached.
			// Read the data before returning ErrTimeout.
			// 超时时读取通道可能仍有数据，先尝试读取再返回超时错误
			select {
			case c.b = <-c.rCh:
			default:
				return ErrTimeout // 返回超时错误
			}
		case <-c.pc.stopCh: // 连接关闭
			// rCh may contain data when stopCh is closed.
			// Read the data before returning EOF.
			// 连接关闭时读取通道可能仍有数据，先尝试读取再返回EOF
			select {
			case c.b = <-c.rCh:
			default:
				return io.EOF // 返回EOF表示连接关闭
			}
		}
	}

	c.bb = c.b.b // 设置内部字节切片为缓冲区的字节切片
	return nil   // 成功获取缓冲区
}

// 预定义的错误变量
var (
	errWouldBlock       = errors.New("would block")       // 非阻塞操作会阻塞时的错误
	errConnectionClosed = errors.New("connection closed") // 连接已关闭错误
)

// Close 实现net.Conn接口的Close方法，关闭管道连接
// 返回:
//   error: 关闭错误，总是返回nil
// 功能: 关闭整个管道连接对，影响两端的连接
func (c *pipeConn) Close() error {
	return c.pc.Close() // 委托给父对象关闭
}

// LocalAddr 实现net.Conn接口的LocalAddr方法，返回本地地址
// 返回:
//   net.Addr: 本地地址对象
// 功能: 返回管道连接的本地地址（虚拟地址）
func (c *pipeConn) LocalAddr() net.Addr {
	return pipeAddr(0) // 返回虚拟的管道地址
}

// RemoteAddr 实现net.Conn接口的RemoteAddr方法，返回远程地址
// 返回:
//   net.Addr: 远程地址对象
// 功能: 返回管道连接的远程地址（虚拟地址）
func (c *pipeConn) RemoteAddr() net.Addr {
	return pipeAddr(0) // 返回虚拟的管道地址
}

// SetDeadline 实现net.Conn接口的SetDeadline方法，设置读写截止时间
// 参数:
//   deadline: 截止时间，零值表示无限期
// 返回:
//   error: 设置错误，总是返回nil
// 功能: 同时设置读取和写入的截止时间
func (c *pipeConn) SetDeadline(deadline time.Time) error {
	c.SetReadDeadline(deadline)  //nolint:errcheck // 设置读取截止时间
	c.SetWriteDeadline(deadline) //nolint:errcheck // 设置写入截止时间
	return nil
}

// SetReadDeadline 实现net.Conn接口的SetReadDeadline方法，设置读取截止时间
// 参数:
//   deadline: 读取截止时间，零值表示无限期
// 返回:
//   error: 设置错误，总是返回nil
// 功能: 设置读取操作的超时时间，超时后读取操作将返回错误
func (c *pipeConn) SetReadDeadline(deadline time.Time) error {
	// 如果读取截止时间定时器不存在，创建一个
	if c.readDeadlineTimer == nil {
		c.readDeadlineTimer = time.NewTimer(time.Hour) // 初始设置为1小时
	}
	// 更新定时器并获取超时通道
	readDeadlineCh := updateTimer(c.readDeadlineTimer, deadline)
	// 线程安全地更新读取截止时间通道
	c.readDeadlineChLock.Lock()
	c.readDeadlineCh = readDeadlineCh
	c.readDeadlineChLock.Unlock()
	return nil
}

// SetWriteDeadline 实现net.Conn接口的SetWriteDeadline方法，设置写入截止时间
// 参数:
//   deadline: 写入截止时间，零值表示无限期
// 返回:
//   error: 设置错误，总是返回nil
// 功能: 设置写入操作的超时时间，超时后写入操作将返回错误
func (c *pipeConn) SetWriteDeadline(deadline time.Time) error {
	// 如果写入截止时间定时器不存在，创建一个
	if c.writeDeadlineTimer == nil {
		c.writeDeadlineTimer = time.NewTimer(time.Hour) // 初始设置为1小时
	}
	// 更新定时器并设置写入截止时间通道
	c.writeDeadlineCh = updateTimer(c.writeDeadlineTimer, deadline)
	return nil
}

// updateTimer 更新定时器并返回超时通道
// 参数:
//   t: 要更新的定时器
//   deadline: 新的截止时间
// 返回:
//   <-chan time.Time: 超时通道，nil表示无超时
// 功能: 安全地更新定时器，处理各种边界情况
func updateTimer(t *time.Timer, deadline time.Time) <-chan time.Time {
	// 安全地停止定时器
	if !t.Stop() {
		// 如果定时器已经触发，清空通道
		select {
		case <-t.C:
		default:
		}
	}
	// 如果截止时间为零值，表示无超时
	if deadline.IsZero() {
		return nil
	}
	// 计算剩余时间
	d := -time.Since(deadline)
	if d <= 0 {
		// 如果已经超时，返回已关闭的通道
		return closedDeadlineCh
	}
	// 重置定时器为剩余时间
	t.Reset(d)
	return t.C // 返回定时器通道
}

// closedDeadlineCh 已关闭的截止时间通道
// 用于表示已经超时的情况，立即可读
var closedDeadlineCh = func() <-chan time.Time {
	ch := make(chan time.Time)
	close(ch) // 立即关闭通道，使其可读
	return ch
}()

// pipeAddr 管道地址类型
// 实现net.Addr接口，用于管道连接的虚拟地址
type pipeAddr int

// Network 实现net.Addr接口的Network方法
// 返回:
//   string: 网络类型，总是返回"pipe"
// 功能: 返回管道连接的网络类型标识
func (pipeAddr) Network() string {
	return "pipe"
}

// String 实现net.Addr接口的String方法
// 返回:
//   string: 地址字符串，总是返回"pipe"
// 功能: 返回管道连接的地址字符串表示
func (pipeAddr) String() string {
	return "pipe"
}

// byteBuffer 字节缓冲区结构体
// 用于在管道连接间传输数据的缓冲区
type byteBuffer struct {
	b []byte // 字节数据切片
}

// acquireByteBuffer 从对象池获取字节缓冲区
// 返回:
//   *byteBuffer: 字节缓冲区对象
// 功能: 从对象池获取可复用的字节缓冲区，减少内存分配
func acquireByteBuffer() *byteBuffer {
	return byteBufferPool.Get().(*byteBuffer)
}

// releaseByteBuffer 释放字节缓冲区回对象池
// 参数:
//   b: 要释放的字节缓冲区
// 功能: 将字节缓冲区放回对象池以供复用
func releaseByteBuffer(b *byteBuffer) {
	if b != nil {
		byteBufferPool.Put(b) // 放回对象池
	}
}

// byteBufferPool 字节缓冲区对象池
// 用于复用字节缓冲区对象，减少内存分配和垃圾回收压力
var byteBufferPool = &sync.Pool{
	New: func() interface{} {
		return &byteBuffer{
			b: make([]byte, 1024), // 默认分配1KB缓冲区
		}
	},
}