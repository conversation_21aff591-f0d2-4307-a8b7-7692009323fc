// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-17 16:49:19
// FilePath: /yaml_scan/pkg/catalog/disk/catalog.go
// Description: 
package disk


// NewCatalog creates a new Catalog structure using provided input items
// using disk based items
func NewCatalog(directory string) *DiskCatalog {
	catalog := &DiskCatalog{templatesDirectory: directory}
	if directory == "" {
		catalog.templatesDirectory = config.DefaultConfig.GetTemplateDir()
	}
	return catalog
}

