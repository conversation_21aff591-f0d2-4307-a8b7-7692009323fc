module yaml_scan

go 1.24

toolchain go1.24.4

require (
	github.com/Knetic/govaluate v3.0.0+incompatible
	github.com/PuerkitoBio/goquery v1.10.3
	github.com/akrylysov/pogreb v0.10.2
	github.com/alecthomas/chroma v0.10.0
	github.com/as<PERSON>evich/govalidator v0.0.0-20230301143203-a9d515a09cc2
	github.com/bits-and-blooms/bloom/v3 v3.7.0
	github.com/clbanning/mxj/v2 v2.7.0
	github.com/cnf/structhash v0.0.0-20250313080605-df4c6cc74a9a
	github.com/dimchansky/utfbom v1.1.1
	github.com/docker/go-units v0.5.0
	github.com/gaissmai/bart v0.20.4
	github.com/getkin/kin-openapi v0.132.0
	github.com/hashicorp/go-version v1.7.0
	github.com/hashicorp/golang-lru/v2 v2.0.7
	github.com/hdm/jarm-go v0.0.7
	github.com/invopop/yaml v0.3.1
	github.com/ipinfo/go/v2 v2.10.0
	github.com/json-iterator/go v1.1.12
	github.com/kataras/jwt v0.1.15
	github.com/logrusorgru/aurora v2.0.3+incompatible
	github.com/mholt/archiver/v3 v3.5.1
	github.com/miekg/dns v1.1.66
	github.com/pkg/errors v0.9.1
	github.com/refraction-networking/utls v1.7.3
	github.com/rs/xid v1.6.0
	github.com/sashabaranov/go-openai v1.40.2
	github.com/spaolacci/murmur3 v1.1.0
	github.com/stretchr/testify v1.10.0
	github.com/syndtr/goleveldb v1.0.0
	github.com/tidwall/buntdb v1.3.2
	github.com/valyala/bytebufferpool v1.0.0
	github.com/valyala/fasttemplate v1.2.2
	github.com/weppos/publicsuffix-go v0.40.3-0.20250617082559-9b2e24a9e482
	github.com/zmap/zcrypto v0.0.0-20250618174828-7ca6a82cf2d4
	go.etcd.io/bbolt v1.4.1
	go.uber.org/multierr v1.11.0
	golang.org/x/exp v0.0.0-20250606033433-dcc06ee1d476
	golang.org/x/net v0.41.0
	golang.org/x/sync v0.15.0
	golang.org/x/text v0.26.0
	golang.org/x/time v0.12.0
	gopkg.in/djherbis/times.v1 v1.3.0
	gopkg.in/yaml.v2 v2.4.0
	gopkg.in/yaml.v3 v3.0.1
)

require (
	github.com/andybalholm/brotli v1.0.6 // indirect
	github.com/andybalholm/cascadia v1.3.3 // indirect
	github.com/bits-and-blooms/bitset v1.10.0 // indirect
	github.com/cloudflare/cfssl v1.6.5 // indirect
	github.com/cloudflare/circl v1.5.0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/dlclark/regexp2 v1.4.0 // indirect
	github.com/dsnet/compress v0.0.2-0.20210315054119-f66993602bf5 // indirect
	github.com/go-openapi/jsonpointer v0.21.0 // indirect
	github.com/go-openapi/swag v0.23.0 // indirect
	github.com/golang/snappy v0.0.2 // indirect
	github.com/google/certificate-transparency-go v1.1.7 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/klauspost/compress v1.17.4 // indirect
	github.com/klauspost/pgzip v1.2.5 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/nwaples/rardecode v1.1.0 // indirect
	github.com/oasdiff/yaml v0.0.0-20250309154309-f31be36b4037 // indirect
	github.com/oasdiff/yaml3 v0.0.0-20250309153720-d2182401db90 // indirect
	github.com/patrickmn/go-cache v2.1.0+incompatible // indirect
	github.com/perimeterx/marshmallow v1.1.5 // indirect
	github.com/pierrec/lz4/v4 v4.1.2 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/tidwall/btree v1.4.2 // indirect
	github.com/tidwall/gjson v1.14.3 // indirect
	github.com/tidwall/grect v0.1.4 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.0 // indirect
	github.com/tidwall/rtred v0.1.2 // indirect
	github.com/tidwall/tinyqueue v0.1.1 // indirect
	github.com/ulikunitz/xz v0.5.9 // indirect
	github.com/xi2/xz v0.0.0-20171230120015-48954b6210f8 // indirect
	golang.org/x/crypto v0.39.0 // indirect
	golang.org/x/mod v0.25.0 // indirect
	golang.org/x/sys v0.33.0 // indirect
	golang.org/x/tools v0.34.0 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
)
