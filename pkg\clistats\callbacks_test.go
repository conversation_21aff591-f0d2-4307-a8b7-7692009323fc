// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-19 11:45:08
// FilePath: /yaml_scan/pkg/clistats/callbacks_test.go
// Description: 
package clistats

import (
	"sync/atomic"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestNewRequestsPerSecondCallback 测试每秒请求数回调函数的创建和功能
func TestNewRequestsPerSecondCallback(t *testing.T) {
	// 创建统计客户端
	stats, err := New()
	require.NoError(t, err, "创建统计客户端不应返回错误")

	// 设置开始时间（10秒前）
	startTime := time.Now().Add(-10 * time.Second)
	stats.AddStatic("startTime", startTime)

	// 设置请求计数
	stats.AddCounter("requests", 100)

	// 创建回调选项
	options := RequestPerSecondCallbackOptions{
		StartTimeFieldID:  "startTime",
		RequestsCounterID: "requests",
	}

	// 创建每秒请求数回调
	callback := NewRequestsPerSecondCallback(options)
	require.NotNil(t, callback, "回调函数不应为nil")

	// 调用回调并验证结果（应该是每秒10个请求）
	result := callback(stats)
	require.NotNil(t, result, "回调结果不应为nil")

	// 验证回调结果是否接近10（考虑到时间精度可能有微小差异）
	rps, ok := result.(uint64)
	require.True(t, ok, "结果应为uint64类型")
	require.InDelta(t, 10.0, float64(rps), 9.0, "每秒请求数应约为10")
}

// TestRequestsPerSecondCallbackEdgeCases 测试每秒请求数回调函数的边缘情况
func TestRequestsPerSecondCallbackEdgeCases(t *testing.T) {
	// 创建统计客户端
	stats, err := New()
	require.NoError(t, err, "创建统计客户端不应返回错误")

	// 创建回调选项
	options := RequestPerSecondCallbackOptions{
		StartTimeFieldID:  "startTime",
		RequestsCounterID: "requests",
	}

	// 创建每秒请求数回调
	callback := NewRequestsPerSecondCallback(options)

	// 测试缺少开始时间字段的情况
	stats.AddCounter("requests", 100)
	result := callback(stats)
	require.Nil(t, result, "缺少开始时间字段时应返回nil")

	// 测试开始时间字段类型错误的情况
	stats.AddStatic("startTime", "not_a_time")
	result = callback(stats)
	require.Nil(t, result, "开始时间字段类型错误时应返回nil")

	// 测试缺少请求计数器的情况
	stats.AddStatic("startTime", time.Now())
	stats.counters = make(map[string]*atomic.Uint64) // 清除所有计数器
	result = callback(stats)
	require.Nil(t, result, "缺少请求计数器时应返回nil")
}

// TestRequestsPerSecondCallbackRealTime 测试每秒请求数回调函数在实时增加请求数的情况下的表现
func TestRequestsPerSecondCallbackRealTime(t *testing.T) {
	// 创建统计客户端
	stats, err := New()
	require.NoError(t, err, "创建统计客户端不应返回错误")

	// 设置开始时间（现在）
	startTime := time.Now()
	stats.AddStatic("startTime", startTime)

	// 初始化请求计数为0
	stats.AddCounter("requests", 0)

	// 创建回调选项
	options := RequestPerSecondCallbackOptions{
		StartTimeFieldID:  "startTime",
		RequestsCounterID: "requests",
	}

	// 创建每秒请求数回调
	callback := NewRequestsPerSecondCallback(options)

	// 等待一小段时间并增加一些请求
	time.Sleep(100 * time.Millisecond) // 等待0.1秒
	stats.IncrementCounter("requests", 5)

	// 调用回调并验证结果（每秒大约50个请求 = 5请求/0.1秒）
	result := callback(stats)
	rps, ok := result.(uint64)
	require.True(t, ok, "结果应为uint64类型")

	// 由于时间精度问题，我们使用较宽松的范围验证
	// 0.1秒内5个请求应该产生约50 RPS，但可能有波动
	require.Greater(t, float64(rps), 30.0, "每秒请求数应大于30")
	require.Less(t, float64(rps), 70.0, "每秒请求数应小于70")
}


