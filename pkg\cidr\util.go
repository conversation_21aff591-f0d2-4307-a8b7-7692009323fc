// Author: chenjb
// Version: V1.0
// Date: 2025-05-30 17:42:31
// FilePath: /yaml_scan/pkg/cidr/util.go
// Description: 提供CIDR包所需的辅助工具函数
package cidr

import (
	"bytes"
	"crypto/rand"
	"encoding/hex"
	"net"
)

// inc 将一个IP地址递增为子网中的下一个IP地址
// @param ip net.IP:  要递增的IP地址
// @return net.IP net.IP: 递增后的下一个IP地址
func inc(ip net.IP) net.IP {
	incIP := make([]byte, len(ip))
	copy(incIP, ip)
	for j := len(incIP) - 1; j >= 0; j-- {
		incIP[j]++
		if incIP[j] > 0 {
			break
		}
	}
	return incIP
}

const upperhex = "0123456789ABCDEF"

// escape 将字符串URL编码为十六进制表示
// @param s string: 要编码的字符串
// @return string string:  URL编码后的字符串
func escape(s string) string {
	var b bytes.Buffer
	for i := 0; i < len(s); i++ {
		b.WriteString("%")
		b.WriteByte(upperhex[s[i]>>4])
		b.WriteByte(upperhex[s[i]&15])
	}
	return b.String()
}

// RandomHex 生成随机十六进制字符串
// @param n int: 要生成的随机字节数
// @param suffix []byte: 要附加到随机字节后的后缀
// @return string string: 生成的十六进制字符串
// @return error error: 可能的错误
func RandomHex(n int, suffix []byte) (string, error) {
	bytes := make([]byte, n)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(append(bytes, suffix...)), nil
}

// TotalIPSInCidrs 计算给定CIDR列表中的IP地址总数
// @param cidrs []*net.IPNet: CIDR列表
// @return totalIPs uint64: P地址总数
func TotalIPSInCidrs(cidrs []*net.IPNet) (totalIPs uint64) {
	for _, cidr := range cidrs {
		totalIPs += AddressCountIpnet(cidr)
	}

	return
}
