//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-15 15:32:57
//FilePath: /yaml_scan/pkg/input/formats/json/json.go
//Description: 用于解析 JSON 格式输入

package json

import (
	"encoding/json"
	"io"
	"os"
	"yaml_scan/pkg/gologger"
	"yaml_scan/pkg/input/formats"
	"yaml_scan/pkg/input/types"

	"github.com/pkg/errors"
)

// JSONFormat 是一个用于解析输入 HTTP 请求的 JSON 格式解析器
type JSONFormat struct {
	opts formats.InputFormatOptions
}

// New 创建一个新的 JSON 格式解析器
func New() *JSONFormat {
	return &JSONFormat{}
}

// 确保 JSONFormat 实现了 formats.Format 接口
var _ formats.Format = &JSONFormat{}

// proxifyRequest 是一个用于 proxify 的请求结构体
type proxifyRequest struct {
	URL     string `json:"url"` // 请求的 URL
	Request struct {
		Header   map[string]string `json:"header"`   // 请求头
		Body     string            `json:"body"`     // 请求体
		Raw      string            `json:"raw"`      // 原始请求字符串
		Endpoint string            `json:"endpoint"` // 请求的端点
	} `json:"request"`
}

// Name 返回格式的名称
// 返回字符串 "jsonl"，表示该解析器的格式类型。
func (j *JSONFormat) Name() string {
	return "jsonl"
}

// SetOptions: 设置输入格式的选项
//
//	@receiver j *JSONFormat:
//	@param options formats.InputFormatOptions: 输入格式的选项，包含解析时需要的配置。
func (j *JSONFormat) SetOptions(options formats.InputFormatOptions) {
	j.opts = options
}

// Parse: 解析输入并调用提供的回调函数
// 对于每个发现的 RawRequest，都会调用该回调函数。
//
//	@receiver j *JSONFormat:
//	@param input string:  输入文件的路径，包含 JSON 格式的数据。
//	@param resultsCb formats.ParseReqRespCallback: 处理解析后的请求的回调函数，类型为 ParseReqRespCallback。
//	@return error error: 如果成功，返回 nil；如果发生错误，返回相应的错误信息。
func (j *JSONFormat) Parse(input string, resultsCb formats.ParseReqRespCallback) error {
	// 打开指定的输入文件
	file, err := os.Open(input)
	if err != nil {
		return errors.Wrap(err, "could not open json file")
	}
	// 确保在函数结束时关闭文件
	defer file.Close()

	// 创建 JSON 解码器
	decoder := json.NewDecoder(file)
	for {
		// 创建 proxifyRequest 实例
		var request proxifyRequest
		// 解码 JSON 数据到 request 结构体
		err := decoder.Decode(&request)
		if err == io.EOF {
			break
		}
		if err != nil {
			return errors.Wrap(err, "could not decode json file")
		}
		// 如果 URL 为空且 Endpoint 不为空，则使用 Endpoint 作为 URL
		if request.URL == "" && request.Request.Endpoint != "" {
			request.URL = request.Request.Endpoint
		}
		// 解析原始请求并附加 URL
		rawRequest, err := types.ParseRawRequestWithURL(request.Request.Raw, request.URL)
		if err != nil {
			// 如果解析请求失败，记录警告并继续处理下一个请求
			gologger.Warning().Msgf("jsonl: Could not parse raw request %s: %s\n", request.URL, err)
			continue
		}
		// 调用回调函数处理解析后的请求
		resultsCb(rawRequest)
	}
	return nil
}
