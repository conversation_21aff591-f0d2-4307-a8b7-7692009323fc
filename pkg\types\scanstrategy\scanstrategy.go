//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-21 11:01:19
//FilePath: /yaml_scan/pkg/types/scanstrategy/scanstrategy.go
//Description:

package scanstrategy

import mapsutil "yaml_scan/utils/maps"

// ScanStrategy 定义了支持的扫描策略类型
type ScanStrategy uint8

// 定义支持的扫描策略常量
const (
	Auto          ScanStrategy = iota // 自动扫描策略
	HostSpray                         // 主机喷射扫描策略
	TemplateSpray                     // 模板喷射扫描策略
)

// strategies 是一个映射，存储 ScanStrategy 常量与其字符串表示之间的关系
var strategies mapsutil.Map[ScanStrategy, string]

// init 函数用于初始化 strategies 映射
func init() {
	strategies = make(mapsutil.Map[ScanStrategy, string])
	strategies[Auto] = "auto"
	strategies[HostSpray] = "host-spray"
	strategies[TemplateSpray] = "template-spray"
}

// String 返回与 ScanStrategy 常量对应的字符串表示。
func (s ScanStrategy) String() string {
	return strategies[s]
}
