// Author: chenjb
// Version: V1.0
// Date: 2025-07-01 11:13:58
// FilePath: /yaml_scan/pkg/tlsx/tlsx/openssl/options_test.go
// Description:
package openssl

import (
	"testing"

	"github.com/stretchr/testify/require"
)


// TestGetProtocol 测试getProtocol函数
// 验证TLS版本字符串到协议枚举的转换
func TestGetProtocol(t *testing.T) {
	t.Run("有效版本转换", func(t *testing.T) {
		testCases := []struct {
			version  string
			expected Protocols
		}{
			{"tls10", TLSv1},
			{"tls11", TLSv1_1},
			{"tls12", TLSv1_2},
		}

		for _, tc := range testCases {
			protocol, err := getProtocol(tc.version)
			require.NoError(t, err, "转换版本'%s'应该成功", tc.version)
			require.Equal(t, tc.expected, protocol, "版本'%s'应该转换为协议%d", tc.version, tc.expected)
		}
	})

	t.Run("无效版本转换", func(t *testing.T) {
		invalidVersions := []string{
			"tls13",     // 注释掉的版本
			"dtls10",    // 注释掉的版本
			"dtls12",    // 注释掉的版本
			"ssl30",     // 不支持的版本
			"invalid",   // 完全无效的版本
			"",          // 空字符串
		}

		for _, version := range invalidVersions {
			protocol, err := getProtocol(version)
			require.Error(t, err, "转换无效版本'%s'应该失败", version)
			require.Equal(t, TLSUnsupported, protocol, "无效版本应该返回TLSUnsupported")
			require.Contains(t, err.Error(), "unsupported", "错误信息应该包含'unsupported'")
		}
	})
}


// TestOptionsArgs 测试Options.Args方法
// 验证OpenSSL命令参数生成功能
func TestOptionsArgs(t *testing.T) {
	t.Run("基本参数生成", func(t *testing.T) {
		opts := &Options{
			Address: "example.com:443",
		}

		args, err := opts.Args()
		require.NoError(t, err, "生成基本参数应该成功")
		require.Equal(t, []string{"s_client", "-connect", "example.com:443", "-tls1"}, args, "基本参数应该正确")
	})

	t.Run("完整参数生成", func(t *testing.T) {
		opts := &Options{
			Address:    "example.com:443",
			Cipher:     []string{"AES128-SHA", "AES256-SHA"},
			ServerName: "example.com",
			CertChain:  true,
			Protocol:   TLSv1_2,
			CAFile:     "/path/to/ca.pem",
		}

		args, err := opts.Args()
		require.NoError(t, err, "生成完整参数应该成功")
		
		expectedArgs := []string{
			"s_client",
			"-connect", "example.com:443",
			"-cipher", "AES128-SHA,AES256-SHA",
			"-servername", "example.com",
			"-showcerts",
			"-tls1_2",
			"-CAfile", "/path/to/ca.pem",
		}
		require.Equal(t, expectedArgs, args, "完整参数应该正确")
	})

	t.Run("缺少地址参数", func(t *testing.T) {
		opts := &Options{
			ServerName: "example.com",
			CertChain:  true,
		}

		args, err := opts.Args()
		require.Error(t, err, "缺少地址应该返回错误")
		require.Contains(t, err.Error(), "address missing", "错误信息应该包含'address missing'")
		require.Equal(t, []string{"s_client"}, args, "错误时应该只返回基础命令")
	})

	t.Run("空地址参数", func(t *testing.T) {
		opts := &Options{
			Address: "",
		}

		_, err := opts.Args()
		require.Error(t, err, "空地址应该返回错误")
		require.Contains(t, err.Error(), "address missing", "错误信息应该包含'address missing'")
	})

	t.Run("可选参数跳过", func(t *testing.T) {
		opts := &Options{
			Address:    "example.com:443",
			Cipher:     []string{}, // 空密码套件列表
			ServerName: "",         // 空服务器名称
			CertChain:  false,      // 不显示证书链
			Protocol:   TLSUnsupported, // 不支持的协议
			CAFile:     "",         // 空CA文件
		}

		args, err := opts.Args()
		require.NoError(t, err, "可选参数为空应该成功")
		require.Equal(t, []string{"s_client", "-connect", "example.com:443"}, args, 
			"空的可选参数应该被跳过")
	})

	t.Run("单个密码套件", func(t *testing.T) {
		opts := &Options{
			Address: "example.com:443",
			Cipher:  []string{"AES128-SHA"},
		}

		args, err := opts.Args()
		require.NoError(t, err, "单个密码套件应该成功")
		require.Contains(t, args, "-cipher", "应该包含-cipher参数")
		require.Contains(t, args, "AES128-SHA", "应该包含密码套件")
	})

	t.Run("多个密码套件连接", func(t *testing.T) {
		opts := &Options{
			Address: "example.com:443",
			Cipher:  []string{"AES128-SHA", "AES256-SHA", "ECDHE-RSA-AES128-GCM-SHA256"},
		}

		args, err := opts.Args()
		require.NoError(t, err, "多个密码套件应该成功")
		
		// 查找-cipher参数的位置
		cipherIndex := -1
		for i, arg := range args {
			if arg == "-cipher" {
				cipherIndex = i
				break
			}
		}
		require.NotEqual(t, -1, cipherIndex, "应该包含-cipher参数")
		require.Less(t, cipherIndex+1, len(args), "cipher参数后应该有值")
		
		cipherValue := args[cipherIndex+1]
		require.Equal(t, "AES128-SHA,AES256-SHA,ECDHE-RSA-AES128-GCM-SHA256", cipherValue,
			"多个密码套件应该用逗号连接")
	})
}

// TestSession 测试Session结构体
// 验证Session结构体的基本功能
func TestSession(t *testing.T) {
	t.Run("Session结构体字段验证", func(t *testing.T) {
		session := &Session{
			Protocol:  "TLSv1.2",
			Cipher:    "ECDHE-RSA-AES256-GCM-SHA384",
			SessionID: "1234567890ABCDEF",
			MasterKey: "FEDCBA0987654321",
		}

		require.Equal(t, "TLSv1.2", session.Protocol, "Protocol字段应该正确设置")
		require.Equal(t, "ECDHE-RSA-AES256-GCM-SHA384", session.Cipher, "Cipher字段应该正确设置")
		require.Equal(t, "1234567890ABCDEF", session.SessionID, "SessionID字段应该正确设置")
		require.Equal(t, "FEDCBA0987654321", session.MasterKey, "MasterKey字段应该正确设置")
	})

	t.Run("Session零值验证", func(t *testing.T) {
		session := &Session{}

		require.Empty(t, session.Protocol, "Protocol默认应该为空")
		require.Empty(t, session.Cipher, "Cipher默认应该为空")
		require.Empty(t, session.SessionID, "SessionID默认应该为空")
		require.Empty(t, session.MasterKey, "MasterKey默认应该为空")
	})
}

// TestSessionGetTLSVersion 测试Session.getTLSVersion方法
// 验证TLS版本标准化功能
func TestSessionGetTLSVersion(t *testing.T) {
	t.Run("标准TLS版本转换", func(t *testing.T) {
		testCases := []struct {
			protocol string
			expected string
		}{
			{"TLSv1", "tls10"},
			{"TLSv1.1", "tls11"},
			{"TLSv1.2", "tls12"},
		}

		for _, tc := range testCases {
			session := &Session{Protocol: tc.protocol}
			result := session.getTLSVersion()
			require.Equal(t, tc.expected, result, "协议'%s'应该转换为'%s'", tc.protocol, tc.expected)
		}
	})

	t.Run("未知协议版本", func(t *testing.T) {
		unknownProtocols := []string{
			"TLSv1.3",    // 注释掉的版本
			"SSLv3",      // 旧版本
			"DTLSv1",     // DTLS版本
			"Unknown",    // 完全未知
			"",           // 空字符串
		}

		for _, protocol := range unknownProtocols {
			session := &Session{Protocol: protocol}
			result := session.getTLSVersion()
			require.Equal(t, protocol, result, "未知协议'%s'应该原样返回", protocol)
		}
	})

	t.Run("大小写敏感性", func(t *testing.T) {
		// 测试大小写敏感性
		session := &Session{Protocol: "tlsv1.2"} // 小写
		result := session.getTLSVersion()
		require.Equal(t, "tlsv1.2", result, "大小写不匹配应该原样返回")
	})
}


