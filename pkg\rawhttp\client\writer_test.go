// Author: chenjb
// Version: V1.0
// Date: 2025-07-25
// FilePath: /yaml_scan/pkg/rawhttp/client/writer_test.go
// Description: rawhttp请求写入器模块单元测试

package client

import (
	"bytes"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestPhase_String 测试phase的String方法
func TestPhase_String(t *testing.T) {
	testCases := []struct {
		phase    phase
		expected string
		desc     string
	}{
		{requestline, "requestline", "请求行阶段"},
		{header, "headers", "头部阶段"},
		{body, "body", "请求体阶段"},
		{phase(999), "UNKNOWN", "未知阶段"},
	}
	
	for _, tc := range testCases {
		result := tc.phase.String()
		require.Equal(t, tc.expected, result, tc.desc+"字符串应该正确")
	}
}

// TestPhaseError_Error 测试phaseError的Error方法
func TestPhaseError_Error(t *testing.T) {
	err := &phaseError{
		expected: header,
		got:      requestline,
	}
	
	expected := "phase error: expected headers, got requestline"
	require.Equal(t, expected, err.Error(), "阶段错误信息应该正确")
}

// TestWriter_WriteRequestLine 测试WriteRequestLine方法
func TestWriter_WriteRequestLine(t *testing.T) {
	var buf bytes.Buffer
	w := &writer{
		phase:  requestline,
		Writer: &buf,
	}
	
	// 测试基本请求行
	err := w.WriteRequestLine("GET", "/path", nil, "HTTP/1.1")
	require.NoError(t, err, "写入请求行应该成功")
	require.Equal(t, header, w.phase, "阶段应该转换为header")
	
	// 验证写入的内容
	expected := "GET /path HTTP/1.1\r\n"
	require.Contains(t, buf.String(), expected, "请求行格式应该正确")
}

// TestWriter_WriteRequestLine_WithQuery 测试带查询参数的请求行
func TestWriter_WriteRequestLine_WithQuery(t *testing.T) {
	var buf bytes.Buffer
	w := &writer{
		phase:  requestline,
		Writer: &buf,
	}
	
	query := []string{"param1=value1", "param2=value2"}
	err := w.WriteRequestLine("GET", "/path", query, "HTTP/1.1")
	require.NoError(t, err, "写入带查询参数的请求行应该成功")
	
	// 验证查询参数格式
	expected := "GET /path?param1=value1&param2=value2 HTTP/1.1\r\n"
	require.Contains(t, buf.String(), expected, "查询参数格式应该正确")
}

// TestWriter_WriteRequestLine_WrongPhase 测试在错误阶段写入请求行
func TestWriter_WriteRequestLine_WrongPhase(t *testing.T) {
	var buf bytes.Buffer
	w := &writer{
		phase:  header, // 错误的阶段
		Writer: &buf,
	}
	
	err := w.WriteRequestLine("GET", "/path", nil, "HTTP/1.1")
	require.Error(t, err, "在错误阶段写入请求行应该返回错误")
	
	// 验证错误类型
	phaseErr, ok := err.(*phaseError)
	require.True(t, ok, "应该返回phaseError类型的错误")
	require.Equal(t, requestline, phaseErr.expected, "期望的阶段应该是requestline")
	require.Equal(t, header, phaseErr.got, "实际的阶段应该是header")
}

// TestWriter_WriteHeader 测试WriteHeader方法
func TestWriter_WriteHeader(t *testing.T) {
	var buf bytes.Buffer
	w := &writer{
		phase:  header,
		Writer: &buf,
	}
	
	// 测试有值的头部
	err := w.WriteHeader("Content-Type", "application/json")
	require.NoError(t, err, "写入有值头部应该成功")
	
	expected := "Content-Type: application/json\r\n"
	require.Equal(t, expected, buf.String(), "有值头部格式应该正确")
	
	// 测试无值的头部
	buf.Reset()
	err = w.WriteHeader("Custom-Header", "")
	require.NoError(t, err, "写入无值头部应该成功")
	
	expected = "Custom-Header\r\n"
	require.Equal(t, expected, buf.String(), "无值头部格式应该正确")
}

// TestWriter_WriteHeader_WrongPhase 测试在错误阶段写入头部
func TestWriter_WriteHeader_WrongPhase(t *testing.T) {
	var buf bytes.Buffer
	w := &writer{
		phase:  requestline, // 错误的阶段
		Writer: &buf,
	}
	
	err := w.WriteHeader("Content-Type", "application/json")
	require.Error(t, err, "在错误阶段写入头部应该返回错误")
	
	// 验证错误类型
	phaseErr, ok := err.(*phaseError)
	require.True(t, ok, "应该返回phaseError类型的错误")
	require.Equal(t, header, phaseErr.expected, "期望的阶段应该是header")
	require.Equal(t, requestline, phaseErr.got, "实际的阶段应该是requestline")
}

// TestWriter_StartBody 测试StartBody方法
func TestWriter_StartBody(t *testing.T) {
	var buf bytes.Buffer
	w := &writer{
		phase:  header,
		Writer: &buf,
	}
	
	// 模拟已经写入了请求行和头部
	w.WriteRequestLine("POST", "/api", nil, "HTTP/1.1")
	w.WriteHeader("Content-Type", "application/json")
	
	// 开始请求体
	err := w.StartBody()
	require.NoError(t, err, "开始请求体应该成功")
	require.Equal(t, body, w.phase, "阶段应该转换为body")
	
	// 验证空行分隔符被写入
	require.Contains(t, buf.String(), "\r\n\r\n", "应该包含空行分隔符")
}

// TestWriter_WriteBody 测试WriteBody方法
func TestWriter_WriteBody(t *testing.T) {
	var buf bytes.Buffer
	w := &writer{
		phase:  body,
		Writer: &buf,
	}
	
	bodyContent := "这是请求体内容"
	bodyReader := strings.NewReader(bodyContent)
	
	err := w.WriteBody(bodyReader)
	require.NoError(t, err, "写入请求体应该成功")
	require.Equal(t, requestline, w.phase, "阶段应该重置为requestline")
	
	// 验证请求体内容
	require.Equal(t, bodyContent, buf.String(), "请求体内容应该正确")
}

// TestWriter_WriteBody_WrongPhase 测试在错误阶段写入请求体
func TestWriter_WriteBody_WrongPhase(t *testing.T) {
	var buf bytes.Buffer
	w := &writer{
		phase:  header, // 错误的阶段
		Writer: &buf,
	}
	
	bodyReader := strings.NewReader("test body")
	err := w.WriteBody(bodyReader)
	require.Error(t, err, "在错误阶段写入请求体应该返回错误")
	
	// 验证错误类型
	phaseErr, ok := err.(*phaseError)
	require.True(t, ok, "应该返回phaseError类型的错误")
	require.Equal(t, body, phaseErr.expected, "期望的阶段应该是body")
	require.Equal(t, header, phaseErr.got, "实际的阶段应该是header")
}

// TestWriter_WriteChunked 测试WriteChunked方法
func TestWriter_WriteChunked(t *testing.T) {
	var buf bytes.Buffer
	w := &writer{
		phase:  body,
		Writer: &buf,
	}
	
	bodyContent := "chunked body content"
	bodyReader := strings.NewReader(bodyContent)
	
	err := w.WriteChunked(bodyReader)
	require.NoError(t, err, "写入分块请求体应该成功")
	require.Equal(t, requestline, w.phase, "阶段应该重置为requestline")
	
	// 验证分块格式（应该包含长度和内容）
	result := buf.String()
	require.NotEmpty(t, result, "分块输出不应该为空")
	require.Contains(t, result, bodyContent, "应该包含原始内容")
}

// TestWriter_WriteChunked_WrongPhase 测试在错误阶段写入分块请求体
func TestWriter_WriteChunked_WrongPhase(t *testing.T) {
	var buf bytes.Buffer
	w := &writer{
		phase:  header, // 错误的阶段
		Writer: &buf,
	}
	
	bodyReader := strings.NewReader("test body")
	err := w.WriteChunked(bodyReader)
	require.Error(t, err, "在错误阶段写入分块请求体应该返回错误")
	
	// 验证错误类型
	phaseErr, ok := err.(*phaseError)
	require.True(t, ok, "应该返回phaseError类型的错误")
	require.Equal(t, body, phaseErr.expected, "期望的阶段应该是body")
	require.Equal(t, header, phaseErr.got, "实际的阶段应该是header")
}

// TestWriter_StartHeaders 测试StartHeaders方法
func TestWriter_StartHeaders(t *testing.T) {
	w := &writer{phase: requestline}
	
	w.StartHeaders()
	require.Equal(t, header, w.phase, "阶段应该设置为header")
}

// TestWriter_CompleteRequest 测试完整的HTTP请求写入流程
func TestWriter_CompleteRequest(t *testing.T) {
	var buf bytes.Buffer
	w := &writer{
		phase:  requestline,
		Writer: &buf,
	}
	
	// 1. 写入请求行
	err := w.WriteRequestLine("POST", "/api/users", []string{"format=json"}, "HTTP/1.1")
	require.NoError(t, err, "写入请求行应该成功")
	
	// 2. 写入头部
	err = w.WriteHeader("Content-Type", "application/json")
	require.NoError(t, err, "写入Content-Type头部应该成功")
	
	err = w.WriteHeader("User-Agent", "test-client")
	require.NoError(t, err, "写入User-Agent头部应该成功")
	
	// 3. 开始请求体
	err = w.StartBody()
	require.NoError(t, err, "开始请求体应该成功")
	
	// 4. 写入请求体
	bodyContent := `{"name":"test","email":"<EMAIL>"}`
	err = w.WriteBody(strings.NewReader(bodyContent))
	require.NoError(t, err, "写入请求体应该成功")
	
	// 验证完整的HTTP请求格式
	result := buf.String()
	require.Contains(t, result, "POST /api/users?format=json HTTP/1.1", "应该包含正确的请求行")
	require.Contains(t, result, "Content-Type: application/json", "应该包含Content-Type头部")
	require.Contains(t, result, "User-Agent: test-client", "应该包含User-Agent头部")
	require.Contains(t, result, bodyContent, "应该包含请求体内容")
	require.Contains(t, result, "\r\n\r\n", "应该包含头部和请求体之间的分隔符")
}
