//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-09 16:30:24
//FilePath: /yaml_scan/pkg/retryabledns/client_queue_test.go
//Description:

package retryabledns

import (
	"testing"
	"time"

	"github.com/miekg/dns"
	"github.com/stretchr/testify/require"
)

// TestClientQueue tests the basic functionality of clientQueue
func TestClientQueue(t *testing.T) {
	// Create an instance of clientQueue
	pq := &clientQueue{}

	// Create some waitingClient instances
	client1 := &waitingClient{
		returnCh:    make(chan *dns.Conn),
		doneCh:      make(chan struct{}),
		arrivalTime: time.Now().Add(-3 * time.Second), // 3 seconds ago
	}
	client2 := &waitingClient{
		returnCh:    make(chan *dns.Conn),
		doneCh:      make(chan struct{}),
		arrivalTime: time.Now().Add(-1 * time.Second), // 1 second ago
	}
	client3 := &waitingClient{
		returnCh:    make(chan *dns.Conn),
		doneCh:      make(chan struct{}),
		arrivalTime: time.Now(), // now
	}

	// Add clients to the queue
	pq.Push(client1)
	pq.Push(client2)
	pq.Push(client3)

	// Verify the length of the queue
	require.Equal(t, 3, pq.Len(), "The length of the queue should be 3")

	// Verify the priority order using Less
	require.True(t, pq.Less(0, 1), "client1 should have higher priority than client2")
	require.True(t, pq.Less(1, 2), "client2 should have higher priority than client3")
	require.False(t, pq.Less(1, 0), "client2 should not have higher priority than client1")

	// Verify the priority order
	require.Equal(t, client1, (*pq)[0], "The highest priority should be client1")
	require.Equal(t, client2, (*pq)[1], "The second highest priority should be client2")
	require.Equal(t, client3, (*pq)[2], "The lowest priority should be client3")

	// Swap elements and verify the order
	pq.Swap(0, 1) // Swap client1 and client2
	require.Equal(t, client2, (*pq)[0], "After swap, the highest priority should be client2")
	require.Equal(t, client1, (*pq)[1], "After swap, the second highest priority should be client1")

	// Pop elements from the queue and verify the order
	poppedClient := pq.Pop()
	require.Equal(t, client3, poppedClient, "The popped element should be client3")
	require.Equal(t, 2, pq.Len(), "The length of the queue should be 2 after popping")

	poppedClient = pq.Pop()
	require.Equal(t, client1, poppedClient, "The popped element should be client1")
	require.Equal(t, 1, pq.Len(), "The length of the queue should be 1 after popping")

	poppedClient = pq.Pop()
	require.Equal(t, client2, poppedClient, "The popped element should be client2")
	require.Equal(t, 0, pq.Len(), "The length of the queue should be 0 after popping")
}
