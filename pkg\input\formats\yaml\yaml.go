// Author: chenjb
// Version: V1.0
// Date: 2024-11-15 15:50:12
// FilePath: /yaml_scan/pkg/input/formats/yaml/yaml.go
// Description: yaml格式解析
package yaml

import (
	"io"
	"os"
	"strings"
	"yaml_scan/pkg/gologger"
	"yaml_scan/pkg/input/formats"
	"yaml_scan/pkg/input/types"

	"github.com/pkg/errors"
	YamlUtil "gopkg.in/yaml.v3"
)

// YamlMultiDocFormat 是一个用于解析输入 HTTP 请求的 YAML 格式解析器
// 支持多个文档，文档之间用 "---" 分隔
type YamlMultiDocFormat struct {
	opts formats.InputFormatOptions
}

// New 创建一个新的 YAML 格式解析器
func New() *YamlMultiDocFormat {
	return &YamlMultiDocFormat{}
}

// 确保 YamlMultiDocFormat 实现了 formats.Format 接口
var _ formats.Format = &YamlMultiDocFormat{}

// proxifyRequest 是一个用于 proxify 的请求结构体
type proxifyRequest struct {
	URL     string `json:"url"`  // 请求的 URL
	Request struct {
		Header map[string]string `json:"header"`  // 请求头
		Body   string            `json:"body"`    // 请求体
		Raw    string            `json:"raw"`    // 原始请求字符串
	} `json:"request"`
}

// Name 返回格式的名称
// 返回字符串 "yaml"，表示该解析器的格式类型。
func (j *YamlMultiDocFormat) Name() string {
	return "yaml"
}

// SetOptions: 设置输入格式的选项
//  @receiver j *YamlMultiDocFormat: 
//  @param options formats.InputFormatOptions: 输入格式的选项，包含解析时需要的配置。
func (j *YamlMultiDocFormat) SetOptions(options formats.InputFormatOptions) {
	j.opts = options
}

// Parse: 解析输入并调用提供的回调函数
// 对于每个发现的 RawRequest，都会调用该回调函数。
//  @receiver j *YamlMultiDocFormat: 
//  @param input string: 输入文件的路径，包含 YAML 格式的数据。
//  @param resultsCb formats.ParseReqRespCallback: 处理解析后的请求的回调函数，类型为 ParseReqRespCallback。
//  @return error error: 如果成功，返回 nil；如果发生错误，返回相应的错误信息。
func (j *YamlMultiDocFormat) Parse(input string, resultsCb formats.ParseReqRespCallback) error {
	// 打开指定的输入文件
	file, err := os.Open(input)
	if err != nil {
		return errors.Wrap(err, "could not open json file")
	}
	// 确保在函数结束时关闭文件
	defer file.Close()

	// 创建 YAML 解码器
	decoder := YamlUtil.NewDecoder(file)
	for {
		 // 创建 proxifyRequest 实例
		var request proxifyRequest
		// 解码 YAML 数据到 request 结构体
		err := decoder.Decode(&request)
		// 如果到达文件末尾，退出循环
		if err == io.EOF {
			break
		}
		if err != nil {
			return errors.Wrap(err, "could not decode json file")
		}
		// 检查原始请求是否为空，若为空则跳过该请求
		if strings.TrimSpace(request.Request.Raw) == "" {
			continue
		}
		// 解析原始请求并附加 URL
		rawRequest, err := types.ParseRawRequestWithURL(request.Request.Raw, request.URL)
		if err != nil {
			// 如果解析请求失败，记录警告并继续处理下一个请求
			gologger.Warning().Msgf("multidoc-yaml: Could not parse raw request %s: %s\n", request.URL, err)
			continue
		}
		// 调用回调函数处理解析后的请求
		resultsCb(rawRequest)
	}
	return nil
}
