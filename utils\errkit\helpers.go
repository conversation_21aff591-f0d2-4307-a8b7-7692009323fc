// 
// Author: chenjb
// Version: V1.0
// Date: 2025-05-20 19:46:31
// FilePath: /yaml_scan/utils/errkit/helpers.go
// Description: 提供了一系列错误处理的辅助函数，简化了错误操作和转换
package errkit

import (
	"errors"
	"log/slog"
)

// Is 是对标准库errors.Is的代理函数
// 检查err是否匹配target中的任何一个错误
// @param err error: 要检查的错误对象
// @param target ...error: 一个或多个目标错误，用于匹配检查
// @return bool bool: 如果err与任一target匹配则返回true，否则返回false
func Is(err error, target ...error) bool {
	if err == nil {
		return false
	}
	for i := range target {
		t := target[i]
		if errors.Is(err, t) {
			return true
		}
	}
	return false
}

// IsKind 检查给定的错误是否等于给定的错误类型之一
// 如果错误尚未有类型，则尝试使用默认错误类型和给定类型进行解析
// @param err error: 要检查的错误对象
// @param match ...ErrKind: 一个或多个错误类型，用于匹配检查
// @return bool bool: 如果错误类型与任一match匹配则返回true，否则返回false
func IsKind(err error, match ...ErrKind) bool {
	if err == nil {
		return false
	}
	x := &ErrorX{}
	parseError(x, err)
	// 尝试从错误中解析类型
	if x.kind == nil {
		// 如果错误没有类型，创建默认错误类型集合
		tmp := []ErrKind{ErrKindDeadline, ErrKindNetworkPermanent, ErrKindNetworkTemporary}
		tmp = append(tmp, match...)
		x.kind = GetErrorKind(err, tmp...)
	}
	if x.kind != nil {
		if val, ok := x.kind.(*multiKind); ok && len(val.kinds) > 0 {
			// 如果是复合类型，检查每个子类型是否与match中任一类型匹配
			for _, kind := range val.kinds {
				for _, k := range match {
					if k.Is(kind) {
						return true
					}
				}
			}
		}
		for _, kind := range match {
			if kind.Is(x.kind) {
				return true
			}
		}
	}
	return false
}

// As 是对标准库errors.As的代理函数
// 尝试将err转换为target类型
// @param err error:  要转换的错误对象
// @param target interface{}: 标接口类型的指针，用于接收转换结果
// @return bool bool: 
func As(err error, target interface{}) bool {
	return errors.As(err, target)
}

// Combine 将多个错误合并为单个错误
// @param errs ...error: 要合并的错误列表
// @return error error: 合并后的错误，如果所有输入都是nil则返回nil
func Combine(errs ...error) error {
	if len(errs) == 0 {
		return nil
	}
	x := &ErrorX{}
	for _, err := range errs {
		if err == nil {
			continue
		}
		// 解析错误并添加到x中
		parseError(x, err)
	}
	return x
}

// Wrap 使用给定的消息包装错误
// @param err error: 要包装的原始错误
// @param message string: 包装消息
// @return error error: 包装后的错误
func Wrap(err error, message string) error {
	if err == nil {
		return nil
	}
	x := &ErrorX{}
	parseError(x, err)
	x.Msgf("%s", message)
	return x
}

// Wrapf 使用格式化的消息包装错误
// @param err error:  要包装的原始错误
// @param format string: 格式化字符串
// @param args ...interface{}: 式化参数
// @return error error: 
func Wrapf(err error, format string, args ...interface{}) error {
	if err == nil {
		return nil
	}
	x := &ErrorX{}
	parseError(x, err)
	x.Msgf(format, args...)
	return x
}

// Errors 返回所有被追加或连接的底层错误
// @param err error: 
// @return []error []error: 
func Errors(err error) []error {
	if err == nil {
		return nil
	}
	x := &ErrorX{}
	parseError(x, err)
	return x.errs
}

// Append 追加给定的错误并返回新的错误
// @param errs ...error: 
// @return error error: 
func Append(errs ...error) error {
	if len(errs) == 0 {
		return nil
	}
	x := &ErrorX{}
	for _, err := range errs {
		if err == nil {
			continue
		}
		parseError(x, err)
	}
	return x
}

// Join 连接给定的错误并返回新的错误
// 忽略所有nil错误
// 注意：与其他库不同，Join不使用`\n`
// 因此它等同于Append函数

// @param errs ...error: 
// @return error error: 
func Join(errs ...error) error {
	return Append(errs...)
}

// Cause  返回导致此错误的原始错误
// @param err error: 
// @return error error: 
func Cause(err error) error {
	if err == nil {
		return nil
	}
	x := &ErrorX{}
	parseError(x, err)
	return x.Cause()
}

// WithMessage 为错误添加消息
// @param err error: 
// @param message string: 
// @return error error: 
func WithMessage(err error, message string) error {
	if err == nil {
		return nil
	}
	x := &ErrorX{}
	parseError(x, err)
	x.Msgf("%s", message)
	return x
}

// WithMessagef  为错误添加格式化的消息
// @param err error: 
// @param format string: 
// @param args ...interface{}: 
// @return error error: 
func WithMessagef(err error, format string, args ...interface{}) error {
	if err == nil {
		return nil
	}
	x := &ErrorX{}
	parseError(x, err)
	x.Msgf(format, args...)
	return x
}

// IsNetworkTemporaryErr 检查给定的错误是否是临时网络错误
// @param err error: 
// @return bool bool: 
func IsNetworkTemporaryErr(err error) bool {
	if err == nil {
		return false
	}
	x := &ErrorX{}
	parseError(x, err)
	return isNetworkTemporaryErr(x)
}

// IsDeadlineErr  检查给定的错误是否是截止时间错误
// @param err error: 
// @return bool bool: 
func IsDeadlineErr(err error) bool {
	if err == nil {
		return false
	}
	x := &ErrorX{}
	parseError(x, err)
	return isDeadlineErr(x)
}

// IsNetworkPermanentErr 检查给定的错误是否是永久性网络错误
// @param err error: 
// @return bool bool: 
func IsNetworkPermanentErr(err error) bool {
	if err == nil {
		return false
	}
	x := &ErrorX{}
	parseError(x, err)
	return isNetworkPermanentErr(x)
}

// With 为错误添加额外的属性	
// @param err error: 
// @param args ...any: 
// @return error error: 
func With(err error, args ...any) error {
	if err == nil {
		return nil
	}
	if len(args) == 0 {
		return err
	}
	x := &ErrorX{}
	x.init()
	parseError(x, err)
	x.record.Add(args...)
	return x
}

// GetAttr 返回给定错误的所有属性（如果有的话）
// @param err error: 
// @return []slog.Attr []slog.Attr: 
func GetAttr(err error) []slog.Attr {
	if err == nil {
		return nil
	}
	x := &ErrorX{}
	parseError(x, err)
	return x.Attrs()
}

// ToSlogAttrGroup  为给定的错误返回一个slog属性组
// 格式如下：
//
//	{
//		"data": {
//			"kind": "<error-kind>",
//			"cause": "<cause>",
//			"errors": [
//				<errs>...
//			]
//		}
//	}
//
// @param err error: 
// @return slog.Attr slog.Attr: 
func ToSlogAttrGroup(err error) slog.Attr {
	attrs := ToSlogAttrs(err)
	g := slog.GroupValue(
		attrs..., // append all attrs
	)
	return slog.Any("data", g)
}

// ToSlogAttrs 为给定的错误返回slog属性列表
// 格式如下：
//
//	{
//		"kind": "<error-kind>",
//		"cause": "<cause>",
//		"errors": [
//			<errs>...
//		]
//	}
//
// @param err error: 
// @return []slog.Attr []slog.Attr: 
func ToSlogAttrs(err error) []slog.Attr {
	x := &ErrorX{}
	parseError(x, err)
	attrs := []slog.Attr{}
	if x.kind != nil {
		attrs = append(attrs, slog.Any("kind", x.kind.String()))
	}
	if cause := x.Cause(); cause != nil {
		attrs = append(attrs, slog.Any("cause", cause))
	}
	if len(x.errs) > 0 {
		attrs = append(attrs, slog.Any("errors", x.errs))
	}
	return attrs
}

// GetAttrValue 返回错误中具有给定键的属性的值
// @param err error: 
// @param key string: 
// @return slog.Value slog.Value: 
func GetAttrValue(err error, key string) slog.Value {
	if err == nil {
		return slog.Value{}
	}
	x := &ErrorX{}
	parseError(x, err)
	for _, attr := range x.Attrs() {
		if attr.Key == key {
			return attr.Value
		}
	}
	return slog.Value{}
}

