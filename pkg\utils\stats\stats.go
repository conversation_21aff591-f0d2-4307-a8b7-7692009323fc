// Author: chenjb
// Version: V1.0
// Date: 2025-06-18 11:11:56
// FilePath: /yaml_scan/pkg/utils/stats/stats.go
// Description:
package stats

import (
	"sync/atomic"
	mapsutil "yaml_scan/utils/maps"
)

type storageDataItem struct {
	description string
	value       atomic.Int64
}


// Storage is a storage for storing statistics information
// about the nuclei engine displaying it at user-defined intervals.
type Storage struct {
	data *mapsutil.SyncLockMap[string, *storageDataItem]
}

// GetValue returns the value for a set variable
func GetValue(name string) int64 {
	return Default.GetValue(name)
}



// GetValue returns the value for a set variable
func (s *Storage) GetValue(name string) int64 {
	data, ok := s.data.Get(name)
	if !ok {
		return 0
	}

	return data.value.Load()
}

