//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-20 16:51:08
//FilePath: /yaml_scan/pkg/goflags/callback_var_test.go
//Description:

package goflags

import (
	"testing"
)


func TestCallbackVar(t *testing.T) {
	var callbackExecuted bool
	callback := func() {
		callbackExecuted = true
	}

	flagSet := NewFlagSet()
	// 添加一个回调标志
	flagSet.CallbackVar(callback, "callback", "callback flag usage")

	// 模拟设置标志值
	err := flagSet.CommandLine.Set("callback", "true")
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}
	if !callbackExecuted {
		t.<PERSON>("Expected callback to be executed, but it was not")
	}
}

func TestCallbackVarP(t *testing.T) {
	var callbackExecuted bool
	callback := func() {
		callbackExecuted = true
	}

	flagSet := NewFlagSet() 

	// 添加一个回调标志
	flagSet.CallbackVarP(callback, "callback", "c", "callback flag usage")

	// 模拟设置标志值
	err := flagSet.CommandLine.Set("c", "true")
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}
	if !callbackExecuted {
		t.Errorf("Expected callback to be executed, but it was not")
	}
}