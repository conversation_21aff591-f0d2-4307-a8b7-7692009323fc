// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 16:53:34
// FilePath: /yaml_scan/pkg/retryablehttp/methods.go
// Description: 提供HTTP客户端的便捷方法，封装常见的HTTP请求方式（GET、HEAD、POST等）
package retryablehttp

import (
	"net/http"
	"net/url"
	"strings"
)

// Get 是一个便捷辅助函数，用于执行简单的GET请求
// @receiver c 
// @param url string: 
// @return *http.Response *http.Response: 
// @return error error: 
func (c *Client) Get(url string) (*http.Response, error) {
	req, err := NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return nil, err
	}
	return c.Do(req)
}

// Head 是一个便捷方法，用于执行简单的HEAD请求
// @receiver c 
// @param url string: 
// @return *http.Response *http.Response: 
// @return error error: 
func (c *Client) Head(url string) (*http.Response, error) {
	req, err := NewRequest(http.MethodHead, url, nil)
	if err != nil {
		return nil, err
	}
	return c.Do(req)
}

// Post 是一个便捷方法，用于执行简单的POST请求
// @receiver c 
// @param url string: 
// @param bodyType string: 
// @param body interface{}: 
// @return *http.Response *http.Response: 
// @return error error: 
func (c *Client) Post(url, bodyType string, body interface{}) (*http.Response, error) {
	req, err := NewRequest(http.MethodPost, url, body)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", bodyType)
	return c.Do(req)
}

// PostForm 是一个便捷方法，用于使用预填充的url.Values表单数据执行简单的POST操作
// @receiver c 
// @param url string: 
// @param data url.Values: 
// @return *http.Response *http.Response: 
// @return error error: 
func (c *Client) PostForm(url string, data url.Values) (*http.Response, error) {
	return c.Post(url, "application/x-www-form-urlencoded", strings.NewReader(data.Encode()))
}

