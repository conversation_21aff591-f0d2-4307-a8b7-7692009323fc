// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-11 16:27:40
// FilePath: /yaml_scan/pkg/retryablehttp/retry_test.go
// Description: 
package retryablehttp

import (
	"context"
	"crypto/x509"
	"errors"
	"net/http"
	"net/url"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestDefaultRetryPolicy 测试默认重试策略
func TestDefaultRetryPolicy(t *testing.T) {
	r := require.New(t)

	// 创建重试策略函数
	retry := DefaultRetryPolicy()
	r.NotNil(retry, "重试策略函数不应为nil")

	// 创建上下文
	ctx := context.Background()

	// 测试用例1: 无错误的情况
	t.Run("无错误", func(t *testing.T) {
		r := require.New(t)

		// 创建正常响应
		resp := &http.Response{
			StatusCode: http.StatusOK,
		}

		// 执行重试策略
		shouldRetry, err := retry(ctx, resp, nil)
		r.<PERSON>Error(err, "无错误情况下不应返回错误")
		r.<PERSON><PERSON><PERSON>(shouldRetry, "无错误情况下不应重试")
	})

	// 测试用例2: 上下文取消的情况
	t.Run("上下文取消", func(t *testing.T) {
		r := require.New(t)

		// 创建已取消的上下文
		cancelCtx, cancel := context.WithCancel(context.Background())
		cancel() // 立即取消

		// 执行重试策略
		shouldRetry, err := retry(cancelCtx, nil, errors.New("测试错误"))
		r.Error(err, "上下文取消情况下应返回错误")
		r.Equal(context.Canceled, err, "应返回上下文取消错误")
		r.False(shouldRetry, "上下文取消情况下不应重试")
	})

	// 测试用例3: 网络错误的情况
	t.Run("网络错误", func(t *testing.T) {
		r := require.New(t)

		// 创建网络错误
		networkErr := errors.New("network error")

		// 执行重试策略
		shouldRetry, err := retry(ctx, nil, networkErr)
		r.NoError(err, "网络错误情况下不应返回新错误")
		r.True(shouldRetry, "网络错误情况下应该重试")
	})
}

// TestCheckRecoverableErrors 测试可恢复错误检查函数
func TestCheckRecoverableErrors(t *testing.T) {

	// 创建上下文
	ctx := context.Background()

	// 测试用例1: URL错误 - 重定向次数过多
	t.Run("重定向次数过多", func(t *testing.T) {
		r := require.New(t)

		// 创建模拟的URL错误，提示重定向次数过多
		urlErr := &url.Error{
			Op:  "Get",
			URL: "https://example.com",
			Err: errors.New("stopped after 10 redirects"),
		}

		// 执行重试策略
		shouldRetry, err := CheckRecoverableErrors(ctx, nil, urlErr)
		r.NoError(err, "不应返回新错误")
		r.False(shouldRetry, "重定向次数过多不应重试")
	})

	// 测试用例2: URL错误 - 不支持的协议
	t.Run("不支持的协议", func(t *testing.T) {
		r := require.New(t)

		// 创建模拟的URL错误，提示不支持的协议
		urlErr := &url.Error{
			Op:  "Get",
			URL: "invalid://example.com",
			Err: errors.New("unsupported protocol scheme"),
		}

		// 执行重试策略
		shouldRetry, err := CheckRecoverableErrors(ctx, nil, urlErr)
		r.NoError(err, "不应返回新错误")
		r.False(shouldRetry, "不支持的协议不应重试")
	})

	// 测试用例3: URL错误 - TLS证书验证失败
	t.Run("TLS证书验证失败", func(t *testing.T) {
		r := require.New(t)

		// 创建模拟的URL错误，提示证书验证失败
		urlErr := &url.Error{
			Op:  "Get",
			URL: "https://example.com",
			Err: x509.UnknownAuthorityError{},
		}

		// 执行重试策略
		shouldRetry, err := CheckRecoverableErrors(ctx, nil, urlErr)
		r.NoError(err, "不应返回新错误")
		r.False(shouldRetry, "TLS证书验证失败不应重试")
	})

	// 测试用例5: 临时性网络错误
	t.Run("临时性网络错误", func(t *testing.T) {
		r := require.New(t)

		// 创建临时性网络错误
		netErr := errors.New("connection reset")

		// 执行重试策略
		shouldRetry, err := CheckRecoverableErrors(ctx, nil, netErr)
		r.NoError(err, "临时性网络错误不应返回新错误")
		r.True(shouldRetry, "临时性网络错误应该重试")
	})

	// 测试用例6: 上下文取消
	t.Run("上下文取消", func(t *testing.T) {
		r := require.New(t)

		// 创建已取消的上下文
		cancelCtx, cancel := context.WithCancel(context.Background())
		cancel() // 立即取消

		// 执行重试策略
		shouldRetry, err := CheckRecoverableErrors(cancelCtx, nil, errors.New("测试错误"))
		r.Error(err, "上下文取消情况下应返回错误")
		r.Equal(context.Canceled, err, "应返回上下文取消错误")
		r.False(shouldRetry, "上下文取消情况下不应重试")
	})
}

// TestHostSprayRetryPolicy 测试主机喷洒重试策略
func TestHostSprayRetryPolicy(t *testing.T) {
	r := require.New(t)

	// 创建重试策略函数
	retry := HostSprayRetryPolicy()
	r.NotNil(retry, "重试策略函数不应为nil")

	// 创建上下文
	ctx := context.Background()

	// 测试用例1: 无错误的情况
	t.Run("无错误", func(t *testing.T) {
		r := require.New(t)

		// 创建正常响应
		resp := &http.Response{
			StatusCode: http.StatusOK,
		}

		// 执行重试策略
		shouldRetry, err := retry(ctx, resp, nil)
		r.NoError(err, "无错误情况下不应返回错误")
		r.False(shouldRetry, "无错误情况下不应重试")
	})

	// 测试用例2: 网络错误的情况
	t.Run("网络错误", func(t *testing.T) {
		r := require.New(t)

		// 创建网络错误
		networkErr := errors.New("network error")

		// 执行重试策略
		shouldRetry, err := retry(ctx, nil, networkErr)
		r.NoError(err, "网络错误情况下不应返回新错误")
		r.True(shouldRetry, "网络错误情况下应该重试")
	})

	// 注意：目前HostSprayRetryPolicy与DefaultRetryPolicy实现相同，
	// 因此测试用例也类似。未来如果实现有差异，应添加更多特定测试。
}


