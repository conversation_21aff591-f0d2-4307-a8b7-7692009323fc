// Author: chenjb
// Version: V1.0
// Date: 2025-06-10 16:39:21
// FilePath: /yaml_scan/pkg/retryablehttp/client_test.go
// Description:
package retryablehttp

import (
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestNewClient 测试创建新客户端的功能
func TestNewClient(t *testing.T) {

	// 测试用例1: 使用默认选项创建客户端
	t.Run("使用默认选项", func(t *testing.T) {
		r := require.New(t)
		options := Options{
			RetryWaitMin:  1 * time.Second,
			RetryWaitMax:  30 * time.Second,
			Timeout:       30 * time.Second,
			RetryMax:      5,
			RespReadLimit: 4096,
		}

		client := NewClient(options)
		r.NotNil(client, "使用默认选项创建的客户端不应为nil")
		r.Equal(options.RetryMax, client.options.RetryMax, "重试最大次数应该与设置一致")
		r.Equal(options.Timeout, client.options.Timeout, "超时应该与设置一致")
	})

	// 测试用例2: 使用自定义HTTP客户端创建
	t.Run("使用自定义HTTP客户端", func(t *testing.T) {
		r := require.New(t)
		customHTTPClient := &http.Client{
			Timeout: 10 * time.Second,
		}

		options := Options{
			HttpClient:   customHTTPClient,
			RetryWaitMin: 1 * time.Second,
			RetryWaitMax: 30 * time.Second,
			Timeout:      20 * time.Second,
		}

		client := NewClient(options)
		r.NotNil(client, "使用自定义HTTP客户端创建的retryablehttp客户端不应为nil")
		r.Equal(customHTTPClient, client.HTTPClient, "HTTP客户端应该是提供的自定义客户端")

		// 检查超时设置是否生效
		r.Equal(20*time.Second, client.HTTPClient.Timeout, "超时时间应该被正确设置")
	})

	// 测试用例3: 调整超时
	t.Run("自动调整超时", func(t *testing.T) {
		r := require.New(t)
		options := Options{
			RetryWaitMin:    1 * time.Second,
			RetryWaitMax:    30 * time.Second,
			Timeout:         30 * time.Second,
			RetryMax:        5,
			NoAdjustTimeout: false, // 允许调整超时
		}

		client := NewClient(options)
		r.NotNil(client, "客户端不应为nil")

		// 检查超时是否被调整（默认调整为总超时的30%）
		expectedTimeout := time.Duration(30*0.3) * time.Second
		r.Equal(expectedTimeout, client.HTTPClient.Timeout, "超时时间应该被调整为总超时的30%")
	})
}

// TestClientSetKillIdleConnections 测试设置关闭空闲连接的功能
func TestClientSetKillIdleConnections(t *testing.T) {
	r := require.New(t)

	// 创建一个测试客户端
	options := DefaultOptionsSingle
	options.KillIdleConn = false // 初始设置为false

	client := NewClient(options)
	r.NotNil(client, "客户端不应为nil")
	r.False(client.options.KillIdleConn, "KillIdleConn初始应为false")

	// 获取初始传输对象并修改设置
	transport, ok := client.HTTPClient.Transport.(*http.Transport)
	r.True(ok, "传输对象应为*http.Transport类型")

	// 修改传输对象设置
	transport.DisableKeepAlives = true

	// 重新设置KillIdleConnections
	client.setKillIdleConnections()

	// 验证KillIdleConn已被设置为true，因为DisableKeepAlives为true
	r.True(client.options.KillIdleConn, "传输对象禁用长连接后，KillIdleConn应被设置为true")
}


// TestDoWithMockServer 使用模拟服务器测试请求执行功能
func TestDoWithMockServer(t *testing.T) {
	r := require.New(t)

	// 创建模拟服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("测试响应成功"))
	}))
	defer server.Close()

	// 创建客户端
	client := NewClient(DefaultOptionsSingle)
	r.NotNil(client, "客户端不应为nil")

	// 创建请求
	req, err := NewRequest("GET", server.URL, nil)
	r.NoError(err, "创建请求不应有错误")

	// 执行请求
	resp, err := client.Do(req)
	r.NoError(err, "执行请求不应有错误")
	r.NotNil(resp, "响应不应为nil")
	r.Equal(http.StatusOK, resp.StatusCode, "状态码应为200")

	// 确保响应可以正常关闭
	defer resp.Body.Close()
}

// TestDoWithRetry 测试重试功能
func TestDoWithRetry(t *testing.T) {
	r := require.New(t)

	// 创建客户端，设置最多重试2次
	options := Options{
		RetryWaitMin:    5 * time.Second,
		RetryWaitMax:    5 * time.Second,
		Timeout:         5 * time.Second,
		RetryMax:        2, // 最多重试2次
		RespReadLimit:   4096,
		NoAdjustTimeout: true,
	}
	client := NewClient(options)

	resp, err := client.Get("https://www.baidu.com")
	r.NoError(err, "执行请求不应有错误")
	r.NotNil(resp, "响应不应为nil")
	r.Equal(http.StatusOK, resp.StatusCode, "状态码应为200")


	// 确保响应可以正常关闭
	defer resp.Body.Close()
}

