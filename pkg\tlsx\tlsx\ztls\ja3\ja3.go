//
// Author: chenjb
// Version: V1.0
// Date: 2025-06-24 15:48:58
// FilePath: /yaml_scan/pkg/tlsx/tlsx/ztls/ja3/ja3.go
// Description: JA3和JA3S TLS指纹识别实现

package ja3

import (
	"crypto/md5"
	"encoding/hex"
	"strconv"

	"github.com/zmap/zcrypto/tls"
)

const (
	dashByte  = byte(45) // ASCII码45，对应字符'-'，用作分隔符
	commaByte = byte(44) // ASCII码44，对应字符','，用作字段分隔符

	// GREASE是一种防止TLS实现过于严格的机制，使用随机值测试扩展性
	// 位掩码覆盖所有GREASE值，用于在指纹计算中过滤这些值
	greaseBitmask uint16 = 0x0F0F
)

// TLS extension numbers
const (
	extensionServerName           uint16 = 0      // 服务器名称指示（SNI）扩展
	extensionStatusRequest        uint16 = 5      // OCSP状态请求扩展
	extensionSupportedCurves      uint16 = 10     // 支持的椭圆曲线扩展
	extensionSupportedPoints      uint16 = 11     // 支持的椭圆曲线点格式扩展
	extensionSignatureAlgorithms  uint16 = 13     // 签名算法扩展
	extensionALPN                 uint16 = 16     // 应用层协议协商（ALPN）扩展
	extensionExtendedMasterSecret uint16 = 23     // 扩展主密钥扩展
	extensionSessionTicket        uint16 = 35     // 会话票据扩展
	extensionNextProtoNeg         uint16 = 13172  // 下一协议协商扩展（非IANA分配）
	extensionRenegotiationInfo    uint16 = 0xff01 // 重新协商信息扩展
	extensionExtendedRandom       uint16 = 0x0028 // 扩展随机数扩展（非IANA分配）
	extensionSCT                  uint16 = 18     // 签名证书时间戳（SCT）扩展
	extensionHeartbeat            uint16 = 15     // 心跳扩展
)

// GetJa3Hash 从TLS ClientHello消息计算JA3客户端指纹哈希
// JA3指纹格式：TLS版本,密码套件,TLS扩展,椭圆曲线,椭圆曲线点格式
// @param clientHello *tls.ClientHello: ZMap TLS库解析的ClientHello消息结构
// @return string string: 32字符的MD5哈希字符串，作为客户端的唯一指纹
// JA3指纹结构说明:
//  1. TLS版本：客户端支持的TLS协议版本
//  2. 密码套件：客户端提供的密码套件列表（用'-'分隔）
//  3. TLS扩展：客户端使用的TLS扩展列表（用'-'分隔，过滤GREASE值）
//  4. 椭圆曲线：客户端支持的椭圆曲线列表（用'-'分隔）
//  5. 椭圆曲线点格式：客户端支持的点格式列表（用'-'分隔）
//     各字段之间用','分隔，最终对整个字符串计算MD5哈希
func GetJa3Hash(clientHello *tls.ClientHello) string {
	// 用于构建JA3指纹字符串的字节缓冲区
	byteString := make([]byte, 0)

	// 第一部分：TLS版本
	// 将TLS版本号转换为十进制字符串并添加到指纹中
	byteString = strconv.AppendUint(byteString, uint64(clientHello.Version), 10)
	byteString = append(byteString, commaByte)

	// 第二部分：密码套件列表
	if len(clientHello.CipherSuites) != 0 {
		// 遍历所有密码套件，将每个套件的数值添加到指纹中
		for _, val := range clientHello.CipherSuites {
			byteString = strconv.AppendUint(byteString, uint64(val), 10)
			byteString = append(byteString, dashByte)
		}
		// 将最后一个'-'替换为','，作为字段分隔符
		byteString[len(byteString)-1] = commaByte
	} else {
		// 如果没有密码套件，直接添加字段分隔符
		byteString = append(byteString, commaByte)
	}

	// 第三部分：TLS扩展列表
	// 根据ClientHello中存在的扩展，按顺序添加扩展编号
	// 注意：这里不包含GREASE值，因为它们会被过滤掉

	// 服务器名称指示（SNI）扩展
	if len(clientHello.ServerName) > 0 {
		byteString = appendExtension(byteString, extensionServerName)
	}

	// if clientHello.NextProtoNeg {
	// 	byteString = appendExtension(byteString, extensionNextProtoNeg)
	// }

	// OCSP装订扩展（在线证书状态协议）
	if clientHello.OcspStapling {
		byteString = appendExtension(byteString, extensionStatusRequest)
	}

	// 支持的椭圆曲线扩展
	if len(clientHello.SupportedCurves) > 0 {
		byteString = appendExtension(byteString, extensionSupportedCurves)
	}

	// 支持的椭圆曲线点格式扩展
	if len(clientHello.SupportedPoints) > 0 {
		byteString = appendExtension(byteString, extensionSupportedPoints)
	}

	// 会话票据扩展
	if clientHello.TicketSupported {
		byteString = appendExtension(byteString, extensionSessionTicket)
	}

	// 签名算法扩展
	if len(clientHello.SignatureAndHashes) > 0 {
		byteString = appendExtension(byteString, extensionSignatureAlgorithms)
	}

	// 安全重新协商扩展
	if clientHello.SecureRenegotiation {
		byteString = appendExtension(byteString, extensionRenegotiationInfo)
	}

	// 应用层协议协商（ALPN）扩展
	if len(clientHello.AlpnProtocols) > 0 {
		byteString = appendExtension(byteString, extensionALPN)
	}

	// 心跳扩展（用于保持连接活跃）
	if clientHello.HeartbeatSupported {
		byteString = appendExtension(byteString, extensionHeartbeat)
	}

	// 扩展随机数扩展（增强安全性）
	if len(clientHello.ExtendedRandom) > 0 {
		byteString = appendExtension(byteString, extensionExtendedRandom)
	}

	// 扩展主密钥扩展（增强密钥安全性）
	if clientHello.ExtendedMasterSecret {
		byteString = appendExtension(byteString, extensionExtendedMasterSecret)
	}

	// 签名证书时间戳（SCT）扩展（证书透明度）
	if clientHello.SctEnabled {
		byteString = appendExtension(byteString, extensionSCT)
	}

	// 处理未知扩展（非标准或新扩展）
	if len(clientHello.UnknownExtensions) > 0 {
		for _, ext := range clientHello.UnknownExtensions {
			exType := uint16(ext[0])<<8 | uint16(ext[1])
			byteString = appendExtension(byteString, exType)
		}
	}
	
	// 处理扩展列表结束：将最后的'-'替换为','作为字段分隔符
	if byteString[len(byteString)-1] == dashByte {
		byteString[len(byteString)-1] = commaByte
	} else {
		// 如果没有扩展，直接添加字段分隔符
		byteString = append(byteString, commaByte)
	}

	// 第四部分：支持的椭圆曲线列表
	// 椭圆曲线用于椭圆曲线密码学（ECC），提供高效的公钥加密
	if len(clientHello.SupportedCurves) > 0 {
		for _, val := range clientHello.SupportedCurves {
			// 将每个椭圆曲线的数值标识符添加到指纹中
			byteString = strconv.AppendUint(byteString, uint64(val), 10)
			byteString = append(byteString, dashByte)
		}
		// 将最后一个'-'替换为','，作为字段分隔符
		byteString[len(byteString)-1] = commaByte
	} else {
		// 如果没有支持的椭圆曲线，直接添加字段分隔符
		byteString = append(byteString, commaByte)
	}

	// 第五部分：椭圆曲线点格式列表
	// 点格式定义了椭圆曲线上点的表示方式（如压缩、非压缩格式）
	if len(clientHello.SupportedPoints) > 0 {
		for _, val := range clientHello.SupportedPoints {
			// 将每个点格式的数值标识符添加到指纹中
			byteString = strconv.AppendUint(byteString, uint64(val), 10)
			byteString = append(byteString, dashByte)
		}
		// 移除最后一个'-'（这是JA3指纹的最后一个字段）
		byteString = byteString[:len(byteString)-1]
	}

	// 计算完整JA3指纹字符串的MD5哈希值
	h := md5.Sum(byteString)
	return hex.EncodeToString(h[:])
}

// GetJa3sHash 从TLS ServerHello消息计算JA3S服务器指纹哈希
// JA3S是JA3的服务器端对应版本，用于分析服务器在SSL/TLS通信中的行为模式
// JA3S指纹格式：TLS版本,密码套件,TLS扩展
// @param serverHello *tls.ServerHello: ZMap TLS库解析的ServerHello消息结构
// @return string string: 32字符的MD5哈希字符串，作为服务器的唯一指纹
// JA3S指纹结构说明:
//   1. TLS版本：服务器选择的TLS协议版本
//   2. 密码套件：服务器选择的单个密码套件
//   3. TLS扩展：服务器返回的TLS扩展列表（用'-'分隔）
//   各字段之间用','分隔，最终对整个字符串计算MD5哈希
//
// 注意:
//   - 原始JA3S实现仅使用实际存在的扩展
//   - 参考Salesforce的JA3 Python实现
//   - 关注ZCrypto库的更新以获取更多字段支持
func GetJa3sHash(serverHello *tls.ServerHello) string {

	byteString := make([]byte, 0)

	// 第一部分：TLS版本
	// 将服务器选择的TLS版本号转换为十进制字符串
	byteString = strconv.AppendUint(byteString, uint64(serverHello.Version), 10)
	byteString = append(byteString, commaByte)

	// 第二部分：密码套件
	// 服务器从客户端提供的列表中选择一个密码套件
	if len(serverHello.CipherSuite.Bytes()) != 0 {
		byteString = strconv.AppendUint(byteString, uint64(serverHello.CipherSuite), 10)
		byteString = append(byteString, commaByte)
	} else {
		// 如果没有密码套件信息，直接添加字段分隔符
		byteString = append(byteString, commaByte)
	}

	// Extensions
	// if serverHello.NextProtoNeg {
	// 	byteString = appendExtension(byteString, extensionNextProtoNeg)
	// }

	// OCSP装订扩展（在线证书状态协议）
	if serverHello.OcspStapling {
		byteString = appendExtension(byteString, extensionStatusRequest)
	}

	// 会话票据扩展（用于会话恢复）
	if serverHello.TicketSupported {
		byteString = appendExtension(byteString, extensionSessionTicket)
	}

	// 安全重新协商扩展（防止重协商攻击）
	if serverHello.SecureRenegotiation {
		byteString = appendExtension(byteString, extensionRenegotiationInfo)
	}

	// 心跳扩展（保持连接活跃）
	if serverHello.HeartbeatSupported {
		byteString = appendExtension(byteString, extensionHeartbeat)
	}

	// 扩展随机数扩展（增强安全性）
	if len(serverHello.ExtendedRandom) > 0 {
		byteString = appendExtension(byteString, extensionExtendedRandom)
	}

	// 扩展主密钥扩展（增强密钥安全性）
	if serverHello.ExtendedMasterSecret {
		byteString = appendExtension(byteString, extensionExtendedMasterSecret)
	}

	// 处理未知扩展（非标准或新扩展）
	if len(serverHello.UnknownExtensions) > 0 {
		for _, ext := range serverHello.UnknownExtensions {
			// 从扩展字节数据中提取扩展类型（前两个字节）
			exType := uint16(ext[0])<<8 | uint16(ext[1])
			byteString = appendExtension(byteString, exType)
		}
	}
	
	// 处理扩展列表结束：将最后的'-'替换为','
	if byteString[len(byteString)-1] == dashByte {
		byteString[len(byteString)-1] = commaByte
	} else {
		// 如果没有扩展，直接添加字段分隔符
		byteString = append(byteString, commaByte)
	}

	// 计算完整JA3S指纹字符串的MD5哈希值
	h := md5.Sum(byteString)
	return hex.EncodeToString(h[:])
}

// appendExtension 将TLS扩展类型添加到JA3指纹字符串中
// 该函数负责过滤GREASE值并将有效的扩展类型添加到指纹构建过程中
// @param byteString []byte: 当前正在构建的JA3指纹字节切片
// @param exType uint16: TLS扩展类型的数值标识符
// @return []byte []byte: 更新后的指纹字节切片
// GREASE过滤机制:
//   - GREASE (Generate Random Extensions And Sustain Extensibility) 是一种防止TLS实现过于严格的机制
//   - 使用位掩码 0x0F0F 检测GREASE值模式
//   - GREASE值的特征是 (value & 0x0F0F) == 0x0A0A
//   - 过滤GREASE值确保指纹的一致性和可重现性
func appendExtension(byteString []byte, exType uint16) []byte {
	// 过滤GREASE扩展：检查扩展类型是否符合GREASE模式
	// GREASE值的位模式特征：(exType & 0x0F0F) == 0x0A0A
	if exType&greaseBitmask != 0x0A0A {
		// 非GREASE扩展，将其数值添加到指纹字符串中
		byteString = strconv.AppendUint(byteString, uint64(exType), 10)
		byteString = append(byteString, dashByte)
	}
	return byteString
}
