//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-09 15:39:59
//FilePath: /yaml_scan/pkg/retryabledns/options.go
//Description: dns客户端的配置选项

package retryabledns

import (
	"errors"
	"fmt"
	"net"
	"time"
)

var (
	// 最大重试次数为零的错误
	ErrMaxRetriesZero = errors.New("retries must be at least 1")
	// 解析器列表为空的错误
	ErrResolversEmpty = errors.New("resolvers list must not be empty")
	// 基础解析列表
	BaseResolvers = []string{
		"*******:53",
		"*******:53",
		"*******:53",
		"*******:53",
	}
	// 默认配置
	DefaultOptions = Options{
		BaseResolvers: BaseResolvers,
		MaxRetries:    1,
		Timeout:       3 * time.Second,
	}
	// 最大默认跟踪CNAME的次数
	DefaultMaxPerCNAMEFollows = 32
	// 默认不检查内部ip
	CheckInternalIPs          = false
)

// Options 配置DNS的选项。
type Options struct {
	BaseResolvers         []string      // 基础解析器列表，通常是 DNS 服务器的地址
	MaxRetries            int           // 最大重试次数
	Timeout               time.Duration // DNS 查询的超时时间
	Hostsfile             bool          // 是否使用 hosts 文件进行解析
	LocalAddrIP           net.IP        // 本地 IP 地址，用于指定源地址
	LocalAddrPort         uint16        // 本地端口，用于指定源端口
	ConnectionPoolThreads int           // 连接池线程数，控制并发连接数
	MaxPerCNAMEFollows    int           // 每个 CNAME 跟随的最大次数
	Proxy                 string        // 代理服务器地址，如果需要通过代理进行 DNS 查询 支持"socks5", "socks5h"
}

// Validate: 验证 Options 的有效性。
//
//	@receiver options *Options:
//	@return error error:
func (options *Options) Validate() error {
	// 如果最大重试次数为零，返回错误
	if options.MaxRetries == 0 {
		return ErrMaxRetriesZero
	}

	// 如果解析器列表为空，返回错误
	if len(options.BaseResolvers) == 0 {
		return ErrResolversEmpty
	}
	return nil
}

// GetLocalAddr:获取本地地址
//
//	@receiver options *Options:
//	@param proto Protocol: 协议类型
//	@return net.Addr net.Addr: 返回本地地址的 net.Addr 接口，如果 LocalAddrIP 为 nil 则返回 nil
func (options *Options) GetLocalAddr(proto Protocol) net.Addr {
	// 检查 LocalAddrIP 是否为 nil
	if options.LocalAddrIP == nil {
		return nil
	}
	// 将 IP 和端口组合成一个字符串
	ipPort := net.JoinHostPort(options.LocalAddrIP.String(), fmt.Sprint(options.LocalAddrPort))
	var ipAddr net.Addr

	switch proto {
	case UDP:
		// 如果协议是 UDP
		ipAddr, _ = net.ResolveUDPAddr("udp", ipPort)
	default:
		// 默认情况下（即 TCP）
		ipAddr, _ = net.ResolveTCPAddr("tcp", ipPort)
	}
	return ipAddr
}

// SetLocalAddrIP: 设置本地地址的 IP
//
//	@receiver options *Options:
//	@param ip string: 要设置的 IP 地址字符串
func (options *Options) SetLocalAddrIP(ip string) {
	// 将传入的 IP 字符串解析为 net.IP 类型
	options.LocalAddrIP = net.ParseIP(ip)
}

// SetLocalAddrIPFromNetInterface: 从指定的网络接口设置第一个可用的 IP 地址
//
//	@receiver options *Options:
//	@param ifaceName string: 网络接口的名称，例如 "eth0"
//	@return error error: 可能发生的错误
func (options *Options) SetLocalAddrIPFromNetInterface(ifaceName string) error {
	// 获取指定名称的网络接口
	iface, err := net.InterfaceByName(ifaceName)
	if err != nil {
		return err
	}

	// 获取该接口的所有地址
	addrs, err := iface.Addrs()
	if err != nil {
		return err
	}

	// 遍历所有地址，寻找第一个有效的 IP 地址
	for _, addr := range addrs {
		// 尝试将地址转换为 *net.IPNet 类型
		ipnetAddr, ok := addr.(*net.IPNet)
		if !ok {
			continue
		}
		// 设置找到的 IP 地址
		options.LocalAddrIP = ipnetAddr.IP
		return nil
	}
	return errors.New("no ip address found for interface")
}
