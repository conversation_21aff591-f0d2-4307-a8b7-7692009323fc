// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-16 14:50:36
// FilePath: /yaml_scan/pkg/authprovider/authx/bearer_auth_test.go
// Description: 
package authx

import (
	"net/http"
	"testing"
	"yaml_scan/pkg/retryablehttp"

	"github.com/stretchr/testify/require"
)

// TestBearerTokenAuthStrategy_Apply 测试令牌认证策略的Apply方法
// 确保它能正确设置HTTP请求的Bearer认证头
func TestBearerTokenAuthStrategy_Apply(t *testing.T) {
	// 创建测试用的密钥数据
	secret := &Secret{
		Token: "test-token-12345",
	}

	// 创建令牌认证策略
	strategy := NewBearerTokenAuthStrategy(secret)

	// 创建测试用的HTTP请求
	req, err := http.NewRequest("GET", "https://example.com", nil)
	require.NoError(t, err, "创建HTTP请求不应该返回错误")

	// 应用认证策略
	strategy.Apply(req)

	// 验证请求头是否正确设置
	authHeader := req.Header.Get("Authorization")
	require.Equal(t, "Bearer test-token-12345", authHeader, "Authorization头应该被正确设置为Bearer格式")
}

// TestBearerTokenAuthStrategy_ApplyOnRR 测试令牌认证策略的ApplyOnRR方法
// 确保它能正确设置可重试HTTP请求的Bearer认证头
func TestBearerTokenAuthStrategy_ApplyOnRR(t *testing.T) {
	// 创建测试用的密钥数据
	secret := &Secret{
		Token: "test-token-12345",
	}

	// 创建令牌认证策略
	strategy := NewBearerTokenAuthStrategy(secret)

	// 创建测试用的可重试HTTP请求
	httpReq, err := http.NewRequest("GET", "https://example.com", nil)
	require.NoError(t, err, "创建HTTP请求不应该返回错误")
	req, err := retryablehttp.FromRequest(httpReq)
	require.NoError(t, err, "创建可重试HTTP请求不应该返回错误")

	// 应用认证策略
	strategy.ApplyOnRR(req)

	// 验证请求头是否正确设置
	authHeader := req.Header.Get("Authorization")
	require.Equal(t, "Bearer test-token-12345", authHeader, "Authorization头应该被正确设置为Bearer格式")
}

// TestNewBearerTokenAuthStrategy 测试创建令牌认证策略的函数
// 确保它能正确创建BearerTokenAuthStrategy对象
func TestNewBearerTokenAuthStrategy(t *testing.T) {
	// 创建测试用的密钥数据
	secret := &Secret{
		Token: "test-token-12345",
	}

	// 创建令牌认证策略
	strategy := NewBearerTokenAuthStrategy(secret)

	// 验证策略对象是否正确创建
	require.NotNil(t, strategy, "应该创建非空的策略对象")
	require.Equal(t, secret, strategy.Data, "策略对象应该包含正确的密钥数据")
}


