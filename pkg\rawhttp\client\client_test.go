// Author: chenjb
// Version: V1.0
// Date: 2025-07-25
// FilePath: /yaml_scan/pkg/rawhttp/client/client_test.go
// Description: rawhttp客户端核心模块单元测试

package client

import (
	"bytes"
	"io"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestVersion_String 测试Version的String方法
func TestVersion_String(t *testing.T) {
	// 测试HTTP/1.0
	v10 := Version{Major: 1, Minor: 0}
	require.Equal(t, "HTTP/1.0", v10.String(), "HTTP/1.0版本字符串应该正确")
	
	// 测试HTTP/1.1
	v11 := Version{Major: 1, Minor: 1}
	require.Equal(t, "HTTP/1.1", v11.String(), "HTTP/1.1版本字符串应该正确")
	
	// 测试HTTP/2
	v2 := Version{Major: 2, Minor: 0}
	require.Equal(t, "HTTP/2", v2.String(), "HTTP/2版本字符串应该正确")
	
	// 测试HTTP/3
	v3 := Version{Major: 3, Minor: 0}
	require.Equal(t, "HTTP/3", v3.String(), "HTTP/3版本字符串应该正确")
}

// TestHTTP_Constants 测试预定义的HTTP版本常量
func TestHTTP_Constants(t *testing.T) {
	// 测试HTTP_1_0常量
	require.Equal(t, 1, HTTP_1_0.Major, "HTTP_1_0主版本号应该是1")
	require.Equal(t, 0, HTTP_1_0.Minor, "HTTP_1_0次版本号应该是0")
	require.Equal(t, "HTTP/1.0", HTTP_1_0.String(), "HTTP_1_0字符串应该正确")
	
	// 测试HTTP_1_1常量
	require.Equal(t, 1, HTTP_1_1.Major, "HTTP_1_1主版本号应该是1")
	require.Equal(t, 1, HTTP_1_1.Minor, "HTTP_1_1次版本号应该是1")
	require.Equal(t, "HTTP/1.1", HTTP_1_1.String(), "HTTP_1_1字符串应该正确")
}

// TestHeader 测试Header结构体
func TestHeader(t *testing.T) {
	header := Header{
		Key:   "Content-Type",
		Value: "application/json",
	}
	
	require.Equal(t, "Content-Type", header.Key, "头部键应该正确")
	require.Equal(t, "application/json", header.Value, "头部值应该正确")
}

// TestRequest_ContentLength 测试Request的ContentLength方法
func TestRequest_ContentLength(t *testing.T) {
	// 测试nil请求体
	req := &Request{Body: nil}
	require.Equal(t, int64(-1), req.ContentLength(), "nil请求体应该返回-1")
	
	// 测试bytes.Buffer
	buffer := bytes.NewBufferString("test content")
	req.Body = buffer
	require.Equal(t, int64(12), req.ContentLength(), "bytes.Buffer应该返回正确长度")
	
	// 测试strings.Reader
	reader := strings.NewReader("hello world")
	req.Body = reader
	require.Equal(t, int64(11), req.ContentLength(), "strings.Reader应该返回正确长度")
	
	// 测试其他类型的Reader
	req.Body = strings.NewReader("test")
	// 将其包装为不支持Len()方法的类型
	req.Body = io.NopCloser(req.Body)
	require.Equal(t, int64(-1), req.ContentLength(), "不支持的Reader类型应该返回-1")
}

// TestRequest 测试Request结构体
func TestRequest(t *testing.T) {
	// 创建完整的请求
	headers := []Header{
		{Key: "Content-Type", Value: "application/json"},
		{Key: "User-Agent", Value: "test-agent"},
	}
	
	body := strings.NewReader("request body")
	
	req := &Request{
		AutomaticContentLength: true,
		AutomaticHost:          true,
		Method:                 "POST",
		Path:                   "/api/test",
		Query:                  []string{"param=value"},
		Version:                HTTP_1_1,
		Headers:                headers,
		Body:                   body,
	}
	
	// 验证所有字段
	require.True(t, req.AutomaticContentLength, "自动Content-Length应该启用")
	require.True(t, req.AutomaticHost, "自动Host应该启用")
	require.Equal(t, "POST", req.Method, "请求方法应该正确")
	require.Equal(t, "/api/test", req.Path, "请求路径应该正确")
	require.Equal(t, []string{"param=value"}, req.Query, "查询参数应该正确")
	require.Equal(t, HTTP_1_1, req.Version, "HTTP版本应该正确")
	require.Equal(t, headers, req.Headers, "头部应该正确")
	require.Equal(t, body, req.Body, "请求体应该正确")
	require.Equal(t, int64(12), req.ContentLength(), "内容长度应该正确")
}

// TestRequest_RawBytes 测试使用原始字节的请求
func TestRequest_RawBytes(t *testing.T) {
	rawBytes := []byte("GET / HTTP/1.1\r\nHost: example.com\r\n\r\n")
	
	req := &Request{
		RawBytes: rawBytes,
	}
	
	require.Equal(t, rawBytes, req.RawBytes, "原始字节应该正确设置")
	require.Equal(t, int64(-1), req.ContentLength(), "使用原始字节时ContentLength应该返回-1")
}

// TestNewClient 测试NewClient函数
func TestNewClient(t *testing.T) {
	// 创建模拟的读写器
	mockRW := &mockReadWriter{}
	
	// 创建客户端
	client := NewClient(mockRW)
	require.NotNil(t, client, "客户端应该被创建")
	
	// 验证客户端实现了Client接口
	_, ok := client.(Client)
	require.True(t, ok, "返回的对象应该实现Client接口")
}

// TestClient_WriteRequest 测试客户端写入请求
func TestClient_WriteRequest(t *testing.T) {
	mockRW := &mockReadWriter{}
	client := NewClient(mockRW)
	
	// 测试写入原始字节请求
	rawBytes := []byte("GET / HTTP/1.1\r\nHost: example.com\r\n\r\n")
	req := &Request{RawBytes: rawBytes}
	
	err := client.WriteRequest(req)
	require.NoError(t, err, "写入原始字节请求应该成功")
	require.Equal(t, rawBytes, mockRW.written, "应该写入原始字节")
}

// TestClient_WriteRequest_Normal 测试写入普通请求
func TestClient_WriteRequest_Normal(t *testing.T) {
	mockRW := &mockReadWriter{}
	client := NewClient(mockRW)
	
	// 创建普通请求
	req := &Request{
		Method:  "GET",
		Path:    "/test",
		Version: HTTP_1_1,
		Headers: []Header{
			{Key: "Host", Value: "example.com"},
		},
	}
	
	err := client.WriteRequest(req)
	require.NoError(t, err, "写入普通请求应该成功")
	require.NotEmpty(t, mockRW.written, "应该有数据被写入")
}

// TestResponse_ContentLength 测试Response的ContentLength方法
func TestResponse_ContentLength(t *testing.T) {
	// 测试有Content-Length头部
	resp := &Response{
		Headers: []Header{
			{Key: "Content-Type", Value: "text/plain"},
			{Key: "Content-Length", Value: "123"},
		},
	}
	
	require.Equal(t, int64(123), resp.ContentLength(), "应该返回正确的Content-Length")
	
	// 测试无Content-Length头部
	resp2 := &Response{
		Headers: []Header{
			{Key: "Content-Type", Value: "text/plain"},
		},
	}
	
	require.Equal(t, int64(-1), resp2.ContentLength(), "没有Content-Length时应该返回-1")
	
	// 测试无效的Content-Length值
	resp3 := &Response{
		Headers: []Header{
			{Key: "Content-Length", Value: "invalid"},
		},
	}
	
	require.Equal(t, int64(-1), resp3.ContentLength(), "无效Content-Length应该返回-1")
}

// TestResponse_CloseRequested 测试Response的CloseRequested方法
func TestResponse_CloseRequested(t *testing.T) {
	// 测试有Connection: close头部
	resp := &Response{
		Headers: []Header{
			{Key: "Connection", Value: "close"},
		},
	}
	
	require.True(t, resp.CloseRequested(), "Connection: close应该返回true")
	
	// 测试有Connection: keep-alive头部
	resp2 := &Response{
		Headers: []Header{
			{Key: "Connection", Value: "keep-alive"},
		},
	}
	
	require.False(t, resp2.CloseRequested(), "Connection: keep-alive应该返回false")
	
	// 测试没有Connection头部
	resp3 := &Response{
		Headers: []Header{
			{Key: "Content-Type", Value: "text/plain"},
		},
	}
	
	require.False(t, resp3.CloseRequested(), "没有Connection头部应该返回false")
}

// TestResponse_TransferEncoding 测试Response的TransferEncoding方法
func TestResponse_TransferEncoding(t *testing.T) {
	// 测试chunked编码
	resp := &Response{
		Headers: []Header{
			{Key: "Transfer-Encoding", Value: "chunked"},
		},
	}
	
	require.Equal(t, "chunked", resp.TransferEncoding(), "应该返回chunked编码")
	
	// 测试identity编码
	resp2 := &Response{
		Headers: []Header{
			{Key: "Transfer-Encoding", Value: "identity"},
		},
	}
	
	require.Equal(t, "identity", resp2.TransferEncoding(), "应该返回identity编码")
	
	// 测试没有Transfer-Encoding头部
	resp3 := &Response{
		Headers: []Header{
			{Key: "Content-Type", Value: "text/plain"},
		},
	}
	
	require.Equal(t, "identity", resp3.TransferEncoding(), "默认应该返回identity编码")
	
	// 测试不支持的编码
	resp4 := &Response{
		Headers: []Header{
			{Key: "Transfer-Encoding", Value: "gzip"},
		},
	}
	
	require.Equal(t, "identity", resp4.TransferEncoding(), "不支持的编码应该返回identity")
}

// mockReadWriter 模拟读写器
type mockReadWriter struct {
	written []byte
	toRead  []byte
	readPos int
}

func (m *mockReadWriter) Write(p []byte) (n int, err error) {
	m.written = append(m.written, p...)
	return len(p), nil
}

func (m *mockReadWriter) Read(p []byte) (n int, err error) {
	if m.readPos >= len(m.toRead) {
		return 0, io.EOF
	}
	
	n = copy(p, m.toRead[m.readPos:])
	m.readPos += n
	return n, nil
}
