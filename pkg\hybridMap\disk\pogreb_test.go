// Author: chenjb
// Version: V1.0
// Date: 2025-05-16 16:02:49
// FilePath: /yaml_scan/pkg/hybridMap/disk/pogreb_test.go
// Description:
package disk

import (
	"os"
	"strconv"
	"testing"
	"time"

	"github.com/akrylysov/pogreb"
	"github.com/stretchr/testify/require"
)

// 测试前的设置：创建模拟的 PogrebDB 实现
func setupPogrebTest(t *testing.T) (*PogrebDB, string, func()) {
	// 创建临时测试目录
	tempDir, err := os.MkdirTemp("", "pogreb-test")
	require.NoError(t, err, "创建临时测试目录失败")

	// 打开 Pogreb 数据库
	db, err := pogreb.Open(tempDir, nil)
	require.NoError(t, err, "打开 Pogreb 数据库应该成功")

	// 创建 PogrebDB 实例
	pdb := &PogrebDB{db: db}

	// 返回清理函数
	cleanupFn := func() {
		pdb.Close()
		os.RemoveAll(tempDir)
	}

	return pdb, tempDir, cleanupFn
}

// TestPogrebDBBasicOperations 测试 PogrebDB 的基本操作，包括添加、获取、删除键值对
func TestPogrebDBBasicOperations(t *testing.T) {
	// 设置测试环境
	pdb, _, cleanup := setupPogrebTest(t)
	defer cleanup()

	// 1. 测试设置和获取键值对
	testKey := "test-key"
	testValue := []byte("test-value")

	// 1.1 设置键值对
	err := pdb.Set(testKey, testValue, 0) // 永不过期
	require.NoError(t, err, "设置键值对应该成功")

	// 1.2 获取键值对
	value, err := pdb.Get(testKey)
	require.NoError(t, err, "获取存在的键应该成功")
	require.Equal(t, testValue, value, "获取的值应该与设置的值相同")

	// 1.3 获取不存在的键
	_, err = pdb.Get("non-existent-key")
	require.Error(t, err, "获取不存在的键应该返回错误")

	// 2. 测试删除键
	err = pdb.Del(testKey)
	require.NoError(t, err, "删除键应该成功")

	// 2.1 验证删除后无法获取
	_, err = pdb.Get(testKey)
	require.Error(t, err, "获取已删除的键应该返回错误")

	// 3. 测试批量获取
	keys := []string{"key1", "key2", "key3"}
	values := [][]byte{[]byte("value1"), []byte("value2"), []byte("value3")}

	for i, key := range keys {
		err = pdb.Set(key, values[i], 0)
		require.NoError(t, err, "设置测试数据应该成功")
	}

	// 3.1 批量获取
	results := pdb.MGet([]string{"key1", "key2", "key3", "nonexistent-key"})
	require.Len(t, results, 4, "应该返回 4 个结果")
	require.Equal(t, []byte("value1"), results[0], "第一个值应该是 value1")
	require.Equal(t, []byte("value2"), results[1], "第二个值应该是 value2")
	require.Equal(t, []byte("value3"), results[2], "第三个值应该是 value3")
	require.Empty(t, results[3], "第四个值应该是空字节数组")
}

// TestPogrebDBExpiration 测试 PogrebDB 的键过期功能
func TestPogrebDBExpiration(t *testing.T) {
	// 设置测试环境
	pdb, _, cleanup := setupPogrebTest(t)
	defer cleanup()

	// 1. 测试永不过期的键
	err := pdb.Set("permanent-key", []byte("permanent-value"), 0)
	require.NoError(t, err, "设置永久键应该成功")

	ttl := pdb.TTL("permanent-key")
	require.Equal(t, int64(-1), ttl, "永久键的 TTL 应该是 -1")

	// 2. 测试过期的键
	// 2.1 设置一个 1 秒过期的键
	err = pdb.Set("expiring-key", []byte("expiring-value"), 1*time.Second)
	require.NoError(t, err, "设置过期键应该成功")

	// 2.2 验证 TTL 值
	ttl = pdb.TTL("expiring-key")
	require.True(t, ttl > 0 && ttl <= 1, "过期键的 TTL 应该是正数且不超过 1")

	// 2.3 等待键过期
	time.Sleep(1200 * time.Millisecond) // 等待超过 1 秒

	// 2.4 验证键已经过期
	_, err = pdb.Get("expiring-key")
	require.Error(t, err, "获取过期的键应该返回错误")

	// 2.5 验证过期键的 TTL
	ttl = pdb.TTL("expiring-key")
	require.Equal(t, int64(-1), ttl, "过期键的 TTL 应该是 -1")
}

// TestPogrebDBIncr 测试 PogrebDB 的自增操作功能
func TestPogrebDBIncr(t *testing.T) {
	// 设置测试环境
	pdb, _, cleanup := setupPogrebTest(t)
	defer cleanup()

	// 1. 测试对不存在的键进行自增
	result, err := pdb.Incr("counter", 5)
	require.NoError(t, err, "对不存在的键进行自增应该成功")
	require.Equal(t, int64(5), result, "初始自增结果应该为 5")

	// 2. 验证值已正确存储
	value, err := pdb.Get("counter")
	require.NoError(t, err, "获取计数器应该成功")
	require.Equal(t, "5", string(value), "存储的计数器值应该是 '5'")

	// 3. 再次对同一个键进行自增
	result, err = pdb.Incr("counter", 3)
	require.NoError(t, err, "自增已存在的计数器应该成功")
	require.Equal(t, int64(8), result, "第二次自增后结果应该为 8")

	// 4. 验证更新后的值
	value, err = pdb.Get("counter")
	require.NoError(t, err, "获取更新后的计数器应该成功")
	require.Equal(t, "8", string(value), "存储的计数器值应该更新为 '8'")

	// 5. 测试负值自增
	result, err = pdb.Incr("counter", -10)
	require.NoError(t, err, "负值自增应该成功")
	require.Equal(t, int64(-2), result, "负值自增后结果应该为 -2")
}

// TestPogrebDBScan 测试 PogrebDB 的扫描功能
func TestPogrebDBScan(t *testing.T) {
	// 设置测试环境
	pdb, _, cleanup := setupPogrebTest(t)
	defer cleanup()

	// 1. 准备测试数据
	testData := map[string][]byte{
		"user:1": []byte("Alice"),
		"user:2": []byte("Bob"),
		"user:3": []byte("Charlie"),
		"post:1": []byte("Hello World"),
		"post:2": []byte("Testing Pogreb"),
		"post:3": []byte("Scan Feature"),
	}

	for k, v := range testData {
		err := pdb.Set(k, v, 0)
		require.NoError(t, err, "设置测试数据应该成功")
	}

	// 2. 测试前缀扫描
	userKeys := make([]string, 0)
	userValues := make([]string, 0)

	err := pdb.Scan(ScannerOptions{
		Prefix:      "user:",
		FetchValues: true,
		Handler: func(k []byte, v []byte) error {
			userKeys = append(userKeys, string(k))
			userValues = append(userValues, string(v))
			return nil
		},
	})

	require.NoError(t, err, "前缀扫描应该成功")
	require.Len(t, userKeys, 3, "应该有 3 个用户键")
	require.Contains(t, userKeys, "user:1", "应包含 user:1")
	require.Contains(t, userKeys, "user:2", "应包含 user:2")
	require.Contains(t, userKeys, "user:3", "应包含 user:3")
	require.Contains(t, userValues, "Alice", "应包含 Alice")
	require.Contains(t, userValues, "Bob", "应包含 Bob")
	require.Contains(t, userValues, "Charlie", "应包含 Charlie")
}

// TestPogrebDBSize 测试 PogrebDB 的大小计算功能
func TestPogrebDBSize(t *testing.T) {
	// 设置测试环境
	pdb, _, cleanup := setupPogrebTest(t)
	defer cleanup()

	// 1. 检查空数据库的大小
	initialSize := pdb.Size()
	require.GreaterOrEqual(t, initialSize, int64(0), "空数据库大小应该是非负数")

	// 2. 添加数据
	for i := 0; i < 100; i++ {
		key := "key_" + strconv.Itoa(i)
		value := []byte("This is test data for size calculation")
		err := pdb.Set(key, value, 0)
		require.NoError(t, err, "设置测试数据应该成功")
	}

	// 3. 运行垃圾回收
	err := pdb.GC()
	require.NoError(t, err, "GC 应该成功")

	// 4. 检查数据添加后的大小
	newSize := pdb.Size()

	// 5. 验证数据库大小已增加
	require.Greater(t, newSize, initialSize, "数据库大小应该在添加数据后增加")
}
