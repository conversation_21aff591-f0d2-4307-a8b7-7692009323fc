package writer

import (
	"os"
	"path/filepath"
	"time"
	"testing"
	"strings"

	"yaml_scan/pkg/gologger/levels"
)



func TestNewFileWithRotation(t *testing.T) {
	// 设置测试选项
	options := &FileWithRotationOptions{
		Location:         t.TempDir(), // 使用临时目录
		Rotate:           true,
		Rotationcheck:    1 * time.Second,
		RotationInterval: 0,
		FileName:         "test.log",
		Compress:         false,
		MaxSize:          1, // 1 MB
		BackupTimeFormat: "2006-01-02_15-04-05",
		RotateEachHour:   false,
		RotateEachDay:    false,
	}

	// 创建 FileWithRotation 实例
	writer, err := NewFileWithRotation(options)
	if err != nil {
		t.Fatalf("failed to create FileWithRotation: %v", err)
	}
	defer writer.Close()

	// 检查日志文件是否创建
	logFilePath := filepath.Join(options.Location, options.FileName)
	if _, err := os.Stat(logFilePath); os.IsNotExist(err) {
		t.Fatalf("log file was not created: %s", logFilePath)
	}
}

func TestWriteLog(t *testing.T) {
	options := &FileWithRotationOptions{
		Location:         t.TempDir(),
		Rotate:           false,
		Rotationcheck:    1 * time.Second,
		RotationInterval: 0,
		FileName:         "test.log",
		Compress:         false,
		MaxSize:          1,
		BackupTimeFormat: "2006-01-02_15-04-05",
		RotateEachHour:   false,
		RotateEachDay:    false,
	}

	writer, err := NewFileWithRotation(options)
	if err != nil {
		t.Fatalf("failed to create FileWithRotation: %v", err)
	}
	defer writer.Close()

	// 写入日志
	logMessage := []byte("This is a test log message.")
	writer.Write(logMessage, levels.LevelSilent)

	// 检查日志文件内容
	content, err := os.ReadFile(filepath.Join(options.Location, options.FileName))
	if err != nil {
		t.Fatalf("failed to read log file: %v", err)
	}

	if !contains(content, logMessage) {
		t.Errorf("log file does not contain expected message: %s", logMessage)
	}
}


func TestRotateLog(t *testing.T) {
	options := &FileWithRotationOptions{
		Location:         t.TempDir(),
		Rotate:           true,
		Rotationcheck:    1 * time.Second,
		RotationInterval: 0,
		FileName:         "test.log",
		Compress:         false,
		MaxSize:          1, // 1 MB
		BackupTimeFormat: "2006-01-02_15-04-05",
		RotateEachHour:   false,
		RotateEachDay:    false,
	}

	writer, err := NewFileWithRotation(options)
	if err != nil {
		t.Fatalf("failed to create FileWithRotation: %v", err)
	}
	defer writer.Close()

	// 写入超过最大大小的日志
	for i := 0; i < 10; i++ {
		writer.Write([]byte("This is a test log message."), levels.LevelSilent)
	}

	// 等待轮换
	time.Sleep(2 * time.Second)

	// 检查是否创建了新的日志文件
	rotatedLogFilePath := filepath.Join(options.Location, "test.log")
	if _, err := os.Stat(rotatedLogFilePath); os.IsNotExist(err) {
		t.Fatalf("rotated log file was not created: %s", rotatedLogFilePath)
	}
}


func contains(content []byte, message []byte) bool {
	return strings.Contains(string(content), string(message))
}