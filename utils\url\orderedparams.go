//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-11 17:52:12
//FilePath: /yaml_scan/utils/url/orderedparams.go
//Description: url 保持参数顺序

package urlutil

import (
	"bytes"
	"strings"
	mapsutil "yaml_scan/utils/maps"
)

// OrderedParams 是一个保留元素顺序的映射
type OrderedParams struct {
	om            mapsutil.OrderedMap[string, []string] // 存储有序参数的映射
	IncludeEquals bool                                  // IncludeEquals 是否在编码参数中包含 '='，默认为 false
}

// NewOrderedParams 创建一个新的 OrderedParams 实例
func NewOrderedParams() *OrderedParams {
	return &OrderedParams{
		om: mapsutil.NewOrderedMap[string, []string](),
	}
}

// Add: 向 OrderedParams 中添加一个键及其对应的值
//
//	@receiver o *OrderedParams:
//	@param key string:
//	@param value ...string:
func (o *OrderedParams) Add(key string, value ...string) {
	// 检查键是否已经存在
	if arr, ok := o.om.Get(key); ok && len(arr) > 0 {
		// 如果键存在且有值，且提供了新值 将新值附加到现有值的后面
		if len(value) != 0 {
			o.om.Set(key, append(arr, value...))
		}
	} else {
		o.om.Set(key, value)
	}
}

// Encode: 返回编码后的参数字符串，保留参数的顺序
//
//	@receiver o *OrderedParams:
//	@return string string:
func (o *OrderedParams) Encode() string {
	if o.om.IsEmpty() {
		return ""
	}
	var buf strings.Builder
	// 遍历所有的键
	for _, k := range o.om.GetKeys() {
		// 获取当前键的值
		vs, _ := o.om.Get(k)
		// 对键进行编码
		keyEscaped := ParamEncode(k)
		for _, v := range vs {
			if buf.Len() > 0 {
				buf.WriteByte('&')
			}
			buf.WriteString(keyEscaped)
			value := ParamEncode(v)
			// 如果 IncludeEquals 为 true 或者值不为空，则添加 '=' 和编码后的值
			if o.IncludeEquals || value != "" {
				buf.WriteRune('=')
				buf.WriteString(value)
			}
		}
	}
	return buf.String()
}

// Decode: 将查询字符串解析为 OrderedParams
//
//	@receiver o *OrderedParams:
//	@param raw string:
func (o *OrderedParams) Decode(raw string) {
	// 如果 OrderedMap 为空，则初始化它
	if o.om.Len() == 0 {
		o.om = mapsutil.NewOrderedMap[string, []string]()
	}
	arr := []string{}
	var tbuff bytes.Buffer
	// 遍历原始字符串中的每个字符
	for _, v := range raw {
		switch v {
		case '&':
			arr = append(arr, tbuff.String())
			tbuff.Reset()
		case ';':
			tbuff.WriteRune(v)
		default:
			tbuff.WriteRune(v)
		}
	}
	// 如果缓冲区中还有内容，添加最后一个参数对
	if tbuff.Len() > 0 {
		arr = append(arr, tbuff.String())
	}

	// 解析参数对并添加到 OrderedParams
	for _, pair := range arr {
		d := strings.SplitN(pair, "=", 2)
		if len(d) == 2 {
			o.Add(d[0], d[1])
		} else if len(d) == 1 {
			// 添加只有键的参数，值为空
			o.Add(d[0], "")
		}
	}
}


// Clone returns a copy of the ordered params
func (o *OrderedParams) Clone() *OrderedParams {
	clone := NewOrderedParams()
	o.om.Iterate(func(key string, value []string) bool {
		// this needs to be a deep copy (from reference in nuclei race condition issue)
		if len(value) != 0 {
			clone.Add(key, value...)
		} else {
			clone.Add(key, "")
		}
		return true
	})
	return clone
}