// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 17:53:44
// FilePath: /yaml_scan/utils/reader/conn_read.go
// Description:此包提供了一系列与读取数据相关的工具函数，特别是针对网络连接的读取操作
package reader

import (
	"context"
	"errors"
	"io"
	"net"
	"os"
	"syscall"
	"time"

	contextutil "yaml_scan/utils/context"

	"github.com/docker/go-units"
)

var (
	// MaxReadSize 定义了允许读取的最大字节数，默认为8MB
	MaxReadSize, _ = units.FromHumanSize("8mb")
)

var (
	// ErrTooLarge 当尝试读取的数据超过MaxReadSize(8MB)时返回此错误
	ErrTooLarge = errors.New("reader: too large only 8MB allowed as per MaxReadSize")
)

// ConnReadN  从reader中最多读取N字节数据，专为基于连接的读取器(如net.Conn)优化
// 不应用于基于文件/缓冲区的读取。相比简单的'conn.Read()'，应优先使用ConnReadN
// 它会忽略EOF、UnexpectedEOF和超时错误
// @param ctx context.Context: 上下文对象，用于控制操作的取消和超时
// @param reader io.Reader: 实现了io.Reader接口的数据源
// @param N int64: 要读取的最大字节数，-1表示读取MaxReadSize，0表示不读取
// @return []byte []byte: 读取的数据
// @return error error: 读取过程中遇到的错误，部分特定错误会被忽略
func ConnReadN(ctx context.Context, reader io.Reader, N int64) ([]byte, error) {
	// 检查N的有效性，并处理特殊值
	if N == -1 {
		N = MaxReadSize
	} else if N < -1 {
		return nil, errors.New("reader: N cannot be less than -1")
	} else if N == 0 {
		// 如果N是0，则返回空字节数组
		return []byte{}, nil
	} else if N > MaxReadSize {
		// 如果N超过最大允许大小，则返回错误
		return nil, ErrTooLarge
	}
	var readErr error
	// 创建一个管道，用于在goroutine之间传递数据
	pr, pw := io.Pipe()

	// 当使用网络协议从连接中读取所有可用数据时，
	// 服务器发送数据后可能会出现超时错误。在这种情况下，
	// 我们应该返回数据并忽略错误(如果是超时错误)。
	// 为避免竞态条件，我们使用io.Pipe()和goroutine。

	// 启动一个goroutine来处理读取操作
	go func() {
		defer pw.Close()
		// 定义一个函数用于从reader读取数据并写入管道
		fn := func() (int64, error) {
			return io.CopyN(pw, io.LimitReader(reader, N), N)
		}
		// ExecFuncWithTwoReturns执行函数，但如果上下文完成则报错
		_, readErr = contextutil.ExecFuncWithTwoReturns(ctx, fn)
	}()

	// 从管道读取数据并返回
	bin, err2 := io.ReadAll(pr)
	if err2 != nil {
		return nil, errors.New("something went wrong while reading from pipe")
	}

	if readErr != nil {
		if IsTimeout(readErr) && len(bin) > 0 {
			// 如果错误是超时错误且已经读取了一些数据
			// 则返回数据并忽略错误
			return bin, nil
		} else if IsAcceptedError(readErr) {
			// 如果错误是可接受的错误，例如：EOF、UnexpectedEOF、连接被拒绝
			// 则返回数据并忽略错误
			return bin, nil
		} else {
			return nil, errors.New("reader: error while reading from connection")
		}
	} else {
		return bin, nil
	}
}

// ConnReadNWithTimeout 功能与ConnReadN相同，但它接受超时参数而非上下文
// 如果读取操作未能在指定时间内完成，则返回错误
// @param reader io.Reader: 实现了io.Reader接口的数据源
// @param N int64: 要读取的最大字节数
// @param after time.Duration: 读取操作的超时时间
// @return []byte []byte: 读取的数据
// @return error error: 读取过程中遇到的错误
func ConnReadNWithTimeout(reader io.Reader, N int64, after time.Duration) ([]byte, error) {
	// 创建一个带有超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), after)
	defer cancel()
	return ConnReadN(ctx, reader, N)
}

// IsAcceptedError 检查错误是否为可接受的错误类型
// 例如：连接被拒绝、io.EOF、io.ErrUnexpectedEOF等
// 在从连接读取数据时可能遇到的错误
// @param err error: 要检查的错误
// @return bool bool: 如果是可接受的错误则返回true，否则返回false
func IsAcceptedError(err error) bool {
	if err == io.EOF || err == io.ErrUnexpectedEOF {
		// 理想情况下，如果遇到超时错误我们应该报错
		// 但这对于我们的用例来说是不同的
		return true
	}
	if errors.Is(err, syscall.ECONNREFUSED) {
		// 连接被拒绝也是一种可接受的错误
		return true
	}
	return false
}

// IsTimeout 检查错误是否为超时错误
// @param err error:
// @return bool bool:
func IsTimeout(err error) bool {
	var net net.Error
	// 检查是否是网络超时错误，或者是上下文或操作系统的截止时间已过错误
	return (errors.As(err, &net) && net.Timeout()) || errors.Is(err, context.DeadlineExceeded) || errors.Is(err, os.ErrDeadlineExceeded)
}
