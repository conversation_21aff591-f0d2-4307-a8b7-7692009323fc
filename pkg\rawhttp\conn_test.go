// Author: chenjb
// Version: V1.0
// Date: 2025-07-25
// FilePath: /yaml_scan/pkg/rawhttp/conn_test.go
// Description: rawhttp连接管理模块单元测试

package rawhttp

import (
	"context"
	"crypto/tls"
	"net"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"yaml_scan/pkg/fastdialer"
	"yaml_scan/pkg/rawhttp/client"
)

// TestDialer_Interface 测试Dialer接口
func TestDialer_Interface(t *testing.T) {
	d := &dialer{}
	
	// 验证dialer实现了Dialer接口
	_, ok := interface{}(d).(Dialer)
	require.True(t, ok, "dialer应该实现Dialer接口")
}

// TestDialer_Dial 测试Dial方法
func TestDialer_Dial(t *testing.T) {
	d := &dialer{}
	options := &Options{}
	
	// 测试拨号（预期失败，因为没有真实的服务器）
	_, err := d.<PERSON>al("http", "example.com:80", options)
	require.Error(t, err, "在测试环境中拨号应该失败")
}

// TestDialer_DialTimeout 测试DialTimeout方法
func TestDialer_DialTimeout(t *testing.T) {
	d := &dialer{}
	options := &Options{}
	timeout := 1 * time.Second
	
	// 测试带超时的拨号
	_, err := d.DialTimeout("http", "example.com:80", timeout, options)
	require.Error(t, err, "在测试环境中拨号应该失败")
}

// TestDialer_DialWithProxy_InvalidURL 测试DialWithProxy方法处理无效代理URL
func TestDialer_DialWithProxy_InvalidURL(t *testing.T) {
	d := &dialer{}
	options := &Options{}
	
	// 测试无效的代理URL
	_, err := d.DialWithProxy("http", "example.com:80", "invalid-url", time.Second, options)
	require.Error(t, err, "无效代理URL应该返回错误")
	require.Contains(t, err.Error(), "unsupported proxy error", "错误信息应该包含代理错误")
}

// TestDialer_DialWithProxy_UnsupportedScheme 测试不支持的代理协议
func TestDialer_DialWithProxy_UnsupportedScheme(t *testing.T) {
	d := &dialer{}
	options := &Options{}
	
	// 测试不支持的代理协议
	_, err := d.DialWithProxy("http", "example.com:80", "ftp://proxy.example.com:8080", time.Second, options)
	require.Error(t, err, "不支持的代理协议应该返回错误")
	require.Contains(t, err.Error(), "unsupported proxy protocol", "错误信息应该包含不支持的协议")
}

// TestClientDial_HTTP 测试HTTP协议的客户端拨号
func TestClientDial_HTTP(t *testing.T) {
	options := &Options{}
	
	// 测试HTTP拨号（预期失败）
	_, err := clientDial("http", "example.com:80", 0, options)
	require.Error(t, err, "在测试环境中HTTP拨号应该失败")
}

// TestClientDial_HTTPS 测试HTTPS协议的客户端拨号
func TestClientDial_HTTPS(t *testing.T) {
	options := &Options{}
	
	// 测试HTTPS拨号（预期失败）
	_, err := clientDial("https", "example.com:443", 0, options)
	require.Error(t, err, "在测试环境中HTTPS拨号应该失败")
}

// TestClientDial_HTTPSWithSNI 测试带SNI的HTTPS拨号
func TestClientDial_HTTPSWithSNI(t *testing.T) {
	options := &Options{
		SNI: "custom.example.com",
	}
	
	// 测试带SNI的HTTPS拨号（预期失败）
	_, err := clientDial("https", "example.com:443", 0, options)
	require.Error(t, err, "在测试环境中带SNI的HTTPS拨号应该失败")
}

// TestClientDial_WithFastDialer 测试使用FastDialer的拨号
func TestClientDial_WithFastDialer(t *testing.T) {
	// 创建FastDialer
	opts := fastdialer.DefaultOptions
	fastDialer, err := fastdialer.NewDialer(opts)
	require.NoError(t, err, "创建FastDialer应该成功")
	
	options := &Options{
		FastDialer: fastDialer,
	}
	
	// 测试使用FastDialer的HTTP拨号
	_, err = clientDial("http", "example.com:80", 0, options)
	require.Error(t, err, "在测试环境中使用FastDialer的拨号应该失败")
}

// TestTlsHandshake 测试TLS握手函数
func TestTlsHandshake(t *testing.T) {
	// 创建模拟连接
	mockConn := &mockNetConn{}
	
	// 测试TLS握手（预期失败，因为是模拟连接）
	_, err := TlsHandshake(mockConn, "example.com:443", time.Second)
	require.Error(t, err, "模拟连接的TLS握手应该失败")
}

// TestTlsHandshake_NoPort 测试没有端口的地址
func TestTlsHandshake_NoPort(t *testing.T) {
	mockConn := &mockNetConn{}
	
	// 测试没有端口的地址
	_, err := TlsHandshake(mockConn, "example.com", time.Second)
	require.Error(t, err, "模拟连接的TLS握手应该失败")
}

// TestConn_Interface 测试Conn接口
func TestConn_Interface(t *testing.T) {
	mockNetConn := &mockNetConn{}
	d := &dialer{}
	
	c := &conn{
		Client: client.NewClient(mockNetConn),
		Conn:   mockNetConn,
		dialer: d,
	}
	
	// 验证conn实现了Conn接口
	_, ok := interface{}(c).(Conn)
	require.True(t, ok, "conn应该实现Conn接口")
	
	// 验证conn实现了client.Client接口
	_, ok = interface{}(c).(client.Client)
	require.True(t, ok, "conn应该实现client.Client接口")
}

// TestConn_Release 测试连接释放
func TestConn_Release(t *testing.T) {
	mockNetConn := &mockNetConn{
		remoteAddr: &mockAddr{addr: "example.com:80"},
	}
	d := &dialer{
		conns: make(map[string][]Conn),
	}
	
	c := &conn{
		Client: client.NewClient(mockNetConn),
		Conn:   mockNetConn,
		dialer: d,
	}
	
	// 测试释放连接
	c.Release()
	
	// 验证连接被添加到连接池
	require.Len(t, d.conns["example.com:80"], 1, "连接应该被添加到连接池")
	require.Equal(t, c, d.conns["example.com:80"][0], "连接池中应该包含正确的连接")
}

// mockNetConn 模拟网络连接
type mockNetConn struct {
	remoteAddr net.Addr
}

func (m *mockNetConn) Read(b []byte) (n int, err error) {
	return 0, net.ErrClosed
}

func (m *mockNetConn) Write(b []byte) (n int, err error) {
	return 0, net.ErrClosed
}

func (m *mockNetConn) Close() error {
	return nil
}

func (m *mockNetConn) LocalAddr() net.Addr {
	return &mockAddr{addr: "127.0.0.1:0"}
}

func (m *mockNetConn) RemoteAddr() net.Addr {
	if m.remoteAddr != nil {
		return m.remoteAddr
	}
	return &mockAddr{addr: "example.com:80"}
}

func (m *mockNetConn) SetDeadline(t time.Time) error {
	return nil
}

func (m *mockNetConn) SetReadDeadline(t time.Time) error {
	return nil
}

func (m *mockNetConn) SetWriteDeadline(t time.Time) error {
	return nil
}

// mockAddr 模拟网络地址
type mockAddr struct {
	addr string
}

func (m *mockAddr) Network() string {
	return "tcp"
}

func (m *mockAddr) String() string {
	return m.addr
}
