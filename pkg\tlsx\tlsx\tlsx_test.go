// Author: chenjb
// Version: V1.0
// Date: 2025-06-26 19:26:33
// FilePath: /yaml_scan/pkg/tlsx/tlsx/tlsx_test.go
// Description:
package tlsx

import (
	"testing"
	"yaml_scan/pkg/fastdialer"
	"yaml_scan/pkg/tlsx/tlsx/clients"

	"github.com/stretchr/testify/require"
)

// TestNew 测试Service的创建功能
// 验证不同扫描模式下Service实例的正确创建
func TestNew(t *testing.T) {
	tests := []struct {
		name        string           // 测试用例名称
		options     *clients.Options // 输入的配置选项
		expectError bool             // 是否期望出现错误
		description string           // 测试用例描述
	}{
		{
			name: "默认配置创建服务",
			options: &clients.Options{
				ScanMode: "ctls",
				Timeout:  5,
				Retries:  3,
			},
			expectError: false,
			description: "使用默认ctls模式创建服务应该成功",
		},
		{
			name: "ztls模式创建服务",
			options: &clients.Options{
				ScanMode: "ztls",
				Timeout:  5,
				Retries:  3,
			},
			expectError: false,
			description: "使用ztls模式创建服务应该成功",
		},
		{
			name: "auto模式创建服务",
			options: &clients.Options{
				ScanMode: "auto",
				Timeout:  5,
				Retries:  3,
			},
			expectError: false,
			description: "使用auto模式创建服务应该成功",
		},
		{
			name: "openssl模式创建服务",
			options: &clients.Options{
				ScanMode: "openssl",
				Timeout:  5,
				Retries:  3,
			},
			expectError: false,
			description: "使用openssl模式创建服务，可能因为系统未安装openssl而失败",
		},
		{
			name: "未知模式默认为ctls",
			options: &clients.Options{
				ScanMode: "unknown",
				Timeout:  5,
				Retries:  3,
			},
			expectError: false,
			description: "未知扫描模式应该默认使用ctls模式",
		},
		{
			name: "空扫描模式默认为ctls",
			options: &clients.Options{
				ScanMode: "",
				Timeout:  5,
				Retries:  3,
			},
			expectError: false,
			description: "空扫描模式应该默认使用ctls模式",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行Service创建
			service, err := New(tt.options)

			if tt.expectError {
				// 期望出现错误的情况
				require.Error(t, err, "应该返回错误: %s", tt.description)
				require.Nil(t, service, "出错时service应该为nil")
			} else {
				// 期望成功的情况
				if err != nil {
					// 对于openssl模式，如果系统未安装openssl，允许失败
					if tt.options.ScanMode == "openssl" {
						t.Logf("OpenSSL模式创建失败（可能系统未安装OpenSSL）: %v", err)
						return
					}
					require.NoError(t, err, "不应该返回错误: %s", tt.description)
				}
				require.NotNil(t, service, "service不应该为nil")
				require.NotNil(t, service.options, "service.options不应该为nil")
				require.NotNil(t, service.client, "service.client不应该为nil")
				require.NotNil(t, service.options.Fastdialer, "Fastdialer应该被自动创建")

				// 验证扫描模式是否正确设置
				if tt.options.ScanMode == "unknown" || tt.options.ScanMode == "" {
					require.Equal(t, "ctls", service.options.ScanMode, "未知或空模式应该设置为ctls")
				} else {
					require.Equal(t, tt.options.ScanMode, service.options.ScanMode, "扫描模式应该匹配")
				}
			}
		})
	}
}

// TestNewWithCustomDialer 测试使用自定义拨号器创建Service
// 验证当提供自定义Fastdialer时不会被覆盖
func TestNewWithCustomDialer(t *testing.T) {
	// 创建自定义拨号器
	customDialer, err := fastdialer.NewDialer(fastdialer.DefaultOptions)
	require.NoError(t, err, "创建自定义拨号器不应该出错")

	options := &clients.Options{
		ScanMode:   "ctls",
		Timeout:    5,
		Retries:    3,
		Fastdialer: customDialer,
	}

	// 创建服务
	service, err := New(options)
	require.NoError(t, err, "使用自定义拨号器创建服务不应该出错")
	require.NotNil(t, service, "service不应该为nil")

	// 验证拨号器没有被替换
	require.Equal(t, customDialer, service.options.Fastdialer, "自定义拨号器不应该被替换")
}

// TestConnect 测试基本连接功能
// 使用真实的网络地址进行连接测试
func TestConnect(t *testing.T) {
	// 创建服务实例
	service, err := New(&clients.Options{
		ScanMode:   "ctls",
		Timeout:    10,
		Retries:    2,
		ScanAllIPs: true,
	})
	require.NoError(t, err, "创建服务不应该出错")

	tests := []struct {
		name        string // 测试用例名称
		host        string // 目标主机
		ip          string // 目标IP
		port        string // 目标端口
		expectError bool   // 是否期望出现错误
		description string // 测试用例描述
	}{
		{
			name:        "连接百度HTTPS",
			host:        "www.baidu.com",
			ip:          "",
			port:        "443",
			expectError: false,
			description: "连接百度的HTTPS服务应该成功",
		},
		{
			name:        "连接指定IP的HTTP",
			host:        "www.baidu.com",
			ip:          "*************",
			port:        "443",
			expectError: false,
			description: "直接连接IP地址的HTTP服务应该成功",
		},
		{
			name:        "无效的主机和IP",
			host:        "",
			ip:          "",
			port:        "443",
			expectError: true,
			description: "主机和IP都为空应该返回错误",
		},
		{
			name:        "无效的端口",
			host:        "www.baidu.com",
			ip:          "",
			port:        "",
			expectError: true,
			description: "端口为空应该返回错误",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行连接
			response, err := service.Connect(tt.host, tt.ip, tt.port)

			if tt.expectError {
				// 期望出现错误的情况
				require.Error(t, err, "应该返回错误: %s", tt.description)
			} else {
				// 期望成功的情况
				require.NoError(t, err, "不应该返回错误: %s", tt.description)
				require.NotNil(t, response, "响应不应该为nil")
				require.Equal(t, tt.port, response.Port, "端口应该匹配")

				if tt.host != "" {
					require.Equal(t, tt.host, response.Host, "主机名应该匹配")
				}

				// 验证TLS版本不为空
				require.NotEmpty(t, response.Version, "TLS版本不应该为空")

				// 验证密码套件不为空
				require.NotEmpty(t, response.Cipher, "密码套件不应该为空")

				t.Logf("连接成功 - TLS版本: %s, 密码套件: %s", response.Version, response.Cipher)
			}
		})
	}
}

// TestConnectWithOptions 测试带选项的连接功能
// 验证各种连接选项的正确处理
func TestConnectWithOptions(t *testing.T) {
	// 创建服务实例
	service, err := New(&clients.Options{
		ScanMode: "ctls",
		Timeout:  10,
		Retries:  2,
	})
	require.NoError(t, err, "创建服务不应该出错")

	tests := []struct {
		name        string                 // 测试用例名称
		host        string                 // 目标主机
		ip          string                 // 目标IP
		port        string                 // 目标端口
		options     clients.ConnectOptions // 连接选项
		expectError bool                   // 是否期望出现错误
		description string                 // 测试用例描述
	}{
		{
			name: "使用SNI连接",
			host: "www.baidu.com",
			ip:   "",
			port: "443",
			options: clients.ConnectOptions{
				SNI: "www.baidu.com",
			},
			expectError: false,
			description: "使用SNI连接应该成功",
		},
		{
			name: "指定TLS版本连接",
			host: "www.baidu.com",
			ip:   "",
			port: "443",
			options: clients.ConnectOptions{
				VersionTLS: "tls12",
			},
			expectError: false,
			description: "指定TLS 1.2版本连接应该成功",
		},
		{
			name:        "空选项连接",
			host:        "www.baidu.com",
			ip:          "",
			port:        "443",
			options:     clients.ConnectOptions{},
			expectError: false,
			description: "使用空选项连接应该成功",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行带选项的连接
			response, err := service.ConnectWithOptions(tt.host, tt.ip, tt.port, tt.options)

			if tt.expectError {
				// 期望出现错误的情况
				require.Error(t, err, "应该返回错误: %s", tt.description)
			} else {
				// 期望成功的情况
				require.NoError(t, err, "不应该返回错误: %s", tt.description)
				require.NotNil(t, response, "响应不应该为nil")
				require.Equal(t, tt.port, response.Port, "端口应该匹配")
				require.Equal(t, tt.host, response.Host, "主机名应该匹配")

				// 如果指定了TLS版本，验证返回的版本
				if tt.options.VersionTLS != "" {
					require.Equal(t, tt.options.VersionTLS, response.Version, "TLS版本应该匹配")
				}

				// 如果指定了SNI，验证返回的SNI
				if tt.options.SNI != "" {
					require.Equal(t, tt.options.SNI, response.ServerName, "SNI应该匹配")
				}

				t.Logf("连接成功 - TLS版本: %s, 密码套件: %s, SNI: %s",
					response.Version, response.Cipher, response.ServerName)
			}
		})
	}
}

// TestConnectWithJARM 测试JARM指纹识别功能
// 验证JARM哈希的正确计算
func TestConnectWithJARM(t *testing.T) {
	// 创建启用JARM的服务实例
	service, err := New(&clients.Options{
		ScanMode: "ctls",
		Timeout:  15,
		Retries:  2,
		Jarm:     true, // 启用JARM指纹识别
	})
	require.NoError(t, err, "创建服务不应该出错")

	// 测试JARM指纹识别
	response, err := service.Connect("www.baidu.com", "", "443")
	require.NoError(t, err, "JARM连接不应该出错")
	require.NotNil(t, response, "响应不应该为nil")

	// 验证JARM哈希
	require.NotEmpty(t, response.JarmHash, "JARM哈希不应该为空")
	require.Len(t, response.JarmHash, 62, "JARM哈希应该是62个字符")

	t.Logf("JARM哈希: %s", response.JarmHash)
}

// TestConnectWithVersionEnum 测试TLS版本枚举功能
// 验证服务器支持的TLS版本的正确枚举
func TestConnectWithVersionEnum(t *testing.T) {
	// 创建启用版本枚举的服务实例
	service, err := New(&clients.Options{
		ScanMode:        "openssl",
		Timeout:         15,
		Retries:         2,
		TlsVersionsEnum: true, // 启用TLS版本枚举
		IPVersion:       []string{"4"},
	})
	require.NoError(t, err, "创建服务不应该出错")

	// 测试TLS版本枚举
	response, err := service.Connect("", "*************", "443")
	require.NoError(t, err, "版本枚举连接不应该出错")
	require.NotNil(t, response, "响应不应该为nil")

	// 验证版本枚举结果
	require.NotEmpty(t, response.VersionEnum, "版本枚举结果不应该为空")
	require.Contains(t, response.VersionEnum, response.Version, "枚举结果应该包含当前连接的版本")

	t.Logf("支持的TLS版本: %v", response.VersionEnum)
}

// TestConnectWithCipherEnum 测试密码套件枚举功能
// 验证服务器支持的密码套件的正确枚举
func TestConnectWithCipherEnum(t *testing.T) {
	// 创建启用密码套件枚举的服务实例
	service, err := New(&clients.Options{
		ScanMode:        "ctls",
		Timeout:         20,
		Retries:         2,
		TlsVersionsEnum: true,            // 需要先枚举版本
		TlsCiphersEnum:  true,            // 启用密码套件枚举
		TLsCipherLevel:  []string{"all"}, // 枚举所有级别的密码套件
	})
	require.NoError(t, err, "创建服务不应该出错")

	// 测试密码套件枚举
	response, err := service.Connect("www.baidu.com", "", "443")
	require.NoError(t, err, "密码套件枚举连接不应该出错")
	require.NotNil(t, response, "响应不应该为nil")

	// 验证密码套件枚举结果
	require.NotEmpty(t, response.TlsCiphers, "密码套件枚举结果不应该为空")

	// 验证每个版本的密码套件
	for _, tlsCipher := range response.TlsCiphers {
		require.NotEmpty(t, tlsCipher.Version, "TLS版本不应该为空")

		// 至少应该有一种类型的密码套件
		totalCiphers := len(tlsCipher.Ciphers.Secure) +
			len(tlsCipher.Ciphers.Weak) +
			len(tlsCipher.Ciphers.Insecure) +
			len(tlsCipher.Ciphers.Unknown)
		require.Greater(t, totalCiphers, 0, "每个版本至少应该有一个密码套件")

		t.Logf("TLS %s 支持的密码套件 - 安全: %d, 弱: %d, 不安全: %d, 未知: %d",
			tlsCipher.Version,
			len(tlsCipher.Ciphers.Secure),
			len(tlsCipher.Ciphers.Weak),
			len(tlsCipher.Ciphers.Insecure),
			len(tlsCipher.Ciphers.Unknown))
	}
}


// TestEnumTlsVersions 测试TLS版本枚举内部方法
// 验证enumTlsVersions方法的正确性
func TestEnumTlsVersions(t *testing.T) {
	// 创建服务实例
	service, err := New(&clients.Options{
		ScanMode: "ctls",
		Timeout:  15,
		Retries:  2,
	})
	require.NoError(t, err, "创建服务不应该出错")

	tests := []struct {
		name        string                  // 测试用例名称
		host        string                  // 目标主机
		ip          string                  // 目标IP
		port        string                  // 目标端口
		options     clients.ConnectOptions  // 连接选项
		expectError bool                    // 是否期望出现错误
		description string                  // 测试用例描述
	}{
		{
			name: "枚举百度支持的TLS版本",
			host: "www.baidu.com",
			ip:   "",
			port: "443",
			options: clients.ConnectOptions{
				EnumMode: clients.Version,
			},
			expectError: false,
			description: "应该能够枚举百度支持的TLS版本",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行TLS版本枚举
			versions, err := service.enumTlsVersions(tt.host, tt.ip, tt.port, tt.options)

			if tt.expectError {
				require.Error(t, err, "应该返回错误: %s", tt.description)
			} else {
				require.NoError(t, err, "不应该返回错误: %s", tt.description)
				require.NotNil(t, versions, "版本列表不应该为nil")

				if len(versions) > 0 {
					t.Logf("枚举到的TLS版本: %v", versions)

					// 验证版本格式
					for _, version := range versions {
						require.NotEmpty(t, version, "TLS版本不应该为空")
					}
				} else {
					t.Logf("未枚举到TLS版本（可能是连接失败）")
				}
			}
		})
	}
}

// TestEnumTlsCiphers 测试密码套件枚举内部方法
// 验证enumTlsCiphers方法的正确性
func TestEnumTlsCiphers(t *testing.T) {
	// 创建服务实例
	service, err := New(&clients.Options{
		ScanMode:       "ctls",
		Timeout:        20,
		Retries:        2,
		TLsCipherLevel: []string{"all"}, // 设置密码套件级别
	})
	require.NoError(t, err, "创建服务不应该出错")

	tests := []struct {
		name        string                  // 测试用例名称
		host        string                  // 目标主机
		ip          string                  // 目标IP
		port        string                  // 目标端口
		options     clients.ConnectOptions  // 连接选项
		expectError bool                    // 是否期望出现错误
		description string                  // 测试用例描述
	}{
		{
			name: "枚举TLS 1.2密码套件",
			host: "www.baidu.com",
			ip:   "",
			port: "443",
			options: clients.ConnectOptions{
				EnumMode:   clients.Cipher,
				VersionTLS: "tls12",
			},
			expectError: false,
			description: "应该能够枚举TLS 1.2的密码套件",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行密码套件枚举
			ciphers, err := service.enumTlsCiphers(tt.host, tt.ip, tt.port, tt.options)

			if tt.expectError {
				require.Error(t, err, "应该返回错误: %s", tt.description)
			} else {
				require.NoError(t, err, "不应该返回错误: %s", tt.description)
				require.NotNil(t, ciphers, "密码套件列表不应该为nil")

				if len(ciphers) > 0 {
					t.Logf("枚举到 %d 个密码套件", len(ciphers))

					// 验证密码套件格式
					for _, cipher := range ciphers {
						require.NotEmpty(t, cipher, "密码套件名称不应该为空")
					}

					// 显示前几个密码套件作为示例
					maxShow := 3
					if len(ciphers) < maxShow {
						maxShow = len(ciphers)
					}
					t.Logf("示例密码套件: %v", ciphers[:maxShow])
				} else {
					t.Logf("未枚举到密码套件（可能是版本不支持或连接失败）")
				}
			}
		})
	}
}