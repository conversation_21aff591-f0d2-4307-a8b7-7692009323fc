// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 16:42:46
// FilePath: /yaml_scan/pkg/retryablehttp/retry.go
// Description:实现HTTP请求重试策略，定义了决定何时进行重试的相关函数和错误处理机制
package retryablehttp

import (
	"context"
	"crypto/x509"
	"net/http"
	"net/url"
	"regexp"
	"yaml_scan/utils/errkit"
)

var (
		// redirectsErrorRegex 是用于匹配net/http在重定向次数耗尽时返回的错误的正则表达式
	redirectsErrorRegex = regexp.MustCompile(`stopped after \d+ redirects\z`)

	// schemeErrorRegex 是用于匹配net/http在URL指定的协议无效时返回的错误的正则表达式
	schemeErrorRegex = regexp.MustCompile(`unsupported protocol scheme`)
)

// CheckRetry 指定处理重试的策略
//
// 它在每个请求之后被调用，参数为http.Client返回的响应和错误值
// 如果CheckRetry返回false，Client停止重试并将响应返回给调用者
// 如果CheckRetry返回错误，那么该错误值将替代请求的错误返回
// Client在重试时会关闭任何响应体，但如果重试被中止，
// CheckRetry回调有责任在返回之前正确关闭任何响应体
//
// 参数:
//   - ctx: 请求上下文
//   - resp: HTTP响应
//   - err: 请求过程中可能发生的错误
//
// 返回:
//   - bool: 如果应该重试，则为true
//   - error: 如果有错误需要报告，则返回该错误
type CheckRetry func(ctx context.Context, resp *http.Response, err error) (bool, error)


// DefaultRetryPolicy  提供Client.CheckRetry的默认回调函数，
// 它将在连接错误和服务器错误时进行重试
// @return ctx context.Context: 
// @return resp *http.Response: 
// @return err error: 
// @return func(ctx context.Context, resp *http.Response, err error) (bool, error) func(ctx context.Context, resp *http.Response, err error) (bool, error): 
func DefaultRetryPolicy() func(ctx context.Context, resp *http.Response, err error) (bool, error) {
	return func(ctx context.Context, resp *http.Response, err error) (bool, error) {
		return CheckRecoverableErrors(ctx, resp, err)
	}
}


// CheckRecoverableErrors 检查是否为可恢复的错误
// @param ctx context.Context: 请求上下文
// @param resp *http.Response: HTTP响应
// @param err error: 可能的错误
// @return bool bool: 如果错误被认为是可恢复的，返回true表示应该重试
// @return error error: 如果有错误需要报告，则返回该错误
func CheckRecoverableErrors(ctx context.Context, resp *http.Response, err error) (bool, error) {
	// 不重试上下文已取消或超时的情况
	if ctx.Err() != nil {
		return false, ctx.Err()
	}

	if err != nil {
		if v, ok := err.(*url.Error); ok {
			// 如果错误是因为重定向次数过多，不重试
			if redirectsErrorRegex.MatchString(v.Error()) {
				return false, nil
			}

			// 如果错误是因为协议方案无效，不重试.
			if schemeErrorRegex.MatchString(v.Error()) {
				return false, nil
			}

			// 如果错误是因为TLS证书验证失败，不重试
			// 这通常表示服务器证书不受信任，重试不会解决问题
			if _, ok := v.Err.(x509.UnknownAuthorityError); ok {
				return false, nil
			}
		}
		// 检查是否为永久性网络错误
		if errkit.IsKind(err, errkit.ErrKindNetworkPermanent) {
			// 不重试永久性网络错误
			return false, err
		}
		// 该错误可能是可恢复的，所以重试
		return true, nil
	}
	return false, nil
}

// HostSprayRetryPolicy 提供一个Client.CheckRetry的回调函数，
// 它将在连接错误和服务器错误时进行重试
// @return ctx context.Context: 
// @return resp *http.Response: 
// @return err error: 
// @return func(ctx context.Context, resp *http.Response, err error) (bool, error) func(ctx context.Context, resp *http.Response, err error) (bool, error): 
func HostSprayRetryPolicy() func(ctx context.Context, resp *http.Response, err error) (bool, error) {
	return func(ctx context.Context, resp *http.Response, err error) (bool, error) {
		return CheckRecoverableErrors(ctx, resp, err)
	}
}