// Author: chenjb
// Version: V1.0
// Date: 2025-06-05 19:46:13
// FilePath: /yaml_scan/pkg/cidr/ip_test.go
// Description:
package cidr

import (
	"math/big"
	"net"
	"reflect"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestCountIPsInCIDR 测试计算CIDR中IP地址数量的函数
func TestCountIPsInCIDR(t *testing.T) {
	// 测试用例
	tests := []struct {
		name             string   // 测试名称
		includeBase      bool     // 是否包括网络地址
		includeBroadcast bool     // 是否包括广播地址
		cidr             string   // CIDR字符串
		expected         *big.Int // 期望的IP地址数量
		wantErr          bool     // 是否期望出错
	}{
		{
			name:             "IPv4 /24 包括网络地址和广播地址",
			includeBase:      true,
			includeBroadcast: true,
			cidr:             "***********/24",
			expected:         big.NewInt(256),
			wantErr:          false,
		},
		{
			name:             "IPv4 /24 不包括网络地址和广播地址",
			includeBase:      false,
			includeBroadcast: false,
			cidr:             "***********/24",
			expected:         big.NewInt(254),
			wantErr:          false,
		},
		{
			name:             "IPv4 /24 只包括网络地址",
			includeBase:      true,
			includeBroadcast: false,
			cidr:             "***********/24",
			expected:         big.NewInt(255),
			wantErr:          false,
		},
		{
			name:             "IPv4 /24 只包括广播地址",
			includeBase:      false,
			includeBroadcast: true,
			cidr:             "***********/24",
			expected:         big.NewInt(255),
			wantErr:          false,
		},
		{
			name:             "IPv4 /32 单个地址",
			includeBase:      true,
			includeBroadcast: true,
			cidr:             "***********/32",
			expected:         big.NewInt(1),
			wantErr:          false,
		},
		{
			name:             "IPv6 /120 包括网络地址和广播地址",
			includeBase:      true,
			includeBroadcast: true,
			cidr:             "2001:db8::/120",
			expected:         big.NewInt(256),
			wantErr:          false,
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 解析CIDR
			_, ipnet, err := net.ParseCIDR(tt.cidr)
			require.NoError(t, err, "解析CIDR出错")

			// 调用被测试函数
			result := CountIPsInCIDR(tt.includeBase, tt.includeBroadcast, ipnet)

			// 验证结果
			require.Equal(t, tt.expected, result, "IP地址数量计算错误")
		})
	}
}

// TestRemoveCIDRs 测试从CIDR列表中移除指定CIDR的函数
func TestRemoveCIDRs(t *testing.T) {
	// 创建测试用例
	tests := []struct {
		name       string   // 测试名称
		allowList  []string // 允许的CIDR列表
		removeList []string // 需要移除的CIDR列表
		expected   []string // 期望的结果CIDR列表
		wantErr    bool     // 是否期望出错
	}{
		{
			name:       "从大范围中移除小范围",
			allowList:  []string{"10.0.0.0/8"},
			removeList: []string{"********/16"},
			expected: []string{
				"**********/9",
				"*********/10",
				"*********/11",
				"*********/12",
				"********/13",
				"********/14",
				"********/15",
				"10.0.0.0/16",
			},
			wantErr: false,
		},
		{
			name:       "移除不相交的CIDR",
			allowList:  []string{"***********/24"},
			removeList: []string{"10.0.0.0/8"},
			expected:   []string{"***********/24"},
			wantErr:    false,
		},
		{
			name:       "IPv4和IPv6混合错误",
			allowList:  []string{"***********/24"},
			removeList: []string{"2001:db8::/64"},
			expected:   nil,
			wantErr:    true,
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 转换允许列表字符串为IPNet对象
			var allowIPNets []*net.IPNet
			for _, cidr := range tt.allowList {
				_, ipnet, err := net.ParseCIDR(cidr)
				require.NoError(t, err, "解析允许CIDR列表出错")
				allowIPNets = append(allowIPNets, ipnet)
			}

			// 转换移除列表字符串为IPNet对象
			var removeIPNets []*net.IPNet
			for _, cidr := range tt.removeList {
				_, ipnet, err := net.ParseCIDR(cidr)
				require.NoError(t, err, "解析移除CIDR列表出错")
				removeIPNets = append(removeIPNets, ipnet)
			}

			// 调用被测试函数
			result, err := RemoveCIDRs(allowIPNets, removeIPNets)

			// 检查错误
			if tt.wantErr {
				require.Error(t, err, "期望出错但没有错误")
				return
			}
			require.NoError(t, err, "不期望出错但出错了")

			// 检查结果数量
			require.Equal(t, len(tt.expected), len(result), "结果CIDR数量不匹配")

			// 转换期望结果为IPNet进行比较
			var expectedIPNets []*net.IPNet
			for _, cidr := range tt.expected {
				_, ipnet, err := net.ParseCIDR(cidr)
				require.NoError(t, err, "解析期望CIDR列表出错")
				expectedIPNets = append(expectedIPNets, ipnet)
			}

			// 验证结果包含所有期望的CIDR
			for _, expectedNet := range expectedIPNets {
				found := false
				for _, resultNet := range result {
					if expectedNet.String() == resultNet.String() {
						found = true
						break
					}
				}
				require.True(t, found, "期望的CIDR %s 在结果中未找到", expectedNet.String())
			}
		})
	}
}

// TestGetNextIP 测试获取下一个IP地址的函数
func TestGetNextIP(t *testing.T) {
	// 测试用例
	tests := []struct {
		name     string // 测试名称
		ip       string // 输入IP地址
		expected string // 期望的下一个IP地址
	}{
		{
			name:     "IPv4普通地址",
			ip:       "***********",
			expected: "***********",
		},
		{
			name:     "IPv4地址最大值循环",
			ip:       "***********55",
			expected: "***********",
		},
		{
			name:     "IPv4最大地址",
			ip:       "***************",
			expected: "***************", // 最大值不变
		},
		{
			name:     "IPv6普通地址",
			ip:       "2001:db8::1",
			expected: "2001:db8::2",
		},
		{
			name:     "IPv6地址部分最大值循环",
			ip:       "2001:db8::ffff",
			expected: "2001:db8::1:0",
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 解析IP地址
			ip := net.ParseIP(tt.ip)
			require.NotNil(t, ip, "解析IP地址出错")

			// 调用被测试函数
			nextIP := GetNextIP(ip)

			// 验证结果
			expected := net.ParseIP(tt.expected)
			require.Equal(t, expected.String(), nextIP.String(), "下一个IP地址计算错误")
		})
	}
}

// TestGetPreviousIP 测试获取上一个IP地址的函数
func TestGetPreviousIP(t *testing.T) {
	// 测试用例
	tests := []struct {
		name     string // 测试名称
		ip       string // 输入IP地址
		expected string // 期望的上一个IP地址
	}{
		{
			name:     "IPv4普通地址",
			ip:       "***********",
			expected: "***********",
		},
		{
			name:     "IPv4地址最小值循环",
			ip:       "***********",
			expected: "***********55",
		},
		{
			name:     "IPv4最小地址",
			ip:       "0.0.0.0",
			expected: "0.0.0.0", // 最小值不变
		},
		{
			name:     "IPv6普通地址",
			ip:       "2001:db8::2",
			expected: "2001:db8::1",
		},
		{
			name:     "IPv6地址部分最小值循环",
			ip:       "2001:db8::1:0",
			expected: "2001:db8::ffff",
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 解析IP地址
			ip := net.ParseIP(tt.ip)
			require.NotNil(t, ip, "解析IP地址出错")

			// 调用被测试函数
			prevIP := getPreviousIP(ip)

			// 验证结果
			expected := net.ParseIP(tt.expected)
			require.Equal(t, expected.String(), prevIP.String(), "上一个IP地址计算错误")
		})
	}
}

// TestKeepUniqueIPs 测试去除重复IP地址的函数
func TestKeepUniqueIPs(t *testing.T) {
	// 测试用例
	tests := []struct {
		name     string   // 测试名称
		ips      []string // 输入IP地址列表
		expected []string // 期望的唯一IP地址列表
	}{
		{
			name:     "无重复IP",
			ips:      []string{"***********", "***********", "***********"},
			expected: []string{"***********", "***********", "***********"},
		},
		{
			name:     "有重复IP",
			ips:      []string{"***********", "***********", "***********", "***********"},
			expected: []string{"***********", "***********", "***********"},
		},
		{
			name:     "混合IPv4和IPv6",
			ips:      []string{"***********", "2001:db8::1", "***********", "2001:db8::1"},
			expected: []string{"***********", "2001:db8::1"},
		},
		{
			name:     "空列表",
			ips:      []string{},
			expected: []string{},
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 转换字符串IP为net.IP对象
			var ips []net.IP
			for _, ipStr := range tt.ips {
				ip := net.ParseIP(ipStr)
				require.NotNil(t, ip, "解析IP地址出错")
				ips = append(ips, ip)
			}

			// 调用被测试函数
			result := KeepUniqueIPs(ips)

			// 验证结果
			require.Equal(t, len(tt.expected), len(result), "唯一IP地址数量不匹配")

			// 验证每个IP地址是否在期望列表中
			for i, expectedIPStr := range tt.expected {
				expectedIP := net.ParseIP(expectedIPStr)
				require.True(t, expectedIP.Equal(result[i]), "唯一IP地址不匹配")
			}
		})
	}
}

// TestIsExcluded 测试IP地址是否被排除的函数
func TestIsExcluded(t *testing.T) {
	// 测试用例
	tests := []struct {
		name        string   // 测试名称
		excludeList []string // 排除IP地址列表
		ip          string   // 要检查的IP地址
		expected    bool     // 期望结果
	}{
		{
			name:        "IP在排除列表中",
			excludeList: []string{"***********", "***********"},
			ip:          "***********",
			expected:    true,
		},
		{
			name:        "IP不在排除列表中",
			excludeList: []string{"***********", "***********"},
			ip:          "***********",
			expected:    false,
		},
		{
			name:        "IPv6地址在排除列表中",
			excludeList: []string{"2001:db8::1", "2001:db8::2"},
			ip:          "2001:db8::1",
			expected:    true,
		},
		{
			name:        "空排除列表",
			excludeList: []string{},
			ip:          "***********",
			expected:    false,
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 转换字符串IP为net.IP对象
			var excludeList []net.IP
			for _, ipStr := range tt.excludeList {
				ip := net.ParseIP(ipStr)
				require.NotNil(t, ip, "解析排除IP地址出错")
				excludeList = append(excludeList, ip)
			}

			// 解析要检查的IP
			ip := net.ParseIP(tt.ip)
			require.NotNil(t, ip, "解析检查IP地址出错")

			// 调用被测试函数
			result := IsExcluded(excludeList, ip)

			// 验证结果
			require.Equal(t, tt.expected, result, "IP排除检查错误")
		})
	}
}

// TestIPToPrefix 测试将IP地址转换为CIDR前缀的函数
func TestIPToPrefix(t *testing.T) {
	// 测试用例
	tests := []struct {
		name         string // 测试名称
		ip           string // 输入IP地址
		expectedCIDR string // 期望的CIDR前缀
	}{
		{
			name:         "IPv4地址",
			ip:           "***********",
			expectedCIDR: "***********/32",
		},
		{
			name:         "IPv6地址",
			ip:           "2001:db8::1",
			expectedCIDR: "2001:db8::1/128",
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 解析IP地址
			ip := net.ParseIP(tt.ip)
			require.NotNil(t, ip, "解析IP地址出错")

			// 调用被测试函数
			result := IPToPrefix(ip)

			// 验证结果
			require.Equal(t, tt.expectedCIDR, result.String(), "IP到CIDR前缀转换错误")
		})
	}
}


// TestFmtIp6 测试将IP格式化为IPv6表示形式的函数
func TestFmtIp6(t *testing.T) {
	// 测试用例
	tests := []struct {
		name     string // 测试名称
		ip       string // 输入IP地址
		short    bool   // 是否使用简短格式
		expected string // 期望的IPv6格式
		wantErr  bool   // 是否期望出错
	}{
		{
			name:     "IPv4地址使用长格式",
			ip:       "***********",
			short:    false,
			expected: "00:00:00:00:00:ffff:c0a8:0101",
			wantErr:  false,
		},
		{
			name:     "IPv4地址使用短格式",
			ip:       "***********",
			short:    true,
			expected: "::ffff:c0a8:0101",
			wantErr:  false,
		},
		{
			name:     "IPv6地址",
			ip:       "2001:db8::1",
			short:    true,
			expected: "2001:db8::1",
			wantErr:  false,
		},
		{
			name:     "无效IP",
			ip:       "invalid",
			short:    true,
			expected: "",
			wantErr:  true,
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 解析IP地址
			ip := net.ParseIP(tt.ip)
			if tt.wantErr {
				require.Nil(t, ip, "预期解析IP地址出错但未出错")
				return
			}
			require.NotNil(t, ip, "解析IP地址出错")

			// 调用被测试函数
			result, err := FmtIp6(ip, tt.short)

			// 检查错误
			if tt.wantErr {
				require.Error(t, err, "期望出错但没有错误")
				return
			}
			require.NoError(t, err, "不期望出错但出错了")

			// 验证结果
			require.Equal(t, tt.expected, result, "IP格式化为IPv6表示形式错误")
		})
	}
}


// TestToIP6 测试将IP转换为IPv6格式的函数
func TestToIP6(t *testing.T) {
	// 测试用例
	tests := []struct {
		name     string // 测试名称
		ip       string // 输入IP地址
		expected string // 期望的IPv6格式
		wantErr  bool   // 是否期望出错
	}{
		{
			name:     "IPv4地址",
			ip:       "***********",
			expected: "***********", // ToIP6实际上返回的是IPv6映射的IPv4地址的字符串表示
			wantErr:  false,
		},
		{
			name:     "IPv6地址",
			ip:       "2001:db8::1",
			expected: "2001:db8::1",
			wantErr:  false,
		},
		{
			name:     "无效IP",
			ip:       "invalid",
			expected: "",
			wantErr:  true,
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 调用被测试函数
			result, err := ToIP6(tt.ip)

			// 检查错误
			if tt.wantErr {
				require.Error(t, err, "期望出错但没有错误")
				return
			}
			require.NoError(t, err, "不期望出错但出错了")

			// 对于IPv4地址，ToIP6可能返回不同的表示形式，我们需要解析并比较实际IP值
			if net.ParseIP(tt.ip).To4() != nil {
				resultIP := net.ParseIP(result)
				expectedIP := net.ParseIP(tt.expected)
				require.True(t, resultIP.Equal(expectedIP), "IP转换为IPv6格式错误")
			} else {
				// 对于IPv6地址，直接比较字符串
				require.Equal(t, tt.expected, result, "IP转换为IPv6格式错误")
			}
		})
	}
}


// TestInetFunctions 测试IP地址与整数之间的转换函数
func TestInetFunctions(t *testing.T) {
	// 测试 Inet_ntoa 函数（整数到IP）
	testCases := []struct {
		ipInt    int64
		expected string
	}{
		{0, "0.0.0.0"},
		{167772160, "10.0.0.0"},
		{3232235521, "***********"},
		{3232235777, "***********"},
		{4294967295, "***************"},
	}

	for _, tc := range testCases {
		result := Inet_ntoa(tc.ipInt)
		require.Equal(t, tc.expected, result.String(), "Inet_ntoa(%d) 应返回 %s", tc.ipInt, tc.expected)
	}

	// 测试 Inet_aton 函数（IP到整数）
	for _, tc := range testCases {
		ip := net.ParseIP(tc.expected)
		result := Inet_aton(ip)
		require.Equal(t, tc.ipInt, result, "Inet_aton(%s) 应返回 %d", tc.expected, tc.ipInt)
	}

	// 测试双向转换
	for _, tc := range testCases {
		ip := Inet_ntoa(tc.ipInt)
		ipInt := Inet_aton(ip)
		require.Equal(t, tc.ipInt, ipInt, "Inet_aton(Inet_ntoa(%d)) 应返回相同值", tc.ipInt)
	}
}

// TestToIPFunctions 测试IP版本转换函数
func TestToIPFunctions(t *testing.T) {
	// 测试 ToIP6 函数
	testCases := []struct {
		input    string
		expected string
		hasError bool
	}{
		{"***********", "::ffff:***********", false},
		{"::1", "::1", false},
		{"2001:db8::1", "2001:db8::1", false},
		{"invalid", "", true},
	}

	for _, tc := range testCases {
		result, err := ToIP6(tc.input)
		if tc.hasError {
			require.Error(t, err, "ToIP6(%s) 应返回错误", tc.input)
		} else {
			require.NoError(t, err, "ToIP6(%s) 不应返回错误", tc.input)
			require.Equal(t, tc.expected, result, "ToIP6(%s) 应返回 %s", tc.input, tc.expected)
		}
	}

	// 测试 ToIP4 函数
	ipv4Cases := []struct {
		input    string
		expected string
		hasError bool
	}{
		{"***********", "***********", false},
		{"::ffff:***********", "***********", false},
		{"2001:db8::1", "", true},
		{"invalid", "", true},
	}

	for _, tc := range ipv4Cases {
		result, err := ToIP4(tc.input)
		if tc.hasError {
			require.Error(t, err, "ToIP4(%s) 应返回错误", tc.input)
		} else {
			require.NoError(t, err, "ToIP4(%s) 不应返回错误", tc.input)
			require.Equal(t, tc.expected, result, "ToIP4(%s) 应返回 %s", tc.input, tc.expected)
		}
	}
}

// TestIPv4MappedConversion 测试IPv4和IPv4-mapped IPv6地址之间的转换
func TestIPv4MappedConversion(t *testing.T) {
	// 测试用例：IPv4地址
	ipv4TestCases := []struct {
		ipv4 string
		ipv6 string
	}{
		{"***********", "::ffff:***********"},
		{"********", "::ffff:********"},
		{"************", "::ffff:************"},
		{"127.0.0.1", "::ffff:127.0.0.1"},
		{"0.0.0.0", "::ffff:0.0.0.0"},
		{"***************", "::ffff:***************"},
	}

	// 测试IPv4到IPv6的转换
	for _, tc := range ipv4TestCases {
		t.Run("IPv4->IPv6: "+tc.ipv4, func(t *testing.T) {
			result, err := ToIP6(tc.ipv4)
			require.NoError(t, err, "ToIP6(%s) 不应返回错误", tc.ipv4)
			require.Equal(t, tc.ipv6, result, "ToIP6(%s) 应返回 %s，实际返回 %s", tc.ipv4, tc.ipv6, result)

			// 验证转换的地址可以被解析为有效的IPv6地址
			ip := net.ParseIP(result)
			require.NotNil(t, ip, "转换后的地址应该是有效的IP地址")
			require.NotNil(t, ip.To16(), "转换后的地址应该是有效的IPv6地址")
		})
	}

	// 测试IPv6到IPv4的转换
	for _, tc := range ipv4TestCases {
		t.Run("IPv6->IPv4: "+tc.ipv6, func(t *testing.T) {
			result, err := ToIP4(tc.ipv6)
			require.NoError(t, err, "ToIP4(%s) 不应返回错误", tc.ipv6)
			require.Equal(t, tc.ipv4, result, "ToIP4(%s) 应返回 %s，实际返回 %s", tc.ipv6, tc.ipv4, result)

			// 验证转换的地址可以被解析为有效的IPv4地址
			ip := net.ParseIP(result)
			require.NotNil(t, ip, "转换后的地址应该是有效的IP地址")
			require.NotNil(t, ip.To4(), "转换后的地址应该是有效的IPv4地址")
		})
	}

	// 测试非IPv4-mapped IPv6地址
	nonMappedIPv6 := []string{
		"2001:db8::1",
		"::1",
		"fe80::1",
		"::ffff:***********56", // 无效IPv4部分
	}

	for _, ipv6 := range nonMappedIPv6 {
		t.Run("Non-mapped IPv6: "+ipv6, func(t *testing.T) {
			_, err := ToIP4(ipv6)
			require.Error(t, err, "ToIP4(%s) 应返回错误，因为它不是IPv4-mapped IPv6地址", ipv6)
		})
	}

	// 测试无效的IP地址
	invalidIPs := []string{
		"invalid",
		"999.999.999.999",
		"::zzz",
	}

	for _, ip := range invalidIPs {
		t.Run("Invalid IP: "+ip, func(t *testing.T) {
			_, err := ToIP6(ip)
			require.Error(t, err, "ToIP6(%s) 应返回错误，因为它是无效的IP地址", ip)

			_, err = ToIP4(ip)
			require.Error(t, err, "ToIP4(%s) 应返回错误，因为它是无效的IP地址", ip)
		})
	}

	// 测试双向转换
	for _, tc := range ipv4TestCases {
		t.Run("Bidirectional: "+tc.ipv4, func(t *testing.T) {
			// IPv4 -> IPv6 -> IPv4
			ipv6, err := ToIP6(tc.ipv4)
			require.NoError(t, err)

			ipv4, err := ToIP4(ipv6)
			require.NoError(t, err)

			require.Equal(t, tc.ipv4, ipv4, "双向转换后应保持原值")
		})
	}
}

// TestPadFunctions 测试IP填充函数
func TestPadFunctions(t *testing.T) {
	// 测试 FixedPad 函数
	ip := net.ParseIP("***********")
	padding := 3

	result := FixedPad(ip, padding)
	expected := "***************"
	require.Equal(t, expected, result, "FixedPad(%s, %d) 应返回 %s", ip, padding, expected)

	// 测试更大的填充
	padding = 5
	result = FixedPad(ip, padding)
	expected = "00192.00168.00001.00001"
	require.Equal(t, expected, result, "FixedPad(%s, %d) 应返回 %s", ip, padding, expected)

	// 测试 IncrementalPad 函数
	padding = 2
	results := IncrementalPad(ip, padding)
	// 预期结果: [***********, ***********1, ************, *************]
	expected1 := "***********"
	expected2 := "***********1"
	expected3 := "************"
	expected4 := "*************"

	require.Contains(t, results, expected1, "IncrementalPad 应包含 %s", expected1)
	require.Contains(t, results, expected2, "IncrementalPad 应包含 %s", expected2)
	require.Contains(t, results, expected3, "IncrementalPad 应包含 %s", expected3)
	require.Contains(t, results, expected4, "IncrementalPad 应包含 %s", expected4)
	require.Len(t, results, 4, "IncrementalPad 应返回 4 个结果")
}

// TestAlterIP 测试IP地址的各种表示形式生成
func TestAlterIP(t *testing.T) {
	ip := "***********"

	// 测试标准表示法
	formats := []string{"1"}
	results := AlterIP(ip, formats, 0, false)
	require.Len(t, results, 1, "标准表示法应返回1个结果")
	require.Equal(t, ip, results[0], "标准表示法应返回原始IP")

	// 测试零优化表示法
	formats = []string{"2"}
	results = AlterIP(ip, formats, 0, false)
	require.Len(t, results, 1, "零优化表示法应返回1个结果")
	require.Equal(t, "192.168.1", results[0], "标准表示法应返回原始IP")

	// 测试八进制表示法
	formats = []string{"3"}
	results = AlterIP(ip, formats, 0, false)
	require.Len(t, results, 1, "八进制表示法应返回1个结果")
	require.Contains(t, results[0], "0", "八进制表示法应包含前导零")
	require.Equal(t, "0300.0250.0.01", results[0], "标准表示法应返回原始IP")

	// 测试十六进制表示法
	formats = []string{"4"}
	results = AlterIP(ip, formats, 0, false)
	require.Len(t, results, 3, "十六进制表示法应返回3个结果")
	require.Contains(t, results[0], "0x", "十六进制表示法应包含0x前缀")
	require.Equal(t, "0xc0.0xa8.0x0.0x1", results[0], "标准表示法应返回原始IP")

	// 测试十进制表示法
	formats = []string{"5"}
	results = AlterIP(ip, formats, 0, false)
	require.Len(t, results, 1, "十进制表示法应返回1个结果")

	// 测试零填充
	formats = []string{"10"}
	padding := 3
	results = AlterIP(ip, formats, padding, false)
	require.Len(t, results, 1, "零填充表示法应返回1个结果")
	require.Contains(t, results[0], "***************", "零填充表示法应包含指定长度的零")

	// 测试多种格式组合
	formats = []string{"1", "3", "5"}
	results = AlterIP(ip, formats, 0, false)
	require.Len(t, results, 3, "多种格式组合应返回3个结果")
}

// TestCIDRFromIPRange 测试从IP范围获取CIDR列表
func TestCIDRFromIPRange(t *testing.T) {
	testCases := []struct {
		firstIP  string
		lastIP   string
		expected []string
		hasError bool
	}{
		{"***********", "***********0", []string{"***********/32", "***********/31", "***********/30", "***********/31", "***********0/32"}, false},
		{"10.0.0.0", "**********", []string{"10.0.0.0/24"}, false},
		{"***********", "*************", []string{"***********/24", "***********/24"}, false},
		{"***********0", "***********", nil, true}, // 逆序范围，应返回错误
	}

	for _, tc := range testCases {
		firstIP := net.ParseIP(tc.firstIP)
		lastIP := net.ParseIP(tc.lastIP)

		cidrs, err := GetCIDRFromIPRange(firstIP, lastIP)

		if tc.hasError {
			require.Error(t, err, "GetCIDRFromIPRange(%s, %s) 应返回错误", tc.firstIP, tc.lastIP)
		} else {
			require.NoError(t, err, "GetCIDRFromIPRange(%s, %s) 不应返回错误", tc.firstIP, tc.lastIP)
			require.Len(t, cidrs, len(tc.expected), "GetCIDRFromIPRange 应返回 %d 个CIDR", len(tc.expected))

			// 检查返回的CIDR是否符合预期
			for i, cidr := range cidrs {
				expectedCIDR := tc.expected[i]
				_, expectedNet, _ := net.ParseCIDR(expectedCIDR)
				require.True(t, cidr.IP.Equal(expectedNet.IP), "CIDR[%d] IP 应为 %s，实际为 %s", i, expectedNet.IP, cidr.IP)

				ones1, _ := cidr.Mask.Size()
				ones2, _ := expectedNet.Mask.Size()
				require.Equal(t, ones2, ones1, "CIDR[%d] 掩码长度应为 %d，实际为 %d", i, ones2, ones1)
			}
		}
	}
}

// TestOverflowLastOctect 测试IP地址最后八位字节溢出函数
func TestOverflowLastOctect(t *testing.T) {
	testCases := []struct {
		ip       string
		expected string
		hasError bool
	}{
		{"***********", "192.168.256", false},
		{"10.0.0.0", "10.0.255", false},
		{"***********", "", true}, // 最后一个八位字节不是0，应返回错误
		{"invalid", "", true},     // 无效IP，应返回错误
	}

	for _, tc := range testCases {
		ip := net.ParseIP(tc.ip)
		if ip == nil && !tc.hasError {
			t.Fatalf("无效的测试IP: %s", tc.ip)
		}

		if ip != nil {
			result, err := overflowLastOctect(ip)

			if tc.hasError {
				require.Error(t, err, "overflowLastOctect(%s) 应返回错误", tc.ip)
			} else {
				require.NoError(t, err, "overflowLastOctect(%s) 不应返回错误", tc.ip)
				require.Equal(t, tc.expected, result, "overflowLastOctect(%s) 应返回 %s", tc.ip, tc.expected)
			}
		}
	}
}


func TestIpRangeToCIDR(t *testing.T) {
	// 定义测试用例
	tests := []struct {
		name    string
		start   string
		end     string
		want    []string
		wantErr bool
	}{
		{
			name:  "IPv4 单个地址",
			start: "***********",
			end:   "***********",
			want:  []string{"***********/32"},
		},
		{
			name:  "IPv4 简单范围",
			start: "***********",
			end:   "***********0",
			want: []string{
				"***********/32",
				"***********/31",
				"***********/30",
				"***********/31",
				"***********0/32",
			},
		},
		{
			name:  "IPv4 跨子网范围",
			start: "***********50",
			end:   "***********0",
			want: []string{
				"***********50/31",
				"***********52/30",
				"***********/29",
				"***********/31",
				"***********0/32",
			},
		},
		{
			name:  "IPv4 完整C类网段",
			start: "***********",
			end:   "***********55",
			want:  []string{"***********/24"},
		},
		{
			name:  "IPv6 单个地址",
			start: "2001:db8::1",
			end:   "2001:db8::1",
			want:  []string{"2001:db8::1/128"},
		},
		{
			name:  "IPv6 简单范围",
			start: "2001:db8::1",
			end:   "2001:db8::10",
			want: []string{
				"2001:db8::1/128",
				"2001:db8::2/127",
				"2001:db8::4/126",
				"2001:db8::8/125",
				"2001:db8::10/128",
			},
		},
		{
			name:    "混合IP类型错误",
			start:   "***********",
			end:     "2001:db8::1",
			wantErr: true,
		},
		{
			name:    "起始IP大于结束IP错误",
			start:   "***********0",
			end:     "***********",
			wantErr: true,
		},
		{
			name:    "无效的起始IP",
			start:   "***********00",
			end:     "***********0",
			wantErr: true,
		},
		{
			name:    "无效的结束IP",
			start:   "***********",
			end:     "***********00",
			wantErr: true,
		},
	}

	// 运行测试用例
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := IpRangeToCIDR(tt.start, tt.end)

			// 检查错误
			if (err != nil) != tt.wantErr {
				t.Errorf("IpRangeToCIDR() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 如果期望有错误，则不检查结果
			if tt.wantErr {
				return
			}

			// 比较结果
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("IpRangeToCIDR() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TestCoalesceCIDRs 测试CIDR合并函数
func TestCoalesceCIDRs(t *testing.T) {
	testCases := []struct {
		cidrs        []string
		expectedIPv4 []string
		expectedIPv6 []string
	}{
		{
			cidrs:        []string{"***********/24", "***********/24", "***********/24", "***********/24"},
			expectedIPv4: []string{"***********/23", "***********/24", "***********/24"},
			expectedIPv6: []string{},
		},
		{
			cidrs:        []string{"10.0.0.0/16", "********/16"},
			expectedIPv4: []string{"10.0.0.0/15"},
			expectedIPv6: []string{},
		},
		{
			cidrs:        []string{"2001:db8::/32", "2001:db8:1::/48", "2001:db8:2::/48"},
			expectedIPv4: []string{},
			expectedIPv6: []string{"2001:db8::/32"},
		},
		{
			cidrs:        []string{"***********/24", "***********/24"}, // 不连续的CIDR
			expectedIPv4: []string{"***********/24", "***********/24"},
			expectedIPv6: []string{},
		},
	}

	for _, tc := range testCases {
		var cidrs []*net.IPNet
		for _, cidrStr := range tc.cidrs {
			_, network, err := net.ParseCIDR(cidrStr)
			require.NoError(t, err)
			cidrs = append(cidrs, network)
		}

		ipv4Cidrs, ipv6Cidrs := CoalesceCIDRs(cidrs)

		// 检查IPv4 CIDR
		require.Len(t, ipv4Cidrs, len(tc.expectedIPv4), "CoalesceCIDRs 应返回 %d 个IPv4 CIDR", len(tc.expectedIPv4))
		for i, cidr := range ipv4Cidrs {
			_, expectedNet, _ := net.ParseCIDR(tc.expectedIPv4[i])
			require.True(t, cidr.IP.Equal(expectedNet.IP), "IPv4 CIDR[%d] IP 应为 %s，实际为 %s", i, expectedNet.IP, cidr.IP)

			ones1, _ := cidr.Mask.Size()
			ones2, _ := expectedNet.Mask.Size()
			require.Equal(t, ones2, ones1, "IPv4 CIDR[%d] 掩码长度应为 %d，实际为 %d", i, ones2, ones1)
		}

		// 检查IPv6 CIDR
		require.Len(t, ipv6Cidrs, len(tc.expectedIPv6), "CoalesceCIDRs 应返回 %d 个IPv6 CIDR", len(tc.expectedIPv6))
		for i, cidr := range ipv6Cidrs {
			if len(tc.expectedIPv6) > 0 {
				_, expectedNet, _ := net.ParseCIDR(tc.expectedIPv6[i])
				require.True(t, cidr.IP.Equal(expectedNet.IP), "IPv6 CIDR[%d] IP 应为 %s，实际为 %s", i, expectedNet.IP, cidr.IP)

				ones1, _ := cidr.Mask.Size()
				ones2, _ := expectedNet.Mask.Size()
				require.Equal(t, ones2, ones1, "IPv6 CIDR[%d] 掩码长度应为 %d，实际为 %d", i, ones2, ones1)
			}
		}
	}
}

// TestAggregateApproxIPv4To24 测试将IPv4地址列表聚合为近似的/24 CIDR块的函数
func TestAggregateApproxIPv4To24(t *testing.T) {
	// 测试用例
	tests := []struct {
		name      string   // 测试名称
		ips       []string // 输入IP地址列表
		expected  []string // 期望的聚合后的CIDR列表
		wantErr   bool     // 是否期望出错
		errString string   // 期望的错误信息
	}{
		{
			name: "相同/24网段的IP地址",
			ips: []string{
				"***********/32",
				"***********/32",
				"***********0/32",
				"***********0/32",
			},
			expected: []string{"***********/26"},
			wantErr:  false,
		},
		{
			name: "不同/24网段的IP地址",
			ips: []string{
				"***********/32",
				"***********/32",
				"***********/32",
			},
			expected: []string{
				"***********/32",
				"***********/32",
				"***********/32",
			},
			wantErr: false,
		},
		{
			name: "同一/24网段内需要调整掩码的IP地址",
			ips: []string{
				"***********/32",
				"***********29/32",
			},
			expected: []string{"***********/24"},
			wantErr:  false,
		},
		{
			name: "多个/24网段混合",
			ips: []string{
				"********/32",
				"********/32",
				"********/32",
				"********/32",
				"***********/32",
			},
			expected: []string{
				"********/30",
				"********/30",
				"***********/32",
			},
			wantErr: false,
		},
		{
			name: "只有一个IP地址",
			ips: []string{
				"***********/32",
			},
			expected:  nil,
			wantErr:   true,
			errString: "no enough ip to aggregate",
		},
		{
			name: "包含IPv6地址",
			ips: []string{
				"***********/32",
				"2001:db8::1/128",
			},
			expected:  nil,
			wantErr:   true,
			errString: "only ipv4 is supported",
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 转换输入IP字符串为*net.IPNet对象
			var ipNets []*net.IPNet
			for _, ipStr := range tt.ips {
				_, ipNet, err := net.ParseCIDR(ipStr)
				if err != nil {
					t.Fatalf("解析IP %s 出错: %v", ipStr, err)
				}
				ipNets = append(ipNets, ipNet)
			}

			// 调用被测试函数
			result, err := AggregateApproxIPv4To24(ipNets)
			t.Log(result)

			// 检查错误
			if tt.wantErr {
				require.Error(t, err, "期望出错但没有错误")
				if tt.errString != "" {
					require.Contains(t, err.Error(), tt.errString, "错误信息不匹配")
				}
				return
			}
			require.NoError(t, err, "不期望出错但出错了")

			// 检查结果数量
			require.Equal(t, len(tt.expected), len(result), "聚合后的CIDR数量不匹配")

			// 转换期望结果为*net.IPNet进行比较
			var expectedNets []*net.IPNet
			for _, cidrStr := range tt.expected {
				_, ipNet, err := net.ParseCIDR(cidrStr)
				require.NoError(t, err, "解析期望CIDR出错")
				expectedNets = append(expectedNets, ipNet)
			}

			// 验证结果包含所有期望的CIDR
			for _, expectedNet := range expectedNets {
				found := false
				for _, resultNet := range result {
					if expectedNet.String() == resultNet.String() {
						found = true
						break
					}
				}
				require.True(t, found, "期望的CIDR %s 在结果中未找到", expectedNet.String())
			}

			// 验证结果中的每个CIDR都在期望列表中
			for _, resultNet := range result {
				found := false
				for _, expectedNet := range expectedNets {
					if resultNet.String() == expectedNet.String() {
						found = true
						break
					}
				}
				require.True(t, found, "结果中的CIDR %s 不在期望列表中", resultNet.String())
			}
		})
	}
}

// TestFindMinCIDR 测试查找包含所有给定IP的最小CIDR的函数
func TestFindMinCIDR(t *testing.T) {
	// 测试用例
	tests := []struct {
		name     string   // 测试名称
		ips      []string // 输入IP地址列表
		expected string   // 期望的最小CIDR
		wantErr  bool     // 是否期望出错
	}{
		{
			name: "单个IPv4地址",
			ips: []string{
				"***********/32",
			},
			expected: "***********/32",
			wantErr:  false,
		},
		{
			name: "相邻IPv4地址",
			ips: []string{
				"***********/32",
				"***********/32",
			},
			expected: "***********/30",
			wantErr:  false,
		},
		{
			name: "多个连续IPv4地址",
			ips: []string{
				"***********/32",
				"***********/32",
				"***********/32",
				"***********/32",
			},
			expected: "***********/29",
			wantErr:  false,
		},
		{
			name: "不连续IPv4地址",
			ips: []string{
				"***********/32",
				"***********/32",
			},
			expected: "***********/29",
			wantErr:  false,
		},
		{
			name: "跨子网IPv4地址",
			ips: []string{
				"***********54/32",
				"***********/32",
			},
			expected: "***********/22",
			wantErr:  false,
		},
		{
			name: "单个IPv6地址",
			ips: []string{
				"2001:db8::1/128",
			},
			expected: "2001:db8::1/128",
			wantErr:  false,
		},
		{
			name: "多个IPv6地址",
			ips: []string{
				"2001:db8::1/128",
				"2001:db8::2/128",
				"2001:db8::3/128",
			},
			expected: "2001:db8::/126",
			wantErr:  false,
		},
		
		{
			name:     "空IP列表",
			ips:      []string{},
			expected: "",
			wantErr:  true,
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 转换输入IP字符串为*net.IPNet对象
			var ipNets []*net.IPNet
			for _, ipStr := range tt.ips {
				_, ipNet, err := net.ParseCIDR(ipStr)
				if err != nil {
					t.Fatalf("解析IP %s 出错: %v", ipStr, err)
				}
				ipNets = append(ipNets, ipNet)
			}

			// 调用被测试函数
			result, err := FindMinCIDR(ipNets)

			// 检查错误
			if tt.wantErr {
				require.Error(t, err, "期望出错但没有错误")
				return
			}
			require.NoError(t, err, "不期望出错但出错了")

			// 检查结果
			_, expectedNet, err := net.ParseCIDR(tt.expected)
			require.NoError(t, err, "解析期望CIDR出错")

			// 比较IP和掩码
			require.Equal(t, expectedNet.IP.String(), result.IP.String(), "IP地址不匹配")

			ones1, bits1 := expectedNet.Mask.Size()
			ones2, bits2 := result.Mask.Size()
			require.Equal(t, ones1, ones2, "掩码长度不匹配")
			require.Equal(t, bits1, bits2, "地址位数不匹配")
		})
	}
}