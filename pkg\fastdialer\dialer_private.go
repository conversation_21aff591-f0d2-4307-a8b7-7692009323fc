// Author: chenjb
// Version: V1.0
// Date: 2025-05-20 17:01:52
// FilePath: /yaml_scan/pkg/fastdialer/dialer_private.go
// Description:提供fastdialer包的私有实现，包括拨号核心逻辑和TLS连接处理
package fastdialer

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"log/slog"
	"net"
	"os"
	"strings"
	"sync/atomic"
	"time"
	"yaml_scan/pkg/fastdialer/ja3"
	"yaml_scan/pkg/fastdialer/utils"
	"yaml_scan/pkg/retryabledns"
	ctxutil "yaml_scan/utils/context"
	cryptoutil "yaml_scan/utils/crypto"
	"yaml_scan/utils/errkit"
	iputil "yaml_scan/utils/ip"
	ptrutil "yaml_scan/utils/ptr"

	utls "github.com/refraction-networking/utls"
	ztls "github.com/zmap/zcrypto/tls"
)

// l4dialer 接口用于条件性地传递net.Dialer或其包装器
// 定义了DialContext方法，用于建立网络连接
type l4dialer interface {
	DialContext(ctx context.Context, network, address string) (net.Conn, error)
}

// 确保DialWrap实现了l4dialer接口
var _ l4dialer = &utils.DialWrap{}

// dialOptions 拨号选项结构，包含建立连接所需的各种参数
type dialOptions struct {
	network             string        // 网络类型，如"tcp"
	address             string        // 目标地址，格式为"host:port"
	shouldUseTLS        bool          // 是否使用TLS
	shouldUseZTLS       bool          // 是否使用ZTLS
	tlsconfig           *tls.Config   // TLS配置
	ztlsconfig          *ztls.Config  // ZTLS配置
	impersonateStrategy ja3.Strategy  // JA3指纹模拟策略
	impersonateIdentity *ja3.Identity // JA3指纹身份
	ips                 []string      // 解析后的IP地址列表
	port                string        // 目标端口
	hostname            string        // 主机名
}

// connHash 返回连接的哈希值
// @receiver d
// @return string string: 连接哈希值，格式为"network-address"
func (d *dialOptions) connHash() string {
	return fmt.Sprintf("%s-%s", d.network, d.address)
}

// logAddress 生成用于日志和错误报告的地址字符串
// @receiver d 
// @return string string:  格式化的地址字符串，格式为"host:port"，适合日志记录和错误报告
func (d *dialOptions) logAddress() string {
	// 优先使用主机名，如果存在
	logAddress := d.hostname
	// 否则使用第一个IP地址
	if logAddress == "" {
		logAddress = d.ips[0]
	}
	// 返回"host:port"格式
	return net.JoinHostPort(logAddress, d.port)
}

// dial 拨号器的核心方法，负责建立网络连接
// 处理DNS解析、IP过滤、连接建立和TLS握手
// @receiver d
// @param ctx context.Context: 上下文，用于控制超时和取消
// @param opts *dialOptions: 拨号选项
// @return conn net.Conn: 建立的网络连接
// @return err error: 连接过程中可能发生的错误
func (d *Dialer) dial(ctx context.Context, opts *dialOptions) (conn net.Conn, err error) {
	// 添加全局超时到上下文
	ctx, cancel := context.WithTimeoutCause(ctx, d.options.DialerTimeout, ErrDialTimeout)
	defer cancel()

	var hostname, port, fixedIP string
	var IPS []string
	// 检查是否存在于缓存中
	dw, err := d.dialCache.GetIFPresent(opts.connHash())
	if err != nil || dw == nil {
		// 解析地址，支持IPv6格式[::1]:80
		if strings.HasPrefix(opts.address, "[") {
			closeBracketIndex := strings.Index(opts.address, "]")
			if closeBracketIndex == -1 {
				return nil, MalformedIP6Error
			}
			hostname = opts.address[:closeBracketIndex+1]
			if len(opts.address) < closeBracketIndex+2 {
				return nil, NoPortSpecifiedError
			}
			port = opts.address[closeBracketIndex+2:]
		} else {
			// 解析常规地址格式
			addressParts := strings.SplitN(opts.address, ":", 3)
			numberOfParts := len(addressParts)

			if numberOfParts >= 2 {
				// ip|host:port
				hostname = addressParts[0]
				port = addressParts[1]
				// ip|host:port:ip => curl --resolve ip:port:ip
				if numberOfParts > 2 {
					fixedIP = addressParts[2]
				}
				// 检查上下文中是否包含IP
				if ctxIP := ctx.Value(IP); ctxIP != nil {
					fixedIP = fmt.Sprint(ctxIP)
				}
			} else {
				// 没有指定端口 => 错误
				return nil, NoPortSpecifiedError
			}
		}

		/// 检查数据是否在缓存中
		hostname = asAscii(hostname)

		// 使用固定IP作为首选
		if fixedIP != "" {
			IPS = append(IPS, fixedIP)
		} else {
			// 使用singleflight避免重复解析同一主机名
			cacheData, err, _ := d.resolutionsGroup.Do(hostname, func() (interface{}, error) {
				return d.GetDNSData(hostname)
			})

			if cacheData == nil {
				return nil, ResolveHostError
			}
			data := cacheData.(*retryabledns.DNSData)
			if err != nil || len(data.A)+len(data.AAAA) == 0 {
				return nil, NoAddressFoundError
			}
			IPS = append(IPS, append(data.A, data.AAAA...)...)
		}

		filteredIPs := []string{}

		// 过滤有效/无效的IP
		for _, ip := range IPS {
			// 检查是否符合网络策略（白名单/黑名单）
			if !d.networkpolicy.Validate(ip) {
				// 如果定义了回调函数 调用
				if d.options.OnInvalidTarget != nil {
					d.options.OnInvalidTarget(hostname, ip, port)
				}
				continue
			}
			if d.options.OnBeforeDial != nil {
				d.options.OnBeforeDial(hostname, ip, port)
			}
			// 添加为允许的IP
			filteredIPs = append(filteredIPs, ip)
		}

		if len(filteredIPs) == 0 {
			return nil, NoAddressAllowedError
		}

		// 更新允许的IP
		IPS = filteredIPs
		opts.ips = IPS
		opts.port = port
		opts.hostname = hostname
	}

	var finalDialer l4dialer = d.dialer

	// 仅当满足以下条件时才缓存
	// 1. 尚未存在于缓存中
	// 2. 是域名而不是IP
	// 3. 至少有一个有效IP
	// 4. 未设置代理拨号器
	if dw == nil && hostname != "" && len(IPS) > 0 && d.proxyDialer == nil {
		dw, err = utils.NewDialWrap(d.dialer, IPS, opts.network, opts.address, opts.port)
		if err != nil {
			return nil, errkit.Wrap(err, "could not create dialwrap")
		}
		if err = d.dialCache.Set(opts.connHash(), dw); err != nil {
			return nil, errkit.Wrap(err, "could not set dialwrap")
		}
	}
	if dw != nil {
		finalDialer = dw
		// 使用dw时，ip、network、port等都已预设
		// 获取其中一个来避免破坏后续逻辑
		ip, port := dw.Address()
		opts.ips = []string{ip}
		opts.port = port
		// 通过解析地址设置主机名
		hostname, _, _ = net.SplitHostPort(opts.address)
		opts.hostname = hostname
	}

	// 使用选择的拨号器建立实际连接
	conn, err = d.dialIPS(ctx, finalDialer, opts)
	if err == nil {
		if conn == nil {
			err = CouldNotConnectError
			return
		}
		// 检查远程地址是否有效
		if conn.RemoteAddr() == nil {
			return nil, errkit.New("remote address is nil")
		}
		// 提取连接的实际IP地址
		ip, _, _ := net.SplitHostPort(conn.RemoteAddr().String())

		// 如果启用了拨号历史记录，保存本次连接信息
		if d.options.WithDialerHistory && d.dialerHistory != nil {
			setErr := d.dialerHistory.Set(hostname, []byte(ip))
			if setErr != nil {
				return nil, setErr
			}
		}

		// 调用拨号成功回调（如果设置）
		if d.options.OnDialCallback != nil {
			d.options.OnDialCallback(hostname, ip)
		}

		// 如果需要收集TLS数据且当前是TLS连接
		if d.options.WithTLSData && opts.shouldUseTLS {
			// 尝试将连接转换为TLS连接类型
			if connTLS, ok := conn.(*tls.Conn); ok {
				// 准备序列化TLS连接状态
				var data bytes.Buffer
				connState := connTLS.ConnectionState()
				err := json.NewEncoder(&data).Encode(cryptoutil.TLSGrab(&connState))
				if err != nil {
					return nil, err
				}
				// 保存TLS数据
				setErr := d.dialerTLSData.Set(hostname, data.Bytes())
				if setErr != nil {
					return nil, setErr
				}
			}
		}
	}
	return
}

// dialIPS 使用指定的拨号器连接到IP地址
// 处理TLS、ZTLS和普通连接的建立
// @receiver d
// @param ctx context.Context:上下文，用于控制超时和取消
// @param l4 l4dialer: 要使用的拨号器接口
// @param opts *dialOptions:  拨号选项
// @return conn net.Conn:   建立的网络连接
// @return err error: 连接过程中可能发生的错误
func (d *Dialer) dialIPS(ctx context.Context, l4 l4dialer, opts *dialOptions) (conn net.Conn, err error) {
	// 构建完整的主机:端口字符串用于拨号
	hostPort := net.JoinHostPort(opts.ips[0], opts.port)

	// 处理TLS连接 - 使用标准TLS或自定义TLS指纹
	if opts.shouldUseTLS {
		// 创建TLS配置的副本
		tlsconfigCopy := opts.tlsconfig.Clone()

		// 设置TLS ServerName (SNI)，按优先级确定：
		// 1. 拨号器全局SNI设置（如果有）
		// 2. 当前上下文中的SNI设置（如果有）
		// 3. 如果目标是域名而非IP，则使用该域名
		switch {
		case d.options.SNIName != "":
			// 使用全局配置的SNI名称
			tlsconfigCopy.ServerName = d.options.SNIName
		case ctx.Value(SniName) != nil:
			// 使用上下文中设置的SNI名称
			sniName := ctx.Value(SniName).(string)
			tlsconfigCopy.ServerName = sniName
		case !iputil.IsIP(opts.hostname):
			// 如果主机名不是IP，使用它作为SNI名称
			tlsconfigCopy.ServerName = opts.hostname
		}

		// 根据JA3指纹模拟策略选择不同的TLS连接方式
		if opts.impersonateStrategy == ja3.None {
			// 不使用JA3指纹模拟，使用标准TLS
			l4Conn, err := l4.DialContext(ctx, opts.network, hostPort)
			if err != nil {
				return nil, d.handleDialError(err, opts)
			}
			// 将TCP连接升级为TLS连接
			TlsConn := tls.Client(l4Conn, tlsconfigCopy)
			// 执行TLS握手，使用上下文控制超时
			if err := TlsConn.HandshakeContext(ctx); err != nil {
				return nil, errkit.Wrap(err, "could not tls handshake")
			}
			conn = TlsConn
		} else {
			// 使用JA3指纹模拟
			// 首先建立基础TCP连接
			nativeConn, err := l4.DialContext(ctx, opts.network, hostPort)
			if err != nil {
				return nil, d.handleDialError(err, opts)
			}
			// 将标准TLS配置转换为uTLS配置
			// uTLS是一个允许自定义TLS客户端指纹的库
			uTLSConfig := &utls.Config{
				InsecureSkipVerify: tlsconfigCopy.InsecureSkipVerify, // 是否跳过证书验证
				ServerName:         tlsconfigCopy.ServerName,         // SNI名称
				MinVersion:         tlsconfigCopy.MinVersion,         // 最低TLS版本
				MaxVersion:         tlsconfigCopy.MaxVersion,         // 最高TLS版本
				CipherSuites:       tlsconfigCopy.CipherSuites,       // 支持的密码套件
			}
			var uTLSConn *utls.UConn

			// 使用随机生成的TLS指纹
			if opts.impersonateStrategy == ja3.Random {
				// HelloRandomized会生成随机但有效的TLS客户端Hello消息
				uTLSConn = utls.UClient(nativeConn, uTLSConfig, utls.HelloRandomized)
			} else if opts.impersonateStrategy == ja3.Custom {
				// 使用自定义指纹（通过JA3字符串或专用配置）
				// HelloCustom允许完全自定义TLS客户端Hello消息
				uTLSConn = utls.UClient(nativeConn, uTLSConfig, utls.HelloCustom)
				// 将JA3身份转换为uTLS客户端Hello规范
				clientHelloSpec := utls.ClientHelloSpec(ptrutil.Safe(opts.impersonateIdentity))
				// 应用自定义Hello规范到连接
				if err := uTLSConn.ApplyPreset(&clientHelloSpec); err != nil {
					return nil, err
				}
			} else if opts.impersonateStrategy == ja3.Chrome {
				// 模拟Chrome浏览器106版本的TLS指纹
				// HelloChrome_106_Shuffle使用Chrome 106的指纹，带有一些随机化
				uTLSConn = utls.UClient(nativeConn, uTLSConfig, utls.HelloChrome_106_Shuffle)
			}
			// 执行TLS握手
			if err := uTLSConn.Handshake(); err != nil {
				return nil, err
			}
			conn = uTLSConn
		}
	} else if opts.shouldUseZTLS {
		// 使用ZTLS连接 (zmap/zcrypto实现的TLS)
		// 通常用于支持非标准或旧版本TLS功能
		ztlsconfigCopy := opts.ztlsconfig.Clone()
		
		// 设置ZTLS的ServerName，逻辑与标准TLS相同
		switch {
		case d.options.SNIName != "":
			ztlsconfigCopy.ServerName = d.options.SNIName
		case ctx.Value(SniName) != nil:
			sniName := ctx.Value(SniName).(string)
			ztlsconfigCopy.ServerName = sniName
		case !iputil.IsIP(opts.hostname):
			ztlsconfigCopy.ServerName = opts.hostname
		}
		// 建立基础TCP连接
		l4Conn, err := l4.DialContext(ctx, opts.network, hostPort)
		if err != nil {
			return nil, d.handleDialError(err, opts)
		}
		// 将TCP连接升级为ZTLS连接
		ztlsConn := ztls.Client(l4Conn, ztlsconfigCopy)
		
		// 执行ZTLS握手，使用辅助函数处理上下文
		// 因为ZTLS的Handshake不直接支持上下文超时
		_, err = ctxutil.ExecFuncWithTwoReturns(ctx, func() (bool, error) {
			// 在goroutine中运行，因为ZTLS不直接支持上下文取消
			return true, ztlsConn.Handshake()
		})
		if err != nil {
			return nil, err
		}
		conn = ztlsConn
	} else {
		// 普通连接（非TLS/ZTLS）
		// 直接建立TCP连接

		// 检查是否使用代理拨号器
		if d.proxyDialer != nil {
			dialer := *d.proxyDialer
			// 许多socks5代理拨号器的超时机制不完善
			// 这里实现自定义超时控制机制
			connectionCh := make(chan net.Conn, 1)
			errCh := make(chan error, 1)

			// 启动goroutine进行实际拨号操作
			go func() {
				conn, err = dialer.Dial(opts.network, hostPort)
				if err != nil {
					errCh <- err
					return
				}
				connectionCh <- conn
			}()
			// 创建定时器用于超时控制
			// 避免使用time.After，因为它会导致内存泄漏（定时器不会被GC回收）
			dialerTime := time.NewTimer(d.options.DialerTimeout)
			defer dialerTime.Stop()
			// 等待连接完成、出错或超时
			select {
			case <-dialerTime.C:
				return nil, fmt.Errorf("timeout after %v", d.options.DialerTimeout)
			case conn = <-connectionCh:
			case err = <-errCh:
			}
			err = d.handleDialError(err, opts)
		} else {
			// 使用标准拨号器直接建立连接
			conn, err = l4.DialContext(ctx, opts.network, hostPort)
			err = d.handleDialError(err, opts)
		}
	}
	
	// TLS连接失败时的ZTLS回退机制
	// 这是一个应急措施，用于处理某些服务器的TLS兼容性问题
	// ZTLS回退可以通过设置环境变量DISABLE_ZTLS_FALLBACK=true或在选项中设置DisableZtlsFallback=true来禁用
	if err != nil && !errkit.Is(err, os.ErrDeadlineExceeded) && !(d.options.DisableZtlsFallback && disableZTLSFallback) {
		var ztlsconfigCopy *ztls.Config
		
		// 准备ZTLS配置
		if opts.shouldUseZTLS {
			ztlsconfigCopy = opts.ztlsconfig.Clone()
		} else {
			// 否则，从标准TLS配置转换为ZTLS配置
			if opts.tlsconfig == nil {
				opts.tlsconfig = &tls.Config{
					Renegotiation:      tls.RenegotiateOnceAsClient, // 允许客户端重新协商
					MinVersion:         tls.VersionTLS10, // 最低支持TLS 1.0
					InsecureSkipVerify: true,// 跳过证书验证
				}
			}
			// 将标准TLS配置转换为ZTLS配置
			ztlsconfigCopy, err = AsZTLSConfig(opts.tlsconfig)
			if err != nil {
				return nil, errkit.Wrap(err, "could not convert tls config to ztls config")
			}
		}
		// 使用Chrome浏览器的密码套件，提高兼容性
		ztlsconfigCopy.CipherSuites = ztls.ChromeCiphers
		// 建立基础TCP连接
		l4Conn, err := l4.DialContext(ctx, opts.network, hostPort)
		if err != nil {
			return nil, d.handleDialError(err, opts)
		}
		// 将TCP连接升级为ZTLS连接
		ztlsConn := ztls.Client(l4Conn, ztlsconfigCopy)
		// 执行ZTLS握手，使用辅助函数处理上下文
		_, err = ctxutil.ExecFuncWithTwoReturns(ctx, func() (bool, error) {
			// 在goroutine中运行，因为ZTLS不直接支持上下文取消
			return true, ztlsConn.Handshake()
		})
		if err != nil {
			return nil, err
		}
		conn = ztlsConn
	}
	return
}

// handleDialError 处理拨号错误的辅助函数
// 向错误添加地址属性，并处理超时错误的计数和分类
// @receiver d
// @param err error: 原始错误
// @param opts *dialOptions: 拨号选项
// @return error error: 处理后的错误
func (d *Dialer) handleDialError(err error, opts *dialOptions) error {
	if err == nil {
		return nil
	}
	// 将普通错误转换为errkit错误
	errx := errkit.FromError(err)
	errx = errx.SetAttr(slog.Any("address", opts.logAddress()))

	if errx.Kind() == errkit.ErrKindUnknown {
		// 检查是否是I/O超时错误
		if errx.Cause() != nil && strings.Contains(errx.Cause().Error(), "i/o timeout") {
			// 将超时错误标记为临时错误
			errx = errx.SetKind(errkit.ErrKindNetworkTemporary)

			// 如果启用了超时错误跟踪
			if d.dialTimeoutErrors != nil {
				// 获取当前目标的超时错误计数
				count, err := d.dialTimeoutErrors.GetIFPresent(opts.connHash())
				if err != nil {
					count = &atomic.Uint32{}
					count.Store(1)
				} else {
					count.Add(1)
				}
				// 更新缓存中的超时计数
				_ = d.dialTimeoutErrors.Set(opts.connHash(), count)

				// 如果超时次数超过配置的临时错误最大值
				// 将错误重新分类为永久性网络错误
				if count.Load() > uint32(d.options.MaxTemporaryErrors) {
					errx = errx.ResetKind().SetKind(errkit.ErrKindNetworkPermanent)
				}
			}
		}
	}
	return errx
}
