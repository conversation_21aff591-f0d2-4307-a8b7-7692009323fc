//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-22 16:06:29
//FilePath: /yaml_scan/utils/permission/permission_file.go
//Description:

package permissionutil

// 权限常量定义
const (
	os_read        = 04  // 读权限的八进制表示
	os_write       = 02  // 写权限的八进制表示
	os_ex          = 01  // 执行权限的八进制表示
	os_user_shift  = 6   // 用户权限的位移
	os_group_shift = 3   // 组权限的位移
	os_other_shift = 0   // 其他用户权限的位移

	// 用户读、写、执行权限
	UserRead             = os_read << os_user_shift  // 用户读权限
	UserWrite            = os_write << os_user_shift // 用户写权限
	UserExecute          = os_ex << os_user_shift    // 用户执行权限
	UserReadWrite        = UserRead | UserWrite      // 用户读写权限
	UserReadWriteExecute = UserReadWrite | UserExecute // 用户读写执行权限

	// 组读、写、执行权限
	GroupRead             = os_read << os_group_shift  // 组读权限
	GroupWrite            = os_write << os_group_shift // 组写权限
	GroupExecute          = os_ex << os_group_shift    // 组执行权限
	GroupReadWrite        = GroupRead | GroupWrite     // 组读写权限
	GroupReadWriteExecute = GroupReadWrite | GroupExecute // 组读写执行权限

	// 其他用户读、写、执行权限
	OtherRead             = os_read << os_other_shift  // 其他用户读权限
	OtherWrite            = os_write << os_other_shift // 其他用户写权限
	OtherExecute          = os_ex << os_other_shift    // 其他用户执行权限
	OtherReadWrite        = OtherRead | OtherWrite     // 其他用户读写权限
	OtherReadWriteExecute = OtherReadWrite | OtherExecute // 其他用户读写执行权限

	// 所有用户的读、写、执行权限
	AllRead             = UserRead | GroupRead | OtherRead             // 所有用户读权限
	AllWrite            = UserWrite | GroupWrite | OtherWrite          // 所有用户写权限
	AllExecute          = UserExecute | GroupExecute | OtherExecute     // 所有用户执行权限
	AllReadWrite        = AllRead | AllWrite                             // 所有用户读写权限
	AllReadWriteExecute = AllReadWrite | AllExecute                     // 所有用户读写执行权限

	// 默认文件/文件夹权限
	ConfigFolderPermission = UserReadWriteExecute // 配置文件夹的默认权限
	ConfigFilePermission   = UserReadWrite        // 配置文件的默认权限
	BinaryPermission       = UserRead | UserExecute // 二进制文件的默认权限
	TempFilePermission     = UserReadWrite        // 临时文件的默认权限
)