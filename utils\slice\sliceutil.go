//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-16 20:19:46
//FilePath: /yaml_scan/utils/slice/sliceutil.go
//Description:

package sliceutil

// Equal 检查两个切片的元素是否相等，且顺序相同。
// 它接受两个任意可比较类型的切片，并返回 true 如果它们相等，
// 这意味着它们具有相同的长度，并且所有对应的元素都相等。
//
// T: 切片中元素的类型，必须是可比较的。
// s1: 第一个要比较的切片。
// s2: 第二个要比较的切片.
//
// 返回值：
// - 如果两个切片相等，返回 true。
// - 如果不相等或长度不同，返回 false。
func Equal[T comparable](s1, s2 []T) bool {
	if len(s1) != len(s2) {
		return false
	}

	for idx := range s1 {
		if s1[idx] != s2[idx] {
			return false
		}
	}

	return true
}

// Dedupe: 从给定的切片中去除重复元素，并保持元素的原始顺序。
//  @param inputSlice []T: 
//  @return result []T: 
func Dedupe[T comparable](inputSlice []T) (result []T) {
	// 创建一个空的 map，用于记录已经见过的元素
	seen := make(map[T]struct{})
	
	for _, inputValue := range inputSlice {
		if _, ok := seen[inputValue]; !ok {
			seen[inputValue] = struct{}{}
			result = append(result, inputValue)
		}
	}

	return
}

// PruneEqual removes items from the slice equal to the specified value
func PruneEqual[T comparable](inputSlice []T, equalTo T) (r []T) {
	for i := range inputSlice {
		if inputSlice[i] != equalTo {
			r = append(r, inputSlice[i])
		}
	}

	return
}

// Merge and dedupe multiple items
func Merge[V comparable](ss ...[]V) []V {
	var final []V
	for _, s := range ss {
		final = append(final, s...)
	}
	return Dedupe(final)
}

// Contains if a slice contains an element
func Contains[T comparable](inputSlice []T, element T) bool {
	for _, inputValue := range inputSlice {
		if inputValue == element {
			return true
		}
	}

	return false
}
