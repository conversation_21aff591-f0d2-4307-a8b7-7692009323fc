// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 16:52:47
// FilePath: /yaml_scan/pkg/retryablehttp/default_client.go
// Description:  提供retryablehttp包的默认客户端和辅助函数，简化常见HTTP请求操作
package retryablehttp

import (
	"net/http"
	"net/url"
)

// DefaultHTTPClient 是使用DefaultOptionsSingle选项配置的HTTP客户端
var DefaultHTTPClient *Client

func init() {
		// 使用默认的单连接选项创建全局客户端
	DefaultHTTPClient = NewClient(DefaultOptionsSingle)
}

// Get 向指定URL发送GET请求
// @param url string: 
// @return *http.Response *http.Response: 
// @return error error: 
func Get(url string) (*http.Response, error) {
	return DefaultHTTPClient.Get(url)
}

// Head 向指定URL发送HEAD请求
// @param url string: 
// @return *http.Response *http.Response: 
// @return error error: 
func Head(url string) (*http.Response, error) {
	return DefaultHTTPClient.Head(url)
}

// Post 向指定URL发送POST请求
// @param url string: 
// @param bodyType string: 
// @param body interface{}: 
// @return *http.Response *http.Response: 
// @return error error: 
func Post(url, bodyType string, body interface{}) (*http.Response, error) {
	return DefaultHTTPClient.Post(url, bodyType, body)
}

// PostForm 向指定URL发送POSTform请求
// @param url string: 
// @param data url.Values: 
// @return *http.Response *http.Response: 
// @return error error: 
func PostForm(url string, data url.Values) (*http.Response, error) {
	return DefaultHTTPClient.PostForm(url, data)
}

