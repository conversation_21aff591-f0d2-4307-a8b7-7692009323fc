//
// Author: chenjb
// Version: V1.0
// Date: 2025-05-21 16:53:24
// FilePath: /yaml_scan/utils/context/ncontext.go
// Description:

// Author: chenjb
// Version: V1.0
// Date: 2025-05-21 16:52:22
// FilePath: /yaml_scan/utils/context/ncontext.go
// Description:
package contextutil

import "context"

// A problematic situation when implementing context in a function
// is when that function has more than one return values
// if function has only one return value we can safely wrap it something like this
/*
	func DoSomething() error {}
	ch := make(chan error)
	go func() {
		ch <- DoSomething()
	}()
	select {
	case err := <-ch:
		// handle error
	case <-ctx.Done():
		// handle context cancelation
	}
*/
// but what if we have more than one value to return?
// we can use generics and a struct and that is what we are doing here
// here we use struct and generics to store return values of a function
// instead of storing it in a []interface{}

// ExecFuncWithTwoReturns wraps a function which has two return values given that last one is error
// and executes that function in a goroutine there by implementing context
// if context is cancelled before function returns it will return context error
// otherwise it will return function's return values

type twoValueCtx[T1 any, T2 any] struct {
	var1 T1
	var2 T2
}

func ExecFuncWithTwoReturns[T1 any](ctx context.Context, fn func() (T1, error)) (T1, error) {
	ch := make(chan twoValueCtx[T1, error])
	go func() {
		x, y := fn()
		ch <- twoValueCtx[T1, error]{var1: x, var2: y}
	}()
	select {
	case <-ctx.Done():
		var tmp T1
		return tmp, ctx.Err()
	case v := <-ch:
		return v.var1, v.var2
	}
}
