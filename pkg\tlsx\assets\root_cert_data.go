// Author: chenjb
// Version: V1.0
// Date: 2025-06-24 14:15:38
// FilePath: /yaml_scan/pkg/tlsx/assets/root_cert_data.go
// Description:根证书数据管理和证书验证功能
package assets

import (
	_ "embed"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"yaml_scan/pkg/gologger"
)

//go:embed root-certs.pem
// rootCertDataBin 包含嵌入的根证书数据
var rootCertDataBin string

// RootCerts 包含解析后的根证书列表
var RootCerts []*x509.Certificate

func init() {
	var err error
	// 解析嵌入的根证书数据
	RootCerts, err = ParseCertificates([]byte(rootCertDataBin))
	if err != nil {
		gologger.Error().Label("rootcert").Msgf("failed to parse root certs: %v", err)
	}
}

// ParseCertificates 从PEM格式数据中解析X.509证书
// @param data []byte:包含一个或多个PEM格式证书的字节数据
// @return []*x509.Certificate []*x509.Certificate: 成功解析的X.509证书列表
// @return error error: 
func ParseCertificates(data []byte) ([]*x509.Certificate, error) {
	var parsedCerts []*x509.Certificate
	var err error
	block, rest := pem.Decode(data)
	
	for block != nil {
		// 检查PEM块类型是否为证书
		if block.Type == "CERTIFICATE" {
			// 解析X.509证书结构
			cert, errx := x509.ParseCertificate(block.Bytes)
			if errx != nil {
				err = fmt.Errorf("could not parse certificate: %s", errx)
				continue
			}
			parsedCerts = append(parsedCerts, cert)
		}
		if len(rest) == 0 {
			break
		}
		// 解析下一个PEM块
		block, rest = pem.Decode(rest)
	}
	return parsedCerts, err
}

// IsRootCert 检查给定证书是否为可信根证书
func IsRootCert(cert *x509.Certificate) bool {
	for _, c := range RootCerts {
		if c.Equal(cert) {
			return true
		}
	}
	return false
}