//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-19 20:16:17
//FilePath: /yaml_scan/pkg/input/provider/interface.go
//Description:

package provider

import (
	"yaml_scan/pkg/input/provider/http"
	configTypes "yaml_scan/pkg/types"
)


const (
	MultiFormatInputProvider = "MultiFormatInputProvider"
	ListInputProvider        = "ListInputProvider"
	SimpleListInputProvider  = "SimpleInputProvider"
)

// SupportedInputFormats: 返回支持的所有输入格式
//
//	@return string string: 含所有支持的输入格式的字符串，以逗号分隔。
func SupportedInputFormats() string {
	return "list, " + http.SupportedFormats()
}

// InputProvider is unified input provider interface that provides
// processed inputs to nuclei by parsing and providing different
// formats such as list,openapi,postman,proxify,burp etc.
type InputProvider interface {
	// Count returns total targets for input provider
	Count() int64
	// Iterate over all inputs in order
	Iterate(callback func(value *contextargs.MetaInput) bool)
	// Set adds item to input provider
	Set(value string)
	// SetWithProbe adds item to input provider with http probing
	SetWithProbe(value string, probe types.InputLivenessProbe) error
	// SetWithExclusions adds item to input provider if it doesn't match any of the exclusions
	SetWithExclusions(value string) error
	// InputType returns the type of input provider
	InputType() string
	// Close the input provider and cleanup any resources
	Close()
}

// InputOptions contains options for input provider
type InputOptions struct {
	// Options for global config
	Options *configTypes.Options
	// NotFoundCallback is the callback to call when input is not found
	// only supported in list input provider
	NotFoundCallback func(template string) bool
}

// NewInputProvider creates a new input provider based on the options
// and returns it
func NewInputProvider(opts InputOptions) (InputProvider, error) {
	// optionally load generated vars values if available
	val, err := formats.ReadOpenAPIVarDumpFile()
	if err != nil && !errors.Is(err, formats.ErrNoVarsDumpFile) {
		// log error and continue
		gologger.Error().Msgf("Could not read vars dump file: %s\n", err)
	}
	extraVars := make(map[string]interface{})
	if val != nil {
		for _, v := range val.Var {
			v = strings.TrimSpace(v)
			// split into key value
			parts := strings.SplitN(v, "=", 2)
			if len(parts) == 2 {
				extraVars[parts[0]] = parts[1]
			}
		}
	}

	// check if input provider is supported
	if strings.EqualFold(opts.Options.InputFileMode, "list") {
		// create a new list input provider
		return list.New(&list.Options{
			Options:          opts.Options,
			NotFoundCallback: opts.NotFoundCallback,
		})
	} else {
		// use HttpInputProvider
		return http.NewHttpInputProvider(&http.HttpMultiFormatOptions{
			InputFile: opts.Options.TargetsFilePath,
			InputMode: opts.Options.InputFileMode,
			Options: formats.InputFormatOptions{
				Variables:            generators.MergeMaps(extraVars, opts.Options.Vars.AsMap()),
				SkipFormatValidation: opts.Options.SkipFormatValidation,
				RequiredOnly:         opts.Options.FormatUseRequiredOnly,
			},
		})
	}
}