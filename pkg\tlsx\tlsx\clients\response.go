// Author: chenjb
// Version: V1.0
// Date: 2025-06-24 17:31:40
// FilePath: /yaml_scan/pkg/tlsx/tlsx/clients/response.go
// Description:
package clients

import (
	ztls "github.com/zmap/zcrypto/tls"
	"time"
)

// Response 是TLS抓取事件返回的响应
type Response struct {
	// Timestamp 是证书响应的时间戳
	Timestamp *time.Time `json:"timestamp,omitempty"`
	// Host 是要请求的主机
	Host string `json:"host"`
	// IP 是请求的IP地址
	IP string `json:"ip,omitempty"`
	// Port 是要请求的端口
	Port string `json:"port"`
	// ProbeStatus 如果TLS探测失败则为false
	ProbeStatus bool `json:"probe_status"`
	// Error 是包含在errors_json标志中的TLS请求的可选错误
	Error string `json:"error,omitempty"`
	// Version 是服务器响应的TLS版本
	Version string `json:"tls_version,omitempty"`
	// Cipher 是TLS请求的密码套件
	Cipher string `json:"cipher,omitempty"`
	// CertificateResponse 是嵌入在JSON中的叶证书
	*CertificateResponse `json:",inline"`
	// TLSConnection 是用于TLS连接的客户端
	// 当使用scan-mode auto运行时
	TLSConnection string `json:"tls_connection,omitempty"`
	// Chain 是证书链
	Chain []*CertificateResponse `json:"chain,omitempty"`
	// JarmHash 是JARM指纹哈希
	JarmHash string `json:"jarm_hash,omitempty"`
	// Ja3Hash 是JA3指纹哈希
	Ja3Hash string `json:"ja3_hash,omitempty"`
	// Ja3sHash 是JA3S指纹哈希
	Ja3sHash string `json:"ja3s_hash,omitempty"`
	// ServerName 是TLS连接使用的SNI
	ServerName string `json:"sni,omitempty"`
	// VersionEnum 是目标支持的TLS版本列表
	VersionEnum []string `json:"version_enum,omitempty"`
	// TlsCiphers 是目标支持的TLS密码套件列表，按版本分组
	TlsCiphers []TlsCiphers `json:"cipher_enum,omitempty"`
	// ClientHello 是TLS客户端Hello消息详情
	ClientHello *ztls.ClientHello `json:"client_hello,omitempty"`
	// ServerHello 是TLS服务器Hello消息详情
	ServerHello *ztls.ServerHello `json:"servers_hello,omitempty"`
	// ClientCertRequired 指示服务器是否要求客户端证书
	ClientCertRequired *bool `json:"client_cert_required,omitempty"`
}

// TlsCiphers 表示特定TLS版本支持的密码套件
type TlsCiphers struct {
	// Version 是TLS版本
	Version string `json:"version,omitempty"`
	// Ciphers 是该版本支持的密码套件，按安全级别分类
	Ciphers CipherTypes `json:"ciphers,omitempty"`
}

// CipherTypes 按安全级别分组的密码套件列表
type CipherTypes struct {
	// Weak 是弱密码套件列表
	Weak []string `json:"weak,omitempty"`
	// Insecure 是不安全密码套件列表
	Insecure []string `json:"insecure,omitempty"`
	// Secure 是安全密码套件列表
	Secure []string `json:"secure,omitempty"`
	// Unknown 是tlsx未知类型的密码套件列表
	Unknown []string `json:"unknown,omitempty"` // cipher type not know to tlsx
}

// CertificateResponse 是证书的响应结构体
type CertificateResponse struct {
	// Expired 指定证书是否已过期
	Expired bool `json:"expired,omitempty"`
	// SelfSigned 如果证书是自签名的，则返回true
	SelfSigned bool `json:"self_signed,omitempty"`
	// MisMatched 如果证书与主机名不匹配，则返回true
	MisMatched bool `json:"mismatched,omitempty"`
	// Revoked 如果证书已被吊销，则返回true
	Revoked bool `json:"revoked,omitempty"`
	// Untrusted 如果证书不受信任，则为true
	Untrusted bool `json:"untrusted,omitempty"`
	// NotBefore 是证书的生效时间
	NotBefore time.Time `json:"not_before,omitempty"`
	// NotAfter 是证书的过期时间
	NotAfter time.Time `json:"not_after,omitempty"`
	// SubjectDN 是证书的可分辨名称
	SubjectDN string `json:"subject_dn,omitempty"`
	// SubjectCN 是证书的通用名称
	SubjectCN string `json:"subject_cn,omitempty"`
	// SubjectOrg 是证书主题的组织
	SubjectOrg []string `json:"subject_org,omitempty"`
	// SubjectAN 是证书的主题备用名称列表
	SubjectAN []string `json:"subject_an,omitempty"`
	// Domains 是去重后的主题通用名称+主题备用名称列表
	Domains []string `json:"domains,omitempty"`
	// Serial 是证书序列号
	Serial string `json:"serial,omitempty"`
	// IssuerDN 是颁发者的可分辨名称
	IssuerDN string `json:"issuer_dn,omitempty"`
	// IssuerCN 是颁发者的通用名称
	IssuerCN string `json:"issuer_cn,omitempty"`
	// IssuerOrg 是颁发者的组织
	IssuerOrg []string `json:"issuer_org,omitempty"`
	// Emails 是证书中的电子邮件列表
	Emails []string `json:"emails,omitempty"`
	// FingerprintHash 是证书的哈希值
	FingerprintHash CertificateResponseFingerprintHash `json:"fingerprint_hash,omitempty"`
	// Certificate 是PEM格式的原始证书
	Certificate string `json:"certificate,omitempty"`
	// WildCardCert 如果TLS证书是通配符证书，则为true
	WildCardCert bool `json:"wildcard_certificate,omitempty"`
}

// CertificateResponseFingerprintHash 是证书指纹哈希的响应
type CertificateResponseFingerprintHash struct {
	// MD5 是证书的MD5哈希
	MD5 string `json:"md5,omitempty"`
	// SHA1 是证书的SHA1哈希
	SHA1 string `json:"sha1,omitempty"`
	// SHA256 是证书的SHA256哈希
	SHA256 string `json:"sha256,omitempty"`
}
