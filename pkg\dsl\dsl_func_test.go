// Author: chenjb
// Version: V1.0
// Date: 2025-05-29 19:20:00
// FilePath: /yaml_scan/pkg/dsl/dsl_func_test.go
// Description:
package dsl

import (
	"regexp"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestEncodingFunctions 测试编码解码函数的功能
func TestEncodingFunctions(t *testing.T) {
	// 初始化环境
	functions = nil
	Base64()
	Base64Decode()
	HexEncode()
	HexDecode()
	UrlEncode()
	UrlDecode()
	
	helper := HelperFunctions()
	base64Func := helper["base64"]
	base64DecodeFunc := helper["base64_decode"]
	hexEncodeFunc := helper["hex_encode"]
	hexDecodeFunc := helper["hex_decode"]
	urlEncodeFunc := helper["url_encode"]
	urlDecodeFunc := helper["url_decode"]
	
	// 测试Base64编码解码
	t.Run("Base64编码解码测试", func(t *testing.T) {
		original := "你好，世界！Hello, World!"
		
		// 编码
		encoded, err := base64Func(original)
		require.NoError(t, err, "Base64编码应该不返回错误")
		
		// 解码
		decoded, err := base64DecodeFunc(encoded)
		require.NoError(t, err, "Base64解码应该不返回错误")
		
		require.Equal(t, original, decoded, "Base64解码后应该与原始字符串相同")
	})
	
	// 测试十六进制编码解码
	t.Run("十六进制编码解码测试", func(t *testing.T) {
		original := "测试HEX编码"
		
		// 编码
		encoded, err := hexEncodeFunc(original)
		require.NoError(t, err, "十六进制编码应该不返回错误")
		
		// 解码
		decoded, err := hexDecodeFunc(encoded)
		require.NoError(t, err, "十六进制解码应该不返回错误")
		
		require.Equal(t, original, decoded, "十六进制解码后应该与原始字符串相同")
	})
	
	// 测试URL编码解码
	t.Run("URL编码解码测试", func(t *testing.T) {
		original := "你好 世界?name=测试&value=10%"
		
		// 编码
		encoded, err := urlEncodeFunc(original)
		require.NoError(t, err, "URL编码应该不返回错误")
		require.Contains(t, encoded, "%E4%BD%A0%E5%A5%BD", "URL编码应该正确处理中文字符")
		require.Contains(t, encoded, "%20", "URL编码应该正确处理空格")
		require.Contains(t, encoded, "%3F", "URL编码应该正确处理问号")
		require.Contains(t, encoded, "%25", "URL编码应该正确处理百分号")
		
		// 解码
		decoded, err := urlDecodeFunc(encoded)
		require.NoError(t, err, "URL解码应该不返回错误")
		require.Equal(t, original, decoded, "URL解码后应该与原始字符串相同")
	})
}

// TestStringFunctions 测试字符串操作函数的功能
func TestStringFunctions(t *testing.T) {
	// 初始化环境
	functions = nil
	Concat()
	Split()
	Join()
	Replace()
	ReplaceRegex()
	Trim()
	Contains()
	StartsWith()
	EndsWith()
	
	helper := HelperFunctions()
	concatFunc := helper["concat"]
	splitFunc := helper["split"]
	joinFunc := helper["join"]
	replaceFunc := helper["replace"]
	replaceRegexFunc := helper["replace_regex"]
	trimFunc := helper["trim"]
	containsFunc := helper["contains"]
	startsWithFunc := helper["starts_with"]
	endsWithFunc := helper["ends_with"]
	
	// 测试字符串连接
	t.Run("字符串连接测试", func(t *testing.T) {
		result, err := concatFunc("Hello", ", ", "World", "!")
		require.NoError(t, err, "连接字符串应该不返回错误")
		require.Equal(t, "Hello, World!", result, "连接结果不符合预期")
	})
	
	// 测试字符串分割
	t.Run("字符串分割测试", func(t *testing.T) {
		// 按分隔符分割
		result1, err := splitFunc("a,b,c,d", ",")
		require.NoError(t, err, "分割字符串应该不返回错误")
		require.Equal(t, []string{"a", "b", "c", "d"}, result1, "分割结果不符合预期")
		
		// 按长度分割
		result2, err := splitFunc("abcdefgh", "2")
		require.NoError(t, err, "按长度分割字符串应该不返回错误")
		require.Equal(t, []string{"ab", "cd", "ef", "gh"}, result2, "按长度分割结果不符合预期")
	})
	
	// 测试字符串连接（使用分隔符）
	t.Run("字符串Join测试", func(t *testing.T) {
		result, err := joinFunc("-", "a", "b", "c")
		require.NoError(t, err, "Join字符串应该不返回错误")
		require.Equal(t, "a-b-c", result, "Join结果不符合预期")
	})
	
	// 测试字符串替换
	t.Run("字符串替换测试", func(t *testing.T) {
		// 普通替换
		result1, err := replaceFunc("hello world", "world", "golang")
		require.NoError(t, err, "替换字符串应该不返回错误")
		require.Equal(t, "hello golang", result1, "替换结果不符合预期")
		
		// 正则替换
		result2, err := replaceRegexFunc("hello 123 world 456", "[0-9]+", "NUM")
		require.NoError(t, err, "正则替换字符串应该不返回错误")
		require.Equal(t, "hello NUM world NUM", result2, "正则替换结果不符合预期")
	})
	
	// 测试字符串修剪
	t.Run("字符串修剪测试", func(t *testing.T) {
		result, err := trimFunc("  hello world  ", " ")
		require.NoError(t, err, "修剪字符串应该不返回错误")
		require.Equal(t, "hello world", result, "修剪结果不符合预期")
	})
	
	// 测试字符串包含检查
	t.Run("字符串包含测试", func(t *testing.T) {
		result, err := containsFunc("hello world", "world")
		require.NoError(t, err, "检查字符串包含应该不返回错误")
		require.Equal(t, true, result, "字符串包含检查结果不符合预期")
		
		result, err = containsFunc("hello world", "golang")
		require.NoError(t, err, "检查字符串包含应该不返回错误")
		require.Equal(t, false, result, "字符串包含检查结果不符合预期")
	})
	
	// 测试前缀后缀检查
	t.Run("前缀后缀测试", func(t *testing.T) {
		// 前缀检查
		resultPrefix, err := startsWithFunc("hello world", "hello")
		require.NoError(t, err, "检查字符串前缀应该不返回错误")
		require.Equal(t, true, resultPrefix, "前缀检查结果不符合预期")
		
		// 后缀检查
		resultSuffix, err := endsWithFunc("hello world", "world")
		require.NoError(t, err, "检查字符串后缀应该不返回错误")
		require.Equal(t, true, resultSuffix, "后缀检查结果不符合预期")
	})
}

// TestStringOperationFunctions 测试字符串操作函数的功能
func TestStringOperationFunctions(t *testing.T) {
	// 初始化环境
	functions = nil
	Index()
	Len()
	ToUpper()
	ToLower()
	Sort()
	Trim()
	TrimLeft()
	TrimRight()
	TrimSpace()
	TrimPrefix()
	TrimSuffix()
	Replace()
	ReplaceRegex()
	Reverse()
	
	helper := HelperFunctions()
	indexFunc := helper["index"]
	lenFunc := helper["len"]
	toUpperFunc := helper["to_upper"]
	toLowerFunc := helper["to_lower"]
	sortFunc := helper["sort"]
	trimFunc := helper["trim"]
	trimLeftFunc := helper["trim_left"]
	trimRightFunc := helper["trim_right"]
	trimSpaceFunc := helper["trim_space"]
	trimPrefixFunc := helper["trim_prefix"]
	trimSuffixFunc := helper["trim_suffix"]
	replaceFunc := helper["replace"]
	replaceRegexFunc := helper["replace_regex"]
	reverseFunc := helper["reverse"]
	
	// 测试Index函数
	t.Run("Index函数测试", func(t *testing.T) {
		// 字符串索引测试
		result1, err := indexFunc("Hello", "0")
		require.NoError(t, err, "索引字符串应该不返回错误")
		require.Equal(t, "H", result1, "索引结果不符合预期")
		
		// 切片索引测试
		strSlice := []string{"Apple", "Banana", "Cherry"}
		result2, err := indexFunc(strSlice, "1")
		require.NoError(t, err, "索引切片应该不返回错误")
		require.Equal(t, "Banana", result2, "索引结果不符合预期")
		
		// 越界索引测试
		_, err = indexFunc("Hello", "10")
		require.Error(t, err, "越界索引应该返回错误")
	})
	
	// 测试Len函数
	t.Run("Len函数测试", func(t *testing.T) {
		// 字符串长度测试
		result1, err := lenFunc("Hello, 世界")
		require.NoError(t, err, "获取字符串长度应该不返回错误")
		require.Equal(t, float64(13), result1, "字符串长度结果不符合预期")
		
		// 空字符串长度测试
		result2, err := lenFunc("")
		require.NoError(t, err, "获取空字符串长度应该不返回错误")
		require.Equal(t, float64(0), result2, "空字符串长度结果不符合预期")
	})
	
	// 测试ToUpper和ToLower函数
	t.Run("大小写转换函数测试", func(t *testing.T) {
		testString := "Hello, World!"
		
		// 转大写测试
		result1, err := toUpperFunc(testString)
		require.NoError(t, err, "转换大写应该不返回错误")
		require.Equal(t, "HELLO, WORLD!", result1, "转换大写结果不符合预期")
		
		// 转小写测试
		result2, err := toLowerFunc(testString)
		require.NoError(t, err, "转换小写应该不返回错误")
		require.Equal(t, "hello, world!", result2, "转换小写结果不符合预期")
	})
	
	// 测试Sort函数
	t.Run("Sort函数测试", func(t *testing.T) {
		// 字符串字符排序测试
		result1, err := sortFunc("dcba")
		require.NoError(t, err, "排序字符串字符应该不返回错误")
		require.Equal(t, "abcd", result1, "字符排序结果不符合预期")
		
		// 多参数排序测试
		result2, err := sortFunc("banana", "apple", "cherry")
		require.NoError(t, err, "排序多个参数应该不返回错误")
		require.ElementsMatch(t, []string{"apple", "banana", "cherry"}, result2, "多参数排序结果不符合预期")
	})
	
	// 测试Trim函数系列
	t.Run("Trim函数系列测试", func(t *testing.T) {
		// Trim测试
		result1, err := trimFunc("  Hello  ", " ")
		require.NoError(t, err, "Trim应该不返回错误")
		require.Equal(t, "Hello", result1, "Trim结果不符合预期")
		
		// TrimLeft测试
		result2, err := trimLeftFunc("  Hello  ", " ")
		require.NoError(t, err, "TrimLeft应该不返回错误")
		require.Equal(t, "Hello  ", result2, "TrimLeft结果不符合预期")
		
		// TrimRight测试
		result3, err := trimRightFunc("  Hello  ", " ")
		require.NoError(t, err, "TrimRight应该不返回错误")
		require.Equal(t, "  Hello", result3, "TrimRight结果不符合预期")
		
		// TrimSpace测试
		result4, err := trimSpaceFunc("  Hello\t\n  ")
		require.NoError(t, err, "TrimSpace应该不返回错误")
		require.Equal(t, "Hello", result4, "TrimSpace结果不符合预期")
		
		// TrimPrefix测试
		result5, err := trimPrefixFunc("HelloWorld", "Hello")
		require.NoError(t, err, "TrimPrefix应该不返回错误")
		require.Equal(t, "World", result5, "TrimPrefix结果不符合预期")
		
		// TrimSuffix测试
		result6, err := trimSuffixFunc("HelloWorld", "World")
		require.NoError(t, err, "TrimSuffix应该不返回错误")
		require.Equal(t, "Hello", result6, "TrimSuffix结果不符合预期")
	})
	
	// 测试Replace函数系列
	t.Run("Replace函数系列测试", func(t *testing.T) {
		// Replace测试
		result1, err := replaceFunc("Hello, World!", "World", "Go")
		require.NoError(t, err, "Replace应该不返回错误")
		require.Equal(t, "Hello, Go!", result1, "Replace结果不符合预期")
		
		// ReplaceRegex测试
		result2, err := replaceRegexFunc("Hello, 123!", "[0-9]+", "数字")
		require.NoError(t, err, "ReplaceRegex应该不返回错误")
		require.Equal(t, "Hello, 数字!", result2, "ReplaceRegex结果不符合预期")
	})
	
	// 测试Reverse函数
	t.Run("Reverse函数测试", func(t *testing.T) {
		result, err := reverseFunc("Hello")
		require.NoError(t, err, "Reverse应该不返回错误")
		require.Equal(t, "olleH", result, "Reverse结果不符合预期")
	})
}


// TestCollectionFunctions 测试集合操作函数的功能
func TestCollectionFunctions(t *testing.T) {
	// 初始化环境
	functions = nil
	Uniq()
	Split()
	Join()
	Concat()
	
	helper := HelperFunctions()
	uniqFunc := helper["uniq"]
	splitFunc := helper["split"]
	joinFunc := helper["join"]
	concatFunc := helper["concat"]
	
	// 测试Uniq函数
	t.Run("Uniq函数测试", func(t *testing.T) {
		// 字符串字符去重测试
		result1, err := uniqFunc("aabbcc")
		require.NoError(t, err, "去除字符串中的重复字符应该不返回错误")
		require.Equal(t, "abc", result1, "字符去重结果不符合预期")
		
		// 多参数去重测试
		result2, err := uniqFunc("apple", "banana", "apple", "cherry", "banana")
		require.NoError(t, err, "去除多个参数中的重复元素应该不返回错误")
		require.ElementsMatch(t, []string{"apple", "banana", "cherry"}, result2, "多参数去重结果不符合预期")
	})
	
	// 测试Split函数
	t.Run("Split函数测试", func(t *testing.T) {
		// 按分隔符分割测试
		result1, err := splitFunc("apple,banana,cherry", ",")
		require.NoError(t, err, "按分隔符分割应该不返回错误")
		require.Equal(t, []string{"apple", "banana", "cherry"}, result1, "分割结果不符合预期")
		
		// 按等长分割测试
		result2, err := splitFunc("HelloWorld", "5")
		require.NoError(t, err, "按等长分割应该不返回错误")
		require.Equal(t, []string{"Hello", "World"}, result2, "等长分割结果不符合预期")
		
		// 限制分割次数测试
		result3, err := splitFunc("one,two,three,four", ",", "2")
		require.NoError(t, err, "限制分割次数应该不返回错误")
		require.Equal(t, []string{"one","two,three,four"}, result3, "限制分割次数结果不符合预期")
	})
	
	// 测试Join函数
	t.Run("Join函数测试", func(t *testing.T) {
		// 连接多个参数测试
		result1, err := joinFunc(",", "apple", "banana", "cherry")
		require.NoError(t, err, "连接多个参数应该不返回错误")
		require.Equal(t, "apple,banana,cherry", result1, "连接结果不符合预期")
		
		// 连接字符串数组测试
		strArray := []string{"apple", "banana", "cherry"}
		result2, err := joinFunc("-", strArray)
		require.NoError(t, err, "连接字符串数组应该不返回错误")
		require.Equal(t, "apple-banana-cherry", result2, "连接结果不符合预期")
	})
	
	// 测试Concat函数
	t.Run("Concat函数测试", func(t *testing.T) {
		result, err := concatFunc("Hello", ", ", "World", "!")
		require.NoError(t, err, "连接字符串应该不返回错误")
		require.Equal(t, "Hello, World!", result, "连接结果不符合预期")
	})
}

// TestMatcherFunctions 测试匹配和比较函数的功能
func TestMatcherFunctions(t *testing.T) {
	// 初始化环境
	functions = nil
	Contains()
	ContainsAny()
	ContainsAll()
	StartsWith()
	EndsWith()
	Reg()
	
	helper := HelperFunctions()
	containsFunc := helper["contains"]
	// containsAnyFunc := helper["contains_any"]
	containsAllFunc := helper["contains_all"]
	startsWithFunc := helper["starts_with"]
	endsWithFunc := helper["ends_with"]
	regexFunc := helper["regex"]
	
	// 测试Contains函数
	t.Run("Contains函数测试", func(t *testing.T) {
		result1, err := containsFunc("Hello, World!", "World")
		require.NoError(t, err, "检查字符串包含应该不返回错误")
		require.True(t, result1.(bool), "字符串应该包含子串")
		
		result2, err := containsFunc("Hello, World!", "Python")
		require.NoError(t, err, "检查字符串包含应该不返回错误")
		require.False(t, result2.(bool), "字符串不应该包含子串")
	})
	
	// 测试ContainsAll函数
	t.Run("ContainsAll函数测试", func(t *testing.T) {
		result1, err := containsAllFunc("Hello, World!", "Hello", "World")
		require.NoError(t, err, "检查字符串包含全部子串应该不返回错误")
		require.True(t, result1.(bool), "字符串应该包含全部子串")
		
		result2, err := containsAllFunc("Hello, World!", "Hello", "Python")
		require.NoError(t, err, "检查字符串包含全部子串应该不返回错误")
		require.False(t, result2.(bool), "字符串不包含全部子串")
	})
	
	// 测试StartsWith和EndsWith函数
	t.Run("StartsWith和EndsWith函数测试", func(t *testing.T) {
		// StartsWith测试
		result1, err := startsWithFunc("Hello, World!", "Hello")
		require.NoError(t, err, "检查字符串开头应该不返回错误")
		require.True(t, result1.(bool), "字符串应该以指定前缀开头")
		
		result2, err := startsWithFunc("Hello, World!", "World")
		require.NoError(t, err, "检查字符串开头应该不返回错误")
		require.False(t, result2.(bool), "字符串不应该以指定前缀开头")
		
		// EndsWith测试
		result3, err := endsWithFunc("Hello, World!", "World!")
		require.NoError(t, err, "检查字符串结尾应该不返回错误")
		require.True(t, result3.(bool), "字符串应该以指定后缀结尾")
		
		result4, err := endsWithFunc("Hello, World!", "Hello")
		require.NoError(t, err, "检查字符串结尾应该不返回错误")
		require.False(t, result4.(bool), "字符串不应该以指定后缀结尾")
	})
	
	// 测试正则表达式函数
	t.Run("正则表达式函数测试", func(t *testing.T) {
		// 测试数字匹配
		result1, err := regexFunc("[0-9]+", "abc123def")
		require.NoError(t, err, "正则表达式匹配应该不返回错误")
		require.True(t, result1.(bool), "字符串应该匹配正则表达式")
		
		// 测试字母匹配
		result2, err := regexFunc("^[a-z]+$", "123")
		require.NoError(t, err, "正则表达式匹配应该不返回错误")
		require.False(t, result2.(bool), "字符串不应该匹配正则表达式")
		
		// 测试无效正则表达式
		_, err = regexFunc("[无效的正则表达式", "test")
		require.Error(t, err, "无效的正则表达式应该返回错误")
	})
} 


// TestHashFunctions 测试哈希函数的功能
func TestHashFunctions(t *testing.T) {
	// 初始化环境
	functions = nil
	MD5()
	Sha1()
	Sha256()
	Sha512()
	Hmac()
	
	helper := HelperFunctions()
	md5Func := helper["md5"]
	sha1Func := helper["sha1"]
	sha256Func := helper["sha256"]
	sha512Func := helper["sha512"]
	hmacFunc := helper["hmac"]
	
	// 测试MD5函数
	t.Run("MD5函数测试", func(t *testing.T) {
		testString := "测试字符串"
		expected := "1f3ca051028d1d1e95a6f4e269d727ab"
		
		result, err := md5Func(testString)
		require.NoError(t, err, "计算MD5哈希应该不返回错误")
		require.Equal(t, expected, result, "生成的MD5哈希值不符合预期")
	})
	
	// 测试SHA1函数
	t.Run("SHA1函数测试", func(t *testing.T) {
		testString := "测试字符串"
		expected := "5f633e42db2cd5dbcb1087ae6e2f50237a90668c"
		
		result, err := sha1Func(testString)
		require.NoError(t, err, "计算SHA1哈希应该不返回错误")
		require.Equal(t, expected, result, "生成的SHA1哈希值不符合预期")
	})
	
	// 测试SHA256函数
	t.Run("SHA256函数测试", func(t *testing.T) {
		testString := "测试字符串"
	
		
		result, err := sha256Func(testString)
		require.NoError(t, err, "计算SHA256哈希应该不返回错误")
		require.Len(t, result, 64, "SHA256哈希值应该是64个十六进制字符")
	})
	
	// 测试SHA512函数
	t.Run("SHA512函数测试", func(t *testing.T) {
		testString := "测试字符串"
		
		result, err := sha512Func(testString)
		require.NoError(t, err, "计算SHA512哈希应该不返回错误")
		require.Len(t, result, 128, "SHA512哈希值应该是128个十六进制字符")
	})
	
	// 测试HMAC函数
	t.Run("HMAC函数测试", func(t *testing.T) {
		data := "测试数据"
		key := "密钥"
		
		// 测试SHA1 HMAC
		sha1Result, err := hmacFunc("sha1", data, key)
		require.NoError(t, err, "使用SHA1计算HMAC应该不返回错误")
		require.NotEmpty(t, sha1Result, "生成的HMAC哈希值不应为空")
		
		// 测试SHA256 HMAC
		sha256Result, err := hmacFunc("sha256", data, key)
		require.NoError(t, err, "使用SHA256计算HMAC应该不返回错误")
		require.NotEmpty(t, sha256Result, "生成的HMAC哈希值不应为空")
		
		// 测试SHA512 HMAC
		sha512Result, err := hmacFunc("sha512", data, key)
		require.NoError(t, err, "使用SHA512计算HMAC应该不返回错误")
		require.NotEmpty(t, sha512Result, "生成的HMAC哈希值不应为空")
		
		// 测试不支持的算法
		_, err = hmacFunc("不支持的算法", data, key)
		require.Error(t, err, "使用不支持的算法应该返回错误")
	})
}

// TestCryptoFunctions 测试加密和压缩函数的功能
func TestCryptoFunctions(t *testing.T) {
	// 初始化环境
	functions = nil
	XOR()
	AesCBC()
	Gzip()
	GzipDecode()
	Zlib()
	ZlibDecode()
	Deflate()
	Inflate()
	
	helper := HelperFunctions()
	xorFunc := helper["xor"]
	gzipFunc := helper["gzip"]
	gzipDecodeFunc := helper["gzip_decode"]
	zlibFunc := helper["zlib"]
	zlibDecodeFunc := helper["zlib_decode"]
	deflateFunc := helper["deflate"]
	inflateFunc := helper["inflate"]
	
	// 测试XOR函数
	t.Run("XOR函数测试", func(t *testing.T) {
		// 对两个相同的字符串进行XOR操作应得到全为0的结果
		testString := "测试XOR运算"
		
		result, err := xorFunc(testString, testString)
		require.NoError(t, err, "对两个相同字符串进行XOR运算应该不返回错误")
		
		// 检查结果是否全为0
		resultBytes := result.([]byte)
		for _, b := range resultBytes {
			require.Equal(t, byte(0), b, "对相同字符串XOR的结果应该是全0")
		}
		
		// 测试长度不同的参数
		_, err = xorFunc("short", "longer string")
		require.Error(t, err, "对长度不同的参数进行XOR运算应该返回错误")
	})
	
	// 测试压缩和解压缩函数
	t.Run("压缩解压缩函数测试", func(t *testing.T) {
		testData := "这是一段需要被压缩的测试数据，应该包含足够的长度以便看出压缩效果。" +
			"这是一段需要被压缩的测试数据，应该包含足够的长度以便看出压缩效果。" + 
			"这是一段需要被压缩的测试数据，应该包含足够的长度以便看出压缩效果。"
		
		// 测试GZIP压缩和解压缩
		gzipped, err := gzipFunc(testData)
		require.NoError(t, err, "GZIP压缩应该不返回错误")
		require.NotEmpty(t, gzipped, "压缩后的数据不应为空")
		
		unzipped, err := gzipDecodeFunc(gzipped)
		require.NoError(t, err, "GZIP解压缩应该不返回错误")
		require.Equal(t, testData, unzipped, "解压缩后的数据应与原数据相同")
		
		// 测试ZLIB压缩和解压缩
		zlibCompressed, err := zlibFunc(testData)
		require.NoError(t, err, "ZLIB压缩应该不返回错误")
		require.NotEmpty(t, zlibCompressed, "压缩后的数据不应为空")
		
		zlibDecompressed, err := zlibDecodeFunc(zlibCompressed)
		require.NoError(t, err, "ZLIB解压缩应该不返回错误")
		require.Equal(t, testData, zlibDecompressed, "解压缩后的数据应与原数据相同")
		
		// 测试Deflate压缩和解压缩
		deflated, err := deflateFunc(testData)
		require.NoError(t, err, "Deflate压缩应该不返回错误")
		require.NotEmpty(t, deflated, "压缩后的数据不应为空")
		
		inflated, err := inflateFunc(deflated)
		require.NoError(t, err, "Inflate解压缩应该不返回错误")
		require.Equal(t, testData, inflated, "解压缩后的数据应与原数据相同")
	})
} 


// TestTimeFunctions 测试时间相关函数的功能
func TestTimeFunctions(t *testing.T) {
	// 初始化环境
	functions = nil
	DateTime()
	UnixTime()
	ToUnixTime()
	WaitFor()
	
	helper := HelperFunctions()
	dateTimeFunc := helper["date_time"]
	unixTimeFunc := helper["unix_time"]
	toUnixTimeFunc := helper["to_unix_time"]
	waitForFunc := helper["wait_for"]
	
	// 测试DateTime函数
	t.Run("DateTime函数测试", func(t *testing.T) {
		// 测试当前时间格式化
		result1, err := dateTimeFunc("2006-01-02 15:04:05")
		require.NoError(t, err, "格式化当前时间应该不返回错误")
		_, err = time.Parse("2006-01-02 15:04:05", result1.(string))
		require.NoError(t, err, "格式化结果应该是有效的时间字符串")
		
		// 测试指定时间戳格式化
		result2, err := dateTimeFunc("2006-01-02", float64(1609459200)) // 2021-01-01 00:00:00
		require.NoError(t, err, "格式化指定时间应该不返回错误")
		require.Equal(t, "2021-01-01", result2, "格式化结果不符合预期")
		
		// 测试简化格式
		result3, err := dateTimeFunc("%Y-%m-%d", float64(1609459200)) // 2021-01-01 00:00:00
		require.NoError(t, err, "使用简化格式应该不返回错误")
		require.Equal(t, "2021-00-01", result3, "简化格式结果不符合预期")
		
		result4, err := dateTimeFunc("%Y-%m-%d %H:%M:%S", float64(1609459200)) // 2021-01-01 00:00:00
		require.NoError(t, err, "使用简化格式应该不返回错误")
		require.Equal(t, "2021-00-01 08:01:00", result4, "简化格式结果不符合预期")
	})
	
	// 测试UnixTime函数
	t.Run("UnixTime函数测试", func(t *testing.T) {
		// 获取当前时间戳
		now := time.Now().Unix()
		result1, err := unixTimeFunc()
		require.NoError(t, err, "获取当前时间戳应该不返回错误")
		// 允许1秒的误差
		require.InDelta(t, now, result1, 1, "获取的时间戳应该接近当前时间")
		
		// // 获取未来时间戳
		// futureTime := time.Now().Add(10 * time.Second).Unix()
		// result2, err := unixTimeFunc(10)
		// require.NoError(t, err, "获取未来时间戳应该不返回错误")
		// // 允许1秒的误差
		// require.InDelta(t, futureTime, result2, 1, "获取的未来时间戳不符合预期")
	})
	
	// 测试ToUnixTime函数
	t.Run("ToUnixTime函数测试", func(t *testing.T) {
		// 测试数字字符串转换
		result1, err := toUnixTimeFunc("1609459200")
		require.NoError(t, err, "数字字符串转换应该不返回错误")
		require.Equal(t, int64(1609459200), result1, "转换结果不符合预期")
		
		// 测试ISO格式时间字符串转换
		result2, err := toUnixTimeFunc("2021-01-01T00:00:00Z")
		require.NoError(t, err, "ISO格式时间字符串转换应该不返回错误")
		require.Equal(t, int64(1609459200), result2, "转换结果不符合预期")
		
		// 测试自定义格式时间字符串转换
		result3, err := toUnixTimeFunc("01/01/2021", "02/01/2006")
		require.NoError(t, err, "自定义格式时间字符串转换应该不返回错误")
		require.Equal(t, int64(1609459200), result3, "转换结果不符合预期")
		
		// 测试无效时间字符串
		_, err = toUnixTimeFunc("不是时间字符串")
		require.Error(t, err, "无效时间字符串应该返回错误")
	})
	
	// 测试WaitFor函数
	t.Run("WaitFor函数测试", func(t *testing.T) {
		// 测试短暂等待
		result, err := waitForFunc(0.1) // 等待0.1秒
		
		require.NoError(t, err, "等待函数应该不返回错误")
		require.True(t, result.(bool), "等待函数应该返回true")
	})
}

// TestConversionFunctions 测试各种数据类型转换函数的功能
func TestConversionFunctions(t *testing.T) {
	// 初始化环境
	functions = nil
	ToNumber()
	ToString()
	DecToHex()
	HexToDec()
	OctToDec()
	BinToDec()
	
	helper := HelperFunctions()
	toNumberFunc := helper["to_number"]
	toStringFunc := helper["to_string"]
	decToHexFunc := helper["dec_to_hex"]
	hexToDecFunc := helper["hex_to_dec"]
	octToDecFunc := helper["oct_to_dec"]
	binToDecFunc := helper["bin_to_dec"]
	
	// 测试ToNumber函数
	t.Run("ToNumber函数测试", func(t *testing.T) {
		// 测试整数转换
		result1, err := toNumberFunc("123")
		require.NoError(t, err, "整数字符串转换应该不返回错误")
		require.Equal(t, float64(123), result1, "整数转换结果不符合预期")
		
		// 测试浮点数转换
		result2, err := toNumberFunc("123.456")
		require.NoError(t, err, "浮点数字符串转换应该不返回错误")
		require.Equal(t, float64(123.456), result2, "浮点数转换结果不符合预期")
		
		// 测试无效数字字符串
		_, err = toNumberFunc("not a number")
		require.Error(t, err, "无效数字字符串应该返回错误")
	})
	
	// 测试ToString函数
	t.Run("ToString函数测试", func(t *testing.T) {
		// 测试数字转字符串
		result1, err := toStringFunc(123)
		require.NoError(t, err, "数字转字符串应该不返回错误")
		require.Equal(t, "123", result1, "数字转字符串结果不符合预期")
		
		// 测试布尔值转字符串
		result2, err := toStringFunc(true)
		require.NoError(t, err, "布尔值转字符串应该不返回错误")
		require.Equal(t, "true", result2, "布尔值转字符串结果不符合预期")
	})
	
	// 测试进制转换函数
	t.Run("进制转换函数测试", func(t *testing.T) {
		// 测试十进制转十六进制
		result1, err := decToHexFunc(float64(255))
		require.NoError(t, err, "十进制转十六进制应该不返回错误")
		require.Equal(t, "ff", result1, "十进制转十六进制结果不符合预期")
		
		// 测试十六进制转十进制
		result2, err := hexToDecFunc("ff")
		require.NoError(t, err, "十六进制转十进制应该不返回错误")
		require.Equal(t, float64(255), result2, "十六进制转十进制结果不符合预期")
		
		// 测试八进制转十进制
		result3, err := octToDecFunc("377")
		require.NoError(t, err, "八进制转十进制应该不返回错误")
		require.Equal(t, float64(255), result3, "八进制转十进制结果不符合预期")
		
		// 测试二进制转十进制
		result4, err := binToDecFunc("11111111")
		require.NoError(t, err, "二进制转十进制应该不返回错误")
		require.Equal(t, float64(255), result4, "二进制转十进制结果不符合预期")
	})
}

// TestMiscFunctions 测试其他辅助函数的功能
func TestMiscFunctions(t *testing.T) {
	// 初始化环境
	functions = nil
	CompareVersion()
	Padding()
	SubStr()
	
	helper := HelperFunctions()
	compareVersionFunc := helper["compare_versions"]
	paddingFunc := helper["padding"]
	subStrFunc := helper["substr"]
	
	// 测试版本比较函数
	t.Run("版本比较函数测试", func(t *testing.T) {
		// 测试相等版本
		result1, err := compareVersionFunc("1.2.3", "= 1.2.3")
		require.NoError(t, err, "相等版本比较应该不返回错误")
		require.True(t, result1.(bool), "相等版本比较结果不符合预期")
		
		// 测试大于版本
		result2, err := compareVersionFunc("1.2.3", "> 1.1.0")
		require.NoError(t, err, "大于版本比较应该不返回错误")
		require.True(t, result2.(bool), "大于版本比较结果不符合预期")
		
		// 测试小于版本
		result3, err := compareVersionFunc("1.2.3", "< 2.0.0")
		require.NoError(t, err, "小于版本比较应该不返回错误")
		require.True(t, result3.(bool), "小于版本比较结果不符合预期")
		
		// 测试版本范围
		result4, err := compareVersionFunc("1.2.3", ">= 1.0.0", "< 2.0.0")
		require.NoError(t, err, "版本范围比较应该不返回错误")
		require.True(t, result4.(bool), "版本范围比较结果不符合预期")
		
		// 测试不满足条件的版本
		result5, err := compareVersionFunc("3.0.0", ">= 1.0.0", "< 2.0.0")
		require.NoError(t, err, "不满足条件的版本比较应该不返回错误")
		require.False(t, result5.(bool), "不满足条件的版本比较结果不符合预期")
	})
	
	// 测试字符串填充函数
	t.Run("字符串填充函数测试", func(t *testing.T) {
		// 测试前缀填充
		result1, err := paddingFunc("123", "0", 5, "prefix")
		require.NoError(t, err, "前缀填充应该不返回错误")
		require.Equal(t, "00123", result1, "前缀填充结果不符合预期")
		
		// 测试后缀填充
		result2, err := paddingFunc("123", "0", 5, "suffix")
		require.NoError(t, err, "后缀填充应该不返回错误")
		require.Equal(t, "12300", result2, "后缀填充结果不符合预期")
		
		// 测试无需填充（长度已达到）
		result3, err := paddingFunc("12345", "0", 5, "prefix")
		require.NoError(t, err, "无需填充应该不返回错误")
		require.Equal(t, "12345", result3, "无需填充结果不符合预期")
		
		// 测试无效填充模式
		_, err = paddingFunc("123", "0", 5, "invalid")
		require.Error(t, err, "无效填充模式应该返回错误")
	})
	
	// 测试子字符串提取函数
	t.Run("子字符串提取函数测试", func(t *testing.T) {
		testString := "Hello, World!"
		
		// 测试指定起始位置
		result1, err := subStrFunc(testString, "7")
		require.NoError(t, err, "指定起始位置应该不返回错误")
		require.Equal(t, "World!", result1, "指定起始位置结果不符合预期")
		
		// 测试指定起始和结束位置
		result2, err := subStrFunc(testString, "0", "5")
		require.NoError(t, err, "指定起始和结束位置应该不返回错误")
		require.Equal(t, "Hello", result2, "指定起始和结束位置结果不符合预期")
		
		// 测试无效位置
		_, err = subStrFunc(testString, "100")
		require.Error(t, err, "无效起始位置应该返回错误")
		
		_, err = subStrFunc(testString, "0", "100")
		require.Error(t, err, "无效结束位置应该返回错误")
		
		_, err = subStrFunc(testString, "5", "2")
		require.Error(t, err, "结束位置小于起始位置应该返回错误")
	})
} 


// TestRandomCharFunctions 测试随机字符生成函数的功能
func TestRandomCharFunctions(t *testing.T) {
	// 初始化环境
	functions = nil
	RandChar()
	RandBase()
	RandTextAlp()
	RandTextAlpha()
	RandTextNumeric()
	
	helper := HelperFunctions()
	randCharFunc := helper["rand_char"]
	randBaseFunc := helper["rand_base"]
	randTextAlphanumericFunc := helper["rand_text_alphanumeric"]
	randTextAlphaFunc := helper["rand_text_alpha"]
	randTextNumericFunc := helper["rand_text_numeric"]
	
	// 测试RandChar函数
	t.Run("RandChar函数测试", func(t *testing.T) {
		// 默认字符集
		result1, err := randCharFunc()
		require.NoError(t, err, "生成随机字符应该不返回错误")
		require.Len(t, result1.(string), 1, "生成的随机字符长度应为1")
		require.Regexp(t, `^[a-zA-Z0-9]$`, result1, "生成的随机字符应该是字母或数字")
		
		// 自定义字符集
		result2, err := randCharFunc("abc")
		require.NoError(t, err, "使用自定义字符集应该不返回错误")
		require.Len(t, result2.(string), 1, "生成的随机字符长度应为1")
		require.Contains(t, "abc", result2, "生成的随机字符应该在自定义字符集中")
	})
	
	// 测试RandBase函数
	t.Run("RandBase函数测试", func(t *testing.T) {
		// 指定长度，默认字符集
		length := 10
		result1, err := randBaseFunc(float64(length))
		require.NoError(t, err, "生成随机字符串应该不返回错误")
		require.Len(t, result1.(string), length, "生成的随机字符串长度不符合预期")
		require.Regexp(t, `^[a-zA-Z0-9]+$`, result1, "生成的随机字符串应该只包含字母和数字")
		
		// 指定长度和字符集
		result2, err := randBaseFunc((float64(length)), "abcdef")
		require.NoError(t, err, "使用自定义字符集应该不返回错误")
		require.Len(t, result2.(string), length, "生成的随机字符串长度不符合预期")
		require.Regexp(t, `^[abcdef]+$`, result2, "生成的随机字符串应该只包含指定的字符")
	})
	
	// 测试RandTextAlphanumeric函数
	t.Run("RandTextAlphanumeric函数测试", func(t *testing.T) {
		// 指定长度，无需排除字符
		length := 15
		result1, err := randTextAlphanumericFunc(float64(length))
		require.NoError(t, err, "生成随机字母数字字符串应该不返回错误")
		require.Len(t, result1.(string), length, "生成的随机字符串长度不符合预期")
		require.Regexp(t, `^[a-zA-Z0-9]+$`, result1, "生成的随机字符串应该只包含字母和数字")
		
		// 指定长度，排除某些字符
		result2, err := randTextAlphanumericFunc(float64(length), "aeiou0123")
		require.NoError(t, err, "排除某些字符应该不返回错误")
		require.Len(t, result2.(string), length, "生成的随机字符串长度不符合预期")
		for _, c := range "aeiou0123" {
			require.NotContains(t, result2.(string), string(c), "生成的随机字符串不应包含被排除的字符")
		}
	})
	
	// 测试RandTextAlpha函数
	t.Run("RandTextAlpha函数测试", func(t *testing.T) {
		// 指定长度，无需排除字符
		length := 12
		result1, err := randTextAlphaFunc(float64(length))
		require.NoError(t, err, "生成随机字母字符串应该不返回错误")
		require.Len(t, result1.(string), length, "生成的随机字符串长度不符合预期")
		require.Regexp(t, `^[a-zA-Z]+$`, result1, "生成的随机字符串应该只包含字母")
		
		// 指定长度，排除某些字符
		result2, err := randTextAlphaFunc(float64(length), "aeiou")
		require.NoError(t, err, "排除某些字符应该不返回错误")
		require.Len(t, result2.(string), length, "生成的随机字符串长度不符合预期")
		require.Regexp(t, `^[bcdfghjklmnpqrstvwxyzBCDFGHJKLIMNOPQRSTVWXYZU]+$`, result2, "生成的随机字符串不应包含被排除的字符")
	})
	
	// 测试RandTextNumeric函数
	t.Run("RandTextNumeric函数测试", func(t *testing.T) {
		// 指定长度，无需排除字符
		length := 8
		result1, err := randTextNumericFunc(float64(length))
		require.NoError(t, err, "生成随机数字字符串应该不返回错误")
		require.Len(t, result1.(string), length, "生成的随机字符串长度不符合预期")
		require.Regexp(t, `^[0-9]+$`, result1, "生成的随机字符串应该只包含数字")
		
		// 指定长度，排除某些字符
		result2, err := randTextNumericFunc(float64(length), "0123")
		require.NoError(t, err, "排除某些字符应该不返回错误")
		require.Len(t, result2.(string), length, "生成的随机字符串长度不符合预期")
		require.Regexp(t, `^[456789]+$`, result2, "生成的随机字符串不应包含被排除的字符")
	})
}

// TestRandomIntFunctions 测试随机整数和IP生成函数的功能
func TestRandomIntFunctions(t *testing.T) {
	// 初始化环境
	functions = nil
	RandInt()
	
	helper := HelperFunctions()
	randIntFunc := helper["rand_int"]
	
	// 测试RandInt函数
	t.Run("RandInt函数测试", func(t *testing.T) {
		// 无参数（使用默认范围）
		result1, err := randIntFunc()
		require.NoError(t, err, "生成随机整数应该不返回错误")
		require.GreaterOrEqual(t, result1.(int), 0, "生成的随机整数应该大于等于0")
		require.Less(t, result1.(int), 2147483647, "生成的随机整数应该小于MaxInt32")
		
		// 指定最小值
		min := 100
		result2, err := randIntFunc(float64(min))
		require.NoError(t, err, "指定最小值应该不返回错误")
		require.GreaterOrEqual(t, result2.(int), min, "生成的随机整数应该大于等于最小值")
		
		// 指定最小值和最大值
		min = 100
		max := 200
		result3, err := randIntFunc(float64(min), float64(max))
		require.NoError(t, err, "指定范围应该不返回错误")
		require.GreaterOrEqual(t, result3.(int), min, "生成的随机整数应该大于等于最小值")
		require.Less(t, result3.(int), max, "生成的随机整数应该小于最大值")
	})
}

// TestRandomStringPatternsAndDistribution 测试随机字符串的模式和分布
func TestRandomStringPatternsAndDistribution(t *testing.T) {
	// 初始化环境
	functions = nil
	RandTextAlp()
	RandTextAlpha()
	RandTextNumeric()
	
	helper := HelperFunctions()
	randTextAlphanumericFunc := helper["rand_text_alphanumeric"]
	randTextAlphaFunc := helper["rand_text_alpha"]
	randTextNumericFunc := helper["rand_text_numeric"]
	
	// 测试多次生成的结果是否不同（随机性）
	t.Run("随机性测试", func(t *testing.T) {
		length := 10
		// 生成两次随机字母数字字符串
		result1, err1 := randTextAlphanumericFunc(float64(length))
		result2, err2 := randTextAlphanumericFunc(float64(length))
		
		require.NoError(t, err1, "第一次生成应该不返回错误")
		require.NoError(t, err2, "第二次生成应该不返回错误")
		require.NotEqual(t, result1, result2, "两次生成的随机字符串应该不同")
		
		// 生成两次随机字母字符串
		result3, err3 := randTextAlphaFunc(float64(length))
		result4, err4 := randTextAlphaFunc(float64(length))
		
		require.NoError(t, err3, "第一次生成应该不返回错误")
		require.NoError(t, err4, "第二次生成应该不返回错误")
		require.NotEqual(t, result3, result4, "两次生成的随机字符串应该不同")
		
		// 生成两次随机数字字符串
		result5, err5 := randTextNumericFunc(float64(length))
		result6, err6 := randTextNumericFunc(float64(length))
		
		require.NoError(t, err5, "第一次生成应该不返回错误")
		require.NoError(t, err6, "第二次生成应该不返回错误")
		require.NotEqual(t, result5, result6, "两次生成的随机字符串应该不同")
	})
	
	// 测试字符分布
	t.Run("字符分布测试", func(t *testing.T) {
		// 生成足够长的随机字符串，检查是否包含所有可能的字符
		length := 1000
		
		// 字母数字字符串
		alphanumResult, err := randTextAlphanumericFunc(float64(length))
		require.NoError(t, err, "生成随机字母数字字符串应该不返回错误")
		
		// 检查是否存在字母和数字
		hasLowercase, _ := regexp.MatchString("[a-z]", alphanumResult.(string))
		hasUppercase, _ := regexp.MatchString("[A-Z]", alphanumResult.(string))
		hasDigit, _ := regexp.MatchString("[0-9]", alphanumResult.(string))
		
		require.True(t, hasLowercase, "随机字母数字字符串应包含小写字母")
		require.True(t, hasUppercase, "随机字母数字字符串应包含大写字母")
		require.True(t, hasDigit, "随机字母数字字符串应包含数字")
	})
} 