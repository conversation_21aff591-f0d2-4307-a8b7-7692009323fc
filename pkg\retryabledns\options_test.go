//
//Author: chenjb
//Version: V1.0
//Date: 2025-04-21 19:57:56
//FilePath: /yaml_scan/pkg/retryabledns/options_test.go
//Description:

package retryabledns

import (
	"net"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestOptionsValidate(t *testing.T) {
	tests := []struct {
		name    string
		options Options
		wantErr error
	}{
		{
			name: "valid options",
			options: Options{
				BaseResolvers: []string{"*******"},
				MaxRetries:    1,
			},
			wantErr: nil,
		},
		{
			name: "max retries zero",
			options: Options{
				BaseResolvers: []string{"*******"},
				MaxRetries:    0,
			},
			wantErr: ErrMaxRetriesZero,
		},
		{
			name: "empty resolvers",
			options: Options{
				BaseResolvers: []string{},
				MaxRetries:    1,
			},
			wantErr: ErrResolversEmpty,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.options.Validate()
			if tt.wantErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.wantErr, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

// TestGetLocalAddr 测试 GetLocalAddr 方法的正确性
func TestGetLocalAddr(t *testing.T) {
	options := Options{
		LocalAddrIP:   net.ParseIP("***********00"), // 示例 IP
		LocalAddrPort: 8080,                         // 示例端口
	}

	// 测试获取 UDP 地址
	udpAddr := options.GetLocalAddr(UDP)
	require.NotNil(t, udpAddr)                               // 断言返回的地址不为 nil
	require.Equal(t, "***********00:8080", udpAddr.String()) // 断言地址字符串是否正确

	// 测试获取 TCP 地址
	tcpAddr := options.GetLocalAddr(TCP)
	require.NotNil(t, tcpAddr)                               // 断言返回的地址不为 nil
	require.Equal(t, "***********00:8080", tcpAddr.String()) // 断言地址字符串是否正确

	// 测试 LocalAddrIP 为 nil 的情况
	options.LocalAddrIP = nil
	nilAddr := options.GetLocalAddr(UDP)
	require.Nil(t, nilAddr) // 断言返回的地址为 nil
}

// TestSetLocalAddrIP 测试 SetLocalAddrIP 函数
func TestSetLocalAddrIP(t *testing.T) {
	tests := []struct {
		name     string
		ip       string
		expected net.IP
	}{
		{
			name:     "Valid IP",
			ip:       "***********",
			expected: net.ParseIP("***********"),
		},
		{
			name:     "Invalid IP",
			ip:       "invalid_ip",
			expected: nil, // 解析无效 IP 应该返回 nil
		},
		{
			name:     "Empty IP",
			ip:       "",
			expected: nil, // 空字符串应返回 nil
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			options := &Options{}
			options.SetLocalAddrIP(tt.ip)
			require.Equal(t, tt.expected, options.LocalAddrIP)
		})
	}
}

// TestSetLocalAddrIPFromNetInterface 测试 SetLocalAddrIPFromNetInterface 函数
func TestSetLocalAddrIPFromNetInterface(t *testing.T) {
	tests := []struct {
		name       string
		ifaceName  string
		expectErr  bool
		expectedIP net.IP
	}{
		{
			name:      "Valid Interface",
			ifaceName: "ens18", // 通常 "lo" 接口会有 IP 地址
			expectErr: false,
		},
		{
			name:      "Invalid Interface",
			ifaceName: "invalid_iface",
			expectErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			options := &Options{}
			err := options.SetLocalAddrIPFromNetInterface(tt.ifaceName)

			if tt.expectErr {
				require.Error(t, err) // 期望返回错误
			} else {
				require.NoError(t, err)                                          // 期望没有错误
				require.NotNil(t, options.LocalAddrIP) // 期望 LocalAddrIP 不为 nil
			}
		})
	}
}
