// Author: chenjb
// Version: V1.0
// Date: 2025-05-20 16:32:59
// FilePath: /yaml_scan/pkg/fastdialer/options.go
// Description:
package fastdialer

import (
	"log"
	"net"
	"time"

	"yaml_scan/pkg/networkpolicy"

	"golang.org/x/net/proxy"
)

// CacheType 定义缓存类型的枚举
type CacheType uint8

// DiskDBType 定义磁盘数据库类型的枚举
type DiskDBType uint8

const (
	LevelDB DiskDBType = iota // 使用LevelDB存储
	Pogreb                    // 使用Pogreb存储
)

const (
	Memory CacheType = iota // 仅内存缓存
	Disk                    // 仅磁盘缓存
	Hybrid                  // 内存和磁盘混合缓存
)

// Options 定义fastdialer的配置选项
type Options struct {
	BaseResolvers                   []string                        // 基础DNS解析服务器列表
	MaxRetries                      int                             // 连接失败时的最大重试次数
	HostsFile                       bool                            // 是否读取系统hosts文件
	ResolversFile                   bool                            // 是否读取resolv.conf文件
	EnableFallback                  bool                            // 是否启用备用DNS服务器
	Allow                           []string                        // 允许连接的IP或CIDR范围列表
	Deny                            []string                        // 禁止连接的IP或CIDR范围列表
	AllowSchemeList                 []string                        // 允许的URI方案列表（如http, https）
	DenySchemeList                  []string                        // 禁止的URI方案列表
	AllowPortList                   []int                           // 允许的端口列表
	DenyPortList                    []int                           // 禁止的端口列表
	CacheType                       CacheType                       // 缓存类型（内存、磁盘或混合）
	CacheMemoryMaxItems             int                             // used by Memory cache type
	DiskDbType                      DiskDBType                      // 磁盘数据库类型（用于磁盘缓存）
	WithDialerHistory               bool                            // 是否保留拨号历史记录
	WithCleanup                     bool                            // 是否在关闭时清理资源
	WithTLSData                     bool                            // 是否收集TLS连接数据
	DialerTimeout                   time.Duration                   // 拨号超时时间
	DialerKeepAlive                 time.Duration                   // 连接保持活跃的时间
	Dialer                          *net.Dialer                     // 自定义拨号器
	ProxyDialer                     *proxy.Dialer                   // 代理拨号器
	WithZTLS                        bool                            // 是否使用ZTLS库
	SNIName                         string                          // 默认的SNI名称
	OnBeforeDial                    func(hostname, IP, port string) // 拨号前的回调函数
	OnInvalidTarget                 func(hostname, IP, port string) // 无效目标的回调函数
	OnDialCallback                  func(hostname, IP string)       // 拨号后的回调函数
	DisableZtlsFallback             bool                            // 是否禁用ZTLS回退
	WithNetworkPolicyOptions        *networkpolicy.Options          // 网络策略选项
	NetworkPolicy                   *networkpolicy.NetworkPolicy    // 可选的网络策略覆盖，用于共享
	Logger                          *log.Logger                     // 可选的日志记录器，用于记录错误（如hosts文件初始化错误）
	MaxTemporaryErrors              int                             // 临时错误的最大次数，超过后标记为永久错误
	MaxTemporaryToPermanentDuration time.Duration                   // 临时错误转为永久错误的最长持续时间
}

// DefaultResolvers 默认DNS解析服务器列表
var DefaultResolvers = []string{
	"*******:53",
	"*******:53",
	"*******:53",
	"*******:53",
}

// DefaultOptions 默认的配置选项
var DefaultOptions = Options{
	BaseResolvers:                   DefaultResolvers, // 使用默认的DNS解析服务器
	MaxRetries:                      5,                // 最大重试5次
	HostsFile:                       true,             // 读取系统hosts文件
	ResolversFile:                   true,             // 读取resolv.conf文件
	CacheType:                       Disk,             // 使用磁盘缓存
	DialerTimeout:                   60 * time.Second, // 拨号超时10秒
	DialerKeepAlive:                 60 * time.Second, // 连接保持活跃10秒
	MaxTemporaryErrors:              30,               // 最多30次临时错误后判定为永久错误
	MaxTemporaryToPermanentDuration: time.Minute,      // 临时错误持续1分钟后转为永久错误
	EnableFallback:                  true,
}
