//
// Author: chenjb
// Version: V1.0
// Date: 2025-07-22 19:21:12
// FilePath: /yaml_scan/pkg/rawhttp/clientpipeline/response.go
// Description: HTTP响应结构定义和解析功能，支持管道化响应的读取和处理

// Package clientpipeline HTTP响应处理模块
package clientpipeline

import (
	"bufio"              // 缓冲I/O包
	"bytes"              // 字节操作包
	"fmt"                // 格式化输出包
	"io"                 // 输入输出接口包
	"net/http/httputil"  // HTTP工具包
	"strconv"            // 字符串转换包
	"strings"            // 字符串处理包
)

// Response 表示符合RFC2616标准的HTTP响应
// 包含响应的所有组成部分：版本、状态、头部、响应体
type Response struct {
	Version         // 嵌入HTTP版本信息
	Status          // 嵌入HTTP状态信息
	Headers []Header // HTTP头部数组
	body    []byte   // 响应体字节数组（内部缓存）
	Body    io.Reader // 响应体读取器
}

// ContentLength 返回响应体的长度
// 返回:
//   int64: 响应体长度，如果长度未知则返回-1
// 功能: 从Content-Length头部解析响应体长度
func (r *Response) ContentLength() int64 {
	// 遍历所有头部查找Content-Length
	for _, h := range r.Headers {
		if strings.EqualFold(h.Key, "Content-Length") {
			// 解析Content-Length值为整数
			length, err := strconv.ParseInt(h.Value, 10, 64)
			if err != nil {
				continue // 解析失败继续查找下一个
			}
			return int64(length) // 返回解析得到的长度
		}
	}
	return -1 // 未找到或解析失败返回-1
}

// CloseRequested 检查响应是否包含Connection: close头部
// 返回:
//   bool: 如果包含Connection: close则返回true，否则返回false
// 功能: 判断服务器是否请求关闭连接
func (r *Response) CloseRequested() bool {
	// 遍历所有头部查找Connection头部
	for _, h := range r.Headers {
		if strings.EqualFold(h.Key, "Connection") {
			return h.Value == "close" // 检查值是否为"close"
		}
	}
	return false // 未找到Connection: close头部
}

// TransferEncoding 返回消息传输时使用的传输编码
// 返回:
//   string: 传输编码类型，如果未指定则假定为"identity"
// 功能: 从Transfer-Encoding头部获取传输编码方式
func (r *Response) TransferEncoding() string {
	// 遍历所有头部查找Transfer-Encoding
	for _, h := range r.Headers {
		if strings.EqualFold(h.Key, "Transfer-Encoding") {
			switch h.Value {
			case "identity", "chunked":
				return h.Value // 返回支持的编码类型
			}
		}
	}
	return "identity" // 默认返回identity编码
}

// Status 表示HTTP状态码
// 包含状态码数字和原因短语
type Status struct {
	Code   int    // 状态码数字（如200、404、500等）
	Reason string // 状态原因短语（如"OK"、"Not Found"、"Internal Server Error"等）
}

// String 返回状态的字符串表示
// 返回:
//   string: 格式为"状态码 原因短语"的字符串
// 功能: 实现fmt.Stringer接口，提供状态的可读表示
func (s Status) String() string {
	return fmt.Sprintf("%d %s", s.Code, s.Reason)
}

// IsRedirect 检查是否为重定向状态码（3xx）
// 返回:
//   bool: 如果是3xx状态码则返回true，否则返回false
// 功能: 判断状态码是否在300-399范围内
func (s Status) IsRedirect() bool {
	return s.Code >= 300 && s.Code < 400
}

// IsSuccess 检查是否为成功状态码（2xx）
// 返回:
//   bool: 如果是2xx状态码则返回true，否则返回false
// 功能: 判断状态码是否在200-299范围内
func (s Status) IsSuccess() bool {
	return s.Code >= 200 && s.Code < 300
}

// IsError 检查是否为错误状态码（4xx或5xx）
// 返回:
//   bool: 如果是4xx或5xx状态码则返回true，否则返回false
// 功能: 判断状态码是否在400及以上范围内
func (s Status) IsError() bool {
	return s.Code >= 400
}

// Read 从缓冲读取器中读取完整的HTTP响应
// 参数:
//   r: 缓冲读取器
// 返回:
//   error: 读取错误，成功时为nil
// 功能: 解析HTTP响应的所有组成部分，包括状态行、头部和响应体
func (resp *Response) Read(r *bufio.Reader) error {
	// 读取状态行（HTTP版本、状态码、状态消息）
	version, code, msg, err := resp.ReadStatusLine(r)
	var headers []Header
	if err != nil {
		return fmt.Errorf("ReadStatusLine: %v", err)
	}
	// 循环读取所有响应头部
	for {
		var key, value string
		var done bool
		key, value, done, err = resp.ReadHeader(r) // 读取单个头部
		if err != nil || done {
			break // 读取完成或出错时退出循环
		}
		if key == "" {
			break // 空键名时退出
		}
		headers = append(headers, Header{key, value}) // 添加头部到数组
	}

	// 设置响应对象的各个字段
	resp.Version = version                          // 设置HTTP版本
	resp.Status = Status{Code: code, Reason: msg}   // 设置状态信息
	resp.Headers = headers                          // 设置头部数组
	resp.Body = resp.ReadBody(r)                    // 设置响应体读取器

	// 根据Content-Length或Transfer-Encoding处理响应体
	if l := resp.ContentLength(); l >= 0 {
		// 如果有明确的内容长度，限制读取长度
		resp.Body = io.LimitReader(resp.Body, l)
	} else if resp.TransferEncoding() == "chunked" {
		// 如果使用分块传输编码，使用分块读取器
		resp.Body = httputil.NewChunkedReader(resp.Body)
	}

	return nil // 读取成功
}

// ReadVersion 从缓冲读取器中读取HTTP版本字符串
// 参数:
//   r: 缓冲读取器
// 返回:
//   Version: 解析得到的HTTP版本对象
//   error: 读取或解析错误，成功时为nil
// 功能: 解析HTTP版本字符串（如"HTTP/1.1 "）
func (resp *Response) ReadVersion(r *bufio.Reader) (Version, error) {
	var major, minor int
	// 逐字节读取并解析HTTP版本字符串
	for pos := 0; pos < len("HTTP/x.x "); pos++ {
		c, err := r.ReadByte()
		if err != nil {
			return invalidVersion, err // 读取失败返回无效版本
		}
		switch pos {
		case 0: // 期望字符'H'
			if c != 'H' {
				return readVersionErr(pos, 'H', c)
			}
		case 1, 2: // 期望字符'T'
			if c != 'T' {
				return readVersionErr(pos, 'T', c)
			}
		case 3: // 期望字符'P'
			if c != 'P' {
				return readVersionErr(pos, 'P', c)
			}
		case 4: // 期望字符'/'
			if c != '/' {
				return readVersionErr(pos, '/', c)
			}
		case 5: // 主版本号（0-9）
			switch c {
			case '0', '1', '2', '3', '4', '5', '6', '7', '8', '9':
				major = int(int(c) - 0x30) // 将ASCII字符转换为数字
			}
		case 6: // 期望字符'.'
			if c != '.' {
				return readVersionErr(pos, '.', c)
			}
		case 7: // 次版本号（0-9）
			switch c {
			case '0', '1', '2', '3', '4', '5', '6', '7', '8', '9':
				minor = int(int(c) - 0x30) // 将ASCII字符转换为数字
			}
		case 8: // 期望空格结束
			if c != ' ' {
				return readVersionErr(pos, ' ', c)
			}
		}
	}
	return Version{Major: major, Minor: minor}, nil // 返回解析得到的版本
}

// invalidVersion 无效版本常量，用于错误情况
var invalidVersion Version

// readVersionErr 生成版本读取错误
// 参数:
//   pos: 错误发生的位置
//   expected: 期望的字节
//   got: 实际读取到的字节
// 返回:
//   Version: 无效版本对象
//   error: 格式化的错误信息
// 功能: 创建详细的版本解析错误信息
func readVersionErr(pos int, expected, got byte) (Version, error) {
	return invalidVersion, fmt.Errorf("ReadVersion: expected %q, got %q at position %v", expected, got, pos)
}

// ReadStatusCode 从缓冲读取器中读取HTTP状态码
// 参数:
//   r: 缓冲读取器
// 返回:
//   int: 解析得到的HTTP状态码（如200、404、500等）
//   error: 读取或解析错误，成功时为nil
// 功能: 解析HTTP状态码，支持3位数字格式，处理特殊情况如空原因短语
func (resp *Response) ReadStatusCode(r *bufio.Reader) (int, error) {
	var code int
	// 逐字节读取状态码（最多4个字符："200 "）
	for pos := 0; pos < len("200 "); pos++ {
		c, err := r.ReadByte()
		if err != nil {
			return 0, err // 读取失败返回错误
		}
		switch pos {
		case 0, 1, 2: // 状态码的三位数字
			switch c {
			case '0', '1', '2', '3', '4', '5', '6', '7', '8', '9':
				switch pos {
				case 0: // 百位数
					code = int(int(c)-0x30) * 100
				case 1: // 十位数
					code += int(int(c)-0x30) * 10
				case 2: // 个位数
					code += int(int(c) - 0x30)
				}
			}
		case 3: // 状态码后的分隔符
			switch c {
			case '\r':
				// special case "HTTP/1.1 301\r\n" has a blank reason.
				// 特殊情况："HTTP/1.1 301\r\n" 没有原因短语
			case ' ':
				// nothing - 正常的空格分隔符
			default:
				return 0, fmt.Errorf("ReadStatusCode: expected %q, got %q at position %v", ' ', c, pos)
			}
		}
	}
	return code, nil // 返回解析得到的状态码
}

// ReadStatusLine 读取HTTP状态行
// 参数:
//   r: 缓冲读取器
// 返回:
//   Version: HTTP版本对象
//   int: 状态码
//   string: 状态消息（原因短语）
//   error: 读取或解析错误，成功时为nil
// 功能: 完整读取HTTP响应的状态行，包括版本、状态码和状态消息
func (resp *Response) ReadStatusLine(r *bufio.Reader) (Version, int, string, error) {
	// 读取HTTP版本
	version, err := resp.ReadVersion(r)
	if err != nil {
		return Version{}, 0, "", err // 版本读取失败
	}
	// 读取状态码
	code, err := resp.ReadStatusCode(r)
	if err != nil {
		return Version{}, 0, "", err // 状态码读取失败
	}
	// 读取状态消息（行的剩余部分）
	msg, _, err := r.ReadLine()
	return version, code, string(msg), err // 返回完整的状态行信息
}

// ReadHeader 读取HTTP头部
// 参数:
//   r: 缓冲读取器
// 返回:
//   string: 头部名称（键）
//   string: 头部值
//   bool: 是否已读取完所有头部（遇到空行时为true）
//   error: 读取或解析错误，成功时为nil
// 功能: 读取单个HTTP头部，解析键值对格式，检测头部结束标志
func (resp *Response) ReadHeader(r *bufio.Reader) (string, string, bool, error) {
	// 读取一行数据
	line, err := resp.readLine(r)
	if err != nil {
		return "", "", false, err // 读取失败
	}
	// 检查是否为空行（头部结束标志）
	if line := string(line); line == "\r\n" || line == "\n" {
		return "", "", true, nil // 返回true表示头部读取完成
	}
	// 按冒号分割头部行，最多分割为2部分
	v := bytes.SplitN(line, []byte(":"), 2)
	if len(v) != 2 {
		return "", "", false, fmt.Errorf("invalid header line: %q", line) // 头部格式错误
	}
	// 返回去除空白的头部名称和值
	return string(bytes.TrimSpace(v[0])), string(bytes.TrimSpace(v[1])), false, nil
}

// ReadBody 读取HTTP响应体
// 参数:
//   r: 缓冲读取器
// 返回:
//   io.Reader: 响应体读取器
// 功能: 根据Content-Length头部决定如何读取响应体
//       如果有明确长度则读取到内存，否则直接返回读取器
func (resp *Response) ReadBody(r *bufio.Reader) io.Reader {
	l := resp.ContentLength() // 获取内容长度
	if l > 0 {
		// 如果有明确的内容长度，读取到内存中
		resp.body = make([]byte, l)        // 分配内存
		io.ReadFull(r, resp.body) //nolint // 读取完整的响应体

		return bytes.NewReader(resp.body) // 返回字节读取器
	}

	// 如果没有明确长度，直接返回缓冲读取器
	return r
}

// readLine 读取以\r\n结尾的一行数据
// 参数:
//   r: 缓冲读取器
// 返回:
//   []byte: 读取到的行数据（包含换行符）
//   error: 读取错误，成功时为nil
// 功能: 内部辅助方法，读取一行数据直到遇到换行符
func (resp *Response) readLine(r *bufio.Reader) ([]byte, error) {
	return r.ReadBytes('\n') // 读取直到遇到换行符
}

