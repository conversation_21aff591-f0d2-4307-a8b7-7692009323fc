// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-17 16:03:21
// FilePath: /yaml_scan/pkg/reporting/reporting.go
// Description: 
package reporting

// Client is a client for nuclei issue tracking module
type Client interface {
	RegisterTracker(tracker Tracker)
	RegisterExporter(exporter Exporter)
	Close()
	Clear()
	CreateIssue(event *output.ResultEvent) error
	CloseIssue(event *output.ResultEvent) error
	GetReportingOptions() *Options
}


// New creates a new nuclei issue tracker reporting client
func New(options *Options, db string, doNotDedupe bool) (Client, error) {
	client := &ReportingClient{options: options}

	if options.GitHub != nil {
		options.GitHub.HttpClient = options.HttpClient
		options.GitHub.OmitRaw = options.OmitRaw
		tracker, err := github.New(options.GitHub)
		if err != nil {
			return nil, errorutil.NewWithErr(err).Wrap(ErrReportingClientCreation)
		}
		client.trackers = append(client.trackers, tracker)
	}
	if options.GitLab != nil {
		options.GitLab.HttpClient = options.HttpClient
		options.GitLab.OmitRaw = options.OmitRaw
		tracker, err := gitlab.New(options.GitLab)
		if err != nil {
			return nil, errorutil.NewWithErr(err).Wrap(ErrReportingClientCreation)
		}
		client.trackers = append(client.trackers, tracker)
	}
	if options.Gitea != nil {
		options.Gitea.HttpClient = options.HttpClient
		options.Gitea.OmitRaw = options.OmitRaw
		tracker, err := gitea.New(options.Gitea)
		if err != nil {
			return nil, errorutil.NewWithErr(err).Wrap(ErrReportingClientCreation)
		}
		client.trackers = append(client.trackers, tracker)
	}
	if options.Jira != nil {
		options.Jira.HttpClient = options.HttpClient
		options.Jira.OmitRaw = options.OmitRaw
		tracker, err := jira.New(options.Jira)
		if err != nil {
			return nil, errorutil.NewWithErr(err).Wrap(ErrReportingClientCreation)
		}
		client.trackers = append(client.trackers, tracker)
	}
	if options.Linear != nil {
		options.Linear.HttpClient = options.HttpClient
		options.Linear.OmitRaw = options.OmitRaw
		tracker, err := linear.New(options.Linear)
		if err != nil {
			return nil, errorutil.NewWithErr(err).Wrap(ErrReportingClientCreation)
		}
		client.trackers = append(client.trackers, tracker)
	}
	if options.MarkdownExporter != nil {
		exporter, err := markdown.New(options.MarkdownExporter)
		if err != nil {
			return nil, errorutil.NewWithErr(err).Wrap(ErrExportClientCreation)
		}
		client.exporters = append(client.exporters, exporter)
	}
	if options.SarifExporter != nil {
		exporter, err := sarif.New(options.SarifExporter)
		if err != nil {
			return nil, errorutil.NewWithErr(err).Wrap(ErrExportClientCreation)
		}
		client.exporters = append(client.exporters, exporter)
	}
	if options.JSONExporter != nil {
		exporter, err := json_exporter.New(options.JSONExporter)
		if err != nil {
			return nil, errorutil.NewWithErr(err).Wrap(ErrExportClientCreation)
		}
		client.exporters = append(client.exporters, exporter)
	}
	if options.JSONLExporter != nil {
		exporter, err := jsonl.New(options.JSONLExporter)
		if err != nil {
			return nil, errorutil.NewWithErr(err).Wrap(ErrExportClientCreation)
		}
		client.exporters = append(client.exporters, exporter)
	}
	if options.ElasticsearchExporter != nil {
		options.ElasticsearchExporter.HttpClient = options.HttpClient
		exporter, err := es.New(options.ElasticsearchExporter)
		if err != nil {
			return nil, errorutil.NewWithErr(err).Wrap(ErrExportClientCreation)
		}
		client.exporters = append(client.exporters, exporter)
	}
	if options.SplunkExporter != nil {
		options.SplunkExporter.HttpClient = options.HttpClient
		exporter, err := splunk.New(options.SplunkExporter)
		if err != nil {
			return nil, errorutil.NewWithErr(err).Wrap(ErrExportClientCreation)
		}
		client.exporters = append(client.exporters, exporter)
	}
	if options.MongoDBExporter != nil {
		exporter, err := mongo.New(options.MongoDBExporter)
		if err != nil {
			return nil, errorutil.NewWithErr(err).Wrap(ErrExportClientCreation)
		}
		client.exporters = append(client.exporters, exporter)
	}

	if doNotDedupe {
		return client, nil
	}

	client.stats = make(map[string]*IssueTrackerStats)
	for _, tracker := range client.trackers {
		trackerName := tracker.Name()

		client.stats[trackerName] = &IssueTrackerStats{
			Created: atomic.Int32{},
			Failed:  atomic.Int32{},
		}
	}

	storage, err := dedupe.New(db)
	if err != nil {
		return nil, err
	}
	client.dedupe = storage
	return client, nil
}

// CreateConfigIfNotExists creates report-config if it doesn't exist
func CreateConfigIfNotExists() error {
	reportingConfig := config.DefaultConfig.GetReportingConfigFilePath()

	if fileutil.FileExists(reportingConfig) {
		return nil
	}
	values := stringslice.StringSlice{Value: []string{}}

	options := &Options{
		AllowList:             &filters.Filter{Tags: values},
		DenyList:              &filters.Filter{Tags: values},
		GitHub:                &github.Options{},
		GitLab:                &gitlab.Options{},
		Gitea:                 &gitea.Options{},
		Jira:                  &jira.Options{},
		Linear:                &linear.Options{},
		MarkdownExporter:      &markdown.Options{},
		SarifExporter:         &sarif.Options{},
		ElasticsearchExporter: &es.Options{},
		SplunkExporter:        &splunk.Options{},
		JSONExporter:          &json_exporter.Options{},
		JSONLExporter:         &jsonl.Options{},
		MongoDBExporter:       &mongo.Options{},
	}
	reportingFile, err := os.Create(reportingConfig)
	if err != nil {
		return errorutil.NewWithErr(err).Msgf("could not create config file")
	}
	defer reportingFile.Close()

	err = yaml.NewEncoder(reportingFile).Encode(options)
	return err
}