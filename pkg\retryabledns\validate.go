//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-26 16:57:53
//FilePath: /yaml_scan/pkg/retryabledns/validate.go
//Description:

package retryabledns

import "net"

// internalRangeChecker  用于检查 IP 地址是否属于内部网络范围。
type internalRangeChecker struct {
	ipv4 []*net.IPNet
	ipv6 []*net.IPNet
}

// ipv4InternalRanges 包含 IPv4 内部网络范围的 CIDR 表示法。
var ipv4InternalRanges = []string{
	"0.0.0.0/8", // 当前网络（仅作为源地址有效）
	// "10.0.0.0/8",      // 私有网络
	// "**********/10",   // 共享地址空间
	"*********/8", // 环回地址
	// "***********/16",  // 链路本地（也包括许多云提供商的元数据端点）
	// "**********/12",   // 私有网络
	// "*********/24",    // IETF 协议分配
	// "*********/24",    // TEST-NET-1，文档和示例
	// "***********/24",  // IPv6 到 IPv4 的中继（包括 2002::/16）
	// "***********/16",  // 私有网络
	// "**********/15",   // 网络基准测试
	// "************/24", // TEST-NET-2，文档和示例
	// "***********/24",  // TEST-NET-3，文档和示例
	// "*********/4",     // IP 多播（以前的 D 类网络）
	// "240.0.0.0/4",     // 保留（以前的 E 类网络）
}

// ipv6InternalRanges 包含 IPv6 内部网络范围的 CIDR 表示法。
var ipv6InternalRanges = []string{
	"::1/128", // 环回地址
	// "64:ff9b::/96",  // IPv4/IPv6 转换（RFC 6052）
	// "100::/64",      // 丢弃前缀（RFC 6666）
	// "2001::/32",     // Teredo 隧道
	// "2001:10::/28",  // 已弃用（以前的 ORCHID）
	// "2001:20::/28",  // ORCHIDv2
	// "2001:db8::/32", // 文档和示例源代码中使用的地址
	// "2002::/16",     // 6to4
	// "fc00::/7",      // 唯一本地地址
	// "fe80::/10",     // 链路本地地址
	// "ff00::/8",      // 多播
}

// appendIPv4Ranges:将一组 IPv4 范围添加到 internalRangeChecker 的列表中。
//
//	@receiver r *internalRangeChecker:
//	@param ranges []string: 要添加的 IPv4 CIDR 范围。
//	@return error error: 如果解析 CIDR 失败，返回错误；否则返回 nil
func (r *internalRangeChecker) appendIPv4Ranges(ranges []string) error {
	for _, ip := range ranges {
		// 解析 CIDR 范围
		_, rangeNet, err := net.ParseCIDR(ip)
		if err != nil {
			return err
		}
		// 将解析后的范围添加到列表中
		r.ipv4 = append(r.ipv4, rangeNet)
	}
	return nil
}

// appendIPv6Ranges: 将一组 IPv6 范围添加到 internalRangeChecker 的列表中。
//
//	@receiver r *internalRangeChecker:
//	@param ranges []string:含要添加的 IPv6 CIDR 范围。
//	@return error error:
func (r *internalRangeChecker) appendIPv6Ranges(ranges []string) error {
	for _, ip := range ranges {
		// 解析 CIDR 范围
		_, rangeNet, err := net.ParseCIDR(ip)
		if err != nil {
			return err
		}
		r.ipv6 = append(r.ipv6, rangeNet)
	}
	return nil
}

// newInternalRangeChecker:创建一个用于检查主机是否来自内部 IP 范围的结构体，
//
//	@return *internalRangeChecker *internalRangeChecker:初始化后的 internalRangeChecker 实例
//	@return error error: 如果解析 CIDR 失败，返回错误；否则返回 nil
func newInternalRangeChecker() (*internalRangeChecker, error) {
	rangeChecker := internalRangeChecker{}

	// 添加 IPv4 内部范围
	err := rangeChecker.appendIPv4Ranges(ipv4InternalRanges)
	if err != nil {
		return nil, err
	}

	// 添加 IPv6 内部范围
	err = rangeChecker.appendIPv6Ranges(ipv6InternalRanges)
	if err != nil {
		return nil, err
	}
	return &rangeChecker, nil
}

// ContainsIPv4: 检查给定的 IP 地址是否存在于内部 IPv4 范围中。
//
//	@receiver r *internalRangeChecker:
//	@param IP net.IP: 要检查的 IPv4 地址
//	@return bool bool: 如果 IP 在内部范围中，返回 true；否则返回 false
func (r *internalRangeChecker) ContainsIPv4(IP net.IP) bool {
	for _, net := range r.ipv4 {
		if net.Contains(IP) {
			return true
		}
	}
	return false
}

// ContainsIPv6:检查给定的 IP 地址是否存在于内部 IPv6 范围中。
//
//	@receiver r *internalRangeChecker:
//	@param IP net.IP: 要检查的 IPv6 地址
//	@return bool bool:如果 IP 在内部范围中，返回 true；否则返回 false
func (r *internalRangeChecker) ContainsIPv6(IP net.IP) bool {
	for _, net := range r.ipv6 {
		if net.Contains(IP) {
			return true
		}
	}
	return false
}
