package iputil

import (
	"context"
	"fmt"
	"io"
	"net"
	"net/http"
	"strings"
	"time"

	stringsutil "yaml_scan/utils/strings"
)

var (
	// ipv4InternalRanges 包含 IPv4 内部地址范围的 CIDR 表示
	ipv4InternalRanges = []string{
		"0.0.0.0/8",       // 当前网络（仅作为源地址有效）
		"10.0.0.0/8",      // 私有网络
		"**********/10",   // 共享地址空间
		"*********/8",     // 回环地址
		"***********/16",  // 链路本地地址（许多云提供商的元数据端点）
		"**********/12",   // 私有网络
		"*********/24",    // IETF 协议分配
		"*********/24",    // TEST-NET-1，用于文档和示例
		"***********/24",  // IPv6 到 IPv4 的中继（包括 2002::/16）
		"***********/16",  // 私有网络
		"**********/15",   // 网络基准测试
		"************/24", // TEST-NET-2，用于文档和示例
		"***********/24",  // TEST-NET-3，用于文档和示例
		"*********/4",     //  IP 多播（以前的 D 类网络）
		"240.0.0.0/4",     // 保留（以前的 E 类网络）
	}

	// ipv6InternalRanges 包含 IPv6 内部地址范围的 CIDR 表示
	ipv6InternalRanges = []string{
		"::1/128",       // 回环地址
		"64:ff9b::/96",  // IPv4/IPv6 转换（RFC 6052）
		"100::/64",      // 丢弃前缀（RFC 6666）
		"2001::/32",     // Teredo 隧道
		"2001:20::/28",  // ORCHIDv2
		"2001:db8::/32", // 用于文档和示例源代码的地址
		"2002::/16",     // 6to4
		"fc00::/7",      // 唯一的本地地址
		"fe80::/10",     // 链路本地地址
		"ff00::/8",      // 多播
	}
	// ipv4 和 ipv6 切片用于存储解析后的 IP 网络范围
	ipv4, ipv6 []*net.IPNet
)

func init() {
	// 解析 IPv4 内部地址范围并存储在 ipv4 切片中
	for _, cidr := range ipv4InternalRanges {
		_, rangeNet, err := net.ParseCIDR(cidr)
		if err != nil {
			panic(err)
		}
		ipv4 = append(ipv4, rangeNet)
	}
	// 解析 IPv6 内部地址范围并存储在 ipv6 切片中
	for _, cidr := range ipv6InternalRanges {
		_, rangeNet, err := net.ParseCIDR(cidr)
		if err != nil {
			panic(err)
		}
		ipv6 = append(ipv6, rangeNet)
	}
}

// IsCIDR: 检查给定的字符串是否是有效的 CIDR 表示法（支持 IPv4 和 IPv6）。
//  @param str string: 要检查的字符串
//  @return bool bool: 如果字符串是有效的 CIDR 表示法，则返回 true；否则返回 false。
func IsCIDR(str string) bool {
	_, _, err := net.ParseCIDR(str)
	return err == nil
}

// IsIPv4: 检查给定的字符串或 net.IP 是否为有效的 IPv4 地址。
//  @param ips ...interface{}: 可以是一个或多个字符串或 net.IP 类型的参数
//  @return bool bool:  如果所有参数都是有效的 IPv4 地址，则返回 true；否则返回 false。
func IsIPv4(ips ...interface{}) bool {
	for _, ip := range ips {
		switch ipv := ip.(type) {
		case string:
			// 处理字符串类型的 IP 地址
			parsedIP := net.ParseIP(ipv)
			isIP4 := parsedIP != nil && parsedIP.To4() != nil && strings.Contains(ipv, ".")
			if !isIP4 {
				return false
			}
		case net.IP:
			// 处理 net.IP 类型的 IP 地址
			isIP4 := ipv != nil && ipv.To4() != nil && strings.Contains(ipv.String(), ".")
			if !isIP4 {
				return false
			}
		}
	}

	return true
}

// IsIPv6: 检查给定的字符串或 net.IP 是否为有效的 IPv6 地址。
//  @param ips ...interface{}: 可以是一个或多个字符串或 net.IP 类型的参数
//  @return bool bool:  如果所有参数都是有效的 IPv6 地址，则返回 true；否则返回 false。
func IsIPv6(ips ...interface{}) bool {
	for _, ip := range ips {
		switch ipv := ip.(type) {
		case string:
			parsedIP := net.ParseIP(ipv)
			isIP6 := parsedIP != nil && parsedIP.To16() != nil && stringsutil.ContainsAny(ipv, ":")
			if !isIP6 {
				return false
			}
		case net.IP:
			isIP6 := ipv != nil && ipv.To16() != nil && stringsutil.ContainsAny(ipv.String(), ":")
			if !isIP6 {
				return false
			}
		}
	}

	return true
}

// WhatsMyIP 获取当前设备的公共 IP 地址。
func WhatsMyIP() (string, error) {
	// 设置请求超时为 2 秒
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	// 确保在函数结束时取消上下文
	defer cancel()
	// 创建 HTTP 请求
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, "https://checkip.amazonaws.com/", nil)
	if err != nil {
		return "", nil
	}
	// 发送请求
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("error fetching ip: %s", resp.Status)
	}

	ip, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	return strings.Trim(string(ip), "\n\r\t "), nil
}

// IsIP: 检查给定的字符串是否是有效的 IPv4 或 IPv6 地址。
//  @param str string:  要检查的字符串
//  @return bool bool: 如果输入是有效的 IP 地址，返回 true；否则返回 false。
func IsIP(str string) bool {
	return net.ParseIP(str) != nil
}