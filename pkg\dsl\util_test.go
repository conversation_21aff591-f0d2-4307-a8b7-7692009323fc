// 
// Author: chenjb
// Version: V1.0
// Date: 2025-05-29 20:13:42
// FilePath: /yaml_scan/pkg/dsl/util_test.go
// Description: 
package dsl

import (
	"bytes"
	"crypto/md5"
	"net"
	"sort"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestToString 测试toString函数的功能
func TestToString(t *testing.T) {
	// 测试各种类型的转换
	testCases := []struct {
		name     string
		input    interface{}
		expected string
	}{
		{"nil值", nil, ""},
		{"字符串", "测试", "测试"},
		{"布尔值", true, "true"},
		{"整数", 42, "42"},
		{"浮点数", 3.14, "3.14"},
		{"字节数组", []byte("Hello"), "Hello"},
		{"实现Stringer接口的类型", bytes.NewBufferString("buffer"), "buffer"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := toString(tc.input)
			require.Equal(t, tc.expected, result, "转换结果不符合预期")
		})
	}
}

// TestInsertInto 测试insertInto函数的功能
func TestInsertInto(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		interval int
		sep      rune
		expected string
	}{
		{"每两个字符后插入冒号", "abcdef", 2, ':', "ab:cd:ef:"},
		{"每三个字符后插入横线", "123456789", 3, '-', "123-456-789-"},
		{"每一个字符后插入空格", "hello", 1, ' ', "h e l l o "},
		{"空字符串", "", 2, ',', ","},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := insertInto(tc.input, tc.interval, tc.sep)
			require.Equal(t, tc.expected, result, "插入分隔符后的结果不符合预期")
		})
	}
}

// TestAppendSingleDigitZero 测试appendSingleDigitZero函数的功能
func TestAppendSingleDigitZero(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{"单位数前加零", "5", "05"},
		{"零前加零", "0", "00"},
		{"两位数不变", "10", "10"},
		{"已经有前导零的数不变", "01", "01"},
		{"非数字字符串不变", "abc", "abc"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := appendSingleDigitZero(tc.input)
			require.Equal(t, tc.expected, result, "添加前导零后的结果不符合预期")
		})
	}
}

// TestFormatDateTime 测试formatDateTime函数的功能
func TestFormatDateTime(t *testing.T) {
	testCases := []struct {
		name        string
		inputFormat string
		matchValue  string
		timeFragment int
		expected    string
	}{
		{"年份替换", "YYYY年", "YYYY", 2023, "2023年"},
		{"月份替换-单位数", "MM月", "MM", 5, "05月"},
		{"月份替换-双位数", "MM月", "MM", 12, "12月"},
		{"日期替换", "DD日", "DD", 9, "09日"},
		{"小时替换", "HH时", "HH", 14, "14时"},
		{"分钟替换", "mm分", "mm", 8, "08分"},
		{"秒数替换", "SS秒", "SS", 45, "45秒"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := formatDateTime(tc.inputFormat, tc.matchValue, tc.timeFragment)
			require.Equal(t, tc.expected, result, "日期时间格式化结果不符合预期")
		})
	}
}

// TestDoSimpleTimeFormat 测试doSimpleTimeFormat函数的功能
func TestDoSimpleTimeFormat(t *testing.T) {
	// 设置一个固定的时间点用于测试
	testTime := time.Date(2023, 12, 31, 14, 30, 45, 0, time.UTC)
	
	testCases := []struct {
		name        string
		formatFragments [][]string
		format      string
		expected    string
	}{
		{
			"年月日格式化", 
			[][]string{{"YYYY", "Y"}, {"MM", "M"}, {"DD", "D"}},
			"YYYY-MM-DD",
			"2023-12-31",
		},
		{
			"时分秒格式化", 
			[][]string{{"HH", "H"}, {"mm", "m"}, {"SS", "S"}},
			"HH:mm:SS",
			"14:30:45",
		},
		{
			"混合格式化", 
			[][]string{{"YYYY", "Y"}, {"MM", "M"}, {"DD", "D"}, {"HH", "H"}, {"mm", "m"}, {"SS", "S"}},
			"YYYY年MM月DD日 HH时mm分SS秒",
			"2023年12月31日 14时30分45秒",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := doSimpleTimeFormat(tc.formatFragments, testTime, tc.format)
			require.NoError(t, err, "时间格式化应该不返回错误")
			require.Equal(t, tc.expected, result, "时间格式化结果不符合预期")
		})
	}
	
	// 测试无效格式
	t.Run("无效格式", func(t *testing.T) {
		_, err := doSimpleTimeFormat([][]string{{"XX", "X"}}, testTime, "XX")
		require.Error(t, err, "无效的格式应该返回错误")
	})
}

// TestToHexEncodedHash 测试toHexEncodedHash函数的功能
func TestToHexEncodedHash(t *testing.T) {
	t.Run("MD5哈希测试", func(t *testing.T) {
		testString := "测试字符串"
		expected := "1f3ca051028d1d1e95a6f4e269d727ab" // 预先计算的MD5哈希值
		
		// 创建MD5哈希实例
		md5Hash := md5.New()
		
		// 调用测试函数
		result, err := toHexEncodedHash(md5Hash, testString)
		require.NoError(t, err, "计算哈希值应该不返回错误")
		require.Equal(t, expected, result, "哈希计算结果不符合预期")
	})
}

// TestToChunks 测试toChunks函数的功能
func TestToChunks(t *testing.T) {
	testCases := []struct {
		name      string
		input     string
		chunkSize int
		expected  []string
	}{
		{"正常分块", "HelloWorld", 5, []string{"Hello", "World"}},
		{"不均匀分块", "abcdefgh", 3, []string{"abc", "def", "gh"}},
		{"块大小为1", "abc", 1, []string{"a", "b", "c"}},
		{"块大小大于字符串长度", "abc", 5, []string{"abc"}},
		{"块大小为0", "abc", 0, []string{"abc"}},
		{"块大小为负数", "abc", -1, []string{"abc"}},
		{"空字符串", "", 2, []string{""}},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := toChunks(tc.input, tc.chunkSize)
			require.Equal(t, tc.expected, result, "字符串分块结果不符合预期")
		})
	}
}

// TestToStringSlice 测试toStringSlice函数的功能
func TestToStringSlice(t *testing.T) {
	testCases := []struct {
		name     string
		input    interface{}
		expected []string
	}{
		{"字符串切片", []string{"a", "b", "c"}, []string{"a", "b", "c"}},
		{"整数切片", []int{1, 2, 3}, []string{"1", "2", "3"}},
		{"浮点数切片", []float64{1.1, 2.2, 3.3}, []string{"1.1", "2.2", "3.3"}},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := toStringSlice(tc.input)
			require.Equal(t, tc.expected, result, "切片转换结果不符合预期")
		})
	}
}

// TestTrimAll 测试TrimAll函数的功能
func TestTrimAll(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		cutset   string
		expected string
	}{
		{"移除空格", "hello world", " ", "helloworld"},
		{"移除标点符号", "hello, world!", ",.!?", "hello world"},
		{"移除多种字符", "a1b2c3d4", "1234", "abcd"},
		{"cutset为空", "abc", "", "abc"},
		{"input为空", "", "abc", ""},
		{"移除所有字符", "abcdef", "abcdef", ""},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := TrimAll(tc.input, tc.cutset)
			require.Equal(t, tc.expected, result, "字符移除结果不符合预期")
		})
	}
}

// TestRandSeq 测试RandSeq函数的功能
func TestRandSeq(t *testing.T) {
	testCases := []struct {
		name   string
		base   string
		length int
	}{
		{"字母字符集", "abcdefghijklmnopqrstuvwxyz", 10},
		{"数字字符集", "0123456789", 5},
		{"混合字符集", "abcdef0123456789", 8},
		{"自定义字符集", "!@#$%^&*()", 3},
		{"长度为0", "abc", 0},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := RandSeq(tc.base, tc.length)
			
			// 验证生成字符串的长度
			require.Equal(t, tc.length, len(result), "生成字符串长度不符合预期")
			
			// 验证生成的字符都来自基础字符集
			for _, c := range result {
				require.Contains(t, tc.base, string(c), "生成的字符不在基础字符集中")
			}
		})
	}
}

// TestAggregate 测试aggregate函数的功能
func TestAggregate(t *testing.T) {
	testCases := []struct {
		name     string
		input    []string
		expected string
	}{
		{
			"有序字符串数组",
			[]string{"a", "b", "c"},
			"\ta\n\tb\n\tc\n",
		},
		{
			"无序字符串数组",
			[]string{"c", "a", "b"},
			"\ta\n\tb\n\tc\n", // 应该按字母顺序排序
		},
		{
			"包含空字符串",
			[]string{"", "a", ""},
			"\t\n\t\n\ta\n",
		},
		{
			"空数组",
			[]string{},
			"",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := aggregate(tc.input)
			require.Equal(t, tc.expected, result, "聚合结果不符合预期")
		})
	}
}

// 对比示例 - 确认聚合结果正确性
func TestAggregateExample(t *testing.T) {
	input := []string{"c", "a", "b"}
	
	// 使用测试函数的实现
	result := aggregate(input)
	
	// 使用标准库自己实现一遍
	sortedInput := make([]string, len(input))
	copy(sortedInput, input)
	sort.Strings(sortedInput)
	
	builder := &strings.Builder{}
	for _, value := range sortedInput {
		builder.WriteRune('\t')
		builder.WriteString(value)
		builder.WriteRune('\n')
	}
	expected := builder.String()
	
	require.Equal(t, expected, result, "聚合结果与预期实现不一致")
}

// TestGetRandomIPWithCidr 测试GetRandomIPWithCidr函数的功能
func TestGetRandomIPWithCidr(t *testing.T) {
	testCases := []struct {
		name  string
		cidrs []string
		valid bool
	}{
		{"有效IPv4 CIDR", []string{"***********/24"}, true},
		{"有效IPv6 CIDR", []string{"2001:db8::/64"}, true},
		{"多个有效CIDR", []string{"***********/24", "10.0.0.0/8"}, true},
		{"无效CIDR格式", []string{"***********"}, false},
		{"空CIDR列表", []string{}, false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			ip, err := GetRandomIPWithCidr(tc.cidrs...)
			
			if tc.valid {
				require.NoError(t, err, "生成随机IP应该不返回错误")
				require.NotNil(t, ip, "生成的IP不应为nil")
				
				if len(tc.cidrs) > 0 {
					// 验证IP在CIDR范围内
					inRange := false
					for _, cidr := range tc.cidrs {
						_, ipnet, err := net.ParseCIDR(cidr)
						if err == nil && ipnet.Contains(ip) {
							inRange = true
							break
						}
					}
					require.True(t, inRange, "生成的IP应该在指定的CIDR范围内")
				}
			} else {
				require.Error(t, err, "使用无效CIDR应该返回错误")
			}
		})
	}
}

// TestStringNumberToDecimal 测试stringNumberToDecimal函数的功能
func TestStringNumberToDecimal(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		prefix   string
		base     int
		expected float64
		hasError bool
	}{
		{"十进制数字", "123", "", 10, 123, false},
		{"十六进制数字", "FF", "0x", 16, 255, false},
		{"带0x前缀的十六进制", "0xFF", "0x", 0, 255, false},
		{"二进制数字", "1010", "0b", 2, 10, false},
		{"八进制数字", "777", "0", 8, 511, false},
		{"非法数字", "abc", "", 10, 0, true},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			args := []interface{}{tc.input}
			result, err := stringNumberToDecimal(args, tc.prefix, tc.base)
			
			if tc.hasError {
				require.Error(t, err, "应该返回错误")
			} else {
				require.NoError(t, err, "不应该返回错误")
				require.Equal(t, tc.expected, result, "转换结果不符合预期")
			}
		})
	}
}

// TestPkcs5Padding 测试pkcs5padding函数的功能
func TestPkcs5Padding(t *testing.T) {
	testCases := []struct {
		name      string
		input     []byte
		blockSize int
		after     int
		expected  int // 期望的填充后长度
	}{
		{"不需要填充", []byte("12345678"), 8, 0, 16},         // 刚好是8的倍数
		{"需要填充1字节", []byte("1234567"), 8, 0, 8},        // 需要填充1字节
		{"需要填充5字节", []byte("123"), 8, 0, 8},            // 需要填充5字节
		{"空数据", []byte{}, 8, 0, 8},                      // 空数据需要填充8字节
		{"大块大小", []byte("123"), 16, 0, 16},              // 块大小为16
		{"多字节数据", []byte("The quick brown fox"), 8, 0, 24} ,// 多字节数据
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := pkcs5padding(tc.input, tc.blockSize, tc.after)
			
			// 验证填充后的长度
			require.Equal(t, tc.expected, len(result), "填充后长度不符合预期")
			
			// 验证原始数据保持不变
			require.Equal(t, tc.input, result[:len(tc.input)], "原始数据应保持不变")
			
			// 验证填充值为填充字节数
			paddingValue := byte(tc.blockSize - len(tc.input)%tc.blockSize)
			for i := len(tc.input); i < len(result); i++ {
				require.Equal(t, paddingValue, result[i], "填充值应为填充字节数")
			}
		})
	}
}

// TestAlgNONE 测试algNONE结构体及其方法的功能
func TestAlgNONE(t *testing.T) {
	// 测试Name方法
	t.Run("测试Name方法", func(t *testing.T) {
		alg := &algNONE{algValue: "none"}
		require.Equal(t, "none", alg.Name(), "Name方法应返回算法名称")
	})

	// 测试Sign方法
	t.Run("测试Sign方法", func(t *testing.T) {
		alg := &algNONE{algValue: "none"}
		sig, err := alg.Sign(nil, []byte("test data"))
		require.NoError(t, err, "Sign方法不应返回错误")
		require.Nil(t, sig, "Sign方法应返回nil签名")
	})

	// 测试Verify方法
	t.Run("测试Verify方法 - 有效签名", func(t *testing.T) {
		alg := &algNONE{algValue: "none"}
		// 空签名是有效的
		err := alg.Verify(nil, []byte("test data"), []byte{})
		require.NoError(t, err, "空签名应该验证通过")
	})

	t.Run("测试Verify方法 - 无效签名", func(t *testing.T) {
		alg := &algNONE{algValue: "none"}
		// 非空签名是无效的
		err := alg.Verify(nil, []byte("test data"), []byte("signature"))
		require.Error(t, err, "非空签名应该验证失败")
	})
}

// TestIsJwtAlgorithmNone 测试isjwtAlgorithmNone函数的功能
func TestIsJwtAlgorithmNone(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected bool
	}{
		{"小写none", "none", true},
		{"大写NONE", "NONE", true},
		{"混合大小写None", "None", true},
		{"带空格", " none ", true},
		{"其他算法", "HS256", false},
		{"空字符串", "", false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := isjwtAlgorithmNone(tc.input)
			require.Equal(t, tc.expected, result, "检测结果不符合预期")
		})
	}
}

// TestGetPublicIP 测试GetPublicIP函数的功能
func TestGetPublicIP(t *testing.T) {
	// 由于GetPublicIP是网络相关函数，可能会依赖外部服务
	// 这里只进行基本的调用测试，不断言具体IP值
	
	t.Run("基本调用测试", func(t *testing.T) {
		// 连续调用两次，验证单例模式
		ip1 := GetPublicIP()
		ip2 := GetPublicIP()
		
		// IP可能为空（如果网络未连接），但两次调用结果应一致
		require.Equal(t, ip1, ip2, "多次调用应返回相同结果")
	})
} 

