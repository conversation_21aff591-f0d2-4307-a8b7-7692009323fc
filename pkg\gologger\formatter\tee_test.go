package formatter

import (
	"testing"
	"bytes"
)


func TestTeeFormat(t *testing.T) {
	// 创建一个字节缓冲区作为 io.Writer
	var buf bytes.Buffer

	// 创建一个 Tee 格式化器实例
	tee := NewTee(&JSON{}, &buf)

	tests := []struct {
		name     string
		event    *LogEvent
		expected string
	}{
		{
			name: "Basic log event",
			event: &LogEvent{
				Message: "This is a test message",
				Metadata: map[string]string{
					"label": "INFO",
				},
			},
			expected: `{"level":"INFO","msg":"This is a test message","timestamp":"`,
		},
		{
			name: "Log event with additional metadata",
			event: &LogEvent{
				Message: "Another test message",
				Metadata: map[string]string{
					"label": "ERROR",
					"user":  "admin",
				},
			},
			expected: `{"level":"ERROR","msg":"Another test message","timestamp":"`,
		},
	}


	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 清空缓冲区
			buf.Reset()

			// 调用 Tee 的 Format 方法
			_, err := tee.Format(tt.event)

			if err != nil {
				t.Fatalf("Format() returned an error: %v", err)
			}

			// 检查缓冲区的内容
			result := buf.String()

			// 由于时间戳是动态生成的，使用前缀进行比较
			if !startsWith(result, tt.expected) {
				t.Errorf("Expected output to start with %q, but got %q", tt.expected, result)
			}
		})
	}
}


func startsWith(s, prefix string) bool {
	return len(s) >= len(prefix) && s[:len(prefix)] == prefix
}