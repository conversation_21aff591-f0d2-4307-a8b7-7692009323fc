// Author: chenjb
// Version: V1.0
// Date: 2025-05-30 17:49:54
// FilePath: /yaml_scan/pkg/cidr/ip.go
// Description: 提供IP地址和CIDR处理的工具包，包含IP地址转换、CIDR计算、IP范围划分等功能
package cidr

import (
	"bytes"
	"encoding/binary"
	"encoding/hex"
	"errors"
	"fmt"
	"math/big"
	"math/bits"
	"net"
	"net/netip"
	"regexp"
	"sort"
	"strconv"
	"strings"

	iputil "yaml_scan/utils/ip"
	stringsutil "yaml_scan/utils/strings"
)

var (
	// DefaultMaskSize4 是IPv4的默认掩码大小
	DefaultMaskSize4 = 32
	// v4Mappedv6Prefix 是RFC2765标准定义的IPv4映射地址前缀
	v4Mappedv6Prefix  = []byte{0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff}
	// ipv4LeadingZeroes 是IPv4地址转换为IPv6时的前导零
	ipv4LeadingZeroes = []byte{0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0}
	// defaultIPv4 是默认的IPv4地址字节表示(::ffff:0.0.0.0)
	defaultIPv4       = []byte{0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0}
	// defaultIPv6 是默认的IPv6地址字节表示(::)
	defaultIPv6       = []byte{0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0}
	// upperIPv4 是IPv4的最大地址字节表示(::ffff:***************)
	upperIPv4         = []byte{0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 255, 255, 255, 255}
	// upperIPv6 是IPv6的最大地址字节表示(ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff)
	upperIPv6         = []byte{0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff}
)

const (
	// ipv4BitLen 表示IPv4地址的位长度(32位)
	ipv4BitLen = 8 * net.IPv4len
	// ipv6BitLen 表示IPv6地址的位长度(128位)
	ipv6BitLen = 8 * net.IPv6len
)


type netWithRange struct {
	First   *net.IP
	Last    *net.IP
	Network *net.IPNet
}

// CountIPsInCIDR 计算给定CIDR中包含的IP地址数量
// @param includeBase bool: 是否包括网络地址(第一个IP)
// @param includeBroadcast bool: 是否包括广播地址(最后一个IP)
// @param ipnet *net.IPNet: 要计算的CIDR网络
// @return *big.Int *big.Int:  CIDR中包含的IP地址数量
func CountIPsInCIDR(includeBase, includeBroadcast bool, ipnet *net.IPNet) *big.Int {
	// 获取CIDR的子网掩码大小和总位数
	subnet, size := ipnet.Mask.Size()
	// 如果子网掩码等于总位数，说明只有一个IP地址
	if subnet == size {
		return big.NewInt(1)
	}
	// 计算IP地址总数: 2^(size-subnet)
	numberOfIps := big.NewInt(2).Exp(big.NewInt(2), big.NewInt(int64(size-subnet)), nil)
	// 如果不包括网络地址，减去1
	if !includeBase {
		numberOfIps = numberOfIps.Sub(numberOfIps, big.NewInt(1))
	}
	// 如果不包括广播地址，减去1
	if !includeBroadcast {
		numberOfIps = numberOfIps.Sub(numberOfIps, big.NewInt(1))
	}
	return numberOfIps
}

// CountIPsInCIDRs 计算多个CIDR网络中包含的IP地址总数
// @param includeBase bool: 是否包括网络地址
// @param includeBroadcast bool: 否包括广播地址
// @param ipnets ...*net.IPNet:  要计算的CIDR网络列表
// @return *big.Int *big.Int: 所有CIDR中包含的IP地址总数
func CountIPsInCIDRs(includeBase, includeBroadcast bool, ipnets ...*net.IPNet) *big.Int {
	// 初始化IP地址总数为0
	numberOfIPs := big.NewInt(0)
	// 遍历每个CIDR网络，累加IP地址数量
	for _, ipnet := range ipnets {
		numberOfIPs = numberOfIPs.Add(numberOfIPs, CountIPsInCIDR(includeBase, includeBroadcast, ipnet))
	}
	return numberOfIPs
}

// NetsByMask 用于按掩码大小对IP网络列表进行排序
// 实现sort.Interface接口
type NetsByMask []*net.IPNet

// Swap 交换切片中的两个元素
// 实现sort.Interface接口的方法
func (s NetsByMask) Swap(i, j int) {
	s[i], s[j] = s[j], s[i]
}

// Less 比较两个元素的大小关系
// 实现sort.Interface接口的方法
// 首先按照掩码大小排序，掩码大小相同时按IP地址排序
func (s NetsByMask) Less(i, j int) bool {
	// 获取两个CIDR的前缀大小
	iPrefixSize, _ := s[i].Mask.Size()
	jPrefixSize, _ := s[j].Mask.Size()
	// 如果掩码大小相同，则按IP地址排序
	if iPrefixSize == jPrefixSize {
		return bytes.Compare(s[i].IP, s[j].IP) < 0
	}
	return iPrefixSize < jPrefixSize
}

// Len 返回切片的长度
// 实现sort.Interface接口的方法
func (s NetsByMask) Len() int {
	return len(s)
}

// NetsByRange 用于按范围对网络列表进行排序
// 首先按最后一个IP地址排序，然后按第一个IP地址排序
// 实现sort.Interface接口
type NetsByRange []*netWithRange

// Swap 交换切片中的两个元素
func (s NetsByRange) Swap(i, j int) {
	s[i], s[j] = s[j], s[i]
}

// Less 比较两个元素的大小关系
func (s NetsByRange) Less(i, j int) bool {
	// 首先比较最后一个IP地址
	lastComparison := bytes.Compare(*s[i].Last, *s[j].Last)
	if lastComparison < 0 {
		return true
	} else if lastComparison > 0 {
		return false
	}

	// 如果最后IP相同，则比较第一个IP地址
	firstComparison := bytes.Compare(*s[i].First, *s[j].First)
	if firstComparison < 0 {
		return true
	} else if firstComparison > 0 {
		return false
	}

	// 如果第一个IP和最后一个IP都相同，则认为相等，不需要交换	
	return false
}

// Len 返回切片的长度
func (s NetsByRange) Len() int {
	return len(s)
}

// RemoveCIDRs 从一组CIDR中移除指定的CIDR
// @param allowCIDRs []*net.IPNet: 原始CIDR集合
// @param removeCIDRs []*net.IPNet: 需要移除的CIDR集合
// @return []*net.IPNet []*net.IPNet: 移除操作后剩余的CIDR集合
// @return error error: 可能的错误
//	如果待移除的CIDR不包含在原始CIDR中，则忽略该CIDR。
//	返回的结果是原始CIDR集合减去被移除的CIDR集合。
//	注意：调用此函数可能会修改输入的两个切片。
func RemoveCIDRs(allowCIDRs, removeCIDRs []*net.IPNet) ([]*net.IPNet, error) {
	// 确保我们按照子网从大到小的顺序遍历要移除的CIDR
	sort.Sort(NetsByMask(removeCIDRs))

PreLoop:
	// 移除那些包含在其他要移除的CIDR中的CIDR（这些是冗余的）
	for j, removeCIDR := range removeCIDRs {
		for i, removeCIDR2 := range removeCIDRs {
			if i == j {
				continue
			}
			if removeCIDR.Contains(removeCIDR2.IP) {
				// 如果当前CIDR包含另一个CIDR的IP，则移除另一个CIDR（冗余）
				removeCIDRs = append(removeCIDRs[:i], removeCIDRs[i+1:]...)
				// 重新开始循环，因为我们修改了正在迭代的切片
				goto PreLoop
			}
		}
	}

	// 遍历每个需要移除的CIDR
	for _, remove := range removeCIDRs {
	Loop:
	// 检查每个允许的CIDR
		for i, allowCIDR := range allowCIDRs {
			// 不允许比较不同地址空间（IPv4和IPv6不能混合）
			if allowCIDR.IP.To4() != nil && remove.IP.To4() == nil ||
				allowCIDR.IP.To4() == nil && remove.IP.To4() != nil {
				return nil, fmt.Errorf("cannot mix IP addresses of different IP protocol versions")
			}

			// 只有当要移除的CIDR包含在允许的CIDR中时才进行移除操作
			if allowCIDR.Contains(remove.IP.Mask(remove.Mask)) {
				nets, err := removeCIDR(allowCIDR, remove)
				if err != nil {
					return nil, err
				}

				// 移除我们刚处理过的CIDR，并添加通过移除计算出的新CIDR
				allowCIDRs = append(allowCIDRs[:i], allowCIDRs[i+1:]...)
				allowCIDRs = append(allowCIDRs, nets...)
				goto Loop
			} else if remove.Contains(allowCIDR.IP.Mask(allowCIDR.Mask)) {
				// 如果要移除的CIDR包含了允许列表中的CIDR，则直接移除允许的CIDR
				allowCIDRs = append(allowCIDRs[:i], allowCIDRs[i+1:]...)
				goto Loop
			}
		}
	}

	return allowCIDRs, nil
}

// removeCIDR 从允许的CIDR中移除指定的CIDR，返回移除后剩余的CIDR集合
// @param allowCIDR *net.IPNet: 原始允许的CIDR
// @param removeCIDR *net.IPNet: 需要从allowCIDR中移除的CIDR
// @return []*net.IPNet []*net.IPNet:  移除操作后剩余的CIDR集合
// @return error error: 可能的错误
func removeCIDR(allowCIDR, removeCIDR *net.IPNet) ([]*net.IPNet, error) {
	var allowIsIpv4, removeIsIpv4 bool
	var allowBitLen int

	// 检查allowCIDR是否为IPv4地址
	if allowCIDR.IP.To4() != nil {
		allowIsIpv4 = true
		allowBitLen = ipv4BitLen
	} else {
		allowBitLen = ipv6BitLen
	}

	// 检查removeCIDR是否为IPv4地址
	if removeCIDR.IP.To4() != nil {
		removeIsIpv4 = true
	}

	// 确保两个CIDR的IP版本相同
	if removeIsIpv4 != allowIsIpv4 {
		return nil, fmt.Errorf("cannot mix IP addresses of different IP protocol versions")
	}

	// 获取每个CIDR的掩码大小
	allowSize, _ := allowCIDR.Mask.Size()
	removeSize, _ := removeCIDR.Mask.Size()

	// 确保allowCIDR的掩码小于removeCIDR的掩码（即allowCIDR包含的范围更大）
	if allowSize >= removeSize {
		return nil, fmt.Errorf("allow CIDR prefix must be a superset of " +
			"remove CIDR prefix")
	}

	// 应用掩码获取网络地址部分
	allowFirstIPMasked := allowCIDR.IP.Mask(allowCIDR.Mask)
	removeFirstIPMasked := removeCIDR.IP.Mask(removeCIDR.Mask)

	// 如果是IPv4地址，则转换为IPv4映射的IPv6地址格式以便统一处理
	if allowIsIpv4 {
		allowFirstIPMasked = append(v4Mappedv6Prefix, allowFirstIPMasked...)
	}

	if removeIsIpv4 {
		removeFirstIPMasked = append(v4Mappedv6Prefix, removeFirstIPMasked...)
	}

	allowFirstIP := &allowFirstIPMasked
	removeFirstIP := &removeFirstIPMasked

	// 创建CIDR前缀，掩码大小从Y+1到X，其中Y是要排除的CIDR前缀B的掩码长度，
	// X是要排除的CIDR前缀A的掩码长度
	allows := make([]*net.IPNet, 0, removeSize-allowSize)
	for i := (allowBitLen - allowSize - 1); i >= (allowBitLen - removeSize); i-- {
		// 每个CIDR前缀的掩码简单地将第i位翻转，
		// 然后将所有后续位清零（前缀的主机标识符部分）
		newMaskSize := allowBitLen - i
		// 翻转第i位
		newIP := (*net.IP)(flipNthBit((*[]byte)(removeFirstIP), uint(i)))
		// 将allowFirstIP和newIP按位或，确保保留allowFirstIP中的网络部分
		for k := range *allowFirstIP {
			(*newIP)[k] = (*allowFirstIP)[k] | (*newIP)[k]
		}

		// 创建新的掩码和应用掩码后的IP
		newMask := net.CIDRMask(newMaskSize, allowBitLen)
		newIPMasked := newIP.Mask(newMask)

		// 创建新的IPNet并添加到结果中
		newIPNet := net.IPNet{IP: newIPMasked, Mask: newMask}
		allows = append(allows, &newIPNet)
	}

	return allows, nil
}

// flipNthBit 翻转IP地址中指定位置的比特位
// @param ip *[]byte: 要操作的IP地址字节切片的指针
// @param bitNum uint: 
// @return *[]byte *[]byte: 
func flipNthBit(ip *[]byte, bitNum uint) *[]byte {
	ipCopy := make([]byte, len(*ip))
	copy(ipCopy, *ip)
	byteNum := getByteIndexOfBit(bitNum)
	ipCopy[byteNum] ^= 1 << (bitNum % 8)

	return &ipCopy
}

// getByteIndexOfBit 计算比特位在字节数组中的索引位置
// @param bit uint:  比特位的位置（从右到左计数）
// @return uint uint: 该比特位在字节数组中的索引位置
func getByteIndexOfBit(bit uint) uint {
	// 从IPv6地址的末尾开始计算索引
	// IPv6len(16) - (bit / 8) - 1 得到相应的字节索引
	return net.IPv6len - (bit / 8) - 1
}

// ipNetToRange 将IPNet转换为包含首尾IP地址的网络范围
// @param ipNet net.IPNet:  要转换的IP网络
// @return netWithRange netWithRange: 包含网络范围首尾IP地址的结构体
func ipNetToRange(ipNet net.IPNet) netWithRange {
	// 创建首尾IP地址的副本
	firstIP := make(net.IP, len(ipNet.IP))
	lastIP := make(net.IP, len(ipNet.IP))

	// 复制原始IP地址
	copy(firstIP, ipNet.IP)
	copy(lastIP, ipNet.IP)

	// 应用掩码获取网络地址部分
	firstIP = firstIP.Mask(ipNet.Mask)
	lastIP = lastIP.Mask(ipNet.Mask)

	// 如果是IPv4地址，转换为IPv4映射的IPv6格式
	if firstIP.To4() != nil {
		firstIP = append(v4Mappedv6Prefix, firstIP...)
		lastIP = append(v4Mappedv6Prefix, lastIP...)
	}

	// 创建最后IP地址的掩码副本
	lastIPMask := make(net.IPMask, len(ipNet.Mask))
	copy(lastIPMask, ipNet.Mask)

	// 计算最后IP地址：将掩码取反后与IP地址按位或
	// 这样可以将网络地址转换为广播地址（所有主机位都设为1）
	for i := range lastIPMask {
		index := len(lastIPMask) - 1 - i
		lastIPMask[index] = ^lastIPMask[index]
		if net.IPv6len-i-1 < len(lastIP) {
			lastIP[net.IPv6len-i-1] |= lastIPMask[index]
		}
	}

	// 返回包含首尾IP和原始网络的结构体
	return netWithRange{First: &firstIP, Last: &lastIP, Network: &ipNet}
}

// getPreviousIP 获取给定IP地址的前一个IP地址
// @param ip net.IP: 输入的IP地址
// @return net.IP net.IP: 前一个IP地址
//	如果输入的IP地址是最小地址(0.0.0.0或::)，则返回相同的地址。
func getPreviousIP(ip net.IP) net.IP {
	// 如果IP已经是最小值(0.0.0.0或::)，则无法再减小，直接返回
	if ip.Equal(net.IP(defaultIPv4)) || ip.Equal(net.IP(defaultIPv6)) {
		return ip
	}

	// 创建IP地址的副本
	previousIP := make(net.IP, len(ip))
	copy(previousIP, ip)

	// 处理借位的标志和起始字节索引
	var overflow bool
	var lowerByteBound int
	// 确定是IPv4还是IPv6地址，设置相应的起始索引
	if ip.To4() != nil {
		lowerByteBound = net.IPv6len - net.IPv4len
	} else {
		lowerByteBound = 0
	}
	// 从最低位字节开始，向高位字节递减
	for i := len(ip) - 1; i >= lowerByteBound; i-- {
		// 如果有借位或者是第一个处理的字节，则减1
		if overflow || i == len(ip)-1 {
			previousIP[i]--
		}
		// 检查是否发生借位：如果原字节是0且减1后变成255，则发生借位
		if ip[i] == 0 && previousIP[i] == 255 {
			overflow = true
		} else {
			overflow = false
		}
	}
	return previousIP
}

// GetNextIP 获取给定IP地址的下一个IP地址
// @param ip net.IP: 输入的IP地址
// @return net.IP net.IP:  下一个IP地址
//	如果输入的IP地址是最大地址(***************或ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff)，
//	则返回相同的地址。
func GetNextIP(ip net.IP) net.IP {
	// 如果IP已经是最大值，则无法再增加，直接返回
	if ip.Equal(upperIPv4) || ip.Equal(upperIPv6) {
		return ip
	}

	// 创建下一个IP地址的存储空间
	nextIP := make(net.IP, len(ip))

	// 根据IP地址类型采用不同的处理方式
	switch len(ip) {
	case net.IPv4len:
		// 对于IPv4地址，直接将其转换为32位整数，加1后再转回IP
		ipU32 := binary.BigEndian.Uint32(ip)
		ipU32++
		binary.BigEndian.PutUint32(nextIP, ipU32)
		return nextIP
	case net.IPv6len:
		// 对于IPv6地址，先处理低64位
		ipU64 := binary.BigEndian.Uint64(ip[net.IPv6len/2:])
		ipU64++
		binary.BigEndian.PutUint64(nextIP[net.IPv6len/2:], ipU64)
		// 如果低64位溢出（变为0），则高64位加1
		if ipU64 == 0 {
			ipU64 = binary.BigEndian.Uint64(ip[:net.IPv6len/2])
			ipU64++
			binary.BigEndian.PutUint64(nextIP[:net.IPv6len/2], ipU64)
		} else {
			// 否则高64位保持不变
			copy(nextIP[:net.IPv6len/2], ip[:net.IPv6len/2])
		}
		return nextIP
	default:
		return ip
	}
}

// createSpanningCIDR 创建一个能够覆盖给定IP范围的最小CIDR
// @param r netWithRange: 包含IP范围首尾地址的netWithRange结构体
// @return net.IPNet net.IPNet: 能够覆盖给定IP范围的最小CIDR
func createSpanningCIDR(r netWithRange) net.IPNet {
	// 创建最低和最高IP地址的副本，避免修改原始值
	lowest := *r.First
	highest := *r.Last

	// 确定IP地址类型和相关参数
	var isIPv4 bool
	var spanningMaskSize, bitLen, byteLen int
	if lowest.To4() != nil {
		isIPv4 = true
		bitLen = ipv4BitLen
		byteLen = net.IPv4len
	} else {
		bitLen = ipv6BitLen
		byteLen = net.IPv6len
	}

	// 初始化掩码大小为最大值
	if isIPv4 {
		spanningMaskSize = ipv4BitLen
	} else {
		spanningMaskSize = ipv6BitLen
	}

	// 将IP地址转换为大整数，以便进行位操作
	// golang只提供最多64位的无符号整数，使用big.Int处理更大的数值
	lowestBig := big.NewInt(0).SetBytes(lowest)
	highestBig := big.NewInt(0).SetBytes(highest)

	// 从最大掩码/最小范围开始，逐步扩大范围（减小掩码）
	// 直到掩码足够小，能够覆盖整个IP范围
	for spanningMaskSize > 0 && lowestBig.Cmp(highestBig) < 0 {
		spanningMaskSize--
		// 创建掩码：2^(bitLen-spanningMaskSize) - 1，然后取反
		mask := big.NewInt(1)
		mask = mask.Lsh(mask, uint(bitLen-spanningMaskSize))
		mask = mask.Mul(mask, big.NewInt(-1))
		// 应用掩码到最高IP，将主机部分置零
		highestBig = highestBig.And(highestBig, mask)
	}

	// 将大整数转回IP地址格式
	// 对于IPv4，需要添加前导零，因为big.Int会去除前导零
	if isIPv4 {
		highest = append(ipv4LeadingZeroes, highestBig.Bytes()...) //nolint
	} else {
		highest = highestBig.Bytes()
	}

	// 处理整数为0的情况
	if len(highest) == 0 {
		highest = make([]byte, byteLen)
	}

	newNet := net.IPNet{IP: highest, Mask: net.CIDRMask(spanningMaskSize, bitLen)}
	return newNet
}

// mergeAdjacentCIDRs  合并相邻的CIDR网络范围
// @param ranges []*netWithRange:  要合并的网络范围列表
// @return []*netWithRange []*netWithRange: 合并后的网络范围列表
func mergeAdjacentCIDRs(ranges []*netWithRange) []*netWithRange {
	// 首先按范围排序：先按最后IP，再按第一个IP，最后按网络本身
	sort.Sort(NetsByRange(ranges))

	// 尝试合并相邻的CIDR
	for i := len(ranges) - 1; i > 0; i-- {
		// 获取当前范围的第一个IP的前一个IP
		first1 := getPreviousIP(*ranges[i].First)

		// 由于网络已排序，如果列表中的一个网络与列表中的另一个网络相邻，
		// 它将是列表中相邻的网络。如果当前处理的网络的前一个IP
		// 与列表中前一个网络的最后一个IP重叠，则可以合并这两个范围。
		if bytes.Compare(first1, *ranges[i-1].Last) <= 0 {
			// 选择两个范围中较小的第一个IP作为新范围的起始IP
			var minFirstIP *net.IP
			if bytes.Compare(*ranges[i-1].First, *ranges[i].First) < 0 {
				minFirstIP = ranges[i-1].First
			} else {
				minFirstIP = ranges[i].First
			}

			// 总是使用第i个范围的最后一个IP
			newRangeLast := make(net.IP, len(*ranges[i].Last))
			copy(newRangeLast, *ranges[i].Last)

			newRangeFirst := make(net.IP, len(*minFirstIP))
			copy(newRangeFirst, *minFirstIP)

			
			// 无法设置网络字段，因为我们正在合并一系列IP，
			// 还不知道哪些CIDR前缀表示新范围
			ranges[i-1] = &netWithRange{First: &newRangeFirst, Last: &newRangeLast, Network: nil}

			// 由于我们已将ranges[i]与ranges列表中的前一项合并，
			// 我们可以从切片中删除ranges[i]
			ranges = append(ranges[:i], ranges[i+1:]...)
		}
	}
	return ranges
}

// coalesceRanges 将IP范围转换为等效的CIDR列表
// @param ranges []*netWithRange: 要转换的IP范围列表
// @return []*net.IPNet []*net.IPNet: 转换后的CIDR列表
func coalesceRanges(ranges []*netWithRange) []*net.IPNet {
	coalescedCIDRs := []*net.IPNet{}
	// 从合并的范围中创建CIDR（如果需要）
	for _, netRange := range ranges {
		// 如果netWithRange的Network字段未被修改，则可以将其添加到返回列表中，
		// 因为它不能与列表中的任何其他CIDR合并
		if netRange.Network != nil {
			coalescedCIDRs = append(coalescedCIDRs, netRange.Network)
		} else {
			// 我们已经合并了两个范围，所以需要找到表示这个范围的新CIDR
			rangeCIDRs := rangeToCIDRs(*netRange.First, *netRange.Last)
			coalescedCIDRs = append(coalescedCIDRs, rangeCIDRs...)
		}
	}

	return coalescedCIDRs
}

// CoalesceCIDRs 将提供的CIDR列表转换为最小等效的IPv4和IPv6 CIDR集合
// @param cidrs []*net.IPNet: 要合并的CIDR列表
// @return coalescedIPV4 []*net.IPNet: 合并后的IPv4 CIDR列表
// @return coalescedIPV6 []*net.IPNet: 合并后的IPv6 CIDR列表
func CoalesceCIDRs(cidrs []*net.IPNet) (coalescedIPV4, coalescedIPV6 []*net.IPNet) {
	ranges4 := []*netWithRange{}
	ranges6 := []*netWithRange{}

	// 将每个CIDR网络转换为范围，并按IPv4/IPv6分类
	for _, network := range cidrs {
		newNetToRange := ipNetToRange(*network)
		if network.IP.To4() != nil {
			ranges4 = append(ranges4, &newNetToRange)
		} else {
			ranges6 = append(ranges6, &newNetToRange)
		}
	}
	coalescedIPV4 = coalesceRanges(mergeAdjacentCIDRs(ranges4))
	coalescedIPV6 = coalesceRanges(mergeAdjacentCIDRs(ranges6))
	return
}

// AggregateApproxIPv4To24 将IPv4地址列表聚合为近似的/24 CIDR块（C类网络）
// @param ips []*net.IPNet: 要聚合的IPv4地址列表
// @return []*net.IPNet []*net.IPNet: 聚合后的CIDR块列表
// @return error error: 可能的错误
func AggregateApproxIPv4To24(ips []*net.IPNet) ([]*net.IPNet, error) {
	// 如果IP地址少于2个，则无法聚合
	if len(ips) < 2 {
		return nil, errors.New("no enough ip to aggregate")
	}
	// 按IP地址排序
	sort.Slice(ips, func(i, j int) bool {
		return bytes.Compare(ips[i].IP, ips[j].IP) < 0
	})
	// 获取排序后的第一个和最后一个IP地址
	ip1 := ips[0].IP
	ip2 := ips[len(ips)-1].IP

	bothIPv4 := iputil.IsIPv4(ip1) && iputil.IsIPv4(ip2)

	if !bothIPv4 {
		return nil, errors.New("only ipv4 is supported")
	}
	// 验证IP地址有效性
	if ip1 == nil || ip2 == nil {
		return nil, errors.New("invalid IP address")
	}

	cidrs := make(map[string]*net.IPNet)
	for _, ip := range ips {
		// 检查IP的/24网段是否已存在于map中
		if n, ok := cidrs[ip.IP.Mask(net.CIDRMask(24, 32)).String()]; ok {
			var baseNet byte
			var nowN, newN byte
			for i := 8; i > 0; i-- {
				// 获取现有CIDR和新IP在第i-1位的值
				nowN = n.IP[3] & (1 << (i - 1)) >> (i - 1)
				newN = ip.IP[3] & (1 << (i - 1)) >> (i - 1)
				// 如果两个位都为1，则保留该位
				if nowN&newN == 1 {
					baseNet += 1 << (i - 1)
				}
				if nowN^newN == 1 {
					n.Mask = net.CIDRMask(32-i, 32)
					n.IP[3] = baseNet
					break
				}
			}
		} else {
			// 如果该/24网段不存在，则添加到map中
			cidrs[ip.IP.Mask(net.CIDRMask(24, 32)).String()] = ip
		}
	}

	// 将map中的CIDR转换为切片
	approxIPs := make([]*net.IPNet, len(cidrs))
	var index int
	for _, cidr := range cidrs {
		approxIPs[index] = cidr
		index++
	}
	// 按IP地址排序
	sort.Slice(approxIPs, func(i, j int) bool {
		return bytes.Compare(approxIPs[i].IP, approxIPs[j].IP) < 0
	})
	return approxIPs, nil
}

// FindMinCIDR  查找包含所有给定IP的最小CIDR
// @param ipNets []*net.IPNet: 要包含的IP列表
// @return *net.IPNet *net.IPNet: 包含所有给定IP的最小CIDR
// @return error error: 可能的错误
func FindMinCIDR(ipNets []*net.IPNet) (*net.IPNet, error) {
	if len(ipNets) == 0 {
		return nil, errors.New("empty IP list")
	}

	// 查找列表中的最小和最大IP地址
	minIP := ipNets[0].IP
	maxIP := ipNets[0].IP
	for _, ipNet := range ipNets {
		if bytes.Compare(ipNet.IP, minIP) < 0 {
			minIP = ipNet.IP
		}
		if bytes.Compare(ipNet.IP, maxIP) > 0 {
			maxIP = ipNet.IP
		}
	}

	// 计算最大和最小IP之间的差异（按位异或）
	diff := make(net.IP, len(minIP))
	for i := range minIP {
		diff[i] = maxIP[i] ^ minIP[i]
	}

	// 查找差异中最高有效位的位置
	prefixLen := len(minIP) * 8
	for i, b := range diff {
		if b == 0 {
			continue
		}
		// 找到第一个非零字节，计算前导零的数量
		prefixLen = i*8 + bits.LeadingZeros8(b)
		break
	}

	// 调整前缀长度以获得下一个2的幂
	prefixLen = (len(minIP) * 8) - bits.Len(uint((1<<uint(len(minIP)*8-prefixLen))-1))

	// 创建包含所有IP的CIDR
	mask := net.CIDRMask(prefixLen, len(minIP)*8)
	finalIPnet := &net.IPNet{
		IP:   minIP.Mask(mask),
		Mask: mask,
	}
	return finalIPnet, nil
}


// rangeToCIDRs 将IP范围转换为CIDR列表
// @param firstIP net.IP: 范围的第一个IP
// @param lastIP net.IP: 范围的最后一个IP
// @return []*net.IPNet []*net.IPNet: 覆盖整个IP范围的CIDR列表
func rangeToCIDRs(firstIP, lastIP net.IP) []*net.IPNet {
	// 首先，创建一个包含两个IP的CIDR
	spanningCIDR := createSpanningCIDR(netWithRange{&firstIP, &lastIP, nil})
	spanningRange := ipNetToRange(spanningCIDR)
	firstIPSpanning := spanningRange.First
	lastIPSpanning := spanningRange.Last

	cidrList := []*net.IPNet{}

	// 如果跨越CIDR的第一个IP小于范围的下界(firstIP)，
	// 我们需要分割跨越CIDR，只取大于分割值的IP，
	// 因为我们不需要小于下界(firstIP)的值。
	if bytes.Compare(*firstIPSpanning, firstIP) < 0 {
		// 在第一个IP的前一个IP上分割，这样分区的右侧IP列表就包括firstIP
		prevFirstRangeIP := getPreviousIP(firstIP)
		var bitLen int
		if prevFirstRangeIP.To4() != nil {
			bitLen = ipv4BitLen
		} else {
			bitLen = ipv6BitLen
		}
		_, _, right := partitionCIDR(spanningCIDR, net.IPNet{IP: prevFirstRangeIP, Mask: net.CIDRMask(bitLen, bitLen)})

		// 添加除第一个以外的所有CIDR，因为这个CIDR包含跨越CIDR的上界，
		// 我们仍然需要对其进行分区
		cidrList = append(cidrList, right...)
		spanningCIDR = *right[0]
		cidrList = cidrList[1:]
	}

	// 同样，如果跨越CIDR的最后一个IP大于范围的上界(lastIP)，
	// 我们需要分割跨越CIDR，只取小于分割值的IP，
	// 因为我们不需要大于上界(lastIP)的值
	if bytes.Compare(*lastIPSpanning, lastIP) > 0 {
		// 在最后一个IP的下一个IP上分割，这样分区的左侧IP列表就包括lastIP
		nextFirstRangeIP := GetNextIP(lastIP)
		var bitLen int
		if nextFirstRangeIP.To4() != nil {
			bitLen = ipv4BitLen
		} else {
			bitLen = ipv6BitLen
		}
		left, _, _ := partitionCIDR(spanningCIDR, net.IPNet{IP: nextFirstRangeIP, Mask: net.CIDRMask(bitLen, bitLen)})
		cidrList = append(cidrList, left...)
	} else {
		// 否则，不需要分区，只需将跨越CIDR添加到网络列表中
		cidrList = append(cidrList, &spanningCIDR)
	}
	return cidrList
}

// partitionCIDR 根据排除CIDR对目标CIDR进行分区
// @param targetCIDR net.IPNet: 要分区的目标CIDR
// @param excludeCIDR net.IPNet: 用于分区的排除CIDR
// @return left []*net.IPNet: 分区中位于excludeCIDR左侧的网络列表
// @return excludeList []*net.IPNet: 果excludeCIDR包含在targetCIDR中，则包含excludeCIDR本身的列表（否则为nil）
// @return right []*net.IPNet: 分区中位于excludeCIDR右侧的网络列表
func partitionCIDR(targetCIDR, excludeCIDR net.IPNet) (left, excludeList, right []*net.IPNet) { //nolint
	var targetIsIPv4 bool
	if targetCIDR.IP.To4() != nil {
		targetIsIPv4 = true
	}

	// 将目标CIDR转换为IP范围（包含首尾IP地址）
	targetIPRange := ipNetToRange(targetCIDR)
	// 将排除CIDR转换为IP范围
	excludeIPRange := ipNetToRange(excludeCIDR)

	targetFirstIP := *targetIPRange.First
	targetLastIP := *targetIPRange.Last

	excludeFirstIP := *excludeIPRange.First
	excludeLastIP := *excludeIPRange.Last

	targetMaskSize, _ := targetCIDR.Mask.Size()
	excludeMaskSize, _ := excludeCIDR.Mask.Size()

	// 如果排除CIDR的最后IP小于目标CIDR的第一个IP，则排除CIDR完全在目标CIDR左侧
	// 此时无需分割，直接返回目标CIDR作为右侧部分
	if bytes.Compare(excludeLastIP, targetFirstIP) < 0 {
		return nil, nil, []*net.IPNet{&targetCIDR}
	} else if bytes.Compare(targetLastIP, excludeFirstIP) < 0 {
		return []*net.IPNet{&targetCIDR}, nil, nil
	}

	// 如果目标CIDR的掩码大小大于等于排除CIDR的掩码大小，
	// 表示目标CIDR是排除CIDR的子网或相等，直接返回目标CIDR作为排除列表
	if targetMaskSize >= excludeMaskSize {
		return nil, []*net.IPNet{&targetCIDR}, nil
	}

	left = []*net.IPNet{}
	right = []*net.IPNet{}

	newPrefixLen := targetMaskSize + 1

	targetFirstCopy := make(net.IP, len(targetFirstIP))
	copy(targetFirstCopy, targetFirstIP)

	iLowerOld := make(net.IP, len(targetFirstCopy))
	copy(iLowerOld, targetFirstCopy)

	// 由于golang只支持最多64位的无符号整数，而我们需要对地址进行加法运算，
	// 因此使用math/big库，它允许操作大整数。

	// 用于跟踪要与excludeCIDR比较的范围的当前下限和上限。
	// 初始化大整数用于表示下限IP地址
	iLower := big.NewInt(0)
	iUpper := big.NewInt(0)
	iLower = iLower.SetBytes(targetFirstCopy)

	var bitLen int

	if targetIsIPv4 {
		bitLen = ipv4BitLen
	} else {
		bitLen = ipv6BitLen
	}
	// 计算位移量：总位长度减去新的前缀长度
	shiftAmount := uint(bitLen - newPrefixLen)

	targetIPInt := big.NewInt(0)
	targetIPInt.SetBytes(targetFirstIP.To16())

	// 初始化用于计算2的幂的大整数
	exp := big.NewInt(0)

	// 使用左移进行2的幂次方计算：2^(bitLen-newPrefixLen)
	exp = exp.Lsh(big.NewInt(1), shiftAmount)
	// 计算上限IP：目标IP + 2^(bitLen-newPrefixLen)
	iUpper = iUpper.Add(targetIPInt, exp)

	matched := big.NewInt(0)

	for excludeMaskSize >= newPrefixLen {
		// 为IPv4地址添加前导零，因为math.Big.Int在将IP地址从字节数组复制到
		// math.Big.Int时不会添加它们。解析IPv4地址以供net.IP/net.IPNet使用时需要前导零。
		var iUpperBytes, iLowerBytes []byte
		if targetIsIPv4 {
			iUpperBytes = append(ipv4LeadingZeroes, iUpper.Bytes()...) //nolint
			iLowerBytes = append(ipv4LeadingZeroes, iLower.Bytes()...) //nolint
		} else {
			// 对于IPv6地址，确保字节数组长度正确
			iUpperBytesLen := len(iUpper.Bytes())
			// 确保数组中的字节数与net包期望的匹配，因为big包不会添加前导零
			if iUpperBytesLen != net.IPv6len {
				numZeroesToAppend := net.IPv6len - iUpperBytesLen
				zeroBytes := make([]byte, numZeroesToAppend)
				iUpperBytes = append(zeroBytes, iUpper.Bytes()...) //nolint
			} else {
				iUpperBytes = iUpper.Bytes()
			}

			// 对下限IP地址执行相同的处理
			iLowerBytesLen := len(iLower.Bytes())
			if iLowerBytesLen != net.IPv6len {
				numZeroesToAppend := net.IPv6len - iLowerBytesLen
				zeroBytes := make([]byte, numZeroesToAppend)
				iLowerBytes = append(zeroBytes, iLower.Bytes()...) //nolint
			} else {
				iLowerBytes = iLower.Bytes()
			}
		}
		// 如果我们要排除的IP的值高于当前生成的CIDR前缀，
		// 则将CIDR前缀添加到排除CIDR左侧的IP集合中
		if bytes.Compare(excludeFirstIP, iUpperBytes) >= 0 {
			left = append(left, &net.IPNet{IP: iLowerBytes, Mask: net.CIDRMask(newPrefixLen, bitLen)})
			matched = matched.Set(iUpper)
		} else {
			// 与上面相同，但相反方向
			// 将当前CIDR添加到右侧列表
			right = append(right, &net.IPNet{IP: iUpperBytes, Mask: net.CIDRMask(newPrefixLen, bitLen)})
			matched = matched.Set(iLower)
		}

		newPrefixLen++

		if newPrefixLen > bitLen {
			break
		}

		// 更新下限IP为匹配的IP
		iLower = iLower.Set(matched)
		// 计算新的上限IP：下限IP + 2^(bitLen-newPrefixLen)
		iUpper = iUpper.Add(matched, big.NewInt(0).Lsh(big.NewInt(1), uint(bitLen-newPrefixLen)))
	}
	excludeList = []*net.IPNet{&excludeCIDR}

	return left, excludeList, right
}

// KeepUniqueIPs 将提供的IP多重集合转换为单一有序集合
// @param ips []net.IP: 要处理的IP地址切片
// @return []net.IP []net.IP: 去重并排序后的IP地址切片
func KeepUniqueIPs(ips []net.IP) []net.IP {
	// 按字节比较对IP切片进行排序
	sort.Slice(ips, func(i, j int) bool {
		return bytes.Compare(ips[i], ips[j]) == -1
	})

	// 创建一个长度为0但容量与输入相同的切片
	returnIPs := ips[:0]

	// 遍历排序后的切片，仅保留唯一IP
	for readIdx, ip := range ips {
		// 如果returnIPs为空或当前IP与最后添加的IP不同，则添加到结果中
		if len(returnIPs) == 0 || !returnIPs[len(returnIPs)-1].Equal(ips[readIdx]) {
			returnIPs = append(returnIPs, ip)
		}
	}

	return returnIPs
}

// IsExcluded 检查给定IP是否应该被排除
// @param excludeList []net.IP: 要排除的IP地址列表
// @param ip net.IP: 要检查的IP地址
// @return bool bool: 如果IP在排除列表中返回true，否则返回false
func IsExcluded(excludeList []net.IP, ip net.IP) bool {
	for _, e := range excludeList {
		// 如果找到匹配项，返回true
		if e.Equal(ip) {
			return true
		}
	}
	return false
}

// GetCIDRPrefixesFromIPs IP地址列表转换为CIDR前缀列表
// @param ips []net.IP: 要转换的IP地址列表
// @return []*net.IPNet []*net.IPNet: 转换后的CIDR前缀列表
func GetCIDRPrefixesFromIPs(ips []net.IP) []*net.IPNet {
	if len(ips) == 0 {
		return nil
	}
	res := make([]*net.IPNet, 0, len(ips))
	// 遍历每个IP地址，转换为CIDR
	for _, ip := range ips {
		res = append(res, IPToPrefix(ip))
	}
	return res
}

// IPToPrefix 返回给定IP对应的CIDR前缀
// @param ip net.IP: 要转换的IP地址
// @return *net.IPNet *net.IPNet: 对应的CIDR前缀
func IPToPrefix(ip net.IP) *net.IPNet {
	// 确定是否为IPv4地址，并设置相应的位长度
	bits := net.IPv6len * 8
	if ip.To4() != nil {
		ip = ip.To4()
		bits = net.IPv4len * 8
	}
	// 创建CIDR前缀
	prefix := &net.IPNet{
		IP:   ip,
		Mask: net.CIDRMask(bits, bits),
	}
	return prefix
}

// Inet_ntoa 将整数转换为IP地址
// @param ipnr int64: 表示IP地址的整数
// @return net.IP net.IP: 转换后的ip地址
func Inet_ntoa(ipnr int64) net.IP { //nolint
	var b [4]byte
	// 将整数转换为4个字节（按照网络字节序）
	b[0] = byte(ipnr & 0xFF)         //nolint
	b[1] = byte((ipnr >> 8) & 0xFF)  //nolint
	b[2] = byte((ipnr >> 16) & 0xFF) //nolint
	b[3] = byte((ipnr >> 24) & 0xFF) //nolint

	return net.IPv4(b[3], b[2], b[1], b[0])
}

// Inet_aton 将IP地址转换为整数
// @param ipnr net.IP: 要转换的IP地址
// @return int64 int64: 转换后的整数表示
func Inet_aton(ipnr net.IP) int64 { //nolint
	// 分割IP地址为4个部分
	bits := strings.Split(ipnr.String(), ".")

	// 将每个部分转换为整数
	b0, _ := strconv.Atoi(bits[0])
	b1, _ := strconv.Atoi(bits[1])
	b2, _ := strconv.Atoi(bits[2])
	b3, _ := strconv.Atoi(bits[3])

	var sum int64

	// 按照网络字节序（大端序）合并为一个整数
	sum += int64(b0) << 24
	sum += int64(b1) << 16
	sum += int64(b2) << 8
	sum += int64(b3)

	return sum
}

// ToIP6 将IP地址转换为IPv6格式
// @param host string:  要转换的IP地址字符串
// @return string string: 转换后的IPv6地址字符串
// @return error error: 可能的错误
func ToIP6(host string) (string, error) {
	ip := net.ParseIP(host)
	switch {
	default:
		return "", errors.New("Couldn't parse IP")
	case ip == nil:
		return "", errors.New("Couldn't parse IP")
	case ip.To4() == nil && ip.To16() != nil:
		// 已经是IPv6地址
		return host, nil
	case ip.To4() != nil:
		// IPv4地址，转换为IPv4-mapped IPv6地址格式
		ipv4 := ip.To4()
		return fmt.Sprintf("::ffff:%d.%d.%d.%d", ipv4[0], ipv4[1], ipv4[2], ipv4[3]), nil
	}
}

// ToIP4  将IP地址转换为IPv4格式
// @param host string: 
// @return string string: 
// @return error error: 
func ToIP4(host string) (string, error) {
	ip := net.ParseIP(host)
	switch {
	default:
		return "", errors.New("Couldn't parse IP")
	case ip == nil:
		return "", errors.New("Couldn't parse IP")
	case ip.To4() != nil:
		// 直接返回IPv4地址的字符串表示
		return ip.To4().String(), nil
	case ip.To16() != nil:
		// 检查是否为IPv4-mapped IPv6地址
		if len(ip) == net.IPv6len &&
			ip[0] == 0 && ip[1] == 0 &&
			ip[2] == 0 && ip[3] == 0 &&
			ip[4] == 0 && ip[5] == 0 &&
			ip[6] == 0 && ip[7] == 0 &&
			ip[8] == 0 && ip[9] == 0 &&
			ip[10] == 0xff && ip[11] == 0xff {
			// 提取IPv4部分
			return fmt.Sprintf("%d.%d.%d.%d", ip[12], ip[13], ip[14], ip[15]), nil
		}
		return "", errors.New("Not an IPv4-mapped IPv6 address")
	}
}

// FmtIP4MappedIP6 以IPv6格式打印IPv4映射的IPv6地址
// @param ip6 net.IP:  IPv6格式的IP地址
// @return string string: 格式化后的IPv6地址字符串
func FmtIP4MappedIP6(ip6 net.IP) string {
	return fmt.Sprintf("00:00:00:00:00:ffff:%02x%02x:%02x%02x", ip6[12], ip6[13], ip6[14], ip6[15])
}

// FmtIP4MappedIP6Short  以简短格式打印IPv4映射的IPv6地址
// @param ip6 net.IP: IPv6格式的IP地址
// @return string string: 简短格式的IPv6地址字符串
func FmtIP4MappedIP6Short(ip6 net.IP) string {
	return fmt.Sprintf("::ffff:%02x%02x:%02x%02x", ip6[12], ip6[13], ip6[14], ip6[15])
}

// FmtIp6 格式化IPv6地址
// @param ip net.IP:  要格式化的IP地址
// @param short bool: 是否使用简短格式
// @return string string: 格式化后的IPv6地址字符串
// @return error error: 可能的错误
func FmtIp6(ip net.IP, short bool) (string, error) {
	// 检查是否为IPv6地址
	if ip6 := ip.To16(); ip6 != nil {
		// 检查是否为IPv4地址，如果是则返回IPv4映射的IPv6格式
		if ip.To4() != nil {
			if short {
				return FmtIP4MappedIP6Short(ip6), nil
			}
			return FmtIP4MappedIP6(ip6), nil
		}
		// 否则直接返回IPv6地址
		return ip6.String(), nil
	}
	return "", fmt.Errorf("%s can't be expressed as ipv6", ip.String())
}

// FixedPad 为IP地址的每个部分添加固定长度的零填充
// @param ip net.IP:  要填充的IP地址
// @param padding int: 填充的长度
// @return string string: 填充后的IP地址字符串
func FixedPad(ip net.IP, padding int) string {
	parts := strings.Split(ip.String(), ".")
	var format bytes.Buffer
	format.WriteString("%#0" + fmt.Sprint(padding) + "s")
	format.WriteString(".%#0" + fmt.Sprint(padding) + "s")
	format.WriteString(".%#0" + fmt.Sprint(padding) + "s")
	format.WriteString(".%#0" + fmt.Sprint(padding) + "s")
	return fmt.Sprintf(format.String(), parts[0], parts[1], parts[2], parts[3])
}
// IncrementalPad 为IP地址的各部分生成所有可能的零填充组合
// @param ip net.IP: 要填充的IP地址
// @param padding int: 最大填充长度
// @return []string []string: 所有可能的填充组合的IP地址字符串数组
func IncrementalPad(ip net.IP, padding int) []string {
	parts := strings.Split(ip.String(), ".")
	var ips []string
	// 记录已生成的IP地址，避免重复
	uniqueIPs := make(map[string]struct{})
	for p1 := 0; p1 < padding; p1++ {
		for p2 := 0; p2 < padding; p2++ {
			for p3 := 0; p3 < padding; p3++ {
				for p4 := 0; p4 < padding; p4++ {
					// 为每个部分构建正确的格式化字符串
					f1 := getFormatForPadding(p1)
					f2 := getFormatForPadding(p2)
					f3 := getFormatForPadding(p3)
					f4 := getFormatForPadding(p4)

					// 应用格式化
					part1 := fmt.Sprintf(f1, parts[0])
					part2 := fmt.Sprintf(f2, parts[1])
					part3 := fmt.Sprintf(f3, parts[2])
					part4 := fmt.Sprintf(f4, parts[3])

					// 组合为完整IP
					alteredIP := part1 + "." + part2 + "." + part3 + "." + part4
					// 检查是否已存在此IP地址
					if _, exists := uniqueIPs[alteredIP]; !exists {
						uniqueIPs[alteredIP] = struct{}{}
						ips = append(ips, alteredIP)
					}
				}
			}
		}
	}
	return ips
}


// getFormatForPadding 根据填充大小返回适当的格式化字符串
func getFormatForPadding(padding int) string {
	if padding <= 0 {
		return "%s" // 不添加前导零
	}
	return "%0" + strconv.Itoa(padding+1) + "s" // +1是为了保证有足够空间放下原始数字
}

// AlterIP 生成IP地址的各种表示形式
// @param ip string:  要处理的IP地址字符串
// @param formats []string: 要生成的表示形式类型列表
// @param zeroPadN int: 零填充的长度
// @param zeroPadPermutation bool:  是否生成所有可能的零填充组合
// @return []string []string: 生成的IP地址表示形式列表
func AlterIP(ip string, formats []string, zeroPadN int, zeroPadPermutation bool) []string {
	var alteredIPs []string

	for _, format := range formats {
		// 解析IP地址
		standardIP := net.ParseIP(ip)
		switch format {
		case "1":
			// 点分十进制表示法
			// 标准格式 x.x.x.x 或 xxxx:xxxx:xxxx:xxxx:xxxx:xxxx:xxxx:xxxx
			alteredIPs = append(alteredIPs, standardIP.String())
		case "2":
			// 零优化的点分十进制表示法
			// IP地址中的0值段可以省略（如 127.0.0.1 => 127.1）
			// 匹配带点的零值 0000.
			var reZeroesWithDot = regexp.MustCompile(`(?m)[0]+\.`)
			// 匹配 .0000
			var reDotWithZeroes = regexp.MustCompile(`(?m)\.[0^]+$`)
			// 去除 0000.
			alteredIP := reZeroesWithDot.ReplaceAllString(standardIP.String(), "")
			// 去除 .0000
			alteredIP = reDotWithZeroes.ReplaceAllString(alteredIP, "")
			alteredIPs = append(alteredIPs, alteredIP)
		case "3":
			// 八进制表示法（需要前导零）：
			// 如: 127.0.0.1 => 0127.0.0.01
			alteredIP := fmt.Sprintf("%#04o.%#o.%#o.%#o", standardIP[12], standardIP[13], standardIP[14], standardIP[15])
			alteredIPs = append(alteredIPs, alteredIP)
		case "4":
			// 十六进制表示法
			// 127.0.0.1 => 0x7f.0x0.0x0.0x1
			// 127.0.0.1 => 0x7f000001
			// 127.0.0.1 => 0xaaaaaaaaaaaaaaaa7f000001（随机前缀）
			alteredIPWithDots := fmt.Sprintf("%#x.%#x.%#x.%#x", standardIP[12], standardIP[13], standardIP[14], standardIP[15])
			alteredIPWithZeroX := fmt.Sprintf("0x%s", hex.EncodeToString(standardIP[12:]))
			alteredIPWithRandomPrefixHex, _ := RandomHex(5, standardIP[12:])
			alteredIPWithRandomPrefix := fmt.Sprintf("0x%s", alteredIPWithRandomPrefixHex)
			alteredIPs = append(alteredIPs, alteredIPWithDots, alteredIPWithZeroX, alteredIPWithRandomPrefix)
		case "5":
			// 十进制表示法（又称dword表示法）
			// 127.0.0.1 => 2130706433
			bigIP, _, _ := IPToInteger(standardIP)
			alteredIPs = append(alteredIPs, bigIP.String())
		case "6":
			// 二进制表示法
			// 127.0.0.1 => 01111111000000000000000000000001
			// 转换为整数
			bigIP, _, _ := IPToInteger(standardIP)
			// 然后转换为二进制
			alteredIP := fmt.Sprintf("%b", bigIP)
			alteredIPs = append(alteredIPs, alteredIP)
		case "7":
			// 混合表示法
			// 仅适用于IPv4
			alteredIP := fmt.Sprintf("%#x.%d.%#o.%#x", standardIP[12], standardIP[13], standardIP[14], standardIP[15])
			alteredIPs = append(alteredIPs, alteredIP)
		case "8":
			// IPv6格式
			// 0000000000000:0000:0000:0000:0000:00000000000000:0000:1 => ::1
			// 0000:0000:0000:0000:0000:0000:0000:0001 => ::1
			// 0:0:0:0:0:0:0:1 => ::1
			// 0:0:0:0::0:0:1 => ::1
			// 标准库已应用零压缩和抑制
			// 将IP转换为IPv6（如果可能），隐式对原生IPv6地址执行零压缩
			ip6, err := FmtIp6(standardIP, true)
			if err == nil {
				alteredIPs = append(alteredIPs, ip6)
			}
		case "9":
			// URL编码的IP地址
			// 127.0.0.1 => %31%32%37%2E%30%2E%30%2E%31
			// ::1 => %3A%3A%31
			alteredIP := escape(ip)
			alteredIPs = append(alteredIPs, alteredIP)
		case "10":
			// 零填充 - 在IP各部分前添加不同数量的零
			// 127.0.0.1 => 0127.00.00.01
			// 仅适用于IPv4
			if zeroPadPermutation {
				// 生成所有可能的零填充组合
				alteredIPs = append(alteredIPs, IncrementalPad(standardIP, zeroPadN)...)
			} else {
				alteredIPs = append(alteredIPs, FixedPad(standardIP, zeroPadN))
			}
		case "11":
			// IP溢出 - 尝试将IP表示为最后一个八位字节的溢出
			// ********* => 127.0.256
			// 仅适用于IPv4
			alteredIP, err := overflowLastOctect(standardIP)
			if err == nil {
				alteredIPs = append(alteredIPs, alteredIP)
			}
		}
	}

	return alteredIPs
}

// overflowLastOctect 将最后两个八位字节合并为一个
// @param ip net.IP: 要处理的IP地址
// @return string string: 处理后的IP地址字符串
// @return error error: 可能的错误
func overflowLastOctect(ip net.IP) (string, error) {
	// 分割IP地址为点分十进制的各部分
	parts := stringsutil.SplitAny(ip.String(), ".")
	if len(parts) != 4 {
		return "", errors.New("invalid ipv4")
	}
	// 转换倒数第二个部分为整数
	part2, err := strconv.Atoi(parts[2])
	if err != nil {
		return "", err
	}
	// 转换最后一个部分为整数
	part3, err := strconv.Atoi(parts[3])
	if err != nil {
		return "", err
	}
	// 只有当最后一个部分为0时才能进行溢出表示
	if part3 == 0 {
		part3 = 255
	} else {
		return "", errors.New("can't convert to overflow ip")
	}
	return fmt.Sprintf("%s.%s.%d", parts[0], parts[1], part2+part3), nil
}

// GetCIDRFromIPRange 从IP范围获取CIDR列表
// @param firstIP net.IP: 范围的第一个IP地址
// @param lastIP net.IP: 范围的最后一个IP地址
// @return []*net.IPNet []*net.IPNet:  覆盖该IP范围的CIDR列表，按IP地址排序
// @return error error: 可能的错误
func GetCIDRFromIPRange(firstIP, lastIP net.IP) ([]*net.IPNet, error) {
	// 检查范围是否有效
	if bytes.Compare(firstIP, lastIP) > 0 {
		return nil, fmt.Errorf("start IP:%s must be less than End IP:%s", firstIP, lastIP)
	}
	cidrs := rangeToCIDRs(firstIP, lastIP)
	// 按IP地址排序
	sort.Slice(cidrs, func(i, j int) bool {
		return bytes.Compare(cidrs[i].IP, cidrs[j].IP) < 0
	})
	return cidrs, nil
}

// IpRangeToCIDR 将IP范围字符串转换为CIDR列表
// @param start string: 起始IP地址字符串
// @param end string: 结束IP地址字符串
// @return []string []string: 覆盖该IP范围的CIDR字符串列表
// @return error error: 可能的错误
func IpRangeToCIDR(start, end string) ([]string, error) {
	// 解析起始IP地址
	ips, err := netip.ParseAddr(start)
	if err != nil {
		return nil, err
	}
	// 解析结束IP地址
	ipe, err := netip.ParseAddr(end)
	if err != nil {
		return nil, err
	}

	// 检查是否为IPv4地址
	isV4 := ips.Is4()
	if isV4 != ipe.Is4() {
		return nil, errors.New("start and end types are different")
	}
	if ips.Compare(ipe) > 0 {
		return nil, errors.New("start > end")
	}

	var (
		// 将IP地址转换为大整数以便进行数学运算
		ipsInt = new(big.Int).SetBytes(ips.AsSlice())
		ipeInt = new(big.Int).SetBytes(ipe.AsSlice())
		nextIp = new(big.Int)
		maxBit = new(big.Int)
		cmpSh  = new(big.Int)
		bits   = new(big.Int)
		mask   = new(big.Int)
		one    = big.NewInt(1)
		buf    []byte
		cidr   []string
		bitSh  uint
	)
	// 根据IP类型设置最大位数和缓冲区大小
	if isV4 {
		maxBit.SetUint64(32)
		buf = make([]byte, 4)
	} else {
		maxBit.SetUint64(128)
		buf = make([]byte, 16)
	}

	for {
		bits.SetUint64(1)
		mask.SetUint64(1)
		// 寻找最佳CIDR前缀长度
		for bits.Cmp(maxBit) < 0 {
			nextIp.Or(ipsInt, mask)

			bitSh = uint(bits.Uint64())
			cmpSh.Lsh(cmpSh.Rsh(ipsInt, bitSh), bitSh)
			// 如果最大IP超过了结束IP或者地址不对齐，回退一位并退出循环
			if (nextIp.Cmp(ipeInt) > 0) || (cmpSh.Cmp(ipsInt) != 0) {
				bits.Sub(bits, one)
				mask.Rsh(mask, 1)
				break
			}
			bits.Add(bits, one)
			mask.Add(mask.Lsh(mask, 1), one)
		}

		// 创建CIDR字符串并添加到结果列表
		addr, _ := netip.AddrFromSlice(ipsInt.FillBytes(buf))
		cidr = append(cidr, addr.String()+"/"+bits.Sub(maxBit, bits).String())

		// 如果当前CIDR已经覆盖或超过了结束IP，结束循环
		if nextIp.Or(ipsInt, mask); nextIp.Cmp(ipeInt) >= 0 {
			break
		}
		ipsInt.Add(nextIp, one)
	}
	return cidr, nil
}