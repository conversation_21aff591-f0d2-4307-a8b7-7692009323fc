// Author: chenjb
// Version: V1.0
// Date: 2025-06-17 17:00:01
// FilePath: /yaml_scan/internal/runner/options.go
// Description:
package runner

import (
	"fmt"
	"os"
	"yaml_scan/pkg/reporting"
	"yaml_scan/pkg/reporting/exporters/jsonexporter"
	"yaml_scan/pkg/reporting/exporters/jsonl"
	"yaml_scan/pkg/reporting/exporters/markdown"
	"yaml_scan/pkg/reporting/exporters/sarif"
	"yaml_scan/pkg/types"
	"yaml_scan/pkg/utils/yaml"
)


func createReportingOptions(options *types.Options) (*reporting.Options, error) {
	var reportingOptions = &reporting.Options{}
	if options.ReportingConfig != "" {
		file, err := os.Open(options.ReportingConfig)
		if err != nil {
			return nil, fmt.Errorf("could not open reporting config file %s", err)
		}
		defer file.Close()

		if err := yaml.DecodeAndValidate(file, reportingOptions); err != nil {
			return nil, fmt.Errorf("could not parse reporting config file %s", err)
		}
		Walk(reportingOptions, expandEndVars)
	}
	if options.MarkdownExportDirectory != "" {
		reportingOptions.MarkdownExporter = &markdown.Options{
			Directory: options.MarkdownExportDirectory,
			OmitRaw:   options.OmitRawRequests,
			SortMode:  options.MarkdownExportSortMode,
		}
	}
	if options.SarifExport != "" {
		reportingOptions.SarifExporter = &sarif.Options{File: options.SarifExport}
	}
	if options.JSONExport != "" {
		reportingOptions.JSONExporter = &jsonexporter.Options{
			File:    options.JSONExport,
			OmitRaw: options.OmitRawRequests,
		}
	}
	// Combine options.
	if options.JSONLExport != "" {
		// Combine the CLI options with the config file options with the CLI options taking precedence
		if reportingOptions.JSONLExporter != nil {
			reportingOptions.JSONLExporter.File = options.JSONLExport
			reportingOptions.JSONLExporter.OmitRaw = options.OmitRawRequests
		} else {
			reportingOptions.JSONLExporter = &jsonl.Options{
				File:    options.JSONLExport,
				OmitRaw: options.OmitRawRequests,
			}
		}
	}

	reportingOptions.OmitRaw = options.OmitRawRequests
	return reportingOptions, nil
}

