//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-15 16:14:13
//FilePath: /yaml_scan/pkg/input/formats/openapi/generator.go
//Description:

package openapi

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/http/httputil"
	"net/url"
	"os"
	"strings"

	"yaml_scan/pkg/config"
	"yaml_scan/pkg/gologger"
	"yaml_scan/pkg/input/formats"
	httpTypes "yaml_scan/pkg/input/types"
	"yaml_scan/pkg/types"
	"yaml_scan/utils/generic"
	mapsutil "yaml_scan/utils/maps"

	"github.com/clbanning/mxj/v2"
	"github.com/getkin/kin-openapi/openapi3"
	"github.com/pkg/errors"
	"github.com/valyala/fasttemplate"
)

const (
	globalAuth                 = "globalAuth"
	DEFAULT_HTTP_SCHEME_HEADER = "Authorization"
)

// generateReqOptions 用于存储生成HTTP请求时所需的选项和参数
type generateReqOptions struct {
	// requiredOnly 指定是否仅生成必需字段
	// 如果为true，则只生成那些在OpenAPI定义中标记为必需的字段。
	requiredOnly bool

	// method 是要使用的HTTP方法
	// 例如，"GET", "POST", "PUT", "DELETE"等。
	method string

	// pathURL 是要使用的基础URL
	// 这是请求的基础部分，通常包括协议和主机名，例如 "https://api.example.com"。
	pathURL string

	// requestPath 是要使用的路径
	// 这是请求的路径部分，通常在基础URL之后，例如 "/v1/users"。
	requestPath string

	// schema 是要使用的OpenAPI模式
	// 这是一个指向OpenAPI 3.0文档对象的指针，包含API的所有定义。
	schema *openapi3.T

	// op 是要使用的操作
	// 这是一个指向OpenAPI 3.0操作对象的指针，表示特定路径和方法的操作。
	op *openapi3.Operation

	// callback 是请求生成后的回调函数
	// 这是一个函数，用于处理生成的请求和响应。
	callback formats.ParseReqRespCallback

	// globalParams 是全局参数
	// 这是一个参数列表，包含在请求中使用的全局参数。
	globalParams openapi3.Parameters

	// reqParams 是请求参数映射
	// 这是一个参数列表，包含特定请求中使用的参数。
	reqParams openapi3.Parameters

	// opts 是全局变量映射
	// 这是一个输入格式选项，包含生成请求时使用的各种选项和变量。
	opts formats.InputFormatOptions

	// missingParamValueCallback 是缺失参数值的回调函数
	// 这是一个函数，用于处理在生成请求时缺失的参数值。
	missingParamValueCallback func(param *openapi3.Parameter, opts *generateReqOptions)
}

// GenerateParameterFromSecurityScheme: 根据安全方案生成参数对象
//
//	@param scheme *openapi3.SecuritySchemeRef: 表示OpenAPI安全方案的引用。
//	@return *openapi3.Parameter *openapi3.Parameter: 返回生成的参数对象，表示安全方案的具体实现。
//	@return error error: 如果生成过程中出现错误，返回一个error对象，否则返回nil。
func GenerateParameterFromSecurityScheme(scheme *openapi3.SecuritySchemeRef) (*openapi3.Parameter, error) {
	// 检查安全方案的类型是否为支持的类型（http或apiKey）
	if !generic.EqualsAny(scheme.Value.Type, "http", "apiKey") {
		return nil, fmt.Errorf("openapi unsupported security scheme type (%s) found in openapi file", scheme.Value.Type)
	}
	// 处理HTTP类型的安全方案
	if scheme.Value.Type == "http" {
		// 检查HTTP方案是否为支持的方案（basic或bearer）
		if !generic.EqualsAny(scheme.Value.Scheme, "basic", "bearer") {
			return nil, fmt.Errorf("openapi unsupported security scheme (%s) found in openapi file", scheme.Value.Scheme)
		}
		// HTTP认证方案（basic或bearer）使用Authorization头
		headerName := scheme.Value.Name
		if headerName == "" {
			headerName = DEFAULT_HTTP_SCHEME_HEADER
		}
		// 根据方案创建参数
		switch scheme.Value.Scheme {
		case "basic":
			h := openapi3.NewHeaderParameter(headerName)
			h.Required = true
			h.Description = globalAuth // 用于区分普通变量和全局认证
			return h, nil
		case "bearer":
			h := openapi3.NewHeaderParameter(headerName)
			h.Required = true
			h.Description = globalAuth // 用于区分普通变量和全局认证
			return h, nil
		}

	}
	// 处理apiKey类型的安全方案
	if scheme.Value.Type == "apiKey" {
		// 验证名称和位置
		if scheme.Value.Name == "" {
			return nil, fmt.Errorf("openapi security scheme (%s) name is empty", scheme.Value.Type)
		}
		if !generic.EqualsAny(scheme.Value.In, "query", "header", "cookie") {
			return nil, fmt.Errorf("openapi unsupported security scheme (%s) in (%s) found in openapi file", scheme.Value.Type, scheme.Value.In)
		}
		// 根据位置创建参数
		switch scheme.Value.In {
		case "query":
			q := openapi3.NewQueryParameter(scheme.Value.Name)
			q.Required = true
			q.Description = globalAuth
			return q, nil
		case "header":
			h := openapi3.NewHeaderParameter(scheme.Value.Name)
			h.Required = true
			h.Description = globalAuth
			return h, nil
		case "cookie":
			c := openapi3.NewCookieParameter(scheme.Value.Name)
			c.Required = true
			c.Description = globalAuth
			return c, nil
		}
	}
	return nil, fmt.Errorf("openapi unsupported security scheme type (%s) found in openapi file", scheme.Value.Type)
}

// GetGlobalParamsForSecurityRequirement: 返回与安全要求相关的全局参数
//
//	@param schema *openapi3.T: OpenAPI 3.0文档对象。
//	@param requirement *openapi3.SecurityRequirements: 表示安全要求的列表。
//	@return []*openapi3.ParameterRef []*openapi3.ParameterRef: 返回一个参数引用的切片，表示与安全要求相关的全局参数。
//	@return error error: 如果在提取过程中出现错误，返回一个error对象，否则返回nil。
func GetGlobalParamsForSecurityRequirement(schema *openapi3.T, requirement *openapi3.SecurityRequirements) ([]*openapi3.ParameterRef, error) {
	// 初始化一个新的参数切片，用于存储全局参数
	globalParams := openapi3.NewParameters()
	// 检查OpenAPI文档中是否定义了安全方案
	if len(schema.Components.SecuritySchemes) == 0 {
		return nil, fmt.Errorf("openapi security requirements (%+v) without any security schemes found in openapi file", schema.Security)
	}
	// 标记是否找到匹配的安全方案
	found := false
	// 遍历每个安全要求，提取相应的安全方案
schemaLabel:
	for _, security := range *requirement {
		for name := range security {
			// 检查安全方案是否在OpenAPI文档中定义
			if scheme, ok := schema.Components.SecuritySchemes[name]; ok {
				found = true
				// 从安全方案生成参数
				param, err := GenerateParameterFromSecurityScheme(scheme)
				if err != nil {
					return nil, err

				}
				// 将生成的参数添加到全局参数列表中
				globalParams = append(globalParams, &openapi3.ParameterRef{Value: param})
				continue schemaLabel
			}
		}
		// 如果未找到匹配的安全方案且安全要求包含多个方案，返回错误
		if !found && len(security) > 1 {
			return nil, fmt.Errorf("openapi security requirement (%+v) not found in openapi file", security)
		}
	}
	// 如果未找到任何匹配的安全方案，返回错误
	if !found {
		return nil, fmt.Errorf("openapi security requirement (%+v) not found in openapi file", requirement)
	}

	return globalParams, nil
}


// generateRequestsFromOp: 从操作和其他OpenAPI Schema路径及方法对象生成请求
//
//	@param opts *generateReqOptions:
//	@return error error:
func generateRequestsFromOp(opts *generateReqOptions) error {
	// 创建一个新的HTTP请求对象，使用指定的HTTP方法和URL
	req, err := http.NewRequest(opts.method, opts.pathURL+opts.requestPath, nil)
	if err != nil {
		return errors.Wrap(err, "could not make request")
	}

	// 获取请求参数，如果没有指定则初始化为空
	reqParams := opts.reqParams
	if reqParams == nil {
		reqParams = openapi3.NewParameters()
	}
	// 将操作中的参数添加到请求参数中
	reqParams = append(reqParams, opts.op.Parameters...)
	// 检查操作是否有特定的安全要求
	if opts.op.Security != nil {
		// 获取与安全要求相关的全局参数
		params, err := GetGlobalParamsForSecurityRequirement(opts.schema, opts.op.Security)
		if err != nil {
			return err
		}
		reqParams = append(reqParams, params...)
	} else {
		// 如果没有特定的安全要求，使用全局参数
		reqParams = append(reqParams, opts.globalParams...)
	}

	// 初始化查询参数
	query := url.Values{}
	// 遍历请求参数
	for _, parameter := range reqParams {
		value := parameter.Value

		// 如果参数没有定义Schema，跳过
		if value.Schema == nil || value.Schema.Value == nil {
			continue
		}

		// 定义参数值变量
		var paramValue interface{}

		// 从全局变量中获取参数值的覆盖值
		if val, ok := opts.opts.Variables[value.Name]; ok {
			paramValue = val
		} else if value.Schema.Value.Default != nil {
			// 使用默认值
			paramValue = value.Schema.Value.Default
		} else if value.Schema.Value.Example != nil {
			// 使用示例值
			paramValue = value.Schema.Value.Example
		} else if len(value.Schema.Value.Enum) > 0 {
			// 使用枚举的第一个值
			paramValue = value.Schema.Value.Enum[0]
		} else {
			// 如果没有找到值且未跳过格式验证
			if !opts.opts.SkipFormatValidation {
				// 如果有缺失参数值的回调函数，调用它
				if opts.missingParamValueCallback != nil {
					opts.missingParamValueCallback(value, opts)
				}
				// 如果参数是必需的，跳过整个请求
				if value.Required {
					return nil
				} else {
					// 如果参数是可选的，移除路径中的参数占位符
					opts.requestPath = strings.Replace(opts.requestPath, fmt.Sprintf("{%s}", value.Name), "", -1)
					if !opts.opts.RequiredOnly {
						gologger.Verbose().Msgf("openapi: skipping optional param (%s) in (%v) in request [%s] %s due to missing value (%v)\n", value.Name, value.In, opts.method, opts.requestPath, value.Name)
					}
					continue
				}
			}
			// 尝试从Schema生成示例值
			exampleX, err := generateExampleFromSchema(value.Schema.Value)
			if err != nil {
				// 如果生成示例失败，处理与上面相同
				if value.Required {
					gologger.Verbose().Msgf("openapi: skipping request [%s] %s due to missing value (%v)\n", opts.method, opts.requestPath, value.Name)
					return nil
				} else {
					opts.requestPath = strings.Replace(opts.requestPath, fmt.Sprintf("{%s}", value.Name), "", -1)
					if !opts.opts.RequiredOnly {
						gologger.Verbose().Msgf("openapi: skipping optinal param (%s) in (%v) in request [%s] %s due to missing value (%v)\n", value.Name, value.In, opts.method, opts.requestPath, value.Name)
					}
					continue
				}
			}
			paramValue = exampleX
		}
		 // 如果只需要必需参数且当前参数不是必需的，跳过
		if opts.requiredOnly && !value.Required {
			opts.requestPath = strings.Replace(opts.requestPath, fmt.Sprintf("{%s}", value.Name), "", -1)
			continue 
		}
		// 根据参数的位置设置请求	
		switch value.In {
		case "query":
			// 设置查询参数
			query.Set(value.Name, types.ToString(paramValue))
		case "header":
			// 设置请求头
			req.Header.Set(value.Name, types.ToString(paramValue))
		case "path":
			// 替换路径中的参数占位符
			opts.requestPath = fasttemplate.ExecuteStringStd(opts.requestPath, "{", "}", map[string]interface{}{
				value.Name: types.ToString(paramValue),
			})
		case "cookie":
			// 添加Cookie
			req.AddCookie(&http.Cookie{Name: value.Name, Value: types.ToString(paramValue)})
		}
	}
	 // 设置请求的查询字符串和路径
	req.URL.RawQuery = query.Encode()
	req.URL.Path = opts.requestPath

	// 处理请求体
	if opts.op.RequestBody != nil {
		// 遍历请求体的内容类型
		for content, value := range opts.op.RequestBody.Value.Content {
			// 克隆请求以便修改
			cloned := req.Clone(req.Context())
			// 从Schema生成示例
			example, err := generateExampleFromSchema(value.Schema.Value)
			if err != nil {
				continue
			}

			// 根据内容类型设置请求体
			switch content {
			case "application/json":
				if marshalled, err := json.Marshal(example); err == nil {
					// body = string(marshalled)
					cloned.Body = io.NopCloser(bytes.NewReader(marshalled))
					cloned.ContentLength = int64(len(marshalled))
					cloned.Header.Set("Content-Type", "application/json")
				}
			case "application/xml":
				exampleVal := mxj.Map(example.(map[string]interface{}))

				if marshalled, err := exampleVal.Xml(); err == nil {
					cloned.Body = io.NopCloser(bytes.NewReader(marshalled))
					cloned.ContentLength = int64(len(marshalled))
					cloned.Header.Set("Content-Type", "application/xml")
				} else {
					gologger.Warning().Msgf("openapi: could not encode xml")
				}
			case "application/x-www-form-urlencoded":
				if values, ok := example.(map[string]interface{}); ok {
					cloned.Form = url.Values{}
					for k, v := range values {
						cloned.Form.Set(k, types.ToString(v))
					}
					encoded := cloned.Form.Encode()
					cloned.ContentLength = int64(len(encoded))
					// body = encoded
					cloned.Body = io.NopCloser(strings.NewReader(encoded))
					cloned.Header.Set("Content-Type", "application/x-www-form-urlencoded")
				}
			case "multipart/form-data":
				if values, ok := example.(map[string]interface{}); ok {
					buffer := &bytes.Buffer{}
					multipartWriter := multipart.NewWriter(buffer)
					for k, v := range values {
						// 如果格式是二进制，则作为文件处理，否则作为字段
						if property, ok := value.Schema.Value.Properties[k]; ok && property.Value.Format == "binary" {
							if writer, err := multipartWriter.CreateFormFile(k, k); err == nil {
								_, _ = writer.Write([]byte(types.ToString(v)))
							}
						} else {
							_ = multipartWriter.WriteField(k, types.ToString(v))
						}
					}
					multipartWriter.Close()
					cloned.Body = io.NopCloser(buffer)
					cloned.ContentLength = int64(len(buffer.Bytes()))
					cloned.Header.Set("Content-Type", multipartWriter.FormDataContentType())
				}
			case "text/plain":
				str := types.ToString(example)
				// body = str
				cloned.Body = io.NopCloser(strings.NewReader(str))
				cloned.ContentLength = int64(len(str))
				cloned.Header.Set("Content-Type", "text/plain")
			case "application/octet-stream":
				str := types.ToString(example)
				if str == "" {
					// use two strings
					str = "string1\nstring2"
				}
				if value.Schema != nil && generic.EqualsAny(value.Schema.Value.Format, "bindary", "byte") {
					cloned.Body = io.NopCloser(bytes.NewReader([]byte(str)))
					cloned.ContentLength = int64(len(str))
					cloned.Header.Set("Content-Type", "application/octet-stream")
				} else {
					// use string placeholder
					cloned.Body = io.NopCloser(strings.NewReader(str))
					cloned.ContentLength = int64(len(str))
					cloned.Header.Set("Content-Type", "text/plain")
				}
			default:
				gologger.Verbose().Msgf("openapi: no correct content type found for body: %s\n", content)
				continue
			}
			// 转储请求以便调试
			dumped, err := httputil.DumpRequestOut(cloned, true)
			if err != nil {
				return errors.Wrap(err, "could not dump request")
			}

			// 解析原始请求
			rr, err := httpTypes.ParseRawRequestWithURL(string(dumped), cloned.URL.String())
			if err != nil {
				return errors.Wrap(err, "could not parse raw request")
			}
			opts.callback(rr)
			continue
		}
	}
	// 如果请求体不为空，返回nil
	if opts.op.RequestBody != nil {
		return nil
	}
	// 转储请求以便调试
	dumped, err := httputil.DumpRequestOut(req, true)
	if err != nil {
		return errors.Wrap(err, "could not dump request")
	}

	// 解析原始请求
	rr, err := httpTypes.ParseRawRequestWithURL(string(dumped), req.URL.String())
	if err != nil {
		return errors.Wrap(err, "could not parse raw request")
	}
	opts.callback(rr)
	return nil
}

// GenerateRequestsFromSchema: 从OpenAPI 3.0文档对象生成HTTP请求
//
//	@param schema *openapi3.T: 表示OpenAPI 3.0文档对象。
//	@param opts formats.InputFormatOptions: 包含输入格式的选项，如是否仅生成必需参数的请求。
//	@param callback formats.ParseReqRespCallback: 回调函数，用于处理生成的请求和响应。
//	@return error error: 如果生成过程中出现错误，返回一个error对象，否则返回nil。
func GenerateRequestsFromSchema(schema *openapi3.T, opts formats.InputFormatOptions, callback formats.ParseReqRespCallback) error {
	// 检查OpenAPI文档中是否定义了服务器
	if len(schema.Servers) == 0 {
		return errors.New("no servers found in openapi schema")
	}

	// 获取全局参数
	globalParams := openapi3.NewParameters()

	// 如果定义了安全要求，获取相应的全局参数
	if len(schema.Security) > 0 {
		params, err := GetGlobalParamsForSecurityRequirement(schema, &schema.Security)
		if err != nil {
			return err
		}
		globalParams = append(globalParams, params...)
	}

	// 验证全局参数
	for _, param := range globalParams {
		if val, ok := opts.Variables[param.Value.Name]; ok {
			param.Value.Example = val
		} else {
			// 如果缺少参数，检查是否跳过格式验证
			if opts.SkipFormatValidation {
				gologger.Verbose().Msgf("openapi: skipping all requests due to missing global auth parameter: %s\n", param.Value.Name)
				return nil
			} else {
				// fatal error
				gologger.Fatal().Msgf("openapi: missing global auth parameter: %s\n", param.Value.Name)
			}
		}
	}

	// 用于记录缺失和可选参数的映射
	missingVarMap := make(map[string]struct{})
	optionalVarMap := make(map[string]struct{})
	// 回调函数，用于处理缺失的参数值
	missingParamValueCallback := func(param *openapi3.Parameter, opts *generateReqOptions) {
		if !param.Required {
			optionalVarMap[param.Name] = struct{}{}
			return
		}
		missingVarMap[param.Name] = struct{}{}
	}

	// 遍历OpenAPI文档中定义的Servers
	for _, serverURL := range schema.Servers {
		pathURL := serverURL.URL
		// 将服务器URL拆分为baseURL和serverPath
		u, err := url.Parse(pathURL)
		if err != nil {
			return errors.Wrap(err, "could not parse server url")
		}
		baseURL := fmt.Sprintf("%s://%s", u.Scheme, u.Host)
		serverPath := u.Path

		// 遍历OpenAPI文档中定义的路径
		for path, v := range schema.Paths.Map() {
			// 获取路径项中的操作
			ops := v.Operations()
			requestPath := path
			if serverPath != "" {
				requestPath = serverPath + path
			}
			// 遍历每个操作，生成请求
			for method, ov := range ops {
				if err := generateRequestsFromOp(&generateReqOptions{
					requiredOnly:              opts.RequiredOnly,
					method:                    method,
					pathURL:                   baseURL,
					requestPath:               requestPath,
					op:                        ov,
					schema:                    schema,
					globalParams:              globalParams,
					reqParams:                 v.Parameters,
					opts:                      opts,
					callback:                  callback,
					missingParamValueCallback: missingParamValueCallback,
				}); err != nil {
					gologger.Warning().Msgf("Could not generate requests from op: %s\n", err)
				}
			}
		}
	}

	if len(missingVarMap) > 0 && !opts.SkipFormatValidation {
		gologger.Error().Msgf("openapi: Found %d missing parameters, use -skip-format-validation flag to skip requests or update missing parameters generated in %s file,you can also specify these vars using -var flag in (key=value) format\n", len(missingVarMap), formats.DefaultVarDumpFileName)
		gologger.Verbose().Msgf("openapi: missing params: %+v", mapsutil.GetSortedKeys(missingVarMap))
		if config.CurrentAppMode == config.AppModeCLI {
			// generate var dump file
			vars := &formats.OpenAPIParamsCfgFile{}
			for k := range missingVarMap {
				vars.Var = append(vars.Var, k+"=")
			}
			vars.OptionalVars = mapsutil.GetSortedKeys(optionalVarMap)
			if err := formats.WriteOpenAPIVarDumpFile(vars); err != nil {
				gologger.Error().Msgf("openapi: could not write params file: %s\n", err)
			}
			// exit with status code 1
			os.Exit(1)
		}
	}

	return nil
}
