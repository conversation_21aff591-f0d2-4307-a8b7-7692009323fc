package stats

import (
	"sync"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestCryptoTLSConnections 测试标准crypto/tls连接计数功能
// 验证IncrementCryptoTLSConnections和LoadCryptoTLSConnections函数
func TestCryptoTLSConnections(t *testing.T) {
	t.Run("基本计数功能", func(t *testing.T) {
		// 重置计数器（通过获取当前值作为基准）
		initial := LoadCryptoTLSConnections()

		// 增加计数
		IncrementCryptoTLSConnections()
		count1 := LoadCryptoTLSConnections()
		require.Equal(t, initial+1, count1, "第一次增加后计数应该正确")

		// 再次增加计数
		IncrementCryptoTLSConnections()
		count2 := LoadCryptoTLSConnections()
		require.Equal(t, initial+2, count2, "第二次增加后计数应该正确")

		t.Logf("CryptoTLS连接计数测试完成，当前计数: %d", count2)
	})

	t.Run("多次增加计数", func(t *testing.T) {
		initial := LoadCryptoTLSConnections()
		const increments = 10

		// 多次增加计数
		for i := 0; i < increments; i++ {
			IncrementCryptoTLSConnections()
		}

		final := LoadCryptoTLSConnections()
		require.Equal(t, initial+increments, final, "多次增加后计数应该正确")
	})
}

// TestZcryptoTLSConnections 测试zcrypto/tls连接计数功能
// 验证IncrementZcryptoTLSConnections和LoadZcryptoTLSConnections函数
func TestZcryptoTLSConnections(t *testing.T) {
	t.Run("基本计数功能", func(t *testing.T) {
		initial := LoadZcryptoTLSConnections()

		// 增加计数
		IncrementZcryptoTLSConnections()
		count1 := LoadZcryptoTLSConnections()
		require.Equal(t, initial+1, count1, "第一次增加后计数应该正确")

		// 再次增加计数
		IncrementZcryptoTLSConnections()
		count2 := LoadZcryptoTLSConnections()
		require.Equal(t, initial+2, count2, "第二次增加后计数应该正确")

		t.Logf("ZcryptoTLS连接计数测试完成，当前计数: %d", count2)
	})

	t.Run("多次增加计数", func(t *testing.T) {
		initial := LoadZcryptoTLSConnections()
		const increments = 15

		// 多次增加计数
		for i := 0; i < increments; i++ {
			IncrementZcryptoTLSConnections()
		}

		final := LoadZcryptoTLSConnections()
		require.Equal(t, initial+increments, final, "多次增加后计数应该正确")
	})
}

// TestOpensslTLSConnections 测试OpenSSL连接计数功能
// 验证IncrementOpensslTLSConnections和LoadOpensslTLSConnections函数
func TestOpensslTLSConnections(t *testing.T) {
	t.Run("基本计数功能", func(t *testing.T) {
		initial := LoadOpensslTLSConnections()

		// 增加计数
		IncrementOpensslTLSConnections()
		count1 := LoadOpensslTLSConnections()
		require.Equal(t, initial+1, count1, "第一次增加后计数应该正确")

		// 再次增加计数
		IncrementOpensslTLSConnections()
		count2 := LoadOpensslTLSConnections()
		require.Equal(t, initial+2, count2, "第二次增加后计数应该正确")

		t.Logf("OpenSSLTLS连接计数测试完成，当前计数: %d", count2)
	})

	t.Run("多次增加计数", func(t *testing.T) {
		initial := LoadOpensslTLSConnections()
		const increments = 20

		// 多次增加计数
		for i := 0; i < increments; i++ {
			IncrementOpensslTLSConnections()
		}

		final := LoadOpensslTLSConnections()
		require.Equal(t, initial+increments, final, "多次增加后计数应该正确")
	})
}

// TestConcurrentAccess 测试并发访问安全性
// 验证多个goroutine同时访问计数器的安全性
func TestConcurrentAccess(t *testing.T) {
	t.Run("并发增加CryptoTLS计数", func(t *testing.T) {
		initial := LoadCryptoTLSConnections()
		const numGoroutines = 100
		const incrementsPerGoroutine = 10
		var wg sync.WaitGroup

		// 启动多个goroutine并发增加计数
		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				for j := 0; j < incrementsPerGoroutine; j++ {
					IncrementCryptoTLSConnections()
				}
			}()
		}

		wg.Wait()

		final := LoadCryptoTLSConnections()
		expected := initial + numGoroutines*incrementsPerGoroutine
		require.Equal(t, expected, final, "并发增加后计数应该正确")

		t.Logf("并发CryptoTLS测试完成: %d个goroutine，每个增加%d次，总增加%d次",
			numGoroutines, incrementsPerGoroutine, numGoroutines*incrementsPerGoroutine)
	})

	t.Run("并发增加ZcryptoTLS计数", func(t *testing.T) {
		initial := LoadZcryptoTLSConnections()
		const numGoroutines = 50
		const incrementsPerGoroutine = 20
		var wg sync.WaitGroup

		// 启动多个goroutine并发增加计数
		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				for j := 0; j < incrementsPerGoroutine; j++ {
					IncrementZcryptoTLSConnections()
				}
			}()
		}

		wg.Wait()

		final := LoadZcryptoTLSConnections()
		expected := initial + numGoroutines*incrementsPerGoroutine
		require.Equal(t, expected, final, "并发增加后计数应该正确")

		t.Logf("并发ZcryptoTLS测试完成: %d个goroutine，每个增加%d次，总增加%d次",
			numGoroutines, incrementsPerGoroutine, numGoroutines*incrementsPerGoroutine)
	})

	t.Run("并发增加OpenSSL计数", func(t *testing.T) {
		initial := LoadOpensslTLSConnections()
		const numGoroutines = 75
		const incrementsPerGoroutine = 15
		var wg sync.WaitGroup

		// 启动多个goroutine并发增加计数
		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				for j := 0; j < incrementsPerGoroutine; j++ {
					IncrementOpensslTLSConnections()
				}
			}()
		}

		wg.Wait()

		final := LoadOpensslTLSConnections()
		expected := initial + numGoroutines*incrementsPerGoroutine
		require.Equal(t, expected, final, "并发增加后计数应该正确")

		t.Logf("并发OpenSSL测试完成: %d个goroutine，每个增加%d次，总增加%d次",
			numGoroutines, incrementsPerGoroutine, numGoroutines*incrementsPerGoroutine)
	})
}

// TestMixedConcurrentAccess 测试混合并发访问
// 验证同时读写不同计数器的安全性
func TestMixedConcurrentAccess(t *testing.T) {
	t.Run("混合并发读写", func(t *testing.T) {
		const numGoroutines = 30
		const operations = 50
		var wg sync.WaitGroup

		// 记录初始值
		initialCrypto := LoadCryptoTLSConnections()
		initialZcrypto := LoadZcryptoTLSConnections()
		initialOpenssl := LoadOpensslTLSConnections()

		// 启动多个goroutine进行混合操作
		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()
				for j := 0; j < operations; j++ {
					// 根据goroutine ID选择不同的操作
					switch id % 3 {
					case 0:
						IncrementCryptoTLSConnections()
						LoadCryptoTLSConnections()
					case 1:
						IncrementZcryptoTLSConnections()
						LoadZcryptoTLSConnections()
					case 2:
						IncrementOpensslTLSConnections()
						LoadOpensslTLSConnections()
					}
				}
			}(i)
		}

		wg.Wait()

		// 验证最终计数
		finalCrypto := LoadCryptoTLSConnections()
		finalZcrypto := LoadZcryptoTLSConnections()
		finalOpenssl := LoadOpensslTLSConnections()

		// 计算每种类型的预期增量
		cryptoGoroutines := (numGoroutines + 2) / 3  // 向上取整
		zcryptoGoroutines := (numGoroutines + 1) / 3
		opensslGoroutines := numGoroutines / 3

		require.Equal(t, initialCrypto+uint64(cryptoGoroutines*operations), finalCrypto,
			"CryptoTLS计数应该正确")
		require.Equal(t, initialZcrypto+uint64(zcryptoGoroutines*operations), finalZcrypto,
			"ZcryptoTLS计数应该正确")
		require.Equal(t, initialOpenssl+uint64(opensslGoroutines*operations), finalOpenssl,
			"OpenSSLTLS计数应该正确")

		t.Logf("混合并发测试完成:")
		t.Logf("  CryptoTLS: %d -> %d (增加 %d)",
			initialCrypto, finalCrypto, finalCrypto-initialCrypto)
		t.Logf("  ZcryptoTLS: %d -> %d (增加 %d)",
			initialZcrypto, finalZcrypto, finalZcrypto-initialZcrypto)
		t.Logf("  OpenSSLTLS: %d -> %d (增加 %d)",
			initialOpenssl, finalOpenssl, finalOpenssl-initialOpenssl)
	})
}

// TestAtomicOperations 测试原子操作的正确性
// 验证atomic包的使用是否正确
func TestAtomicOperations(t *testing.T) {
	t.Run("原子操作一致性", func(t *testing.T) {
		// 获取初始值
		initial := LoadCryptoTLSConnections()

		// 连续增加并检查
		for i := 1; i <= 5; i++ {
			IncrementCryptoTLSConnections()
			current := LoadCryptoTLSConnections()
			require.Equal(t, initial+uint64(i), current, "第%d次增加后计数应该正确", i)
		}
	})

	t.Run("不同计数器独立性", func(t *testing.T) {
		// 获取初始值
		initialCrypto := LoadCryptoTLSConnections()
		initialZcrypto := LoadZcryptoTLSConnections()
		initialOpenssl := LoadOpensslTLSConnections()

		// 只增加一个计数器
		IncrementCryptoTLSConnections()

		// 验证只有对应计数器增加
		require.Equal(t, initialCrypto+1, LoadCryptoTLSConnections(), "CryptoTLS计数应该增加")
		require.Equal(t, initialZcrypto, LoadZcryptoTLSConnections(), "ZcryptoTLS计数不应该变化")
		require.Equal(t, initialOpenssl, LoadOpensslTLSConnections(), "OpenSSLTLS计数不应该变化")
	})
}
