/*
 * @Author: chenjb
 * @Version: V1.0
 * @Date: 2024-11-11 15:45:19
 * @FilePath: /yaml_scan/pkg/input/formats/burp/burpxml.go
 * @Description: 提供burpxml解析
 */
package burp

import (
	"encoding/base64"
	"encoding/xml"
	"errors"
	"io"
	"strconv"
)

// Request 表示 XML 中的 <request> 元素，包含 HTTP 请求的相关信息
type Request struct {
	Base64 string `xml:"base64,attr"` // 请求是否为 Base64 编码
	Raw    string `xml:",chardata"`   // 包含原始的 HTTP 请求字符串
	Body   string // 存储解码后的请求主体
}

// Response 表示 XML 中的 <response> 元素，包含 HTTP 响应的相关信息.
type Response struct {
	Base64 string `xml:"base64,attr"` // 响应是否为 Base64 编码
	Raw    string `xml:",chardata"`   // 原始的 HTTP 响应字符串
	Body   string // 存储解码后的响应主体
}

// 表示 XML 中的 <host> 元素，包含远程主机的信息
type Host struct {
	Ip   string `xml:"ip,attr"`   // 存储远程 IP 地址
	Name string `xml:",chardata"` // 存储远程主机名
}

// Item 表示 XML 中的 <item> 元素，包含 HTTP的详细信息.
type Item struct {
	Time           string   `xml:"time"`           // 表示请求发送的时间
	Url            string   `xml:"url"`            // 表示请求的完整 URL
	Host           Host     `xml:"host"`           // 表示远程主机的信息
	Port           string   `xml:"port"`           // 表示远程主机的端口号
	Protocol       string   `xml:"protocol"`       // 表示使用的协议，例如 HTTP、HTTPS、FTP 等
	Path           string   `xml:"path"`           // 表示请求的 URL 路径部分
	Extension      string   `xml:"extension"`      // Burp Suite 特有的扩展字段，通常用于存储与请求相关的额外信息
	Request        Request  `xml:"request"`        // 表示 HTTP 请求的详细信息
	Status         string   `xml:"status"`         // 表示 HTTP 响应的状态码，例如 200、404、500 等
	ResponseLength string   `xml:"responselength"` // 表示 HTTP 响应的内容长度
	MimeType       string   `xml:"mimetype"`       // 表示 HTTP 响应的 MIME 类型，例如 text/html、application/json 等
	Response       Response `xml:"response"`       // 表示 HTTP 响应的详细信息
	Comment        string   `xml:"comment"`        //  Burp Suite 特有的注释字段，通常用于存储与请求相关的附加信息
}

// Items 表示 XML 中的 <items> 元素，包含多个 <item> 元素
type Items struct {
	Items []Item `xml:"item"`
}


// ParseXml: 读取 XML 数据
//  @param f io.Reader: XML 数据的来源，可以是文件、网络连接等
//  @param decode bool: 一个标志，指示是否解码 Base64 编码的请求和响应主体。
//  @return Items Items:  包含解析后的 HTTP 事务的结构体
//  @return error error: 如果解析失败，返回一个错误对象；否则返回 nil
func ParseXml(f io.Reader, decode bool) (Items, error) {
	var items Items

	 // 创建 XML 解码器
	dec := xml.NewDecoder(f)
	 // 解析 XML 数据到 Items 结构体
	err := dec.Decode(&items)
	if err != nil {
		return items, errors.New("could not parse xml ⇒ " + err.Error())
	}

	 // 如果不需要解码，直接返回解析结果
	if !decode {
		return items, nil
	}

	// 解码 Base64 编码的请求和响应主体
	for n, item := range items.Items {
		encoding := base64.StdEncoding
		
		 // 解析请求的 Base64
		b, err := strconv.ParseBool(item.Request.Base64)
		if err != nil {
			return items, errors.New("could not parse request base64 bool ⇒ " + err.Error())
		}
		 // 如果请求是 Base64 编码，进行解码
		if b {
			decoded, err := encoding.DecodeString(item.Request.Raw)
			if err != nil {
				return items, errors.New("could not decode base64 request ⇒ " + err.Error())
			}
			// 存储解码后的请求主体
			items.Items[n].Request.Body = string(decoded)
		}

		b, err = strconv.ParseBool(item.Response.Base64)
		if err != nil {
			return items, errors.New("could not parse response base64 bool ⇒ " + err.Error())
		}
		if b {
			decoded, err := encoding.DecodeString(item.Response.Raw)
			if err != nil {
				return items, errors.New("could not decode base64 response ⇒ " + err.Error())
			}
			items.Items[n].Response.Body = string(decoded)
		}
	}

	return items, nil
}
