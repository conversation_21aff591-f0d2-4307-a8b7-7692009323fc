// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 19:10:15
// FilePath: /yaml_scan/utils/reader/reader_keypress.go
// Description: 提供键盘按键读取功能，支持原始模式和缓冲模式的输入处理
package reader

import (
	"context"
	"errors"
	"os"
	"sync"
	"time"

	"yaml_scan/utils/reader/rawmode"
)

var ErrTimeout = errors.New("Timeout")

// KeyPressReader 是一个用于读取键盘按键的读取器
// 它支持两种模式：原始模式(Raw)和缓冲模式
// 原始模式下，每个按键都会立即被处理
// 缓冲模式下，只有在按下回车键后才会处理输入
type KeyPressReader struct {
	originalMode interface{}   // 存储终端的原始模式设置
	Timeout      time.Duration // 读取超时时间
	datachan     chan []byte   // 用于传递读取到的数据的通道
	Once         *sync.Once    // 确保初始化代码只执行一次
	Raw          bool          // 是否使用原始模式
	BufferSize   int           // 读取缓冲区大小
}

// Start 启动按键读取器
// @receiver reader 
// @return error error: 启动过程中可能发生的错误
func (reader *KeyPressReader) Start() error {
	// 确保初始化代码只执行一次
	reader.Once.Do(func() {
		// 启动后台读取线程
		go reader.read()
		// 获取并保存终端的原始模式
		reader.originalMode, _ = rawmode.GetMode(os.Stdin)
		if reader.Raw {
			reader.BufferSize = 1
		} else {
			reader.BufferSize = 512
		}
	})
	// 设置原始模式
	if reader.Raw {
		// 获取当前终端模式
		mode, _ := rawmode.GetMode(os.Stdin)
		return rawmode.SetRawMode(os.Stdin, mode)
	}

	// 缓冲模式下不需要特殊设置
	// 只有在按下回车键后才会检测到新行
	return nil
}

// Stop 停止按键读取器并恢复终端原始设置
// @receiver reader 
// @return error error: 停止过程中可能发生的错误
func (reader *KeyPressReader) Stop() error {
	// 禁用原始模式
	if reader.Raw {
		// 恢复终端的原始模式设置
		return rawmode.SetMode(os.Stdin, reader.originalMode)
	}

	// nop
	return nil
}

// read 
// @receiver reader 是一个内部方法，用于持续读取输入数据并发送到数据通道
func (reader *KeyPressReader) read() {
	// 如果数据通道尚未初始化，则创建它
	if reader.datachan == nil {
		reader.datachan = make(chan []byte)
	}

	// 无限循环，持续读取输入
	for {
		var (
			n   int                               // 读取的字节数
			err error                             // 读取过程中的错误
			r   = make([]byte, reader.BufferSize) // 创建适当大小的缓冲区
		)

		// 根据模式选择不同的读取方法
		if reader.Raw {
			n, err = rawmode.Read(os.Stdin, r)
		} else {
			// 缓冲模式下直接从标准输入读取
			n, err = os.Stdin.Read(r)
		}
		// 如果成功读取了数据 发送
		if n > 0 && err == nil {
			reader.datachan <- r
		}
	}
}

// Read 实现了io.Reader接口的Read方法
// @receiver reader 
// @param p []byte: 用于存储读取数据的字节切片
// @return n int: 读取的字节数
// @return err error: 读取过程中可能发生的错误，包括超时
func (reader KeyPressReader) Read(p []byte) (n int, err error) {
	var (
		ctx    context.Context
		cancel context.CancelFunc
	)
	// 如果设置了超时时间，创建一个带有超时的上下文
	if reader.Timeout > 0 {
		ctx, cancel = context.WithTimeout(context.Background(), time.Duration(reader.Timeout))
		defer cancel()
	}

	select {
	case <-ctx.Done():
		// 如果上下文完成（超时），返回超时错误
		err = ErrTimeout
		return
	case data := <-reader.datachan:
		// 如果从数据通道接收到数据，复制到目标缓冲区
		n = copy(p, data)
		return
	}
}

