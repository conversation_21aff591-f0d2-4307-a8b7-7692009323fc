// 
// Author: chenjb
// Version: V1.0
// Date: 2025-05-16 09:54:57
// FilePath: /yaml_scan/pkg/hybridMap/disk/kv.go
// Description: 磁盘存储的键值接口，提供了统一的数据持久化操作规范
package disk

import "time"

// DB 定义了磁盘存储的通用接口，所有实现该接口的数据库都需要提供这些基本功能
type DB interface {
	// Incr 将指定键的值增加给定的数值
	// 参数:
	//   - k: 要增加值的键名
	//   - by: 增加的数值
	// 返回:
	//   - 增加后的新值
	//   - 可能的错误
	Incr(k string, by int64) (int64, error)

	// Set 设置键值对，可选择设置过期时间
	// 参数:
	//   - k: 键名
	//   - v: 值（字节数组）
	//   - ttl: 过期时间，如果为0则表示永不过期
	// 返回:
	//   - 可能的错误
	Set(k string, v []byte, ttl time.Duration) error
	
	// MSet 批量设置多个键值对
	// 参数:
	//   - data: 键值对映射
	// 返回:
	//   - 可能的错误
	MSet(data map[string][]byte) error

	// Get 获取指定键的值
	// 参数:
	//   - k: 键名
	// 返回:
	//   - 键对应的值
	//   - 如果键不存在或已过期，返回错误
	Get(k string) ([]byte, error)

	// MGet 批量获取多个键的值
	// 参数:
	//   - keys: 键名列表
	// 返回:
	//   - 对应值的列表，如果某个键不存在，对应位置为空字节数组
	MGet(keys []string) [][]byte
	
	// TTL 获取键值对剩余的生存时间
	// 参数:
	//   - key: 键名
	// 返回:
	//   - 剩余秒数，-1表示永不过期，-2表示键不存在或已过期
	TTL(key string) int64

	// MDel 批量删除多个键
	// 参数:
	//   - keys: 要删除的键名列表
	// 返回:
	//   - 可能的错误
	MDel(keys []string) error
	
	// Del 删除单个键
	// 参数:
	//   - key: 要删除的键名
	// 返回:
	//   - 可能的错误
	Del(key string) error

	// Scan 使用提供的处理函数遍历数据库
	// 参数:
	//   - ScannerOpt: 扫描选项，包含前缀过滤、偏移量、处理函数等
	// 返回:
	//   - 可能的错误
	Scan(ScannerOpt ScannerOptions) error

	// Size 返回数据库占用的磁盘空间大小（字节数）
	// 返回:
	//   - 数据库大小
	Size() int64

	// GC 运行垃圾回收，清理过期数据并优化存储空间
	// 返回:
	//   - 可能的错误
	GC() error

	// Close 关闭数据库连接，释放资源
	Close()
}

// ScannerOptions - 表示扫描器的选项
// 提供数据库遍历时的各种控制参数，如起始位置、前缀过滤等
type ScannerOptions struct {
	// Offset 指定从哪个键开始扫描
	// 扫描将从此键开始，根据IncludeOffset决定是否包含此键
	Offset string

	// IncludeOffset 是否在结果中包含偏移键
	// true表示包含，false表示排除
	IncludeOffset bool

	// Prefix 指定扫描时的键前缀
	// 只有具有此前缀的键才会被处理
	Prefix string

	// FetchValues 是否获取键对应的值
	// true表示同时获取值，false表示只遍历键
	FetchValues bool

	// Handler 处理扫描到的键值对的回调函数
	// 每扫描到一个符合条件的键值对时都会调用此函数
	// 如果函数返回错误，扫描将中断
	Handler func(k []byte, v []byte) error
}

