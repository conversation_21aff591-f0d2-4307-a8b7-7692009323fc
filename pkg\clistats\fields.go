// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-18 17:06:00
// FilePath: /yaml_scan/pkg/clistats/fields.go
// Description: 字段管理实现，提供计数器、静态字段和动态字段的增删改查功能
package clistats

import "sync/atomic"

// AddCounter 向统计客户端添加uint64计数器字段
//
// 计数器用于跟踪增加的数量，如请求数、错误数等。方法通过原子操作确保线程安全。
// @receiver s 
// @param id string: 计数器的唯一标识符
// @param value uint64: 计数器的初始值
func (s *Statistics) AddCounter(id string, value uint64) {
	newUint64 := &atomic.Uint64{}
	 // 使用原子操作存储初始值
	newUint64.Store(value)
	// 添加到计数器映射中
	s.counters[id] = newUint64
}

// GetCounter  返回计数器的当前值
// @receiver s 
// @param id string: 计数器的唯一标识符
// @return uint64 uint64: 计数器的当前值
// @return bool bool: 果计数器存在则为true，否则为false
func (s *Statistics) GetCounter(id string) (uint64, bool) {
	counter, ok := s.counters[id]
	if !ok {
		return 0, false
	}
	return counter.Load(), true
}

// IncrementCounter 将计数器的值增加指定的数量
// @receiver s 
// @param id string: 计数器的唯一标识符
// @param count int: 要增加的数量
func (s *Statistics) IncrementCounter(id string, count int) {
	counter, ok := s.counters[id]
	if !ok {
		return
	}
	counter.Add(uint64(count))
}

// AddStatic 统计信息添加静态信息字段
//
// 静态字段的值在统计客户端的生命周期内保持不变，常用于记录不变的信息如版本号、启动时间等
// @receiver s 
// @param id string:  静态字段的唯一标识符
// @param value interface{}: 要存储的值，可以是任意类型
func (s *Statistics) AddStatic(id string, value interface{}) {
	s.static[id] = value
}

// GetStatic 返回静态字段的原始值
//
// @receiver s 
// @param id string:  静态字段的唯一标识符
// @return interface{} interface{}: 静态字段的值
// @return bool bool: 如果字段存在则为true，否则为false
func (s *Statistics) GetStatic(id string) (interface{}, bool) {
	static, ok := s.static[id]
	if !ok {
		return nil, false
	}
	return static, true
}

// AddDynamic 添加一个动态字段，其值通过运行回调函数获取
//
// 动态字段用于计算和显示变化的值，如每秒请求数、已用时间等
// @receiver s 
// @param id string: 动态字段的唯一标识符
// @param Callback DynamicCallback: 用于计算动态字段值的回调函数
func (s *Statistics) AddDynamic(id string, Callback DynamicCallback) {
	s.dynamic[id] = Callback
}

// GetDynamic 返回用于数据检索的动态字段回调函数
// @receiver s 
// @param id string: 动态字段的唯一标识符
// @return DynamicCallback DynamicCallback:动态字段的回调函数 
// @return bool bool:  如果字段存在则为true，否则为false
func (s *Statistics) GetDynamic(id string) (DynamicCallback, bool) {
	dynamic, ok := s.dynamic[id]
	if !ok {
		return nil, false
	}
	return dynamic, true
}

