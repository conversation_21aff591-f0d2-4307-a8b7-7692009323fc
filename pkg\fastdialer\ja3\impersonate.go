// Author: chenjb
// Version: V1.0
// Date: 2025-05-20 17:03:44
// FilePath: /yaml_scan/pkg/fastdialer/ja3/impersonate.go
// Description:提供TLS指纹模拟功能的常量定义和类型
package ja3

import (
	utls "github.com/refraction-networking/utls"
)

// Strategy 表示指纹模拟使用的策略类型
// 定义不同的TLS指纹模拟方式
type Strategy uint8

// Identity 包含结构化的客户端Hello规范
// 基于uTLS的ClientHelloSpec类型，用于精确控制TLS握手过程
type Identity utls.ClientHelloSpec


const (
	// None 默认策略，使用标准客户端Hello规范
	// 不进行任何指纹模拟，使用原生TLS行为
	None Strategy = iota
	// Random 随机策略，使用随机生成的客户端Hello规范
	// 每次连接使用不同的TLS指纹
	Random
	// Custom 自定义策略，从JA3字符串或原始数据解析客户端Hello规范
	// 允许使用特定预设的JA3指纹
	Custom
	// Chrome 模拟Chrome浏览器的客户端Hello规范
	// 使Chrome浏览器的特征指纹进行连接
	Chrome
)
