// Package rawhttp HTTP管道客户端配置选项模块
package rawhttp

import (
	"time"                                  // 时间处理包
	"yaml_scan/pkg/rawhttp/clientpipeline"  // 客户端管道包
)

// PipelineOptions 包含管道化HTTP客户端的配置选项
// 提供了管道连接管理、性能调优和行为控制的完整参数集
type PipelineOptions struct {
	Dialer              clientpipeline.DialFunc // 拨号函数，用于建立网络连接
	Host                string                  // 目标主机地址，包含主机名和端口
	Timeout             time.Duration           // 请求超时时间，控制单个请求的最大执行时间
	MaxConnections      int                     // 最大连接数，控制到目标主机的并发连接数量
	MaxPendingRequests  int                     // 最大待处理请求数，控制管道中排队等待的请求数量
	AutomaticHostHeader bool                    // 是否自动添加Host头部，true表示自动设置Host字段
}

// DefaultPipelineOptions 是管道化HTTP客户端的默认配置选项
// 提供了适用于大多数场景的合理默认值，平衡了性能和资源使用
var DefaultPipelineOptions = PipelineOptions{
	Timeout:             30 * time.Second, // 默认超时30秒
	MaxConnections:      5,                // 默认最大5个连接
	MaxPendingRequests:  100,              // 默认最大100个待处理请求
	AutomaticHostHeader: true,             // 默认自动添加Host头部
}