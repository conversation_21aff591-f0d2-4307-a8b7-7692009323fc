// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-17 20:32:34
// FilePath: /yaml_scan/pkg/operators/operators.go
// Description: 
package operators



// Result is a result structure created from operators running on data.
type Result struct {
	// Matched is true if any matchers matched
	Matched bool
	// Extracted is true if any result type values were extracted
	Extracted bool
	// Matches is a map of matcher names that we matched
	Matches map[string][]string
	// Extracts contains all the data extracted from inputs
	Extracts map[string][]string
	// OutputExtracts is the list of extracts to be displayed on screen.
	OutputExtracts []string
	outputUnique   map[string]struct{}

	// DynamicValues contains any dynamic values to be templated
	DynamicValues map[string][]string
	// PayloadValues contains payload values provided by user. (Optional)
	PayloadValues map[string]interface{}

	// Optional lineCounts for file protocol
	LineCount string
	// Operators is reference to operators that generated this result (Read-Only)
	Operators *Operators
}