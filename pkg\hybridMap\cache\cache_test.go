// 
// Author: chenjb
// Version: V1.0
// Date: 2025-05-19 10:39:45
// FilePath: /yaml_scan/pkg/hybridMap/cache/cache_test.go
// Description: 
package cache


import (
	"bytes"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestNewCache 测试创建新缓存实例
func TestNewCache(t *testing.T) {
	// 创建带默认过期时间的缓存
	defaultExpiration := 1 * time.Minute
	cleanupInterval := 1 * time.Minute
	c := New(defaultExpiration, cleanupInterval)
	
	// 验证缓存已正确创建
	require.NotNil(t, c, "创建的缓存不应为空")
	require.NotNil(t, c.cacheMemory, "缓存内部实现不应为空")
	require.Equal(t, defaultExpiration, c.DefaultExpiration, "默认过期时间应与指定值相同")
	require.NotNil(t, c.janitor, "清理器不应为空")
}

// TestSetAndGet 测试缓存的基本设置和获取功能
func TestSetAndGet(t *testing.T) {
	// 创建缓存
	c := New(5*time.Minute, 0)
	
	// 测试基本的设置和获取
	key := "test-key"
	value := []byte("test-value")
	
	// 设置键值对
	c.Set(key, value)
	
	// 获取并验证值
	v, found := c.Get(key)
	require.True(t, found, "应该找到设置的键")
	require.Equal(t, value, v, "获取的值应该与设置的值相同")
	
	// 测试获取不存在的键
	_, found = c.Get("non-existent-key")
	require.False(t, found, "不应该找到不存在的键")
}

// TestSetWithExpiration 测试设置带过期时间的缓存项
func TestSetWithExpiration(t *testing.T) {
	// 创建缓存，不设置自动清理
	c := New(NoExpiration, 0)
	
	// 设置一个永不过期的项
	c.SetWithExpiration("permanent", []byte("permanent-value"), NoExpiration)
	
	// 设置一个会过期的项（100毫秒后过期）
	c.SetWithExpiration("temporary", []byte("temp-value"), 100*time.Millisecond)
	
	// 立即获取两个项，应该都能找到
	_, found := c.Get("permanent")
	require.True(t, found, "应该找到永久项")
	
	_, found = c.Get("temporary")
	require.True(t, found, "过期前应该找到临时项")
	
	// 等待过期时间
	time.Sleep(150 * time.Millisecond)
	
	// 再次获取，永久项应存在，临时项应过期
	_, found = c.Get("permanent")
	require.True(t, found, "永久项不应过期")
	
	_, found = c.Get("temporary")
	require.False(t, found, "过期后不应找到临时项")
}

// TestDelete 测试删除缓存项
func TestDelete(t *testing.T) {
	// 创建缓存
	c := New(NoExpiration, 0)
	
	// 添加测试项
	c.Set("key1", []byte("value1"))
	c.Set("key2", []byte("value2"))
	
	// 验证项目已添加
	_, found := c.Get("key1")
	require.True(t, found, "应该找到添加的键")
	
	// 删除键
	c.Delete("key1")
	
	// 验证项目已删除
	_, found = c.Get("key1")
	require.False(t, found, "删除后不应找到键")
	
	// 验证其他键不受影响
	_, found = c.Get("key2")
	require.True(t, found, "未删除的键应该仍然存在")
}

// TestOnEvicted 测试删除回调函数
func TestOnEvicted(t *testing.T) {
	// 创建缓存
	c := New(NoExpiration, 0)
	
	// 用于跟踪被删除项的变量
	var deletedKey string
	var deletedValue interface{}
	
	// 设置删除回调
	c.OnEvicted(func(k string, v interface{}) {
		deletedKey = k
		deletedValue = v
	})
	
	// 设置测试项
	c.Set("key1", []byte("value1"))
	
	// 删除项
	c.Delete("key1")
	
	// 验证回调函数被正确调用
	require.Equal(t, "key1", deletedKey, "删除回调应接收到正确的键")
	require.Equal(t, []byte("value1"), deletedValue, "删除回调应接收到正确的值")
}

// TestDeleteExpired 测试手动删除过期项
func TestDeleteExpired(t *testing.T) {
	// 创建缓存，不设置自动清理
	c := New(NoExpiration, 0)
	
	// 添加会过期的项
	c.SetWithExpiration("key1", []byte("value1"), 50*time.Millisecond)
	c.SetWithExpiration("key2", []byte("value2"), 150*time.Millisecond)
	c.SetWithExpiration("key3", []byte("value3"), NoExpiration)
	
	// 等待第一个键过期
	time.Sleep(100 * time.Millisecond)
	
	// 手动触发过期项清理
	c.DeleteExpired()
	
	// 验证结果
	_, found := c.Get("key1")
	require.False(t, found, "过期的键1应该被删除")
	
	_, found = c.Get("key2")
	require.True(t, found, "未过期的键2应该仍然存在")
	
	_, found = c.Get("key3")
	require.True(t, found, "永久键3不应被删除")
}

// TestJanitor 测试自动清理功能
func TestJanitor(t *testing.T) {
	// 创建带清理器的缓存，清理间隔为100毫秒
	c := New(NoExpiration, 100*time.Millisecond)
	
	// 添加会过期的项
	c.SetWithExpiration("key1", []byte("value1"), 50*time.Millisecond)
	c.SetWithExpiration("key2", []byte("value2"), 250*time.Millisecond)
	
	// 等待第一次清理（应删除key1）
	time.Sleep(200 * time.Millisecond)
	
	// 验证key1已被自动清理
	_, found := c.Get("key1")
	require.False(t, found, "key1应该被自动清理")
	
	// 验证key2仍然存在
	_, found = c.Get("key2")
	require.True(t, found, "key2不应被清理")
	
	// 等待第二次清理（应删除key2）
	time.Sleep(200 * time.Millisecond)
	
	// 验证key2已被自动清理
	_, found = c.Get("key2")
	require.False(t, found, "key2应该被自动清理")
}

// TestScan 测试扫描功能
func TestScan(t *testing.T) {
	// 创建缓存
	c := New(NoExpiration, 0)
	
	// 添加测试数据
	testData := map[string][]byte{
		"key1": []byte("value1"),
		"key2": []byte("value2"),
		"key3": []byte("value3"),
	}
	
	for k, v := range testData {
		c.Set(k, v)
	}
	
	// 用于收集扫描结果的映射
	scannedItems := make(map[string][]byte)
	
	// 执行扫描
	c.Scan(func(k []byte, v []byte) error {
		scannedItems[string(k)] = v
		return nil
	})
	
	// 验证扫描结果
	require.Equal(t, len(testData), len(scannedItems), "扫描应返回所有项")
	
	for k, v := range testData {
		scannedValue, found := scannedItems[k]
		require.True(t, found, "扫描结果应包含键："+k)
		require.True(t, bytes.Equal(v, scannedValue), "扫描值应与原始值匹配")
	}
}

// TestCloneItems 测试克隆功能
func TestCloneItems(t *testing.T) {
	// 创建缓存
	c := New(NoExpiration, 0)
	
	// 添加测试数据
	c.Set("key1", []byte("value1"))
	c.Set("key2", []byte("value2"))
	c.SetWithExpiration("key3", []byte("value3"), 50*time.Millisecond)
	
	// 等待key3过期
	time.Sleep(100 * time.Millisecond)
	
	// 克隆项目
	cloned := c.CloneItems()
	
	// 验证克隆结果
	require.Equal(t, 2, len(cloned), "过期项应该不包含在克隆结果中")
	
	_, found := cloned["key1"]
	require.True(t, found, "克隆结果应包含key1")
	
	_, found = cloned["key2"]
	require.True(t, found, "克隆结果应包含key2")
	
	_, found = cloned["key3"]
	require.False(t, found, "克隆结果不应包含过期的key3")
}

// TestItemCount 测试项目计数功能
func TestItemCount(t *testing.T) {
	// 创建缓存
	c := New(NoExpiration, 0)
	
	// 验证空缓存的计数
	require.Equal(t, 0, c.ItemCount(), "空缓存的项目数应为0")
	
	// 添加项目
	c.Set("key1", []byte("value1"))
	c.Set("key2", []byte("value2"))
	
	// 验证计数增加
	require.Equal(t, 2, c.ItemCount(), "添加2个项目后计数应为2")
	
	// 添加过期项目
	c.SetWithExpiration("key3", []byte("value3"), 50*time.Millisecond)
	
	// 验证计数再次增加
	require.Equal(t, 3, c.ItemCount(), "添加过期项目后计数应为3")
	
	// 等待项目过期
	time.Sleep(100 * time.Millisecond)
	
	// 验证计数不变（仅在显式调用DeleteExpired后减少）
	require.Equal(t, 3, c.ItemCount(), "项目过期但未清理时计数应仍为3")
	
	// 清理过期项目
	c.DeleteExpired()
	
	// 验证计数减少
	require.Equal(t, 2, c.ItemCount(), "清理过期项目后计数应为2")
} 

