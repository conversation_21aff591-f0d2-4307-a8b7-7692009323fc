//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-20 14:14:59
//FilePath: /yaml_scan/cmd/runner/options.go
//Description:

package runner

import (
	"yaml_scan/pkg/config"
	"yaml_scan/pkg/goflags"
)

const (
	// 用于保存协议流量的默认目录
	DefaultDumpTrafficOutputFolder = "output"
)

// ConfigureOptions 配置命令行标志选项
// 该函数主要用于设置与文件相关的选项，特别是如何处理文件名和字符串的关系。
// 如果文件的扩展名为 `.yaml` 或 `.json`，则这些文件将被视为字符串，而不是要读取的文件。
func ConfigureOptions() error {
	// isFromFileFunc 是一个回调函数，用于判断给定的文件名是否是模板文件。
	isFromFileFunc := func(s string) bool {
		// 调用 config.IsTemplate 函数，检查文件名 s 是否是模板文件。
		// 如果是模板文件，返回 false；如果不是模板文件，返回 true。
		return !config.IsTemplate(s)
	}
	goflags.FileNormalizedStringSliceOptions.IsFromFile = isFromFileFunc
	goflags.FileStringSliceOptions.IsFromFile = isFromFileFunc
	goflags.FileCommaSeparatedStringSliceOptions.IsFromFile = isFromFileFunc
	return nil
}
