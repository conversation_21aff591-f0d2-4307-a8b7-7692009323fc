//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-11 20:25:53
//FilePath: /yaml_scan/utils/maps/ordered_map_test.go
//Description:ordered_map单测

package mapsutil

import (
	"testing"
)

func TestNewOrderedMap(t *testing.T) {
	// 创建一个新的 OrderedMap
	om := NewOrderedMap[string, int]()

	// 检查 keys 切片是否为空
	if len(om.keys) != 0 {
		t.<PERSON><PERSON><PERSON>("Expected keys to be empty, got %v", om.keys)
	}

	// 检查映射是否为空
	if len(om.m) != 0 {
		t.<PERSON><PERSON><PERSON>("Expected map to be empty, got %v", om.m)
	}
}

func TestIsEmpty(t *testing.T) {
	om := NewOrderedMap[string, int]()

	// 检查新创建的 OrderedMap 是否为空
	if !om.IsEmpty() {
		t.<PERSON>rror("Expected OrderedMap to be empty")
	}

	// 添加一个元素
	om.m["key1"] = 1
	om.keys = append(om.keys, "key1")

	// 检查 OrderedMap 是否为空
	if om.IsEmpty() {
		t.<PERSON><PERSON><PERSON>("Expected OrderedMap to not be empty")
	}
}

func TestGetKeys(t *testing.T) {
	om := NewOrderedMap[string, int]()

	// 添加一些键值对
	om.m["key1"] = 1
	om.keys = append(om.keys, "key1")
	om.m["key2"] = 2
	om.keys = append(om.keys, "key2")

	// 获取所有的键
	keys := om.GetKeys()

	// 检查返回的键是否正确
	expectedKeys := []string{"key1", "key2"}
	for i, key := range expectedKeys {
		if keys[i] != key {
			t.Errorf("Expected key %s, got %s", key, keys[i])
		}
	}
}

func TestGet(t *testing.T) {
	om := NewOrderedMap[string, int]()

	// 添加一个键值对
	om.m["key1"] = 1
	om.keys = append(om.keys, "key1")

	// 测试获取存在的键
	value, ok := om.Get("key1")
	if !ok {
		t.Error("Expected key 'key1' to exist")
	}
	if value != 1 {
		t.Errorf("Expected value 1, got %d", value)
	}

	// 测试获取不存在的键
	value, ok = om.Get("key2")
	if ok {
		t.Error("Expected key 'key2' to not exist")
	}
	if value != 0 { // int 的零值
		t.Errorf("Expected zero value for non-existent key, got %d", value)
	}
}

func TestSet(t *testing.T) {
	// 创建一个新的 OrderedMap
	om := NewOrderedMap[string, int]()

	// 测试添加新键
	om.Set("one", 1)
	om.Set("two", 2)

	// 验证键的顺序
	expectedKeys := []string{"one", "two"}
	if keys := om.GetKeys(); !equal(keys, expectedKeys) {
		t.Errorf("Expected keys %v, got %v", expectedKeys, keys)
	}

	// 验证值
	if value, ok := om.Get("one"); !ok || value != 1 {
		t.Errorf("Expected value for 'one' to be 1, got %v", value)
	}
	if value, ok := om.Get("two"); !ok || value != 2 {
		t.Errorf("Expected value for 'two' to be 2, got %v", value)
	}

	// 测试更新现有键
	om.Set("one", 11)
	if value, ok := om.Get("one"); !ok || value != 11 {
		t.Errorf("Expected updated value for 'one' to be 11, got %v", value)
	}

	// 测试添加新键并保持顺序
	om.Set("three", 3)
	expectedKeysAfterAdd := []string{"one", "two", "three"}
	if keys := om.GetKeys(); !equal(keys, expectedKeysAfterAdd) {
		t.Errorf("Expected keys after adding 'three' %v, got %v", expectedKeysAfterAdd, keys)
	}

	// 测试在迭代中设置键
	om.inIter = true // 模拟在迭代中
	om.Set("four", 4)
	if !om.dirty {
		t.Error("Expected dirty to be true after setting a key in iteration")
	}
}

// equal 用于比较两个字符串切片是否相等
func equal(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}
	for i := range a {
		if a[i] != b[i] {
			return false
		}
	}
	return true
}

func TestOrderedMapLen(t *testing.T) {
	// 创建一个新的 OrderedMap 实例
	om := NewOrderedMap[string, int]()

	// 测试初始长度
	if length := om.Len(); length != 0 {
		t.Errorf("Expected length to be 0, got %d", length)
	}

	// 添加一个键值对
	om.Set("one", 1)
	if length := om.Len(); length != 1 {
		t.Errorf("Expected length to be 1, got %d", length)
	}

	// 添加多个键值对
	om.Set("two", 2)
	om.Set("three", 3)
	if length := om.Len(); length != 3 {
		t.Errorf("Expected length to be 3, got %d", length)
	}

	// 更新一个键的值，不应改变长度
	om.Set("one", 10)
	if length := om.Len(); length != 3 {
		t.Errorf("Expected length to remain 3 after updating, got %d", length)
	}

}
