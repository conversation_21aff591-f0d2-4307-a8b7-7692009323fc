// Author: chenjb
// Version: V1.0
// Date: 2025-07-18 11:28:17
// FilePath: /yaml_scan/pkg/wappalyzergo/fingerprint_headers.go
// Description: 实现基于HTTP响应头的技术指纹识别功能

package wappalyzergo

import "strings"

// checkHeaders 检查目标的HTTP响应头是否匹配指纹库中的模式
// HTTP响应头是技术栈识别的重要信息源，包含服务器软件、编程语言、框架等关键信息
//
// 参数:
//   - headers: 已标准化的HTTP响应头映射，键为头部名称（小写），值为头部值（小写）
//            例如: {"server": "nginx/1.18.0", "x-powered-by": "php/7.4.0"}
//
// 返回值:
//   - []matchPartResult: 匹配到的技术列表，每个结果包含：
//     * application: 技术名称（如"nginx", "Apache", "PHP"等）
//     * version: 从头部值中提取的版本号（如"1.18.0", "7.4.0"）
//     * confidence: 匹配置信度（0-100），头部匹配通常具有较高置信度
//
// 常见的技术指纹头部:
//   - Server: Web服务器信息（nginx, Apache, IIS等）
//   - X-Powered-By: 后端技术栈（PHP, ASP.NET等）
//   - X-Generator: 内容管理系统（WordPress, Drupal等）
//   - X-Framework: 应用框架信息
//   - X-Version: 应用版本信息
//   - Set-Cookie: 会话管理技术
//
// 工作原理:
//   1. 遍历所有HTTP响应头
//   2. 对每个头部使用预编译的正则表达式进行匹配
//   3. 从匹配结果中提取技术名称和版本信息
//   4. 计算匹配置信度并返回结果列表
//
// 使用示例:
//   headers := map[string]string{
//       "server": "nginx/1.18.0 (ubuntu)",
//       "x-powered-by": "php/7.4.0",
//       "x-generator": "wordpress 5.8"
//   }
//   results := wappalyzer.checkHeaders(headers)
//   // 可能返回: [{"nginx", "1.18.0", 95}, {"PHP", "7.4.0", 90}, {"WordPress", "5.8", 85}]
func (s *Wappalyze) checkHeaders(headers map[string]string) []matchPartResult {
	// 使用指纹库对HTTP响应头进行模式匹配
	// headersPart 表示这是HTTP头部类型的指纹匹配
	// matchMapString 方法会：
	// 1. 遍历headers映射中的所有键值对
	// 2. 对每个头部名称和值应用相应的正则表达式模式
	// 3. 提取匹配的技术名称、版本号和置信度
	// 4. 返回所有匹配结果的列表
	technologies := s.fingerprints.matchMapString(headers, headersPart)
	return technologies
}

// getHeadersMap 将HTTP响应头数组转换为字符串映射
// 该函数将标准的HTTP头部格式（一个键对应多个值）转换为简化的格式（一个键对应一个合并的字符串值）
//
// 参数:
	//   - headersArray: 原始HTTP响应头映射，键为头部名称，值为头部值数组
	//                 例如: {"Content-Type": ["text/html; charset=utf-8"], "Set-Cookie": ["id=123", "session=abc"]}
//
// 返回值:
//   - map[string]string: 转换后的头部映射，键为头部名称，值为合并后的字符串
//                      例如: {"Content-Type": "text/html; charset=utf-8", "Set-Cookie": "id=123, session=abc"}
//
// 处理逻辑:
//   1. 为每个头部名称创建一个字符串构建器
//   2. 将同一头部的多个值用逗号分隔符连接
//   3. 返回合并后的头部映射
//
// 使用场景:
//   - 简化HTTP头部处理，便于正则表达式匹配
//   - 统一处理单值和多值头部
//   - 提高头部匹配的效率
func getHeadersMap(headersArray map[string][]string) map[string]string {
	// 创建结果映射，预分配容量以提高性能
	headers := make(map[string]string, len(headersArray))

	// 创建字符串构建器，用于高效拼接多个头部值
	builder := &strings.Builder{}
	
	// 遍历所有头部
	for key, value := range headersArray {
		// 遍历当前头部的所有值
		for i, v := range value {
			// 添加当前值到构建器
			builder.WriteString(v)
			// 如果不是最后一个值，添加逗号分隔符
			if i != len(value)-1 {
				builder.WriteString(", ")
			}
		}
		// 获取合并后的头部值
		headerValue := builder.String()

		// 将头部名称和合并值添加到结果映射
		headers[key] = headerValue
		// 重置构建器，准备处理下一个头部
		builder.Reset()
	}
	return headers
}

// normalizeHeaders 标准化HTTP响应头以便进行技术识别
// 该方法将原始HTTP头部转换为标准化格式，包括转换为小写和合并多值头部
//
// 参数:
//   - headers: 原始HTTP响应头映射，键为头部名称，值为头部值数组
//
// 返回值:
//   - map[string]string: 标准化后的头部映射，键和值均为小写
//
// 标准化过程:
//   1. 调用getHeadersMap将多值头部合并为单个字符串
//   2. 将所有头部名称和值转换为小写，确保大小写不敏感的匹配
//   3. 返回标准化后的头部映射
//
// 使用场景:
//   - 确保指纹匹配不受大小写影响
//   - 简化头部处理逻辑
//   - 提高匹配准确性和一致性
//
// 示例:
//   原始头部:
//     {"Content-Type": ["text/HTML"], "Server": ["Nginx/1.18.0"]}
//   标准化后:
//     {"content-type": "text/html", "server": "nginx/1.18.0"}
func (s *Wappalyze) normalizeHeaders(headers map[string][]string) map[string]string {
	// 创建标准化结果映射，预分配容量
	normalized := make(map[string]string, len(headers))
	
	// 先将多值头部合并为单值
	data := getHeadersMap(headers)

	// 遍历合并后的头部，转换为小写
	for header, value := range data {
		normalized[strings.ToLower(header)] = strings.ToLower(value)
	}
	
	return normalized
}

// findSetCookie 从HTTP响应头中提取所有Set-Cookie值
// 该方法专门用于提取和处理Set-Cookie头部，这是识别会话管理技术的重要信息源
//
// 参数:
//   - headers: 标准化后的HTTP响应头映射
//
// 返回值:
//   - []string: 提取的Cookie字符串列表，每个元素包含一个完整的Cookie定义
//
// 处理逻辑:
//   1. 检查headers映射中是否存在"set-cookie"键
//   2. 如果存在，按逗号分隔提取所有Cookie值
//   3. 返回提取的Cookie列表
//
// 使用场景:
//   - 提取会话管理Cookie进行技术识别
//   - 分析认证和状态管理机制
//   - 识别基于Cookie的框架和CMS
//
// 注意事项:
//   - 由于HTTP头部已标准化为小写，这里使用"set-cookie"作为键名
//   - 多个Cookie值通常以逗号分隔，但某些Cookie值本身可能包含逗号，可能导致解析不准确
func (s *Wappalyze) findSetCookie(headers map[string]string) []string {
	// 检查是否存在Set-Cookie头部
	if cookie, ok := headers["set-cookie"]; ok {
		// 按逗号分隔提取所有Cookie值
		// 注意：这种简单的分割方式可能在某些复杂Cookie情况下不够准确
		return strings.Split(cookie, ",")
	}
	// 如果没有Set-Cookie头部，返回空列表
	return []string{}
}

// analyzeHeaderPatterns 分析HTTP头部模式以识别特定技术特征
// 该方法通过分析HTTP头部的命名模式和值格式来识别特定的技术栈
//
// 参数:
//   - headers: 标准化的HTTP响应头映射
//
// 返回值:
//   - map[string]float64: 技术名称到置信度的映射
//
// 分析维度:
//   1. 头部命名模式（前缀、后缀、特殊标识符）
//   2. 头部值格式和特征字符串
//   3. 特定头部组合模式
//
// 识别示例:
//   - "x-powered-by: php" -> PHP (95%)
//   - "x-aspnet-version: 4.0" -> ASP.NET (100%)
//   - "x-drupal-cache" 存在 -> Drupal (90%)
//   - 特定的安全头部组合 -> 特定的WAF或安全产品
//
// 技术识别策略:
//   - 基于前缀识别: 检查头部名称的特定前缀（如"x-powered-by"、"x-aspnet"）
//   - 基于内容识别: 分析头部值中的特征字符串（如"apache"、"nginx"）
//   - 组合模式识别: 检测多个相关头部的共同存在（如ASP.NET和IIS）
//   - 安全特征识别: 检测安全相关头部（如"x-xss-protection"、"content-security-policy"）
func (s *Wappalyze) analyzeHeaderPatterns(headers map[string]string) map[string]float64 {
	// 创建结果映射，存储技术名称和置信度
	results := make(map[string]float64)
	
	// 检查常见的头部命名模式
	for header := range headers {
		// 检查前缀模式
		if strings.HasPrefix(header, "x-powered-by") {
			results["Generic Web Technology"] = 70
		}
		if strings.HasPrefix(header, "x-aspnet") {
			results["ASP.NET"] = 95
		}
		if strings.HasPrefix(header, "x-drupal") {
			results["Drupal"] = 90
		}
		
		// 检查特定的安全头部
		if header == "x-xss-protection" || header == "content-security-policy" {
			results["Security Headers"] = 80
		}
	}
	
	// 检查特定的头部值模式
	if server, ok := headers["server"]; ok {
		if strings.Contains(server, "apache") {
			results["Apache"] = 90
		}
		if strings.Contains(server, "nginx") {
			results["Nginx"] = 90
		}
		if strings.Contains(server, "microsoft-iis") {
			results["IIS"] = 90
		}
	}
	
	// 检查头部组合模式
	if _, hasASP := headers["x-aspnet-version"]; hasASP {
		if _, hasIIS := results["IIS"]; hasIIS {
			// 如果同时存在ASP.NET和IIS，提高ASP.NET的置信度
			results["ASP.NET"] = 100
		}
	}
	
	return results
}


