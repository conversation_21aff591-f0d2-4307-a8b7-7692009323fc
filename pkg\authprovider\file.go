// Author: chenjb
// Version: V1.0
// Date: 2025-06-13 15:13:45
// FilePath: /yaml_scan/pkg/authprovider/file.go
// Description: 实现了基于文件的认证提供者，可以从配置文件中加载认证策略
package authprovider

import (
	"errors"
	"fmt"
	"net"
	"net/url"
	"regexp"
	"strings"
	"yaml_scan/pkg/authprovider/authx"
	urlutil "yaml_scan/utils/url"
)

// FileAuthProvider  是基于文件的认证提供者
// 它接受一个密钥文件并返回相应的提供者
type FileAuthProvider struct {
	// Path 存储密钥文件的路径
	Path     string
	// store 保存从文件加载的认证数据
	store    *authx.Authx
	// compiled 存储编译后的正则表达式与认证策略的映射
	compiled map[*regexp.Regexp][]authx.AuthStrategy
	// domains 存储域名与认证策略的映射
	domains  map[string][]authx.AuthStrategy
}

// NewFileAuthProvider 创建一个新的基于文件的认证提供者
// @param path string: 密钥文件的路径
// @param callback authx.LazyFetchSecret:  用于懒加载动态密钥的回调函数
// @return AuthProvider AuthProvider: 创建的认证提供者接口实现
// @return error error: 如果创建过程中发生错误则返回错误，否则返回nil
func NewFileAuthProvider(path string, callback authx.LazyFetchSecret) (AuthProvider, error) {
	// 从文件加载认证数据
	store, err := authx.GetAuthDataFromFile(path)
	if err != nil {
		return nil, err
	}
	// 检查是否存在密钥或动态配置
	if len(store.Secrets) == 0 && len(store.Dynamic) == 0 {
		return nil, ErrNoSecrets
	}
	
	// 如果存在动态配置但没有提供回调函数，则返回错误
	if len(store.Dynamic) > 0 && callback == nil {
		return nil, errors.New("lazy fetch callback is required for dynamic secrets")
	}
	// 验证所有静态密钥的有效性
	for _, secret := range store.Secrets {
		if err := secret.Validate(); err != nil {
			return nil, fmt.Errorf("invalid secret in file: %s", path)
		}
	}

	// 验证所有动态配置的有效性并设置回调函数
	for i, dynamic := range store.Dynamic {
		if err := dynamic.Validate(); err != nil {
			return nil, fmt.Errorf("invalid dynamic in file: %s", path)
		}
		dynamic.SetLazyFetchCallback(callback)
		store.Dynamic[i] = dynamic
	}
	// 创建并初始化提供者
	f := &FileAuthProvider{Path: path, store: store}
	f.init()
	return f, nil
}

// init 初始化文件认证提供者
// @receiver f 
func (f *FileAuthProvider) init() {
	// 处理静态密钥
	for _, _secret := range f.store.Secrets {
		 // 分配指针的副本以避免循环问题
		secret := _secret
		if len(secret.DomainsRegex) > 0 {
			for _, domain := range secret.DomainsRegex {
				if f.compiled == nil {
					f.compiled = make(map[*regexp.Regexp][]authx.AuthStrategy)
				}
				compiled, err := regexp.Compile(domain)
				if err != nil {
					continue
				}

				// 将策略添加到编译后的正则表达式映射中
				if ss, ok := f.compiled[compiled]; ok {
					f.compiled[compiled] = append(ss, secret.GetStrategy())
				} else {
					f.compiled[compiled] = []authx.AuthStrategy{secret.GetStrategy()}
				}
			}
		}

		// 处理普通域名
		for _, domain := range secret.Domains {
			if f.domains == nil {
				f.domains = make(map[string][]authx.AuthStrategy)
			}
			// 标准化域名格式
			domain = strings.TrimSpace(domain)
			domain = strings.TrimSuffix(domain, ":80")
			domain = strings.TrimSuffix(domain, ":443")
			// 将策略添加到域名映射中
			if ss, ok := f.domains[domain]; ok {
				f.domains[domain] = append(ss, secret.GetStrategy())
			} else {
				f.domains[domain] = []authx.AuthStrategy{secret.GetStrategy()}
			}
		}
	}

	// 处理动态配置
	for _, dynamic := range f.store.Dynamic {
		domain, domainsRegex := dynamic.GetDomainAndDomainRegex()

		if len(domainsRegex) > 0 {
			for _, domain := range domainsRegex {
				if f.compiled == nil {
					f.compiled = make(map[*regexp.Regexp][]authx.AuthStrategy)
				}
				compiled, err := regexp.Compile(domain)
				if err != nil {
					continue
				}
				// 将动态策略添加到编译后的正则表达式映射中
				if ss, ok := f.compiled[compiled]; !ok {
					f.compiled[compiled] = []authx.AuthStrategy{&authx.DynamicAuthStrategy{Dynamic: dynamic}}
				} else {
					f.compiled[compiled] = append(ss, &authx.DynamicAuthStrategy{Dynamic: dynamic})
				}
			}
		}
		// 处理普通域名
		for _, domain := range domain {
			if f.domains == nil {
				f.domains = make(map[string][]authx.AuthStrategy)
			}
			domain = strings.TrimSpace(domain)
			domain = strings.TrimSuffix(domain, ":80")
			domain = strings.TrimSuffix(domain, ":443")

			if ss, ok := f.domains[domain]; !ok {
				f.domains[domain] = []authx.AuthStrategy{&authx.DynamicAuthStrategy{Dynamic: dynamic}}
			} else {
				f.domains[domain] = append(ss, &authx.DynamicAuthStrategy{Dynamic: dynamic})
			}
		}
	}
}

// LookupAddr 根据给定的域名/地址查找并返回适当的认证策略
// @receiver f 
// @param addr string: 要查找的域名或地址字符串
// @return []authx.AuthStrategy []authx.AuthStrategy: 匹配的认证策略列表
func (f *FileAuthProvider) LookupAddr(addr string) []authx.AuthStrategy {
	var strategies []authx.AuthStrategy

	// 处理包含端口的地址格式（如host:port）
	if strings.Contains(addr, ":") {
		host, port, err := net.SplitHostPort(addr)
		if err == nil && (port == "80" || port == "443") {
			// 对于标准HTTP/HTTPS端口，只使用主机名部分
			addr = host
		}
	}
	// 查找精确匹配的域名
	for domain, strategy := range f.domains {
		if strings.EqualFold(domain, addr) {
			strategies = append(strategies, strategy...)
		}
	}
	// 查找匹配正则表达式的域名
	for compiled, strategy := range f.compiled {
		if compiled.MatchString(addr) {
			strategies = append(strategies, strategy...)
		}
	}

	return strategies
}

// LookupURL 根据给定的URL查找并返回适当的认证策略
// @receiver f 
// @param u *url.URL: 有效的url.URL结构体指针
// @return []authx.AuthStrategy []authx.AuthStrategy: 匹配的认证策略列表
func (f *FileAuthProvider) LookupURL(u *url.URL) []authx.AuthStrategy {
	return f.LookupAddr(u.Host)
}

// LookupURLX 根据给定的URL查找并返回适当的认证策略
// @receiver f 
// @param u *urlutil.URL: 有效的url.URL结构体指针
// @return []authx.AuthStrategy []authx.AuthStrategy: 匹配的认证策略列表
func (f *FileAuthProvider) LookupURLX(u *urlutil.URL) []authx.AuthStrategy {
	return f.LookupAddr(u.Host)
}

// GetTemplatePaths 返回认证提供者的模板路径
// @receiver f 
// @return []string []string: 模板路径列表
func (f *FileAuthProvider) GetTemplatePaths() []string {
	res := []string{}
	// 收集所有动态配置中的模板路径
	for _, dynamic := range f.store.Dynamic {
		if dynamic.TemplatePath != "" {
			res = append(res, dynamic.TemplatePath)
		}
	}
	return res
}

// PreFetchSecrets 预先获取认证提供者中的密钥
// @receiver f 
// @return error error: 可能的错误
func (f *FileAuthProvider) PreFetchSecrets() error {
	// 预取域名映射中的动态密钥
	for _, ss := range f.domains {
		for _, s := range ss {
			if val, ok := s.(*authx.DynamicAuthStrategy); ok {
				if err := val.Dynamic.Fetch(false); err != nil {
					return err
				}
			}
		}
	}
	// 预取正则表达式映射中的动态密钥
	for _, ss := range f.compiled {
		for _, s := range ss {
			if val, ok := s.(*authx.DynamicAuthStrategy); ok {
				if err := val.Dynamic.Fetch(false); err != nil {
					return err
				}
			}
		}
	}
	return nil
}

