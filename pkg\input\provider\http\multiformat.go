//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-11 16:36:38
//FilePath: /yaml_scan/pkg/input/provider/http/multiformat.go
//Description:

package http

import (
	"strings"
	"yaml_scan/pkg/input/formats"
	"yaml_scan/pkg/input/formats/burp"
	"yaml_scan/pkg/input/formats/json"
	"yaml_scan/pkg/input/formats/openapi"
	"yaml_scan/pkg/input/formats/swagger"
	"yaml_scan/pkg/input/formats/yaml"
)

// 支持的格式提供者列表
var providersList = []formats.Format{
	burp.New(),
	json.New(),
	yaml.New(),
	openapi.New(),
	swagger.New(),
}

// SupportedFormats: 返回支持的格式列表，以逗号分隔的字符串形式
//  @return string string: 支持的格式名称列表，以逗号分隔。
func SupportedFormats() string {
	var formats []string
	for _, provider := range providersList {
		// 获取每个提供者的名称并添加到格式列表中
		formats = append(formats, provider.Name())
	}
	// 将格式名称列表连接成一个以逗号分隔的字符串
	return strings.Join(formats, ", ")
}
