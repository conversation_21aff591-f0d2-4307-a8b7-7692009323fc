// Author: chenjb
// Version: V1.0
// Date: 2024-12-20 16:20:27
// FilePath: /yaml_scan/pkg/gcache/lru.go
// Description: 最近最少使用(最长时间)淘汰算法（Least Recently Used）。LRU是淘汰最长时间没有被使用的
package gcache

import (
	"time"
	"yaml_scan/pkg/gcache/list"
)

// lruItem 表示 LRU 缓存中的一个项。
type lruItem[K comparable, V any] struct {
	clock      Clock      // 用于获取当前时间的时钟接口
	key        K          // 缓存项的键
	value      V          // 缓存项的值
	expiration *time.Time // 过期时间
}

// LRUCache 表示一个最近最少使用的缓存。
// 它会优先丢弃最少使用的项。
type LRUCache[K comparable, V any] struct {
	baseCache[K, V]                                     // 嵌入的基础缓存结构体
	items           map[K]*list.Element[*lruItem[K, V]] // 存储缓存项的映射
	evictList       *list.List[*lruItem[K, V]]          // 用于管理 LRU 的双向链表
}

// init 初始化 LRUCache。
func (c *LRUCache[K, V]) init() {
	// 创建一个新的双向链表用于 LRU
	c.evictList = list.New[*lruItem[K, V]]()
	// 初始化缓存项映射
	c.items = make(map[K]*list.Element[*lruItem[K, V]], c.size+1)
}

// newLRUCache 创建一个新的 LRUCache 实例。
// @param cb *CacheBuilder:  CacheBuilder 实例，包含缓存配置
// @return *LRUCache *LRUCache: 新创建的 LRUCache 实例
func newLRUCache[K comparable, V any](cb *CacheBuilder[K, V]) *LRUCache[K, V] {
	c := &LRUCache[K, V]{}       // 创建 LRUCache 实例
	buildCache(&c.baseCache, cb) // 初始化基础缓存字段
	c.init()                     // 初始化 items 映射和 evictList
	c.loadGroup.cache = c        // 设置 loadGroup 的缓存实例
	return c                     // 返回 LRUCache 实例
}

// set 内部方法，用于设置键值对
// @param key: 要设置的键
// @param value: 对应的值
// @return *lruItem[K, V]: 设置的缓存条目
// @return error: 操作失败时的错误
func (c *LRUCache[K, V]) set(key K, value V) (*lruItem[K, V], error) {
	var err error
	// 如果定义了序列化函数，则调用它
	if c.serializeFunc != nil {
		value, err = c.serializeFunc(key, value)
		if err != nil {
			return nil, err
		}
	}

	// 检查是否存在已有项
	var item *lruItem[K, V]
	if it, ok := c.items[key]; ok {
		// 如果项已存在，将其移动到链表前面（最近使用）
		c.evictList.MoveToFront(it)
		// 获取条目
		item = it.Value
		// 更新值
		item.value = value
	} else {
		// 验证缓存大小是否未超过限制
		if c.evictList.Len() >= c.size {
			// 移除一个最旧的项
			c.evict(1)
		}
		// 创建新的 lruItem
		item = &lruItem[K, V]{
			clock: c.clock,
			key:   key,
			value: value,
		}
		// 将新条目插入链表前端
		c.items[key] = c.evictList.PushFront(item)
	}

	// 设置过期时间
	if c.expiration != nil {
		t := c.clock.Now().Add(*c.expiration)
		item.expiration = &t
	}

	// 调用添加回调函数
	if c.addedFunc != nil {
		c.addedFunc(key, value)
	}

	return item, nil
}

// Set 插入或更新指定的键值对
// @param key: 要设置的键
// @param value: 对应的值
// @return error: 操作失败时的错误
func (c *LRUCache[K, V]) Set(key K, value V) error {
	// 加写锁，确保并发安全
	c.mu.Lock()
	defer c.mu.Unlock()
	_, err := c.set(key, value)
	return err
}

// SetWithExpire 插入或更新指定的键值对，并设置过期时间
// @param key: 要设置的键
// @param value: 对应的值
// @param expiration: 过期时间
// @return error: 操作失败时的错误
func (c *LRUCache[K, V]) SetWithExpire(key K, value V, expiration time.Duration) error {
	// 加写锁，确保并发安全
	c.mu.Lock()
	defer c.mu.Unlock()
	item, err := c.set(key, value)
	if err != nil {
		return err
	}

	// 计算过期时间
	t := c.clock.Now().Add(expiration)
	item.expiration = &t
	return nil
}

// getValue 内部方法，用于获取原始值
// @param key: 要获取的键
// @param enableCount: 是否触发统计
// @return V: 键对应的值
// @return error: 操作失败时的错误
func (c *LRUCache[K, V]) getValue(key K, enableCount bool) (V, error) {
	// 加写锁，确保并发安全
	c.mu.Lock()
	// 检查键是否存在
	item, ok := c.items[key]
	if ok {
		it := item.Value
		// 检查项是否过期
		if !it.IsExpired(nil) {
			// 将项移动到链表的前面 （最近使用）
			c.evictList.MoveToFront(item)
			v := it.value
			// 自动延长租约
			c.autoLease(it)
			c.mu.Unlock()
			if enableCount {
				// 增加命中计数
				c.stats.IncrHitCount()
			}
			return v, nil
		}
		// 如果过期，移除该项
		c.removeElement(item)
	}
	c.mu.Unlock()
	if enableCount {
		// 增加未命中计数
		c.stats.IncrMissCount()
	}
	return c.nilV, KeyNotFoundError
}

// get 内部方法，用于获取值
// @param key: 要获取的键
// @param enableCount: 是否触发统计
// @return V: 键对应的值
// @return error: 操作失败时的错误
func (c *LRUCache[K, V]) get(key K, enableCount bool) (V, error) {
	v, err := c.getValue(key, enableCount)
	if err != nil {
		return c.nilV, err
	}
	// 如果定义了反序列化函数
	if c.deserializeFunc != nil {
		return c.deserializeFunc(key, v)
	}
	return v, nil
}

// getWithLoader  使用 LoaderFunc 加载值
// @param key: 要加载的键
// @param isWait: 是否等待加载完成
// @return V: 加载的值
// @return error: 加载过程中的错误
func (c *LRUCache[K, V]) getWithLoader(key K, isWait bool) (V, error) {
	// 检查是否定义了加载器函数
	if c.loaderExpireFunc == nil {
		return c.nilV, KeyNotFoundError
	}

	value, _, err := c.load(key, func(v V, expiration *time.Duration, e error) (V, error) {
		if e != nil {
			return c.nilV, e
		}
		c.mu.Lock()
		defer c.mu.Unlock()
		// 将加载的值设置到缓存中
		item, err := c.set(key, v)
		if err != nil {
			return c.nilV, err
		}
		// 设置过期时间
		if expiration != nil {
			t := c.clock.Now().Add(*expiration)
			item.expiration = &t
		}
		return v, nil
	}, isWait)
	if err != nil {
		return c.nilV, err
	}
	return value, nil
}

// Get 从缓存中获取键的值，如果未找到则通过加载器加载。

// Get 从缓存中获取键的值，如果未找到则通过加载器加载。
// @param key: 要获取的键
// @return  V: 键对应的值
// @return error: 操作失败时的错误
func (c *LRUCache[K, V]) Get(key K) (V, error) {
	v, err := c.get(key, false)
	if err == KeyNotFoundError {
		return c.getWithLoader(key, true)
	}
	return v, err
}

// evict 移除缓存中最旧的项。
// @param count: 要移除的条目数量
func (c *LRUCache[K, V]) evict(count int) {
	for i := 0; i < count; i++ {
		// 获取链表的最后一个元素（最旧的项）
		ent := c.evictList.Back()
		if ent == nil {
			return
		} else {
			c.removeElement(ent)
		}
	}
}

// has 检查缓存中是否存在指定键的有效项。
// @param key: 要检查的键
// @param now: 当前时间
// @return bool: 如果键存在且未过期，返回 true
func (c *LRUCache[K, V]) has(key K, now *time.Time) bool {
	// 尝试获取指定键的项
	item, ok := c.items[key]
	if !ok {
		return false
	}
	// 检查该项是否过期，返回结果
	return !item.Value.IsExpired(now)
}

// Has 检查缓存中是否存在指定键的有效项。
// @param key: 要检查的键
// @return bool: 如果键存在且未过期，返回 true
func (c *LRUCache[K, V]) Has(key K) bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	now := time.Now()
	return c.has(key, &now)
}

// IsExpired 检查缓存条目是否已过期
// @param now: 当前时间，如果为 nil 则使用条目的时钟获取
// @return bool: 如果条目已过期，返回 true
func (it *lruItem[K, V]) IsExpired(now *time.Time) bool {
	// 如果没有设置过期时间，则认为未过期
	if it.expiration == nil {
		return false
	}
	if now == nil {
		t := it.clock.Now()
		now = &t
	}
	// 检查是否过期
	return it.expiration.Before(*now)
}

// removeElement 从缓存中移除指定的链表元素
// @param e: 要移除的链表元素
func (c *LRUCache[K, V]) removeElement(e *list.Element[*lruItem[K, V]]) {
	// 从链表中移除元素
	c.evictList.Remove(e)
	// 获取要移除的项
	entry := e.Value
	// 从映射中删除该项
	delete(c.items, entry.key)

	// 如果定义了 evictedFunc，则调用它
	if c.evictedFunc != nil {
		entry := e.Value
		c.evictedFunc(entry.key, entry.value)
	}
}

// remove 内部方法，删除指定的键
// @param key: 要删除的键
// @return bool: 如果键存在并被删除，返回 true
func (c *LRUCache[K, V]) remove(key K) bool {
	if ent, ok := c.items[key]; ok {
		c.removeElement(ent)
		return true
	}
	return false
}

// Remove 删除指定的键
// @param key: 要删除的键
// @return bool: 如果键存在并被删除，返回 true
func (c *LRUCache[K, V]) Remove(key K) bool {
	// 加写锁，确保并发安全
	c.mu.Lock()
	defer c.mu.Unlock()

	return c.remove(key)
}

// keys 返回缓存中所有键的切片（内部方法）
func (c *LRUCache[K, V]) keys() []K {
	c.mu.RLock()                    // 加读锁，允许多个读取操作并发
	defer c.mu.RUnlock()            // 函数结束时解锁
	keys := make([]K, len(c.items)) // 初始化键切片
	var i = 0                       // 计数器
	for k := range c.items {        // 遍历所有键
		keys[i] = k // 添加键
		i++         // 计数器加一
	}
	return keys // 返回键切片
}

// Keys 返回缓存中所有键的切片
// @param checkExpired: 是否检查过期条目
// @return []K: 缓存中的键切片
func (c *LRUCache[K, V]) Keys(checkExpired bool) []K {
	c.mu.RLock()
	defer c.mu.RUnlock()
	// 创建一个存储结果的切片
	keys := make([]K, 0, len(c.items))
	now := time.Now()
	for k := range c.items {
		// 检查是否需要过期检查
		if !checkExpired || c.has(k, &now) {
			keys = append(keys, k)
		}
	}
	return keys
}

// GetALL 返回缓存中的所有键值对。
// @param checkExpired: 是否检查过期条目
// @return map[K]V: 缓存中的键值对映射
func (c *LRUCache[K, V]) GetALL(checkExpired bool) map[K]V {
	c.mu.RLock()
	defer c.mu.RUnlock()
	// 创建一个存储结果的映射
	items := make(map[K]V, len(c.items))
	now := time.Now()
	for k, item := range c.items {
		// 检查是否需要过期检查
		if !checkExpired || c.has(k, &now) {
			items[k] = item.Value.value
		}
	}
	return items
}

// Len 返回缓存中项的数量
// @param checkExpired: 是否检查过期条目
// @return int: 缓存中的项数
func (c *LRUCache[K, V]) Len(checkExpired bool) int {
	c.mu.RLock()
	defer c.mu.RUnlock()
	// 如果不检查过期，直接返回项的数量
	if !checkExpired {
		return len(c.items)
	}
	var length int
	now := time.Now()
	for k := range c.items {
		// 检查该项是否有效
		if c.has(k, &now) {
			length++
		}
	}
	return length
}

// autoLease 自动续租缓存条目
// @param item: 要续租的缓存条目
func (c *LRUCache[K, V]) autoLease(item *lruItem[K, V]) {
	// 如果缓存项没有设置过期时间，则不进行任何操作
	if item.expiration == nil {
		return
	}
	if c.lease == nil {
		return
	}
	// 获取当前时间并加上租约时间
	t := item.clock.Now().Add(*c.lease)
	item.expiration = &t
}

// GetIFPresent  从缓存中获取指定键的值，不等待函数加载完成 异步调用
// @param  key: 要获取的键
// @return  V: 键对应的值
// @return error: 操作失败时的错误
func (c *LRUCache[K, V]) GetIFPresent(key K) (V, error) {
	v, err := c.get(key, false)
	if err == KeyNotFoundError {
		return c.getWithLoader(key, false)
	}
	return v, err
}

// Purge 完全清空缓存。
func (c *LRUCache[K, V]) Purge() {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 如果定义了清除访问者函数，则遍历所有项并调用该函数
	if c.purgeVisitorFunc != nil {
		for key, item := range c.items {
			it := item.Value
			v := it.value
			c.purgeVisitorFunc(key, v)
		}
	}

	c.init()
}
