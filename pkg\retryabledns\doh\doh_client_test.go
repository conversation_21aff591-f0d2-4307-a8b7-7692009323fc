//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-26 16:28:16
//FilePath: /yaml_scan/pkg/retryabledns/doh/doh_client_test.go
//Description:

package doh

import (
	"net/http"
	"testing"

	"github.com/miekg/dns"
	"github.com/stretchr/testify/require"
)

// TestNew 测试 New 函数
func TestNew(t *testing.T) {
	// 调用 New 创建 Client
	client := New()
	// 验证默认配置是否正确
	require.Equal(t, Google, client.DefaultResolver, "默认解析器应为 Google")
	require.NotNil(t, client.httpClient, "httpClient 不应为 nil")
	require.Equal(t, DefaultTimeout, client.httpClient.Timeout, "httpClient 的超时应为默认值")
}

// TestNewWithOptions 测试 NewWithOptions 函数
func TestNewWithOptions(t *testing.T) {
	// 创建测试用的 Options
	options := Options{
		DefaultResolver: Google,
		HttpClient:      &http.Client{},
	}
	// 调用 NewWithOptions 创建 Client
	client := NewWithOptions(options)
	// 验证 Client 的字段是否正确配置
	require.Equal(t, Google, client.DefaultResolver, "默认解析器应为 Cloudflare")
	require.Equal(t, options.HttpClient, client.httpClient, "httpClient 应与传入的相同")
}

// TestQuery 测试 Query 方法
func TestQuery(t *testing.T) {
	// 创建一个Client
	client := New()
	// 调用 Query 方法
	resp, err := client.Query("www.baidu.com", A)
	// 验证结果
	require.NoError(t, err, "Query 不应返回错误")
	require.NotNil(t, resp, "响应不应为 nil")
	require.Equal(t, 0, resp.Status, "响应状态应为 0")
	require.Len(t, resp.Answer, 4, "应返回一个 Answer")
	require.Equal(t, "www.baidu.com.", resp.Answer[0].Name, "Answer 的 Name 应正确")
	// require.Equal(t, 5, resp.Answer[0].Type, "Answer 的 Type 应正确")
	// require.Equal(t, 3600, resp.Answer[0].TTL, "Answer 的 TTL 应正确")
	// require.Equal(t, "93.184.216.34", resp.Answer[0].Data, "Answer 的 Data 应正确")
}

// TestQueryWithResolver 测试 QueryWithResolver 方法
func TestQueryWithResolver(t *testing.T) {
	// 创建一个Client
	client := New()
	// 调用 QueryWithResolver 方法
	resp, err := client.QueryWithResolver(Tencent, "www.baidu.com", A)
	// 验证结果
	require.NoError(t, err, "QueryWithResolver 不应返回错误")
	require.NotNil(t, resp, "响应不应为 nil")
	require.Equal(t, 0, resp.Status, "响应状态应为 0")
}

// TestQueryWithJsonAPI 测试 QueryWithJsonAPI 方法
func TestQueryWithJsonAPI(t *testing.T) {
	// 创建一个Client
	client := New()
	// 调用 QueryWithJsonAPI 方法
	resp, err := client.QueryWithJsonAPI(Quad, "www.baidu.com", A)
	// 验证结果
	require.NoError(t, err, "QueryWithJsonAPI 不应返回错误")
	require.NotNil(t, resp, "响应不应为 nil")
	require.Equal(t, 0, resp.Status, "响应状态应为 0")
}

// TestQueryWithDOH 测试 QueryWithDOH 方法
func TestQueryWithDOH(t *testing.T) {
	// 创建一个Client
	client := New()
	// 调用 QueryWithDOH 方法
	resp, err := client.QueryWithDOH(MethodGet, Ali, "www.baidu.com", dns.TypeA)
	// 验证结果
	require.NoError(t, err, "QueryWithDOH 不应返回错误")
	require.NotNil(t, resp, "响应不应为 nil")
}

// TestQueryWithDOHMsg 测试 QueryWithDOHMsg 方法
func TestQueryWithDOHMsg(t *testing.T) {
	// 创建一个Client
	client := New()

	// 创建 DNS 消息
	msg := &dns.Msg{
		Question: []dns.Question{
			{
				Name:   "www.baidu.com.",
				Qtype:  dns.TypeA,
				Qclass: dns.ClassINET,
			},
		},
	}

	// 测试 POST 方法
	resp, err := client.QueryWithDOHMsg(MethodPost, Ali, msg)
	require.NoError(t, err, "QueryWithDOHMsg 不应返回错误")
	require.NotNil(t, resp, "响应不应为 nil")
	require.Equal(t, "www.baidu.com.", resp.Answer[0].Header().Name, "Answer 的 Name 应正确")
}
