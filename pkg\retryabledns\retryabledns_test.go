//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-16 16:13:20
//FilePath: /yaml_scan/pkg/retryabledns/retryabledns_test.go
//Description:

package retryabledns

import (
	"fmt"
	"testing"
	"time"

	"github.com/miekg/dns"
	"github.com/stretchr/testify/require"
)

// TestNewWithOptions 测试 NewWithOptions 函数的正确性
func TestNewWithOptions(t *testing.T) {
	// 场景 1: 成功创建客户端实例（无代理、无连接池）
	t.Run("成功创建客户端（无代理、无连接池）", func(t *testing.T) {
		options := Options{
			BaseResolvers: []string{"*******", "*******"},
			Timeout:       5 * time.Second,
			MaxRetries:    3,
		}
		client, err := NewWithOptions(options)
		require.NoError(t, err, "NewWithOptions 不应返回错误")
		require.NotNil(t, client, "客户端实例不应为 nil")
		require.Len(t, client.resolvers, 2, "解析器数量应为 2")
		require.NotNil(t, client.udpClient, "UDP 客户端不应为 nil")
		require.NotNil(t, client.tcpClient, "TCP 客户端不应为 nil")
		require.NotNil(t, client.dohClient, "DoH 客户端不应为 nil")
		require.NotNil(t, client.dotClient, "DoT 客户端不应为 nil")
		require.Nil(t, client.udpProxy, "UDP 代理应为 nil")
		require.Nil(t, client.tcpProxy, "TCP 代理应为 nil")
		require.Nil(t, client.dotProxy, "DoT 代理应为 nil")
		require.True(t, client.udpConnPool.Map.IsEmpty(), "连接池应为空")
	})

	// 场景 2: 成功创建客户端实例（带代理）
	t.Run("成功创建客户端（带代理）", func(t *testing.T) {
		options := Options{
			BaseResolvers: []string{"*******", "*******"},
			Timeout:       5 * time.Second,
			MaxRetries:    3,
			Proxy:         "socks5://127.0.0.1:8080",
		}
		client, err := NewWithOptions(options)
		require.NoError(t, err, "NewWithOptions 不应返回错误")
		require.NotNil(t, client, "客户端实例不应为 nil")
		require.NotNil(t, client.udpProxy, "UDP 代理不应为 nil")
		require.NotNil(t, client.tcpProxy, "TCP 代理不应为 nil")
		require.NotNil(t, client.dotProxy, "DoT 代理不应为 nil")
		// 验证解析器是否被强制转换为 TCP
		for _, resolver := range client.resolvers {
			if networkResolver, ok := resolver.(*NetworkResolver); ok {
				require.Equal(t, TCP, networkResolver.Protocol, "解析器协议应为 TCP")
			}
		}
	})

	// 场景 3: 成功创建客户端实例（带连接池）
	t.Run("成功创建客户端（带连接池）", func(t *testing.T) {
		options := Options{
			BaseResolvers:         []string{"*******", "*******"},
			Timeout:               5 * time.Second,
			MaxRetries:            3,
			ConnectionPoolThreads: 2,
		}
		client, err := NewWithOptions(options)
		require.NoError(t, err, "NewWithOptions 不应返回错误")
		require.NotNil(t, client, "客户端实例不应为 nil")
		require.False(t, client.udpConnPool.Map.IsEmpty(), "连接池不应为空")
		require.Len(t, client.udpConnPool.Map, 2, "连接池数量应为 2")
	})

	// 场景 4: 无效的代理 URL
	t.Run("无效的代理 URL", func(t *testing.T) {
		options := Options{
			BaseResolvers: []string{"*******"},
			Timeout:       5 * time.Second,
			MaxRetries:    3,
			Proxy:         "invalid-url",
		}
		client, err := NewWithOptions(options)
		require.Error(t, err, "NewWithOptions 应返回错误")
		require.Nil(t, client, "客户端实例应为 nil")
	})
}

// TestResolveWithSyscall 测试ResolveWithSyscall方法
func TestResolveWithSyscall(t *testing.T) {
	// 创建新的客户端实例
	options := Options{
		BaseResolvers:         []string{"*******", "*******"},
		Timeout:               2 * time.Second,
		MaxRetries:            3,
		ConnectionPoolThreads: 2,
	}
	client, err := NewWithOptions(options)
	require.NoError(t, err, "创建客户端失败")

	// 测试解析已知的主机名
	t.Run("解析已知主机名", func(t *testing.T) {
		host := "www.baidu.com"
		dnsData, err := client.ResolveWithSyscall(host)
		require.NoError(t, err, "ResolveWithSyscall解析已知主机名不应返回错误")
		require.NotNil(t, dnsData, "DNSData不应为nil")
		require.Equal(t, host, dnsData.Host, "主机名应与查询的主机名一致")
		require.NotEmpty(t, dnsData.A, "example.com的A记录不应为空")
		// 可选：检查AAAA记录是否存在
	})

	// 测试解析不存在的主机名
	t.Run("解析不存在的主机名", func(t *testing.T) {
		host := "nonexistent.example.com"
		dnsData, err := client.ResolveWithSyscall(host)
		require.Error(t, err, "ResolveWithSyscall解析不存在的主机名应返回错误")
		require.Nil(t, dnsData, "不存在的主机名DNSData应为nil")
	})

	// 测试解析IP地址（应失败，因为它不是主机名）
	t.Run("解析IP地址", func(t *testing.T) {
		host := "**************"
		dnsData, err := client.ResolveWithSyscall(host)
		require.NoError(t, err, "ResolveWithSyscall解析已知主机名不应返回错误")
		require.NotNil(t, dnsData, "DNSData不应为nil")
		require.Equal(t, host, dnsData.Host, "主机名应与查询的主机名一致")
	})
}

// TestQueryMultiple 测试 QueryMultiple 方法
func TestQueryMultiple(t *testing.T) {
	options := Options{
		BaseResolvers:         []string{"*******"},
		Timeout:               5 * time.Second,
		MaxRetries:            3,
		ConnectionPoolThreads: 2,
	}
	client, err := NewWithOptions(options)
	host := "www.baidu.com"
	requestTypes := []uint16{dns.TypeA, dns.TypeAAAA}
	dnsData, err := client.QueryMultiple(host, requestTypes)
	require.NoError(t, err, "QueryMultiple 不应返回错误")
	require.NotNil(t, dnsData, "DNSData 不应为 nil")
	require.Equal(t, host, dnsData.Host, "主机名应与查询的主机名一致")
}

// TestResolve 测试 Resolve 方法
func TestResolve(t *testing.T) {
	options := Options{
		BaseResolvers:         []string{"*******"},
		Timeout:               5 * time.Second,
		MaxRetries:            3,
		ConnectionPoolThreads: 2,
	}
	client, err := NewWithOptions(options)
	host := "www.baidu.com"
	dnsData, err := client.Resolve(host)
	require.NoError(t, err, "Resolve 不应返回错误")
	require.NotNil(t, dnsData, "DNSData 不应为 nil")
	require.Equal(t, host, dnsData.Host, "主机名应与查询的主机名一致")
}

// TestDo 测试 Do 方法的正确性
func TestDo(t *testing.T) {
	options := Options{
		BaseResolvers:         []string{"*******"},
		Timeout:               5 * time.Second,
		MaxRetries:            3,
		ConnectionPoolThreads: 2,
	}
	client, err := NewWithOptions(options)

	// 创建一个 DNS 查询消息，查询 "example.com" 的 A 记录
	msg := &dns.Msg{}
	msg.SetQuestion("www.baidu.com.", dns.TypeA)

	// 调用 Do 方法发送查询
	resp, err := client.Do(msg)

	// 使用 testify/require 验证结果
	require.NoError(t, err, "Do 方法不应返回错误")                      // 确保没有错误
	require.NotNil(t, resp, "DNS 响应不应为 nil")                    // 确保响应不为空
	require.Equal(t, dns.RcodeSuccess, resp.Rcode, "响应状态码应为成功") // 确保状态码为成功
}

// TestQuery 测试 Query 方法的功能。
func TestQuery(t *testing.T) {
	// 初始化客户端实例
	options := Options{
		BaseResolvers:         []string{"*******"},
		Timeout:               5 * time.Second,
		MaxRetries:            3,
		ConnectionPoolThreads: 2,
	}
	client, err := NewWithOptions(options)
	host := "www.baidu.com"
	requestType := dns.TypeA

	// 执行 Query 方法进行测试
	dnsData, err := client.Query(host, requestType)
	// 断言没有错误返回
	require.NoError(t, err, "Query 方法不应返回错误")
	// 断言返回的 DNSData 不为空
	require.NotNil(t, dnsData, "DNSData 不应为 nil")
}

// TestA 测试 A 方法的功能。
func TestA(t *testing.T) {
	// 初始化客户端实例
	options := Options{
		BaseResolvers:         []string{"*******"},
		Timeout:               5 * time.Second,
		MaxRetries:            3,
		ConnectionPoolThreads: 2,
	}
	client, err := NewWithOptions(options)
	host := "www.baidu.com"

	// 执行 A 方法进行测试
	dnsData, err := client.A(host)
	// 断言没有错误返回
	require.NoError(t, err, "A 方法不应返回错误")
	// 断言返回的 DNSData 不为空
	require.NotNil(t, dnsData, "DNSData 不应为 nil")
}

// TestQueryMultipleWithResolver 测试 QueryMultipleWithResolver 方法
func TestQueryMultipleWithResolver(t *testing.T) {
	// 创建客户端
	client, _ := New([]string{"*******"}, 3)

	// 测试用例：成功查询
	t.Run("成功查询", func(t *testing.T) {
		host := "www.baidu.com"
		requestTypes := []uint16{dns.TypeA}
		dnsData, err := client.QueryMultipleWithResolver(host, requestTypes, client.resolvers[0])
		fmt.Println(dnsData)
		require.NoError(t, err, "QueryMultipleWithResolver 不应返回错误")
		require.NotNil(t, dnsData, "DNSData 不应为 nil")
		require.Equal(t, host, dnsData.Host, "主机名应与输入一致")
	})
}

// TestAXFR 测试 AXFR 方法
func TestAXFR(t *testing.T) {
	t.Skip("大多数的公共dns服务器禁用了axfr")
	// 创建模拟客户端
	client, _ := New([]string{"*******"}, 3)

	// 测试用例：成功执行 AXFR
	t.Run("成功执行 AXFR", func(t *testing.T) {
		host := "www.a.shifen.com"

		axfrData, err := client.AXFR(host)
		require.NoError(t, err, "AXFR 不应返回错误")
		require.NotNil(t, axfrData, "AXFRData 不应为 nil")
		require.Equal(t, host, axfrData.Host, "主机名应与输入一致")
		require.Len(t, axfrData.DNSData, 3, "DNSData 应包含 3 条记录（2 个 NS 服务器 + 1 个默认解析器）")
	})

}

// TestQueryParallel 测试 QueryParallel 方法的功能。
func TestQueryParallel(t *testing.T) {
	// 创建模拟客户端
	client, _ := New([]string{"*******"}, 3)

	// 测试用例 1：成功查询多个解析器
	t.Run("成功查询", func(t *testing.T) {
		host := "www.baidu.com"
		requestType := dns.TypeA
		resolvers := []string{"*******:53", "***************:53"}

		// 执行 QueryParallel 方法
		dnsDatas, err := client.QueryParallel(host, requestType, resolvers)

		// 断言结果
		require.NoError(t, err, "QueryParallel 不应返回错误")
		require.Len(t, dnsDatas, 2, "应返回 2 个 DNSData 实例")
		for i, dnsData := range dnsDatas {
			require.Equal(t, host, dnsData.Host, "主机名应为 www.baidu.com")
			require.Equal(t, dns.RcodeToString[dns.RcodeSuccess], dnsData.StatusCode, "状态码应为 NOERROR")
			require.Equal(t, dns.RcodeSuccess, dnsData.StatusCodeRaw, "原始状态码应为 0")
			require.Contains(t, dnsData.Resolver, resolvers[i], "解析器地址应正确")
			require.NotNil(t, dnsData.RawResp, "原始响应不应为 nil")
			require.NotEmpty(t, dnsData.Raw, "原始响应字符串不应为空")
		}
	})
}

// TestTrace 测试 Trace 方法的功能。
func TestTrace(t *testing.T) {
	// 创建客户端
	client, _ := New([]string{"*******"}, 3)

	// 测试用例 1：成功追踪，无 CNAME
	t.Run("成功追踪无CNAME", func(t *testing.T) {
		host := "www.baidu.com"
		requestType := dns.TypeA
		maxrecursion := 3

		// 执行 Trace 方法
		traceData, err := client.Trace(host, requestType, maxrecursion)

		// 断言结果
		require.NoError(t, err, "Trace 不应返回错误")
		require.NotNil(t, traceData, "TraceData 不应为 nil")
		require.Equal(t, "www.baidu.com.", traceData.DNSData[0].Host, "第一个 DNSData 的主机名应为 www.baidu.com")
		require.Equal(t, "www.baidu.com.", traceData.DNSData[1].Host, "第二个 DNSData 的主机名应为 www.baidu.com")
	})
}
