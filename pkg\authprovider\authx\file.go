// Author: chenjb
// Version: V1.0
// Date: 2025-06-13 14:44:44
// FilePath: /yaml_scan/pkg/authprovider/authx/file.go
// Description: 实现了认证文件的解析和管理，定义了各种认证类型和配置结构
package authx

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"yaml_scan/utils/generic"
	stringsutil "yaml_scan/utils/strings"

	"gopkg.in/yaml.v2"
)

// AuthType 表示认证类型的字符串类型
type AuthType string

const (
	BasicAuth       AuthType = "BasicAuth"   // 基本认证（用户名密码）
	BearerTokenAuth AuthType = "BearerToken" // 令牌认证
	HeadersAuth     AuthType = "Header"      // 请求头认证
	CookiesAuth     AuthType = "Cookie"      // Cookie认证
	QueryAuth       AuthType = "Query"       // 查询参数认证
)

// KV 表示键值对结构，用于请求头、查询参数等
type KV struct {
	Key   string `json:"key" yaml:"key"`     // 键名
	Value string `json:"value" yaml:"value"` // 键值
}

// Validate 验证键值对的有效性
// @receiver k
// @return error error:可能的错误
func (k *KV) Validate() error {
	if k.Key == "" {
		return fmt.Errorf("key cannot be empty")
	}
	if k.Value == "" {
		return fmt.Errorf("value cannot be empty")
	}
	return nil
}

// Cookie 表示Cookie结构，用于Cookie认证
type Cookie struct {
	Key   string `json:"key" yaml:"key"`     // Cookie名称
	Value string `json:"value" yaml:"value"` // Cookie值
	Raw   string `json:"raw" yaml:"raw"`     // 原始Cookie字符串
}

// Validate 验证Cookie的有效性
// @receiver c 
// @return error error: 
func (c *Cookie) Validate() error {
	if c.Raw != "" {
		return nil
	}
	if c.Key == "" {
		return fmt.Errorf("key cannot be empty")
	}
	if c.Value == "" {
		return fmt.Errorf("value cannot be empty")
	}
	return nil
}

// Parse 解析原始Cookie字符串
// @receiver c 
// @return error error: 可能的错误
func (c *Cookie) Parse() error {
	if c.Raw == "" {
		return fmt.Errorf("raw cookie cannot be empty")
	}
	// 去除前缀
	tmp := strings.TrimPrefix(c.Raw, "Set-Cookie: ")
	slice := strings.Split(tmp, ";")
	if len(slice) == 0 {
		return fmt.Errorf("invalid raw cookie no ; found")
	}
	// 第一个元素是Cookie名称和值
	cookie := strings.Split(slice[0], "=")
	if len(cookie) == 2 {
		c.Key = cookie[0]
		c.Value = cookie[1]
		return nil
	}
	return fmt.Errorf("invalid raw cookie: %s", c.Raw)
}

// AuthFileInfo 表示认证文件的元信息
type AuthFileInfo struct {
	Name        string `json:"name" yaml:"name"`               // 名称
	Author      string `json:"author" yaml:"author"`           // 作者
	Severity    string `json:"severity" yaml:"severity"`       // 严重程度
	Description string `json:"description" yaml:"description"` // 描述
}

// Authx 表示密钥或凭证文件的结构
type Authx struct {
	ID      string       `json:"id" yaml:"id"`           // 标识符
	Info    AuthFileInfo `json:"info" yaml:"info"`       // 元信息
	Secrets []Secret     `json:"static" yaml:"static"`   // 静态密钥列表
	Dynamic []Dynamic    `json:"dynamic" yaml:"dynamic"` // 动态密钥列表
}

// Secret  表示密钥或凭证的结构
type Secret struct {
	Type            string   `json:"type" yaml:"type"`                   // 认证类型
	Domains         []string `json:"domains" yaml:"domains"`             // 适用的域名列表
	DomainsRegex    []string `json:"domains-regex" yaml:"domains-regex"` // 适用的域名正则表达式列表
	Headers         []KV     `json:"headers" yaml:"headers"`             // 请求头列表（用于HeadersAuth）
	Cookies         []Cookie `json:"cookies" yaml:"cookies"`             // Cookie列表（用于CookiesAuth）
	Params          []KV     `json:"params" yaml:"params"`               // 查询参数列表（用于QueryAuth）
	Username        string   `json:"username" yaml:"username"`           // 用户名（可以是邮箱或用户名）
	Password        string   `json:"password" yaml:"password"`           // 密码
	Token           string   `json:"token" yaml:"token"`                 // 令牌（用于BearerTokenAuth）
	skipCookieParse bool     `json:"-" yaml:"-"`                         // 临时标志，用于跳过Cookie解析（在动态密钥中使用）
}

// GetStrategy 返回密钥的认证策略
// @receiver s 
// @return AuthStrategy AuthStrategy: 根据密钥类型创建的认证策略
func (s *Secret) GetStrategy() AuthStrategy {
	switch {
	case strings.EqualFold(s.Type, string(BasicAuth)):
		return NewBasicAuthStrategy(s)
	case strings.EqualFold(s.Type, string(BearerTokenAuth)):
		return NewBearerTokenAuthStrategy(s)
	case strings.EqualFold(s.Type, string(HeadersAuth)):
		return NewHeadersAuthStrategy(s)
	case strings.EqualFold(s.Type, string(CookiesAuth)):
		return NewCookiesAuthStrategy(s)
	case strings.EqualFold(s.Type, string(QueryAuth)):
		return NewQueryAuthStrategy(s)
	}
	return nil
}

// Validate 验证密钥的有效性
// @receiver s 
// @return error error: 可能的错误
func (s *Secret) Validate() error {
	// 检查认证类型是否有效
	if !stringsutil.EqualFoldAny(s.Type, SupportedAuthTypes()...) {
		return fmt.Errorf("invalid type: %s", s.Type)
	}
	// 检查域名或域名正则表达式是否为空
	if len(s.Domains) == 0 && len(s.DomainsRegex) == 0 {
		return fmt.Errorf("domains or domains-regex cannot be empty")
	}
	// 验证域名正则表达式的有效性
	if len(s.DomainsRegex) > 0 {
		for _, domain := range s.DomainsRegex {
			_, err := regexp.Compile(domain)
			if err != nil {
				return fmt.Errorf("invalid domain regex: %s", domain)
			}
		}
	}

	// 根据认证类型进行特定验证
	switch {
	case strings.EqualFold(s.Type, string(BasicAuth)):
		// 基本认证需要用户名和密码
		if s.Username == "" {
			return fmt.Errorf("username cannot be empty in basic auth")
		}
		if s.Password == "" {
			return fmt.Errorf("password cannot be empty in basic auth")
		}
	case strings.EqualFold(s.Type, string(BearerTokenAuth)):
		// 令牌认证需要令牌
		if s.Token == "" {
			return fmt.Errorf("token cannot be empty in bearer token auth")
		}
	case strings.EqualFold(s.Type, string(HeadersAuth)):
		// 请求头认证需要请求头列表
		if len(s.Headers) == 0 {
			return fmt.Errorf("headers cannot be empty in headers auth")
		}
		// 验证每个请求头的有效性
		for _, header := range s.Headers {
			if err := header.Validate(); err != nil {
				return fmt.Errorf("invalid header in headersAuth: %s", err)
			}
		}
	case strings.EqualFold(s.Type, string(CookiesAuth)):
		// Cookie认证需要Cookie列表
		if len(s.Cookies) == 0 {
			return fmt.Errorf("cookies cannot be empty in cookies auth")
		}
		for _, cookie := range s.Cookies {
			// 解析和验证每个Cookie的有效性
			if cookie.Raw != "" && !s.skipCookieParse {
				if err := cookie.Parse(); err != nil {
					return fmt.Errorf("invalid raw cookie in cookiesAuth: %s", err)
				}
			}
			if err := cookie.Validate(); err != nil {
				return fmt.Errorf("invalid cookie in cookiesAuth: %s", err)
			}
		}
	case strings.EqualFold(s.Type, string(QueryAuth)):
		// 查询参数认证需要查询参数列表
		if len(s.Params) == 0 {
			return fmt.Errorf("query cannot be empty in query auth")
		}
		for _, query := range s.Params {
			if err := query.Validate(); err != nil {
				return fmt.Errorf("invalid query in queryAuth: %s", err)
			}
		}
	default:
		return fmt.Errorf("invalid type: %s", s.Type)
	}
	return nil
}

// GetAuthDataFromFile 从文件中读取认证数据
// @param file string: 文件路径
// @return *Authx *Authx:解析出的认证数据结构
// @return error error:可能的错误
func GetAuthDataFromFile(file string) (*Authx, error) {
	// 获取文件扩展名
	ext := filepath.Ext(file)
	// 检查文件扩展名是否有效
	if !generic.EqualsAny(ext, ".yml", ".yaml", ".json") {
		return nil, fmt.Errorf("invalid file extension: supported extensions are .yml,.yaml and .json got %s", ext)
	}
	bin, err := os.ReadFile(file)
	if err != nil {
		return nil, err
	}
	// 根据文件扩展名选择解析方法
	if ext == ".yml" || ext == ".yaml" {
		return GetAuthDataFromYAML(bin)
	}
	return GetAuthDataFromJSON(bin)
}

// GetAuthDataFromYAML 从YAML数据中读取认证数据
// @param data []byte:  YAML数据字节切片
// @return *Authx *Authx: 解析出的认证数据结构
// @return error error: 可能的错误
func GetAuthDataFromYAML(data []byte) (*Authx, error) {
	var auth Authx
	// 反序列化YAML数据
	err := yaml.Unmarshal(data, &auth)
	if err != nil {
		return nil, fmt.Errorf("could not unmarshal yaml")
	}
	return &auth, nil
}

// GetAuthDataFromJSON 从JSON数据中读取认证数据
// @param data []byte: JSON数据字节切片
// @return *Authx *Authx: 解析出的认证数据结构
// @return error error: 可能的错误
func GetAuthDataFromJSON(data []byte) (*Authx, error) {
	var auth Authx
	err := json.Unmarshal(data, &auth)
	if err != nil {
		return nil, fmt.Errorf("could not unmarshal json")
	}
	return &auth, nil
}

// SupportedAuthTypes 返回支持的认证类型列表
// @return []string []string: 支持的认证类型字符串列表
func SupportedAuthTypes() []string {
	return []string{
		string(BasicAuth),
		string(BearerTokenAuth),
		string(HeadersAuth),
		string(CookiesAuth),
		string(QueryAuth),
	}
}


// GetTemplatePathsFromSecretFile reads the template IDs from the secret file
func GetTemplatePathsFromSecretFile(file string) ([]string, error) {
	auth, err := GetAuthDataFromFile(file)
	if err != nil {
		return nil, err
	}
	var paths []string
	for _, dynamic := range auth.Dynamic {
		paths = append(paths, dynamic.TemplatePath)
	}
	return paths, nil
}