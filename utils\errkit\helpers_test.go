// 
// Author: chenjb
// Version: V1.0
// Date: 2025-05-21 14:53:42
// FilePath: /yaml_scan/utils/errkit/helpers_test.go
// Description: 
package errkit

import (
	"errors"
	"fmt"
	"log/slog"
	"testing"

	"github.com/stretchr/testify/require"
)

// 测试Is函数
func TestIs(t *testing.T) {
	// 创建基础错误
	err1 := errors.New("错误1")
	err2 := errors.New("错误2")
	wrappedErr := fmt.Errorf("包装: %w", err1)
	
	// 测试单个目标错误
	require.True(t, Is(err1, err1), "错误应该匹配自身")
	require.False(t, Is(err1, err2), "不同的错误不应匹配")
	require.True(t, Is(wrappedErr, err1), "包装错误应该匹配其包装的错误")
	
	// 测试多个目标错误
	require.True(t, Is(err1, err2, err1), "错误应该匹配多个目标之一")
	require.False(t, Is(err1, err2, errors.New("错误3")), "错误不应匹配任何不相关的目标")
	
	// 测试nil错误
	require.False(t, Is(nil, err1), "nil错误不应匹配任何错误")
}

// 测试IsKind函数
func TestIsKind(t *testing.T) {
	// 创建有类型的错误
	typedErr := New("网络错误").SetKind(ErrKindNetworkTemporary)
	
	// 测试直接类型匹配
	require.True(t, IsKind(typedErr, ErrKindNetworkTemporary), "错误应匹配其类型")
	require.False(t, IsKind(typedErr, ErrKindNetworkPermanent), "错误不应匹配不同的类型")

	
	// 测试nil错误
	require.False(t, IsKind(nil, ErrKindNetworkTemporary), "nil错误不应匹配任何类型")
}


type customError struct {
	msg string
}

func (e customError) Error() string {
	return e.msg
}


// 测试As函数
func TestAs(t *testing.T) {
	// 创建自定义错误类型
	
	// 创建自定义错误实例
	custErr := customError{msg: "自定义错误"}
	wrappedCustErr := fmt.Errorf("包装: %w", custErr)
	
	// 测试成功的类型断言
	var target customError
	require.True(t, As(custErr, &target), "应成功断言为自定义错误类型")
	require.Equal(t, "自定义错误", target.msg, "错误消息应匹配")
	
	// 测试包装错误的类型断言
	var target2 customError
	require.True(t, As(wrappedCustErr, &target2), "应成功断言包装的自定义错误类型")
	require.Equal(t, "自定义错误", target2.msg, "错误消息应匹配")
	
	// 测试失败的类型断言
	var target3 *ErrorX
	require.False(t, As(custErr, &target3), "不应成功断言为不相关的类型")
}

// 测试Combine函数
func TestCombine(t *testing.T) {
	// 准备错误
	err1 := errors.New("错误1")
	err2 := errors.New("错误2")
	
	// 测试组合错误
	combined := Combine(err1, err2)
	errs := Errors(combined)
	require.Len(t, errs, 2, "组合错误应包含两个错误")
	require.Equal(t, err1.Error(), errs[0].Error(), "第一个错误应匹配")
	require.Equal(t, err2.Error(), errs[1].Error(), "第二个错误应匹配")
	
	// 测试包含nil错误的组合
	combined = Combine(err1, nil, err2)
	errs = Errors(combined)
	require.Len(t, errs, 2, "组合错误应忽略nil错误")
	
	// 测试空组合
	combined = Combine()
	require.Nil(t, combined, "空组合应返回nil")
}

// 测试Wrap和Wrapf函数
func TestWrap(t *testing.T) {
	// 准备基础错误
	baseErr := errors.New("基础错误")
	
	// 测试Wrap
	wrapped := Wrap(baseErr, "包装信息")
	require.Contains(t, wrapped.Error(), "基础错误", "包装错误应包含基础错误")
	require.Contains(t, wrapped.Error(), "包装信息", "包装错误应包含包装信息")
	
	// 测试Wrapf
	wrappedF := Wrapf(baseErr, "格式化包装: %s", "参数")
	require.Contains(t, wrappedF.Error(), "基础错误", "格式化包装错误应包含基础错误")
	require.Contains(t, wrappedF.Error(), "格式化包装: 参数", "格式化包装错误应包含格式化的包装信息")
	
	// 测试nil错误的包装
	nilWrapped := Wrap(nil, "包装nil")
	require.Nil(t, nilWrapped, "包装nil应返回nil")
	
	nilWrappedF := Wrapf(nil, "格式化包装nil: %s", "参数")
	require.Nil(t, nilWrappedF, "格式化包装nil应返回nil")
}

// 测试Errors函数
func TestErrors(t *testing.T) {
	// 准备错误
	err1 := errors.New("错误1")
	err2 := errors.New("错误2")
	combined := Combine(err1, err2)
	
	// 测试提取错误
	errs := Errors(combined)
	require.Len(t, errs, 2, "应返回两个错误")
	require.Equal(t, err1.Error(), errs[0].Error(), "第一个错误应匹配")
	require.Equal(t, err2.Error(), errs[1].Error(), "第二个错误应匹配")
	
	// 测试nil错误
	nilErrs := Errors(nil)
	require.Nil(t, nilErrs, "对nil调用Errors应返回nil")
}

// 测试Append和Join函数
func TestAppendAndJoin(t *testing.T) {
	// 准备错误
	err1 := errors.New("错误1")
	err2 := errors.New("错误2")
	
	// 测试Append
	appended := Append(err1, err2)
	errs := Errors(appended)
	require.Len(t, errs, 2, "追加错误应包含两个错误")
	
	// 测试Join（应与Append相同）
	joined := Join(err1, err2)
	errs = Errors(joined)
	require.Len(t, errs, 2, "连接错误应包含两个错误")
	
	// 测试nil错误的处理
	appended = Append(nil, err1, nil, err2)
	errs = Errors(appended)
	require.Len(t, errs, 2, "应忽略nil错误")
	
	// 测试空参数
	appended = Append()
	require.Nil(t, appended, "空追加应返回nil")
	
	joined = Join()
	require.Nil(t, joined, "空连接应返回nil")
}

// 测试Cause函数
func TestCause(t *testing.T) {
	// 准备错误
	baseErr := errors.New("基础错误")
	wrapped := Wrap(baseErr, "包装信息")
	
	// 测试获取原因
	cause := Cause(wrapped)
	require.Equal(t, baseErr.Error(), cause.Error(), "原因应为基础错误")
	
	// 测试nil错误
	nilCause := Cause(nil)
	require.Nil(t, nilCause, "nil错误的原因应为nil")
}

// 测试WithMessage和WithMessagef函数
func TestWithMessage(t *testing.T) {
	// 准备基础错误
	baseErr := errors.New("基础错误")
	
	// 测试WithMessage
	withMsg := WithMessage(baseErr, "附加信息")
	require.Contains(t, withMsg.Error(), "基础错误", "应包含基础错误")
	require.Contains(t, withMsg.Error(), "附加信息", "应包含附加信息")
	
	// 测试WithMessagef
	withMsgf := WithMessagef(baseErr, "格式化附加: %s", "参数")
	require.Contains(t, withMsgf.Error(), "基础错误", "应包含基础错误")
	require.Contains(t, withMsgf.Error(), "格式化附加: 参数", "应包含格式化的附加信息")
	
	// 测试nil错误
	nilWithMsg := WithMessage(nil, "附加到nil")
	require.Nil(t, nilWithMsg, "nil错误的WithMessage应返回nil")
	
	nilWithMsgf := WithMessagef(nil, "格式化附加到nil: %s", "参数")
	require.Nil(t, nilWithMsgf, "nil错误的WithMessagef应返回nil")
}

// 测试错误类型检查函数
func TestErrorTypeChecks(t *testing.T) {
	// 准备各种类型的错误
	noHostErr := errors.New("no such host")
	genericErr := errors.New("一般错误")
	
	// 测试IsNetworkTemporaryErr
	require.False(t, IsNetworkTemporaryErr(noHostErr), "找不到主机不应被识别为临时网络错误")
	
	// 测试IsNetworkPermanentErr
	require.True(t, IsNetworkPermanentErr(noHostErr), "找不到主机应被识别为永久网络错误")
	
	// 测试IsDeadlineErr
	require.False(t, IsDeadlineErr(genericErr), "一般错误不应被识别为截止时间错误")
	
	// 测试nil错误
	require.False(t, IsNetworkTemporaryErr(nil), "nil不应被识别为临时网络错误")
	require.False(t, IsNetworkPermanentErr(nil), "nil不应被识别为永久网络错误")
	require.False(t, IsDeadlineErr(nil), "nil不应被识别为截止时间错误")
}

// 测试With函数
func TestWith(t *testing.T) {
	// 准备基础错误
	baseErr := errors.New("基础错误")
	
	// 测试添加属性
	withAttr := With(baseErr, "key", "value")
	
	// 验证错误包含原始错误
	require.Contains(t, withAttr.Error(), "基础错误", "应包含基础错误")
	
	// 验证属性已添加
	attrs := GetAttr(withAttr)
	require.NotEmpty(t, attrs, "属性不应为空")
	require.Equal(t, "key", attrs[0].Key, "属性键应为'key'")
	require.Equal(t, "value", attrs[0].Value.String(), "属性值应为'value'")
	
	// 测试空属性
	withEmptyAttr := With(baseErr)
	require.Equal(t, baseErr.Error(), Cause(withEmptyAttr).Error(), "空属性应返回原始错误")
	
	// 测试nil错误
	nilWith := With(nil, "key", "value")
	require.Nil(t, nilWith, "nil错误的With应返回nil")
}

// 测试GetAttr函数
func TestGetAttr(t *testing.T) {
	// 准备带属性的错误
	err := New("测试错误", "key1", "value1", "key2", "value2")
	
	// 获取所有属性
	attrs := GetAttr(err)
	
	// 验证属性
	require.Len(t, attrs, 2, "应返回两个属性")
	require.Equal(t, "key1", attrs[0].Key, "第一个属性键应为'key1'")
	require.Equal(t, "value1", attrs[0].Value.String(), "第一个属性值应为'value1'")
	
	// 测试无属性的错误
	noAttrErr := errors.New("无属性错误")
	attrs = GetAttr(noAttrErr)
	require.Empty(t, attrs, "无属性错误应返回空属性列表")
	
	// 测试nil错误
	nilAttrs := GetAttr(nil)
	require.Nil(t, nilAttrs, "nil错误的GetAttr应返回nil")
}

// 测试ToSlogAttrs和ToSlogAttrGroup函数
func TestToSlogAttrs(t *testing.T) {
	// 准备错误
	baseErr := errors.New("基础错误")
	typedErr := New("网络错误").SetKind(ErrKindNetworkTemporary)
	
	// 测试ToSlogAttrs
	attrs := ToSlogAttrs(typedErr)
	require.NotEmpty(t, attrs, "属性不应为空")
	
	// 寻找kind属性
	var kindFound bool
	for _, attr := range attrs {
		if attr.Key == "kind" {
			kindFound = true
			require.Equal(t, ErrKindNetworkTemporary.String(), attr.Value.String(), "kind属性值应匹配")
			break
		}
	}
	require.True(t, kindFound, "应找到kind属性")
	
	// 测试ToSlogAttrGroup
	group := ToSlogAttrGroup(typedErr)
	require.Equal(t, "data", group.Key, "组键应为'data'")
	
	// 测试无类型的错误
	attrs = ToSlogAttrs(baseErr)
	require.NotEmpty(t, attrs, "即使无类型，属性也不应为空")
}

// 测试GetAttrValue函数
func TestGetAttrValue(t *testing.T) {
	// 准备带属性的错误
	err := New("测试错误", "key1", "value1", "key2", 42)
	
	// 获取存在的属性值
	value1 := GetAttrValue(err, "key1")
	require.Equal(t, "value1", value1.String(), "应返回正确的字符串值")
	
	value2 := GetAttrValue(err, "key2")
	require.Equal(t, "42", value2.String(), "应返回正确的数字值")
	
	// 获取不存在的属性值
	nonExistent := GetAttrValue(err, "nonexistent")
	require.Equal(t, slog.Value{}, nonExistent, "不存在的属性应返回空值")
	
	// 测试nil错误
	nilValue := GetAttrValue(nil, "key1")
	require.Equal(t, slog.Value{}, nilValue, "nil错误应返回空值")
} 

