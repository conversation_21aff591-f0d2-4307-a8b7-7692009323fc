// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-19 20:01:51
// FilePath: /yaml_scan/pkg/dnsx/libs/dnsx/dnsx_test.go
// Description: 
package dnsx

import (
	"testing"
	
	"github.com/stretchr/testify/require"
	"github.com/miekg/dns"
)

// TestNew 测试创建新的DNSX实例
func TestNew(t *testing.T) {
	// 测试使用默认选项创建DNSX实例
	options := DefaultOptions
	dnsx, err := New(options)
	
	// 断言创建成功且没有错误
	require.NoError(t, err, "应该能够成功创建DNSX实例")
	require.NotNil(t, dnsx, "DNSX实例不应为nil")
	require.NotNil(t, dnsx.dnsClient, "DNS客户端不应为nil")
	require.NotNil(t, dnsx.Options, "选项不应为nil")
	require.True(t, dnsx.dnsClient.TCPFallback, "TCP回退应该开启")
	
	// 测试自定义解析器
	customOptions := Options{
		BaseResolvers: []string{"udp:*******:53"},
		MaxRetries: 3,
		QuestionTypes: []uint16{dns.TypeA},
		TraceMaxRecursion: 10,
		Hostsfile: false,
	}
	
	customDnsx, err := New(customOptions)
	require.NoError(t, err, "应该能够使用自定义选项创建DNSX实例")
	require.NotNil(t, customDnsx, "自定义DNSX实例不应为nil")
	require.Equal(t, 1, len(customDnsx.Options.BaseResolvers), "应该有1个基础解析器")
	require.Equal(t, "udp:*******:53", customDnsx.Options.BaseResolvers[0], "解析器应该是指定的值")
	require.Equal(t, 3, customDnsx.Options.MaxRetries, "最大重试次数应为3")
}



// TestDNSXLookup 测试DNSX的Lookup方法
func TestDNSXLookup(t *testing.T) {
	// 注意：这是集成测试，需要网络连接
	// 如果需要进行单元测试，应该模拟DNS客户端
	
	// 创建DNSX实例
	dnsx, err := New(DefaultOptions)
	require.NoError(t, err, "创建DNSX实例不应有错误")
	
	// 测试IP地址输入
	ips, err := dnsx.Lookup("127.0.0.1")
	require.NoError(t, err, "查询IP地址不应有错误")
	require.Equal(t, []string{"127.0.0.1"}, ips, "IP地址查询应返回输入的IP")
	
	// 注意：以下测试需要网络连接，可能会失败
	// 如果想要可靠的单元测试，应该使用模拟
	// 测试域名查询
	ips, err = dnsx.Lookup("www.baidu.com")
	require.NoError(t, err, "baidu.com不应有错误")
	require.NotEmpty(t, ips, "baidu.com的IP地址不应为空")
	
	// 测试不存在的域名
	ips, err = dnsx.Lookup("this-domain-does-not-exist-abc123.com")
	require.Error(t, err, "查询不存在的域名应该返回错误")
	require.Empty(t, ips, "不存在域名的IP应该为空")

}

// TestQueryMultipleFilter 测试QueryMultiple方法的查询类型过滤功能
func TestQueryMultipleFilter(t *testing.T) {
	// 创建带有多种查询类型的DNSX实例
	options := DefaultOptions
	options.QuestionTypes = []uint16{dns.TypeA, dns.TypePTR, dns.TypeMX}
	options.QueryAll = true
	
	dnsx, err := New(options)
	require.NoError(t, err, "创建DNSX实例不应有错误")
	
	// 测试非IP地址输入时的过滤（应排除PTR）
	//hostname := "example.com"
	// 这里我们不实际执行查询，而是检查过滤后的查询类型
	filteredTypes := []uint16{}
	for _, qt := range dnsx.Options.QuestionTypes {
		if qt != dns.TypePTR {
			filteredTypes = append(filteredTypes, qt)
		}
	}
	
	// 检查IP地址输入时只使用PTR查询
	//ipAddr := "***********"
	dnsx2, err := New(options)
	require.NoError(t, err, "创建第二个DNSX实例不应有错误")
	
	// 如果是IP地址，则应只进行PTR查询
	require.Contains(t, dnsx2.Options.QuestionTypes, dns.TypePTR, "查询类型应包含PTR")
} 

