// Author: chenjb
// Version: V1.0
// Date: 2025-07-22 17:30:01
// FilePath: /yaml_scan/pkg/rawhttp/client/status.go
// Description: HTTP状态码定义和状态处理功能，包含所有标准HTTP状态码常量

// Package client HTTP状态码处理模块
package client

import "fmt" // 格式化输出包

// HTTP状态码常量定义
// 按照HTTP规范分类定义所有标准状态码
const (
	// 1xx 信息性状态码 - 表示请求已接收，继续处理
	INFO_CONTINUE           = 100 // 继续，客户端应继续发送请求
	INFO_SWITCHING_PROTOCOL = 101 // 切换协议，服务器正在切换协议
	INFO_PROCESSING         = 102 // 处理中，服务器正在处理请求

	// 2xx 成功状态码 - 表示请求已成功接收、理解和处理
	SUCCESS_OK                = 200 // 成功，请求已成功处理
	SUCCESS_CREATED           = 201 // 已创建，请求成功并创建了新资源
	SUCCESS_ACCEPTED          = 202 // 已接受，请求已接受但尚未处理完成
	SUCCESS_NON_AUTHORITATIVE = 203 // 非权威信息，返回的信息来自第三方
	SUCCESS_NO_CONTENT        = 204 // 无内容，请求成功但无返回内容
	SUCCESS_RESET_CONTENT     = 205 // 重置内容，请求成功，客户端应重置文档视图
	SUCCESS_PARTIAL_CONTENT   = 206 // 部分内容，服务器成功处理了部分GET请求
	SUCCESS_MULTI_STATUS      = 207 // 多状态，多个资源的状态信息

	// 3xx 重定向状态码 - 表示需要进一步操作以完成请求
	REDIRECTION_MULTIPLE_CHOICES   = 300 // 多种选择，请求的资源有多个可用表示
	REDIRECTION_MOVED_PERMANENTLY  = 301 // 永久移动，资源已永久移动到新位置
	REDIRECTION_MOVED_TEMPORARILY  = 302 // 临时移动，资源临时移动到新位置
	REDIRECTION_SEE_OTHER          = 303 // 查看其他，应使用GET方法访问其他URI
	REDIRECTION_NOT_MODIFIED       = 304 // 未修改，资源未修改，可使用缓存版本
	REDIRECTION_USE_PROXY          = 305 // 使用代理，必须通过指定的代理访问
	REDIRECTION_TEMPORARY_REDIRECT = 307 // 临时重定向，临时重定向到新位置

	// 4xx 客户端错误状态码 - 表示客户端请求有错误
	CLIENT_ERROR_BAD_REQUEST                     = 400 // 错误请求，请求语法错误
	CLIENT_ERROR_UNAUTHORIZED                    = 401 // 未授权，需要身份验证
	CLIENT_ERROR_PAYMENT_REQUIRED                = 402 // 需要付费，保留状态码
	CLIENT_ERROR_FORBIDDEN                       = 403 // 禁止访问，服务器拒绝请求
	CLIENT_ERROR_NOT_FOUND                       = 404 // 未找到，请求的资源不存在
	CLIENT_ERROR_METHOD_NOT_ALLOWED              = 405 // 方法不允许，请求方法不被允许
	CLIENT_ERROR_NOT_ACCEPTABLE                  = 406 // 不可接受，无法生成客户端可接受的内容
	CLIENT_ERROR_PROXY_AUTHENTIFICATION_REQUIRED = 407 // 需要代理身份验证
	CLIENT_ERROR_REQUEST_TIMEOUT                 = 408 // 请求超时，客户端请求超时
	CLIENT_ERROR_CONFLICT                        = 409 // 冲突，请求与资源当前状态冲突
	CLIENT_ERROR_GONE                            = 410 // 已删除，资源已被永久删除
	CLIENT_ERROR_LENGTH_REQUIRED                 = 411 // 需要长度，需要Content-Length头部
	CLIENT_ERROR_PRECONDITION_FAILED             = 412 // 前提条件失败，请求头中的前提条件失败
	CLIENT_ERROR_REQUEST_ENTITY_TOO_LARGE        = 413 // 请求实体过大，请求体过大
	CLIENT_ERROR_REQUEST_URI_TOO_LONG            = 414 // 请求URI过长，请求URI过长
	CLIENT_ERROR_UNSUPPORTED_MEDIA_TYPE          = 415 // 不支持的媒体类型
	CLIENT_ERROR_REQUESTED_RANGE_NOT_SATISFIABLE = 416 // 请求范围无法满足
	CLIENT_ERROR_EXPECTATION_FAILED              = 417 // 期望失败，Expect头部字段无法满足
	CLIENT_ERROR_UNPROCESSABLE_ENTITY            = 422 // 无法处理的实体，请求格式正确但语义错误
	CLIENT_ERROR_LOCKED                          = 423 // 已锁定，资源被锁定
	CLIENT_ERROR_FAILED_DEPENDENCY               = 424 // 依赖失败，请求依赖的操作失败

	// 5xx 服务器错误状态码 - 表示服务器在处理请求时发生错误
	SERVER_ERROR_INTERNAL                   = 500 // 内部服务器错误，服务器遇到意外情况
	SERVER_ERROR_NOT_IMPLEMENTED            = 501 // 未实现，服务器不支持请求的功能
	SERVER_ERROR_BAD_GATEWAY                = 502 // 错误网关，网关或代理服务器收到无效响应
	SERVER_ERROR_SERVICE_UNAVAILABLE        = 503 // 服务不可用，服务器暂时无法处理请求
	SERVER_ERROR_GATEWAY_TIMEOUT            = 504 // 网关超时，网关或代理服务器超时
	SERVER_ERROR_HTTP_VERSION_NOT_SUPPORTED = 505 // HTTP版本不支持，服务器不支持请求的HTTP版本
	SERVER_ERROR_INSUFFICIENT_STORAGE       = 507 // 存储空间不足，服务器无法存储请求
)

// Status 表示HTTP状态码
// 包含状态码数字和原因短语
type Status struct {
	Code   int    // 状态码数字（如200、404、500等）
	Reason string // 状态原因短语（如"OK"、"Not Found"、"Internal Server Error"等）
}

// String 返回状态的字符串表示
// 返回:
//   string: 格式为"状态码 原因短语"的字符串
// 功能: 实现fmt.Stringer接口，提供状态的可读表示
func (s Status) String() string {
	return fmt.Sprintf("%d %s", s.Code, s.Reason)
}

// IsInformational 检查是否为信息性状态码（1xx）
// 返回:
//   bool: 如果是1xx状态码则返回true，否则返回false
// 功能: 判断状态码是否在100-199范围内
func (s Status) IsInformational() bool {
	return s.Code >= INFO_CONTINUE && s.Code < SUCCESS_OK
}

// IsSuccess 检查是否为成功状态码（2xx）
// 返回:
//   bool: 如果是2xx状态码则返回true，否则返回false
// 功能: 判断状态码是否在200-299范围内
func (s Status) IsSuccess() bool {
	return s.Code >= SUCCESS_OK && s.Code < REDIRECTION_MULTIPLE_CHOICES
}

// IsRedirect 检查是否为重定向状态码（3xx）
// 返回:
//   bool: 如果是需要重定向的3xx状态码则返回true，否则返回false
// 功能: 判断状态码是否在300-399范围内，但排除304（未修改）
// 注意: 根据RFC 9110第15.4.5节，304响应在头部结束时终止，指向本地资源
//       收到304响应后不应发出进一步的请求
func (s Status) IsRedirect() bool {
	// Per RFC 9110 section 15.4.5 a 304 response is terminated by the end of the header section and it refers to a local resource.
	// No further requests are supposed to be issued after a 304 response is received.
	// 根据RFC 9110第15.4.5节，304响应在头部结束时终止，指向本地资源
	// 收到304响应后不应发出进一步的请求
	if s.Code == REDIRECTION_NOT_MODIFIED {
		return false // 304不被视为需要重定向的状态码
	}
	return s.Code >= REDIRECTION_MULTIPLE_CHOICES && s.Code < CLIENT_ERROR_BAD_REQUEST
}

// IsError 检查是否为错误状态码（4xx或5xx）
// 返回:
//   bool: 如果是4xx或5xx状态码则返回true，否则返回false
// 功能: 判断状态码是否在400及以上范围内
func (s Status) IsError() bool {
	return s.Code >= CLIENT_ERROR_BAD_REQUEST
}

// IsClientError 检查是否为客户端错误状态码（4xx）
// 返回:
//   bool: 如果是4xx状态码则返回true，否则返回false
// 功能: 判断状态码是否在400-499范围内
func (s Status) IsClientError() bool {
	return s.Code >= CLIENT_ERROR_BAD_REQUEST && s.Code < SERVER_ERROR_INTERNAL
}

// IsServerError 检查是否为服务器错误状态码（5xx）
// 返回:
//   bool: 如果是5xx状态码则返回true，否则返回false
// 功能: 判断状态码是否在500及以上范围内
func (s Status) IsServerError() bool {
	return s.Code >= SERVER_ERROR_INTERNAL
}

