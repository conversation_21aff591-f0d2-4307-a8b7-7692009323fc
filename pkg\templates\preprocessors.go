// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-17 15:27:52
// FilePath: /yaml_scan/pkg/templates/preprocessors.go
// Description: 
package templates


type Preprocessor interface {
	// Process processes the data and returns the processed data.
	ProcessNReturnData(data []byte) ([]byte, map[string]interface{})
	// Exists check if the preprocessor exists in the data.
	Exists(data []byte) bool
}


