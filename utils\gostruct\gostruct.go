package structutil

import (
	"bytes"
	"encoding/binary"
	"errors"
	"strconv"
	"strings"
)

// Endianess 表示字节序类型。
type Endianess int

const (
	BIG_ENDIAN Endianess = iota // 大端字节序
	LITTLE_ENDIAN // 小端字节序
)

// ByteOrder: 返回对应的 binary.ByteOrder 类型。
//  @receiver e Endianess: 
//  @return binary.ByteOrder binary.ByteOrder: 
func (e Endianess) ByteOrder() binary.ByteOrder {
	if e == BIG_ENDIAN {
		return binary.BigEndian
	} else {
		return binary.LittleEndian
	}
}

// CalcSize: 返回与给定格式对应的结构体（字节切片）的大小。
//
//	@param format []string: 包含格式符号，支持以下格式：
//	   - "b", "B": 字节，大小为 1 字节
//	   - "h", "H": 短整型，大小为 2 字节
//	   - "i", "I", "l", "L", "f": 整型和浮点型，大小为 4 字节
//	   - "q", "Q", "d": 长整型和双精度浮点型，大小为 8 字节
//	   - "?": 布尔值，大小为 1 字节
//	   - "sN": 字符串，大小为 N 字节，其中 N 是一个正整数
//	   - "<", ">": 大小端标志，当前未使用
//
//	@return int int: 计算得到的字节大小
//	@return error error:
func CalcSize(format []string) (int, error) {
	var size int

	for _, f := range format {
		switch f {
		case "<", ">":
		case "?":
			// 布尔值占用 1 字节
			size = size + 1
		case "b", "B":
			// 字节占用 1 字节
			size++
		case "h", "H":
			// 短整型占用 2 字节
			size = size + 2
		case "i", "I", "l", "L", "f":
			// 整型和浮点型占用 4 字节
			size = size + 4
		case "q", "Q", "d":
			// 长整型和双精度浮点型占用 8 字节
			size = size + 8
		default:
			//  处理字符串格式
			if strings.Contains(f, "s") {
				n, _ := strconv.Atoi(strings.TrimRight(f, "s"))
				size = size + n
			} else {
				return 0, errors.New("Unexpected format token: '" + f + "'")
			}
		}
	}

	return size, nil
}

// intToBytes: 将整数 n 转换为指定大小和字节序的字节切片。
//  @param n int: 要转换的整数。
//  @param size int: 字节大小，支持 1、2、4 或 8 字节。
//  @param endianess Endianess: 字节序，使用 Endianess 类型（大端或小端）。
//  @return []byte []byte: 返回转换后的字节切片，长度为 size。
func intToBytes(n int, size int, endianess Endianess) []byte {
	buf := bytes.NewBuffer([]byte{})

	switch size {
	case 1:
		// 写入 1 字节
		_ = binary.Write(buf, endianess.ByteOrder(), int8(n))
	case 2:
		// 写入 2 字节
		_ = binary.Write(buf, endianess.ByteOrder(), int16(n))
	case 4:
		// 写入 4 字节
		_ = binary.Write(buf, endianess.ByteOrder(), int32(n))
	default:
		// 默认写入 8 字节
		_ = binary.Write(buf, endianess.ByteOrder(), int64(n))
	}

	return buf.Bytes()[0:size]
}

// boolToBytes: 将布尔值转换为字节切片
//  @param x bool: 要转换的布尔值。
//  @param endianess Endianess:  字节序，使用 Endianess 类型（大端或小端）。
//  @return []byte []byte: 
func boolToBytes(x bool, endianess Endianess) []byte {
	if x {
		// 将 true 转换为字节 1
		return intToBytes(1, 1, endianess)
	}
	// 将 false 转换为字节 0
	return intToBytes(0, 1, endianess)
}

// bytesToInt: 将字节切片转换为整数，支持指定字节序。
//  @param b []byte: 要转换的字节切片。
//  @param endianess Endianess: 字节序，使用 Endianess 类型（大端或小端）。
//  @return int int: 
func bytesToInt(b []byte, endianess Endianess) int {
	buf := bytes.NewBuffer(b)

	switch len(b) {
	case 1:
		var x int8
		_ = binary.Read(buf, endianess.ByteOrder(), &x)
		return int(x)
	case 2:
		var x int16
		_ = binary.Read(buf, endianess.ByteOrder(), &x)
		return int(x)
	case 4:
		var x int32
		_ = binary.Read(buf, endianess.ByteOrder(), &x)
		return int(x)
	default:
		var x int64
		_ = binary.Read(buf, endianess.ByteOrder(), &x)
		return int(x)
	}
}

// bytesToBool: 将字节切片转换为布尔值，支持指定字节序。
//  @param b []byte: 要转换的字节切片。
//  @param endianess Endianess: 字节序，使用 Endianess 类型（大端或小端）。
//  @return bool bool: 
func bytesToBool(b []byte, endianess Endianess) bool {
	return bytesToInt(b, endianess) > 0
}

// bytesToFloat32: 将字节切片转换为 float32，支持指定字节序。
//  @param b []byte:  要转换的字节切片。
//  @param endianess Endianess:  字节序，使用 Endianess 类型（大端或小端）。
//  @return float32 float32: 
func bytesToFloat32(b []byte, endianess Endianess) float32 {
	var x float32
	buf := bytes.NewBuffer(b)
	_ = binary.Read(buf, endianess.ByteOrder(), &x)
	return x
}

// bytesToFloat32: 将字节切片转换为 float64，支持指定字节序。
//  @param b []byte:  要转换的字节切片。
//  @param endianess Endianess:  字节序，使用 Endianess 类型（大端或小端）。
//  @return float32 float32: 
func bytesToFloat64(b []byte, endianess Endianess) float64 {
	var x float64
	buf := bytes.NewBuffer(b)
	_ = binary.Read(buf, endianess.ByteOrder(), &x)
	return x
}

// bytesToUint:  将字节切片转换为无符号整数
//  @param b []byte: 要转换的字节切片。
//  @return int int: 
func bytesToUint(b []byte) int {
	buf := bytes.NewBuffer(b)

	switch len(b) {
	case 1:
		var x uint8
		_ = binary.Read(buf, binary.LittleEndian, &x)
		return int(x)
	case 2:
		var x uint16
		_ = binary.Read(buf, binary.LittleEndian, &x)
		return int(x)
	case 4:
		var x uint32
		_ = binary.Read(buf, binary.LittleEndian, &x)
		return int(x)
	default:
		var x uint64
		_ = binary.Read(buf, binary.LittleEndian, &x)
		return int(x)
	}
}

// UnPack: 根据给定的格式解包字节切片（假定由 Pack(format, msg) 打包）。
// 字节切片必须包含不小于格式所需的数据量（len(msg) 必须大于或等于 CalcSize(format)）
//  @param format []string: ，定义了解包的格式，支持多种数据类型（如整数、浮点数、布尔值和字符串）。
//  @param msg []byte:  要解包的字节切片，包含按格式打包的数据。
//  @return []interface{} []interface{}: 包含解包后的数据
//  @return error error: 
func UnPack(format []string, msg []byte) ([]interface{}, error) {
	 // 计算预期大小
	expected_size, err := CalcSize(format)

	// 默认字节序为小端
	endianess := LITTLE_ENDIAN

	if err != nil {
		return nil, err
	}

	if expected_size > len(msg) {
		return nil, errors.New("expected size is bigger than actual size of message")
	}

	res := []interface{}{}

	for _, f := range format {
		switch f {
		case "<":
			// 小端
			endianess = LITTLE_ENDIAN
		case ">":
			// 大端
			endianess = BIG_ENDIAN
		case "?":
			// 布尔值
			res = append(res, bytesToBool(msg[:1], endianess))
			msg = msg[1:]
		case "b":
			// 有符号 8 位整数
			res = append(res, bytesToInt(msg[:1], endianess))
			msg = msg[1:]
		case "B":
			// 无符号 8 位整数
			res = append(res, bytesToUint(msg[:1]))
			msg = msg[1:]
		case "h", "H":
			// 有符号 16 位整数
			res = append(res, bytesToInt(msg[:2], endianess))
			msg = msg[2:]
		case "i", "I", "l", "L":
			// 有符号 32 位整数
			res = append(res, bytesToInt(msg[:4], endianess))
			msg = msg[4:]
		case "q", "Q":
			// 有符号 64 位整数
			res = append(res, bytesToInt(msg[:8], endianess))
			msg = msg[8:]
		case "f":
			// 32 位浮点数
			res = append(res, bytesToFloat32(msg[:4], endianess))
			msg = msg[4:]
		case "d":
			// 64 位浮点数
			res = append(res, bytesToFloat64(msg[:8], endianess))
			msg = msg[8:]
		default:
			if strings.Contains(f, "s") {
				n, _ := strconv.Atoi(strings.TrimRight(f, "s"))
				res = append(res, string(msg[:n]))
				msg = msg[n:]
			} else {
				return nil, errors.New("Unexpected format token: '" + f + "'")
			}
		}
	}

	return res, nil
}
