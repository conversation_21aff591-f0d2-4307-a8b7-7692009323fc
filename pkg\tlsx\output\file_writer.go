// Author: chenjb
// Version: V1.0
// Date: 2025-06-23 20:09:07
// FilePath: /yaml_scan/pkg/tlsx/output/file_writer.go
// Description:文件输出写入器，提供高效的缓冲文件写入功能
package output

import (
	"bufio"
	"os"
)

// fileWriter 并发安全的基于文件的输出写入器
type fileWriter struct {
	file   *os.File      // 底层文件句柄，用于文件操作
	writer *bufio.Writer // 缓冲写入器，减少系统调用提高性能
}

// newFileOutputWriter 为指定文件创建新的缓冲写入器
// 该函数创建或覆盖指定文件，并初始化缓冲写入器
// @param file string: 目标文件路径，如果文件存在将被覆盖
// @return *fileWriter *fileWriter: 初始化完成的文件写入器实例
// @return error error:  文件创建过程中的错误，成功时为nil
func newFileOutputWriter(file string) (*fileWriter, error) {
	// 创建或覆盖目标文件
	output, err := os.Create(file)
	if err != nil {
		return nil, err
	}
	return &fileWriter{file: output, writer: bufio.NewWriter(output)}, nil
}

// Write 把数据写入到底层文件
// 该方法将字节数据写入缓冲区，并自动添加换行符
// @receiver w 
// @param data []byte: 要写入的字节数据
// @return error error: 
func (w *fileWriter) Write(data []byte) error {
	// 将数据写入缓冲区
	_, err := w.writer.Write(data)
	if err != nil {
		return err
	}
	// 自动添加换行符，确保每行数据独立
	_, err = w.writer.WriteRune('\n')
	return err
}

// Close 关闭底层写入器并将所有数据刷新到磁盘
// 该方法执行完整的关闭流程，确保数据完整性和资源释放
// @receiver w 
// @return error error: 
func (w *fileWriter) Close() error {
	w.writer.Flush()
	// 强制同步到磁盘，确保数据持久化
	w.file.Sync()
	return w.file.Close()
}
