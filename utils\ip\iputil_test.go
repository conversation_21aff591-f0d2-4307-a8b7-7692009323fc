//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-02 16:49:35
//FilePath: /yaml_scan/utils/ip/iputil_test.go
//Description:

package iputil

import (
	"net"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestIsCIDR 测试 IsCIDR 函数的正确性
func TestIsCIDR(t *testing.T) {
	// 定义测试用例
	tests := []struct {
		cidr     string // 输入的 CIDR 字符串
		expected bool   // 期望的结果
	}{
		{"***********/24", true},                              // 有效的 IPv4 CIDR
		{"10.0.0.0/8", true},                                  // 有效的 IPv4 CIDR
		{"**********/12", true},                               // 有效的 IPv4 CIDR
		{"0.0.0.0/0", true},                                   // 有效的 IPv4 CIDR
		{"***************/32", true},                          // 有效的 IPv4 CIDR
		{"2001:db8::/32", true},                               // 有效的 IPv6 CIDR
		{"::1/128", true},                                     // 有效的 IPv6 CIDR
		{"2001:0db8:85a3:0000:0000:8a2e:0370:7334/128", true}, // 有效的 IPv6 CIDR
		{"invalidCIDR", false},                                // 无效的 CIDR
		{"192.168.1.256/24", false},                           // 无效的 IPv4 CIDR（超出范围）
		{"***********/33", false},                             // 无效的 CIDR（位数超出范围）
		{"2001:db8::/129", false},                             // 无效的 IPv6 CIDR（位数超出范围）
	}

	// 遍历测试用例
	for _, test := range tests {
		result := IsCIDR(test.cidr) // 调用 IsCIDR 函数
		require.Equal(t, test.expected, result, "Expected %v for CIDR %q", test.expected, test.cidr)
	}
}

// TestIsIPv4 测试 IsIPv4 函数的正确性
func TestIsIPv4(t *testing.T) {
	// 定义测试用例
	tests := []struct {
		input    interface{} // 输入的 IP 地址
		expected bool        // 期望的结果
	}{
		{"***********", true},              // 有效的 IPv4 地址
		{"********", true},                 // 有效的 IPv4 地址
		{"***************", true},          // 有效的 IPv4 地址
		{"0.0.0.0", true},                  // 有效的 IPv4 地址
		{"invalidIP", false},               // 无效的 IP 地址
		{"256.256.256.256", false},         // 超出范围的 IPv4 地址
		{net.ParseIP("***********"), true}, // 有效的 net.IP 类型
		{net.ParseIP("::1"), false},        // 无效的 IPv6 地址
	}

	// 遍历测试用例
	for _, test := range tests {
		result := IsIPv4(test.input) // 调用 IsIPv4 函数
		require.Equal(t, test.expected, result, "Expected %v for input %v", test.expected, test.input)
	}
}

// TestIsIPv6 测试 IsIPv6 函数的正确性
func TestIsIPv6(t *testing.T) {
	// 定义测试用例
	tests := []struct {
		input    interface{} // 输入的 IP 地址
		expected bool        // 期望的结果
	}{
		{"2001:0db8:85a3:0000:0000:8a2e:0370:7334", true}, // 有效的 IPv6 地址
		{"::1", true},          // 有效的 IPv6 地址
		{"invalidIP", false},   // 无效的 IP 地址
		{"***********", false}, // 无效的 IPv4 地址
		{net.ParseIP("2001:0db8:85a3:0000:0000:8a2e:0370:7334"), true}, // 有效的 net.IP 类型
		{net.ParseIP("::1"), true},                                     // 有效的 net.IP 类型
		{net.ParseIP("***********"), false},                            // 无效的 net.IP 类型
	}

	// 遍历测试用例
	for _, test := range tests {
		result := IsIPv6(test.input) // 调用 IsIPv6 函数
		require.Equal(t, test.expected, result, "Expected %v for input %v", test.expected, test.input)
	}
}

func TestWhatsMyIP(t *testing.T) {
	// 调用 WhatsMyIP 函数
	_, err := WhatsMyIP()
	require.NoError(t, err, "Expected no error")
	// require.Equal(t, "*********", ip, "Expected IP address to match")
}

func TestIsIP(t *testing.T) {
	// 测试有效的 IPv4 地址
	require.True(t, IsIP("***********"))
	require.True(t, IsIP("***************"))
	require.True(t, IsIP("0.0.0.0"))

	// 测试有效的 IPv6 地址
	require.True(t, IsIP("::1"))                                     // Loopback address
	require.True(t, IsIP("2001:0db8:85a3:0000:0000:8a2e:0370:7334")) // Example IPv6 address
	require.True(t, IsIP("fe80::1ff:fe23:4567:890a"))                // Link-local address

	// 测试无效的 IP 地址
	require.False(t, IsIP("192.168.1.256"))       // 超出范围的 IPv4 地址
	require.False(t, IsIP("256.256.256.256"))     // 超出范围的 IPv4 地址
	require.False(t, IsIP("invalid_ip"))          // 无效的字符串
	require.False(t, IsIP("::g"))                 // 无效的 IPv6 地址
	require.False(t, IsIP(""))                    // 空字符串
	require.False(t, IsIP("1234.1234.1234.1234")) // 无效的 IPv4 地址
}
