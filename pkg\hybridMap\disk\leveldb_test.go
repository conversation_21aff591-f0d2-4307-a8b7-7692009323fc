// Author: chenjb
// Version: V1.0
// Date: 2025-05-16 11:52:30
// FilePath: /yaml_scan/pkg/hybridMap/disk/leveldb_test.go
// Description:
package disk

import (
	"os"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestLevelDBBasicOperations 测试 LevelDB 的基本操作，包括添加、获取、删除键值对
func TestLevelDBBasicOperations(t *testing.T) {
	// 创建临时测试目录
	tempDir, err := os.MkdirTemp("", "leveldb-test")
	require.NoError(t, err, "创建临时测试目录失败")
	defer os.RemoveAll(tempDir) // 测试结束后清理

	// 创建并打开 LevelDB 实例
	db, err := OpenLevelDB(tempDir)
	require.NoError(t, err, "OpenLevelDB 应该成功")
	defer db.Close()

	// 1. 测试设置和获取键值对
	testKey := "test-key"
	testValue := []byte("test-value")

	// 1.1 设置键值对
	err = db.Set(testKey, testValue, 0) // 永不过期
	require.NoError(t, err, "设置键值对应该成功")

	// 1.2 获取键值对
	value, err := db.Get(testKey)
	require.NoError(t, err, "获取存在的键应该成功")
	require.Equal(t, testValue, value, "获取的值应该与设置的值相同")

	// 1.3 获取不存在的键
	_, err = db.Get("non-existent-key")
	require.Error(t, err, "获取不存在的键应该返回错误")

	// 2. 测试删除键
	err = db.Del(testKey)
	require.NoError(t, err, "删除键应该成功")

	// 2.1 验证删除后无法获取
	_, err = db.Get(testKey)
	require.Error(t, err, "获取已删除的键应该返回错误")

	// 3. 测试批量操作
	batchData := map[string][]byte{
		"key1": []byte("value1"),
		"key2": []byte("value2"),
		"key3": []byte("value3"),
	}

	// 3.1 批量设置
	err = db.MSet(batchData)
	require.NoError(t, err, "批量设置键值对应该成功")

	// 3.2 批量获取
	values := db.MGet([]string{"key1", "key2", "key3", "nonexistent-key"})
	require.Len(t, values, 4, "应该返回 4 个结果")
	require.Equal(t, []byte("value1"), values[0], "第一个值应该是 value1")
	require.Equal(t, []byte("value2"), values[1], "第二个值应该是 value2")
	require.Equal(t, []byte("value3"), values[2], "第三个值应该是 value3")
	require.Empty(t, values[3], "第四个值应该是空字节数组")

	// 3.3 批量删除
	err = db.MDel([]string{"key1", "key2"})
	require.NoError(t, err, "批量删除应该成功")

	// 3.4 验证批量删除结果
	_, err = db.Get("key1")
	require.Error(t, err, "key1 应该已被删除")
	_, err = db.Get("key2")
	require.Error(t, err, "key2 应该已被删除")
	val, err := db.Get("key3")
	require.NoError(t, err, "key3 应该还存在")
	require.Equal(t, []byte("value3"), val, "key3 的值应该保持不变")
}


// TestLevelDBExpiration 测试 LevelDB 的键过期功能
// 本测试用例验证键值对的过期以及 TTL 功能
func TestLevelDBExpiration(t *testing.T) {
	// 创建临时测试目录
	tempDir, err := os.MkdirTemp("", "leveldb-expiration-test")
	require.NoError(t, err, "创建临时测试目录失败")
	defer os.RemoveAll(tempDir) // 测试结束后清理

	// 创建并打开 LevelDB 实例
	db, err := OpenLevelDB(tempDir)
	require.NoError(t, err, "OpenLevelDB 应该成功")
	defer db.Close()

	// 1. 测试永不过期的键
	err = db.Set("permanent-key", []byte("permanent-value"), 0)
	require.NoError(t, err, "设置永久键应该成功")

	ttl := db.TTL("permanent-key")
	require.Equal(t, int64(-1), ttl, "永久键的 TTL 应该是 -1")

	// 2. 测试过期的键
	// 2.1 设置一个 1 秒过期的键
	err = db.Set("expiring-key", []byte("expiring-value"), 1*time.Second)
	require.NoError(t, err, "设置过期键应该成功")

	// 2.2 验证 TTL 值
	ttl = db.TTL("expiring-key")
	require.True(t, ttl > 0 && ttl <= 1, "过期键的 TTL 应该是正数且不超过 1")

	// 2.3 等待键过期
	time.Sleep(1100 * time.Millisecond) // 等待超过 1 秒

	// 2.4 验证键已经过期
	_, err = db.Get("expiring-key")
	require.Error(t, err, "获取过期的键应该返回错误")

	// 2.5 验证过期键的 TTL
	ttl = db.TTL("expiring-key")
	require.Equal(t, int64(-2), ttl, "过期键的 TTL 应该是 -2")
}

// TestLevelDBIncr 测试 LevelDB 的自增操作功能
func TestLevelDBIncr(t *testing.T) {
	// 创建临时测试目录
	tempDir, err := os.MkdirTemp("", "leveldb-incr-test")
	require.NoError(t, err, "创建临时测试目录失败")
	defer os.RemoveAll(tempDir) // 测试结束后清理

	// 创建并打开 LevelDB 实例
	db, err := OpenLevelDB(tempDir)
	require.NoError(t, err, "OpenLevelDB 应该成功")
	defer db.Close()

	// 1. 测试对不存在的键进行自增
	result, err := db.Incr("counter", 5)
	require.NoError(t, err, "对不存在的键进行自增应该成功")
	require.Equal(t, int64(5), result, "初始自增结果应该为 5")

	// 2. 验证值已正确存储
	value, err := db.Get("counter")
	require.NoError(t, err, "获取计数器应该成功")
	require.Equal(t, "5", string(value), "存储的计数器值应该是 '5'")

	// 3. 再次对同一个键进行自增
	result, err = db.Incr("counter", 3)
	require.NoError(t, err, "自增已存在的计数器应该成功")
	require.Equal(t, int64(8), result, "第二次自增后结果应该为 8")

	// 4. 验证更新后的值
	value, err = db.Get("counter")
	require.NoError(t, err, "获取更新后的计数器应该成功")
	require.Equal(t, "8", string(value), "存储的计数器值应该更新为 '8'")

	// 5. 测试负值自增
	result, err = db.Incr("counter", -10)
	require.NoError(t, err, "负值自增应该成功")
	require.Equal(t, int64(-2), result, "负值自增后结果应该为 -2")
}

// TestLevelDBScan 测试 LevelDB 的扫描功能
func TestLevelDBScan(t *testing.T) {
	// 创建临时测试目录
	tempDir, err := os.MkdirTemp("", "leveldb-scan-test")
	require.NoError(t, err, "创建临时测试目录失败")
	defer os.RemoveAll(tempDir) // 测试结束后清理

	// 创建并打开 LevelDB 实例
	db, err := OpenLevelDB(tempDir)
	require.NoError(t, err, "OpenLevelDB 应该成功")
	defer db.Close()

	// 1. 准备测试数据
	testData := map[string][]byte{
		"user:1": []byte("Alice"),
		"user:2": []byte("Bob"),
		"user:3": []byte("Charlie"),
		"post:1": []byte("Hello World"),
		"post:2": []byte("Testing LevelDB"),
		"post:3": []byte("Scan Feature"),
	}
	for k, v := range testData {
		err = db.Set(k, v, 0)
		require.NoError(t, err, "设置测试数据应该成功")
	}

	// 2. 测试前缀扫描
	userKeys := make([]string, 0)
	userValues := make([]string, 0)

	err = db.Scan(ScannerOptions{
		Prefix:      "user",
		FetchValues: true,
		Handler: func(k []byte, v []byte) error {
			t.Logf("Scanning key: %s, value: %s", k, v) // 调试日志
			userKeys = append(userKeys, string(k))
			userValues = append(userValues, string(v))
			return nil
		},
	})

	require.NoError(t, err, "前缀扫描应该成功")
	require.Len(t, userKeys, 3, "应该有 3 个用户键")
	require.Contains(t, userKeys, "user:1", "应包含 user:1")
	require.Contains(t, userKeys, "user:2", "应包含 user:2")
	require.Contains(t, userKeys, "user:3", "应包含 user:3")
	require.Contains(t, userValues, "Alice", "应包含 Alice")
	require.Contains(t, userValues, "Bob", "应包含 Bob")
	require.Contains(t, userValues, "Charlie", "应包含 Charlie")

	// 3. 测试带偏移量的扫描
	postKeys := make([]string, 0)
	postValues := make([]string, 0)

	err = db.Scan(ScannerOptions{
		Offset:        "post:2",
		IncludeOffset: true,
		Prefix:        "post:",
		FetchValues:   true,
		Handler: func(k []byte, v []byte) error {
			postKeys = append(postKeys, string(k))
			postValues = append(postValues, string(v))
			return nil
		},
	})

	require.NoError(t, err, "带偏移量的扫描应该成功")
	require.Len(t, postKeys, 2, "应该有 2 个带偏移量的 post 键")
	require.Contains(t, postKeys, "post:2", "应包含 post:2 (偏移量)")
	require.Contains(t, postKeys, "post:3", "应包含 post:3")
	require.NotContains(t, postKeys, "post:1", "不应包含 post:1 (在偏移量之前)")

	// 4. 测试不包含偏移量的扫描
	postKeys = make([]string, 0)

	err = db.Scan(ScannerOptions{
		Offset:        "post:2",
		IncludeOffset: false,
		Prefix:        "post:",
		FetchValues:   false, // 只获取键
		Handler: func(k []byte, v []byte) error {
			postKeys = append(postKeys, string(k))
			return nil
		},
	})

	require.NoError(t, err, "不包含偏移量的扫描应该成功")
	require.Len(t, postKeys, 1, "应该只有 1 个 post 键")
	require.Contains(t, postKeys, "post:3", "应包含 post:3")
	require.NotContains(t, postKeys, "post:2", "不应包含偏移量 post:2")
}

// TestLevelDBSize 测试 LevelDB 的大小计算功能
func TestLevelDBSize(t *testing.T) {
	// 创建临时测试目录
	tempDir, err := os.MkdirTemp("", "leveldb-size-test")
	require.NoError(t, err, "创建临时测试目录失败")
	defer os.RemoveAll(tempDir) // 测试结束后清理

	// 创建并打开 LevelDB 实例
	db, err := OpenLevelDB(tempDir)
	require.NoError(t, err, "OpenLevelDB 应该成功")
	defer db.Close()

	// 1. 检查空数据库的大小
	initialSize := db.Size()

	// 2. 添加数据
	for i := 0; i < 100; i++ {
		key := "key_" + strconv.Itoa(i)
		value := []byte("This is test data for size calculation: " + strconv.Itoa(i))
		err = db.Set(key, value, 0)
		require.NoError(t, err, "设置测试数据应该成功")
	}

	// 3. 运行垃圾回收
	err = db.GC()
	require.NoError(t, err, "GC 应该成功")

	// 4. 检查数据添加后的大小
	newSize := db.Size()

	// 验证数据库大小已增加
	// 注意: 这里使用 -1 来表示可能无法获取大小信息的情况
	if initialSize != -1 && newSize != -1 {
		require.True(t, newSize > initialSize, "数据库大小应该在添加数据后增加")
	}
} 
