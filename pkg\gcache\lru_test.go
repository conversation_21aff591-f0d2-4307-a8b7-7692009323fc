//
// Author: chenjb
// Version: V1.0
// Date: 2025-05-10 11:11:06
// FilePath: /yaml_scan/pkg/gcache/lru_test.go
// Description:

package gcache

import (
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestLRUCacheBasicOperations 测试 LRUCache 的基本操作
func TestLRUCacheBasicOperations(t *testing.T) {
	cache := New[string, int](2).LRU().Build() // 创建容量为 2 的 LRU 缓存

	// 测试 Set 和 Get
	err := cache.Set("key1", 100)   // 设置键值对
	require.NoError(t, err)         // 确保无错误
	value, err := cache.Get("key1") // 获取值
	require.NoError(t, err)         // 确保无错误
	require.Equal(t, 100, value)    // 验证值正确

	// 测试 GetIFPresent
	value, err = cache.GetIFPresent("key1")   // 获取存在的键
	require.NoError(t, err)                   // 确保无错误
	require.Equal(t, 100, value)              // 验证值正确
	_, err = cache.GetIFPresent("key2")       // 获取不存在的键
	require.ErrorIs(t, err, KeyNotFoundError) // 验证返回 KeyNotFoundError

	// 测试 Remove
	removed := cache.Remove("key1")           // 删除键
	require.True(t, removed)                  // 验证删除成功
	_, err = cache.GetIFPresent("key1")       // 获取已删除的键
	require.ErrorIs(t, err, KeyNotFoundError) // 验证返回 KeyNotFoundError

	// 测试 Purge
	cache.Set("key2", 200)                // 设置键值对
	cache.Purge()                         // 清空缓存
	require.Equal(t, 0, cache.Len(false)) // 验证缓存为空
}

// TestLRUCacheEviction 测试 LRUCache 的驱逐机制
func TestLRUCacheEviction(t *testing.T) {
	cache := New[string, int](2).LRU().Build() // 创建容量为 2 的 LRU 缓存

	// 填充缓存
	err := cache.Set("key1", 100) // 设置键值对
	require.NoError(t, err)       // 确保无错误
	err = cache.Set("key2", 200)  // 设置键值对
	require.NoError(t, err)       // 确保无错误

	// 触发驱逐
	err = cache.Set("key3", 300)              // 设置新键值对，触发驱逐
	require.NoError(t, err)                   // 确保无错误
	_, err = cache.GetIFPresent("key1")       // 获取 key1（应已被驱逐）
	require.ErrorIs(t, err, KeyNotFoundError) // 验证 key1 已被驱逐
	value, err := cache.Get("key2")           // 获取 key2（应保留）
	require.NoError(t, err)                   // 确保无错误
	require.Equal(t, 200, value)              // 验证值正确
}

// TestLRUCacheExpiration 测试 LRUCache 的过期功能
func TestLRUCacheExpiration(t *testing.T) {
	fakeClock := NewFakeClock()                                 // 创建模拟时钟
	cache := New[string, int](2).LRU().Clock(fakeClock).Build() // 使用模拟时钟创建缓存

	// 测试 SetWithExpire
	err := cache.SetWithExpire("key1", 100, 1*time.Second) // 设置带 1 秒过期时间的键值对
	require.NoError(t, err)                                // 确保无错误
	value, err := cache.Get("key1")                        // 获取值
	require.NoError(t, err)                                // 确保无错误
	require.Equal(t, 100, value)                           // 验证值正确

	// 推进时间使条目过期
	fakeClock.Advance(10 * time.Second)       // 推进 10 秒
	_, err = cache.GetIFPresent("key1")       // 获取已过期键
	require.ErrorIs(t, err, KeyNotFoundError) // 验证返回 KeyNotFoundError
}

// TestLRUCacheLoader 测试 LRUCache 的 LoaderFunc
func TestLRUCacheLoader(t *testing.T) {
	loaderCalled := false
	cache := New[string, int](2).LRU().LoaderExpireFunc(
		func(k string) (int, *time.Duration, error) {
			loaderCalled = true
			expiration := 1 * time.Second
			return 200, &expiration, nil
		}).Build()

	// 测试 Get 触发 LoaderFunc
	value, err := cache.Get("key1") // 获取不存在的键，触发加载
	require.NoError(t, err)         // 确保无错误
	require.True(t, loaderCalled)   // 验证 LoaderFunc 被调用
	require.Equal(t, 200, value)    // 验证加载的值正确

	// 测试 GetIFPresent 不触发 LoaderFunc
	loaderCalled = false
	_, err = cache.GetIFPresent("key2")       // 获取不存在的键
	require.ErrorIs(t, err, KeyNotFoundError) // 验证返回 KeyNotFoundError
	require.False(t, loaderCalled)            // 验证 LoaderFunc 未被调用
}

// TestLRUCacheCallbacks 测试 LRUCache 的回调函数
func TestLRUCacheCallbacks(t *testing.T) {
	addedCalled := false
	evictedCalled := false
	purgeVisitorCalled := false

	cache := New[string, int](1).LRU(). // 容量为 1 以触发驱逐
						AddedFunc(func(k string, v int) {
			addedCalled = true
		}).
		EvictedFunc(func(k string, v int) {
			evictedCalled = true
		}).
		PurgeVisitorFunc(func(k string, v int) {
			purgeVisitorCalled = true
		}).Build()

	// 测试 AddedFunc
	err := cache.Set("key1", 100) // 设置键值对
	require.NoError(t, err)       // 确保无错误
	require.True(t, addedCalled)  // 验证 AddedFunc 被调用

	// 测试 EvictedFunc
	err = cache.Set("key2", 200)   // 设置新键值对，触发驱逐
	require.NoError(t, err)        // 确保无错误
	require.True(t, evictedCalled) // 验证 EvictedFunc 被调用

	// 测试 PurgeVisitorFunc
	cache.Purge()                       // 清空缓存
	require.True(t, purgeVisitorCalled) // 验证 PurgeVisitorFunc 被调用
}

// TestLRUCacheAutoLease 测试 LRUCache 的自动续租功能
func TestLRUCacheAutoLease(t *testing.T) {
	fakeClock := NewFakeClock() // 创建模拟时钟
	lease := 2 * time.Second    // 租约时间为 2 秒
	cache := New[string, int](2).LRU().
		Clock(fakeClock).
		Lease(lease).
		Build()

	// 设置带过期时间的键值对
	err := cache.SetWithExpire("key1", 100, 1*time.Second) // 过期时间为 1 秒
	require.NoError(t, err)                                // 确保无错误

	// 获取值，触发自动续租
	_, err = cache.Get("key1") // 获取值
	require.NoError(t, err)    // 确保无错误

	// 推进 1.5 秒（原过期时间已过，但续租后应未过期）
	fakeClock.Advance(1500 * time.Millisecond)
	_, err = cache.GetIFPresent("key1") // 获取值
	require.NoError(t, err)             // 确保未过期

	// 推进 10 秒（续租时间已过）
	fakeClock.Advance(10 * time.Second)
	_, err = cache.GetIFPresent("key1")       // 获取值
	require.ErrorIs(t, err, KeyNotFoundError) // 验证已过期
}

// TestLRUCacheConcurrency 测试 LRUCache 的并发安全性
func TestLRUCacheConcurrency(t *testing.T) {
	cache := New[string, int](100).LRU().Build() // 创建缓存实例
	var wg sync.WaitGroup

	// 并发写入
	for i := 0; i < 100; i++ {
		wg.Add(1)
		go func(i int) {
			defer wg.Done()
			key := "key" + string(rune(i))
			err := cache.Set(key, i) // 并发设置键值对
			require.NoError(t, err)  // 确保无错误
		}(i)
	}
	wg.Wait()

	// 验证写入结果
	require.Equal(t, 100, cache.Len(false)) // 验证缓存包含 100 个条目

	// 并发读取
	for i := 0; i < 100; i++ {
		wg.Add(1)
		go func(i int) {
			defer wg.Done()
			key := "key" + string(rune(i))
			value, err := cache.GetIFPresent(key) // 并发读取
			require.NoError(t, err)               // 确保无错误
			require.Equal(t, i, value)            // 验证值正确
		}(i)
	}
	wg.Wait()
}
