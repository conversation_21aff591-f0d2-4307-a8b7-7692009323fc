// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-24 14:51:51
// FilePath: /yaml_scan/pkg/tlsx/output/stats/stats.go
// Description: TLS连接统计数据收集和管理

package stats

import "sync/atomic"

var (
	// cryptoTLSConnections 记录使用标准crypto/tls库建立的连接数量
	// 这是Go标准库实现的TLS连接，适用于大多数现代TLS服务器
	cryptoTLSConnections uint64

	// zcryptoTLSConnections 记录使用zcrypto/tls库建立的连接数量
	// zcrypto是ZMap项目的TLS实现，提供了更多的自定义选项和更广泛的兼容性
	zcryptoTLSConnections uint64

	// opensslTLSConnections 记录使用OpenSSL建立的连接数量
	// OpenSSL是一个广泛使用的跨平台加密库，通过命令行工具集成
	opensslTLSConnections uint64
)

// IncrementCryptoTLSConnections  增加标准crypto/tls连接计数
func IncrementCryptoTLSConnections() {
	atomic.AddUint64(&cryptoTLSConnections, 1)
}

// IncrementZcryptoTLSConnections 增加zcrypto/tls连接计数
func IncrementZcryptoTLSConnections() {
	atomic.AddUint64(&zcryptoTLSConnections, 1)
}

// IncrementOpensslTLSConnections i增加OpenSSL连接计数
func IncrementOpensslTLSConnections() {
	atomic.AddUint64(&opensslTLSConnections, 1)
}

// LoadCryptoTLSConnections 获取标准crypto/tls连接计数
func LoadCryptoTLSConnections() uint64 {
	return atomic.LoadUint64(&cryptoTLSConnections)
}

// LoadZcryptoTLSConnections 获取zcrypto/tls连接计数
func LoadZcryptoTLSConnections() uint64 {
	return atomic.LoadUint64(&zcryptoTLSConnections)
}

// LoadOpensslTLSConnections 获取OpenSSL连接计数
func LoadOpensslTLSConnections() uint64 {
	return atomic.LoadUint64(&opensslTLSConnections)
}