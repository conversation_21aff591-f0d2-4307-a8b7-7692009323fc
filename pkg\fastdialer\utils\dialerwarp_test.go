// 
// Author: chenjb
// Version: V1.0
// Date: 2025-05-27 15:59:33
// FilePath: /yaml_scan/pkg/fastdialer/utils/dialerwarp_test.go
// Description: 
package utils

import (
	"context"
	"net"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// setupTestServer 设置测试TCP服务器
// 返回监听器和服务器地址
func setupTestServer(t *testing.T) (net.Listener, string) {
	listener, err := net.Listen("tcp", "127.0.0.1:0")
	require.NoError(t, err, "创建测试TCP服务器失败")
	
	// 启动接受连接的goroutine
	go func() {
		for {
			conn, err := listener.Accept()
			if err != nil {
				return // 监听器已关闭
			}
			// 简单地关闭连接，这对测试已足够
			conn.Close()
		}
	}()
	
	return listener, listener.Addr().String()
}

// TestNewDialWrap 测试创建新的拨号包装器
func TestNewDialWrap(t *testing.T) {
	// 创建基本拨号器
	dialer := &net.Dialer{
		Timeout: 5 * time.Second,
	}
	
	// 测试创建拨号包装器，使用有效的IP
	dw, err := NewDialWrap(dialer, []string{"127.0.0.1", "::1"}, "tcp", "example.com", "80")
	require.NoError(t, err, "使用有效IP创建DialWrap应该成功")
	require.NotNil(t, dw, "返回的DialWrap不应为nil")
	
	// 验证IPv4和IPv6地址是否正确分离
	require.Len(t, dw.ipv4, 1, "应有1个IPv4地址")
	require.Len(t, dw.ipv6, 1, "应有1个IPv6地址")
	require.Len(t, dw.ips, 2, "总共应有2个IP地址")
	
	// 测试创建拨号包装器，使用无效的IP
	_, err = NewDialWrap(dialer, []string{"invalid-ip"}, "tcp", "example.com", "80")
	require.Error(t, err, "使用无效IP创建DialWrap应该失败")
	require.Equal(t, ErrNoIPs, err, "错误应该是ErrNoIPs")
	
	// 测试创建拨号包装器，使用空IP列表
	_, err = NewDialWrap(dialer, []string{}, "tcp", "example.com", "80")
	require.Error(t, err, "使用空IP列表创建DialWrap应该失败")
	require.Equal(t, ErrNoIPs, err, "错误应该是ErrNoIPs")
}

// TestAddress 测试获取地址功能
func TestAddress(t *testing.T) {
	// 创建基本拨号器
	dialer := &net.Dialer{
		Timeout: 5 * time.Second,
	}
	
	// 创建拨号包装器
	dw, err := NewDialWrap(dialer, []string{"***********", "********"}, "tcp", "example.com", "8080")
	require.NoError(t, err, "创建DialWrap应该成功")
	
	// 测试Address方法
	ip, port := dw.Address()
	require.Equal(t, "***********", ip, "应返回第一个IP地址")
	require.Equal(t, "8080", port, "应返回正确的端口")
	
	// 测试没有IP时的情况
	emptyDw := &DialWrap{
		ips:  []net.IP{},
		port: "8080",
	}
	ip, port = emptyDw.Address()
	require.Equal(t, "", ip, "没有IP时应返回空字符串")
	require.Equal(t, "", port, "没有IP时应返回空字符串")
}

// TestSetFirstConnectionDuration 测试设置第一个连接时间
func TestSetFirstConnectionDuration(t *testing.T) {
	// 创建基本拨号器
	dialer := &net.Dialer{
		Timeout: 5 * time.Second,
	}
	
	// 创建拨号包装器
	dw, err := NewDialWrap(dialer, []string{"127.0.0.1"}, "tcp", "example.com", "80")
	require.NoError(t, err, "创建DialWrap应该成功")
	
	// 验证初始值
	require.Equal(t, time.Duration(0), dw.FirstConnectionTook(), "初始连接时间应为0")
	
	// 设置值并验证
	testDuration := 2 * time.Second
	dw.SetFirstConnectionDuration(testDuration)
	require.Equal(t, testDuration, dw.FirstConnectionTook(), "应返回设置的连接时间")
}

// TestDialContext 测试拨号上下文功能
func TestDialContext(t *testing.T) {
	// 设置测试服务器
	listener, addr := setupTestServer(t)
	defer listener.Close()
	
	// 解析地址获取主机和端口
	host, port, err := net.SplitHostPort(addr)
	require.NoError(t, err, "解析地址失败")
	
	// 创建基本拨号器
	dialer := &net.Dialer{
		Timeout: 5 * time.Second,
	}
	
	// 创建拨号包装器
	dw, err := NewDialWrap(dialer, []string{host}, "tcp", host, port)
	require.NoError(t, err, "创建DialWrap应该成功")
	
	// 测试拨号
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	conn, err := dw.DialContext(ctx, "", "")
	require.NoError(t, err, "拨号到测试服务器应该成功")
	require.NotNil(t, conn, "连接不应为nil")
	defer conn.Close()
	
	// 测试已取消的上下文
	canceledCtx, cancelFunc := context.WithCancel(context.Background())
	cancelFunc() // 立即取消
	
	_, err = dw.DialContext(canceledCtx, "", "")
	require.Error(t, err, "使用已取消的上下文拨号应该失败")
	
	// 测试不存在的服务器
	invalidDw, err := NewDialWrap(dialer, []string{"127.0.0.1"}, "tcp", "127.0.0.1", "12345")
	require.NoError(t, err, "创建无效目标的DialWrap应该成功")
	
	_, err = invalidDw.DialContext(ctx, "", "")
	require.Error(t, err, "拨号到不存在的服务器应该失败")
} 

