//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-11 17:12:28
//FilePath: /yaml_scan/pkg/input/formats/burp/burpxml_test.go
//Description: burpxml单测

package burp

import (
	"encoding/base64"
	"strings"
	"testing"
)

// 测试用例结构体
type testCase struct {
	name      string
	input     string
	decode    bool
	expected  Items
	expectErr bool
}

func TestParseXml(t *testing.T) {
	// 定义测试用例
	testCases := []testCase{
		{
			name: "正常解析 XML",
			input: `<items>
						<item>
							<time>2024-11-11T15:45:19Z</time>
							<url>http://example.com</url>
							<host ip="***********">example.com</host>
							<port>80</port>
							<protocol>HTTP</protocol>
							<path>/path</path>
							<request base64="false">GET /path HTTP/1.1</request>
							<status>200</status>
							<responselength>1234</responselength>
							<mimetype>text/html</mimetype>
							<response base64="false">HTTP/1.1 200 OK</response>
						</item>
					</items>`,
			decode: false,
			expected: Items{
				Items: []Item{
					{
						Time:           "2024-11-11T15:45:19Z",
						Url:            "http://example.com",
						Host:           Host{Ip: "***********", Name: "example.com"},
						Port:           "80",
						Protocol:       "HTTP",
						Path:           "/path",
						Request:        Request{Base64: "false", Raw: "GET /path HTTP/1.1"},
						Status:         "200",
						ResponseLength: "1234",
						MimeType:       "text/html",
						Response:       Response{Base64: "false", Raw: "HTTP/1.1 200 OK"},
					},
				},
			},
			expectErr: false,
		},
		{
			name: "Base64 编码请求和响应",
			input: `<items>
						<item>
							<time>2024-11-11T15:45:19Z</time>
							<url>http://example.com</url>
							<host ip="***********">example.com</host>
							<port>80</port>
							<protocol>HTTP</protocol>
							<path>/path</path>
							<request base64="true">` + base64.StdEncoding.EncodeToString([]byte("GET /path")) + `</request>
							<status>200</status>
							<responselength>1234</responselength>
							<mimetype>text/html</mimetype>
							<response base64="true">SFRUUC8xLjE=</response>
						</item>
					</items>`,
			decode: true,
			expected: Items{
				Items: []Item{
					{
						Time:           "2024-11-11T15:45:19Z",
						Url:            "http://example.com",
						Host:           Host{Ip: "***********", Name: "example.com"},
						Port:           "80",
						Protocol:       "HTTP",
						Path:           "/path",
						Request:        Request{Base64: "true", Raw: base64.StdEncoding.EncodeToString([]byte("GET /path")), Body: "GET /path"},
						Status:         "200",
						ResponseLength: "1234",
						MimeType:       "text/html",
						Response:       Response{Base64: "true", Raw: "SFRUUC8xLjE=", Body: "HTTP/1.1"},
					},
				},
			},
			expectErr: false,
		},
		{
			name: "解析失败的 XML",
			input: `<items>
						<item>
							<time>2024-11-11T15:45:19Z</time>
							<url>http://example.com</url>
							<host ip="***********">example.com</host>
							<port>80</port>
							<protocol>HTTP</protocol>
							<path>/path</path>
							<request base64="true">无效的Base64</request>
							<status>200</status>
							<responselength>1234</responselength>
							<mimetype>text/html</mimetype>
							<response base64="true">无效的Base64</response>
						</item>
					</items>`,
			decode:    true,
			expected:  Items{},
			expectErr: true,
		},
	}

	// 执行测试用例
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			reader := strings.NewReader(tc.input)
			result, err := ParseXml(reader, tc.decode)

			// 检查错误
			if (err != nil) != tc.expectErr {
				t.Errorf("expected error: %v, got: %v", tc.expectErr, err)
			}

			// 如果没有错误，检查结果是否符合预期
			if !tc.expectErr && !itemsEqual(result, tc.expected) {
				t.Errorf("expected: %+v, got: %+v", tc.expected, result)
			}
		})
	}
}

// 比较两个 Items 结构体是否相等
func itemsEqual(a, b Items) bool {
	if len(a.Items) != len(b.Items) {
		return false
	}
	for i := range a.Items {
		if !itemEqual(a.Items[i], b.Items[i]) {
			return false
		}
	}
	return true
}

// 比较两个 Item 结构体是否相等
func itemEqual(a, b Item) bool {
	return a.Time == b.Time &&
		a.Url == b.Url &&
		a.Host == b.Host &&
		a.Port == b.Port &&
		a.Protocol == b.Protocol &&
		a.Path == b.Path &&
		a.Extension == b.Extension &&
		a.Request == b.Request &&
		a.Status == b.Status &&
		a.ResponseLength == b.ResponseLength &&
		a.MimeType == b.MimeType &&
		a.Response == b.Response &&
		a.Comment == b.Comment
}
