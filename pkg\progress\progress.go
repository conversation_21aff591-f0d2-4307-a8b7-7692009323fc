// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-17 16:01:46
// FilePath: /yaml_scan/pkg/progress/progress.go
// Description: 
package progress


// Progress is an interface implemented by nuclei progress display
// driver.
type Progress interface {
	// Stop stops the progress recorder.
	Stop()
	// Init inits the progress bar with initial details for scan
	Init(hostCount int64, rulesCount int, requestCount int64)
	// AddToTotal adds a value to the total request count
	AddToTotal(delta int64)
	// IncrementRequests increments the requests counter by 1.
	IncrementRequests()
	// SetRequests sets the counter by incrementing it with a delta
	SetRequests(count uint64)
	// IncrementMatched increments the matched counter by 1.
	IncrementMatched()
	// IncrementErrorsBy increments the error counter by count.
	IncrementErrorsBy(count int64)
	// IncrementFailedRequestsBy increments the number of requests counter by count
	// along with errors.
	IncrementFailedRequestsBy(count int64)
}


// NewStatsTicker creates and returns a new progress tracking object.
func NewStatsTicker(duration int, active, outputJSON, cloud bool, port int) (Progress, error) {
	var tickDuration time.Duration
	if active && duration != -1 {
		tickDuration = time.Duration(duration) * time.Second
	} else {
		tickDuration = -1
	}

	progress := &StatsTicker{}

	statsOpts := &clistats.DefaultOptions
	statsOpts.ListenPort = port
	// metrics port is enabled by default and is not configurable with new version of clistats
	// by default 63636 is used and than can be modified with -mp flag

	stats, err := clistats.NewWithOptions(context.TODO(), statsOpts)
	if err != nil {
		return nil, err
	}
	// only print in verbose mode
	gologger.Verbose().Msgf("Started metrics server at localhost:%v", stats.Options.ListenPort)
	progress.cloud = cloud
	progress.active = active
	progress.stats = stats
	progress.tickDuration = tickDuration
	progress.outputJSON = outputJSON

	return progress, nil
}