//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-11 16:36:11
//FilePath: /yaml_scan/pkg/input/formats/burp/burp.go
//Description: burp格式化解析

package burp

import (
	"encoding/base64"
	"os"
	"strings"

	"yaml_scan/pkg/input/formats"
	"yaml_scan/pkg/input/types"
	"yaml_scan/utils/conversion"

	"github.com/pkg/errors"
)

// BurpFormat 是一个 Burp XML 文件解析器
type BurpFormat struct {
	opts formats.InputFormatOptions
}

var _ formats.Format = &BurpFormat{}

// New 创建一个新的 Burp XML 文件解析器
func New() *BurpFormat {
	return &BurpFormat{}
}

// Name returns the name of the format
func (j *BurpFormat) Name() string {
	return "burp"
}

// SetOptions 设置输入格式的选项
func (j *BurpFormat) SetOptions(options formats.InputFormatOptions) {
	j.opts = options
}

// Parse: 解析输入并调用提供的回调函数
// 对于每个发现的 RawRequest，都会调用该回调函数。
//
//	@receiver j *BurpFormat:
//	@param input string:  输入文件的路径，包含 Burp Suite 导出的 XML 数据。
//	@param resultsCb formats.ParseReqRespCallback: 处理解析后的请求的回调函数，类型为 ParseReqRespCallback。
//	@return error error:
func (j *BurpFormat) Parse(input string, resultsCb formats.ParseReqRespCallback) error {
	// 打开指定的输入文件
	file, err := os.Open(input)
	if err != nil {
		return errors.Wrap(err, "could not open data file")
	}
	// 确保在函数结束时关闭文件
	defer file.Close()

	// 解析 XML 文件
	items, err := ParseXml(file, true)
	if err != nil {
		return errors.Wrap(err, "could not decode burp xml schema")
	}

	// 打印解析的数据以进行验证
	for _, item := range items.Items {
		// 创建一个局部变量以避免闭包问题
		item := item
		// 解码 Base64 编码的请求
		binx, err := base64.StdEncoding.DecodeString(item.Request.Raw)
		if err != nil {
			return errors.Wrap(err, "could not decode base64")
		}
		// 检查解码后的请求是否为空
		if strings.TrimSpace(conversion.String(binx)) == "" {
			continue
		}
		// 解析原始请求并附加 URL
		rawRequest, err := types.ParseRawRequestWithURL(conversion.String(binx), item.Url)
		if err != nil {
			return errors.Wrap(err, "could not parse raw request")
		}
		// 调用回调函数处理解析后的请求
		resultsCb(rawRequest) 
	}
	return nil
}
