package config



import (
	"os"
	"testing"
)

func TestLoadConfig(t *testing.T) {
	// 创建一个临时的 YAML 配置文件
	tempFile, err := os.CreateTemp("", "test_config.yaml")
	if err != nil {
		t.Fatalf("failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name()) // 清理临时文件

	// 写入测试配置内容
	configContent := `
log:
  location: "/var/log/app.log"
  rotate: true
  rotate_check: "1h"
  rotation_interval: "24h"
  max_size: 100
  compress: true
  backup_time_format: "%Y-%m-%d"
  archive_format: "zip"
  rotate_each_hour: false
  rotate_each_day: true
  max_level: "info"
  file_name: "app.log"
flag:
  caseSensitive: true
`
	if _, err := tempFile.WriteString(configContent); err != nil {
		t.Fatalf("failed to write to temp file: %v", err)
	}

	// 关闭文件以确保可以读取
	if err := tempFile.Close(); err != nil {
		t.Fatalf("failed to close temp file: %v", err)
	}

	loadConfig(tempFile.Name())

	// 验证配置是否正确加载
	logConfig := GetLogConfig()
	if logConfig.Location != "/var/log/app.log" {
		t.Errorf("expected location to be '/var/log/app.log', got '%s'", logConfig.Location)
	}
	if logConfig.Rotate != true {
		t.Errorf("expected rotate to be true, got %v", logConfig.Rotate)
	}
	if logConfig.Rotationcheck != "1h" {
		t.Errorf("expected rotation check to be '1h', got '%s'", logConfig.Rotationcheck)
	}
	if logConfig.RotationInterval != "24h" {
		t.Errorf("expected rotation interval to be '24h', got '%s'", logConfig.RotationInterval)
	}
	if logConfig.MaxSize != 100 {
		t.Errorf("expected max size to be 100, got %d", logConfig.MaxSize)
	}
	if logConfig.Compress != true {
		t.Errorf("expected compress to be true, got %v", logConfig.Compress)
	}
	if logConfig.MaxLevel != "info" {
		t.Errorf("expected max level to be 'info', got '%s'", logConfig.MaxLevel)
	}

	flagConfig := GetFlagConfig()
	if flagConfig.CaseSensitive != true {
		t.Errorf("expected caseSensitive to be true, got %v", flagConfig.CaseSensitive)
	}
}


func TestInvalidConfig(t *testing.T) {
	// 测试无效的配置文件
	invalidFile, err := os.CreateTemp("", "invalid_config.yaml")
	if err != nil {
		t.Fatalf("failed to create temp file: %v", err)
	}
	defer os.Remove(invalidFile.Name()) // 清理临时文件

	// 写入无效的配置内容
	invalidContent := `
log:
  location: "/var/log/app.log"
  rotate: true
  rotate_check: "invalid_duration"  # 这是无效的
`
	if _, err := invalidFile.WriteString(invalidContent); err != nil {
		t.Fatalf("failed to write to temp file: %v", err)
	}

	// 关闭文件以确保可以读取
	if err := invalidFile.Close(); err != nil {
		t.Fatalf("failed to close temp file: %v", err)
	}

	// 尝试加载无效配置
	if err := loadConfig(invalidFile.Name()); err == nil {
		t.Fatal("expected error loading invalid config, got nil")
	}
}

func TestMultipleLoads(t *testing.T) {
	// 创建一个临时的 YAML 配置文件
	tempFile, err := os.CreateTemp("", "test_config_multiple.yaml")
	if err != nil {
		t.Fatalf("failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name()) // 清理临时文件

	// 写入测试配置内容
	configContent := `
log:
  location: "/var/log/app_multiple.log"
  rotate: true
  rotate_check: "1h"
  rotation_interval: "24h"
  max_size: 100
  compress: true
  backup_time_format: "%Y-%m-%d"
  archive_format: "zip"
  rotate_each_hour: false
  rotate_each_day: true
  max_level: "info"
  file_name: "app_multiple.log"
flag:
  caseSensitive: true
`
	if _, err := tempFile.WriteString(configContent); err != nil {
		t.Fatalf("failed to write to temp file: %v", err)
	}

	// 关闭文件以确保可以读取
	if err := tempFile.Close(); err != nil {
		t.Fatalf("failed to close temp file: %v", err)
	}

	// 初始化配置
	if err := loadConfig(tempFile.Name()); err != nil {
		t.Fatalf("failed to load config: %v", err)
	}

	// 再次尝试加载配置，应该不会重复加载
	if err := loadConfig(tempFile.Name()); err != nil {
		t.Fatalf("expected no error on second load, got: %v", err)
	}

	// 验证配置是否仍然正确
	logConfig := GetLogConfig()
	if logConfig == nil {
		t.Fatal("expected log config to be loaded, got nil")
	}
	if logConfig.Location != "/var/log/app_multiple.log" {
		t.Errorf("expected location to be '/var/log/app_multiple.log', got '%s'", logConfig.Location)
	}
}