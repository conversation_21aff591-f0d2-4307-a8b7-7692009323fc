// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-12 15:15:42
// FilePath: /yaml_scan/pkg/ratelimit/ratelimit_test.go
// Description: 
package ratelimit

import (
	"context"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestRateLimiterTake 测试限流器的Take方法
// 验证限流器是否正确限制请求速率
func TestRateLimiterTake(t *testing.T) {
	// 创建一个令牌桶限流器，最大令牌数为5，间隔为100毫秒
	ctx := context.Background()
	limiter := New(ctx, 5, 100*time.Millisecond)
	defer limiter.Stop()

	// 测试能否正常获取预设数量的令牌
	for i := 0; i < 5; i++ {
		require.True(t, limiter.CanTake(), "应该能够获取令牌")
		limiter.Take()
	}

	// 此时应该没有可用令牌
	require.False(t, limiter.CanTake(), "所有令牌都已被使用，应该无法获取更多令牌")

	// 等待令牌重新填充
	time.Sleep(150 * time.Millisecond)

	// 验证令牌已经重新填充
	require.True(t, limiter.CanTake(), "令牌应该已经重新填充")
}

// TestRateLimiterSetLimit 测试限流器的SetLimit方法
// 验证是否可以动态调整限流器的限制速率
func TestRateLimiterSetLimit(t *testing.T) {
	// 创建一个令牌桶限流器，最大令牌数为3，间隔为100毫秒
	ctx := context.Background()
	limiter := New(ctx, 3, 100*time.Millisecond)
	defer limiter.Stop()

	// 验证初始限制速率
	require.Equal(t, uint(3), limiter.GetLimit(), "初始限制速率应为3")

	// 消耗所有令牌
	for i := 0; i < 3; i++ {
		limiter.Take()
	}
	require.False(t, limiter.CanTake(), "所有令牌都已被使用，应该无法获取更多令牌")

	// 增加限制速率
	limiter.SetLimit(5)
	require.Equal(t, uint(5), limiter.GetLimit(), "限制速率应更新为5")

	// 等待令牌重新填充
	time.Sleep(150 * time.Millisecond)

	// 现在应该能够获取5个令牌
	for i := 0; i < 5; i++ {
		require.True(t, limiter.CanTake(), "应该能够获取令牌")
		limiter.Take()
	}
}

// TestRateLimiterSetDuration 测试限流器的SetDuration方法
// 验证是否可以动态调整限流器的填充间隔
func TestRateLimiterSetDuration(t *testing.T) {
	// 创建一个令牌桶限流器，最大令牌数为2，间隔为200毫秒
	ctx := context.Background()
	limiter := New(ctx, 2, 200*time.Millisecond)
	defer limiter.Stop()

	// 消耗所有令牌
	for i := 0; i < 2; i++ {
		limiter.Take()
	}

	// 减少填充间隔
	limiter.SetDuration(50 * time.Millisecond)

	// 等待新的填充间隔过去
	time.Sleep(100 * time.Millisecond)

	// 验证令牌已经重新填充
	require.True(t, limiter.CanTake(), "令牌应该已经重新填充")
}

// TestNewUnlimited 测试无限制限流器
// 验证无限制限流器是否始终允许请求通过
func TestNewUnlimited(t *testing.T) {
	// 创建一个无限制限流器
	ctx := context.Background()
	limiter := NewUnlimited(ctx)
	defer limiter.Stop()

	// 尝试大量获取令牌，验证是否始终可以获取令牌
	for i := 0; i < 1000; i++ {
		require.True(t, limiter.CanTake(), "无限制限流器应该始终允许请求通过")
		limiter.Take()
	}
}

// TestLeakyBucketLimiter 测试漏桶限流器
// 验证漏桶限流器是否正常工作
func TestLeakyBucketLimiter(t *testing.T) {
	// 创建一个漏桶限流器，最大令牌数为3，间隔为50毫秒
	ctx := context.Background()
	limiter := NewLeakyBucket(ctx, 3, 50*time.Millisecond)

	// 验证可以获取3个令牌（允许的突发大小）
	require.True(t, limiter.CanTake(), "应该能够获取令牌")
	limiter.Take()
	require.True(t, limiter.CanTake(), "应该能够获取令牌")
	limiter.Take()
	require.True(t, limiter.CanTake(), "应该能够获取令牌")
	limiter.Take()

	// 验证设置新的限制速率
	limiter.SetLimit(5)
	require.Equal(t, uint(5), limiter.GetLimit(), "限制速率应更新为5")

	// 验证设置新的时间间隔
	oldInterval := limiter.interval
	newInterval := 100 * time.Millisecond
	limiter.SetDuration(newInterval)
	require.Equal(t, newInterval, limiter.interval, "时间间隔应更新为100毫秒")
	require.NotEqual(t, oldInterval, limiter.interval, "时间间隔应发生变化")
}

// TestConcurrentTake 测试并发场景下的限流器
// 验证限流器是否在多个goroutine中正常工作
func TestConcurrentTake(t *testing.T) {
	// 创建一个令牌桶限流器，最大令牌数为10，间隔为100毫秒
	ctx := context.Background()
	limiter := New(ctx, 10, 100*time.Millisecond)
	defer limiter.Stop()

	// 创建10个goroutine同时获取令牌
	var wg sync.WaitGroup
	tokenCount := 0
	var mu sync.Mutex

	// 启动10个goroutine
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			limiter.Take()
			mu.Lock()
			tokenCount++
			mu.Unlock()
		}()
	}

	// 等待所有goroutine完成
	wg.Wait()

	// 验证正好获取了10个令牌
	require.Equal(t, 10, tokenCount, "应该获取了10个令牌")
	require.False(t, limiter.CanTake(), "所有令牌都已被使用，应该无法获取更多令牌")

	// 等待令牌重新填充
	time.Sleep(150 * time.Millisecond)
	require.True(t, limiter.CanTake(), "令牌应该已经重新填充")
}


