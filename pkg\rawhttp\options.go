// Author: chenjb
// Version: V1.0
// Date: 2025-07-22 17:25:19
// FilePath: /yaml_scan/pkg/rawhttp/options.go
// Description: rawhttp客户端配置选项定义，包含超时、重定向、代理等各种配置参数

// Package rawhttp 客户端配置选项模块
package rawhttp

import (
	"time"                            // 时间处理包
	"yaml_scan/pkg/fastdialer"        // 快速拨号器包
	"yaml_scan/pkg/rawhttp/client"    // HTTP客户端包
)

// Options 包含rawhttp客户端的配置选项
// 提供了完整的HTTP客户端行为控制参数
type Options struct {
	Timeout                time.Duration         // 请求超时时间，控制整个请求的最大执行时间
	FollowRedirects        bool                  // 是否跟踪HTTP重定向，true表示自动跟踪重定向
	MaxRedirects           int                   // 最大重定向次数，防止无限重定向循环
	AutomaticHostHeader    bool                  // 是否自动添加Host头部，true表示自动设置Host字段
	AutomaticContentLength bool                  // 是否自动计算Content-Length，true表示自动计算请求体长度
	CustomHeaders          client.Headers        // 自定义HTTP头部，用于添加额外的请求头
	ForceReadAllBody       bool                  // 强制读取所有响应体，忽略Content-Length并读取全部内容
	CustomRawBytes         []byte                // 自定义原始字节数据，用于发送特定格式的请求
	Proxy                  string                // 代理服务器URL，支持HTTP和SOCKS5代理
	ProxyDialTimeout       time.Duration         // 代理连接超时时间，控制通过代理建立连接的最大时间
	SNI                    string                // 服务器名称指示（Server Name Indication），用于TLS握手
	FastDialer             *fastdialer.Dialer    // 快速拨号器实例，用于优化网络连接建立
}

// DefaultOptions 是客户端的默认配置选项
// 提供了合理的默认值，适用于大多数HTTP请求场景
var DefaultOptions = &Options{
	Timeout:                30 * time.Second, // 默认超时30秒
	FollowRedirects:        true,             // 默认跟踪重定向
	MaxRedirects:           10,               // 默认最大重定向10次
	AutomaticHostHeader:    true,             // 默认自动添加Host头部
	AutomaticContentLength: true,             // 默认自动计算Content-Length
}

