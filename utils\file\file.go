//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-22 15:51:18
//FilePath: /yaml_scan/utils/file/file.go
//Description:

package fileutil

import (
	"bufio"
	"io"
	"os"
	"path/filepath"
	"strings"
	"unicode"

	"github.com/mattn/go-isatty"

	stringsutil "yaml_scan/utils/strings"

	"github.com/pkg/errors"
)

// FileExists 检查指定路径的文件是否存在
//
// filename: 要检查的文件路径
// 返回值: 如果文件存在且不是目录，则返回 true；否则返回 false。
func FileExists(filename string) bool {
	info, err := os.Stat(filename)
	if os.IsNotExist(err) {
		return false
	}
	if err != nil {
		return false
	}
	return !info.IsDir()
}

// ReadFile 读取指定文件的内容并通过通道返回每一行
//
// filename: 要读取的文件路径
// 返回值: 一个字符串通道和可能发生的错误
func ReadFile(filename string) (chan string, error) {
	// 检查文件是否存在
	if !FileExists(filename) {
		return nil, errors.New("file doesn't exist")
	}

	// 创建一个字符串通道用于输出文件内容
	out := make(chan string)

	// 启动一个 goroutine 来读取文件
	go func() {
		// 确保在函数结束时关闭通道
		defer close(out)
		// dak wenj
		f, err := os.Open(filename)
		if err != nil {
			return
		}
		// 确保在函数结束时关闭文件
		defer f.Close()
		// 创建一个扫描器来逐行读取文件
		scanner := bufio.NewScanner(f)
		// 将每一行发送到通道
		for scanner.Scan() {
			out <- scanner.Text()
		}
	}()

	return out, nil
}

// FolderExists: 检查指定的文件夹是否存在。
//
//	@param foldername string: 要检查的文件夹名称或路径。
//	@return bool bool: 如果文件夹存在且是一个目录，则返回 true；否则返回 false。
func FolderExists(foldername string) bool {
	// 获取文件或目录的状态信息
	info, err := os.Stat(foldername)
	// 检查是否因为不存在而返回错误
	if os.IsNotExist(err) {
		return false
	}
	if err != nil {
		return false
	}
	return info.IsDir()
}

// CreateFolder: 用于在指定路径创建一个文件夹。
//
//	@param path string:要创建的文件夹路径。
//	@return error error: 如果创建文件夹时发生错误，返回相应的错误信息；如果成功则返回 nil。
func CreateFolder(path string) error {
	return os.MkdirAll(path, 0700)
}

// ExecutableName: 获取当前可执行文件的名称（不包括扩展名）
//
//	@return string string:
func ExecutableName() string {
	executablePath, err := os.Executable()
	if err == nil {
		executablePath = os.Args[0]
	}
	// 获取文件名
	executableNameWithExt := filepath.Base(executablePath)
	// 去掉扩展名
	return stringsutil.TrimSuffixAny(executableNameWithExt, filepath.Ext(executableNameWithExt))
}

// IsEmpty:  检查文件是否为空
//
//	@param filename string: 要检查的文件名
//	@return bool bool: true 表示文件为空，false 表示文件不为空
//	@return error error: 如果发生错误，返回错误信息；否则返回 nil
//
// 空文件的定义：
//  1. 文件大小为 0 字节
//  2. 文件内容仅包含空格、null 字符、换行符或回车符
func IsEmpty(filename string) (bool, error) {
	// 获取文件信息
	fileInfo, err := os.Stat(filename)
	if err != nil {
		return false, err
	}

	// 检查文件大小是否为 0
	if fileInfo.Size() == 0 {
		return true, nil
	}

	// 打开文件
	file, err := os.Open(filename)
	if err != nil {
		return false, err
	}
	defer file.Close()

	// 读取文件的前 16 个字节
	buffer := make([]byte, 16)
	n, err := file.Read(buffer)
	if err != nil && err != io.EOF {
		return false, err
	}

	// 检查读取到的字节是否全是空格、null、换行符或回车符
	for i := 0; i < n; i++ {
		if !unicode.IsSpace(rune(buffer[i])) && buffer[i] != 0 && buffer[i] != '\n' && buffer[i] != '\r' {
			return false, nil
		}
	}
	return true, nil
}

// isCommentOnly:  检查文件是否只包含注释行或空行
//
//	@param filePath string: 要检查的文件路径
//	@return bool bool: true 表示文件只包含注释行或空行，false 表示文件包含其他内容
func IsCommentOnly(filePath string) bool {
	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return false
	}
	defer file.Close()

	// 创建bufio.Scanner来逐行读取文件内容
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()
		// 如果当前行不为空且不以#开头，说明文件包含非注释内容，返回false
		if line != "" && !strings.HasPrefix(line, "#") {
			return false
		}
	}
	return true
}


// GetTempFileName 生成一个临时文件名 
// @return string string: 生成的临时文件名
// @return error error:  操作过程中发生的错误（如创建、关闭或删除文件失败）
func GetTempFileName() (string, error) {
	// 在系统临时目录创建临时文件，文件名以空前缀开头
	tmpfile, err := os.CreateTemp("", "")
	if err != nil {
		return "", err
	}
	// 获取临时文件的完整路径名
	tmpFileName := tmpfile.Name()
	if err := tmpfile.Close(); err != nil {
		return tmpFileName, err
	}
	// 删除临时文件
	err = os.RemoveAll(tmpFileName)
	return tmpFileName, err
}

// HasPermission checks if the file has the requested permission
func HasPermission(fileName string, permission int) (bool, error) {
	file, err := os.OpenFile(fileName, permission, 0666)
	if err != nil {
		if os.IsPermission(err) {
			return false, errors.Wrap(err, "permission error")
		}
		return false, err
	}
	file.Close()

	return true, nil
}

// IsReadable verify file readability
func IsReadable(fileName string) (bool, error) {
	return HasPermission(fileName, os.O_RDONLY)
}

// IsWriteable verify file writeability
func IsWriteable(fileName string) (bool, error) {
	return HasPermission(fileName, os.O_WRONLY)
}

// HasStdin determines if the user has piped input
func HasStdin() bool {
	if (isatty.IsTerminal(os.Stdin.Fd()) || isatty.IsCygwinTerminal(os.Stdin.Fd())) {
		return false
	}
	stat, err := os.Stdin.Stat()
	if err != nil {
		return false
	}

	mode := stat.Mode()

	isPipedFromChrDev := (mode & os.ModeCharDevice) == 0
	isPipedFromFIFO := (mode & os.ModeNamedPipe) != 0

	return isPipedFromChrDev || isPipedFromFIFO
}

// ReadFileWithReader and stream on a channel
func ReadFileWithReader(r io.Reader) (chan string, error) {
	out := make(chan string)
	go func() {
		defer close(out)
		scanner := bufio.NewScanner(r)
		for scanner.Scan() {
			out <- scanner.Text()
		}
	}()

	return out, nil
}