// Package client 提供原始HTTP客户端实现，支持低级别的HTTP协议操作
package client

import (
	"bufio"                // 缓冲I/O包
	"bytes"                // 字节操作包
	"errors"               // 错误处理包
	"fmt"                  // 格式化输出包
	"io"                   // 输入输出接口包
	"net/http/httputil"    // HTTP工具包
	"strconv"              // 字符串转换包
	"strings"              // 字符串处理包
)

// Version 表示HTTP协议版本
// 包含主版本号和次版本号，用于标识HTTP协议的具体版本
type Version struct {
	Major int // 主版本号（如HTTP/1.1中的1）
	Minor int // 次版本号（如HTTP/1.1中的1）
}

// String 返回HTTP版本的字符串表示
// 返回:
//   string: HTTP版本字符串（如"HTTP/1.1"或"HTTP/2"）
// 功能: 根据版本号生成标准的HTTP版本字符串
func (v *Version) String() string {
	if v.Major < 2 {
		// HTTP/1.x版本格式：HTTP/主版本.次版本
		return fmt.Sprintf("HTTP/%d.%d", v.Major, v.Minor)
	}
	// HTTP/2及以上版本格式：HTTP/主版本
	return fmt.Sprintf("HTTP/%d", v.Major)
}

// 预定义的HTTP版本常量
var (
	HTTP_1_0 = Version{Major: 1, Minor: 0} // HTTP/1.0版本
	HTTP_1_1 = Version{Major: 1, Minor: 1} // HTTP/1.1版本
)

// Header 表示一个HTTP头部
// 包含头部名称和值的键值对
type Header struct {
	Key   string // 头部名称（如"Content-Type"）
	Value string // 头部值（如"application/json"）
}

// Request 表示一个完整的HTTP请求
// 包含请求的所有组成部分：方法、路径、头部、请求体等
type Request struct {
	RawBytes               []byte    // 原始字节数据，如果设置则直接发送这些字节
	AutomaticContentLength bool      // 是否自动计算Content-Length头部
	AutomaticHost          bool      // 是否自动添加Host头部
	Method                 string    // HTTP请求方法（GET、POST等）
	Path                   string    // 请求路径（如"/api/users"）
	Query                  []string  // 查询参数数组
	Version                         // 嵌入HTTP版本信息

	Headers []Header  // HTTP头部数组

	Body io.Reader    // 请求体读取器
}

// ContentLength 返回请求体的长度
// 返回:
//   int64: 请求体长度，如果长度未知则返回-1
// 功能: 计算请求体的字节长度，支持bytes.Buffer和strings.Reader类型
func (r *Request) ContentLength() int64 {
	// TODO(dfc) this should support anything with a Len() int64 method.
	// 如果没有请求体，返回-1
	if r.Body == nil {
		return -1
	}
	// 根据请求体类型计算长度
	switch b := r.Body.(type) {
	case *bytes.Buffer:
		return int64(b.Len()) // 返回字节缓冲区的长度
	case *strings.Reader:
		return int64(b.Len()) // 返回字符串读取器的长度
	default:
		return -1 // 其他类型无法确定长度
	}
}

// readerBuffer 读取器缓冲区大小（4KB）
// 用于优化网络I/O性能
const readerBuffer = 4096

// Client 表示到HTTP服务器的单个连接接口
// 遵循HTTP的KeepAlive条件，但连接池管理应在更高层处理
type Client interface {
	// WriteRequest 发送HTTP请求到服务器
	// 参数:
	//   *Request: 要发送的HTTP请求对象
	// 返回:
	//   error: 发送错误，成功时为nil
	WriteRequest(*Request) error

	// ReadResponse 从服务器读取HTTP响应
	// 参数:
	//   forceReadAll: 是否强制读取所有响应体内容
	// 返回:
	//   *Response: 读取到的HTTP响应对象
	//   error: 读取错误，成功时为nil
	ReadResponse(forceReadAll bool) (*Response, error)
}

// NewClient 创建一个新的Client实现
// 参数:
//   rw: 用于通信的读写器（通常是网络连接）
// 返回:
//   Client: 新创建的客户端实例
// 功能: 使用指定的读写器创建HTTP客户端，配置缓冲区大小
func NewClient(rw io.ReadWriter) Client {
	return &client{
		reader: reader{bufio.NewReaderSize(rw, readerBuffer)}, // 创建带缓冲的读取器
		writer: writer{Writer: rw},                           // 创建写入器
	}
}

// client HTTP客户端的具体实现
// 组合了读取器和写入器功能
type client struct {
	reader // 嵌入读取器，提供HTTP响应读取功能
	writer // 嵌入写入器，提供HTTP请求写入功能
}

// WriteRequest 将HTTP请求序列化并发送到网络
// 参数:
//   req: 要发送的HTTP请求对象
// 返回:
//   error: 发送错误，成功时为nil
// 功能: 完整地发送HTTP请求，包括请求行、头部和请求体
func (c *client) WriteRequest(req *Request) error {
	// 如果有原始字节数据，直接发送
	if len(req.RawBytes) > 0 {
		_, err := c.Write(req.RawBytes)
		return err
	}
	// 发送请求行（方法 路径 版本）
	if err := c.WriteRequestLine(req.Method, req.Path, req.Query, req.Version.String()); err != nil {
		return err
	}
	// 发送所有请求头部
	for _, h := range req.Headers {
		if err := c.WriteHeader(h.Key, h.Value); err != nil {
			return err
		}
	}

	// 处理自动Content-Length计算
	l := req.ContentLength()
	if req.AutomaticContentLength {
		if l >= 0 {
			// 如果能确定内容长度，添加Content-Length头部
			if err := c.WriteHeader("Content-Length", fmt.Sprintf("%d", l)); err != nil {
				return err
			}
		}
	}

	// 如果没有请求体，只发送结束标记
	if req.Body == nil {
		// doesn't actually start the body, just sends the terminating \r\n
		// 不实际开始请求体，只发送终止的\r\n
		return c.StartBody()
	}

	// 开始请求体并发送内容
	if err := c.StartBody(); err != nil {
		return err
	}
	return c.WriteBody(req.Body) // 写入请求体内容
}

// ReadResponse 从网络反序列化HTTP响应
// 参数:
//   forceReadAll: 是否强制读取所有响应体内容，忽略Content-Length限制
// 返回:
//   *Response: 解析得到的HTTP响应对象
//   error: 读取或解析错误，成功时为nil
// 功能: 完整地读取HTTP响应，包括状态行、头部和响应体
func (c *client) ReadResponse(forceReadAll bool) (*Response, error) {
	// 读取状态行（HTTP版本、状态码、状态消息）
	version, code, msg, err := c.ReadStatusLine()
	var headers []Header
	if err != nil {
		return nil, fmt.Errorf("ReadStatusLine: %v", err)
	}
	// 循环读取所有响应头部
	for {
		var key, value string
		var done bool
		key, value, done, err = c.ReadHeader() // 读取单个头部
		if err != nil || done {
			break // 读取完成或出错时退出循环
		}
		if key == "" {
			// empty header values are valid, rfc 2616 s4.2.
			// 空头部值是有效的，符合RFC 2616第4.2节
			err = errors.New("invalid header")
			break
		}
		headers = append(headers, Header{key, value}) // 添加头部到数组
	}
	// 构建响应对象
	var resp = Response{
		Version: version,           // 设置HTTP版本
		Status:  Status{code, msg}, // 设置状态信息
		Headers: headers,           // 设置头部数组
		Body:    c.ReadBody(),      // 设置响应体读取器
	}
	// 根据Content-Length或Transfer-Encoding处理响应体
	if l := resp.ContentLength(); l >= 0 && !forceReadAll {
		// 如果有明确的内容长度且不强制读取全部，限制读取长度
		resp.Body = io.LimitReader(resp.Body, l)
	} else if resp.TransferEncoding() == "chunked" {
		// 如果使用分块传输编码，使用分块读取器
		resp.Body = httputil.NewChunkedReader(resp.Body)
	}
	return &resp, err // 返回响应对象
}

// Response 表示符合RFC2616标准的HTTP响应
// 包含响应的所有组成部分：版本、状态、头部、响应体
type Response struct {
	Version         // 嵌入HTTP版本信息
	Status          // 嵌入HTTP状态信息
	Headers []Header // HTTP头部数组
	Body    io.Reader // 响应体读取器
}

// ContentLength 返回响应体的长度
// 返回:
//   int64: 响应体长度，如果长度未知则返回-1
// 功能: 从Content-Length头部解析响应体长度
func (r *Response) ContentLength() int64 {
	// 遍历所有头部查找Content-Length
	for _, h := range r.Headers {
		if strings.EqualFold(h.Key, "Content-Length") {
			// 解析Content-Length值为整数
			length, err := strconv.ParseInt(h.Value, 10, 64)
			if err != nil {
				continue // 解析失败继续查找下一个
			}
			return int64(length) // 返回解析得到的长度
		}
	}
	return -1 // 未找到或解析失败返回-1
}

// CloseRequested 检查响应是否包含Connection: close头部
// 返回:
//   bool: 如果包含Connection: close则返回true，否则返回false
// 功能: 判断服务器是否请求关闭连接
func (r *Response) CloseRequested() bool {
	// 遍历所有头部查找Connection头部
	for _, h := range r.Headers {
		if strings.EqualFold(h.Key, "Connection") {
			return h.Value == "close" // 检查值是否为"close"
		}
	}
	return false // 未找到Connection: close头部
}

// TransferEncoding 返回消息传输时使用的传输编码
// 返回:
//   string: 传输编码类型，如果未指定则假定为"identity"
// 功能: 从Transfer-Encoding头部获取传输编码方式
func (r *Response) TransferEncoding() string {
	// 遍历所有头部查找Transfer-Encoding
	for _, h := range r.Headers {
		if strings.EqualFold(h.Key, "Transfer-Encoding") {
			switch h.Value {
			case "identity", "chunked":
				return h.Value // 返回支持的编码类型
			}
		}
	}
	return "identity" // 默认返回identity编码
}

// Message 表示请求和响应的共同特征接口
// 定义了HTTP消息的基本操作方法
type Message interface {
	ContentLength() int64    // 获取内容长度
	CloseRequested() bool    // 检查是否请求关闭连接
}