//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-20 16:29:30
//FilePath: /yaml_scan/pkg/goflags/duration_var_test.go
//Description:

package goflags


import (
	"testing"
	"time"
)

func TestDurationVarP(t *testing.T) {
	var duration time.Duration
	flagSet := NewFlagSet() // 假设有一个构造函数来创建 FlagSet 实例

	// 添加一个持续时间标志
	defaultValue := 10 * time.Second
	flagData := flagSet.DurationVarP(&duration, "duration", "d", defaultValue, "duration flag usage")

	// 检查 FlagData 是否正确
	if flagData.long != "duration" {
		t.Errorf("Expected long name 'duration', but got '%s'", flagData.long)
	}
	if flagData.short != "d" {
		t.<PERSON><PERSON>("Expected short name 'd', but got '%s'", flagData.short)
	}
	if flagData.defaultValue != defaultValue {
		t.<PERSON>rf("Expected default value %v, but got %v", defaultValue, flagData.defaultValue)
	}
	if flagData.usage != "duration flag usage" {
		t.<PERSON>rrorf("Expected usage 'duration flag usage', but got '%s'", flagData.usage)
	}

	// 模拟设置标志值
	err := flagSet.CommandLine.Set("duration", "2h")
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}
	if duration != 2*time.Hour {
		t.Errorf("Expected duration 2h, but got %v", duration)
	}

	// 测试短名称
	err = flagSet.CommandLine.Set("d", "1d")
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}
	if duration != 24*time.Hour {
		t.Errorf("Expected duration 24h, but got %v", duration)
	}
}