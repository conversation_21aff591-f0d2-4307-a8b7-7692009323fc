// Author: chenjb
// Version: V1.0
// Date: 2025-05-27 16:00:35
// FilePath: /yaml_scan/pkg/fastdialer/ja3/ja3_test.go
// Description:
package ja3

import (
	"testing"

	utls "github.com/refraction-networking/utls"
	"github.com/stretchr/testify/require"
)

// TestParseVersion 测试TLS版本解析功能
func TestParseVersion(t *testing.T) {
	testCases := []struct {
		name       string
		versionStr string
		expected   uint16
		expectErr  bool
	}{
		{
			name:       "有效的TLS 1.2版本",
			versionStr: "771",
			expected:   uint16(771),
			expectErr:  false,
		},
		{
			name:       "有效的TLS 1.3版本",
			versionStr: "772",
			expected:   uint16(772),
			expectErr:  false,
		},
		{
			name:       "无效的版本字符串",
			versionStr: "invalid",
			expected:   0,
			expectErr:  true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			version, err := parseVersion(tc.versionStr)

			if tc.expectErr {
				require.Error(t, err, "应当返回错误")
			} else {
				require.NoError(t, err, "不应返回错误")
				require.Equal(t, tc.expected, version, "解析的版本应匹配预期")
			}
		})
	}
}

// TestParseCipherSuites 测试密码套件解析功能
func TestParseCipherSuites(t *testing.T) {
	testCases := []struct {
		name      string
		cipherStr string
		expected  []uint16
		expectErr bool
	}{
		{
			name:      "有效的密码套件列表",
			cipherStr: "4865-4866-4867",
			expected:  []uint16{4865, 4866, 4867},
			expectErr: false,
		},
		{
			name:      "带空格的密码套件列表",
			cipherStr: " 49195-49199-52393 ",
			expected:  []uint16{49195, 49199, 52393},
			expectErr: false,
		},
		{
			name:      "空字符串",
			cipherStr: "",
			expected:  nil,
			expectErr: true,
		},
		{
			name:      "无效的密码套件ID",
			cipherStr: "4865-invalid-4867",
			expected:  nil,
			expectErr: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			cipherSuites, err := parseCipherSuites(tc.cipherStr)

			if tc.expectErr {
				require.Error(t, err, "应当返回错误")
			} else {
				require.NoError(t, err, "不应返回错误")
				require.Equal(t, tc.expected, cipherSuites, "解析的密码套件应匹配预期")
			}
		})
	}
}

// TestParseExtensions 测试扩展解析功能
func TestParseExtensions(t *testing.T) {
	testCases := []struct {
		name         string
		extensionStr string
		expectLen    int
		expectErr    bool
	}{
		{
			name:         "有效的扩展列表",
			extensionStr: "0-5-13-16-18",
			expectLen:    5,
			expectErr:    false,
		},
		{
			name:         "带空格的扩展列表",
			extensionStr: " 0-5-13 ",
			expectLen:    3,
			expectErr:    false,
		},
		{
			name:         "空字符串",
			extensionStr: "",
			expectLen:    0,
			expectErr:    true,
		},
		{
			name:         "无效的扩展ID",
			extensionStr: "0-999999-13",
			expectLen:    0,
			expectErr:    true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			extensions, err := parseExtensions(tc.extensionStr)

			if tc.expectErr {
				require.Error(t, err, "应当返回错误")
			} else {
				require.NoError(t, err, "不应返回错误")
				require.Len(t, extensions, tc.expectLen, "解析的扩展数量应匹配预期")
			}
		})
	}
}

// TestParseSupportedCurves 测试支持的曲线解析功能
func TestParseSupportedCurves(t *testing.T) {
	testCases := []struct {
		name      string
		curvesStr string
		expected  []utls.CurveID
		expectErr bool
	}{
		{
			name:      "有效的曲线列表",
			curvesStr: "29-23-24",
			expected:  []utls.CurveID{29, 23, 24},
			expectErr: false,
		},
		{
			name:      "带空格的曲线列表",
			curvesStr: " 29-23-24 ",
			expected:  []utls.CurveID{29, 23, 24},
			expectErr: false,
		},
		{
			name:      "无效的曲线ID",
			curvesStr: "29-invalid-24",
			expected:  nil,
			expectErr: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			curves, err := parseSupportedCurves(tc.curvesStr)

			if tc.expectErr {
				require.Error(t, err, "应当返回错误")
			} else {
				require.NoError(t, err, "不应返回错误")
				require.Equal(t, tc.expected, curves, "解析的曲线应匹配预期")
			}
		})
	}
}

// TestParseSupportedPoints 测试支持的点格式解析功能
func TestParseSupportedPoints(t *testing.T) {
	testCases := []struct {
		name      string
		pointsStr string
		expected  []byte
		expectErr bool
	}{
		{
			name:      "有效的点格式列表",
			pointsStr: "0-1-2",
			expected:  []byte{0, 1, 2},
			expectErr: false,
		},
		{
			name:      "带空格的点格式列表",
			pointsStr: " 0-1-2 ",
			expected:  []byte{0, 1, 2},
			expectErr: false,
		},
		{
			name:      "无效的点格式ID",
			pointsStr: "0-invalid-2",
			expected:  nil,
			expectErr: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			points, err := parseSupportedPoints(tc.pointsStr)

			if tc.expectErr {
				require.Error(t, err, "应当返回错误")
			} else {
				require.NoError(t, err, "不应返回错误")
				require.Equal(t, tc.expected, points, "解析的点格式应匹配预期")
			}
		})
	}
}

// TestParseWithJa3 测试完整JA3字符串解析功能
func TestParseWithJa3(t *testing.T) {
	// 有效的JA3字符串，对应Chrome浏览器
	validJa3 := "771,4865-4866-4867-49195-49199-49196-49200-52393-52392-49171-49172-156-157-47-53,0-23-65281-35-16-5-13-18-51-45-43-21,29-23-24,0"

	// 测试有效JA3字符串解析
	clientHello, err := ParseWithJa3(validJa3)
	require.NoError(t, err, "解析有效的JA3字符串不应返回错误")
	require.NotNil(t, clientHello, "解析结果不应为nil")

	// 验证解析的各个部分
	require.Equal(t, uint16(771), clientHello.TLSVersMin, "TLS最小版本应为771")
	require.Equal(t, uint16(771), clientHello.TLSVersMax, "TLS最大版本应为771")
	require.Len(t, clientHello.CipherSuites, 15, "应有15个密码套件")
	require.Len(t, clientHello.Extensions, 12, "应有12个扩展")

	// 测试无效JA3字符串解析
	invalidJa3 := "771,4865-4866-4867" // 不完整，缺少部分
	_, err = ParseWithJa3(invalidJa3)
	require.Error(t, err, "解析无效的JA3字符串应返回错误")
}

// TestErrExtensionNotExist 测试扩展不存在的错误
func TestErrExtensionNotExist(t *testing.T) {
	// 创建扩展不存在错误
	err := ErrExtensionNotExist("99999")

	// 验证错误消息
	require.Contains(t, err.Error(), "Extension does not exist: 99999", "错误消息应包含不存在的扩展ID")
}
