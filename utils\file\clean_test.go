//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-06 17:41:45
//FilePath: /yaml_scan/utils/file/clean_test.go
//Description:

package fileutil

import (
	"os"
	"path/filepath"
	"testing"
)

// TestCleanPath 测试 CleanPath 函数
func TestCleanPath(t *testing.T) {
	// 创建一个临时目录
	tempDir := t.TempDir()

	// 设置当前工作目录为临时目录
	if err := os.Chdir(tempDir); err != nil {
		t.Fatalf("failed to change working directory: %v", err)
	}

	// 测试绝对路径
	absPath, err := CleanPath(tempDir)
	if err != nil {
		t.Fatalf("failed to clean absolute path: %v", err)
	}
	if absPath != tempDir {
		t.Errorf("expected %s, got %s", tempDir, absPath)
	}

	// 测试相对路径
	relativePath := "subdir/../file.txt"
	expectedPath := filepath.Join(tempDir, "file.txt")
	cleanedPath, err := CleanPath(relativePath)
	if err != nil {
		t.Fatalf("failed to clean relative path: %v", err)
	}
	if cleanedPath != expectedPath {
		t.Errorf("expected %s, got %s", expectedPath, cleanedPath)
	}
}

// TestFixMissingDirs 测试 FixMissingDirs 函数
func TestFixMissingDirs(t *testing.T) {
	// 创建一个临时目录
	tempDir := t.TempDir()
	// 定义一个目标文件路径，包含缺失的目录
	targetPath := filepath.Join(tempDir, "subdir", "file.txt")

	// 调用 FixMissingDirs
	if err := FixMissingDirs(targetPath); err != nil {
		t.Fatalf("failed to fix missing directories: %v", err)
	}

	// 验证目录是否已创建
	if _, err := os.Stat(filepath.Dir(targetPath)); os.IsNotExist(err) {
		t.Errorf("expected directory %s to be created, but it does not exist", filepath.Dir(targetPath))
	}
}
