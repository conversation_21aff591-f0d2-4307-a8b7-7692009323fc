// Author: chenjb
// Version: V1.0
// Date: 2025-05-28 15:05:50
// FilePath: /yaml_scan/pkg/dsl/func.go
// Description:
package dsl

import (
	"fmt"
	"strconv"
	"strings"
	"yaml_scan/pkg/gcache"

	"github.com/Knetic/govaluate"
)

var (
	// DefaultCacheSize 定义函数结果缓存的默认大小
	DefaultCacheSize = 6144
	// resultCache 用于缓存函数执行结果，提高性能
	resultCache      = gcache.New[string, interface{}](DefaultCacheSize).Build()
)

// dslFunction 表示一个DSL函数
// 包含函数名、签名、参数数量、实现和缓存设置等信息
type dslFunction struct {
	IsCacheable        bool                         // 标识函数结果是否可以被缓存
	Name               string                       // 函数名称
	NumberOfArgs       int                          // 如果定义了参数数量，函数签名将自动生成
	Signatures         []string                     // 函数的签名列表
	ExpressionFunction govaluate.ExpressionFunction // 函数的实际实现
}

// GetSignatures 返回函数的所有签名
// @receiver d 
// @return []string []string: 函数签名的字符串列表
func (d dslFunction) GetSignatures() []string {
	// 固定数量的参数意味着静态签名
	if d.NumberOfArgs > 0 {
		args := make([]string, 0, d.NumberOfArgs)
		// 为每个参数生成名称（arg1, arg2, ...）
		for i := 1; i <= d.NumberOfArgs; i++ {
			args = append(args, "arg"+strconv.Itoa(i))
		}
		// 生成完整的参数部分
		argsPart := fmt.Sprintf("(%s interface{}) interface{}", strings.Join(args, ", "))
		signature := d.Name + argsPart
		return []string{signature}
	}

	// 处理多个签名的情况
	var signatures []string
	for _, signature := range d.Signatures {
		signatures = append(signatures, d.Name+signature)
	}

	return signatures
}

// hash  根据函数名和参数生成缓存键
// @receiver d 
// @param args ...interface{}: 可变参数列表
// @return string string: 生成的哈希字符串，用作缓存键
func (d dslFunction) hash(args ...interface{}) string {
	var sb strings.Builder
	// 添加函数名
	_, _ = sb.WriteString(d.Name)
	_, _ = sb.WriteString("-")

	// 添加所有参数
	for i, arg := range args {
		_, _ = sb.WriteString(fmt.Sprintf("%v", arg))
		if i < len(args)-1 {
			_, _ = sb.WriteString(",")
		}
	}

	return sb.String()
}

// Exec 执行DSL函数
// @receiver d 
// @param args ...interface{}: 传递给函数的参数列表
// @return interface{} interface{}: 函数执行结果
// @return error error: 执行过程中的错误
func (d dslFunction) Exec(args ...interface{}) (interface{}, error) {
	// 验证参数数量是否符合预期
	if d.NumberOfArgs > 0 {
		if len(args) != d.NumberOfArgs {
			signatures := d.GetSignatures()
			if len(signatures) > 0 {
				return nil, fmt.Errorf("%w. correct method signature %q", ErrInvalidDslFunction, signatures[0])
			}
			return nil, ErrInvalidDslFunction
		}
	}

	// 如果函数结果不可缓存，直接执行
	if !d.IsCacheable {
		return d.ExpressionFunction(args...)
	}

	// 尝试从缓存获取结果
	functionHash := d.hash(args...)
	if result, err := resultCache.Get(functionHash); err == nil {
		return result, nil
	}

	// 执行函数并缓存结果
	result, err := d.ExpressionFunction(args...)
	if err == nil {
		_ = resultCache.Set(functionHash, result)
	}

	return result, err
}
