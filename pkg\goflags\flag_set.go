//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-21 15:48:50
//FilePath: /yaml_scan/pkg/goflags/flag_set.go
//Description:

package goflags

import (
	"flag"
	"strconv"
)

// StringSliceVar: 添加一个带有长名称的字符串切片标志
//  @receiver flagSet *FlagSet:
//  @param field *StringSlice: 指向 StringSlice 的指针，用于存储标志的值。
//  @param long string: 长标志的名称，例如 "--flag"。
//  @param defaultValue []string: 默认值，标志未提供时使用的值。
//  @param usage string: 标志的用法说明。
//  @param options Options: 自定义选项，用于控制标志的行为。
//  @return *FlagData *FlagData: 返回一个 FlagData 实例，包含标志的元数据。
func (flagSet *FlagSet) StringSliceVar(field *StringSlice, long string, defaultValue []string, usage string, options Options) *FlagData {
	return flagSet.StringSliceVarP(field, long, "", defaultValue, usage, options)
}


// StringSliceVarP 添加一个带有短名称和长名称的字符串切片标志
// 
// field: 指向 StringSlice 的指针，用于存储标志的值。
// long: 长标志的名称，例如 "--flag"。
// short: 短标志的名称，例如 "-f"。
// defaultValue: 默认值，标志未提供时使用的值。
// usage: 标志的用法说明。
// options: 自定义选项，用于控制标志的行为。
func (flagSet *FlagSet) StringSliceVarP(field *StringSlice, long, short string, defaultValue StringSlice, usage string, options Options) *FlagData {
	 // 将选项与字段关联
	optionMap[field] = options
	// 遍历默认值，将其设置到字段中
	for _, defaultItem := range defaultValue {
		// 将默认值转换为字符串切片
		values, _ := ToStringSlice(defaultItem, options)
		for _, value := range values {
			_ = field.Set(value)
		}
	}
	 // 将字段的当前值存储为默认值
	optionDefaultValues[field] = *field

	// 创建 FlagData 实例以存储标志的元数据
	flagData := &FlagData{
		usage:        usage,
		long:         long,
		defaultValue: defaultValue,
	}
	// 如果提供了短标志，则将其添加到命令行标志集中和 flagKeys 中
	if short != "" {
		flagData.short = short
		flagSet.CommandLine.Var(field, short, usage)
		flagSet.flagKeys.Set(short, flagData)
	}
	flagSet.CommandLine.Var(field, long, usage)
	flagSet.flagKeys.Set(long, flagData)
	return flagData
}

// VarP 方法添加一个带有短名称和长名称的 Var 标志。
// 它将标志的元数据存储在 FlagData 中，并将其添加到 FlagSet 的 CommandLine 中。
//
// field: 实现了 flag.Value 接口的字段，用于存储标志的值。
// long: 长标志的名称，例如 "--flag"。
// short: 短标志的名称，例如 "-f"。
// usage: 标志的用法说明。
func (flagSet *FlagSet) VarP(field flag.Value, long, short, usage string) *FlagData {
	// 创建一个新的 FlagData 实例
	flagData := &FlagData{
		usage:        usage,
		long:         long,
		defaultValue: field,
	}
	 // 如果提供了短标志，则将其添加到 CommandLine 和 flagKeys 中
	if short != "" {
		flagData.short = short
		flagSet.CommandLine.Var(field, short, usage)
		flagSet.flagKeys.Set(short, flagData)
	}
	 // 将长标志添加到命令行标志集中和 flagKeys 中
	flagSet.CommandLine.Var(field, long, usage)
	flagSet.flagKeys.Set(long, flagData)
	return flagData
}


// Var 方法添加一个仅带有长名称的 Var 标志。
// 它调用 VarP 方法，短名称为空。
//
// field: 实现了 flag.Value 接口的字段，用于存储标志的值。
// long: 长标志的名称，例如 "--flag"。
// usage: 标志的用法说明。
func (flagSet *FlagSet) Var(field flag.Value, long, usage string) *FlagData {
	return flagSet.VarP(field, long, "", usage)
}


// StringVarP 添加一个带有短名称和长名称的字符串标志
// 
//  field: 指向一个字符串变量的指针，用于存储用户输入的标志值。
//  long: 标志的长名称，通常以 -- 开头，例如 --config。
//  short: 标志的短名称，通常以 - 开头，例如 -c。如果不需要短名称，可以传递空字符串。
//  defaultValue: 标志的默认值，如果用户没有提供该标志，则使用此值。
//  usage: 标志的使用说明，描述该标志的功能，通常在帮助信息中显示。
// 返回值: 返回一个指向 FlagData 的指针，包含标志的元数据。
func (flagSet *FlagSet) StringVarP(field *string, long, short, defaultValue, usage string) *FlagData {
	// 创建一个 FlagData 实例，用于存储标志的元数据
	flagData := &FlagData{
		usage:        usage,
		long:         long,
		defaultValue: defaultValue,
	}
	// 如果提供了短名称，则注册短名称标志
	if short != "" {
		flagData.short = short
		flagSet.CommandLine.StringVar(field, short, defaultValue, usage)
		flagSet.flagKeys.Set(short, flagData)
	}
	 // 注册长名称标志
	flagSet.CommandLine.StringVar(field, long, defaultValue, usage)
	flagSet.flagKeys.Set(long, flagData)
	return flagData
}

// StringVar 添加一个带有长名称的字符串标志
// 
// field: 指向一个字符串变量的指针，用于存储用户输入的标志值。
// long: 标志的长名称，通常以 -- 开头，例如 --config。
// defaultValue: 标志的默认值，如果用户没有提供该标志，则使用此值。
// usage: 标志的使用说明，描述该标志的功能，通常在帮助信息中显示。
// 返回值: 返回一个指向 FlagData 的指针，包含标志的元数据。
func (flagSet *FlagSet) StringVar(field *string, long, defaultValue, usage string) *FlagData {
	return flagSet.StringVarP(field, long, "", defaultValue, usage)
}


// BoolVarP 添加一个带有短名称和长名称的布尔标志
// 
// field: 指向一个布尔变量的指针，用于存储用户输入的标志值。
// long: 标志的长名称，通常以 -- 开头，例如 --verbose。
// short: 标志的短名称，通常以 - 开头，例如 -v。如果不需要短名称，可以传递空字符串。
// defaultValue: 标志的默认值，如果用户没有提供该标志，则使用此值。
// usage: 标志的使用说明，描述该标志的功能，通常在帮助信息中显示。
// 返回值: 返回一个指向 FlagData 的指针，包含标志的元数据。
func (flagSet *FlagSet) BoolVarP(field *bool, long, short string, defaultValue bool, usage string) *FlagData {
	 // 创建一个 FlagData 实例，用于存储标志的元数据
	flagData := &FlagData{
		usage:        usage,
		long:         long,
		defaultValue: strconv.FormatBool(defaultValue),  // 将布尔值转换为字符串并设置默认值
	}
	 // 如果提供了短名称，则注册短名称标志
	if short != "" {
		flagData.short = short
		flagSet.CommandLine.BoolVar(field, short, defaultValue, usage)
		flagSet.flagKeys.Set(short, flagData)
	}
	// 注册长名称标志
	flagSet.CommandLine.BoolVar(field, long, defaultValue, usage)
	flagSet.flagKeys.Set(long, flagData)
	return flagData
}

// BoolVar 添加一个带有长名称的布尔标志
// 
// field: 指向一个布尔变量的指针，用于存储用户输入的标志值。
// long: 标志的长名称，通常以 -- 开头，例如 --verbose。
// defaultValue: 标志的默认值，如果用户没有提供该标志，则使用此值。
// usage: 标志的使用说明，描述该标志的功能，通常在帮助信息中显示。
// 返回值: 返回一个指向 FlagData 的指针，包含标志的元数据。
func (flagSet *FlagSet) BoolVar(field *bool, long string, defaultValue bool, usage string) *FlagData {
	return flagSet.BoolVarP(field, long, "", defaultValue, usage)
}


// StringSliceVarConfigOnly 添加一个字符串切片配置值（不带标志）
// 
// field: 指向一个 StringSlice 类型的指针，用于存储用户输入的字符串切片值。
// long: 配置项的长名称，通常用于标识该配置项。
// defaultValue: 配置项的默认值，如果用户没有提供该配置项，则使用此值。
// usage: 配置项的使用说明，描述该配置项的功能，通常在帮助信息中显示。
// 返回值: 返回一个指向 FlagData 的指针，包含配置项的元数据。
func (flagSet *FlagSet) StringSliceVarConfigOnly(field *StringSlice, long string, defaultValue []string, usage string) *FlagData {
	for _, item := range defaultValue {
		_ = field.Set(item)
	}
	flagData := &FlagData{
		usage:        usage,
		long:         long,
		defaultValue: defaultValue,
		field:        field,
	}
	flagSet.configOnlyKeys.Set(long, flagData)
	flagSet.flagKeys.Set(long, flagData)
	return flagData
}

// IntVarP: 向 FlagSet 添加一个整数标志，支持短名称和长名称
//  @receiver flagSet *FlagSet: 
//  @param field *int: 指向整数变量的指针，用于存储标志的值。
//  @param long string: 标志的长名称。
//  @param short string:  标志的短名称。
//  @param defaultValue int: 标志的默认值。
//  @param usage string:  标志的使用说明。
//  @return *FlagData *FlagData: 返回一个 FlagData 指针，包含标志的相关信息。
func (flagSet *FlagSet) IntVarP(field *int, long, short string, defaultValue int, usage string) *FlagData {
	flagData := &FlagData{
		usage:        usage,
		short:        short,
		long:         long,
		defaultValue: strconv.Itoa(defaultValue),
	}
	if short != "" {
		flagData.short = short
		flagSet.CommandLine.IntVar(field, short, defaultValue, usage)
		flagSet.flagKeys.Set(short, flagData)
	}
	flagSet.CommandLine.IntVar(field, long, defaultValue, usage)
	flagSet.flagKeys.Set(long, flagData)
	return flagData
}


func (flagSet *FlagSet) IntVar(field *int, long string, defaultValue int, usage string) *FlagData {
	return flagSet.IntVarP(field, long, "", defaultValue, usage)
}

// RuntimeMapVarP: 向 FlagSet 添加一个仅在运行时使用的映射标志，支持短名称和长名称
//  @receiver flagSet *FlagSet: 
//  @param field *RuntimeMap: 指向 RuntimeMap 的指针，用于存储标志的值。
//  @param long string: 标志的长名称。
//  @param short string: 标志的短名称。
//  @param defaultValue []string:  标志的默认值列表，格式为字符串切片，每个元素为 "key=value"。
//  @param usage string: 标志的使用说明。
//  @return *FlagData *FlagData: 返回一个 FlagData 指针，包含标志的相关信息。
func (flagSet *FlagSet) RuntimeMapVarP(field *RuntimeMap, long, short string, defaultValue []string, usage string) *FlagData {
	for _, item := range defaultValue {
		_ = field.Set(item)
	}

	flagData := &FlagData{
		usage:        usage,
		long:         long,
		defaultValue: defaultValue,
		skipMarshal:  true,
	}

	if short != "" {
		flagData.short = short
		flagSet.CommandLine.Var(field, short, usage)
		flagSet.flagKeys.Set(short, flagData)
	}
	flagSet.CommandLine.Var(field, long, usage)
	flagSet.flagKeys.Set(long, flagData)
	return flagData
}


// EnumVarP: 添加一个具有短名称和长名称的枚举标志
//  @receiver flagSet *FlagSet: 
//  @param field *string:  指向存储值的字符串指针，用于保存用户选择的枚举值。
//  @param long string:  标志的长名称。
//  @param short string: 标志的短名称。
//  @param defaultValue EnumVariable: 默认的枚举值，必须在允许的类型中定义。
//  @param usage string: 标志的使用说明。
//  @param allowedTypes AllowdTypes: 允许的枚举类型映射。
//  @return *FlagData *FlagData: 返回一个 FlagData 指针，包含标志的相关信息。
func (flagSet *FlagSet) EnumVarP(field *string, long, short string, defaultValue EnumVariable, usage string, allowedTypes AllowdTypes) *FlagData {
	var hasDefaultValue bool
	// 检查默认值是否在允许的类型中
	for k, v := range allowedTypes {
		if v == defaultValue {
			hasDefaultValue = true
			*field = k
		}
	}
	// 如果没有找到默认值，抛出 panic
	if !hasDefaultValue {
		panic("undefined default value")
	}
	flagData := &FlagData{
		usage:        usage,
		long:         long,
		defaultValue: *field,
	}
	if short != "" {
		flagData.short = short
		flagSet.CommandLine.Var(&EnumVar{allowedTypes, field}, short, usage)
		flagSet.flagKeys.Set(short, flagData)
	}
	flagSet.CommandLine.Var(&EnumVar{allowedTypes, field}, long, usage)
	flagSet.flagKeys.Set(long, flagData)
	return flagData
}