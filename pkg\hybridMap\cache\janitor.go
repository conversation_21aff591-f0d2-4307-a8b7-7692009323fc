// Author: chenjb
// Version: V1.0
// Date: 2025-05-16 09:52:40
// FilePath: /yaml_scan/pkg/hybridMap/cache/janitor.go
// Description:  缓存清理器实现，用于定期清理过期的缓存项
package cache

import (
	"time"
)

// janitor 是缓存清理器，负责定期删除过期的缓存项
type janitor struct {
	Interval time.Duration // 清理间隔时间
	stop     chan struct{} // 停止清理的信号通道
}

// Run 启动清理器的运行循环
// 以指定的时间间隔调用缓存的删除过期项方法
// @receiver j 
// @param c *cacheMemory: 要清理的缓存实例
func (j *janitor) Run(c *cacheMemory) {
	ticker := time.NewTicker(j.Interval)
	for {
		select {
		case <-ticker.C:
			c.DeleteExpired()
		case <-j.stop:
			ticker.Stop()
			return
		}
	}
}

// stopJanitor 停止缓存清理器
// @param c *cacheMemory: 包含清理器的缓存实例
func stopJanitor(c *cacheMemory) {
	c.janitor.stop <- struct{}{}
}

// runJanitor 创建并启动缓存清理器
// @param c *cacheMemory: 要清理的缓存实例
// @param ci time.Duration: 清理间隔时间
func runJanitor(c *cacheMemory, ci time.Duration) {
	j := &janitor{
		Interval: ci,
		stop:     make(chan struct{}),
	}
	c.janitor = j
	go j.Run(c)
}
