//
// Author: chenjb
// Version: V1.0
// Date: 2025-07-22 17:49:36
// FilePath: /yaml_scan/pkg/rawhttp/proxy/socks5.go
// Description: SOCKS5代理实现，支持通过SOCKS5协议建立代理连接

// Package proxy SOCKS5代理功能模块
package proxy

import (
	"net"     // 网络包
	"net/url" // URL解析包
	"time"    // 时间处理包

	"golang.org/x/net/proxy" // Go扩展网络代理包
)

// Socks5Dialer 创建SOCKS5代理拨号函数
// 参数:
//   proxyAddr: SOCKS5代理服务器地址（格式：socks5://[user:pass@]host:port）
//   timeout: 连接超时时间（注意：当前实现中未使用此参数）
// 返回:
//   DialFunc: SOCKS5代理拨号函数
// 功能: 通过SOCKS5协议建立代理连接，支持用户名密码认证
func Socks5Dialer(proxyAddr string, timeout time.Duration) DialFunc {
	var (
		u      *url.URL      // 解析后的代理URL
		err    error         // 错误信息
		dialer proxy.Dialer  // 代理拨号器
	)
	// 解析代理地址并创建SOCKS5拨号器
	if u, err = url.Parse(proxyAddr); err == nil {
		// 使用golang.org/x/net/proxy包创建SOCKS5拨号器
		// proxy.Direct作为fallback拨号器，当代理不可用时直接连接
		dialer, err = proxy.FromURL(u, proxy.Direct)
	}
	// 返回拨号函数
	return func(addr string) (net.Conn, error) {
		if err != nil {
			return nil, err // 如果初始化时有错误，返回错误
		}
		// 通过SOCKS5代理建立TCP连接
		return dialer.Dial("tcp", addr)
	}
}

