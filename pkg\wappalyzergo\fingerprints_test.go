// Author: chenjb
// Version: V1.0
// Date: 2025-07-18 11:12:12
// FilePath: /yaml_scan/pkg/wappalyzergo/fingerprints_test.go
// Description: fingerprints.go 的单元测试文件，测试指纹数据结构和编译功能

package wappalyzergo

import (
    "testing"

    "github.com/stretchr/testify/require"
)

// TestFingerprint 测试基础指纹结构
func TestFingerprint(t *testing.T) {
    t.Run("创建完整指纹", func(t *testing.T) {
        fingerprint := &Fingerprint{
            Cats:        []int{1, 2, 3},
            CSS:         []string{"body.test", ".container"},
            Cookies:     map[string]string{"PHPSESSID": ".*", "test_cookie": "value"},
            Dom:         map[string]map[string]interface{}{"div": {"exists": ""}},
            JS:          map[string]string{"jQuery": ".*", "angular": ".*"},
            Headers:     map[string]string{"Server": "nginx", "X-Powered-By": "PHP"},
            HTML:        []string{"<meta name=\"generator\"", "wordpress"},
            Script:      []string{"var test", "function init"},
            ScriptSrc:   []string{"jquery.min.js", "bootstrap.js"},
            Meta:        map[string][]string{"generator": {"WordPress", "Drupal"}},
            Implies:     []string{"PHP", "MySQL"},
            Description: "测试应用",
            Website:     "https://example.com",
            CPE:         "cpe:2.3:a:test:app:*:*:*:*:*:*:*:*",
            Icon:        "test.png",
        }

        require.NotNil(t, fingerprint, "指纹不应为nil")
        require.Equal(t, []int{1, 2, 3}, fingerprint.Cats, "分类应该匹配")
        require.Equal(t, "测试应用", fingerprint.Description, "描述应该匹配")
        require.Equal(t, "https://example.com", fingerprint.Website, "网站应该匹配")
        require.NotEmpty(t, fingerprint.Headers, "Headers不应为空")
        require.NotEmpty(t, fingerprint.JS, "JS不应为空")
    })

    t.Run("空指纹结构", func(t *testing.T) {
        fingerprint := &Fingerprint{}
        require.NotNil(t, fingerprint, "空指纹不应为nil")
        require.Empty(t, fingerprint.Cats, "空分类应该为空")
        require.Empty(t, fingerprint.Description, "空描述应该为空")
    })
}

// TestFingerprints 测试指纹集合
func TestFingerprints(t *testing.T) {
    t.Run("创建指纹集合", func(t *testing.T) {
        fingerprints := &Fingerprints{
            Apps: map[string]*Fingerprint{
                "nginx": {
                    Cats:        []int{22},
                    Headers:     map[string]string{"Server": "nginx"},
                    Description: "Web服务器",
                },
                "WordPress": {
                    Cats:        []int{1},
                    Meta:        map[string][]string{"generator": {"WordPress"}},
                    Description: "内容管理系统",
                },
            },
        }

        require.NotNil(t, fingerprints, "指纹集合不应为nil")
        require.Len(t, fingerprints.Apps, 2, "应该有2个应用")
        require.Contains(t, fingerprints.Apps, "nginx", "应该包含nginx")
        require.Contains(t, fingerprints.Apps, "WordPress", "应该包含WordPress")
    })
}

// TestCompiledFingerprint 测试编译后的指纹
func TestCompiledFingerprint(t *testing.T) {
    t.Run("GetJSRules方法", func(t *testing.T) {
        // 创建测试用的ParsedPattern
        testPattern := &ParsedPattern{
            Regex:      nil,
            Version:    "",
            Confidence: 100,
        }

        compiled := &CompiledFingerprint{
            js: map[string]*ParsedPattern{
                "jQuery":  testPattern,
                "angular": testPattern,
            },
        }

        jsRules := compiled.GetJSRules()
        require.NotNil(t, jsRules, "JS规则不应为nil")
        require.Len(t, jsRules, 2, "应该有2个JS规则")
        require.Contains(t, jsRules, "jQuery", "应该包含jQuery")
        require.Contains(t, jsRules, "angular", "应该包含angular")
    })

    t.Run("空JS规则", func(t *testing.T) {
        compiled := &CompiledFingerprint{
            js: make(map[string]*ParsedPattern),
        }

        jsRules := compiled.GetJSRules()
        require.NotNil(t, jsRules, "空JS规则不应为nil")
        require.Empty(t, jsRules, "空JS规则应该为空")
    })
}

// TestCompileFingerprint 测试指纹编译功能
func TestCompileFingerprint(t *testing.T) {
    t.Run("编译基本指纹", func(t *testing.T) {
        fingerprint := &Fingerprint{
            Cats:        []int{1, 2},
            Implies:     []string{"PHP"},
            Description: "测试应用",
            Website:     "https://example.com",
            Icon:        "test.png",
            CPE:         "cpe:2.3:a:test:app:*:*:*:*:*:*:*:*",
            Headers:     map[string]string{"Server": "nginx"},
            JS:          map[string]string{"jQuery": ".*"},
            HTML:        []string{"<title>Test</title>"},
            Script:      []string{"var test"},
            ScriptSrc:   []string{"jquery.min.js"},
        }

        compiled := compileFingerprint(fingerprint)
        require.NotNil(t, compiled, "编译结果不应为nil")
        require.Equal(t, fingerprint.Cats, compiled.cats, "分类应该匹配")
        require.Equal(t, fingerprint.Implies, compiled.implies, "隐含技术应该匹配")
        require.Equal(t, fingerprint.Description, compiled.description, "描述应该匹配")
        require.Equal(t, fingerprint.Website, compiled.website, "网站应该匹配")
        require.Equal(t, fingerprint.Icon, compiled.icon, "图标应该匹配")
        require.Equal(t, fingerprint.CPE, compiled.cpe, "CPE应该匹配")
    })

    t.Run("编译Cookie指纹", func(t *testing.T) {
        fingerprint := &Fingerprint{
            Cookies: map[string]string{
                "PHPSESSID":      ".*",
                "laravel_session": ".*",
            },
        }

        compiled := compileFingerprint(fingerprint)
        require.NotNil(t, compiled, "编译结果不应为nil")
        require.NotNil(t, compiled.cookies, "Cookie映射不应为nil")
    })

    t.Run("编译DOM指纹", func(t *testing.T) {
        fingerprint := &Fingerprint{
            Dom: map[string]map[string]interface{}{
                "div": {
                    "exists": "",
                },
                "span": {
                    "text": "test",
                },
                "input": {
                    "attributes": map[string]interface{}{
                        "type": "hidden",
                        "name": "csrf_token",
                    },
                },
            },
        }

        compiled := compileFingerprint(fingerprint)
        require.NotNil(t, compiled, "编译结果不应为nil")
        require.NotNil(t, compiled.dom, "DOM映射不应为nil")
    })

    t.Run("编译Meta指纹", func(t *testing.T) {
        fingerprint := &Fingerprint{
            Meta: map[string][]string{
                "generator":   {"WordPress", "Drupal"},
                "description": {"Test site"},
            },
        }

        compiled := compileFingerprint(fingerprint)
        require.NotNil(t, compiled, "编译结果不应为nil")
        require.NotNil(t, compiled.meta, "Meta映射不应为nil")
    })

    t.Run("编译空指纹", func(t *testing.T) {
        fingerprint := &Fingerprint{}

        compiled := compileFingerprint(fingerprint)
        require.NotNil(t, compiled, "编译结果不应为nil")
        require.NotNil(t, compiled.dom, "DOM映射不应为nil")
        require.NotNil(t, compiled.cookies, "Cookie映射不应为nil")
        require.NotNil(t, compiled.js, "JS映射不应为nil")
        require.NotNil(t, compiled.headers, "Headers映射不应为nil")
        require.NotNil(t, compiled.html, "HTML切片不应为nil")
        require.NotNil(t, compiled.script, "Script切片不应为nil")
        require.NotNil(t, compiled.scriptSrc, "ScriptSrc切片不应为nil")
        require.NotNil(t, compiled.meta, "Meta映射不应为nil")
    })
}

// TestMatchPartResult 测试匹配结果结构
func TestMatchPartResult(t *testing.T) {
    t.Run("创建匹配结果", func(t *testing.T) {
        result := matchPartResult{
            application: "nginx",
            version:     "1.18.0",
            confidence:  95,
        }

        require.Equal(t, "nginx", result.application, "应用名应该匹配")
        require.Equal(t, "1.18.0", result.version, "版本应该匹配")
        require.Equal(t, 95, result.confidence, "置信度应该匹配")
    })

    t.Run("无版本匹配结果", func(t *testing.T) {
        result := matchPartResult{
            application: "Unknown",
            version:     "",
            confidence:  50,
        }

        require.Equal(t, "Unknown", result.application, "应用名应该匹配")
        require.Empty(t, result.version, "版本应该为空")
        require.Equal(t, 50, result.confidence, "置信度应该匹配")
    })
}

// TestCatsInfo 测试分类信息结构
func TestCatsInfo(t *testing.T) {
    t.Run("创建分类信息", func(t *testing.T) {
        catsInfo := CatsInfo{
            Cats: []int{1, 2, 22, 27},
        }

        require.NotNil(t, catsInfo.Cats, "分类列表不应为nil")
        require.Len(t, catsInfo.Cats, 4, "应该有4个分类")
        require.Contains(t, catsInfo.Cats, 1, "应该包含分类1")
        require.Contains(t, catsInfo.Cats, 22, "应该包含分类22")
    })

    t.Run("空分类信息", func(t *testing.T) {
        catsInfo := CatsInfo{
            Cats: []int{},
        }

        require.NotNil(t, catsInfo.Cats, "分类列表不应为nil")
        require.Empty(t, catsInfo.Cats, "分类列表应该为空")
    })
}

// TestFormatAppVersion 测试应用版本格式化
func TestFormatAppVersion(t *testing.T) {
    t.Run("带版本号格式化", func(t *testing.T) {
        result := FormatAppVersion("nginx", "1.18.0")
        require.Equal(t, "nginx:1.18.0", result, "应该返回带版本号的格式")
    })

    t.Run("无版本号格式化", func(t *testing.T) {
        result := FormatAppVersion("WordPress", "")
        require.Equal(t, "WordPress", result, "应该只返回应用名")
    })

    t.Run("空应用名格式化", func(t *testing.T) {
        result := FormatAppVersion("", "1.0.0")
        require.Equal(t, ":1.0.0", result, "应该返回冒号加版本号")
    })

    t.Run("都为空格式化", func(t *testing.T) {
        result := FormatAppVersion("", "")
        require.Equal(t, "", result, "应该返回空字符串")
    })

    t.Run("特殊字符处理", func(t *testing.T) {
        result := FormatAppVersion("jQuery UI", "1.12.1")
        require.Equal(t, "jQuery UI:1.12.1", result, "应该正确处理空格")
    })
}

// TestGetFingerprints 测试获取指纹数据
func TestGetFingerprints(t *testing.T) {
    t.Run("获取嵌入指纹数据", func(t *testing.T) {
        fingerprintsData := GetFingerprints()
        require.NotEmpty(t, fingerprintsData, "指纹数据不应为空")
        require.IsType(t, "", fingerprintsData, "应该返回字符串类型")
        
        // 验证是否为有效的JSON格式（基本检查）
        require.Contains(t, fingerprintsData, "{", "应该包含JSON开始标记")
        require.Contains(t, fingerprintsData, "}", "应该包含JSON结束标记")
    })
}

// TestCompiledFingerprintsMatchMapString 测试键值对匹配
func TestCompiledFingerprintsMatchMapString(t *testing.T) {
    // 创建测试用的编译指纹
    testPattern := &ParsedPattern{
        Regex:      nil,
        Version:    "1.0.0",
        Confidence: 90,
    }

    compiled := &CompiledFingerprints{
        Apps: map[string]*CompiledFingerprint{
            "nginx": {
                headers: map[string]*ParsedPattern{
                    "server": testPattern,
                },
                cookies: map[string]*ParsedPattern{
                    "nginx_session": testPattern,
                },
                meta: map[string][]*ParsedPattern{
                    "generator": {testPattern},
                },
            },
        },
    }

    t.Run("匹配Headers", func(t *testing.T) {
        headers := map[string]string{
            "server":       "nginx/1.18.0",
            "content-type": "text/html",
        }

        results := compiled.matchMapString(headers, headersPart)
        require.NotNil(t, results, "结果不应为nil")
    })

    t.Run("匹配Cookies", func(t *testing.T) {
        cookies := map[string]string{
            "nginx_session": "abc123",
            "other_cookie":  "value",
        }

        results := compiled.matchMapString(cookies, cookiesPart)
        require.NotNil(t, results, "结果不应为nil")
    })

    t.Run("匹配Meta", func(t *testing.T) {
        meta := map[string]string{
            "generator":   "Test Generator",
            "description": "Test site",
        }

        results := compiled.matchMapString(meta, metaPart)
        require.NotNil(t, results, "结果不应为nil")
    })

    t.Run("空输入匹配", func(t *testing.T) {
        results := compiled.matchMapString(map[string]string{}, headersPart)
        require.NotNil(t, results, "空输入结果不应为nil")
        require.Empty(t, results, "空输入应该返回空结果")
    })
}

// TestCompiledFingerprintsMatchString 测试字符串匹配
func TestCompiledFingerprintsMatchString(t *testing.T) {
    testPattern := &ParsedPattern{
        Regex:      nil,
        Version:    "3.6.0",
        Confidence: 85,
    }

    compiled := &CompiledFingerprints{
        Apps: map[string]*CompiledFingerprint{
            "jQuery": {
                js:        map[string]*ParsedPattern{"jQuery": testPattern},
                html:      []*ParsedPattern{testPattern},
                scriptSrc: []*ParsedPattern{testPattern},
            },
        },
    }

    t.Run("匹配JavaScript", func(t *testing.T) {
        jsCode := "var jQuery = function() {};"
        results := compiled.matchString(jsCode, jsPart)
        require.NotNil(t, results, "JS匹配结果不应为nil")
    })

    t.Run("匹配HTML", func(t *testing.T) {
        html := "<script src='jquery.min.js'></script>"
        results := compiled.matchString(html, htmlPart)
        require.NotNil(t, results, "HTML匹配结果不应为nil")
    })

    t.Run("匹配Script源", func(t *testing.T) {
        scriptSrc := "https://code.jquery.com/jquery-3.6.0.min.js"
        results := compiled.matchString(scriptSrc, scriptPart)
        require.NotNil(t, results, "Script源匹配结果不应为nil")
    })

    t.Run("空字符串匹配", func(t *testing.T) {
        results := compiled.matchString("", htmlPart)
        require.NotNil(t, results, "空字符串结果不应为nil")
        require.Empty(t, results, "空字符串应该返回空结果")
    })
}

// TestCompiledFingerprintsMatchKeyValueString 测试键值对精确匹配
func TestCompiledFingerprintsMatchKeyValueString(t *testing.T) {
    testPattern := &ParsedPattern{
        Regex:      nil,
        Version:    "5.8",
        Confidence: 95,
    }

    compiled := &CompiledFingerprints{
        Apps: map[string]*CompiledFingerprint{
            "WordPress": {
                headers: map[string]*ParsedPattern{
                    "x-powered-by": testPattern,
                },
                cookies: map[string]*ParsedPattern{
                    "wordpress_session": testPattern,
                },
                meta: map[string][]*ParsedPattern{
                    "generator": {testPattern},
                },
                implies: []string{"PHP", "MySQL"},
            },
        },
    }

    t.Run("精确匹配Header", func(t *testing.T) {
        results := compiled.matchKeyValueString("x-powered-by", "WordPress/5.8", headersPart)
        require.NotNil(t, results, "Header匹配结果不应为nil")
    })

    t.Run("精确匹配Cookie", func(t *testing.T) {
        results := compiled.matchKeyValueString("wordpress_session", "abc123", cookiesPart)
        require.NotNil(t, results, "Cookie匹配结果不应为nil")
    })

    t.Run("精确匹配Meta", func(t *testing.T) {
        results := compiled.matchKeyValueString("generator", "WordPress 5.8", metaPart)
        require.NotNil(t, results, "Meta匹配结果不应为nil")
    })

    t.Run("不匹配的键名", func(t *testing.T) {
        results := compiled.matchKeyValueString("unknown-header", "value", headersPart)
        require.NotNil(t, results, "不匹配结果不应为nil")
        require.Empty(t, results, "不匹配应该返回空结果")
    })

    t.Run("空值匹配", func(t *testing.T) {
        results := compiled.matchKeyValueString("x-powered-by", "", headersPart)
        require.NotNil(t, results, "空值匹配结果不应为nil")
    })
}