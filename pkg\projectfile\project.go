// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-17 16:00:15
// FilePath: /yaml_scan/pkg/projectfile/project.go
// Description: 
package projectfile

type ProjectFile struct {
	Path string
	hm   *hybrid.HybridMap
}


type Options struct {
	Path    string
	Cleanup bool
}


func New(options *Options) (*ProjectFile, error) {
	var p ProjectFile
	hOptions := hybrid.DefaultDiskOptions
	hOptions.Path = options.Path
	hOptions.Cleanup = options.Cleanup
	var err error
	p.hm, err = hybrid.New(hOptions)
	if err != nil {
		return nil, err
	}

	return &p, nil
}
