// 
// Author: chenjb
// Version: V1.0
// Date: 2025-05-21 15:10:57
// FilePath: /yaml_scan/utils/errkit/interfaces_test.go
// Description: 
package errkit

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/require"
)

// 测试ErrorX是否正确实现了JoinedError接口
func TestErrorX_JoinedError(t *testing.T) {
	// 创建两个基础错误
	err1 := errors.New("error1")
	err2 := errors.New("error2")
	
	// 使用Join方法将错误合并为ErrorX
	joinedErr := Join(err1, err2)
	
	// 断言joinedErr实现了JoinedError接口
	joined, ok := joinedErr.(JoinedError)
	require.True(t, ok, "joinedErr应该实现JoinedError接口")
	
	// 断言Unwrap方法返回了原始错误
	unwrapped := joined.Unwrap()
	require.Len(t, unwrapped, 2, "应该有两个基础错误")
	require.Equal(t, err1.Error(), unwrapped[0].Error(), "第一个错误应该是error1")
	require.Equal(t, err2.Error(), unwrapped[1].Error(), "第二个错误应该是error2")
}

// 测试ErrorX是否正确实现了CauseError接口
func TestErrorX_CauseError(t *testing.T) {
	// 创建一个基础错误
	originalErr := errors.New("original error")
	
	// 使用Wrap方法包装错误
	wrappedErr := Wrap(originalErr, "wrapped message")
	
	// 断言wrappedErr实现了CauseError接口
	causeErr, ok := wrappedErr.(CauseError)
	require.True(t, ok, "wrappedErr应该实现CauseError接口")
	
	// 断言Cause方法返回原始错误
	cause := causeErr.Cause()
	require.Equal(t, originalErr.Error(), cause.Error(), "原始错误应该被正确返回")
}

// 测试ErrorX是否正确实现了ComparableError接口
func TestErrorX_ComparableError(t *testing.T) {
	// 创建一个基础错误
	originalErr := errors.New("original error")
	
	// 使用New和FromError方法创建ErrorX
	newErr := New("new error")
	fromErr := FromError(originalErr)
	
	// 断言实现了ComparableError接口
	// 先将具体类型转换为error接口类型，再进行接口类型断言
	var errInterface error = newErr
	comparable1, ok := errInterface.(ComparableError)
	require.True(t, ok, "newErr应该实现ComparableError接口")
	
	errInterface = fromErr
	comparable2, ok := errInterface.(ComparableError)
	require.True(t, ok, "fromErr应该实现ComparableError接口")
	
	// 测试Is方法
	require.False(t, comparable1.Is(originalErr), "newErr不应该包含originalErr")
	require.True(t, comparable2.Is(originalErr), "fromErr应该包含originalErr")
}

// 创建一个模拟实现WrappedError接口的类型
type mockWrappedError struct {
	err error
}

// 实现Unwrap方法
func (m mockWrappedError) Unwrap() error {
	return m.err
}

func (m mockWrappedError) Error() string {
	return "mock: " + m.err.Error()
}

// 测试WrappedError接口
func TestWrappedError(t *testing.T) {
	
	
	// 创建一个基础错误
	baseErr := errors.New("base error")
	
	// 创建一个包装错误
	mockErr := mockWrappedError{err: baseErr}
	
	// 使用FromError解析包装错误
	parsed := FromError(mockErr)
	
	// 验证错误被正确解析
	require.Contains(t, parsed.Error(), baseErr.Error(), "解析后的错误应该包含基础错误")
} 

