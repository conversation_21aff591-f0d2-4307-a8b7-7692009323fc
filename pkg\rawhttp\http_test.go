// Author: chenjb
// Version: V1.0
// Date: 2025-07-25
// FilePath: /yaml_scan/pkg/rawhttp/http_test.go
// Description: rawhttp默认客户端模块单元测试

package rawhttp

import (
	"testing"

	"github.com/stretchr/testify/require"
)

// TestDefaultClient 测试默认客户端
func TestDefaultClient(t *testing.T) {
	// 验证默认客户端不为nil
	require.NotNil(t, DefaultClient, "默认客户端不应该为nil")
	
	// 验证默认客户端的拨号器
	require.NotNil(t, DefaultClient.dialer, "默认客户端的拨号器不应该为nil")
	
	// 验证默认客户端的选项
	require.NotNil(t, DefaultClient.Options, "默认客户端的选项不应该为nil")
	require.Equal(t, DefaultOptions, DefaultClient.Options, "默认客户端应该使用默认选项")
}

// TestDefaultClient_Options 测试默认客户端的选项配置
func TestDefaultClient_Options(t *testing.T) {
	// 验证默认选项的具体值
	require.Equal(t, DefaultOptions.Timeout, DefaultClient.Options.Timeout, "超时时间应该匹配")
	require.Equal(t, DefaultOptions.FollowRedirects, DefaultClient.Options.FollowRedirects, "重定向设置应该匹配")
	require.Equal(t, DefaultOptions.MaxRedirects, DefaultClient.Options.MaxRedirects, "最大重定向次数应该匹配")
	require.Equal(t, DefaultOptions.AutomaticHostHeader, DefaultClient.Options.AutomaticHostHeader, "自动Host头部设置应该匹配")
	require.Equal(t, DefaultOptions.AutomaticContentLength, DefaultClient.Options.AutomaticContentLength, "自动Content-Length设置应该匹配")
}

// TestDefaultClient_Type 测试默认客户端的类型
func TestDefaultClient_Type(t *testing.T) {
	// 验证DefaultClient是Client类型
	_, ok := interface{}(DefaultClient).(Client)
	require.True(t, ok, "DefaultClient应该是Client类型")
	
	// 验证DefaultClient不是指针类型（它是值类型）
	require.IsType(t, Client{}, DefaultClient, "DefaultClient应该是Client值类型")
}

// TestDefaultClient_Dialer 测试默认客户端的拨号器
func TestDefaultClient_Dialer(t *testing.T) {
	// 验证拨号器类型
	_, ok := DefaultClient.dialer.(*dialer)
	require.True(t, ok, "默认拨号器应该是*dialer类型")
	
	// 验证拨号器实现了Dialer接口
	_, ok = DefaultClient.dialer.(Dialer)
	require.True(t, ok, "默认拨号器应该实现Dialer接口")
}

// TestDefaultClient_Independence 测试默认客户端的独立性
func TestDefaultClient_Independence(t *testing.T) {
	// 保存原始值
	originalTimeout := DefaultClient.Options.Timeout
	originalFollowRedirects := DefaultClient.Options.FollowRedirects
	
	// 修改默认客户端的选项
	DefaultClient.Options.Timeout = originalTimeout * 2
	DefaultClient.Options.FollowRedirects = !originalFollowRedirects
	
	// 验证修改生效
	require.Equal(t, originalTimeout*2, DefaultClient.Options.Timeout, "超时时间修改应该生效")
	require.Equal(t, !originalFollowRedirects, DefaultClient.Options.FollowRedirects, "重定向设置修改应该生效")
	
	// 恢复原始值
	DefaultClient.Options.Timeout = originalTimeout
	DefaultClient.Options.FollowRedirects = originalFollowRedirects
	
	// 验证恢复成功
	require.Equal(t, originalTimeout, DefaultClient.Options.Timeout, "超时时间应该恢复")
	require.Equal(t, originalFollowRedirects, DefaultClient.Options.FollowRedirects, "重定向设置应该恢复")
}

// TestDefaultClient_Methods 测试默认客户端的方法可用性
func TestDefaultClient_Methods(t *testing.T) {
	// 验证DefaultClient具有所有必要的方法
	// 这些方法调用会因为网络连接失败而返回错误，但不应该panic
	
	// 测试Head方法存在
	_, err := DefaultClient.Head("http://example.com")
	require.Error(t, err, "在测试环境中Head方法应该返回连接错误")
	
	// 测试Get方法存在
	_, err = DefaultClient.Get("http://example.com")
	require.Error(t, err, "在测试环境中Get方法应该返回连接错误")
	
	// 测试Post方法存在
	_, err = DefaultClient.Post("http://example.com", "text/plain", nil)
	require.Error(t, err, "在测试环境中Post方法应该返回连接错误")
	
	// 测试DoRaw方法存在
	_, err = DefaultClient.DoRaw("GET", "http://example.com", "", nil, nil)
	require.Error(t, err, "在测试环境中DoRaw方法应该返回连接错误")
	
	// 测试Close方法存在且不会panic
	require.NotPanics(t, func() {
		DefaultClient.Close()
	}, "Close方法不应该panic")
}

// TestDefaultClient_Singleton 测试默认客户端的单例性质
func TestDefaultClient_Singleton(t *testing.T) {
	// 获取默认客户端的引用
	client1 := &DefaultClient
	client2 := &DefaultClient
	
	// 验证它们指向同一个对象
	require.Equal(t, client1, client2, "默认客户端应该是单例")
	
	// 修改一个引用，另一个也应该改变
	originalTimeout := client1.Options.Timeout
	client1.Options.Timeout = originalTimeout * 3
	
	require.Equal(t, client1.Options.Timeout, client2.Options.Timeout, "修改应该在所有引用中生效")
	
	// 恢复原始值
	client1.Options.Timeout = originalTimeout
}
