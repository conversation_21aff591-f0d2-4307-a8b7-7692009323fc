//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-04 17:44:56
//FilePath: /yaml_scan/utils/conn/inflight_test.go
//Description:

package connpool

import (
	"net"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// MockConn 是一个实现 net.Conn 接口的模拟连接，用于测试。
type MockConn struct {
	closed bool
}

func (m *MockConn) Close() error {
	m.closed = true
	return nil
}

func (m *MockConn) Read(b []byte) (n int, err error) {
	return 0, nil
}

func (m *MockConn) Write(b []byte) (n int, err error) {
	return 0, nil
}

func (m *MockConn) LocalAddr() net.Addr {
	return nil
}

func (m *MockConn) RemoteAddr() net.Addr {
	return nil
}

func (m *MockConn) SetDeadline(t time.Time) error {
	return nil
}

func (m *MockConn) SetReadDeadline(t time.Time) error {
	return nil
}

func (m *MockConn) SetWriteDeadline(t time.Time) error {
	return nil
}

// TestInFlightConns 测试 InFlightConns 的基本功能。
func TestInFlightConns(t *testing.T) {
	inFlightConns, err := NewInFlightConns()
	require.NoError(t, err)

	// 创建一个 MockConn 实例
	mockConn := &MockConn{}

	// 测试添加连接
	inFlightConns.Add(mockConn)

	// 测试移除连接
	inFlightConns.Remove(mockConn)

	// 测试 Close 方法
	inFlightConns.Add(mockConn) // 重新添加连接
	err = inFlightConns.Close()
	require.NoError(t, err)
	require.True(t, mockConn.closed, "Expected connection to be closed")
}
