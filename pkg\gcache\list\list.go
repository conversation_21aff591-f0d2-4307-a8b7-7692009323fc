//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-20 16:23:18
//FilePath: /yaml_scan/pkg/gcache/list/list.go
//Description: 双链表实现

package list

// Element 是双向链表中的一个元素。
type Element[T any] struct {
	// next 和 prev 是链表中的前后指针
	// 为了简化实现，内部将链表 l 实现为一个环形结构，
	// 这样 &l.root 既是最后一个元素（l.Back()）的 next，
	// 也是第一个元素（l.Front()）的 prev
	next, prev *Element[T]

	// 该元素所属的链表。
	list *List[T]

	// 存储在该元素中的值。
	Value T
}

// List 表示一个双向链表。
// list 的零值是一个空链表，随时可以使用。
type List[T any] struct {
	root Element[T] // 哨兵元素，仅使用 &root、root.prev 和 root.next
	len  int        // 当前链表长度，不包括哨兵元素
}

// Next 返回下一个链表元素或 nil。
func (e *Element[T]) Next() *Element[T] {
	// 如果下一个元素存在且不是根元素，则返回下一个元素
	if p := e.next; e.list != nil && p != &e.list.root {
		return p
	}
	return nil
}

// Prev 返回链表中的上一个元素，如果没有则返回 nil
func (e *Element[T]) Prev() *Element[T] {
	if p := e.prev; e.list != nil && p != &e.list.root {
		return p // 返回上一个元素
	}
	return nil // 如果没有上一个元素，返回 nil
}

// Init 初始化或清空链表 l。
func (l *List[T]) Init() *List[T] {
	l.root.next = &l.root // 将根元素的下一个指针指向自身，形成环形结构
	l.root.prev = &l.root // 将根元素的前一个指针指向自身 形成环形结构
	l.len = 0             // 将链表长度设置为 0
	return l
}

// New 返回一个初始化的链表。
func New[T any]() *List[T] { return new(List[T]).Init() }

// Front 返回链表 l 的第一个元素，如果链表为空则返回 nil。
func (l *List[T]) Front() *Element[T] {
	if l.len == 0 {
		return nil
	}
	return l.root.next
}

// Back 返回链表 l 的最后一个元素，如果链表为空则返回 nil。
func (l *List[T]) Back() *Element[T] {
	if l.len == 0 {
		return nil
	}
	return l.root.prev
}

// lazyInit 惰性初始化链表的零值
func (l *List[T]) lazyInit() {
	if l.root.next == nil {
		l.Init()
	}
}

// insert 在元素 at 之后插入元素 e，增加 l.len，并返回 e。
func (l *List[T]) insert(e, at *Element[T]) *Element[T] {
	e.prev = at      // 将 e 的前一个元素设置为 at
	e.next = at.next // 将 e 的下一个元素设置为 at 的下一个元素
	e.prev.next = e  // 将 at 的 next 指向 e
	e.next.prev = e  // 将 e 的下一个元素的 prev 指向 e
	e.list = l       // 设置 e 所属的链表为 l
	l.len++          // 增加链表的长度
	return e
}

// insertValue 是 insert(&Element{Value: v}, at) 的便捷包装，用于将值 v 插入到指定的元素 at 之前。
func (l *List[T]) insertValue(v T, at *Element[T]) *Element[T] {
	return l.insert(&Element[T]{Value: v}, at)
}

// remove 从链表中移除元素 e，减少长度。
func (l *List[T]) remove(e *Element[T]) {
	e.prev.next = e.next // 将 e 的前一个元素的 next 指向 e 的下一个元素
	e.next.prev = e.prev // 将 e 的下一个元素的 prev 指向 e 的前一个元素
	e.next = nil         // 避免内存泄漏
	e.prev = nil         // 避免内存泄漏
	e.list = nil         // 清除元素的链表引用
	l.len--              // 减少链表的长度
}

// move 将元素 e 移动到元素 at 之后。
func (l *List[T]) move(e, at *Element[T]) {
	// 如果 e 和 at 是同一个元素，则不需要移动
	if e == at {
		return
	}

	e.prev.next = e.next // 将 e 的前一个元素的 next 指向 e 的下一个元素   断开 e 的前向连接
	e.next.prev = e.prev // 将 e 的下一个元素的 prev 指向 e 的前一个元素   断开 e 的后向连接

	// 将 e 插入到 at 之后
	e.prev = at      // 将 e 的前一个元素设置为 at
	e.next = at.next // 将 e 的下一个元素设置为 at 的下一个元素
	e.prev.next = e  // 将 at 的 next 指向 e
	e.next.prev = e  // 将 e 的下一个元素的 prev 指向 e
}

// Remove 从链表 l 中移除元素 e（如果 e 是链表 l 的元素）。
// 返回元素的值 e.Value。
// 元素 e 不能为空。
func (l *List[T]) Remove(e *Element[T]) any {
	if e.list == l {
		// 如果 e 是链表 l 的元素，调用 remove 方法移除 e
		l.remove(e)
	}
	return e.Value
}

// PushFront 在链表的前面插入一个新元素 e，值为 v，并返回 e。
func (l *List[T]) PushFront(v T) *Element[T] {
	// 惰性初始化链表
	l.lazyInit()
	// 在根节点之后插入
	return l.insertValue(v, &l.root)
}

// PushBack 在链表 l 的后端插入一个新元素 e，值为 v，并返回 e
func (l *List[T]) PushBack(v T) *Element[T] {
	l.lazyInit()                         // 惰性初始化链表
	return l.insertValue(v, l.root.prev) // 在最后一个元素之后插入
}

// InsertBefore 在 mark 之前插入一个新元素 e，值为 v，并返回 e
// 如果 mark 不是 l 的元素，则不修改链表
// mark 不能为 nil
func (l *List[T]) InsertBefore(v T, mark *Element[T]) *Element[T] {
	if mark.list != l {
		return nil // 如果 mark 不属于 l，返回 nil
	}
	return l.insertValue(v, mark.prev) // 在 mark 的前一个元素之后插入
}

// InsertAfter 在 mark 之后插入一个新元素 e，值为 v，并返回 e
// 如果 mark 不是 l 的元素，则不修改链表
// mark 不能为 nil
func (l *List[T]) InsertAfter(v T, mark *Element[T]) *Element[T] {
	// 检查 mark 是否属于当前链表
	if mark.list != l {
		return nil
	}

	return l.insertValue(v, mark)
}

// MoveToFront 将元素 e 移动到链表 l 的前面。
// 如果 e 不是 l 的元素，则不修改链表。
// 元素 e 不能为空。
func (l *List[T]) MoveToFront(e *Element[T]) {

	// 如果 e 不属于链表 l 或者 e 已经在前面，则不需要移动
	if e.list != l || l.root.next == e {
		return
	}
	l.move(e, &l.root)
}

// MoveToBack 将元素 e 移动到链表 l 的后端
// 如果 e 不是 l 的元素，则不修改链表
// 元素 e 不能为 nil
func (l *List[T]) MoveToBack(e *Element[T]) {
	if e.list != l || l.root.prev == e {
		return // 如果 e 不属于 l 或已在后端，无需移动
	}
	l.move(e, l.root.prev) // 移动 e 到最后一个元素之后
}

// MoveBefore 将元素 e 移动到 mark 之前
// 如果 e 或 mark 不是 l 的元素，或者 e == mark，则不修改链表
// 元素 e 和 mark 不能为 nil
func (l *List[T]) MoveBefore(e, mark *Element[T]) {
	if e.list != l || e == mark || mark.list != l {
		return // 如果 e 或 mark 不属于 l，或 e 和 mark 相同，无需移动
	}
	l.move(e, mark.prev) // 移动 e 到 mark 的前一个元素之后
}

// MoveAfter 将元素 e 移动到 mark 之后
// 如果 e 或 mark 不是 l 的元素，或者 e == mark，则不修改链表
// 元素 e 和 mark 不能为 nil
func (l *List[T]) MoveAfter(e, mark *Element[T]) {
	if e.list != l || e == mark || mark.list != l {
		return // 如果 e 或 mark 不属于 l，或 e 和 mark 相同，无需移动
	}
	l.move(e, mark) // 移动 e 到 mark 之后
}

// PushBackList 在链表 l 的后端插入另一个链表 other 的副本
// 链表 l 和 other 可以是同一个，但都不能为 nil
func (l *List[T]) PushBackList(other *List[T]) {
	l.lazyInit() // 惰性初始化链表
	for i, e := other.Len(), other.Front(); i > 0; i, e = i-1, e.Next() {
		l.insertValue(e.Value, l.root.prev) // 从 other 的前端开始逐个插入到 l 的后端
	}
}

// PushFrontList 在链表 l 的前端插入另一个链表 other 的副本
// 链表 l 和 other 可以是同一个，但都不能为 nil
func (l *List[T]) PushFrontList(other *List[T]) {
	l.lazyInit() // 惰性初始化链表
	for i, e := other.Len(), other.Back(); i > 0; i, e = i-1, e.Prev() {
		l.insertValue(e.Value, &l.root) // 从 other 的后端开始逐个插入到 l 的前端
	}
}

// Len 返回链表 l 中元素的数量。
// 复杂度为 O(1)。
func (l *List[T]) Len() int { return l.len }
