// Author: chenjb
// Version: V1.0
// Date: 2025-06-18 16:09:32
// FilePath: /yaml_scan/pkg/clistats/clistats.go
// Description: CLI统计信息包，提供终端统计数据收集、展示和Web接口功能
package clistats

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"sync/atomic"
	"time"
	"yaml_scan/pkg/utils/freeport"

	jsoniter "github.com/json-iterator/go"
)

// DynamicCallback 是动态字段计算时调用的回调函数类型
//
// 从此回调函数返回的值将显示为动态字段的当前值。
// 这可用于计算诸如经过时间、每秒请求数等统计信息。
type DynamicCallback func(client StatisticsClient) interface{}

// StatisticsClient 是统计客户端实现的接口
//
// 需要为要显示的字段提供唯一ID和描述。
//
// 提供了多种类型的统计数据，如计数器和静态字段，
// 静态字段仅显示静态信息。
//
// 一旦客户端启动，就不能再添加指标。如果无法添加指标，
// 将返回错误。同名的已有字段将被覆盖。
type StatisticsClient interface {
	// Start 启动统计客户端的事件循环
	Start() error
	// Stop 停止统计客户端的事件循环
	Stop() error

	// AddCounter 向统计客户端添加uint64计数器字段
	//
	// 计数器用于跟踪增加的数量，如请求数、错误数等
	AddCounter(id string, value uint64)

	// GetCounter 返回计数器的当前值
	GetCounter(id string) (uint64, bool)

	// IncrementCounter 将计数器的值增加指定的数量
	IncrementCounter(id string, count int)

	// AddStatic 向统计信息添加静态信息字段
	//
	// 这些指标的值在统计客户端的生命周期内将保持不变。
	// 所有值都将转换为字符串并显示。
	AddStatic(id string, value interface{})

	// GetStatic 返回静态字段的原始值
	GetStatic(id string) (interface{}, bool)

	// AddDynamic 添加一个动态字段，其值通过运行回调函数获取
	//
	// 回调函数执行一些操作并返回要显示的值。
	// 通常用于计算每秒请求数、经过时间等。
	AddDynamic(id string, Callback DynamicCallback)

	// GetDynamic 返回用于数据检索的动态字段回调
	GetDynamic(id string) (DynamicCallback, bool)

	// GetStatResponse 返回给定时间间隔的'/metrics'响应
	GetStatResponse(interval time.Duration, callback func(string, error) error)
}

// Statistics 是用于在标准输出上显示统计信息的客户端
type Statistics struct {
	Options *Options           // 配置选项
	ctx     context.Context    // 上下文，用于控制生命周期
	cancel  context.CancelFunc // 取消函数，用于停止服务

	// counters 是客户端的计数器列表。这些计数器只能
	// 通过原子操作并发访问，并且一旦主事件循环启动
	// 就不能修改。
	counters map[string]*atomic.Uint64

	// static 包含客户端的静态计数器列表
	static map[string]interface{}

	// dynamic 包含客户端的动态指标列表
	dynamic map[string]DynamicCallback

	httpServer *http.Server // HTTP服务器，用于提供指标API
}

// 确保Statistics实现了StatisticsClient接口
var _ StatisticsClient = (*Statistics)(nil)

// New 使用默认选项创建一个新的CLI统计信息客户端
// @return *Statistics *Statistics: 统计客户端实例
// @return error error:
func New() (*Statistics, error) {
	return NewWithOptions(context.Background(), &DefaultOptions)
}

// NewWithOptions 使用自定义选项创建一个新的客户端
// @param ctx context.Context: 上下文，用于控制生命周期
// @param options *Options: 自定义配置
// @return *Statistics *Statistics: 统计客户端实例
// @return error error:
func NewWithOptions(ctx context.Context, options *Options) (*Statistics, error) {
	ctx, cancel := context.WithCancel(ctx)

	statistics := &Statistics{
		Options:  options,
		ctx:      ctx,
		cancel:   cancel,
		counters: make(map[string]*atomic.Uint64),
		static:   make(map[string]interface{}),
		dynamic:  make(map[string]DynamicCallback),
	}
	return statistics, nil
}

// metricsHandler 处理HTTP指标请求，返回JSON格式的统计数据
// @receiver s
// @param w http.ResponseWriter:  HTTP响应写入器
// @param req *http.Request: HTTP请求
func (s *Statistics) metricsHandler(w http.ResponseWriter, req *http.Request) {
	// 创建返回项目的映射
	items := make(map[string]interface{})

	// 添加所有计数器的当前值
	for k, v := range s.counters {
		items[k] = v.Load()
	}

	// 添加所有静态值
	for k, v := range s.static {
		items[k] = v
	}

	// 通过回调函数获取并添加所有动态值
	for k, v := range s.dynamic {
		items[k] = v(s)
	}

	// 处理常见字段
	requests, hasRequests := s.GetCounter("requests")   // 获取请求计数
	startedAt, hasStartedAt := s.GetStatic("startedAt") // 获取开始时间
	total, hasTotal := s.GetCounter("total")            // 获取总计数
	var (
		duration    time.Duration
		hasDuration bool
	)
	// 计算持续时间
	if hasStartedAt {
		if stAt, ok := startedAt.(time.Time); ok {
			duration = time.Since(stAt)
			// 格式化并添加持续时间
			items["duration"] = FmtDuration(duration)
			hasDuration = true
		}
	}

	// 计算每秒请求数(RPS)
	if hasRequests && hasDuration {
		items["rps"] = String(uint64(float64(requests) / duration.Seconds()))
	}
	// 计算百分比完成度
	if hasRequests && hasTotal {
		percentData := (float64(requests) * float64(100)) / float64(total)
		percent := String(uint64(percentData))
		items["percent"] = percent
	}

	// 将数据转换为JSON格式
	data, err := jsoniter.Marshal(items)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		_, _ = w.Write([]byte(fmt.Sprintf(`{"error":"%s"}`, err)))
		return
	}
	_, _ = w.Write(data)
}

// Start  启动统计客户端的事件循环
// @receiver s
// @return error error:
func (s *Statistics) Start() error {
	// 检查服务器是否已经启动
	if s.httpServer != nil {
		return errors.New("server already started")
	}

	// 如果启用了Web选项，启动HTTP服务器
	if s.Options.Web {
		mux := http.NewServeMux()
		mux.HandleFunc("/metrics", s.metricsHandler)

		// 检查默认端口是否可用
		port, err := freeport.GetPort(freeport.TCP, "127.0.0.1", s.Options.ListenPort)
		if err != nil {
			// 如果不可用，选择一个随机端口并更新选项
			port, err = freeport.GetFreeTCPPort("127.0.0.1")
			if err != nil {
				return err
			}
			s.Options.ListenPort = port.Port
		}

		// 创建HTTP服务器
		s.httpServer = &http.Server{
			Addr:    fmt.Sprintf("%s:%d", port.Address, port.Port),
			Handler: mux,
		}

		errChan := make(chan error, 1)
		var done atomic.Bool

		// 启动HTTP服务器
		go func() {
			if err := s.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed && !done.Load() {
				errChan <- err
			}
		}()

		// 捕获初始致命错误
		select {
		case err := <-errChan:
			return err
		case <-time.After(250 * time.Millisecond):
			// 等待250毫秒，如果没有错误则认为启动成功
			done.Store(true)
			close(errChan)
		}

	}
	return nil
}

// Stop 停止统计客户端的事件循环
// @receiver s
// @return error error:
func (s *Statistics) Stop() error {
	defer s.cancel()

	// 如果HTTP服务器存在，关闭它
	if s.httpServer != nil {
		if err := s.httpServer.Shutdown(s.ctx); err != nil {
			return err
		}
	}
	s.httpServer = nil

	return nil
}

// GetStatResponse 按给定的时间间隔获取并返回'/metrics'响应
// @receiver s 
// @param interval time.Duration:  获取指标的时间间隔
// @param callback func(string, error) error: 处理获取到的指标数据的回调函数
func (s *Statistics) GetStatResponse(interval time.Duration, callback func(string, error) error) {
	// 定义获取指标的回调函数
	metricCallback := func(url string) (string, error) {
		// 发送HTTP GET请求获取指标
		response, err := http.Get(url)
		if err != nil {
			return "", fmt.Errorf("Error getting /metrics response: %v", err)
		}
		defer func() {
			_ = response.Body.Close()
		}()
		// 读取响应体
		body, err := io.ReadAll(response.Body)
		if err != nil {
			return "", fmt.Errorf("Error reading /metrics response body: %v", err)
		}
		return string(body), nil
	}

	// 构建指标URL
	url := fmt.Sprintf("http://127.0.0.1:%v/metrics", s.Options.ListenPort)
	go func() {
		// 创建定时器
		ticker := time.NewTicker(interval)
		defer ticker.Stop()
		for {
			select {
			case <-s.ctx.Done():
				return
			case <-ticker.C:
				 // 定时器触发时，获取并处理指标
				if err := callback(metricCallback(url)); err != nil {
					return
				}
			}
		}
	}()
}
