//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-15 14:48:29
//FilePath: /yaml_scan/pkg/input/formats/burp/burp_test.go
//Description: burp 单测

package burp

import (
	"testing"

	"yaml_scan/pkg/input/types"

	"github.com/stretchr/testify/require"
)

func TestBurpParse(t *testing.T) {
	format := New()

	proxifyInputFile := "../testdata/burp.xml"

	var gotMethodsToURLs []string

	err := format.Parse(proxifyInputFile, func(request *types.RequestResponse) bool {
		gotMethodsToURLs = append(gotMethodsToURLs, request.URL.String())
		return false
	})
	if err != nil {
		t.Fatal(err)
	}

	if len(gotMethodsToURLs) != 2 {
		t.Fatalf("invalid number of methods: %d", len(gotMethodsToURLs))
	}
	var expectedURLs = []string{
		"http://localhost:8087/scans",
		"http://google.com/",
	}
	require.ElementsMatch(t, expectedURLs, gotMethodsToURLs, "could not get burp urls")
}
