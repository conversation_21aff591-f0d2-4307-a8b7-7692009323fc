//
// Author: chenjb
// Version: V1.0
// Date: 2025-07-22 17:31:15
// FilePath: /yaml_scan/pkg/rawhttp/client/writer.go
// Description: HTTP请求写入器实现，提供分阶段的HTTP请求构建和发送功能

// Package client HTTP请求写入模块
package client

import (
	"bufio"              // 缓冲I/O包
	"fmt"                // 格式化输出包
	"io"                 // 输入输出接口包
	"net/http/httputil"  // HTTP工具包
	"strings"            // 字符串处理包
)

// NewLine HTTP协议使用的换行符（CRLF）
// 符合HTTP/1.1规范要求的行结束符
const NewLine = "\r\n"

// phase 表示HTTP请求写入的当前阶段
// 用于确保HTTP请求按正确顺序构建
type phase int

// HTTP请求写入阶段常量定义
const (
	requestline phase = iota // 请求行阶段（方法 路径 版本）
	header                   // 头部阶段（键值对形式的头部信息）
	body                     // 请求体阶段（实际的请求内容）
)

// String 返回阶段的字符串表示
// 返回:
//   string: 阶段名称字符串
// 功能: 实现fmt.Stringer接口，用于调试和错误信息
func (p phase) String() string {
	switch p {
	case requestline:
		return "requestline" // 请求行阶段
	case header:
		return "headers"     // 头部阶段
	case body:
		return "body"        // 请求体阶段
	default:
		return "UNKNOWN"     // 未知阶段
	}
}

// phaseError 阶段错误类型
// 当在错误的阶段调用方法时产生的错误
type phaseError struct {
	expected, got phase // 期望的阶段和实际的阶段
}

// Error 实现error接口，返回阶段错误的描述
// 返回:
//   string: 错误描述字符串
// 功能: 提供详细的阶段错误信息
func (p *phaseError) Error() string {
	return fmt.Sprintf("phase error: expected %s, got %s", p.expected, p.got)
}

// writer HTTP请求写入器结构体
// 管理HTTP请求的分阶段写入过程
type writer struct {
	phase              // 当前写入阶段
	io.Writer          // 底层写入器
	tmp io.Writer      // 临时写入器，在头部阶段用于保存原始写入器
}

// StartHeaders 将连接移动到头部阶段
// 功能: 设置当前阶段为头部写入阶段
func (w *writer) StartHeaders() { w.phase = header }

// WriteRequestLine 写入请求行并将连接移动到头部阶段
// 参数:
//   method: HTTP请求方法（如GET、POST等）
//   path: 请求路径
//   query: 查询参数数组
//   version: HTTP版本字符串
// 返回:
//   error: 写入错误，成功时为nil
// 功能: 构建并写入HTTP请求行，格式为"方法 路径?查询参数 版本\r\n"
func (w *writer) WriteRequestLine(method, path string, query []string, version string) error {
	// 检查当前阶段是否正确
	if w.phase != requestline {
		return &phaseError{requestline, w.phase}
	}
	// 构建查询字符串
	q := strings.Join(query, "&")
	if len(q) > 0 {
		q = "?" + q // 添加查询字符串前缀
	}
	// 设置缓冲写入器以提高性能
	w.tmp, w.Writer = w.Writer, bufio.NewWriter(w.Writer)
	// 写入请求行
	_, err := fmt.Fprintf(w, "%s %s%s %s\r\n", method, path, q, version)
	w.StartHeaders() // 移动到头部阶段
	return err
}

// WriteHeader 将标准格式的头部写入网络
// 参数:
//   key: 头部名称
//   value: 头部值
// 返回:
//   error: 写入错误，成功时为nil
// 功能: 写入HTTP头部，支持有值和无值的头部格式
func (w *writer) WriteHeader(key, value string) error {
	// 检查当前阶段是否正确
	if w.phase != header {
		return &phaseError{header, w.phase}
	}
	var err error
	if value != "" {
		// 有值的头部：键: 值\r\n
		_, err = fmt.Fprintf(w, "%s: %s\r\n", key, value)
	} else {
		// 无值的头部：键\r\n
		_, err = fmt.Fprintf(w, "%s\r\n", key)
	}

	return err
}

// StartBody 将连接移动到请求体阶段，此时不能再发送头部
// 返回:
//   error: 写入错误，成功时为nil
// 功能: 结束头部阶段，写入空行分隔符，开始请求体阶段
func (w *writer) StartBody() error {
	// 写入空行分隔头部和请求体
	if _, err := w.Write([]byte(NewLine)); err != nil {
		return err
	}
	// 刷新缓冲区确保头部数据被发送
	err := w.Writer.(*bufio.Writer).Flush()
	// 恢复原始写入器，清理临时写入器
	w.Writer, w.tmp = w.tmp, nil
	w.phase = body // 移动到请求体阶段
	return err
}

// WriteBody 将读取器r的内容写入网络
// 参数:
//   r: 包含请求体内容的读取器
// 返回:
//   error: 写入错误，成功时为nil
// 功能: 写入完整的请求体内容，完成后重置到请求行阶段
func (w *writer) WriteBody(r io.Reader) error {
	// 检查当前阶段是否正确
	if w.phase != body {
		return &phaseError{body, w.phase}
	}
	// 将读取器内容复制到写入器
	_, err := io.Copy(w, r)
	w.phase = requestline // 重置到请求行阶段，准备下一个请求
	return err
}

// WriteChunked 以分块格式将读取器r的内容写入网络
// 参数:
//   r: 包含请求体内容的读取器
// 返回:
//   error: 写入错误，成功时为nil
// 功能: 使用HTTP分块传输编码写入请求体，适用于大文件或流式数据
func (w *writer) WriteChunked(r io.Reader) error {
	// 检查当前阶段是否正确
	if w.phase != body {
		return &phaseError{body, w.phase}
	}
	// 创建分块写入器
	cw := httputil.NewChunkedWriter(w)
	// 将读取器内容以分块格式写入
	if _, err := io.Copy(cw, r); err != nil {
		return nil // 注意：这里原代码返回nil可能是错误，应该返回err
	}
	w.phase = requestline // 重置到请求行阶段
	return cw.Close()     // 关闭分块写入器，发送结束标记
}

