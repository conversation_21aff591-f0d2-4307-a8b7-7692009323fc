//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-19 14:51:11
//FilePath: /yaml_scan/pkg/input/formats/formats.go
//Description:

package formats

import (
	"errors"
	"os"
	"yaml_scan/pkg/input/types"

	"gopkg.in/yaml.v2"
)

// InputFormatOptions 包含输入的选项
// 这些可以是可以传递的变量、覆盖项或其他选项
// 提供灵活的请求生成和格式验证控制。
type InputFormatOptions struct {
	// Variables 是一个变量列表，可以在生成请求时使用
	// 以给定格式生成请求
	Variables map[string]interface{}
	// SkipFormatValidation 用于在调试或测试时跳过格式验证
	// 如果格式无效，则请求将被跳过，而不是创建无效请求
	SkipFormatValidation bool
	// RequiredOnly 仅在生成请求时使用必需字段
	// 而不是所有字段
	RequiredOnly bool
}

// OpenAPIParamsCfgFile 表示OpenAPI参数配置文件的结构
type OpenAPIParamsCfgFile struct {
	Var          []string `yaml:"var"` // 含必需变量的字符串切片。这些变量会被序列化为YAML格式。
	OptionalVars []string `yaml:"-"`   // 包含可选变量的字符串切片。这些变量不会被序列化为YAML格式，而是作为注释写入文件。
}

var (
	DefaultVarDumpFileName = "required_openapi_params.yaml"
	ErrNoVarsDumpFile      = errors.New("no required params file found")
)

// ParseReqRespCallback 是一个回调函数，用于处理发现的原始请求
type ParseReqRespCallback func(rr *types.RequestResponse) bool

// Format 一个接口，由所有输入格式实现
type Format interface {
	// Name 返回格式的名称
	Name() string
	// Parse 解析输入并调用提供的回调函数
	// 对于每个发现的原始请求，它都会调用该函数。
	Parse(input string, resultsCb ParseReqRespCallback) error
	// SetOptions 设置输入格式的选项
	SetOptions(options InputFormatOptions)
}

// WriteOpenAPIVarDumpFile:  将OpenAPI参数配置写入文件
//
//	@param vars *OpenAPIParamsCfgFile: 包含要写入文件的必需和可选变量。
//	@return error error:如果写入过程中出现错误，返回相应的错误；否则返回nil。
func WriteOpenAPIVarDumpFile(vars *OpenAPIParamsCfgFile) error {
	// 打开或创建文件，权限为0644
	f, err := os.OpenFile(DefaultVarDumpFileName, os.O_RDWR|os.O_CREATE|os.O_TRUNC, 0644)
	if err != nil {
		return err
	}
	defer f.Close()
	// 将必需变量序列化为YAML格式
	bin, err := yaml.Marshal(vars)
	if err != nil {
		return err
	}
	// 将序列化后的YAML数据写入文件
	_, _ = f.Write(bin)
	// 如果存在可选变量，将它们作为注释写入文件
	if len(vars.OptionalVars) > 0 {
		_, _ = f.WriteString("\n    # Optional parameters\n")
		for _, v := range vars.OptionalVars {
			_, _ = f.WriteString("    # - " + v + "=\n")
		}
	}
	return f.Sync()
}
