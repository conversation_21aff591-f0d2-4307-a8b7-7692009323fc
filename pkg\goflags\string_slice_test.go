package goflags


import (
	"testing"
)


// MockOptions 是一个模拟的选项类型，用于测试。
var MockOptions = Options{
	IsEmpty: func(s string) bool { return s == "" },
	Normalize: func(s string) string { return s },
}


// TestSet 测试 StringSlice 的 Set 方法
func TestSet(t *testing.T) {
	// 初始化测试数据
	optionMap = make(map[*StringSlice]Options)
	optionDefaultValues = make(map[*StringSlice][]string)

	// 创建一个 StringSlice 实例
	var ss StringSlice

	// 将选项与 StringSlice 关联
	optionMap[&ss] = MockOptions
	optionDefaultValues[&ss] = []string{"default"}

	// 测试正常添加值
	err := ss.Set("value1")
	if err != nil {
		t.Fatalf("Set failed: %v", err)
	}
	if len(ss) != 1 || ss[0] != "value1" {
		t.Errorf("Expected ss to be [value1], got %v", ss)
	}

	// 测试添加多个值
	err = ss.Set("value2,value3")
	if err != nil {
		t.Fatalf("Set failed: %v", err)
	}
	if len(ss) != 3 || ss[1] != "value2" || ss[2] != "value3" {
		t.Errorf("Expected ss to be [value1 value2 value3], got %v", ss)
	}

	// 测试与默认值相同的情况
	ss = StringSlice{"default"}
	err = ss.Set("default")
	if err != nil {
		t.Fatalf("Set failed: %v", err)
	}
	if len(ss) != 1 || ss[0] != "default" {
		t.Errorf("Expected ss to be [default], got %v", ss)
	}

	// 测试错误情况
	err = ss.Set("")
	if err == nil {
		t.Error("Expected error for empty value, got nil")
	}
}