// Author: chenjb
// Version: V1.0
// Date: 2025-04-30 15:42:41
// FilePath: /yaml_scan/pkg/gcache/simple.go
// Description:
package gcache

import (
	"time"
)

// simpleItem 表示 SimpleCache 中的单个缓存条目
type simpleItem[V any] struct {
	clock      Clock      // 时钟接口，用于获取时间
	value      V          // 缓存的值
	expiration *time.Time // 过期时间
}

// IsExpired  检查缓存条目是否已过期
// @param now: 当前时间，如果为 nil 则使用条目的时钟获取
// @return bool: 如果条目已过期，返回 true
func (si *simpleItem[V]) IsExpired(now *time.Time) bool {
	// 如果没有过期时间 返回未过期
	if si.expiration == nil {
		return false
	}
	// 如果未提供当前时间 使用条目的时钟获取时间
	if now == nil {
		t := si.clock.Now()
		now = &t
	}
	return si.expiration.Before(*now)
}

// SimpleCache 是一个简单的缓存实现，没有明确的驱逐优先级，依赖于键值映射的顺序
type SimpleCache[K comparable, V any] struct {
	baseCache[K, V]                      // 基础缓存结构体，包含通用字段和方法
	items           map[K]*simpleItem[V] // 存储键值对的映射

}

// newSimpleCache 创建一个新的 SimpleCache 实例
// @param cb *CacheBuilder:  CacheBuilder 实例，包含缓存配置
// @return *SimpleCache *SimpleCache: 新创建的 SimpleCache 实例
func newSimpleCache[K comparable, V any](cb *CacheBuilder[K, V]) *SimpleCache[K, V] {
	// 创建 SimpleCache 实例
	c := &SimpleCache[K, V]{}
	// 初始化基础缓存字段
	buildCache(&c.baseCache, cb)

	// 初始化 items 映射
	c.init()
	// 设置 loadGroup 的缓存实例
	c.loadGroup.cache = c
	return c
}

// init 初始化 SimpleCache 的 items 映射
func (c *SimpleCache[K, V]) init() {
	if c.size <= 0 {
		c.items = make(map[K]*simpleItem[V])
	} else {
		c.items = make(map[K]*simpleItem[V], c.size)
	}
}

// remove 内部方法，删除指定的键
// @param key: 要删除的键
// @return bool: 如果键存在并被删除，返回 true
func (c *SimpleCache[K, V]) remove(key K) bool {
	// 检查键是否存在
	item, ok := c.items[key]
	if ok {
		delete(c.items, key)
		// 如果有逐出回调
		if c.evictedFunc != nil {
			c.evictedFunc(key, item.value)
		}
		return true
	}
	// 键不存在，返回 false
	return false
}

// evict 驱逐指定数量的缓存条目
// @param count: 要驱逐的条目数量
func (c *SimpleCache[K, V]) evict(count int) {
	// 获取当前时间
	now := c.clock.Now()
	current := 0
	// 遍历缓存条目
	for key, item := range c.items {
		// 如果已驱逐足够数量 退出
		if current >= count {
			return
		}
		// 如果条目无过期时间或已过期 则删除
		if item.expiration == nil || now.After(*item.expiration) {
			defer c.remove(key)
			current++
		}
	}
}

// set  内部方法，用于设置键值对
// @param key: 要设置的键
// @param value: 对应的值
// @return *simpleItem[V]: 设置的缓存条目
// @return error: 操作失败时的错误
func (c *SimpleCache[K, V]) set(key K, value V) (*simpleItem[V], error) {
	var err error
	// 如果有序列化函数 则先序列化一下
	if c.serializeFunc != nil {
		value, err = c.serializeFunc(key, value)
		if err != nil {
			return nil, err
		}
	}

	// 检查是否已有条目
	item, ok := c.items[key]
	// 如果键已存在 更新一下值
	if ok {
		item.value = value
	} else {
		// 检查是否超过容量 如果超过容量 删除一个
		if (len(c.items) >= c.size) && c.size > 0 {
			c.evict(1)
		}

		// 创建新条目
		item = &simpleItem[V]{
			clock: c.clock,
			value: value,
		}
		// 存入映射
		c.items[key] = item
	}

	// 如果有默认过期时间 设置条目过期时间
	if c.expiration != nil {
		t := c.clock.Now().Add(*c.expiration)
		item.expiration = &t
	}

	// 如果有添加回调 则调用
	if c.addedFunc != nil {
		c.addedFunc(key, value)
	}

	return item, nil
}

// Set  插入或更新指定的键值对
// @param key: 要插入或更新的键
// @param value: 对应的值
// @return  error: 操作失败时的错误
func (c *SimpleCache[K, V]) Set(key K, value V) error {
	// 加写锁，确保并发安全
	c.mu.Lock()
	defer c.mu.Unlock()
	_, err := c.set(key, value)
	return err
}

// SetWithExpire 插入或更新指定的键值对，并设置过期时间
// @param key: 要插入或更新的键
// @param value: 对应的值
// @param expiration: 过期时间
// @return error: 操作失败时的错误
func (c *SimpleCache[K, V]) SetWithExpire(key K, value V, expiration time.Duration) error {
	// 加写锁，确保并发安全
	c.mu.Lock()
	defer c.mu.Unlock()
	item, err := c.set(key, value)
	if err != nil {
		return err
	}

	// 设置条目的过期时间
	t := c.clock.Now().Add(expiration)
	item.expiration = &t
	return nil
}

// autoLease 自动续租缓存条目
// @param item: 要续租的缓存条目
func (c *SimpleCache[K, V]) autoLease(item *simpleItem[V]) {
	// 如果条目没有过期时间 直接返回
	if item.expiration == nil {
		return
	}
	// 如果没有租约时间 也直接返回
	if c.lease == nil {
		return
	}
	//重新设置新的过期时间
	t := item.clock.Now().Add(*c.lease)
	item.expiration = &t
}

// getValue 内部方法，用于获取原始值
// @param key: 要获取的键
// @param enableCount: 是否触发统计
// @return  V: 键对应的值
// @return error: 操作失败时的错误
func (c *SimpleCache[K, V]) getValue(key K, enableCount bool) (V, error) {
	//  加写锁，确保并发安全
	c.mu.Lock()
	// 检查键是否存在
	item, ok := c.items[key]
	if ok {
		// 如果条目未过期 则自动续租
		if !item.IsExpired(nil) {
			v := item.value
			c.autoLease(item)
			c.mu.Unlock()
			// 如果不是加载操作 增加命中次数统计
			if enableCount {
				c.stats.IncrHitCount()
			}
			return v, nil
		}
		// 条目已过期，移除
		c.remove(key)
	}
	c.mu.Unlock()
	// 如果不是加载操作 增加未命中次数统计
	if enableCount {
		c.stats.IncrMissCount()
	}
	return c.nilV, KeyNotFoundError
}

// getValue 内部方法，用于获取原始值
// @param key: 要获取的键
// @param enableCount: 是否触发统计
// @return  V: 键对应的值
// @return error: 操作失败时的错误
func (c *SimpleCache[K, V]) get(key K, enableCount bool) (V, error) {
	v, err := c.getValue(key, enableCount)
	if err != nil {
		return c.nilV, err
	}
	// 如果有反序列化函数 反序列化值
	if c.deserializeFunc != nil {
		return c.deserializeFunc(key, v)
	}
	return v, nil
}

// getWithLoader  使用 LoaderFunc 加载值
// @param key: 要加载的键
// @param isWait: 是否等待加载完成
// @return V: 加载的值
// @return error: 加载过程中的错误
func (c *SimpleCache[K, V]) getWithLoader(key K, isWait bool) (V, error) {
	// 如果没有 LoaderFunc 返回键未找到
	if c.loaderExpireFunc == nil {
		return c.nilV, KeyNotFoundError
	}
	value, _, err := c.load(key, func(v V, expiration *time.Duration, e error) (V, error) {
		if e != nil {
			return c.nilV, e
		}
		c.mu.Lock()
		defer c.mu.Unlock()
		// 设置加载的值
		item, err := c.set(key, v)
		if err != nil {
			return c.nilV, err
		}
		// 如果指定了过期时间 设置时间
		if expiration != nil {
			t := c.clock.Now().Add(*expiration)
			item.expiration = &t
		}
		return v, nil
	}, isWait)
	if err != nil {
		return c.nilV, err
	}
	return value, nil
}

// Get 从缓存中获取指定键的值
// 如果键不存在且有 LoaderFunc，则调用 LoaderFunc 加载值
// @param key: 要获取的键
// @return  V: 键对应的值
// @return error: 操作失败时的错误
func (c *SimpleCache[K, V]) Get(key K) (V, error) {
	v, err := c.get(key, false)
	if err == KeyNotFoundError {
		return c.getWithLoader(key, true)
	}
	return v, err
}

// GetIFPresent  从缓存中获取指定键的值，不等待函数加载完成 异步调用
// @param  key: 要获取的键
// @return  V: 键对应的值
// @return error: 操作失败时的错误
func (c *SimpleCache[K, V]) GetIFPresent(key K) (V, error) {
	v, err := c.get(key, false)
	if err == KeyNotFoundError {
		return c.getWithLoader(key, false)
	}
	return v, nil
}

// Has  检查键是否存在于缓存中
// @param  key: 要检查的键
// @return bool: 如果键存在且未过期，返回 true
func (c *SimpleCache[K, V]) Has(key K) bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	now := time.Now()
	return c.has(key, &now)
}

// has  检查键是否存在于缓存中
// @param  key: 要检查的键
// @return bool: 如果键存在且未过期，返回 true
func (c *SimpleCache[K, V]) has(key K, now *time.Time) bool {
	item, ok := c.items[key]
	if !ok {
		return false
	}
	// 检查条目是否过期
	return !item.IsExpired(now)
}

// Remove 删除指定的键
// @param key: 要删除的键
// @return bool: 如果键存在并被删除，返回 true
func (c *SimpleCache[K, V]) Remove(key K) bool {
	c.mu.Lock()
	defer c.mu.Unlock()

	return c.remove(key)
}

// keys 返回缓存中所有键的切片
func (c *SimpleCache[K, V]) keys() []K {
	c.mu.RLock()
	defer c.mu.RUnlock()
	keys := make([]K, len(c.items))
	var i = 0
	for k := range c.items {
		keys[i] = k
		i++
	}
	return keys
}

// GetALL 返回缓存中所有键值对
// @param checkExpired: 是否检查过期条目
// @return map[K]V: 缓存中的键值对映射
func (c *SimpleCache[K, V]) GetALL(checkExpired bool) map[K]V {
	// 加读锁，允许多个读取操作并发
	c.mu.RLock()
	defer c.mu.RUnlock()
	items := make(map[K]V, len(c.items))
	now := time.Now()
	for k, item := range c.items {
		// 如果不需要检查过期或条目未过期
		if !checkExpired || c.has(k, &now) {
			items[k] = item.value
		}
	}
	return items
}

// Keys 回缓存中所有键的切片
// @param checkExpired: 是否检查过期条目
// @return []K: 缓存中的键切片
func (c *SimpleCache[K, V]) Keys(checkExpired bool) []K {
	c.mu.RLock()
	defer c.mu.RUnlock()
	keys := make([]K, 0, len(c.items))
	now := time.Now()
	for k := range c.items {
		if !checkExpired || c.has(k, &now) {
			keys = append(keys, k)
		}
	}
	return keys
}

// Len 返回缓存中项的数量
// @param checkExpired: 是否检查过期条目
// @return int: 缓存中的项数
func (c *SimpleCache[K, V]) Len(checkExpired bool) int {
	c.mu.RLock()
	defer c.mu.RUnlock()
	// 如果不检查过期 直接返回
	if !checkExpired {
		return len(c.items)
	}
	var length int
	now := time.Now()
	for k := range c.items {
		if c.has(k, &now) {
			length++
		}
	}
	return length
}

// Purge  清除缓存中的所有键值对
func (c *SimpleCache[K, V]) Purge() {
	 // 加写锁，确保并发安全
	c.mu.Lock()
	defer c.mu.Unlock()

	// 如果有清除访问者函数 逐个用清除访问者函数
	if c.purgeVisitorFunc != nil {
		for key, item := range c.items {
			c.purgeVisitorFunc(key, item.value)
		}
	}

	// 重新初始化 items 映射
	c.init()
}
