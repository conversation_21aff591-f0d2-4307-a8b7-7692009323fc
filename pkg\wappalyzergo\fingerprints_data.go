// Author: chenjb
// Version: V1.0
// Date: 2025-07-18 11:40:56
// FilePath: /yaml_scan/pkg/wappalyzergo/fingerprints_data.go
// Description: 管理嵌入式指纹数据和分类数据，提供数据访问和初始化功能

package wappalyzergo

import (
	_ "embed"
	"encoding/json"
	"strconv"
	"sync"
)

// 嵌入式数据文件
// 使用go:embed指令将JSON数据文件嵌入到编译后的二进制文件中
// 这样可以确保指纹数据始终可用，无需外部文件依赖
var (
	// fingerprints 嵌入的指纹数据JSON字符串
	// 包含所有Web技术的检测指纹，从fingerprints_data.json文件嵌入
	// 该数据包含数千种技术的完整指纹信息，是技术识别的核心数据源
	//go:embed fingerprints_data.json
	fingerprints string

	// cateogriesData 嵌入的分类数据JSON字符串
	// 包含技术分类的定义和优先级信息，从categories_data.json文件嵌入
	// 用于将技术按类型分组，如Web服务器、数据库、框架等
	//go:embed categories_data.json
	cateogriesData string

	// 全局变量用于数据初始化和缓存
	// 使用sync.Once确保初始化只执行一次，提高性能并保证线程安全
	syncOnce          sync.Once
	categoriesMapping map[int]categoryItem // 分类ID到分类信息的映射缓存
)

// init 包初始化函数
// 该函数在包被导入时自动执行，负责解析和缓存分类数据
// 使用sync.Once确保即使在并发环境下也只初始化一次
//
// 初始化过程:
//   1. 解析嵌入的分类JSON数据
//   2. 将字符串格式的分类ID转换为整数
//   3. 构建分类ID到分类信息的映射表
//   4. 缓存映射表以供后续快速查询
//
// 错误处理:
//   如果JSON解析失败，程序会panic，因为分类数据是系统运行的必要条件
//   这种设计确保了数据完整性，避免在运行时出现不可预期的错误
func init() {
	syncOnce.Do(func() {
		// 初始化分类映射表
		categoriesMapping = make(map[int]categoryItem)

		// 临时变量用于JSON解析
		// 原始JSON中分类ID是字符串格式，需要转换为整数
		var categories map[string]categoryItem
		
		// 解析嵌入的分类JSON数据
		if err := json.Unmarshal([]byte(cateogriesData), &categories); err != nil {
			// 分类数据解析失败是致命错误，直接panic
			// 这表明嵌入的数据文件可能损坏或格式不正确
			panic("Failed to parse embedded categories data: " + err.Error())
		}
		
		// 将字符串ID转换为整数ID并构建映射
		for categoryIDStr, categoryData := range categories {
			// 将字符串格式的分类ID转换为整数
			// 忽略转换错误，无效的ID将被跳过
			categoryID, err := strconv.Atoi(categoryIDStr)
			if err != nil {
				// 记录警告但不中断初始化过程
				// 这样可以容忍部分数据错误，提高系统的健壮性
				continue
			}
			
			// 存储到映射表中，供后续快速查询
			categoriesMapping[categoryID] = categoryData
		}
	})
}

// GetRawFingerprints 获取原始指纹数据字符串
// 该函数返回嵌入的原始JSON格式指纹数据，主要用于以下场景：
//
// 使用场景:
//   1. 数据导出和备份
//   2. 外部工具集成
//   3. 调试和数据分析
//   4. 自定义指纹解析器开发
//
// 返回值:
//   - string: 完整的JSON格式指纹数据字符串
//
// 注意事项:
//   - 返回的是原始JSON字符串，需要进一步解析才能使用
//   - 数据大小可能较大（通常几MB），建议仅在必要时调用
//   - 该数据是只读的，修改不会影响系统行为
//
// 使用示例:
//   rawData := GetRawFingerprints()
//   var fingerprints Fingerprints
//   json.Unmarshal([]byte(rawData), &fingerprints)
func GetRawFingerprints() string {
	return fingerprints
}

// GetCategoriesMapping 获取技术分类映射表
// 该函数返回预处理的分类ID到分类信息的映射，用于快速查询技术分类
//
// 返回值:
//   - map[int]categoryItem: 分类ID到分类信息的映射表
//     * 键: 分类ID（整数），如1=CMS, 2=消息板, 3=数据库管理器
//     * 值: categoryItem结构体，包含分类名称和优先级
//
// 映射表结构:
//   - 键为整数类型的分类ID，便于快速查找
//   - 值包含分类的显示名称和优先级信息
//   - 映射表在包初始化时构建，运行时只读访问
//
// 使用场景:
//   1. 将技术的分类ID转换为可读的分类名称
//   2. 根据分类优先级对技术进行排序
//   3. 实现基于分类的技术过滤功能
//   4. 生成技术分类统计报告
//
// 使用示例:
//   categories := GetCategoriesMapping()
//   if category, exists := categories[1]; exists {
//       fmt.Printf("分类名称: %s, 优先级: %d\n", category.Name, category.Priority)
//   }
//
// 性能特性:
//   - 映射表在初始化时构建，查询时间复杂度为O(1)
//   - 线程安全，可在并发环境中安全使用
//   - 内存占用较小，适合频繁调用
func GetCategoriesMapping() map[int]categoryItem {
	return categoriesMapping
}

// categoryItem 技术分类项目结构体
// 该结构体定义单个技术分类的属性和元数据
// 用于描述技术的类型、用途和在分类体系中的位置
type categoryItem struct {
	// Name 分类显示名称
	// 用户友好的分类名称，支持多语言
	// 例如: "Web服务器", "内容管理系统", "JavaScript框架", "数据库"
	Name string `json:"name"`
	
	// Priority 分类优先级
	// 用于排序和显示的数值优先级
	// 数值越小表示优先级越高，用于确定技术的主要分类
	// 当一个技术属于多个分类时，优先级最高的分类作为主分类
	Priority int `json:"priority"`
}

// GetCategoryName 根据分类ID获取分类名称
// 该函数提供便捷的分类ID到名称的转换功能
//
// 参数:
//   - categoryID: 要查询的分类ID
//
// 返回值:
//   - string: 分类名称，如果ID不存在则返回空字符串
//   - bool: 是否找到对应的分类
//
// 使用示例:
//   name, exists := GetCategoryName(1)
//   if exists {
//       fmt.Printf("分类名称: %s\n", name)
//   }
func GetCategoryName(categoryID int) (string, bool) {
	if category, exists := categoriesMapping[categoryID]; exists {
		return category.Name, true
	}
	return "", false
}

// GetCategoryPriority 根据分类ID获取分类优先级
// 该函数用于获取指定分类的优先级信息
//
// 参数:
//   - categoryID: 要查询的分类ID
//
// 返回值:
//   - int: 分类优先级，如果ID不存在则返回-1
//   - bool: 是否找到对应的分类
//
// 使用示例:
//   priority, exists := GetCategoryPriority(1)
//   if exists {
//       fmt.Printf("分类优先级: %d\n", priority)
//   }
func GetCategoryPriority(categoryID int) (int, bool) {
	if category, exists := categoriesMapping[categoryID]; exists {
		return category.Priority, true
	}
	return -1, false
}

// GetAllCategories 获取所有可用的技术分类
// 该函数返回系统中定义的所有技术分类信息
//
// 返回值:
//   - []categoryItem: 所有分类的列表，按优先级排序
//
// 使用场景:
//   1. 构建分类选择界面
//   2. 生成分类统计报告
//   3. 实现分类过滤功能
//   4. 数据完整性检查
//
// 使用示例:
//   categories := GetAllCategories()
//   for _, category := range categories {
//       fmt.Printf("分类: %s (优先级: %d)\n", category.Name, category.Priority)
//   }
func GetAllCategories() []categoryItem {
	categories := make([]categoryItem, 0, len(categoriesMapping))
	
	// 收集所有分类
	for _, category := range categoriesMapping {
		categories = append(categories, category)
	}
	
	// 按优先级排序（优先级数值越小越靠前）
	for i := 0; i < len(categories)-1; i++ {
		for j := i + 1; j < len(categories); j++ {
			if categories[i].Priority > categories[j].Priority {
				categories[i], categories[j] = categories[j], categories[i]
			}
		}
	}
	
	return categories
}

// GetCategoriesCount 获取分类总数
// 该函数返回系统中定义的技术分类总数
//
// 返回值:
//   - int: 分类总数
//
// 使用场景:
//   1. 系统状态监控
//   2. 数据完整性验证
//   3. 性能分析和优化
//
// 使用示例:
//   count := GetCategoriesCount()
//   fmt.Printf("系统共有 %d 个技术分类\n", count)
func GetCategoriesCount() int {
	return len(categoriesMapping)
}

// ValidateCategoryID 验证分类ID是否有效
// 该函数检查给定的分类ID是否在系统中定义
//
// 参数:
//   - categoryID: 要验证的分类ID
//
// 返回值:
//   - bool: 分类ID是否有效
//
// 使用场景:
//   1. 输入数据验证
//   2. API参数检查
//   3. 配置文件验证
//   4. 错误处理和调试
//
// 使用示例:
//   if ValidateCategoryID(categoryID) {
//       // 处理有效的分类ID
//   } else {
//       // 处理无效的分类ID
//   }
func ValidateCategoryID(categoryID int) bool {
	_, exists := categoriesMapping[categoryID]
	return exists
}

// GetCategoriesByPriority 根据优先级范围获取分类
// 该函数返回指定优先级范围内的所有分类
//
// 参数:
//   - minPriority: 最小优先级（包含）
//   - maxPriority: 最大优先级（包含）
//
// 返回值:
//   - []categoryItem: 符合条件的分类列表
//
// 使用场景:
//   1. 实现分层的分类显示
//   2. 根据重要性过滤分类
//   3. 构建分类层次结构
//
// 使用示例:
//   highPriorityCategories := GetCategoriesByPriority(1, 10)
//   for _, category := range highPriorityCategories {
//       fmt.Printf("高优先级分类: %s\n", category.Name)
//   }
func GetCategoriesByPriority(minPriority, maxPriority int) []categoryItem {
	var result []categoryItem
	
	for _, category := range categoriesMapping {
		if category.Priority >= minPriority && category.Priority <= maxPriority {
			result = append(result, category)
		}
	}
	
	// 按优先级排序
	for i := 0; i < len(result)-1; i++ {
		for j := i + 1; j < len(result); j++ {
			if result[i].Priority > result[j].Priority {
				result[i], result[j] = result[j], result[i]
			}
		}
	}
	
	return result
}

// SearchCategoriesByName 根据名称搜索分类
// 该函数支持模糊搜索，查找名称中包含指定关键词的分类
//
// 参数:
//   - keyword: 搜索关键词（不区分大小写）
//
// 返回值:
//   - []categoryItem: 匹配的分类列表
//
// 使用场景:
//   1. 实现分类搜索功能
//   2. 用户界面的自动完成
//   3. 分类管理和维护
//
// 使用示例:
//   webCategories := SearchCategoriesByName("web")
//   for _, category := range webCategories {
//       fmt.Printf("匹配的分类: %s\n", category.Name)
//   }
func SearchCategoriesByName(keyword string) []categoryItem {
	var result []categoryItem
	keyword = strings.ToLower(keyword)
	
	for _, category := range categoriesMapping {
		if strings.Contains(strings.ToLower(category.Name), keyword) {
			result = append(result, category)
		}
	}
	
	return result
}
