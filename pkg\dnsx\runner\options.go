// Author: chenjb
// Version: V1.0
// Date: 2025-06-19 15:38:01
// FilePath: /yaml_scan/pkg/dnsx/runner/options.go
// Description: 定义DNS查询运行器的配置选项
package runner

import (
	"errors"
	"strconv"
	"strings"
	"yaml_scan/pkg/gologger"
	"yaml_scan/pkg/gologger/levels"
	"yaml_scan/pkg/utils"

	fileutil "yaml_scan/utils/file"
)

// DefaultResumeFile 是默认的恢复配置文件名
const (
	DefaultResumeFile = "dnsx-resume.cfg"
)

// ResumeCfg 是恢复配置结构体，用于存储恢复扫描所需的信息
type ResumeCfg struct {
	ResumeFrom   string // 恢复扫描的起始位置
	Index        int    // 恢复位置的索引
	current      string // 当前处理的项目
	currentIndex int    // 当前项目的索引
}

var PDCPApiKey string

// Options 是DNS查询运行器的配置选项结构体
type Options struct {
	Resolvers         string           // DNS解析器列表，以逗号分隔或文件路径
	Hosts             string           // 主机列表文件路径
	Domains           string           // 域名列表，以逗号分隔或文件路径
	WordList          string           // 单词列表文件路径，用于生成子域名
	Threads           int              // 并发线程数量
	RateLimit         int              // 查询速率限制(每秒请求数)
	Retries           int              // 查询失败时的重试次数
	OutputFormat      string           // 输出格式
	OutputFile        string           // 输出文件路径
	Raw               bool             // 是否输出原始DNS响应
	Silent            bool             // 是否静默模式(不输出到控制台)
	Verbose           bool             // 是否详细模式(输出更多信息)
	NoColor           bool             // 是否禁用彩色输出
	Response          bool             // 是否输出DNS响应
	ResponseOnly      bool             // 是否只输出DNS响应而不输出主机名
	A                 bool             // 是否查询A记录
	AAAA              bool             // 是否查询AAAA记录
	NS                bool             // 是否查询NS记录
	CNAME             bool             // 是否查询CNAME记录
	PTR               bool             // 是否查询PTR记录
	MX                bool             // 是否查询MX记录
	SOA               bool             // 是否查询SOA记录
	ANY               bool             // 是否查询ANY记录
	TXT               bool             // 是否查询TXT记录
	SRV               bool             // 是否查询SRV记录
	AXFR              bool             // 是否查询AXFR记录
	JSON              bool             // 是否以JSON格式输出
	OmitRaw           bool             // 是否在JSON输出中忽略原始数据
	Trace             bool             // 是否开启DNS追踪
	TraceMaxRecursion int              // DNS追踪的最大递归深度
	WildcardThreshold int              // 通配符检测阈值
	WildcardDomain    string           // 用于通配符检测的域名
	ShowStatistics    bool             // 是否显示统计信息
	rcodes            map[int]struct{} // 存储DNS响应码的映射
	RCode             string           // DNS响应码过滤
	hasRCodes         bool             // 是否有响应码过滤
	Resume            bool             // 是否从断点恢复扫描
	resumeCfg         *ResumeCfg       // 恢复配置
	HostsFile         bool             // 是否使用本地hosts文件
	Stream            bool             // 是否使用流模式
	CAA               bool             // 是否查询CAA记录
	QueryAll          bool             // 是否查询所有记录类型
	ExcludeType       []string         // 排除的查询类型列表
	Proxy             string
}

// ShouldLoadResume  判断是否应该加载恢复文件
// @receiver options
// @return bool bool: 如果启用了恢复选项且恢复文件存在则返回true，否则返回false
func (options *Options) ShouldLoadResume() bool {
	return options.Resume && fileutil.FileExists(DefaultResumeFile)
}

// ShouldSaveResume 判断是否应该保存恢复文件
//
// 返回值:
//   - bool: 始终返回true
func (options *Options) ShouldSaveResume() bool {
	return true
}

// validateOptions 验证选项的有效性，检查选项之间的冲突和依赖关系
// @receiver options
func (options *Options) validateOptions() {
	// 检查响应选项互斥性
	if options.Response && options.ResponseOnly {
		gologger.Fatal().Msgf("resp and resp-only can't be used at the same time")
	}

	// 检查重试次数
	if options.Retries == 0 {
		gologger.Fatal().Msgf("retries must be at least 1")
	}

	// 检查输入选项
	wordListPresent := options.WordList != ""
	domainsPresent := options.Domains != ""
	hostsPresent := options.Hosts != ""

	// 检查主机列表和域名/单词列表互斥
	if hostsPresent && (wordListPresent || domainsPresent) {
		gologger.Fatal().Msgf("list(l) flag can not be used domain(d) or wordlist(w) flag")
	}

	// 检查域名和单词列表依赖
	if wordListPresent && !domainsPresent {
		gologger.Fatal().Msg("missing domain(d) flag required with wordlist(w) input")
	}
	if domainsPresent && !wordListPresent {
		gologger.Fatal().Msgf("missing wordlist(w) flag required with domain(d) input")
	}

	// 检查标准输入用法
	if argumentHasStdin(options.Domains) && argumentHasStdin(options.WordList) {
		if options.Stream {
			gologger.Fatal().Msgf("argument stdin not supported in stream mode")
		}
		gologger.Fatal().Msgf("stdin can be set for one flag")
	}

	// 检查流模式限制
	if options.Stream {
		if wordListPresent {
			gologger.Fatal().Msgf("wordlist not supported in stream mode")
		}
		if domainsPresent {
			gologger.Fatal().Msgf("domains not supported in stream mode")
		}
		if options.Resume {
			gologger.Fatal().Msgf("resume not supported in stream mode")
		}
		if options.WildcardDomain != "" {
			gologger.Fatal().Msgf("wildcard not supported in stream mode")
		}
		if options.ShowStatistics {
			gologger.Fatal().Msgf("stats not supported in stream mode")
		}
	}
}

// argumentHasStdin 检查参数是否为标准输入标记
// @param arg string: 要检查的参数字符串
// @return bool bool: 如果参数是标准输入标记则返回true，否则返回false
func argumentHasStdin(arg string) bool {
	return arg == stdinMarker
}

// configureOutput 检查参数是否为标准输入标记
// @receiver options
func (options *Options) configureOutput() {
	// 如果用户需要详细输出，显示详细信息
	if options.Verbose {
		gologger.DefaultLogger.SetMaxLevel(levels.LevelVerbose)
	}
	if options.Silent {
		gologger.DefaultLogger.SetMaxLevel(levels.LevelSilent)
	}
}

// configureRcodes  解析并配置DNS响应码过滤器
// @receiver options
// @return error error:
func (options *Options) configureRcodes() error {
	options.rcodes = make(map[int]struct{})
	rcodes := strings.Split(options.RCode, ",")
	// 遍历处理每个响应码
	for _, rcode := range rcodes {
		var rc int
		switch strings.ToLower(rcode) {
		case "": // 跳过空值
			continue
		case "noerror": // 无错误(0)
			rc = 0
		case "formerr": // 格式错误(1)
			rc = 1
		case "servfail": // 服务器故障(2)
			rc = 2
		case "nxdomain": // 不存在的域名(3)
			rc = 3
		case "notimp": // 未实现(4)
			rc = 4
		case "refused": // 拒绝(5)
			rc = 5
		case "yxdomain": // 已存在的域名(6)
			rc = 6
		case "yxrrset": // 已存在的记录集(7)
			rc = 7
		case "nxrrset": // 不存在的记录集(8)
			rc = 8
		case "notauth": // 未授权(9)
			rc = 9
		case "notzone": // 不在区域(10)
			rc = 10
		case "badsig", "badvers": // 签名/版本错误(16)
			rc = 16
		case "badkey": // 错误密钥(17)
			rc = 17
		case "badtime": // 错误时间(18)
			rc = 18
		case "badmode": // 错误模式(19)
			rc = 19
		case "badname": // 错误名称(20)
			rc = 20
		case "badalg": // 错误算法(21)
			rc = 21
		case "badtrunc": // 错误截断(22)
			rc = 22
		case "badcookie": // 错误Cookie(23)
			rc = 23
		default: // 尝试将其解析为数字
			var err error
			rc, err = strconv.Atoi(rcode)
			if err != nil {
				return errors.New("无效的RCODE值")
			}
		}

		// 将解析的RCODE添加到映射中
		options.rcodes[rc] = struct{}{}
	}

	// 设置hasRCodes标志，表示是否提供了RCODE过滤
	options.hasRCodes = options.RCode != ""
	return nil
}

// configureResume 配置恢复选项
// @receiver options
// @return error error:
func (options *Options) configureResume() error {
	options.resumeCfg = &ResumeCfg{}
	if options.Resume && fileutil.FileExists(DefaultResumeFile) {
		return utils.Load(&options.resumeCfg, DefaultResumeFile)

	}
	return nil
}

// configureQueryOptions 根据用户选项配置DNS查询类型
// @receiver options
func (options *Options) configureQueryOptions() {
	queryMap := map[string]*bool{
		"a":     &options.A,
		"aaaa":  &options.AAAA,
		"cname": &options.CNAME,
		"ns":    &options.NS,
		"txt":   &options.TXT,
		"srv":   &options.SRV,
		"ptr":   &options.PTR,
		"mx":    &options.MX,
		"soa":   &options.SOA,
		"axfr":  &options.AXFR,
		"caa":   &options.CAA,
		"any":   &options.ANY,
	}

	// 如果启用了QueryAll选项，则启用所有查询类型
	if options.QueryAll {
		for _, val := range queryMap {
			*val = true
		}
		options.Response = true
		// ANY查询类型不被retryabledns库支持，
		// 因此在与其他查询类型组合使用时很难过滤结果
		options.ExcludeType = append(options.ExcludeType, "any")
	}

	for _, et := range options.ExcludeType {
		if val, ok := queryMap[et]; ok {
			*val = false
		}
	}
}
