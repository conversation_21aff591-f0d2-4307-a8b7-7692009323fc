// Author: chenjb
// Version: V1.0
// Date: 2025-06-16 14:55:16
// FilePath: /yaml_scan/pkg/authprovider/authx/dynamic_test.go
// Description:
package authx

import (
	"fmt"
	"sync"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestDynamic_GetDomainAndDomainRegex 测试Dynamic结构体的GetDomainAndDomainRegex方法
// 确保它能正确返回域名和域名正则表达式
func TestDynamic_GetDomainAndDomainRegex(t *testing.T) {
	// 创建包含Secret的Dynamic
	dynamic := &Dynamic{
		Secret: &Secret{
			Domains:      []string{"example.com", "example.net"},
			DomainsRegex: []string{".*\\.example\\.org"},
		},
		Secrets: []*Secret{
			{
				Domains:      []string{"api.example.com", "example.net"},
				DomainsRegex: []string{".*\\.api\\.example\\.org"},
			},
		},
	}
	
	// 获取域名和域名正则表达式
	domains, domainsRegex := dynamic.GetDomainAndDomainRegex()
	
	// 验证结果
	require.Len(t, domains, 3, "应该返回3个去重后的域名")
	require.Contains(t, domains, "example.com", "域名列表应包含example.com")
	require.Contains(t, domains, "example.net", "域名列表应包含example.net")
	require.Contains(t, domains, "api.example.com", "域名列表应包含api.example.com")
	
	require.Len(t, domainsRegex, 2, "应该返回2个去重后的域名正则表达式")
	require.Contains(t, domainsRegex, ".*\\.example\\.org", "域名正则表达式列表应包含.*\\.example\\.org")
	require.Contains(t, domainsRegex, ".*\\.api\\.example\\.org", "域名正则表达式列表应包含.*\\.api\\.example\\.org")
}

// TestDynamic_GetStrategies 测试Dynamic结构体的GetStrategies方法
// 确保它能正确返回认证策略列表
func TestDynamic_GetStrategies(t *testing.T) {
	// 创建带有一个密钥的Dynamic
	dynamic := &Dynamic{
		Secret: &Secret{
			Type:     string(BasicAuth),
			Domains:  []string{"example.com"},
			Username: "test-user",
			Password: "test-pass",
		},
		Secrets: []*Secret{
			{
				Type:   string(BearerTokenAuth),
				Domains: []string{"api.example.com"},
				Token:  "test-token",
			},
		},
		fetched: true, // 设置为已获取，跳过fetch调用
	}
	
	// 获取策略
	strategies := dynamic.GetStrategies()
	
	// 验证结果
	require.Len(t, strategies, 2, "应该返回2个策略")
	require.IsType(t, &BasicAuthStrategy{}, strategies[0], "第一个策略应该是BasicAuthStrategy")
	require.IsType(t, &BearerTokenAuthStrategy{}, strategies[1], "第二个策略应该是BearerTokenAuthStrategy")
}

// TestDynamic_UnmarshalJSON 测试Dynamic结构体的UnmarshalJSON方法
// 确保它能正确反序列化包含Secret的JSON数据
func TestDynamic_UnmarshalJSON(t *testing.T) {
	// 包含Secret字段的JSON数据
	jsonData := []byte(`{
		"type": "BasicAuth",
		"domains": ["example.com"],
		"username": "test-user",
		"password": "test-pass",
		"template": "test-template",
		"variables": [
			{
				"key": "test-key",
				"value": "test-value"
			}
		]
	}`)
	
	// 反序列化
	var dynamic Dynamic
	err := dynamic.UnmarshalJSON(jsonData)
	require.NoError(t, err, "反序列化不应该返回错误")
	
	// 验证结果
	require.NotNil(t, dynamic.Secret, "Secret字段应该被设置")
	require.Equal(t, "BasicAuth", dynamic.Secret.Type, "Secret类型应该正确")
	require.Equal(t, "test-user", dynamic.Secret.Username, "用户名应该正确")
	require.Equal(t, "test-pass", dynamic.Secret.Password, "密码应该正确")
	require.Equal(t, "test-template", dynamic.TemplatePath, "模板路径应该正确")
	require.Len(t, dynamic.Variables, 1, "应该有1个变量")
	require.Equal(t, "test-key", dynamic.Variables[0].Key, "变量键名应该正确")
	require.Equal(t, "test-value", dynamic.Variables[0].Value, "变量值应该正确")

	// 验证不会引起递归调用导致栈溢出
	jsonDataLarge := []byte(`{
		"type": "BasicAuth",
		"domains": ["example.com"],
		"username": "test-user",
		"password": "test-pass",
		"template": "test-template",
		"variables": [
			{
				"key": "test-key",
				"value": "test-value"
			}
		],
		"secrets": [
			{
				"type": "BearerTokenAuth",
				"domains": ["api.example.com"],
				"token": "test-token"
			}
		]
	}`)
	
	var dynamicLarge Dynamic
	err = dynamicLarge.UnmarshalJSON(jsonDataLarge)
	require.NoError(t, err, "反序列化复杂JSON不应该返回错误")
	require.NotNil(t, dynamicLarge.Secret, "Secret字段应该被设置")
	require.Len(t, dynamicLarge.Secrets, 1, "应该有1个子密钥")
}

// TestDynamic_Validate 测试Dynamic结构体的Validate方法
// 确保它能正确验证动态密钥的有效性
func TestDynamic_Validate(t *testing.T) {
	// 创建有效的Dynamic
	validDynamic := &Dynamic{
		Secret: &Secret{
			Type:     string(BasicAuth),
			Domains:  []string{"example.com"},
			Username: "test-user",
			Password: "test-pass",
		},
		TemplatePath: "test-template",
		Variables: []KV{
			{Key: "test-key", Value: "test-value"},
		},
	}
	
	// 验证有效的Dynamic
	err := validDynamic.Validate()
	require.NoError(t, err, "有效的Dynamic应该通过验证")
	require.NotNil(t, validDynamic.m, "互斥锁应该被初始化")
	
	// 创建缺少模板路径的Dynamic
	missingTemplateDynamic := &Dynamic{
		Secret: &Secret{
			Type:     string(BasicAuth),
			Domains:  []string{"example.com"},
			Username: "test-user",
			Password: "test-pass",
		},
		Variables: []KV{
			{Key: "test-key", Value: "test-value"},
		},
	}
	
	// 验证缺少模板路径的Dynamic
	err = missingTemplateDynamic.Validate()
	require.Error(t, err, "缺少模板路径的Dynamic应该返回错误")
	require.Contains(t, err.Error(), "template-path is required", "错误信息应该指出缺少模板路径")
	
	// 创建缺少变量的Dynamic
	missingVariablesDynamic := &Dynamic{
		Secret: &Secret{
			Type:     string(BasicAuth),
			Domains:  []string{"example.com"},
			Username: "test-user",
			Password: "test-pass",
		},
		TemplatePath: "test-template",
	}
	
	// 验证缺少变量的Dynamic
	err = missingVariablesDynamic.Validate()
	require.Error(t, err, "缺少变量的Dynamic应该返回错误")
	require.Contains(t, err.Error(), "variables are required", "错误信息应该指出缺少变量")
}

// TestDynamic_Fetch 测试Dynamic结构体的Fetch方法
// 确保它能正确获取动态密钥
func TestDynamic_Fetch(t *testing.T) {
	// 创建一个Dynamic
	dynamic := &Dynamic{
		m: &sync.Mutex{}, // 初始化互斥锁
	}
	
	// 设置一个成功的回调函数
	successCalled := false
	dynamic.fetchCallback = func(d *Dynamic) error {
		successCalled = true
		return nil
	}
	
	// 测试获取
	err := dynamic.Fetch(false)
	require.NoError(t, err, "Fetch不应该返回错误")
	require.True(t, successCalled, "回调函数应该被调用")
	
	// 重置并设置一个失败的回调函数
	dynamic.fetched = false
	failureCalled := false
	expectedError := fmt.Errorf("fetch error")
	dynamic.fetchCallback = func(d *Dynamic) error {
		failureCalled = true
		return expectedError
	}
	
	// 测试获取失败
	err = dynamic.Fetch(false)
	require.Error(t, err, "Fetch应该返回错误")
	require.Equal(t, expectedError, err, "应该返回预期的错误")
	require.True(t, failureCalled, "回调函数应该被调用")
	
	// 测试已经获取过的情况
	dynamic.fetched = true
	called := false
	dynamic.fetchCallback = func(d *Dynamic) error {
		called = true
		return nil
	}
	
	err = dynamic.Fetch(false)
	require.NoError(t, err, "对于已获取的Dynamic，Fetch不应该返回错误")
	require.False(t, called, "对于已获取的Dynamic，回调函数不应该被调用")
}

// TestDynamic_Error 测试Dynamic结构体的Error方法
// 确保它能正确返回错误
func TestDynamic_Error(t *testing.T) {
	// 创建一个有错误的Dynamic
	expectedError := fmt.Errorf("test error")
	dynamic := &Dynamic{
		error: expectedError,
	}
	
	// 测试Error方法
	err := dynamic.Error()
	require.Equal(t, expectedError, err, "Error应该返回预期的错误")
	
	// 创建一个没有错误的Dynamic
	noErrorDynamic := &Dynamic{}
	
	// 测试Error方法
	err = noErrorDynamic.Error()
	require.Nil(t, err, "对于没有错误的Dynamic，Error应该返回nil")
}

// TestDynamic_SetLazyFetchCallback 测试Dynamic结构体的SetLazyFetchCallback方法
// 确保它能正确设置懒加载回调函数
func TestDynamic_SetLazyFetchCallback(t *testing.T) {
	// 创建一个Dynamic
	dynamic := &Dynamic{
		Secret: &Secret{
			Type:     string(BasicAuth),
			Domains:  []string{"example.com"},
			Username: "{{username}}",
			Password: "{{password}}",
		},
		m: &sync.Mutex{}, // 初始化互斥锁
	}
	
	// 设置一个回调函数
	callbackCalled := false
	dynamic.SetLazyFetchCallback(func(d *Dynamic) error {
		callbackCalled = true
		
		// 设置提取的值
		d.Extracted = map[string]interface{}{
			"username": "extracted-user",
			"password": "extracted-pass",
		}
		
		return nil
	})
	
	// 调用包装后的回调函数
	err := dynamic.fetchCallback(dynamic)
	require.NoError(t, err, "包装后的回调函数不应该返回错误")
	require.True(t, callbackCalled, "原始回调函数应该被调用")
	require.True(t, dynamic.fetched, "fetched标志应该被设置为true")
	
	// 验证模板变量是否被替换
	require.Equal(t, "extracted-user", dynamic.Secret.Username, "用户名应该被替换为提取的值")
	require.Equal(t, "extracted-pass", dynamic.Secret.Password, "密码应该被替换为提取的值")
} 

