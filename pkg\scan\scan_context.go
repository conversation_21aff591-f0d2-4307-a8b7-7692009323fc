// Author: chenjb
// Version: V1.0
// Date: 2025-06-17 20:20:37
// FilePath: /yaml_scan/pkg/scan/scan_context.go
// Description:
package scan

import (
	"context"
	"sync"
	"yaml_scan/pkg/output"
	"yaml_scan/pkg/protocols/common/contextargs"
)


type Scan<PERSON>ontext struct {
	ctx context.Context

	// exported / configurable fields
	Input *contextargs.Context

	// callbacks or hooks
	OnError   func(error)
	OnResult  func(e *output.InternalWrappedEvent)
	OnWarning func(string)

	// unexported state fields
	error    error
	warnings []string
	events   []*output.InternalWrappedEvent
	results  []*output.ResultEvent

	// what to log
	withEvents bool

	// might not be required but better to sync
	m sync.Mutex
}


// NewScanContext creates a new scan context using input
func NewScanContext(ctx context.Context, input *contextargs.Context) *ScanContext {
	return &ScanContext{ctx: ctx, Input: input}
}


