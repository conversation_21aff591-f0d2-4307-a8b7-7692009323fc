// 
// Author: chenjb
// Version: V1.0
// Date: 2025-05-19 19:47:39
// FilePath: /yaml_scan/pkg/hybridMap/hybrid/memoryguard_test.go
// Description: 
package hybrid

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestRunMemoryGuard 测试runMemoryGuard函数功能
func TestRunMemoryGuard(t *testing.T) {
	// 创建测试用的HybridMap
	// 使用默认混合选项，设置内存限制
	options := DefaultHybridOptions
	options.MaxMemorySize = 100
	hm, err := New(options)
	require.NoError(t, err, "创建HybridMap应成功")
	defer hm.Close()

	// 运行内存监控
	// 调用runMemoryGuard函数设置并启动监控
	runMemoryGuard(hm, 100*time.Millisecond)

	// 检查memoryguard是否已被设置
	// 验证memoryguard实例是否已创建并附加到HybridMap
	require.NotNil(t, hm.memoryguard, "runMemoryGuard应设置HybridMap的memoryguard")
	// 验证时间间隔是否被正确设置
	require.Equal(t, 100*time.Millisecond, hm.memoryguard.Interval, "memoryguard的Interval应被正确设置")
	// 验证stop通道是否被初始化
	require.NotNil(t, hm.memoryguard.stop, "memoryguard的stop通道应被初始化")
}

// TestStopMemoryGuard 测试stopMemoryGuard函数功能
func TestStopMemoryGuard(t *testing.T) {
	// 创建测试用的HybridMap
	options := DefaultHybridOptions
	hm, err := New(options)
	require.NoError(t, err, "创建HybridMap应成功")
	defer hm.Close()

	// 创建一个memoryguard，手动设置
	// 创建标志和通道，用于检测stop通道是否被触发
	stopCalled := false
	stopChan := make(chan bool)
	
	// 创建一个特殊的监听器来检测stop通道是否被触发
	// 这个goroutine会等待stopChan接收信号，然后设置标志
	go func() {
		<-stopChan
		stopCalled = true
	}()
	
	// 创建memoryguard实例，使用自定义的通道
	mg := &memoryguard{
		Enabled:  true,
		Interval: 100 * time.Millisecond,
		stop:     stopChan,    // 使用自定义通道，便于检测信号
	}
	hm.memoryguard = mg

	// 调用stopMemoryGuard
	// 这应该向stop通道发送信号
	stopMemoryGuard(hm)
	
	// 等待一小段时间确保通道处理完成
	// 给goroutine足够的时间处理信号
	time.Sleep(50 * time.Millisecond)
	
	// 验证stop通道是否接收到信号
	require.True(t, stopCalled, "stopMemoryGuard应发送信号到stop通道")
}

// TestMemoryGuardIntegration 测试内存监控集成功能
func TestMemoryGuardIntegration(t *testing.T) {
	// 创建一个内存保护配置的HybridMap
	// 启用内存保护，设置较短的检查间隔和较小的内存限制
	options := DefaultHybridOptions
	options.MemoryGuard = true              // 启用内存保护
	options.MemoryGuardTime = 50 * time.Millisecond  // 设置较短的检查间隔
	options.MaxMemorySize = 100            // 设置较小的内存限制，便于触发保护机制
	
	// 创建HybridMap实例
	hm, err := New(options)
	require.NoError(t, err, "创建HybridMap应成功")
	
	// 检查内存监控是否已启动
	// 验证memoryguard是否已创建
	require.NotNil(t, hm.memoryguard, "内存监控应被启动")
	
	// 生成大量数据以触发内存保护
	// 创建一个足够大的字节数组，超过设定的内存限制
	largeData := make([]byte, 1000)
	for i := range largeData {
		largeData[i] = byte(i % 256)  // 填充数据，增加内存使用
	}
	
	// 等待内存监控执行几次
	// 给监控器足够的时间完成几个检查周期
	time.Sleep(200 * time.Millisecond)
	
	// 存储数据
	// 存储大量数据，应触发内存保护机制
	err = hm.Set("large_key", largeData)
	require.NoError(t, err)
	
	// 内存监控应已触发
	// 验证是否切换到了强制磁盘模式
	require.True(t, hm.options.MemoryGuardForceDisk, "内存保护应触发强制磁盘模式")
	
	// 清理资源
	// 关闭HybridMap，释放资源
	hm.Close()
} 

