// Author: chenjb
// Version: V1.0
// Date: 2025-07-01 16:29:35
// FilePath: /yaml_scan/pkg/tlsx/output/output_test.go
// Description:
package output

import (
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"yaml_scan/pkg/tlsx/tlsx/clients"
)

// TestNew 测试New函数
// 验证输出写入器的创建和初始化
func TestNew(t *testing.T) {
	t.Run("基本写入器创建", func(t *testing.T) {
		options := &clients.Options{
			JSON:    false,
			NoColor: false,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建写入器应该成功")
		require.NotNil(t, writer, "写入器不应该为nil")

		err = writer.Close()
		require.NoError(t, err, "关闭写入器应该成功")
	})

	t.Run("JSON模式写入器", func(t *testing.T) {
		options := &clients.Options{
			JSON:    true,
			NoColor: false,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建JSON写入器应该成功")
		require.NotNil(t, writer, "写入器不应该为nil")

		// 验证是StandardWriter类型
		standardWriter, ok := writer.(*StandardWriter)
		require.True(t, ok, "应该是StandardWriter类型")
		require.True(t, standardWriter.json, "应该启用JSON模式")

		err = writer.Close()
		require.NoError(t, err, "关闭写入器应该成功")
	})

	t.Run("无颜色模式写入器", func(t *testing.T) {
		options := &clients.Options{
			JSON:    false,
			NoColor: true,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建无颜色写入器应该成功")
		require.NotNil(t, writer, "写入器不应该为nil")

		err = writer.Close()
		require.NoError(t, err, "关闭写入器应该成功")
	})

	t.Run("文件输出写入器", func(t *testing.T) {
		// 创建临时输出文件
		tmpFile, err := os.CreateTemp("", "tlsx_output_test_*.txt")
		require.NoError(t, err, "创建临时文件应该成功")
		tmpFile.Close()
		defer os.Remove(tmpFile.Name())

		options := &clients.Options{
			JSON:       false,
			NoColor:    false,
			OutputFile: tmpFile.Name(),
		}

		writer, err := New(options)
		require.NoError(t, err, "创建文件输出写入器应该成功")
		require.NotNil(t, writer, "写入器不应该为nil")

		// 验证是StandardWriter类型且有文件输出器
		standardWriter, ok := writer.(*StandardWriter)
		require.True(t, ok, "应该是StandardWriter类型")
		require.NotNil(t, standardWriter.outputFile, "应该有文件输出器")

		err = writer.Close()
		require.NoError(t, err, "关闭写入器应该成功")
	})

	t.Run("无效输出文件路径", func(t *testing.T) {
		options := &clients.Options{
			JSON:       false,
			NoColor:    false,
			OutputFile: "/invalid/path/output.txt",
		}

		writer, err := New(options)
		require.Error(t, err, "无效文件路径应该返回错误")
		require.Nil(t, writer, "错误时写入器应该为nil")
		require.Contains(t, err.Error(), "could not create output file", "错误信息应该正确")
	})
}

// TestStandardWriter 测试StandardWriter结构体
// 验证StandardWriter的基本功能
func TestStandardWriter(t *testing.T) {
	t.Run("StandardWriter字段验证", func(t *testing.T) {
		options := &clients.Options{
			JSON:    true,
			NoColor: true,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建写入器应该成功")

		standardWriter, ok := writer.(*StandardWriter)
		require.True(t, ok, "应该是StandardWriter类型")

		require.True(t, standardWriter.json, "JSON字段应该正确设置")
		require.NotNil(t, standardWriter.aurora, "aurora字段不应该为nil")
		require.NotNil(t, standardWriter.outputMutex, "outputMutex字段不应该为nil")
		require.NotNil(t, standardWriter.options, "options字段不应该为nil")

		err = writer.Close()
		require.NoError(t, err, "关闭写入器应该成功")
	})
}

// TestWrite 测试Write方法
// 验证TLS响应写入功能
func TestWrite(t *testing.T) {

	var layout = "2006-01-02 15:04:05"
	parsedTime, _ := time.Parse(layout, "2025-07-01 16:34:00")
	t.Run("写入基本响应", func(t *testing.T) {
		options := &clients.Options{
			JSON:    false,
			NoColor: true,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建写入器应该成功")
		defer writer.Close()

		// 创建测试响应
		response := &clients.Response{
			Timestamp:   &parsedTime,
			Host:        "example.com",
			Port:        "443",
			ProbeStatus: true,
			CertificateResponse: &clients.CertificateResponse{
				Expired: false,
			},
		}

		err = writer.Write(response)
		require.NoError(t, err, "写入响应应该成功")
	})

	t.Run("写入JSON响应", func(t *testing.T) {
		options := &clients.Options{
			JSON:    true,
			NoColor: true,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建写入器应该成功")
		defer writer.Close()

		// 创建测试响应
		response := &clients.Response{
			Timestamp:   &parsedTime,
			Host:        "example.com",
			Port:        "443",
			ProbeStatus: true,
			CertificateResponse: &clients.CertificateResponse{
				Expired: false,
			},
		}

		err = writer.Write(response)
		require.NoError(t, err, "写入JSON响应应该成功")
	})

	t.Run("写入到文件", func(t *testing.T) {
		// 创建临时输出文件
		tmpFile, err := os.CreateTemp("", "tlsx_write_test_*.txt")
		require.NoError(t, err, "创建临时文件应该成功")
		tmpFile.Close()
		defer os.Remove(tmpFile.Name())

		options := &clients.Options{
			JSON:       true,
			NoColor:    true,
			OutputFile: tmpFile.Name(),
		}

		writer, err := New(options)
		require.NoError(t, err, "创建写入器应该成功")

		// 创建测试响应
		response := &clients.Response{
			Timestamp:   &parsedTime,
			Host:        "example.com",
			Port:        "443",
			ProbeStatus: true,
			CertificateResponse: &clients.CertificateResponse{
				Expired: false,
			},
		}

		err = writer.Write(response)
		require.NoError(t, err, "写入到文件应该成功")

		err = writer.Close()
		require.NoError(t, err, "关闭写入器应该成功")

		// 验证文件内容
		content, err := os.ReadFile(tmpFile.Name())
		require.NoError(t, err, "读取文件应该成功")
		require.NotEmpty(t, content, "文件内容不应该为空")
		require.Contains(t, string(content), "example.com", "文件应该包含主机名")
	})
}

// TestFormatJSON 测试formatJSON方法
// 验证JSON格式化功能
func TestFormatJSON(t *testing.T) {
	var layout = "2006-01-02 15:04:05"
	parsedTime, _ := time.Parse(layout, "2025-07-01 16:34:00")
	options := &clients.Options{
		JSON:    true,
		NoColor: true,
	}

	writer, err := New(options)
	require.NoError(t, err, "创建写入器应该成功")
	defer writer.Close()

	standardWriter := writer.(*StandardWriter)

	t.Run("格式化基本响应", func(t *testing.T) {
		response := &clients.Response{
			Timestamp:   &parsedTime,
			Host:        "example.com",
			Port:        "443",
			ProbeStatus: true,
			Version:     "tls1.3",
			Cipher:      "TLS_AES_256_GCM_SHA384",
		}

		data, err := standardWriter.formatJSON(response)
		require.NoError(t, err, "JSON格式化应该成功")
		require.NotEmpty(t, data, "JSON数据不应该为空")

		// 验证JSON包含关键字段
		jsonStr := string(data)
		require.Contains(t, jsonStr, "example.com", "JSON应该包含主机名")
		require.Contains(t, jsonStr, "443", "JSON应该包含端口")
		require.Contains(t, jsonStr, "tls1.3", "JSON应该包含TLS版本")
	})

	t.Run("格式化带证书的响应", func(t *testing.T) {
		response := &clients.Response{
			Timestamp:   &parsedTime,
			Host:        "example.com",
			Port:        "443",
			ProbeStatus: true,
			CertificateResponse: &clients.CertificateResponse{
				SubjectCN:    "example.com",
				SubjectAN:    []string{"example.com", "www.example.com"},
				SubjectOrg:   []string{"Example Corp"},
				Expired:      false,
				SelfSigned:   false,
				MisMatched:   false,
				Revoked:      false,
				Untrusted:    false,
				WildCardCert: false,
				Serial:       "123456789",
				// FingerprintHash: clients.FingerprintHash{
				// 	MD5:    "d41d8cd98f00b204e9800998ecf8427e",
				// 	SHA1:   "da39a3ee5e6b4b0d3255bfef95601890afd80709",
				// 	SHA256: "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",
				// },
			},
		}

		data, err := standardWriter.formatJSON(response)
		require.NoError(t, err, "JSON格式化应该成功")
		require.NotEmpty(t, data, "JSON数据不应该为空")

		// 验证JSON包含证书信息
		jsonStr := string(data)
		require.Contains(t, jsonStr, "subject_cn", "JSON应该包含证书主题CN")
		require.Contains(t, jsonStr, "subject_an", "JSON应该包含证书主题AN")
		require.Contains(t, jsonStr, "Example Corp", "JSON应该包含组织信息")
	})

	t.Run("格式化nil响应", func(t *testing.T) {
		data, err := standardWriter.formatJSON(nil)
		require.NoError(t, err, "格式化nil应该成功")
		require.Equal(t, "null", string(data), "nil应该格式化为null")
	})
}

// TestFormatStandard 测试formatStandard方法
// 验证标准格式化功能
func TestFormatStandard(t *testing.T) {
	var layout = "2006-01-02 15:04:05"
	parsedTime, _ := time.Parse(layout, "2025-07-01 16:34:00")
	t.Run("格式化基本响应", func(t *testing.T) {
		options := &clients.Options{
			JSON:    false,
			NoColor: true,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建写入器应该成功")
		defer writer.Close()

		standardWriter := writer.(*StandardWriter)

		response := &clients.Response{
			Timestamp:   &parsedTime,
			Host:        "example.com",
			Port:        "443",
			ProbeStatus: true,
			CertificateResponse: &clients.CertificateResponse{
				SubjectCN: "example.com",
			},
		}

		data, err := standardWriter.formatStandard(response)
		require.NoError(t, err, "标准格式化应该成功")
		require.NotEmpty(t, data, "格式化数据不应该为空")

		output := string(data)
		require.Contains(t, output, "example.com:443", "输出应该包含主机名和端口")
	})

	t.Run("格式化nil响应", func(t *testing.T) {
		options := &clients.Options{
			JSON:    false,
			NoColor: true,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建写入器应该成功")
		defer writer.Close()

		standardWriter := writer.(*StandardWriter)

		data, err := standardWriter.formatStandard(nil)
		require.Error(t, err, "格式化nil响应应该返回错误")
		require.Contains(t, err.Error(), "empty certificate response", "错误信息应该正确")
		require.Nil(t, data, "错误时数据应该为nil")
	})

	t.Run("格式化无证书响应", func(t *testing.T) {
		options := &clients.Options{
			JSON:    false,
			NoColor: true,
		}

		writer, err := New(options)
		require.NoError(t, err, "创建写入器应该成功")
		defer writer.Close()

		standardWriter := writer.(*StandardWriter)

		response := &clients.Response{
			Timestamp:           &parsedTime,
			Host:                "example.com",
			Port:                "443",
			ProbeStatus:         true,
			CertificateResponse: nil, // 无证书响应
		}

		data, err := standardWriter.formatStandard(response)
		require.Error(t, err, "无证书响应应该返回错误")
		require.Contains(t, err.Error(), "empty leaf certificate", "错误信息应该正确")
		require.Nil(t, data, "错误时数据应该为nil")
	})
}
