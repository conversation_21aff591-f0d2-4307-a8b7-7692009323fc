// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-24 16:10:15
// FilePath: /yaml_scan/utils/errors/errlevel.go
// Description: 
package errorutil

type ErrorLevel uint

const (
	Panic ErrorLevel = iota
	Fatal
	Runtime // Default
)

func (l ErrorLevel) String() string {
	switch l {
	case Panic:
		return "PANIC"
	case Fatal:
		return "FATAL"
	case Runtime:
		return "RUNTIME"
	}
	return "RUNTIME" //default is runtime
}

