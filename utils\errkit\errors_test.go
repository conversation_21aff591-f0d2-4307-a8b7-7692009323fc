// 
// Author: chenjb
// Version: V1.0
// Date: 2025-05-21 11:16:14
// FilePath: /yaml_scan/utils/errkit/errors_test.go
// Description: 
package errkit

import (
	"encoding/json"
	"errors"
	"testing"

	"github.com/stretchr/testify/require"
)

// 测试ErrorX.init方法
func TestErrorX_init(t *testing.T) {
	// 创建一个ErrorX实例
	e := &ErrorX{}
	
	// 初始化
	e.init()
	
	// 验证record已被初始化
	require.NotNil(t, e.record, "记录应该被初始化")
	
	// 如果启用了trace，验证source也被初始化
	if EnableTrace {
		require.NotNil(t, e.source, "开启跟踪模式时，source应该被初始化")
	}
}

// 测试ErrorX.append方法
func TestErrorX_append(t *testing.T) {
	// 创建一个ErrorX实例
	e := &ErrorX{}
	e.init()
	
	// 准备两个不同的错误
	err1 := errors.New("error1")
	err2 := errors.New("error2")
	
	// 添加错误
	e.append(err1, err2)
	
	// 验证错误被正确添加
	require.Len(t, e.errs, 2, "应该有两个错误被添加")
	require.Equal(t, err1.Error(), e.errs[0].Error(), "第一个错误应该是error1")
	require.Equal(t, err2.Error(), e.errs[1].Error(), "第二个错误应该是error2")
	
	// 添加重复的错误
	e.append(err1)
	
	// 验证没有添加重复错误
	require.Len(t, e.errs, 2, "添加重复错误后，错误数量不应变化")
}

// 测试ErrorX.MarshalJSON方法
func TestErrorX_MarshalJSON(t *testing.T) {
	// 创建一个ErrorX实例
	e := New("test error")
	e.SetKind(ErrKindUnknown)
	
	// 序列化为JSON
	data, err := json.Marshal(e)
	
	// 验证序列化成功
	require.NoError(t, err, "序列化应该成功")
	require.NotNil(t, data, "序列化数据不应为空")
	
	// 验证JSON包含预期字段
	jsonStr := string(data)
	require.Contains(t, jsonStr, "kind", "JSON应包含kind字段")
	require.Contains(t, jsonStr, "errors", "JSON应包含errors字段")
}

// 测试ErrorX.Cause方法
func TestErrorX_Cause(t *testing.T) {
	// 创建一个基础错误
	baseErr := errors.New("base error")
	
	// 创建包含该错误的ErrorX
	e := FromError(baseErr)
	
	// 验证Cause返回正确的错误
	cause := e.Cause()
	require.Equal(t, baseErr.Error(), cause.Error(), "Cause应返回原始错误")
	
	// 测试空ErrorX
	emptyErr := &ErrorX{}
	require.Nil(t, emptyErr.Cause(), "空ErrorX的Cause应返回nil")
}

// 测试ErrorX.Errors方法
func TestErrorX_Errors(t *testing.T) {
	// 创建含多个错误的ErrorX
	err1 := errors.New("error1")
	err2 := errors.New("error2")
	e := Join(err1, err2).(*ErrorX)
	
	// 验证Errors返回所有错误
	errs := e.Errors()
	require.Len(t, errs, 2, "应返回两个错误")
	require.Equal(t, err1.Error(), errs[0].Error(), "第一个错误应该是error1")
	require.Equal(t, err2.Error(), errs[1].Error(), "第二个错误应该是error2")
}

// 测试ErrorX.Attrs方法
func TestErrorX_Attrs(t *testing.T) {
	// 创建带属性的ErrorX
	e := New("test error", "key", "value")
	
	// 验证Attrs返回正确的属性
	attrs := e.Attrs()
	require.NotEmpty(t, attrs, "属性不应为空")
	require.Equal(t, "key", attrs[0].Key, "属性键应为'key'")
	require.Equal(t, "value", attrs[0].Value.String(), "属性值应为'value'")
}

// 测试ErrorX.Error方法
func TestErrorX_Error(t *testing.T) {
	// 创建单个错误的ErrorX
	baseErr := errors.New("base error")
	e := FromError(baseErr)
	
	// 验证Error生成的字符串
	errStr := e.Error()
	require.Contains(t, errStr, "base error", "错误字符串应包含原始错误")
	
	// 创建多个错误的ErrorX
	err2 := errors.New("second error")
	e.append(err2)
	
	// 验证错误链
	errStr = e.Error()
	require.Contains(t, errStr, "chain=", "多个错误时应包含chain字段")
	require.Contains(t, errStr, "second error", "错误链中应包含第二个错误")
}

// 测试ErrorX.Kind和SetKind方法
func TestErrorX_Kind_SetKind(t *testing.T) {
	// 创建没有指定Kind的ErrorX
	e := New("test error")
	
	// 验证默认为Unknown
	require.Equal(t, ErrKindUnknown.String(), e.Kind().String(), "未设置Kind时应默认为Unknown")
	
	// 设置Kind
	e.SetKind(ErrKindNetworkTemporary)
	
	// 验证Kind已被设置
	require.Equal(t, ErrKindNetworkTemporary.String(), e.Kind().String(), "Kind应已被设置为NetworkTemporary")
	
	// 重置Kind
	e.ResetKind()
	
	// 验证Kind已被重置为Unknown
	require.Equal(t, ErrKindUnknown.String(), e.Kind().String(), "重置后Kind应为Unknown")
}

// 测试FromError函数
func TestFromError(t *testing.T) {
	// 创建基础错误
	baseErr := errors.New("base error")
	
	// 使用FromError解析
	e := FromError(baseErr)
	
	// 验证错误已被正确解析
	require.NotNil(t, e, "FromError应返回非nil的ErrorX")
	require.Equal(t, baseErr.Error(), e.Cause().Error(), "原始错误应被正确解析")
	
	// 测试nil错误
	e = FromError(nil)
	require.Nil(t, e, "FromError(nil)应返回nil")
}

// 测试New函数
func TestNew(t *testing.T) {
	// 创建简单错误
	e := New("test error")
	
	// 验证错误已被创建
	require.NotNil(t, e, "New应创建非nil的ErrorX")
	require.Equal(t, "test error", e.Cause().Error(), "错误消息应被正确设置")
	
	// 创建带属性的错误
	e = New("test error", "key", "value")
	
	// 验证属性已被设置
	attrs := e.Attrs()
	require.NotEmpty(t, attrs, "属性不应为空")
	require.Equal(t, "key", attrs[0].Key, "属性键应为'key'")
}

// 测试ErrorX.Msgf方法
func TestErrorX_Msgf(t *testing.T) {
	// 创建ErrorX实例
	e := New("test error")
	
	// 添加消息
	e.Msgf("additional message")
	
	// 验证消息已被添加
	errStr := e.Error()
	require.Contains(t, errStr, "additional message", "附加消息应被添加到错误中")
	
	// 对nil进行操作不应崩溃
	var nilErr *ErrorX = nil
	nilErr.Msgf("should not panic")
}

// 测试parseError函数
func TestParseError(t *testing.T) {
	// 创建一个复杂的错误层次
	baseErr := errors.New("base error")
	wrappedErr := Wrap(baseErr, "wrapped error")
	secondWrap := Wrap(wrappedErr, "second wrap")
	
	// 解析错误
	e := &ErrorX{}
	parseError(e, secondWrap)
	
	// 验证所有错误都被解析
	require.Contains(t, e.Error(), "base error", "应解析基础错误")
	require.Contains(t, e.Error(), "wrapped error", "应解析第一层包装")
	require.Contains(t, e.Error(), "second wrap", "应解析第二层包装")
	
	// 测试nil错误
	e = &ErrorX{}
	parseError(e, nil)
	require.Empty(t, e.errs, "解析nil错误应产生空错误列表")
	
	// 测试带有分隔符的错误字符串
	semicolonErr := errors.New("error1; error2")
	e = &ErrorX{}
	parseError(e, semicolonErr)
	require.Len(t, e.errs, 2, "分号分隔的错误应该被解析为两个错误")
} 

