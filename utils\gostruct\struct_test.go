//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-04 10:55:59
//FilePath: /yaml_scan/utils/struct/struct_test.go
//Description:

package structutil

import (
	"testing"

	"github.com/stretchr/testify/require"
)

func TestCalcSize(t *testing.T) {
	tests := []struct {
		format   []string
		expected int
		hasError bool
	}{
		{[]string{"b"}, 1, false},
		{[]string{"h"}, 2, false},
		{[]string{"i"}, 4, false},
		{[]string{"q"}, 8, false},
		{[]string{"s0"}, 0, false},     // 0 字节字符串
		{[]string{"s-1"}, 0, false},    // 无效字符串长度
		{[]string{"unknown"}, 0, true}, // 无效格式符号
	}

	for _, test := range tests {
		size, err := CalcSize(test.format)
		if test.hasError {
			require.Error(t, err, "Expected error for format: %v", test.format)
		} else {
			require.NoError(t, err, "Expected no error for format: %v", test.format)
			require.Equal(t, test.expected, size, "Expected size %d for format: %v", test.expected, test.format)
		}
	}
}

func TestIntToBytes(t *testing.T) {
	tests := []struct {
		n        int
		size     int
		endian   Endianess
		expected []byte
	}{
		{1, 1, LITTLE_ENDIAN, []byte{1}},
		{1, 2, LITTLE_ENDIAN, []byte{1, 0}},
		{1, 4, LITTLE_ENDIAN, []byte{1, 0, 0, 0}},
		{1, 8, LITTLE_ENDIAN, []byte{1, 0, 0, 0, 0, 0, 0, 0}},
		{1, 1, BIG_ENDIAN, []byte{1}},
		{1, 2, BIG_ENDIAN, []byte{0, 1}},
		{1, 4, BIG_ENDIAN, []byte{0, 0, 0, 1}},
		{1, 8, BIG_ENDIAN, []byte{0, 0, 0, 0, 0, 0, 0, 1}},
	}

	for _, test := range tests {
		result := intToBytes(test.n, test.size, test.endian)
		require.Equal(t, test.expected, result, "Expected %v for input: %v", test.expected, test)
	}

}

func TestBoolToBytes(t *testing.T) {
	tests := []struct {
		input    bool
		endian   Endianess
		expected []byte
	}{
		{true, LITTLE_ENDIAN, []byte{1}},
		{false, LITTLE_ENDIAN, []byte{0}},
		{true, BIG_ENDIAN, []byte{1}},
		{false, BIG_ENDIAN, []byte{0}},
	}

	for _, test := range tests {
		result := boolToBytes(test.input, test.endian)
		require.Equal(t, test.expected, result, "Expected %v for input: %v", test.expected, test)
	}
}

func TestBytesToInt(t *testing.T) {
	tests := []struct {
		input    []byte
		endian   Endianess
		expected int
	}{
		{[]byte{1}, LITTLE_ENDIAN, 1},
		{[]byte{1}, BIG_ENDIAN, 1},
		{[]byte{0, 1}, LITTLE_ENDIAN, 256},
		{[]byte{1, 0}, BIG_ENDIAN, 256},
		{[]byte{0, 0, 0, 1}, LITTLE_ENDIAN, 16777216},
		{[]byte{0, 0, 0, 1}, BIG_ENDIAN, 1},
		{[]byte{0, 0, 0, 0, 0, 0, 0, 1}, LITTLE_ENDIAN, 72057594037927936},
		{[]byte{0, 0, 0, 0, 0, 0, 0, 1}, BIG_ENDIAN, 1},
	}

	for _, test := range tests {
		result := bytesToInt(test.input, test.endian)
		// require.NoError(t, err, "Expected no error for input: %v", test)
		require.Equal(t, test.expected, result, "Expected %d for input: %v", test.expected, test)
	}

}

func TestBytesToBool(t *testing.T) {
	tests := []struct {
		input    []byte
		endian   Endianess
		expected bool
	}{
		{[]byte{1}, LITTLE_ENDIAN, true},
		{[]byte{0}, LITTLE_ENDIAN, false},
		{[]byte{1}, BIG_ENDIAN, true},
		{[]byte{0}, BIG_ENDIAN, false},
		{[]byte{0, 1}, LITTLE_ENDIAN, true},        // 1 in little-endian
		{[]byte{1, 0}, BIG_ENDIAN, true},           // 256 in big-endian
		{[]byte{0, 0, 0, 1}, LITTLE_ENDIAN, true},  // 1 in little-endian
		{[]byte{0, 0, 0, 0}, LITTLE_ENDIAN, false}, // 0 in little-endian
		{[]byte{}, LITTLE_ENDIAN, false},           // empty slice
	}

	for _, test := range tests {
		result := bytesToBool(test.input, test.endian)
		require.Equal(t, test.expected, result, "Expected %v for input: %v", test.expected, test)
	}
}

func TestBytesToFloat32(t *testing.T) {
	tests := []struct {
		input    []byte
		endian   Endianess
		expected float32
	}{
		{[]byte{0x00, 0x00, 0x80, 0x3F}, LITTLE_ENDIAN, 1.0},     // 1.0 in little-endian
		{[]byte{0x00, 0x00, 0x00, 0x40}, LITTLE_ENDIAN, 2.0},     // 2.0 in little-endian
		{[]byte{0x00, 0x00, 0xA0, 0xC1}, LITTLE_ENDIAN, -20},     // -5.0 in little-endian
		{[]byte{0x00, 0x00, 0x80, 0x3F}, BIG_ENDIAN, 4.6006e-41}, // 1.0 in big-endian
		{[]byte{0x00, 0x00, 0x00, 0x40}, BIG_ENDIAN, 9e-44},      // 2.0 in big-endian
	}

	for _, test := range tests {
		result := bytesToFloat32(test.input, test.endian)
		require.Equal(t, test.expected, result, "Expected %f for input: %v", test.expected, test)
	}
}

func TestBytesToUint(t *testing.T) {
	tests := []struct {
		input    []byte
		expected int
	}{
		{[]byte{1}, 1},              // 1 byte
		{[]byte{0, 1}, 256},         // 2 bytes
		{[]byte{0, 0, 1, 0}, 65536}, // 4 bytes
		{[]byte{0, 0, 0, 0, 0, 0, 0, 1}, 72057594037927936}, // 8 bytes

	}

	for _, test := range tests {
		result := bytesToUint(test.input)
		require.Equal(t, test.expected, result, "Expected %d for input: %v", test.expected, test)
	}
}

func TestUnPack(t *testing.T) {
	// 假设 msg 是通过 Pack 函数打包的字节切片
	msg := []byte{0x01, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x3F, 0x80, 0x00, 0x00} // 1, 2, 1.0
	format := []string{"i", "i", "f"}                                                     // 期望解包的格式

	result, err := UnPack(format, msg)
	require.NoError(t, err, "Expected no error")
	require.Equal(t, []interface{}{1, 2, float32(4.6006e-41)}, result, "Expected unpacked result to match")
}

