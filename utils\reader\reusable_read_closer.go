// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 19:12:26
// FilePath: /yaml_scan/utils/reader/reusable_read_closer.go
// Description: 提供可重复使用的读取器实现，允许多次读取同一内容而无需重新加载数据源
package reader

import (
	"bytes"
	"errors"
	"fmt"
	"io"
	"strings"
	"sync"
)

// ReusableReadCloser 是一个可重用的读取器，具有空操作的关闭方法
// 它允许内容被多次读取，而不会像普通readers那样在读取后耗尽
type ReusableReadCloser struct {
	*sync.RWMutex               // 读写锁，用于保护并发读写操作
	io.Reader                   // 嵌入式Reader接口实现
	readBuf       *bytes.Buffer // 主要读取缓冲区
	backBuf       *bytes.Buffer // 备份缓冲区，用于存储已读取的数据以便重用
}

// NewReusableReadCloser 为任何类型的输入创建并返回一个ReusableReadCloser
// 支持多种类型的输入，包括[]byte、string、各种缓冲区和读取器
// @param raw interface{}:  任意类型的输入数据，可以是[]byte、string、io.Reader等
// @return *ReusableReadCloser *ReusableReadCloser: 创建的可重用读取器
// @return error error: 创建过程中可能发生的错误
func NewReusableReadCloser(raw interface{}) (*ReusableReadCloser, error) {
	// 创建两个缓冲区，readBuf用于主要读取，backBuf用于备份已读数据
	readBuf := bytes.Buffer{}
	backBuf := bytes.Buffer{}
	if raw != nil {
		switch body := raw.(type) {

		case []byte:
			// 如果是字节数组，创建一个从这些字节读取的缓冲区
			readBuf = *bytes.NewBuffer(body)

		case *[]byte:
			// 如果是指向字节数组的指针，解引用后创建缓冲区
			readBuf = *bytes.NewBuffer(*body)

		case string:
			// 如果是字符串，创建一个从该字符串读取的缓冲区
			readBuf = *bytes.NewBufferString(body)

		case *bytes.Buffer:
			// 如果提供的是字节缓冲区指针，直接使用它
			readBuf = *body

		case *bytes.Reader:
			// if *bytes.Reader , make buffer read from reader
			if _, er := readBuf.ReadFrom(body); er != nil {
				return nil, er
			}

		case *strings.Reader:
			// 如果是字符串读取器，将其内容读入readBuf
			if _, er := readBuf.ReadFrom(body); er != nil {
				return nil, er
			}

		case io.ReadSeeker:
			// 如果实现了ReadSeeker接口，将其内容读入readBuf
			if _, er := readBuf.ReadFrom(body); er != nil {
				return nil, er
			}

		case io.Reader:
				// 如果实现了Reader接口，将其内容读入readBuf
			if _, er := readBuf.ReadFrom(body); er != nil {
				return nil, er
			}
		default:
			// 不支持的类型或无法处理的类型
			return nil, fmt.Errorf("cannot handle type %T", body)
		}

	}
	// 创建并返回ReusableReadCloser实例
	reusableReadCloser := &ReusableReadCloser{
		&sync.RWMutex{},                  // 初始化读写锁
		io.TeeReader(&readBuf, &backBuf), // 创建一个TeeReader，读取时会同时向backBuf写入
		&readBuf,                         // 主读取缓冲区
		&backBuf,                         // 备份缓冲区
	}

	return reusableReadCloser, nil

}

// Read 实现了io.Reader接口的Read方法
// 从Reader读取数据到提供的缓冲区，并在读完所有数据后重置Reader
// @receiver r 
// @param p []byte:  用于存储读取数据的字节切片
// @return int int: 读取的字节数
// @return error error: 读取过程中可能发生的错误
func (r ReusableReadCloser) Read(p []byte) (int, error) {
	// 加锁以确保并发安全
	r.Lock()
	defer r.Unlock()

	// 从嵌入的Reader读取数据
	n, err := r.Reader.Read(p)
	// 如果读取到文件末尾，重置Reader以便可以再次读取
	if errors.Is(err, io.EOF) {
		r.reset()
	}
	return n, err
}

// reset 重置Reader，使其可以再次从头开始读取
// @receiver r 
func (r ReusableReadCloser) reset() {
	// 将备份缓冲区中的数据复制回主读取缓冲区
	// 忽略可能的错误，因为复制操作不太可能失败
	_, _ = io.Copy(r.readBuf, r.backBuf)
}

// Close 实现了io.Closer接口的Close方法
// 对于ReusableReadCloser，这是一个空操作，不执行任何实际关闭
// @receiver r 
// @return error error: 
func (r ReusableReadCloser) Close() error {
	return nil
}

