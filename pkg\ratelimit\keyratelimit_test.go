// Author: chenjb
// Version: V1.0
// Date: 2025-06-12 15:21:28
// FilePath: /yaml_scan/pkg/ratelimit/keyratelimit_test.go
// Description:
package ratelimit

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestOptionsValidate 测试Options的Validate方法
// 验证配置选项的验证逻辑
func TestOptionsValidate(t *testing.T) {
	// 测试有效配置
	opts := &Options{
		Key:         "test",
		IsUnlimited: false,
		MaxCount:    10,
		Duration:    100 * time.Millisecond,
	}
	err := opts.Validate()
	require.NoError(t, err, "有效配置应通过验证")

	// 测试无限制配置
	unlimitedOpts := &Options{
		IsUnlimited: true,
	}
	err = unlimitedOpts.Validate()
	require.NoError(t, err, "无限制配置应通过验证，无需其他参数")

	// 测试无效配置：空键
	invalidKeyOpts := &Options{
		Key:         "",
		IsUnlimited: false,
		MaxCount:    10,
		Duration:    100 * time.Millisecond,
	}
	err = invalidKeyOpts.Validate()
	require.Error(t, err, "空键应该报错")
	require.Contains(t, err.Error(), "empty keys not allowed", "错误消息应该提及空键")

	// 测试无效配置：零最大计数
	invalidMaxCountOpts := &Options{
		Key:         "test",
		IsUnlimited: false,
		MaxCount:    0,
		Duration:    100 * time.Millisecond,
	}
	err = invalidMaxCountOpts.Validate()
	require.Error(t, err, "零最大计数应该报错")
	require.Contains(t, err.Error(), "maxcount cannot be zero", "错误消息应该提及零最大计数")

	// 测试无效配置：零时间间隔
	invalidDurationOpts := &Options{
		Key:         "test",
		IsUnlimited: false,
		MaxCount:    10,
		Duration:    0,
	}
	err = invalidDurationOpts.Validate()
	require.Error(t, err, "零时间间隔应该报错")
	require.Contains(t, err.Error(), "time duration not set", "错误消息应该提及零时间间隔")
}

// TestMultiLimiterAdd 测试MultiLimiter的Add方法
// 验证添加限流器实例的逻辑
func TestMultiLimiterAdd(t *testing.T) {
	ctx := context.Background()

	// 创建有效配置
	opts := &Options{
		Key:         "test1",
		IsUnlimited: false,
		MaxCount:    10,
		Duration:    100 * time.Millisecond,
	}

	// 创建MultiLimiter实例
	multiLimiter, err := NewMultiLimiter(ctx, opts)
	require.NoError(t, err, "创建MultiLimiter应成功")

	// 尝试添加一个新的限流器实例
	newOpts := &Options{
		Key:         "test2",
		IsUnlimited: false,
		MaxCount:    5,
		Duration:    200 * time.Millisecond,
	}
	err = multiLimiter.Add(newOpts)
	require.NoError(t, err, "添加新的限流器实例应成功")

	// 尝试添加一个已存在键的限流器实例
	err = multiLimiter.Add(opts)
	require.Error(t, err, "添加已存在键的限流器实例应失败")
	require.Contains(t, err.Error(), "key: test1", "错误消息应该提及已存在的键")

	// 尝试添加一个无效配置的限流器实例
	invalidOpts := &Options{
		Key:         "",
		IsUnlimited: false,
		MaxCount:    10,
		Duration:    100 * time.Millisecond,
	}
	err = multiLimiter.Add(invalidOpts)
	require.Error(t, err, "添加无效配置的限流器实例应失败")
	require.Contains(t, err.Error(), "empty keys not allowed", "错误消息应该提及配置无效的原因")
}

// TestMultiLimiterGetLimit 测试MultiLimiter的GetLimit方法
// 验证获取限流器限制速率的逻辑
func TestMultiLimiterGetLimit(t *testing.T) {
	ctx := context.Background()

	// 创建MultiLimiter实例
	opts := &Options{
		Key:         "test",
		IsUnlimited: false,
		MaxCount:    10,
		Duration:    100 * time.Millisecond,
	}
	multiLimiter, err := NewMultiLimiter(ctx, opts)
	require.NoError(t, err, "创建MultiLimiter应成功")

	// 获取已存在键的限制速率
	limit, err := multiLimiter.GetLimit("test")
	require.NoError(t, err, "获取已存在键的限制速率应成功")
	require.Equal(t, uint(10), limit, "限制速率应为10")

	// 获取不存在键的限制速率
	_, err = multiLimiter.GetLimit("nonexistent")
	require.Error(t, err, "获取不存在键的限制速率应失败")
	require.Contains(t, err.Error(), "key: nonexistent", "错误消息应该提及不存在的键")
}

// TestMultiLimiterTake 测试MultiLimiter的Take方法
// 验证从限流器获取令牌的逻辑
func TestMultiLimiterTake(t *testing.T) {
	ctx := context.Background()

	// 创建MultiLimiter实例
	opts := &Options{
		Key:         "test",
		IsUnlimited: false,
		MaxCount:    3,
		Duration:    100 * time.Millisecond,
	}
	multiLimiter, err := NewMultiLimiter(ctx, opts)
	require.NoError(t, err, "创建MultiLimiter应成功")

	// 从已存在键的限流器获取令牌
	for i := 0; i < 3; i++ {
		require.True(t, multiLimiter.CanTake("test"), "应该能够获取令牌")
		err = multiLimiter.Take("test")
		require.NoError(t, err, "获取令牌应成功")
	}

	// 此时应该没有可用令牌
	require.False(t, multiLimiter.CanTake("test"), "所有令牌都已被使用，应该无法获取更多令牌")

	// 从不存在键的限流器获取令牌
	err = multiLimiter.Take("nonexistent")
	require.Error(t, err, "从不存在键的限流器获取令牌应失败")
	require.Contains(t, err.Error(), "key: nonexistent", "错误消息应该提及不存在的键")

	// 等待令牌重新填充
	time.Sleep(150 * time.Millisecond)
	require.True(t, multiLimiter.CanTake("test"), "令牌应该已经重新填充")
}

// TestMultiLimiterAddAndTake 测试MultiLimiter的AddAndTake方法
// 验证添加限流器实例并获取令牌的逻辑
func TestMultiLimiterAddAndTake(t *testing.T) {
	ctx := context.Background()

	// 创建MultiLimiter实例
	opts := &Options{
		Key:         "test",
		IsUnlimited: false,
		MaxCount:    3,
		Duration:    100 * time.Millisecond,
	}
	multiLimiter, err := NewMultiLimiter(ctx, opts)
	require.NoError(t, err, "创建MultiLimiter应成功")

	// 添加并获取已存在键的令牌
	multiLimiter.AddAndTake(opts)
	require.True(t, multiLimiter.CanTake("test"), "应该还能获取令牌")

	// 添加并获取新键的令牌
	newOpts := &Options{
		Key:         "test2",
		IsUnlimited: false,
		MaxCount:    5,
		Duration:    200 * time.Millisecond,
	}
	multiLimiter.AddAndTake(newOpts)
	require.True(t, multiLimiter.CanTake("test2"), "应该能够获取新键的令牌")
	_, err = multiLimiter.GetLimit("test2")
	require.NoError(t, err, "应该可以获取新键的限制速率")
}

// TestMultiLimiterStop 测试MultiLimiter的Stop方法
// 验证停止限流器实例的逻辑
func TestMultiLimiterStop(t *testing.T) {
	ctx := context.Background()

	// 创建MultiLimiter实例，添加多个限流器
	multiLimiter, _ := NewMultiLimiter(ctx, &Options{
		Key:         "test1",
		IsUnlimited: false,
		MaxCount:    3,
		Duration:    100 * time.Millisecond,
	})
	multiLimiter.Add(&Options{
		Key:         "test2",
		IsUnlimited: false,
		MaxCount:    5,
		Duration:    200 * time.Millisecond,
	})

	// 停止特定键的限流器
	multiLimiter.Stop("test1")
	// 由于内部实现，我们无法直接验证限流器是否已停止
	// 但至少可以验证代码执行没有崩溃

	// 停止所有限流器
	multiLimiter.Stop()
	// 同样，我们无法直接验证所有限流器是否已停止
	// 但可以验证代码执行没有崩溃
}
