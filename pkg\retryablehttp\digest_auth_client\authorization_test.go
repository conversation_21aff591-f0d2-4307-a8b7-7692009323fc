// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-11 17:17:15
// FilePath: /yaml_scan/pkg/retryablehttp/digest_auth_client/authorization_test.go
// Description: 
package digestauthclient

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestAuthorizationHash 测试根据不同算法计算哈希值的功能
func TestAuthorizationHash(t *testing.T) {
	// 创建测试用例
	tests := []struct {
		name      string // 测试用例名称
		algorithm string // 哈希算法
		input     string // 输入字符串
		expected  string // 期望的哈希结果（十六进制）
	}{
		{
			name:      "MD5哈希算法",
			algorithm: "MD5",
			input:     "test",
			expected:  "098f6bcd4621d373cade4e832627b4f6",
		},
		{
			name:      "SHA-256哈希算法",
			algorithm: "SHA-256",
			input:     "test",
			expected:  "9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08",
		},
		{
			name:      "默认算法（未指定时使用MD5）",
			algorithm: "",
			input:     "test",
			expected:  "098f6bcd4621d373cade4e832627b4f6",
		},
		{
			name:      "MD5-SESS算法",
			algorithm: "MD5-SESS",
			input:     "test",
			expected:  "098f6bcd4621d373cade4e832627b4f6",
		},
		{
			name:      "SHA-256-SESS算法",
			algorithm: "SHA-256-SESS",
			input:     "test",
			expected:  "9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08",
		},
		{
			name:      "未知算法",
			algorithm: "UNKNOWN",
			input:     "test",
			expected:  "",
		},
	}

	// 执行测试用例
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建授权对象
			ah := &authorization{
				Algorithm: tt.algorithm,
			}

			// 调用被测函数
			result := ah.hash(tt.input)

			// 验证结果
			assert.Equal(t, tt.expected, result, "哈希结果不匹配")
		})
	}
}

// TestComputeA1 测试计算A1值
func TestComputeA1(t *testing.T) {
	// 创建测试用例
	tests := []struct {
		name      string // 测试用例名称
		algorithm string // 哈希算法
		username  string // 用户名
		realm     string // 域
		password  string // 密码
		nonce     string // 服务器随机数
		cnonce    string // 客户端随机数
		expected  string // 期望的A1值（格式化字符串，非哈希值）
	}{
		{
			name:      "MD5算法",
			algorithm: "MD5",
			username:  "user",
			realm:     "realm",
			password:  "pass",
			nonce:     "nonce",
			cnonce:    "cnonce",
			expected:  "user:realm:pass",
		},
		{
			name:      "SHA-256算法",
			algorithm: "SHA-256",
			username:  "user",
			realm:     "realm",
			password:  "pass",
			nonce:     "nonce",
			cnonce:    "cnonce",
			expected:  "user:realm:pass",
		},
		{
			name:      "MD5-SESS算法",
			algorithm: "MD5-SESS",
			username:  "user",
			realm:     "realm",
			password:  "pass",
			nonce:     "nonce",
			cnonce:    "cnonce",
			// 这里expectedHash应该是MD5("user:realm:pass"):nonce:cnonce
			// 但在测试中我们只检查格式，具体哈希值在computeResponse中计算
			expected: "MD5_HASH:nonce:cnonce",
		},
		{
			name:      "SHA-256-SESS算法",
			algorithm: "SHA-256-SESS",
			username:  "user",
			realm:     "realm",
			password:  "pass",
			nonce:     "nonce",
			cnonce:    "cnonce",
			// 这里expectedHash应该是SHA-256("user:realm:pass"):nonce:cnonce
			expected: "SHA256_HASH:nonce:cnonce",
		},
	}

	// 执行测试用例
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建授权对象
			ah := &authorization{
				Algorithm: tt.algorithm,
				Username:  tt.username,
				Realm:     tt.realm,
				Nonce:     tt.nonce,
				Cnonce:    tt.cnonce,
			}

			// 创建请求对象
			dr := &DigestRequest{
				Password: tt.password,
			}

			// 调用被测函数
			result := ah.computeA1(dr)

			// 验证结果
			if tt.algorithm == "MD5" || tt.algorithm == "SHA-256" || tt.algorithm == "" {
				assert.Equal(t, tt.expected, result, "A1值不匹配")
			} else if tt.algorithm == "MD5-SESS" {
				// 检查格式：MD5(用户名:域:密码):nonce:cnonce
				upHash := ah.hash(tt.username + ":" + tt.realm + ":" + tt.password)
				expected := upHash + ":" + tt.nonce + ":" + tt.cnonce
				assert.Equal(t, expected, result, "MD5-SESS A1值格式不匹配")
			} else if tt.algorithm == "SHA-256-SESS" {
				// 检查格式：SHA-256(用户名:域:密码):nonce:cnonce
				upHash := ah.hash(tt.username + ":" + tt.realm + ":" + tt.password)
				expected := upHash + ":" + tt.nonce + ":" + tt.cnonce
				assert.Equal(t, expected, result, "SHA-256-SESS A1值格式不匹配")
			}
		})
	}
}

// TestComputeA2 测试计算A2值
func TestComputeA2(t *testing.T) {
	// 创建测试用例
	tests := []struct {
		name      string // 测试用例名称
		qop       string // 服务质量参数
		method    string // HTTP方法
		uri       string // 请求URI
		body      string // 请求体
		expected  string // 期望的A2值
		resultQop string // 期望设置到授权对象的QoP值
	}{
		{
			name:      "auth模式",
			qop:       "auth",
			method:    "GET",
			uri:       "/api/resource",
			body:      "request body",
			expected:  "GET:/api/resource",
			resultQop: "auth",
		},
		{
			name:      "auth-int模式",
			qop:       "auth-int",
			method:    "POST",
			uri:       "/api/resource",
			body:      "request body",
			expected:  "POST:/api/resource:BODY_HASH",
			resultQop: "auth-int",
		},
		{
			name:      "混合模式，优先选择auth-int",
			qop:       "auth,auth-int",
			method:    "PUT",
			uri:       "/api/resource",
			body:      "request body",
			expected:  "PUT:/api/resource:BODY_HASH",
			resultQop: "auth-int",
		},
		{
			name:      "未指定QoP，默认使用auth",
			qop:       "",
			method:    "DELETE",
			uri:       "/api/resource",
			body:      "request body",
			expected:  "DELETE:/api/resource",
			resultQop: "auth",
		},
		{
			name:      "未知QoP类型",
			qop:       "unknown",
			method:    "GET",
			uri:       "/api/resource",
			body:      "request body",
			expected:  "",
			resultQop: "",
		},
	}

	// 执行测试用例
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建授权对象
			ah := &authorization{
				URI:       tt.uri,
				Algorithm: "MD5", // 使用MD5简化测试
			}

			// 创建认证挑战信息
			wa := &wwwAuthenticate{
				Qop: tt.qop,
			}

			// 创建请求对象
			dr := &DigestRequest{
				Method: tt.method,
				Body:   tt.body,
				Wa:     wa,
			}


			// 调用被测函数
			result := ah.computeA2(dr)

			// 验证结果
			if tt.qop == "auth" || tt.qop == "" || tt.qop == "unknown" {
				assert.Equal(t, tt.expected, result, "A2值不匹配")
			} else {
				// 对于auth-int或混合模式，验证结果是否包含正确的格式
				expectedFormat := tt.method + ":" + tt.uri + ":621674f3152fefcc6d9e36c3cd2a2701"
				assert.Equal(t, expectedFormat, result, "A2值格式不匹配")
			}

			// 验证QoP设置是否正确
			assert.Equal(t, tt.resultQop, ah.Qop, "设置的QoP值不匹配")
		})
	}
}


// TestAuthorizationToString 测试生成授权头字符串
func TestAuthorizationToString(t *testing.T) {
	// 测试用例：所有字段都有值
	t.Run("所有字段都有值", func(t *testing.T) {
		ah := &authorization{
			Algorithm: "MD5",
			Cnonce:    "testcnonce",
			Nc:        1,
			Nonce:     "testnonce",
			Opaque:    "testopaque",
			Qop:       "auth",
			Realm:     "testrealm",
			Response:  "testresponse",
			URI:       "/test",
			Userhash:  true,
			Username:  "testuser",
		}

		result := ah.toString()

		// 验证生成的字符串是否包含所有字段
		assert.Contains(t, result, "Digest ")
		assert.Contains(t, result, `username="testuser"`)
		assert.Contains(t, result, `realm="testrealm"`)
		assert.Contains(t, result, `nonce="testnonce"`)
		assert.Contains(t, result, `uri="/test"`)
		assert.Contains(t, result, `response="testresponse"`)
		assert.Contains(t, result, "algorithm=MD5")
		assert.Contains(t, result, `cnonce="testcnonce"`)
		assert.Contains(t, result, `opaque="testopaque"`)
		assert.Contains(t, result, "qop=auth")
		assert.Contains(t, result, "nc=00000001")
		assert.Contains(t, result, "userhash=true")

		// 验证最后没有多余的逗号和空格
		assert.False(t, result[len(result)-2:] == ", ", "字符串不应以逗号空格结尾")
	})

	// 测试用例：只有必要字段
	t.Run("只有必要字段", func(t *testing.T) {
		ah := &authorization{
			Nonce:    "testnonce",
			Realm:    "testrealm",
			Response: "testresponse",
			URI:      "/test",
			Username: "testuser",
		}

		result := ah.toString()

		// 验证生成的字符串
		assert.Contains(t, result, "Digest ")
		assert.Contains(t, result, `username="testuser"`)
		assert.Contains(t, result, `realm="testrealm"`)
		assert.Contains(t, result, `nonce="testnonce"`)
		assert.Contains(t, result, `uri="/test"`)
		assert.Contains(t, result, `response="testresponse"`)

		// 验证不包含可选字段
		assert.NotContains(t, result, "algorithm=")
		assert.NotContains(t, result, "cnonce=")
		assert.NotContains(t, result, "opaque=")
		assert.NotContains(t, result, "qop=")
		assert.NotContains(t, result, "nc=")
		assert.NotContains(t, result, "userhash=")
	})
}

// TestRefreshAuthorization 测试刷新授权信息
func TestRefreshAuthorization(t *testing.T) {
	// 设置初始授权对象
	ah := &authorization{
		Algorithm: "MD5",
		Cnonce:    "oldcnonce",
		Nc:        5, // 当前计数器值
		Nonce:     "testnonce",
		Opaque:    "testopaque",
		Qop:       "auth",
		Realm:     "testrealm",
		Response:  "oldresponse",
		URI:       "/olduri",
		Username:  "olduser",
		Userhash:  false,
	}

	// 创建请求对象
	dr := &DigestRequest{
		Method:   "GET",
		Password: "testpass",
		URI:      "http://example.com/newuri?param=value",
		Username: "newuser",
		Wa: &wwwAuthenticate{
			Userhash: false,
		},
	}

	
	// 调用被测函数
	result, err := ah.refreshAuthorization(dr)

	// 验证结果
	require.NoError(t, err, "刷新授权应成功")
	require.NotNil(t, result, "应返回非空授权对象")

	// 验证更新的字段
	assert.Equal(t, "newuser", result.Username, "用户名未正确更新")
	assert.Equal(t, 6, result.Nc, "计数器未递增")
	//assert.Equal(t, "508a0ff767054907f2f4ff5b951aa885", result.Cnonce, "客户端随机数未更新")
	assert.Equal(t, "/newuri?param=value", result.URI, "URI未正确提取")
	//assert.Equal(t, "8741c99b32ef7533e532013647ce3225", result.Response, "响应未重新计算")

	// 验证未变更的字段
	assert.Equal(t, "MD5", result.Algorithm, "算法不应变化")
	assert.Equal(t, "testnonce", result.Nonce, "服务器随机数不应变化")
	assert.Equal(t, "testopaque", result.Opaque, "不透明字符串不应变化")
	assert.Equal(t, "testrealm", result.Realm, "域不应变化")
}

// TestNewAuthorization 测试创建新的授权对象
func TestNewAuthorization(t *testing.T) {
	// 创建WWW-Authenticate挑战信息
	wa := &wwwAuthenticate{
		Algorithm: "MD5",
		Nonce:     "testnonce",
		Opaque:    "testopaque",
		Qop:       "auth",
		Realm:     "testrealm",
		Userhash:  false,
	}

	// 创建请求对象
	dr := &DigestRequest{
		Method:   "GET",
		Password: "testpass",
		URI:      "http://example.com/test",
		Username: "testuser",
		Wa:       wa,
	}

	// 调用被测函数
	result, err := newAuthorization(dr)

	// 验证结果
	require.NoError(t, err, "创建授权对象应成功")
	require.NotNil(t, result, "应返回非空授权对象")

	// 验证初始化的字段
	assert.Equal(t, "MD5", result.Algorithm, "算法应从挑战中获取")
	assert.Equal(t, "testnonce", result.Nonce, "nonce应从挑战中获取")
	assert.Equal(t, "testopaque", result.Opaque, "opaque应从挑战中获取")
	assert.Equal(t, "testrealm", result.Realm, "realm应从挑战中获取")
	assert.Equal(t, false, result.Userhash, "userhash应从挑战中获取")
	assert.Equal(t, "testuser", result.Username, "用户名应从请求中获取")
	assert.Equal(t, 1, result.Nc, "计数器应初始化为1")
	assert.NotEmpty(t, result.Cnonce, "应生成客户端随机数")
	assert.Equal(t, "/test", result.URI, "应提取URI路径")
	assert.NotEmpty(t, result.Response, "应计算响应值")
}

// // TestUserNameHashingInRefreshAuthorization 测试用户名哈希处理
// func TestUserNameHashingInRefreshAuthorization(t *testing.T) {
// 	// 测试启用用户名哈希的情况
// 	t.Run("启用用户名哈希", func(t *testing.T) {
// 		// 设置初始授权对象
// 		ah := &authorization{
// 			Algorithm: "MD5",
// 			Realm:     "testrealm",
// 			Userhash:  true, // 启用用户名哈希
// 		}

// 		// 创建请求对象
// 		dr := &DigestRequest{
// 			Username: "testuser",
// 			Wa: &wwwAuthenticate{
// 				Userhash: true,
// 			},
// 		}

// 		// 记录原始hash方法并替换为模拟函数
// 		originalHash := ah.hash
// 		defer func() { ah.hash = originalHash }()

// 		hashCalled := false
// 		ah.hash = func(s string) string {
// 			if s == "testuser:testrealm" {
// 				hashCalled = true
// 				return "HASHED_USERNAME"
// 			}
// 			return originalHash(s)
// 		}

// 		// 调用被测函数（简化版，只测试用户名哈希）
// 		ah.Username = dr.Username
// 		if ah.Userhash {
// 			ah.Username = ah.hash(ah.Username + ":" + ah.Realm)
// 		}

// 		// 验证结果
// 		assert.True(t, hashCalled, "应调用hash函数处理用户名")
// 		assert.Equal(t, "HASHED_USERNAME", ah.Username, "用户名应被哈希处理")
// 	})

// 	// 测试禁用用户名哈希的情况
// 	t.Run("禁用用户名哈希", func(t *testing.T) {
// 		// 设置初始授权对象
// 		ah := &authorization{
// 			Algorithm: "MD5",
// 			Realm:     "testrealm",
// 			Userhash:  false, // 禁用用户名哈希
// 		}

// 		// 创建请求对象
// 		dr := &DigestRequest{
// 			Username: "testuser",
// 			Wa: &wwwAuthenticate{
// 				Userhash: false,
// 			},
// 		}

// 		// 记录原始hash方法并替换为模拟函数
// 		originalHash := ah.hash
// 		defer func() { ah.hash = originalHash }()

// 		hashCalled := false
// 		ah.hash = func(s string) string {
// 			if s == "testuser:testrealm" {
// 				hashCalled = true
// 				return "HASHED_USERNAME"
// 			}
// 			return originalHash(s)
// 		}

// 		// 调用被测函数（简化版，只测试用户名哈希）
// 		ah.Username = dr.Username
// 		if ah.Userhash {
// 			ah.Username = ah.hash(ah.Username + ":" + ah.Realm)
// 		}

// 		// 验证结果
// 		assert.False(t, hashCalled, "不应调用hash函数处理用户名")
// 		assert.Equal(t, "testuser", ah.Username, "用户名不应被哈希处理")
// 	})
// }


