package gologger

import (
	"bytes"
	"testing"
	"fmt"

	"yaml_scan/pkg/gologger/levels"
	"yaml_scan/pkg/gologger/formatter"
)


// MockWriter 是一个模拟的写入器，用于测试
type MockWriter struct {
	buffer bytes.Buffer
}


// Write 实现 writer.Writer 接口
func (m *MockWriter) Write(data []byte, level levels.Level) {
	m.buffer.Write(data)
}


// TestLogger 测试 Logger 的功能
func TestLogger(t *testing.T) {
	mockWriter := &MockWriter{}
	logger := &Logger{}
	logger.SetWriter(mockWriter)
	logger.SetMaxLevel(levels.LevelDebug) // 设置最大日志级别为 Debug
	logger.SetFormatter(formatter.NewCLI(true)) // 使用 CLI 格式化器

	tests := []struct {
		level    levels.Level
		message  string
		expected string
	}{
		{levels.LevelInfo, "This is an info message.", "This is an info message."},
		{levels.LevelDebug, "This is a debug message.", "This is a debug message."},
		{levels.LevelWarning, "This is a warning message.", "This is a warning message."},
		{levels.LevelError, "This is an error message.", "This is an error message."},
		{levels.LevelFatal, "This is a fatal message.", "This is a fatal message."},
	}

	for _, tt := range tests {
		t.Run(fmt.Sprintf("Log level %s", tt.level), func(t *testing.T) {
			event := newEventWithLevelAndLogger(tt.level, logger)
			event.Msg(tt.message)

			// 检查写入的内容是否符合预期
			if got := mockWriter.buffer.String(); got != tt.expected {
				t.Errorf("expected %q, got %q", tt.expected, got)
			}

			// 清空缓冲区以便下一个测试
			mockWriter.buffer.Reset()
		})
	}
}


// TestLoggerWithLabel 测试带标签的日志记录
func TestLoggerWithLabel(t *testing.T) {
	mockWriter := &MockWriter{}
	logger := &Logger{}
	logger.SetWriter(mockWriter)
	logger.SetMaxLevel(levels.LevelDebug)
	logger.SetFormatter(formatter.NewCLI(true))

	event := newEventWithLevelAndLogger(levels.LevelInfo, logger)
	event.Str("label", "TEST").Msg("This is a message with a label.")

	expected := "[TEST] This is a message with a label."
	if got := mockWriter.buffer.String(); got != expected {
		t.Errorf("expected %q, got %q", expected, got)
	}
	// 清空缓冲区以便下一个测试
	mockWriter.buffer.Reset()
}


// TestLoggerWithTimestamp 测试带时间戳的日志记录
func TestLoggerWithTimestamp(t *testing.T) {
	mockWriter := &MockWriter{}
	logger := &Logger{}
	logger.SetWriter(mockWriter)
	logger.SetMaxLevel(levels.LevelDebug)
	logger.SetFormatter(formatter.NewCLI(true)) // 禁用颜色
	logger.timestamp = true
	logger.timestampMinLevel = levels.LevelInfo

	event := newEventWithLevelAndLogger(levels.LevelInfo, logger)
	event.TimeStamp().Msg("This is a message with a timestamp.")

	// 检查输出中是否包含时间戳
	if !bytes.Contains(mockWriter.buffer.Bytes(), []byte("This is a message with a timestamp.")) {
		t.Errorf("expected message to be logged, but it was not")
	}
	// 清空缓冲区以便下一个测试
	mockWriter.buffer.Reset()
}


// TestLoggerWithMetadata 测试带有元数据的日志记录
func TestLoggerWithMetadata(t *testing.T) {
	mockWriter := &MockWriter{}
	logger := &Logger{}
	logger.SetWriter(mockWriter)
	logger.SetMaxLevel(levels.LevelDebug)
	logger.SetFormatter(formatter.NewCLI(true)) // 禁用颜色

	event := newEventWithLevelAndLogger(levels.LevelInfo, logger)
	event.Str("key1", "value1").Str("key2", "value2").Msg("This is a message with metadata.")

	expected := "This is a message with metadata. key1=value1 key2=value2"
	if got := mockWriter.buffer.String(); got != expected {
		t.Errorf("expected %q, got %q", expected, got)
	}

	// 清空缓冲区以便下一个测试
	mockWriter.buffer.Reset()
}


// TestFatalLog 测试致命日志
func TestFatalLog(t *testing.T) {
	mockWriter := &MockWriter{}
	logger := &Logger{}
	logger.SetWriter(mockWriter)
	logger.SetMaxLevel(levels.LevelFatal) // 设置最大日志级别为 Fatal
	logger.SetFormatter(formatter.NewCLI(true)) // 禁用颜色

	event := newEventWithLevelAndLogger(levels.LevelFatal, logger)

	// 使用 defer 捕获 os.Exit
	defer func() {
		if r := recover(); r == nil {
			t.Errorf("expected panic on fatal log, but did not panic")
		}
	}()
	event.Msg("This is a fatal message.")

	// 清空缓冲区以便下一个测试
	mockWriter.buffer.Reset()
}


// TestGetFunctionNameAndLine 测试 GetFunctionNameAndLine 函数
func TestGetFunctionNameAndLine(t *testing.T) {
	// 定义一个内部函数，用于测试
	internalFunc := func() {
		_, line := GetFunctionNameAndLine()
		if line == 0 {
			t.Error("Expected line number to be greater than 0")
		}
	}

	// 定义一个外部函数，用于测试
	externalFunc := func() {
		func() {
			func() {
				func() {
					func() {
						func() {
							// 调用内部函数
							internalFunc()
						}()
					}()
				}()
			}()
		}()
	}

	// 调用外部函数
	externalFunc()
}


// TestIsInternalFunction 测试 isInternalFunction 函数
func TestIsInternalFunction(t *testing.T) {
	tests := []struct {
		funcName string
		expected bool
	}{
		{"gologger.TestIsInternalFunction", true}, // 属于内部函数
		{"main.main", false},                       // 不属于内部函数
		{"gologger.GetFunctionNameAndLine", true}, // 属于内部函数
		{"fmt.Println", false},                     // 不属于内部函数
	}

	for _, test := range tests {
		result := isInternalFunction(test.funcName)
		if result != test.expected {
			t.Errorf("isInternalFunction(%q) = %v; want %v", test.funcName, result, test.expected)
		}
	}
}