// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-17 16:31:58
// FilePath: /yaml_scan/pkg/catalog/config/constants.go
// Description: 
package config

const (
	TemplateConfigFileName          = ".templates-config.json"
	NucleiTemplatesDirName          = "nuclei-templates"
	OfficialNucleiTemplatesRepoName = "nuclei-templates"
	NucleiIgnoreFileName            = ".nuclei-ignore"
	NucleiTemplatesIndexFileName    = ".templates-index" // contains index of official nuclei templates
	NucleiTemplatesCheckSumFileName = ".checksum"
	NewTemplateAdditionsFileName    = ".new-additions"
	CLIConfigFileName               = "config.yaml"
	ReportingConfigFilename         = "reporting-config.yaml"
	// Version is the current version of nuclei
	Version = `v3.4.5`
	// Directory Names of custom templates
	CustomS3TemplatesDirName     = "s3"
	CustomGitHubTemplatesDirName = "github"
	CustomAzureTemplatesDirName  = "azure"
	CustomGitLabTemplatesDirName = "gitlab"
	BinaryName                   = "nuclei"
	FallbackConfigFolderName     = ".nuclei-config"
	NucleiConfigDirEnv           = "NUCLEI_CONFIG_DIR"
)


// similar to go pattern of enabling debug related features
// we add custom/extra switches for debugging purposes
const (
	// DebugArgHostErrorStats is used to print host error stats
	// when it is closed
	DebugArgHostErrorStats = "host-error-stats"
	// DebugExportReqURLPattern is used to export request URL pattern
	DebugExportURLPattern = "req-url-pattern"
)