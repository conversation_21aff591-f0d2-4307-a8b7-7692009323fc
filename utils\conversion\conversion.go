//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-11 17:38:57
//FilePath: /yaml_scan/utils/conversion/conversion.go
//Description: 主要实现字节和字符串相互转换

package conversion

import "unsafe"

// Bytes: 将字符串转换为字节切片。
// 该函数使用 unsafe 包直接访问字符串的底层数据，避免了内存复制。
//
//	@param s string: 要转换的字符串
//	@return []byte []byte: 返回一个字节切片，表示字符串的底层字节数据
func Bytes(s string) []byte {
	return unsafe.Slice(unsafe.StringData(s), len(s))
}

// String: 将字节切片转换为字符串。
// 如果字节切片为空，返回空字符串
//
//	@param b []byte:  要转换的字节切片
//	@return string string:返回一个字符串，表示字节切片的内容
func String(b []byte) string {
	if len(b) == 0 {
		return ""
	}
	return unsafe.String(unsafe.SliceData(b), len(b))
}
