// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-11 16:24:40
// FilePath: /yaml_scan/pkg/retryablehttp/default_client_test.go
// Description: 
package retryablehttp



import (
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestDefaultHTTPClient 测试默认HTTP客户端是否已初始化
func TestDefaultHTTPClient(t *testing.T) {
	r := require.New(t)

	// 检查默认客户端是否已初始化
	r.NotNil(DefaultHTTPClient, "默认HTTP客户端不应为nil")

	// 检查默认客户端是否使用了DefaultOptionsSingle选项
	r.Equal(DefaultOptionsSingle.RetryMax, DefaultHTTPClient.options.RetryMax, "默认客户端应使用DefaultOptionsSingle选项")
	r.Equal(DefaultOptionsSingle.KillIdleConn, DefaultHTTPClient.options.KillIdleConn, "默认客户端应使用DefaultOptionsSingle选项")
}

// TestGetFunction 测试默认的Get函数
func TestGetFunction(t *testing.T) {
	r := require.New(t)

	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 验证请求方法
		if r.Method != "GET" {
			w.WriteHeader(http.StatusMethodNotAllowed)
			return
		}

		w.WriteHeader(http.StatusOK)
		w.Write([]byte("GET响应成功"))
	}))
	defer server.Close()

	// 使用默认Get函数发送请求
	resp, err := Get(server.URL)
	r.NoError(err, "Get请求不应有错误")
	r.NotNil(resp, "Get响应不应为nil")
	r.Equal(http.StatusOK, resp.StatusCode, "状态码应为200")

	// 关闭响应体
	resp.Body.Close()
}

// TestHeadFunction 测试默认的Head函数
func TestHeadFunction(t *testing.T) {
	r := require.New(t)

	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 验证请求方法
		if r.Method != "HEAD" {
			w.WriteHeader(http.StatusMethodNotAllowed)
			return
		}

		w.Header().Set("Content-Type", "text/plain")
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	// 使用默认Head函数发送请求
	resp, err := Head(server.URL)
	r.NoError(err, "Head请求不应有错误")
	r.NotNil(resp, "Head响应不应为nil")
	r.Equal(http.StatusOK, resp.StatusCode, "状态码应为200")
	r.Equal("text/plain", resp.Header.Get("Content-Type"), "内容类型应正确")

	// 关闭响应体
	resp.Body.Close()
}

// TestPostFunction 测试默认的Post函数
func TestPostFunction(t *testing.T) {
	r := require.New(t)

	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 验证请求方法
		if r.Method != "POST" {
			w.WriteHeader(http.StatusMethodNotAllowed)
			return
		}

		// 验证内容类型
		contentType := r.Header.Get("Content-Type")
		if contentType != "text/plain" {
			w.WriteHeader(http.StatusBadRequest)
			return
		}

		// 读取请求体
		buf := make([]byte, 1024)
		n, _ := r.Body.Read(buf)

		// 验证请求体
		if string(buf[:n]) != "测试数据" {
			w.WriteHeader(http.StatusBadRequest)
			return
		}

		w.WriteHeader(http.StatusOK)
		w.Write([]byte("POST响应成功"))
	}))
	defer server.Close()

	// 使用默认Post函数发送请求
	resp, err := Post(server.URL, "text/plain", "测试数据")
	r.NoError(err, "Post请求不应有错误")
	r.NotNil(resp, "Post响应不应为nil")
	r.Equal(http.StatusOK, resp.StatusCode, "状态码应为200")

	// 关闭响应体
	resp.Body.Close()
}

// TestPostFormFunction 测试默认的PostForm函数
func TestPostFormFunction(t *testing.T) {
	r := require.New(t)

	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 验证请求方法
		if r.Method != "POST" {
			w.WriteHeader(http.StatusMethodNotAllowed)
			return
		}

		// 验证内容类型
		contentType := r.Header.Get("Content-Type")
		if contentType != "application/x-www-form-urlencoded" {
			w.WriteHeader(http.StatusBadRequest)
			return
		}

		// 解析表单数据
		err := r.ParseForm()
		if err != nil {
			w.WriteHeader(http.StatusBadRequest)
			return
		}

		// 验证表单数据
		if r.Form.Get("key") != "value" || r.Form.Get("test") != "data" {
			w.WriteHeader(http.StatusBadRequest)
			return
		}

		w.WriteHeader(http.StatusOK)
		w.Write([]byte("PostForm响应成功"))
	}))
	defer server.Close()

	// 创建表单数据
	formData := url.Values{}
	formData.Set("key", "value")
	formData.Set("test", "data")

	// 使用默认PostForm函数发送请求
	resp, err := PostForm(server.URL, formData)
	r.NoError(err, "PostForm请求不应有错误")
	r.NotNil(resp, "PostForm响应不应为nil")
	r.Equal(http.StatusOK, resp.StatusCode, "状态码应为200")

	// 关闭响应体
	resp.Body.Close()
}
