// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-19 11:48:05
// FilePath: /yaml_scan/pkg/clistats/fields_test.go
// Description: 
package clistats

import (
	"testing"

	"github.com/stretchr/testify/require"
)

// TestAddGetCounter 测试添加和获取计数器
func TestAddGetCounter(t *testing.T) {
	// 创建统计客户端
	stats, err := New()
	require.NoError(t, err, "创建统计客户端不应返回错误")

	// 测试添加计数器
	stats.AddCounter("test_counter", 10)

	// 测试获取计数器
	value, exists := stats.GetCounter("test_counter")
	require.True(t, exists, "应找到添加的计数器")
	require.Equal(t, uint64(10), value, "计数器值应为10")

	// 测试获取不存在的计数器
	value, exists = stats.GetCounter("nonexistent")
	require.False(t, exists, "不应找到不存在的计数器")
	require.Equal(t, uint64(0), value, "不存在的计数器值应为0")
}

// TestIncrementCounter 测试递增计数器
func TestIncrementCounter(t *testing.T) {
	// 创建统计客户端
	stats, err := New()
	require.NoError(t, err, "创建统计客户端不应返回错误")

	// 添加计数器
	stats.AddCounter("test_counter", 5)

	// 测试递增计数器
	stats.IncrementCounter("test_counter", 3)
	value, exists := stats.GetCounter("test_counter")
	require.True(t, exists, "应找到添加的计数器")
	require.Equal(t, uint64(8), value, "递增后计数器值应为8")

	// 再次递增
	stats.IncrementCounter("test_counter", 7)
	value, exists = stats.GetCounter("test_counter")
	require.True(t, exists, "应找到添加的计数器")
	require.Equal(t, uint64(15), value, "再次递增后计数器值应为15")

	// 测试递增不存在的计数器（应该无操作，不报错）
	stats.IncrementCounter("nonexistent", 10)
	value, exists = stats.GetCounter("nonexistent")
	require.False(t, exists, "不应找到不存在的计数器")
}

// TestAddGetStatic 测试添加和获取静态字段
func TestAddGetStatic(t *testing.T) {
	// 创建统计客户端
	stats, err := New()
	require.NoError(t, err, "创建统计客户端不应返回错误")

	// 测试添加各种类型的静态字段
	stats.AddStatic("string_field", "test_value")
	stats.AddStatic("int_field", 42)
	stats.AddStatic("bool_field", true)
	stats.AddStatic("float_field", 3.14)

	// 测试获取字符串字段
	value, exists := stats.GetStatic("string_field")
	require.True(t, exists, "应找到添加的静态字段")
	require.Equal(t, "test_value", value, "静态字段值应为'test_value'")

	// 测试获取整数字段
	value, exists = stats.GetStatic("int_field")
	require.True(t, exists, "应找到添加的静态字段")
	require.Equal(t, 42, value, "静态字段值应为42")

	// 测试获取布尔字段
	value, exists = stats.GetStatic("bool_field")
	require.True(t, exists, "应找到添加的静态字段")
	require.Equal(t, true, value, "静态字段值应为true")

	// 测试获取浮点数字段
	value, exists = stats.GetStatic("float_field")
	require.True(t, exists, "应找到添加的静态字段")
	require.Equal(t, 3.14, value, "静态字段值应为3.14")

	// 测试获取不存在的静态字段
	value, exists = stats.GetStatic("nonexistent")
	require.False(t, exists, "不应找到不存在的静态字段")
	require.Nil(t, value, "不存在的静态字段值应为nil")
}

// TestAddGetDynamic 测试添加和获取动态字段
func TestAddGetDynamic(t *testing.T) {
	// 创建统计客户端
	stats, err := New()
	require.NoError(t, err, "创建统计客户端不应返回错误")

	// 添加计数器用于动态字段计算
	stats.AddCounter("requests", 100)

	// 创建动态回调函数
	callback := func(client StatisticsClient) interface{} {
		reqs, _ := client.GetCounter("requests")
		return reqs * 2 // 返回请求数的两倍
	}

	// 添加动态字段
	stats.AddDynamic("double_requests", callback)

	// 获取动态字段回调
	retrievedCallback, exists := stats.GetDynamic("double_requests")
	require.True(t, exists, "应找到添加的动态字段")
	require.NotNil(t, retrievedCallback, "回调函数不应为nil")

	// 执行回调并验证结果
	result := retrievedCallback(stats)
	require.Equal(t, uint64(200), result, "动态字段结果应为请求数的两倍")

	// 测试获取不存在的动态字段
	retrievedCallback, exists = stats.GetDynamic("nonexistent")
	require.False(t, exists, "不应找到不存在的动态字段")
	require.Nil(t, retrievedCallback, "不存在的动态字段回调应为nil")
}


