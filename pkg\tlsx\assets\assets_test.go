// 
// Author: chenjb
// Version: V1.0
// Date: 2025-07-01 16:57:09
// FilePath: /yaml_scan/pkg/tlsx/assets/assets_test.go
// Description: 

package assets

import (
	"crypto/x509"
	"encoding/json"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestCipherDataBin 测试嵌入的密码套件数据
// 验证密码套件安全级别数据的完整性
func TestCipherDataBin(t *testing.T) {
	t.Run("密码套件数据非空", func(t *testing.T) {
		require.NotEmpty(t, CipherDataBin, "嵌入的密码套件数据不应该为空")
		require.Greater(t, len(CipherDataBin), 100, "密码套件数据应该有合理的长度")
	})

	t.Run("密码套件数据JSON格式验证", func(t *testing.T) {
		var data map[string]string
		err := json.Unmarshal([]byte(CipherDataBin), &data)
		require.NoError(t, err, "密码套件数据应该是有效的JSON格式")
		require.NotEmpty(t, data, "解析后的数据不应该为空")

		t.Logf("密码套件数据包含 %d 个条目", len(data))
	})

	t.Run("密码套件数据内容验证", func(t *testing.T) {
		var data map[string]string
		err := json.Unmarshal([]byte(CipherDataBin), &data)
		require.NoError(t, err, "JSON解析应该成功")

		// 验证安全级别值的有效性
		validLevels := map[string]bool{
			"Recommended": true,
			"Secure":      true,
			"Weak":        true,
			"Insecure":    true,
		}

		for cipher, level := range data {
			require.NotEmpty(t, cipher, "密码套件名称不应该为空")
			require.True(t, validLevels[level], "安全级别应该是有效值: %s", level)
			require.True(t, strings.HasPrefix(cipher, "TLS_"), "密码套件名称应该以TLS_开头: %s", cipher)
		}
	})
}

// TestCipherSecLevel 测试CipherSecLevel全局变量
// 验证密码套件安全级别映射表的初始化
func TestCipherSecLevel(t *testing.T) {
	t.Run("CipherSecLevel初始化验证", func(t *testing.T) {
		require.NotNil(t, CipherSecLevel, "CipherSecLevel不应该为nil")
		
		// 如果数据已经加载，验证其内容
		if len(CipherSecLevel) > 0 {
			require.Greater(t, len(CipherSecLevel), 10, "应该包含足够数量的密码套件")
			
			// 验证映射表中的数据格式
			for cipher, level := range CipherSecLevel {
				require.NotEmpty(t, cipher, "密码套件名称不应该为空")
				require.NotEmpty(t, level, "安全级别不应该为空")
				require.True(t, strings.HasPrefix(cipher, "TLS_"), "密码套件名称应该以TLS_开头")
			}
			
			t.Logf("CipherSecLevel包含 %d 个密码套件", len(CipherSecLevel))
		} else {
			t.Log("CipherSecLevel为空，可能需要手动初始化")
		}
	})
}

// TestGetSecureCipherSuites 测试安全密码套件获取功能
// 验证GetSecureCipherSuites函数的正确性
func TestGetSecureCipherSuites(t *testing.T) {
	t.Run("获取安全密码套件", func(t *testing.T) {
		secureCiphers := GetSecureCipherSuites()
		require.NotNil(t, secureCiphers, "安全密码套件列表不应该为nil")
		
		// 如果有数据，验证其内容
		if len(secureCiphers) > 0 {
			require.Greater(t, len(secureCiphers), 0, "应该有安全的密码套件")
			
			// 验证返回的密码套件格式
			for _, cipher := range secureCiphers {
				require.NotEmpty(t, cipher, "密码套件名称不应该为空")
				require.True(t, strings.HasPrefix(cipher, "TLS_"), "密码套件名称应该以TLS_开头")
			}
			
			t.Logf("找到 %d 个安全密码套件", len(secureCiphers))
		} else {
			t.Log("未找到安全密码套件，可能数据未初始化")
		}
	})

	t.Run("安全密码套件唯一性", func(t *testing.T) {
		secureCiphers := GetSecureCipherSuites()
		
		if len(secureCiphers) > 0 {
			cipherSet := make(map[string]bool)
			for _, cipher := range secureCiphers {
				require.False(t, cipherSet[cipher], "密码套件不应该重复: %s", cipher)
				cipherSet[cipher] = true
			}
		}
	})
}

// TestGetInSecureCipherSuites 测试不安全密码套件获取功能
// 验证GetInSecureCipherSuites函数的正确性
func TestGetInSecureCipherSuites(t *testing.T) {
	t.Run("获取不安全密码套件", func(t *testing.T) {
		insecureCiphers := GetInSecureCipherSuites()
		require.NotNil(t, insecureCiphers, "不安全密码套件列表不应该为nil")
		
		// 如果有数据，验证其内容
		if len(insecureCiphers) > 0 {
			// 验证返回的密码套件格式
			for _, cipher := range insecureCiphers {
				require.NotEmpty(t, cipher, "密码套件名称不应该为空")
				require.True(t, strings.HasPrefix(cipher, "TLS_"), "密码套件名称应该以TLS_开头")
			}
			
			t.Logf("找到 %d 个不安全密码套件", len(insecureCiphers))
		} else {
			t.Log("未找到不安全密码套件，可能数据未初始化")
		}
	})
}

// TestGetWeakCipherSuites 测试弱密码套件获取功能
// 验证GetWeakCipherSuites函数的正确性
func TestGetWeakCipherSuites(t *testing.T) {
	t.Run("获取弱密码套件", func(t *testing.T) {
		weakCiphers := GetWeakCipherSuites()
		require.NotNil(t, weakCiphers, "弱密码套件列表不应该为nil")
		
		// 如果有数据，验证其内容
		if len(weakCiphers) > 0 {
			// 验证返回的密码套件格式
			for _, cipher := range weakCiphers {
				require.NotEmpty(t, cipher, "密码套件名称不应该为空")
				require.True(t, strings.HasPrefix(cipher, "TLS_"), "密码套件名称应该以TLS_开头")
			}
			
			t.Logf("找到 %d 个弱密码套件", len(weakCiphers))
		} else {
			t.Log("未找到弱密码套件，可能数据未初始化")
		}
	})
}

// TestRootCertDataBin 测试嵌入的根证书数据
// 验证根证书数据的完整性
func TestRootCertDataBin(t *testing.T) {
	t.Run("根证书数据非空", func(t *testing.T) {
		require.NotEmpty(t, rootCertDataBin, "嵌入的根证书数据不应该为空")
		require.Greater(t, len(rootCertDataBin), 1000, "根证书数据应该有合理的长度")
	})

	t.Run("根证书数据PEM格式验证", func(t *testing.T) {
		// 验证数据包含PEM格式的证书
		require.Contains(t, rootCertDataBin, "-----BEGIN CERTIFICATE-----", "应该包含PEM证书开始标记")
		require.Contains(t, rootCertDataBin, "-----END CERTIFICATE-----", "应该包含PEM证书结束标记")
		
		// 统计证书数量
		beginCount := strings.Count(rootCertDataBin, "-----BEGIN CERTIFICATE-----")
		endCount := strings.Count(rootCertDataBin, "-----END CERTIFICATE-----")
		require.Equal(t, beginCount, endCount, "开始和结束标记数量应该相等")
		require.Greater(t, beginCount, 10, "应该包含多个根证书")
		
		t.Logf("根证书数据包含 %d 个证书", beginCount)
	})
}

// TestRootCerts 测试RootCerts全局变量
// 验证根证书列表的初始化和内容
func TestRootCerts(t *testing.T) {
	t.Run("RootCerts初始化验证", func(t *testing.T) {
		require.NotNil(t, RootCerts, "RootCerts不应该为nil")
		require.Greater(t, len(RootCerts), 10, "应该包含多个根证书")
		
		// 验证每个证书的有效性
		for i, cert := range RootCerts {
			require.NotNil(t, cert, "证书[%d]不应该为nil", i)
			require.IsType(t, &x509.Certificate{}, cert, "应该是x509.Certificate类型")
			require.NotEmpty(t, cert.Subject.String(), "证书主题不应该为空")
			
			// 只记录前几个证书的信息
			if i < 3 {
				t.Logf("根证书[%d]: %s", i, cert.Subject.String())
			}
		}
		
		t.Logf("RootCerts包含 %d 个根证书", len(RootCerts))
	})

	t.Run("根证书唯一性验证", func(t *testing.T) {
		if len(RootCerts) == 0 {
			t.Skip("RootCerts为空，跳过唯一性测试")
		}

		// 使用证书指纹检查唯一性
		fingerprints := make(map[string]bool)
		for i, cert := range RootCerts {
			fingerprint := string(cert.Signature)
			require.False(t, fingerprints[fingerprint], "证书[%d]不应该重复", i)
			fingerprints[fingerprint] = true
		}
	})
}

// TestParseCertificates 测试ParseCertificates函数
// 验证证书解析功能
func TestParseCertificates(t *testing.T) {
	t.Run("解析有效PEM证书", func(t *testing.T) {
		// 创建测试PEM数据
		testPEM := `-----BEGIN CERTIFICATE-----
MIIFmzCCBSGgAwIBAgIQCtiTuvposLf7ekBPBuyvmjAKBggqhkjOPQQDAzBZMQsw
CQYDVQQGEwJVUzEVMBMGA1UEChMMRGlnaUNlcnQgSW5jMTMwMQYDVQQDEypEaWdp
Q2VydCBHbG9iYWwgRzMgVExTIEVDQyBTSEEzODQgMjAyMCBDQTEwHhcNMjUwMTE1
MDAwMDAwWhcNMjYwMTE1MjM1OTU5WjCBjjELMAkGA1UEBhMCVVMxEzARBgNVBAgT
CkNhbGlmb3JuaWExFDASBgNVBAcTC0xvcyBBbmdlbGVzMTwwOgYDVQQKEzNJbnRl
cm5ldCBDb3Jwb3JhdGlvbiBmb3IgQXNzaWduZWQgTmFtZXMgYW5kIE51bWJlcnMx
FjAUBgNVBAMMDSouZXhhbXBsZS5jb20wWTATBgcqhkjOPQIBBggqhkjOPQMBBwNC
AASaSJeELWFsCMlqFKDIOIDmAMCH+plXDhsA4tiHklfnCPs8XrDThCg3wSQRjtMg
cXS9k49OCQPOAjuw5GZzz6/uo4IDkzCCA48wHwYDVR0jBBgwFoAUiiPrnmvX+Tdd
+W0hOXaaoWfeEKgwHQYDVR0OBBYEFPDBajIN7NrH6o/NDW0ZElnRvnLtMCUGA1Ud
EQQeMByCDSouZXhhbXBsZS5jb22CC2V4YW1wbGUuY29tMD4GA1UdIAQ3MDUwMwYG
Z4EMAQICMCkwJwYIKwYBBQUHAgEWG2h0dHA6Ly93d3cuZGlnaWNlcnQuY29tL0NQ
UzAOBgNVHQ8BAf8EBAMCA4gwHQYDVR0lBBYwFAYIKwYBBQUHAwEGCCsGAQUFBwMC
MIGfBgNVHR8EgZcwgZQwSKBGoESGQmh0dHA6Ly9jcmwzLmRpZ2ljZXJ0LmNvbS9E
aWdpQ2VydEdsb2JhbEczVExTRUNDU0hBMzg0MjAyMENBMS0yLmNybDBIoEagRIZC
aHR0cDovL2NybDQuZGlnaWNlcnQuY29tL0RpZ2lDZXJ0R2xvYmFsRzNUTFNFQ0NT
SEEzODQyMDIwQ0ExLTIuY3JsMIGHBggrBgEFBQcBAQR7MHkwJAYIKwYBBQUHMAGG
GGh0dHA6Ly9vY3NwLmRpZ2ljZXJ0LmNvbTBRBggrBgEFBQcwAoZFaHR0cDovL2Nh
Y2VydHMuZGlnaWNlcnQuY29tL0RpZ2lDZXJ0R2xvYmFsRzNUTFNFQ0NTSEEzODQy
MDIwQ0ExLTIuY3J0MAwGA1UdEwEB/wQCMAAwggF7BgorBgEEAdZ5AgQCBIIBawSC
AWcBZQB0AA5XlLzzrqk+MxssmQez95Dfm8I9cTIl3SGpJaxhxU4hAAABlGd6v8cA
AAQDAEUwQwIfJBcPWkx80ik7uLYW6OGvNYvJ4NmOR2RXc9uviFPH6QIgUtuuUenH
IT5UNWJffBBRq31tUGi7ZDTSrrM0f4z1Va4AdQBkEcRspBLsp4kcogIuALyrTygH
1B41J6vq/tUDyX3N8AAAAZRnesAFAAAEAwBGMEQCIHCu6NgHhV1Qvif/G7BHq7ci
MGH8jdch/xy4LzrYlesXAiByMFMvDhGg4sYm1MsrDGVedcwpE4eN0RuZcFGmWxwJ
cgB2AEmcm2neHXzs/DbezYdkprhbrwqHgBnRVVL76esp3fjDAAABlGd6wBkAAAQD
AEcwRQIgaFh67yEQ2lwgm3X16n2iWjEQFII2b2fpONtBVibZVWwCIQD5psqjXDYs
IEb1hyh0S8bBN3O4u2sA9zisKIlYjZg8wjAKBggqhkjOPQQDAwNoADBlAjEA+aaC
RlPbb+VY+u4avPyaG7fvUDJqN8KwlrXD4XptT7QL+D03+BA/FUEo3dD1iz37AjBk
Y3jhsuLAW7pWsDbtX/Qwxp6kNsK4jh1/RjvV/260sxQwM/GM7t0+T0uP2L+Y12U=
-----END CERTIFICATE-----`

		certs, err := ParseCertificates([]byte(testPEM))
		require.NoError(t, err, "解析有效PEM证书应该成功")
		require.Len(t, certs, 1, "应该解析出1个证书")
		require.NotNil(t, certs[0], "证书不应该为nil")
	})

	t.Run("解析空数据", func(t *testing.T) {
		certs, err := ParseCertificates([]byte{})
		require.NoError(t, err, "解析空数据应该成功")
		require.Empty(t, certs, "空数据应该返回空列表")
	})

	t.Run("解析无效PEM数据", func(t *testing.T) {
		invalidPEM := []byte("这不是有效的PEM数据")
		
		certs, err := ParseCertificates(invalidPEM)
		require.NoError(t, err, "解析无效PEM数据应该成功（返回空列表）")
		require.Empty(t, certs, "无效PEM数据应该返回空列表")
	})
}
