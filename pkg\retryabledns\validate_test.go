package retryabledns

import (
	"net"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestNewInternalRangeChecker 测试 newInternalRangeChecker 函数
func TestNewInternalRangeChecker(t *testing.T) {
	// 创建 internalRangeChecker 实例
	checker, err := newInternalRangeChecker()
	// 验证没有错误返回
	require.NoError(t, err, "创建 internalRangeChecker 不应返回错误")
	// 验证实例不为空
	require.NotNil(t, checker, "internalRangeChecker 不应为 nil")
	// 验证 IPv4 范围数量与预定义的数量一致
	require.Len(t, checker.ipv4, len(ipv4InternalRanges), "IPv4 范围数量应与预定义的相等")
	// 验证 IPv6 范围数量与预定义的数量一致
	require.Len(t, checker.ipv6, len(ipv6InternalRanges), "IPv6 范围数量应与预定义的相等")
}

// TestContainsIPv4 测试 ContainsIPv4 方法
func TestContainsIPv4(t *testing.T) {
	// 创建 internalRangeChecker 实例
	checker, err := newInternalRangeChecker()
	require.NoError(t, err, "创建 internalRangeChecker 不应返回错误")

	// 测试用例 1：环回地址（应在内部范围）
	loopbackIP := net.ParseIP("127.0.0.1")
	require.True(t, checker.ContainsIPv4(loopbackIP), "127.0.0.1 应在内部 IPv4 范围内")

	// 测试用例 2：当前网络地址（应在内部范围）
	currentNetworkIP := net.ParseIP("0.0.0.0")
	require.True(t, checker.ContainsIPv4(currentNetworkIP), "0.0.0.0 应在内部 IPv4 范围内")

	// 测试用例 3：外部地址（不应在内部范围）
	externalIP := net.ParseIP("*******")
	require.False(t, checker.ContainsIPv4(externalIP), "******* 不应在内部 IPv4 范围内")
}

// TestContainsIPv6 测试 ContainsIPv6 方法
func TestContainsIPv6(t *testing.T) {
	// 创建 internalRangeChecker 实例
	checker, err := newInternalRangeChecker()
	require.NoError(t, err, "创建 internalRangeChecker 不应返回错误")

	// 测试用例 1：环回地址（应在内部范围）
	loopbackIP := net.ParseIP("::1")
	require.True(t, checker.ContainsIPv6(loopbackIP), "::1 应在内部 IPv6 范围内")

	// 测试用例 2：外部地址（不应在内部范围）
	externalIP := net.ParseIP("2001:4860:4860::8888")
	require.False(t, checker.ContainsIPv6(externalIP), "2001:4860:4860::8888 不应在内部 IPv6 范围内")
}

// TestAppendIPv4Ranges 测试 appendIPv4Ranges 方法
func TestAppendIPv4Ranges(t *testing.T) {
	// 创建空的 internalRangeChecker 实例
	checker := &internalRangeChecker{}

	// 测试用例 1：添加有效的 IPv4 范围
	err := checker.appendIPv4Ranges([]string{"***********/16"})
	require.NoError(t, err, "添加有效的 IPv4 范围不应返回错误")
	require.Len(t, checker.ipv4, 1, "IPv4 范围数量应为 1")

	// 测试用例 2：添加无效的 CIDR
	err = checker.appendIPv4Ranges([]string{"invalid-cidr"})
	require.Error(t, err, "添加无效的 IPv4 CIDR 应返回错误")
}


// TestAppendIPv6Ranges 测试 appendIPv6Ranges 方法
func TestAppendIPv6Ranges(t *testing.T) {
	// 创建空的 internalRangeChecker 实例
	checker := &internalRangeChecker{}

	// 测试用例 1：添加有效的 IPv6 范围
	err := checker.appendIPv6Ranges([]string{"fe80::/10"})
	require.NoError(t, err, "添加有效的 IPv6 范围不应返回错误")
	require.Len(t, checker.ipv6, 1, "IPv6 范围数量应为 1")

	// 测试用例 2：添加无效的 CIDR
	err = checker.appendIPv6Ranges([]string{"invalid-cidr"})
	require.Error(t, err, "添加无效的 IPv6 CIDR 应返回错误")
}