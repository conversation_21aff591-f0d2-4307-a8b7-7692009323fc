// 
// Author: chenjb
// Version: V1.0
// Date: 2025-05-16 09:54:10
// FilePath: /yaml_scan/pkg/hybridMap/disk/buntdb.go
// Description: 基于 BuntDB 的磁盘存储引擎，BuntDB 是一个纯 Go 编写的嵌入式键值数据库
package disk

import (
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/tidwall/buntdb"
)

// BuntDB - 表示基于 BuntDB 的数据库实现
// BuntDB 是一个快速的键值存储，支持事务和内存/磁盘存储
type BuntDB struct {
	db *buntdb.DB
	sync.RWMutex
}

// OpenBuntDB 打开指定路径的 BuntDB 数据库
// @param path string: 数据库文件路径
// @return *BuntDB *BuntDB: 数据库实例指针
// @return error error: 可能得错误
func OpenBuntDB(path string) (*BuntDB, error) {
	db, err := buntdb.Open(path)
	if err != nil {
		return nil, err
	}

	bdb := new(BuntDB)
	bdb.db = db

	return bdb, nil
}

// Size - 为实现
func (bdb *BuntDB) Size() int64 {
	return 0
}


// Close 关闭数据库连接并释放相关资源
func (bdb *BuntDB) Close() {
	bdb.db.Close()
}

// GC - 运行垃圾回收，对数据库进行压缩以回收空间
func (bdb *BuntDB) GC() error {
	return bdb.db.Shrink()
}

// Incr 将指定键的值增加指定数量
// @receiver ldb 
// @param k string: 要增加值的键
// @param by int64: 要增加的数量
// @return int64 int64: 增加后的新值
// @return error error: 可能的错误
func (bdb *BuntDB) Incr(k string, by int64) (int64, error) {
	bdb.Lock()
	defer bdb.Unlock()

	var valP int64
	err := bdb.db.Update(func(tx *buntdb.Tx) error {
		val, err := tx.Get(k)
		if err != nil {
			return err
		}
		valP, _ = strconv.ParseInt(val, 10, 64)
		valP += by
		_, _, err = tx.Set(k, strconv.FormatInt(valP, 10), nil)
		return err
	})

	return valP, err
}

// Set 设置键值对并处理过期时间
// @receiver ldb 
// @param k string:  键名
// @param v []byte:  值字节数组
// @param ttl time.Duration:  过期时间，-1表示永不过期
// @return error error: 可能的错误
func (bdb *BuntDB) Set(k string, v []byte, ttl time.Duration) error {
	return bdb.db.Update(func(tx *buntdb.Tx) error {
		opts := new(buntdb.SetOptions)
		opts.Expires = ttl > 0
		opts.TTL = ttl
		_, _, err := tx.Set(k, string(v), opts)
		return err
	})
}

// MSet 批量设置多个键值对
// @receiver ldb 
// @param data map[string][]byte: 要设置的键值对映射
// @return error error: 可能的错误
func (bdb *BuntDB) MSet(data map[string][]byte) error {
	return bdb.db.Update(func(tx *buntdb.Tx) error {
		for k, v := range data {
			if _, _, err := tx.Set(k, string(v), nil); err != nil {
				return err
			}
		}
		return nil
	})
}

// Get 获取指定键的值
// @receiver ldb 
// @param k string: 要获取的键名
// @return []byte []byte: 键对应的值（如果存在且未过期）
// @return error error: 可能的错误，如键不存在或已过期
func (bdb *BuntDB) Get(k string) ([]byte, error) {
	var data []byte
	err := bdb.db.View(func(tx *buntdb.Tx) error {
		val, err := tx.Get(k)
		if err != nil {
			return err
		}
		data = []byte(val)
		return nil
	})
	return data, err
}

// MGet 批量获取多个键的值
// @receiver ldb 
// @param keys []string:  要获取的键名列表
// @return [] []: 对应的值列表，返回的数组内项的顺序与传入的keys一致 
// 如果某个键不存在或已过期，对应位置为空字节数组
func (bdb *BuntDB) MGet(keys []string) [][]byte {
	var data [][]byte
	_ = bdb.db.View(func(tx *buntdb.Tx) error {
		for _, k := range keys {
			val, err := tx.Get(k)
			if err != nil {
				data = append(data, []byte{})
			} else {
				data = append(data, []byte(val))
			}
		}
		return nil
	})
	return data
}

// TTL 返回指定键值对的剩余生存时间（秒）
// @receiver ldb 
// @param key string:  要查询的键名
// @return int64 int64: 剩余生存时间（秒），-1表示永不过期，-2表示键不存在或已过期
func (bdb *BuntDB) TTL(key string) int64 {
	var ttl int64
	_ = bdb.db.View(func(tx *buntdb.Tx) error {
		d, err := tx.TTL(key)
		if err != nil {
			return err
		}
		ttl = int64(d)
		return nil
	})
	return ttl
}


// MDel 批量删除多个键
// @receiver ldb 
// @param keys []string: 要删除的键名列表
// @return error error: 可能的错误
func (bdb *BuntDB) MDel(keys []string) error {
	return bdb.db.Update(func(tx *buntdb.Tx) error {
		for _, k := range keys {
			if _, err := tx.Delete(k); err != nil {
				return err
			}
		}
		return nil
	})
}

// Del 删除指定的键
// @receiver ldb 
// @param key string: 要删除的键名
// @return error error: 可能得错误
func (bdb *BuntDB) Del(key string) error {
	return bdb.db.Update(func(tx *buntdb.Tx) error {
		_, err := tx.Delete(key)
		if err != nil {
			return err
		}
		return nil
	})
}

// Scan 使用指定的处理函数遍历整个存储
// @receiver ldb 
// @param scannerOpt ScannerOptions: 扫描选项，包含偏移量、前缀过滤、处理函数等
// @return error error: 可能的错误
func (bdb *BuntDB) Scan(opt ScannerOptions) error {
	valid := func(k, v string) bool {
		// 如果不包含偏移量且指定了偏移键，跳过该项
		if !opt.IncludeOffset && len(opt.Offset) > 0 && k == opt.Offset {
			return true
		}

		// 如果指定了前缀但当前键不符合前缀，退出扫描
		if len(opt.Prefix) > 0 && !strings.HasPrefix(k, opt.Prefix) {
			return false
		}

		// 调用处理函数，如果返回错误则停止扫描
		if opt.Handler([]byte(k), []byte(v)) != nil {
			return false
		}
		return true
	}
	return bdb.db.View(func(tx *buntdb.Tx) error {
		// 如果指定了偏移量，从该偏移量开始向后扫描
		if len(opt.Offset) > 0 {
			return tx.AscendGreaterOrEqual("", opt.Offset, valid)
		}

		// 如果只指定了前缀，使用前缀扫描
		if len(opt.Prefix) > 0 && len(opt.Offset) == 0 {
			return tx.AscendKeys(opt.Prefix+"*", valid)
		}

		return tx.Ascend("", valid)
	})
}

