//
// Author: chenjb
// Version: V1.0
// Date: 2025-07-22 17:27:37
// FilePath: /yaml_scan/pkg/rawhttp/client/header.go
// Description: HTTP头部集合类型定义，实现了sort.Interface接口以支持头部排序

// Package client HTTP头部处理模块
package client

// Headers HTTP头部集合类型
// 实现了sort.Interface接口，支持对HTTP头部进行排序操作
// 排序规则：首先按头部名称排序，名称相同时按头部值排序
type Headers []Header

// Len 返回头部集合的长度
// 返回:
//   int: 头部数量
// 功能: 实现sort.Interface接口的Len方法
func (h Headers) Len() int { return len(h) }

// Swap 交换指定位置的两个头部
// 参数:
//   i: 第一个头部的索引位置
//   j: 第二个头部的索引位置
// 功能: 实现sort.Interface接口的Swap方法，用于排序时交换元素
func (h Headers) Swap(i, j int) { h[i], h[j] = h[j], h[i] }

// Less 比较两个头部的大小关系
// 参数:
//   i: 第一个头部的索引位置
//   j: 第二个头部的索引位置
// 返回:
//   bool: 如果第i个头部小于第j个头部则返回true，否则返回false
// 功能: 实现sort.Interface接口的Less方法，定义排序规则
//       首先按头部名称（Key）进行字典序比较
//       如果名称相同，则按头部值（Value）进行字典序比较
func (h Headers) Less(i, j int) bool {
	switch {
	case h[i].Key < h[j].Key:
		return true  // 第i个头部的名称小于第j个头部的名称
	case h[i].Key > h[j].Key:
		return false // 第i个头部的名称大于第j个头部的名称
	default:
		return h[i].Value < h[j].Value // 名称相同时比较头部值
	}
}

