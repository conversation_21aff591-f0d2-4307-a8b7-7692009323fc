// 
// Author: chenjb
// Version: V1.0
// Date: 2025-05-19 16:54:40
// FilePath: /yaml_scan/pkg/hybridMap/cache/janitor_test.go
// Description: 
package cache

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestJanitorRun 测试清理器的运行功能
func TestJanitorRun(t *testing.T) {
	// 创建缓存和清理器
	cache := newCache(NoExpiration, map[string]Item{})
	j := &janitor{
		Interval: 100 * time.Millisecond,
		stop:     make(chan struct{}),
	}
	cache.janitor = j
	
	// 添加过期项目
	cache.set("key1", []byte("value1"), 50*time.Millisecond)
	cache.set("key2", []byte("value2"), 250*time.Millisecond)
	
	// 启动清理器
	go j.Run(cache)
	
	// 确保项目存在
	_, found := cache.Get("key1")
	require.True(t, found, "key1应该存在")
	_, found = cache.Get("key2")
	require.True(t, found, "key2应该存在")
	
	// 等待第一次清理（应删除key1）
	time.Sleep(150 * time.Millisecond)
	
	// 验证key1已被自动清理
	_, found = cache.Get("key1")
	require.False(t, found, "key1应该被清理器清理")
	
	// 验证key2仍然存在
	_, found = cache.Get("key2")
	require.True(t, found, "key2不应被清理")
	
	// 停止清理器
	stopJanitor(cache)
	
	// 添加新的过期项目
	cache.set("key3", []byte("value3"), 50*time.Millisecond)
	
	// 等待足够时间使key3过期
	time.Sleep(100 * time.Millisecond)
	
	// 由于清理器已停止，过期的key3应该仍然可以获取（但Get方法会检查过期）
	_, found = cache.Get("key3")
	require.False(t, found, "过期项应该在Get时检测")
	
}

// TestRunJanitor 测试创建并启动清理器
func TestRunJanitor(t *testing.T) {
	// 创建缓存
	cache := newCache(NoExpiration, map[string]Item{})
	
	// 运行清理器
	runJanitor(cache, 100*time.Millisecond)
	
	// 验证清理器已创建
	require.NotNil(t, cache.janitor, "清理器应该被创建")
	require.Equal(t, 100*time.Millisecond, cache.janitor.Interval, "清理间隔应该正确设置")
	require.NotNil(t, cache.janitor.stop, "停止通道应该被创建")
	
	// 添加过期项目
	cache.set("key1", []byte("value1"), 50*time.Millisecond)
	
	// 等待清理
	time.Sleep(200 * time.Millisecond)
	
	// 验证过期项已被清理
	_, found := cache.Get("key1")
	require.False(t, found, "过期项应该被清理器清理")
	
	// 停止清理器
	stopJanitor(cache)
}

// TestStopJanitor 测试停止清理器
func TestStopJanitor(t *testing.T) {
	// 创建缓存和清理器
	cache := newCache(NoExpiration, map[string]Item{})
	j := &janitor{
		Interval: 100 * time.Millisecond,
		stop:     make(chan struct{}),
	}
	cache.janitor = j
	
	// 启动清理器
	done := make(chan bool)
	go func() {
		j.Run(cache)
		done <- true
	}()
	
	// 停止清理器
	stopJanitor(cache)
	
	// 等待清理器停止的信号
	select {
	case <-done:
		// 清理器已停止，测试通过
	case <-time.After(500 * time.Millisecond):
		t.Fatal("清理器未能正确停止")
	}
} 

