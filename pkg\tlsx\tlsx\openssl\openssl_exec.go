// Author: chenjb
// Version: V1.0
// Date: 2025-06-23 20:02:20
// FilePath: /yaml_scan/pkg/tlsx/tlsx/openssl/openssl_exec.go
// Description:OpenSSL命令执行和输出解析
package openssl

import (
	"bufio"
	"bytes"
	"context"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"io"
	"os"
	"os/exec"
	"strings"
	errorutils "yaml_scan/utils/errors"
)

// commadFormat 定义命令格式化字符串模板
const commadFormat string = "Command: %v"

// CMDOUT 表示OpenSSL命令的执行结果
type CMDOUT struct {
	Command string // 执行的完整命令字符串
	Stdout  string // 标准输出内容，包含证书和响应数据
	Stderr  string // 标准错误输出，包含OpenSSL检测到的错误和警告
}

// execOpenSSL 执行OpenSSL命令并获取结果
// @param ctx context.Context:上下文，用于控制命令执行超时和取消
// @param args []string:  OpenSSL命令参数列表
// @return *CMDOUT *CMDOUT: 命令执行结果
// @return error error:纯粹与I/O和命令执行相关的错误
func execOpenSSL(ctx context.Context, args []string) (*CMDOUT, error) {
	// 创建用于捕获输出的缓冲区
	var outbuff, inbuff, errbuff bytes.Buffer

	// 创建带上下文的OpenSSL命令
	cmd := exec.CommandContext(ctx, BinaryPath)

	// 仅为OpenSSL（非LibreSSL）设置自定义配置文件
	if !IsLibreSSL {
		newenv := "OPENSSL_CONF=" + OPENSSL_CONF
		cmd.Env = append(os.Environ(), newenv)
	}

	// 设置命令参数和I/O流
	cmd.Args = args
	cmd.Stderr = &errbuff
	cmd.Stdout = &outbuff
	cmd.Stdin = &inbuff

	// 向标准输入写入"Q"，用于在OpenSSL交互模式下退出
	inbuff.WriteString("Q")

	cmdstring := BinaryPath + " " + strings.Join(args, " ")
	// 启动OpenSSL命令进程
	if err := cmd.Start(); err != nil {
		return &CMDOUT{Command: cmdstring, Stderr: errbuff.String(), Stdout: outbuff.String()}, fmt.Errorf("failed to start openssl: %v", err)
	}
	// 等待命令执行完成
	if err := cmd.Wait(); err != nil && errbuff.Len() == 0 {
		return &CMDOUT{Command: cmdstring, Stderr: errbuff.String(), Stdout: outbuff.String()}, err
	}
	// 命令执行成功，返回完整的输出结果
	return &CMDOUT{Command: cmdstring, Stderr: errbuff.String(), Stdout: outbuff.String()}, nil
}

// getCiphers 获取OpenSSL支持的所有密码套件列表
// 通过执行"openssl ciphers"命令获取系统中OpenSSL支持的密码套件
// @return []string []string: OpenSSL支持的密码套件名称列表
// @return error error: 获取过程中的错误，成功时为nil
func getCiphers() ([]string, error) {
	ciphers := []string{}

	// 执行"openssl ciphers"命令
	res, err := execOpenSSL(context.TODO(), []string{"ciphers"})
	if err != nil {
		return ciphers, err
	}
	// 清理输出并按冒号分割密码套件
	out := strings.TrimSpace(res.Stdout)
	ciphers = append(ciphers, strings.Split(out, ":")...)
	return ciphers, nil
}

// getResponse 读取并解析OpenSSL s_client命令的响应
// 执行OpenSSL s_client连接并解析返回的TLS握手信息
// @param ctx context.Context: 上下文，用于控制命令执行超时
// @param opts *Options: OpenSSL命令选项，包含连接参数和解析配置
// @return *Response *Response: 解析后的TLS响应，包含证书链和会话信息
// @return errorutils.Error errorutils.Error: 可能的错误
func getResponse(ctx context.Context, opts *Options) (*Response, errorutils.Error) {
	// 根据选项生成OpenSSL命令参数
	args, errx := opts.Args()
	if errx != nil {
		return nil, errorutils.NewWithErr(errx).WithTag(PkgTag).Msgf("failed to create cmd from args got %v", *opts)
	}

	// 执行OpenSSL s_client命令
	result, err := execOpenSSL(ctx, args)
	if err != nil {
		return nil, errorutils.NewWithErr(err).WithTag(PkgTag, BinaryPath).Msgf("failed to execute openssl got %v", result.Stderr).Msgf(commadFormat, result.Command)
	}
	response := &Response{}

	// 检查连接是否成功建立
	if !strings.Contains(result.Stdout, "CONNECTED") {
		// 如果输出中没有"CONNECTED"字符串，说明OpenSSL连接完全失败
		// 这通常表示网络连接问题或服务器拒绝连接
		return nil, errorutils.NewWithTag(PkgTag, "failed to parse 'CONNECTED' not found got %v", result.Stderr).Msgf(commadFormat, result.Command)
	}
	var errParseCertificates, errParseSessionData error
	// 解析服务器证书链
	// OpenSSL s_client返回大量数据，但大部分信息可以从证书解析中获得
	if !opts.SkipCertParse {
		response.AllCerts, errParseCertificates = parseCertificates(result.Stdout)
	}
	
	// 解析TLS会话数据（版本、密码套件等）
	response.Session, errParseSessionData = readSessionData(result.Stdout)

	var allerrors errorutils.Error
	// 检查证书解析是否失败
	if errParseCertificates != nil {
		allerrors = Wrap(allerrors, errorutils.NewWithErr(errParseCertificates).WithTag(PkgTag).Msgf("failed to parse server certificate from response"))
	}

	// 检查会话数据解析是否失败
	if errParseSessionData != nil {
		allerrors = Wrap(allerrors, errorutils.NewWithErr(errParseSessionData).WithTag(PkgTag).Msgf("failed to parse session data from response"))
	}

		// 检查是否成功获取到服务器证书
	if !opts.SkipCertParse && len(response.AllCerts) == 0 {
		allerrors = Wrap(allerrors, errorutils.NewWithTag(PkgTag, "no server certificates found"))
	}

	// 如果存在任何解析错误，返回组合错误信息
	if allerrors != nil {
		return nil, allerrors.Msgf("failed to parse openssl response. original response is:\n%v", *result).Msgf(commadFormat, result.Command)
	}

	// 检查服务器是否要求客户端证书认证
	response.ClientCertRequired = isClientCertRequired(result.Stderr)
	return response, nil
}


// readSessionData 从OpenSSL响应中读取TLS会话数据
// 解析OpenSSL s_client输出中的会话信息，包括TLS版本、密码套件等
// @param data string: penSSL s_client命令的完整输出字符串
// @return *Session *Session: 析后的TLS会话信息结构
// @return error error: 解析过程中的错误，成功时为nil
func readSessionData(data string) (*Session, error) {
	respreader := bufio.NewReader(strings.NewReader(data)) // 创建字符串读取器
	inFlight := false                                      // 标记是否正在解析会话数据块
	osession := &Session{}                                 // 初始化会话结构

readline:
// 逐行读取OpenSSL输出
	line, err := respreader.ReadString('\n')
	if err != nil && err != io.EOF {
		return nil, errorutils.NewWithErr(err).WithTag(PkgTag).Wrap(ErrNoSession)
	} else if err == io.EOF {
		return osession, nil
	}
	line = strings.TrimSpace(line)
	// 检测会话数据块的开始
	if strings.HasPrefix(line, "SSL-Session") {
		inFlight = true
		goto readline
	}
	if inFlight {
		// 解析会话数据中的各个字段
		switch {
		case strings.HasPrefix(line, "Protocol"):
			osession.Protocol = parseSessionValue(line)
		case strings.HasPrefix(line, "Cipher"):
			osession.Cipher = parseSessionValue(line)
		case strings.HasPrefix(line, "Master-Key"):
			osession.MasterKey = parseSessionValue(line)
		}
		if strings.HasPrefix(line, "Timeout") {			
			return osession, nil
		}
	}
	goto readline
}

// parseCertificates 解析OpenSSL输出中的证书数据
// 从OpenSSL s_client命令的输出中提取并解析所有PEM格式的证书
// @param data string: s_client命令的完整输出字符串
// @return []*x509.Certificate []*x509.Certificate: 解析成功的X.509证书列表，按在输出中出现的顺序排列
// @return error error: 解析过程中的错误，成功时为nil
// 取"-----BEGIN CERTIFICATE-----"到"-----END CERTIFICATE-----"之间的内容
func parseCertificates(data string) ([]*x509.Certificate, error) {
	var certBuff bytes.Buffer                              // 用于累积单个证书的PEM数据
	certArr := []*x509.Certificate{}                       // 存储解析成功的证书列表
	certReader := bufio.NewReader(strings.NewReader(data)) // 创建字符串读取器
	inFlight := false                                      // 标记是否正在读取证书内容

readline:
	// 逐行读取OpenSSL输出
	line, err := certReader.ReadString('\n')
	if err != nil && err != io.EOF {
		return nil, ErrCertParse
	} else if err == io.EOF {
		// 读取完成，返回解析的证书列表
		return certArr, nil
	}
	line = strings.TrimSpace(line)
	// 检测证书开始标记
	if line == "-----BEGIN CERTIFICATE-----" {
		certBuff.WriteString(line)
		certBuff.WriteString("\n")
		inFlight = true
	} else if line == "-----END CERTIFICATE-----" && inFlight {
		// 检测证书结束标记
		certBuff.WriteString(line)
		certBuff.WriteString("\n")
		inFlight = false

		// 解析当前证书的PEM数据
		xcert, certerr := getx509Certificate(certBuff.Bytes())
		if certerr != nil {
			return nil, errorutils.NewWithErr(certerr).WithTag(PkgTag).Msgf("failed to parse x509 certificate from PEM data of openssl")
		}
		certArr = append(certArr, xcert)
		// 重置缓冲区，准备读取下一个证书
		certBuff.Reset()
	} else if inFlight {
		// 正在读取证书内容，累积到缓冲区
		certBuff.WriteString(line)
		certBuff.WriteString("\n")
	}
	goto readline // 继续读取直到缓冲区为空
}

// getx509Certificate 将PEM格式的证书数据转换为x509.Certificate结构
// 该函数负责将从OpenSSL输出中提取的PEM证书数据解析为Go标准库的证书结构
// @param certBin []byte: PEM格式的证书二进制数据
// @return *x509.Certificate *x509.Certificate: 解析后的X.509证书结构
// @return error error: 解析过程中的错误，成功时为nil
func getx509Certificate(certBin []byte) (*x509.Certificate, error) {
	if len(certBin) == 0 {
		return nil, fmt.Errorf("cert is empty: %v", ErrCertParse)
	}

	// 解码PEM格式数据
	block, _ := pem.Decode(certBin)
	if block == nil {
		return nil, fmt.Errorf("not a valid pem")
	}
	// 解析X.509证书结构
	crt, e := x509.ParseCertificate(block.Bytes)
	if e != nil {
		return nil, fmt.Errorf("parsex509: %v", e)
	}
	return crt, nil
}
