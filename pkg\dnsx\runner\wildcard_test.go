package runner

import (
	"sync"
	"testing"
	"yaml_scan/pkg/dnsx/libs/dnsx"

	"github.com/stretchr/testify/require"
)



// TestIsWildcard 测试IsWildcard函数的功能
func TestIsWildcard(t *testing.T) {
	tests := []struct {
		name           string              // 测试名称
		host           string              // 测试的主机名
		wildcardDomain string              // 通配符域名
		recordsMap     map[string][]string // 模拟DNS记录
		expectWildcard bool                // 期望结果是否为通配符
	}{
		{
			name:           "非通配符域名",
			host:           "example.com",
			wildcardDomain: "example.com",
			recordsMap: map[string][]string{
				"example.com":            {"***********"},
				"random-xyz.example.com": {"***********"}, // 不同的IP
			},
			expectWildcard: false,
		},
		// {
		// 	name:           "通配符域名",
		// 	host:           "pan.baidu.com",
		// 	wildcardDomain: "baidu.com",
		// 	recordsMap: map[string][]string{
		// 		"pan.baidu.com":       {"***********"},
		// 		"tieba.baidu.com": {"***********"}, // 相同的IP，表示通配符
		// 	},
		// 	expectWildcard: true,
		// },
		{
			name:           "无记录域名",
			host:           "norecord.com",
			wildcardDomain: "norecord.com",
			recordsMap:     map[string][]string{},
			expectWildcard: false,
		},
		{
			name:           "多级子域名-通配符",
			host:           "sub1.sub2.example.com",
			wildcardDomain: "example.com",
			recordsMap: map[string][]string{
				"sub1.sub2.example.com":       {"***********"},
				"random-xyz.sub2.example.com": {"***********"}, // 相同的IP，表示通配符
				"random-xyz.example.com":      {"***********"},
			},
			expectWildcard: false,
		},
	}
	options := dnsx.DefaultOptions
	dnsx, _ := dnsx.New(options)

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			// 创建Runner实例
			runner := &Runner{
				dnsx: dnsx,
				options: &Options{
					WildcardDomain: test.wildcardDomain,
				},
				wildcardscache:      make(map[string][]string),
				wildcardscachemutex: sync.Mutex{},
			}

			// 测试IsWildcard函数
			isWildcard := runner.IsWildcard(test.host)
			require.Equal(t, test.expectWildcard, isWildcard, "测试'%s': IsWildcard应返回%v，但得到%v",
				test.name, test.expectWildcard, isWildcard)
		})
	}
}