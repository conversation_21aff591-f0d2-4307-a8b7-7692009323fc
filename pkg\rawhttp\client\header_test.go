// Author: chenjb
// Version: V1.0
// Date: 2025-07-25
// FilePath: /yaml_scan/pkg/rawhttp/client/header_test.go
// Description: rawhttp头部集合模块单元测试

package client

import (
	"sort"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestHeaders_Len 测试Headers的Len方法
func TestHeaders_Len(t *testing.T) {
	// 测试空头部集合
	headers := Headers{}
	require.Equal(t, 0, headers.Len(), "空头部集合长度应该为0")
	
	// 测试有元素的头部集合
	headers = Headers{
		{Key: "Content-Type", Value: "application/json"},
		{Key: "User-Agent", Value: "test-agent"},
	}
	require.Equal(t, 2, headers.Len(), "头部集合长度应该为2")
	
	// 测试单个元素
	headers = Headers{
		{Key: "Host", Value: "example.com"},
	}
	require.Equal(t, 1, headers.Len(), "单个头部集合长度应该为1")
}

// TestHeaders_Swap 测试Headers的Swap方法
func TestHeaders_Swap(t *testing.T) {
	headers := Headers{
		{Key: "Content-Type", Value: "application/json"},
		{Key: "User-Agent", Value: "test-agent"},
		{Key: "Host", Value: "example.com"},
	}
	
	// 保存原始状态
	original0 := headers[0]
	original1 := headers[1]
	
	// 交换位置0和1
	headers.Swap(0, 1)
	
	// 验证交换结果
	require.Equal(t, original1, headers[0], "位置0应该是原来位置1的元素")
	require.Equal(t, original0, headers[1], "位置1应该是原来位置0的元素")
	require.Equal(t, "Host", headers[2].Key, "位置2应该保持不变")
}

// TestHeaders_Less 测试Headers的Less方法
func TestHeaders_Less(t *testing.T) {
	headers := Headers{
		{Key: "Content-Type", Value: "application/json"},
		{Key: "User-Agent", Value: "test-agent"},
		{Key: "Accept", Value: "text/html"},
		{Key: "Accept", Value: "application/xml"},
	}
	
	// 测试按键名排序
	require.True(t, headers.Less(2, 0), "Accept应该小于Content-Type")
	require.False(t, headers.Less(0, 2), "Content-Type不应该小于Accept")
	
	// 测试相同键名时按值排序
	require.True(t, headers.Less(2, 3), "text/html应该小于application/xml")
	require.False(t, headers.Less(3, 2), "application/xml不应该小于text/html")
	
	// 测试不同键名
	require.True(t, headers.Less(0, 1), "Content-Type应该小于User-Agent")
	require.False(t, headers.Less(1, 0), "User-Agent不应该小于Content-Type")
}

// TestHeaders_Sort 测试Headers的排序功能
func TestHeaders_Sort(t *testing.T) {
	headers := Headers{
		{Key: "User-Agent", Value: "test-agent"},
		{Key: "Content-Type", Value: "application/json"},
		{Key: "Accept", Value: "text/html"},
		{Key: "Accept", Value: "application/xml"},
		{Key: "Host", Value: "example.com"},
	}
	
	// 执行排序
	sort.Sort(headers)
	
	// 验证排序结果
	expected := Headers{
		{Key: "Accept", Value: "application/xml"},
		{Key: "Accept", Value: "text/html"},
		{Key: "Content-Type", Value: "application/json"},
		{Key: "Host", Value: "example.com"},
		{Key: "User-Agent", Value: "test-agent"},
	}
	
	require.Equal(t, expected, headers, "头部应该按正确顺序排序")
}

// TestHeaders_SortInterface 测试Headers实现sort.Interface接口
func TestHeaders_SortInterface(t *testing.T) {
	headers := Headers{}
	
	// 验证Headers实现了sort.Interface接口
	_, ok := interface{}(headers).(sort.Interface)
	require.True(t, ok, "Headers应该实现sort.Interface接口")
}

// TestHeaders_SortStability 测试排序的稳定性
func TestHeaders_SortStability(t *testing.T) {
	// 创建具有相同键值对的头部
	headers := Headers{
		{Key: "Accept", Value: "text/html"},
		{Key: "Content-Type", Value: "application/json"},
		{Key: "Accept", Value: "text/html"}, // 重复的键值对
	}
	
	// 执行排序
	sort.Sort(headers)
	
	// 验证相同元素的相对位置
	require.Equal(t, "Accept", headers[0].Key, "第一个Accept头部应该在前面")
	require.Equal(t, "Accept", headers[1].Key, "第二个Accept头部应该在后面")
	require.Equal(t, "text/html", headers[0].Value, "第一个Accept值应该正确")
	require.Equal(t, "text/html", headers[1].Value, "第二个Accept值应该正确")
	require.Equal(t, "Content-Type", headers[2].Key, "Content-Type应该在最后")
}

// TestHeaders_EmptySort 测试空集合排序
func TestHeaders_EmptySort(t *testing.T) {
	headers := Headers{}
	
	// 对空集合排序不应该panic
	require.NotPanics(t, func() {
		sort.Sort(headers)
	}, "空集合排序不应该panic")
	
	require.Equal(t, 0, headers.Len(), "排序后空集合长度仍为0")
}

// TestHeaders_SingleElementSort 测试单元素排序
func TestHeaders_SingleElementSort(t *testing.T) {
	headers := Headers{
		{Key: "Content-Type", Value: "application/json"},
	}
	
	// 对单元素集合排序
	sort.Sort(headers)
	
	require.Equal(t, 1, headers.Len(), "单元素集合长度应该为1")
	require.Equal(t, "Content-Type", headers[0].Key, "元素应该保持不变")
	require.Equal(t, "application/json", headers[0].Value, "值应该保持不变")
}

// TestHeaders_CaseInsensitiveSort 测试大小写敏感的排序
func TestHeaders_CaseInsensitiveSort(t *testing.T) {
	headers := Headers{
		{Key: "content-type", Value: "application/json"},
		{Key: "Content-Type", Value: "text/html"},
		{Key: "CONTENT-TYPE", Value: "application/xml"},
	}
	
	// 执行排序
	sort.Sort(headers)
	
	// 验证大小写敏感的排序（大写字母ASCII值小于小写字母）
	require.Equal(t, "CONTENT-TYPE", headers[0].Key, "大写应该排在前面")
	require.Equal(t, "Content-Type", headers[1].Key, "首字母大写应该在中间")
	require.Equal(t, "content-type", headers[2].Key, "小写应该排在后面")
}

// TestHeaders_SpecialCharacters 测试特殊字符排序
func TestHeaders_SpecialCharacters(t *testing.T) {
	headers := Headers{
		{Key: "X-Custom-Header", Value: "value1"},
		{Key: "Accept", Value: "text/html"},
		{Key: "X-Another-Header", Value: "value2"},
		{Key: "Authorization", Value: "Bearer token"},
	}
	
	// 执行排序
	sort.Sort(headers)
	
	// 验证排序结果（按字典序）
	require.Equal(t, "Accept", headers[0].Key, "Accept应该排在第一位")
	require.Equal(t, "Authorization", headers[1].Key, "Authorization应该排在第二位")
	require.Equal(t, "X-Another-Header", headers[2].Key, "X-Another-Header应该排在第三位")
	require.Equal(t, "X-Custom-Header", headers[3].Key, "X-Custom-Header应该排在第四位")
}

// TestHeaders_ValueSorting 测试相同键名时的值排序
func TestHeaders_ValueSorting(t *testing.T) {
	headers := Headers{
		{Key: "Accept", Value: "text/plain"},
		{Key: "Accept", Value: "application/json"},
		{Key: "Accept", Value: "text/html"},
		{Key: "Accept", Value: "application/xml"},
	}
	
	// 执行排序
	sort.Sort(headers)
	
	// 验证值的排序（按字典序）
	require.Equal(t, "application/json", headers[0].Value, "application/json应该排在第一位")
	require.Equal(t, "application/xml", headers[1].Value, "application/xml应该排在第二位")
	require.Equal(t, "text/html", headers[2].Value, "text/html应该排在第三位")
	require.Equal(t, "text/plain", headers[3].Value, "text/plain应该排在第四位")
}
