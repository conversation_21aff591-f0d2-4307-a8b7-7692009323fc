//
// Author: chenjb
// Version: V1.0
// Date: 2025-07-22 19:21:37
// FilePath: /yaml_scan/pkg/rawhttp/clientpipeline/tcpdialer.go
// Description: TCP拨号器实现，提供高性能的TCP连接建立功能，支持连接池和DNS缓存

// Package clientpipeline TCP拨号器模块
package clientpipeline

// Original Source: https://github.com/valyala/fasthttp
// 原始来源：基于fasthttp项目的TCP拨号器实现

import (
	"context"     // 上下文包
	"errors"      // 错误处理包
	"net"         // 网络包
	"strconv"     // 字符串转换包
	"sync"        // 同步原语包
	"sync/atomic" // 原子操作包
	"time"        // 时间处理包
)

// Dial 使用默认拨号器建立TCP连接
// 参数:
//   addr: 目标地址（格式：host:port）
// 返回:
//   net.Conn: 建立的网络连接
//   error: 连接错误，成功时为nil
// 功能: 使用默认配置建立TCP连接
func Dial(addr string) (net.Conn, error) {
	return defaultDialer.Dial(addr)
}

// DialTimeout 使用默认拨号器建立带超时的TCP连接
// 参数:
//   addr: 目标地址
//   timeout: 连接超时时间
// 返回:
//   net.Conn: 建立的网络连接
//   error: 连接错误，成功时为nil
// 功能: 在指定超时时间内建立TCP连接
func DialTimeout(addr string, timeout time.Duration) (net.Conn, error) {
	return defaultDialer.DialTimeout(addr, timeout)
}

// DialDualStack 使用默认拨号器建立双栈TCP连接
// 参数:
//   addr: 目标地址
// 返回:
//   net.Conn: 建立的网络连接
//   error: 连接错误，成功时为nil
// 功能: 支持IPv4和IPv6的双栈连接
func DialDualStack(addr string) (net.Conn, error) {
	return defaultDialer.DialDualStack(addr)
}

// DialDualStackTimeout 使用默认拨号器建立带超时的双栈TCP连接
// 参数:
//   addr: 目标地址
//   timeout: 连接超时时间
// 返回:
//   net.Conn: 建立的网络连接
//   error: 连接错误，成功时为nil
// 功能: 在指定超时时间内建立支持IPv4和IPv6的双栈连接
func DialDualStackTimeout(addr string, timeout time.Duration) (net.Conn, error) {
	return defaultDialer.DialDualStackTimeout(addr, timeout)
}

// 默认拨号器实例，并发数设置为1000
var (
	defaultDialer = &TCPDialer{Concurrency: 1000}
)

// Resolver TCP解析器接口
// 定义了DNS解析的标准接口
type Resolver interface {
	// LookupIPAddr 查找主机名对应的IP地址列表
	// 参数:
	//   context.Context: 上下文对象
	//   string: 主机名
	// 返回:
	//   []net.IPAddr: IP地址列表
	//   error: 解析错误，成功时为nil
	LookupIPAddr(context.Context, string) (names []net.IPAddr, err error)
}

// TCPDialer TCP拨号器结构体
// 包含控制一组拨号调用的选项和状态
type TCPDialer struct {
	Concurrency   int                        // 最大并发连接数
	LocalAddr     *net.TCPAddr               // 本地绑定地址
	Resolver      Resolver                   // DNS解析器
	tcpAddrsLock  sync.Mutex                 // TCP地址映射的互斥锁
	tcpAddrsMap   map[string]*tcpAddrEntry   // TCP地址缓存映射
	concurrencyCh chan struct{}              // 并发控制通道
	once          sync.Once                  // 确保初始化只执行一次
}

// Dial 建立TCP连接
// 参数:
//   addr: 目标地址
// 返回:
//   net.Conn: 建立的网络连接
//   error: 连接错误，成功时为nil
// 功能: 使用默认超时时间建立IPv4连接
func (d *TCPDialer) Dial(addr string) (net.Conn, error) {
	return d.dial(addr, false, DefaultDialTimeout)
}

// DialTimeout 建立带超时的TCP连接
// 参数:
//   addr: 目标地址
//   timeout: 连接超时时间
// 返回:
//   net.Conn: 建立的网络连接
//   error: 连接错误，成功时为nil
// 功能: 在指定超时时间内建立IPv4连接
func (d *TCPDialer) DialTimeout(addr string, timeout time.Duration) (net.Conn, error) {
	return d.dial(addr, false, timeout)
}

// DialDualStack 建立双栈TCP连接
// 参数:
//   addr: 目标地址
// 返回:
//   net.Conn: 建立的网络连接
//   error: 连接错误，成功时为nil
// 功能: 使用默认超时时间建立支持IPv4和IPv6的连接
func (d *TCPDialer) DialDualStack(addr string) (net.Conn, error) {
	return d.dial(addr, true, DefaultDialTimeout)
}

// DialDualStackTimeout 建立带超时的双栈TCP连接
// 参数:
//   addr: 目标地址
//   timeout: 连接超时时间
// 返回:
//   net.Conn: 建立的网络连接
//   error: 连接错误，成功时为nil
// 功能: 在指定超时时间内建立支持IPv4和IPv6的连接
func (d *TCPDialer) DialDualStackTimeout(addr string, timeout time.Duration) (net.Conn, error) {
	return d.dial(addr, true, timeout)
}

// dial TCP拨号的核心实现方法
// 参数:
//   addr: 目标地址
//   dualStack: 是否使用双栈（IPv4和IPv6）
//   timeout: 连接超时时间
// 返回:
//   net.Conn: 建立的网络连接
//   error: 连接错误，成功时为nil
// 功能: 解析地址、管理连接池、尝试连接多个IP地址
func (d *TCPDialer) dial(addr string, dualStack bool, timeout time.Duration) (net.Conn, error) {
	// 确保拨号器只初始化一次
	d.once.Do(func() {
		// 如果设置了并发限制，创建并发控制通道
		if d.Concurrency > 0 {
			d.concurrencyCh = make(chan struct{}, d.Concurrency)
		}
		d.tcpAddrsMap = make(map[string]*tcpAddrEntry) // 初始化TCP地址缓存映射
		go d.tcpAddrsClean()                           // 启动地址缓存清理协程
	})

	// 获取目标地址的TCP地址列表和起始索引
	addrs, idx, err := d.getTCPAddrs(addr, dualStack)
	if err != nil {
		return nil, err // 地址解析失败
	}
	// 设置网络类型
	network := "tcp4" // 默认使用IPv4
	if dualStack {
		network = "tcp" // 双栈模式使用tcp（支持IPv4和IPv6）
	}

	var conn net.Conn
	n := uint32(len(addrs))           // 可用地址数量
	deadline := time.Now().Add(timeout) // 计算连接截止时间
	// 轮询尝试连接所有可用地址
	for n > 0 {
		// 尝试连接当前地址
		conn, err = d.tryDial(network, &addrs[idx%n], deadline, d.concurrencyCh)
		if err == nil {
			return conn, nil // 连接成功，返回连接
		}
		if err == ErrDialTimeout {
			return nil, err // 超时错误，直接返回
		}
		idx++ // 尝试下一个地址
		n--   // 减少剩余尝试次数
	}
	return nil, err // 所有地址都尝试失败，返回最后的错误
}

// tryDial 尝试拨号连接到指定的TCP地址
// 参数:
//   network: 网络类型（tcp、tcp4、tcp6）
//   addr: 目标TCP地址
//   deadline: 连接截止时间
//   concurrencyCh: 并发控制通道，用于限制同时进行的连接数
// 返回:
//   net.Conn: 建立的网络连接
//   error: 连接错误，成功时为nil
// 功能: 在并发控制下尝试建立TCP连接，支持超时控制
func (d *TCPDialer) tryDial(network string, addr *net.TCPAddr, deadline time.Time, concurrencyCh chan struct{}) (net.Conn, error) {
	// 计算剩余超时时间
	timeout := -time.Since(deadline)
	if timeout <= 0 {
		return nil, ErrDialTimeout // 已经超时，直接返回超时错误
	}

	// 并发控制：限制同时进行的连接数
	if concurrencyCh != nil {
		select {
		case concurrencyCh <- struct{}{}: // 尝试获取并发槽位
		default:
			// 并发槽位已满，等待可用槽位或超时
			tc := time.NewTimer(timeout)
			isTimeout := false
			select {
			case concurrencyCh <- struct{}{}: // 等待槽位可用
			case <-tc.C: // 等待超时
				isTimeout = true
			}
			tc.Stop() // 停止定时器
			if isTimeout {
				return nil, ErrDialTimeout // 等待槽位超时
			}
		}
	}

	// 从对象池获取拨号结果通道
	chv := dialResultChanPool.Get()
	if chv == nil {
		chv = make(chan dialResult, 1) // 池中没有可用通道时创建新的
	}
	ch := chv.(chan dialResult)

	// 在goroutine中执行实际的拨号操作
	go func() {
		var dr dialResult
		// 执行TCP拨号
		dr.conn, dr.err = net.DialTCP(network, d.LocalAddr, addr)
		ch <- dr // 将结果发送到通道
		if concurrencyCh != nil {
			<-concurrencyCh // 释放并发槽位
		}
	}()

	// 初始化返回变量
	var (
		conn net.Conn
		err  error
	)

	// 等待拨号完成或超时
	tc := time.NewTimer(timeout)
	select {
	case dr := <-ch: // 拨号完成
		conn = dr.conn                 // 获取连接
		err = dr.err                   // 获取错误
		dialResultChanPool.Put(ch)     // 将通道放回对象池
	case <-tc.C: // 拨号超时
		err = ErrDialTimeout
	}
	tc.Stop() // 停止定时器

	return conn, err // 返回连接结果
}

// dialResultChanPool 拨号结果通道对象池
// 用于复用拨号结果通道，减少内存分配
var dialResultChanPool sync.Pool

// dialResult 拨号结果结构体
// 包含拨号操作的结果：连接对象和可能的错误
type dialResult struct {
	conn net.Conn // 建立的网络连接
	err  error    // 拨号过程中的错误
}

// ErrDialTimeout TCP拨号超时错误
// 当TCP拨号超时时返回此错误
var ErrDialTimeout = errors.New("dialing to the given TCP address timed out")

// DefaultDialTimeout 默认拨号超时时间
// 用于Dial和DialDualStack方法建立TCP连接的超时时间（3秒）
const DefaultDialTimeout = 3 * time.Second

// tcpAddrEntry TCP地址缓存条目结构体
// 用于缓存DNS解析结果，提高连接性能
type tcpAddrEntry struct {
	addrs    []net.TCPAddr // 解析得到的TCP地址列表
	addrsIdx uint32        // 当前使用的地址索引，用于轮询

	resolveTime time.Time // DNS解析时间，用于缓存过期判断
	pending     bool      // 是否正在进行DNS解析，防止重复解析
}

// DefaultDNSCacheDuration DNS缓存持续时间
// Dial*函数缓存已解析TCP地址的默认时间（1分钟）
const DefaultDNSCacheDuration = time.Minute

// tcpAddrsClean TCP地址缓存清理协程
// 功能: 定期清理过期的DNS缓存条目，防止内存泄漏
func (d *TCPDialer) tcpAddrsClean() {
	expireDuration := 2 * DefaultDNSCacheDuration // 过期时间设为缓存时间的2倍
	for {
		time.Sleep(time.Second) // 每秒检查一次
		t := time.Now()         // 获取当前时间

		d.tcpAddrsLock.Lock() // 加锁保护缓存映射
		// 遍历所有缓存条目，删除过期的条目
		for k, e := range d.tcpAddrsMap {
			if t.Sub(e.resolveTime) > expireDuration {
				delete(d.tcpAddrsMap, k) // 删除过期条目
			}
		}
		d.tcpAddrsLock.Unlock() // 解锁
	}
}

// getTCPAddrs 获取TCP地址列表，支持缓存和轮询
// 参数:
//   addr: 目标地址字符串（host:port格式）
//   dualStack: 是否支持双栈（IPv4和IPv6）
// 返回:
//   []net.TCPAddr: TCP地址列表
//   uint32: 轮询索引，用于负载均衡
//   error: 解析错误，成功时为nil
// 功能: 从缓存获取或重新解析TCP地址，实现DNS缓存和负载均衡
func (d *TCPDialer) getTCPAddrs(addr string, dualStack bool) ([]net.TCPAddr, uint32, error) {
	d.tcpAddrsLock.Lock() // 加锁保护缓存访问
	e := d.tcpAddrsMap[addr] // 获取缓存条目
	// 检查缓存是否过期且未在解析中
	if e != nil && !e.pending && time.Since(e.resolveTime) > DefaultDNSCacheDuration {
		e.pending = true // 标记为正在解析
		e = nil          // 清空条目，触发重新解析
	}
	d.tcpAddrsLock.Unlock() // 解锁

	// 如果没有有效的缓存条目，进行DNS解析
	if e == nil {
		addrs, err := resolveTCPAddrs(addr, dualStack, d.Resolver)
		if err != nil {
			// 解析失败，清除pending标记
			d.tcpAddrsLock.Lock()
			e = d.tcpAddrsMap[addr]
			if e != nil && e.pending {
				e.pending = false // 清除pending标记
			}
			d.tcpAddrsLock.Unlock()
			return nil, 0, err // 返回解析错误
		}

		// 创建新的缓存条目
		e = &tcpAddrEntry{
			addrs:       addrs,        // 保存解析结果
			resolveTime: time.Now(),   // 记录解析时间
		}

		// 将新条目保存到缓存
		d.tcpAddrsLock.Lock()
		d.tcpAddrsMap[addr] = e
		d.tcpAddrsLock.Unlock()
	}

	// 原子操作增加索引，实现轮询负载均衡
	idx := atomic.AddUint32(&e.addrsIdx, 1)
	return e.addrs, idx, nil
}

// resolveTCPAddrs 解析TCP地址，将域名转换为IP地址列表
// 参数:
//   addr: 地址字符串（host:port格式）
//   dualStack: 是否支持双栈（IPv4和IPv6）
//   resolver: DNS解析器，为nil时使用默认解析器
// 返回:
//   []net.TCPAddr: 解析得到的TCP地址列表
//   error: 解析错误，成功时为nil
// 功能: 执行DNS查询，将域名解析为可用的TCP地址列表
func resolveTCPAddrs(addr string, dualStack bool, resolver Resolver) ([]net.TCPAddr, error) {
	// 分离主机名和端口号
	host, portS, err := net.SplitHostPort(addr)
	if err != nil {
		return nil, err // 地址格式错误
	}
	// 将端口字符串转换为整数
	port, err := strconv.Atoi(portS)
	if err != nil {
		return nil, err // 端口号格式错误
	}

	// 如果没有指定解析器，使用默认解析器
	if resolver == nil {
		resolver = net.DefaultResolver
	}

	// 执行DNS查询
	ctx := context.Background()
	ipaddrs, err := resolver.LookupIPAddr(ctx, host)
	if err != nil {
		return nil, err // DNS查询失败
	}

	// 构建TCP地址列表
	n := len(ipaddrs)
	addrs := make([]net.TCPAddr, 0, n)
	for i := 0; i < n; i++ {
		ip := ipaddrs[i]
		// 如果不支持双栈且当前IP是IPv6，跳过
		if !dualStack && ip.IP.To4() == nil {
			continue
		}
		// 添加TCP地址到列表
		addrs = append(addrs, net.TCPAddr{
			IP:   ip.IP,   // IP地址
			Port: port,    // 端口号
			Zone: ip.Zone, // IPv6区域标识符
		})
	}
	// 检查是否有可用的地址
	if len(addrs) == 0 {
		return nil, errNoDNSEntries
	}
	return addrs, nil
}

// errNoDNSEntries DNS查询无结果错误
// 当无法找到指定域名的DNS条目时返回此错误，建议尝试使用DialDualStack
var errNoDNSEntries = errors.New("couldn't find DNS entries for the given domain. Try using DialDualStack")