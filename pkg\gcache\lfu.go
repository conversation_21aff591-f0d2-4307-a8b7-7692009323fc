/*
 * @Author: chenjb
 * @Version: V1.0
 * @Date: 2025-04-29 11:49:46
 * @FilePath: /yaml_scan/pkg/gcache/lfu.go
 * @Description:
 */

package gcache

import (
	"time"
	"yaml_scan/pkg/gcache/list"
)

// lfuItem 表示 LFU 缓存中的一个项。
type lfuItem[K comparable, V any] struct {
	clock       Clock                           // 用于获取当前时间的时钟接口
	key         K                               // 缓存项的键
	value       V                               // 缓存项的值
	freqElement *list.Element[*freqEntry[K, V]] // 指向频率条目的链表元素
	expiration  *time.Time                      // 过期时间
}

// freqEntry 表示频率条目，包含频率和对应的缓存项。
type freqEntry[K comparable, V any] struct {
	freq  uint                        // 访问频率
	items map[*lfuItem[K, V]]struct{} // 存储具有相同频率的缓存项
}

// LFUCache 是一个最不经常使用（LFU）缓存实现，优先驱逐最不经常使用的条目
type LFUCache[K comparable, V any] struct {
	baseCache[K, V]                              // 基础缓存结构
	items           map[K]*lfuItem[K, V]         // 存储缓存项的映射
	freqList        *list.List[*freqEntry[K, V]] // 双向链表，用于跟踪频率条目
}

// init 初始化 LFUCache。
func (c *LFUCache[K, V]) init() {
	// 创建频率条目的链表
	c.freqList = list.New[*freqEntry[K, V]]()
	// 初始化缓存项映射
	c.items = make(map[K]*lfuItem[K, V], c.size)
	// 在链表前端插入频率为 0 的条目
	c.freqList.PushFront(&freqEntry[K, V]{
		freq:  0,
		items: make(map[*lfuItem[K, V]]struct{}),
	})
}

// newLFUCache 创建一个新的 LFUCache 实例
// @param cb *CacheBuilder: cacheBuilder 实例，包含缓存配置
// @return *LFUCache *LFUCache:  新创建的 LFUCache 实例
func newLFUCache[K comparable, V any](cb *CacheBuilder[K, V]) *LFUCache[K, V] {
	// 创建 LFUCache 实例
	c := &LFUCache[K, V]{}
	// 初始化基础缓存字段
	buildCache(&c.baseCache, cb)
	// 初始化 items 映射和 freqList
	c.init()
	// 设置 loadGroup 的缓存实例
	c.loadGroup.cache = c
	return c
}

// IsExpired 检查缓存条目是否已过期
// @param now: 当前时间，如果为 nil 则使用条目的时钟获取
// @return bool: 如果条目已过期，返回 true
func (it *lfuItem[K, V]) IsExpired(now *time.Time) bool {
	// 如果没有设置过期时间，则认为未过期
	if it.expiration == nil {
		return false
	}
	if now == nil {
		t := it.clock.Now()
		now = &t
	}
	return it.expiration.Before(*now)
}

// autoLease 自动续租缓存条目
// @param item: 要续租的缓存条目
func (c *LFUCache[K, V]) autoLease(item *lfuItem[K, V]) {
	// 如果没有设置过期时间，则不进行延长
	if item.expiration == nil {
		return
	}
	if c.lease == nil {
		return
	}
	// 计算新的过期时间
	t := item.clock.Now().Add(*c.lease)
	item.expiration = &t
}

// isRemovableFreqEntry 判断频率条目是否可以被移除。
// @param entry: 要检查的频率条目 
// @return bool: 如果频率条目非零且为空，则返回 true
func isRemovableFreqEntry[K comparable, V any](entry *freqEntry[K, V]) bool {
	// 如果频率不为 0 且没有缓存项，则可以移除
	return entry.freq != 0 && len(entry.items) == 0
}

// increment 增加缓存项的频率。
// @param item: 要增加频率的条目
func (c *LFUCache[K, V]) increment(item *lfuItem[K, V]) {
	// 获取当前频率条目的元素
	currentFreqElement := item.freqElement
	// 获取当前频率条目
	currentFreqEntry := currentFreqElement.Value
	// 计算下一个频率
	nextFreq := currentFreqEntry.freq + 1
	// 从当前频率条目中删除该项
	delete(currentFreqEntry.items, item)

	// 检查当前频率条目是否可以被移除
	removable := isRemovableFreqEntry(currentFreqEntry)

	// 获取下一个频率条目的元素
	nextFreqElement := currentFreqElement.Next()
	switch {
	case nextFreqElement == nil || nextFreqElement.Value.freq > nextFreq:
		// 如果下一个频率条目不存在或其频率大于下一个频率
		if removable {
			// 如果当前条目可以被移除，更新其频率
			currentFreqEntry.freq = nextFreq
			nextFreqElement = currentFreqElement
		} else {
			// 否则，插入一个新的频率条目
			nextFreqElement = c.freqList.InsertAfter(&freqEntry[K, V]{
				freq:  nextFreq,
				items: make(map[*lfuItem[K, V]]struct{}),
			}, currentFreqElement)
		}
	case nextFreqElement.Value.freq == nextFreq:
		// 如果下一个频率条目的频率等于下一个频率
		if removable {
			// 如果当前条目可以被移除，则从链表中移除当前频率条目
			c.freqList.Remove(currentFreqElement)
		}
	default:
		panic("unreachable")
	}
	// 将该项添加到下一个频率条目中
	nextFreqElement.Value.items[item] = struct{}{}
	item.freqElement = nextFreqElement
}

// removeItem 从缓存中移除指定的条目
// @param item: 要移除的条目
func (c *LFUCache[K, V]) removeItem(item *lfuItem[K, V]) {
	// 获取当前频率条目
	entry := item.freqElement.Value
	// 从缓存中删除该项
	delete(c.items, item.key)
	// 从频率条目中删除该项
	delete(entry.items, item)

	// 检查当前频率条目是否可以被移除
	if isRemovableFreqEntry(entry) {
		// 从频率列表中移除该频率条目
		c.freqList.Remove(item.freqElement)
	}

	// 如果定义了 evictedFunc，则调用它
	if c.evictedFunc != nil {
		c.evictedFunc(item.key, item.value)
	}
}

// Remove 删除指定的键
// @param key: 要删除的键
// @return bool: 如果键存在并被删除，返回 true
func (c *LFUCache[K, V]) Remove(key K) bool {
	c.mu.Lock()
	defer c.mu.Unlock()

	return c.remove(key)
}

// remove 内部方法，删除指定的键
// @param key: 要删除的键
// @return bool: 如果键存在并被删除，返回 true。
func (c *LFUCache[K, V]) remove(key K) bool {
	if item, ok := c.items[key]; ok {
		c.removeItem(item)
		return true
	}
	return false
}

// getValue 内部方法，用于获取原始值
// @param key: 要获取的键
// @param enableCount: 是否触发统计
// @return V: 键对应的值
// @return error: 操作失败时的错误
func (c *LFUCache[K, V]) getValue(key K, enableCount bool) (V, error) {
	c.mu.Lock()
	// 查找缓存项
	item, ok := c.items[key]
	if ok {
		// 检查项是否过期
		if !item.IsExpired(nil) {
			// 增加频率
			c.increment(item)
			v := item.value
			// 自动续租（如果适用）
			c.autoLease(item)
			c.mu.Unlock()
			// 增加命中计数
			if enableCount {
				c.stats.IncrHitCount()
			}
			return v, nil
		}
		// 如果过期，移除该项
		c.removeItem(item)
	}
	c.mu.Unlock()
	// 增加未命中计数
	if enableCount {
		c.stats.IncrMissCount()
	}
	return c.nilV, KeyNotFoundError
}

// get 内部方法，用于获取值
// @param key: 要获取的键
// @param enableCount: 是否触发统计
// @return V: 键对应的值
// @return error: 操作失败时的错误
func (c *LFUCache[K, V]) get(key K, enableCount bool) (V, error) {
	v, err := c.getValue(key, enableCount)
	if err != nil {
		return c.nilV, err
	}
	if c.deserializeFunc != nil {
		return c.deserializeFunc(key, v)
	}
	return v, nil
}

// getWithLoader  使用 LoaderFunc 加载值
// @param key: 要加载的键
// @param isWait: 是否等待加载完成
// @return V: 加载的值
// @return error: 加载过程中的错误
func (c *LFUCache[K, V]) getWithLoader(key K, isWait bool) (V, error) {
	// 检查是否定义了加载器函数
	if c.loaderExpireFunc == nil {
		return c.nilV, KeyNotFoundError
	}
	// 调用加载器函数
	value, _, err := c.load(key, func(v V, expiration *time.Duration, e error) (V, error) {
		if e != nil {
			return c.nilV, e
		}
		c.mu.Lock()
		defer c.mu.Unlock()
		// 将加载的值设置到缓存中
		item, err := c.set(key, v)
		if err != nil {
			return c.nilV, err
		}
		// 如果有过期时间，则设置缓存项的过期时间
		if expiration != nil {
			t := c.clock.Now().Add(*expiration)
			item.expiration = &t
		}
		return v, nil
	}, isWait)
	if err != nil {
		return c.nilV, err
	}
	return value, nil
}

// Get 从缓存中获取键的值，如果未找到则通过加载器加载。
// @param key: 要获取的键
// @return  V: 键对应的值
// @return error: 操作失败时的错误
func (c *LFUCache[K, V]) Get(key K) (V, error) {
	v, err := c.get(key, false)
	if err == KeyNotFoundError {
		return c.getWithLoader(key, true)
	}
	return v, err
}

// GetIFPresent  从缓存中获取指定键的值，不等待函数加载完成 异步调用
// @param  key: 要获取的键
// @return  V: 键对应的值
// @return error: 操作失败时的错误
func (c *LFUCache[K, V]) GetIFPresent(key K) (V, error) {
	v, err := c.get(key, false)
	if err == KeyNotFoundError {
		return c.getWithLoader(key, false)
	}
	return v, err
}

// GetALL 返回缓存中的所有键值对。
// @param checkExpired: 是否检查过期条目
// @return map[K]V: 缓存中的键值对映射
func (c *LFUCache[K, V]) GetALL(checkExpired bool) map[K]V {
	c.mu.RLock()
	defer c.mu.RUnlock()
	// 创建一个存储结果的映射
	items := make(map[K]V, len(c.items))
	// 获取当前时间
	now := time.Now()
	for k, item := range c.items {
		// 如果需要检查过期，且该项未过期，则添加到结果中
		if !checkExpired || c.has(k, &now) {
			items[k] = item.value
		}
	}
	return items
}

// has 检查缓存中是否存在指定键的有效项。
// @param key: 要检查的键
// @param now: 当前时间
// @return bool: 如果键存在且未过期，返回 true
func (c *LFUCache[K, V]) has(key K, now *time.Time) bool {
	item, ok := c.items[key]
	if !ok {
		return false
	}
	// 返回该项是否未过期
	return !item.IsExpired(now)
}

// Has 检查缓存中是否存在指定键的有效项。
// @param key: 要检查的键
// @return bool: 如果键存在且未过期，返回 true
func (c *LFUCache[K, V]) Has(key K) bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	now := time.Now()
	return c.has(key, &now)
}


// Len 返回缓存中项的数量
// @param checkExpired: 是否检查过期条目
// @return int: 缓存中的项数
func (c *LFUCache[K, V]) Len(checkExpired bool) int {
	c.mu.RLock()
	defer c.mu.RUnlock()
	// 如果不需要检查过期
	if !checkExpired {
		return len(c.items)
	}
	var length int
	now := time.Now()
	for k := range c.items {
		// 检查该项是否未过期
		if c.has(k, &now) {
			length++
		}
	}
	return length
}

// Purge 完全清空缓存。
func (c *LFUCache[K, V]) Purge() {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 如果设置了 purgeVisitorFunc，则遍历所有项并调用该函数
	if c.purgeVisitorFunc != nil {
		for key, item := range c.items {
			c.purgeVisitorFunc(key, item.value)
		}
	}

	// 重新初始化缓存
	c.init()
}

// keys 返回缓存中所有键的切片（内部方法）
func (c *LFUCache[K, V]) keys() []K {
	c.mu.RLock()                    // 加读锁，允许多个读取操作并发
	defer c.mu.RUnlock()            // 函数结束时解锁
	keys := make([]K, len(c.items)) // 初始化键切片
	var i = 0                       // 计数器
	for k := range c.items {        // 遍历所有键
		keys[i] = k // 添加键
		i++         // 计数器加一
	}
	return keys // 返回键切片
}

// Keys 返回缓存中所有键的切片
// @param checkExpired: 是否检查过期条目
// @return []K: 缓存中的键切片
func (c *LFUCache[K, V]) Keys(checkExpired bool) []K {
	c.mu.RLock()
	defer c.mu.RUnlock()
	// 创建一个切片以存储键
	keys := make([]K, 0, len(c.items))
	now := time.Now()
	for k := range c.items {
		// 检查是否需要过期检查
		if !checkExpired || c.has(k, &now) {
			keys = append(keys, k)
		}
	}
	return keys
}

// evict 移除缓存中最旧的项。
// @param count: 要移除的条目数量
func (c *LFUCache[K, V]) evict(count int) {
	// 获取频率列表的第一个元素
	entry := c.freqList.Front()
	for i := 0; i < count; {
		if entry == nil {
			return
		} else {
			for item := range entry.Value.items {
				if i >= count {
					return
				}
				// 移除当前项
				c.removeItem(item)
				i++
			}
			// 移动到下一个频率条目
			entry = entry.Next()
		}
	}
}

// set 内部方法，用于设置键值对
// @param key: 要设置的键
// @param value: 对应的值
// @return *lruItem[K, V]: 设置的缓存条目
// @return error: 操作失败时的错误
func (c *LFUCache[K, V]) set(key K, value V) (*lfuItem[K, V], error) {
	var err error
	// 如果定义了序列化函数，则调用它
	if c.serializeFunc != nil {
		value, err = c.serializeFunc(key, value)
		if err != nil {
			return nil, err
		}
	}

	// 检查是否已有该项
	item, ok := c.items[key]
	if ok {
		item.value = value
	} else {
		// 如果不存在，检查缓存大小是否超出限制
		if len(c.items) >= c.size {
			// 移除一个最不常用的项
			c.evict(1)
		}
		// 创建新的缓存项
		item = &lfuItem[K, V]{
			clock:       c.clock,
			key:         key,
			value:       value,
			freqElement: nil,
		}

		// 获取频率列表的第一个元素
		el := c.freqList.Front()
		// 获取频率条目
		fe := el.Value
		// 将新项添加到频率条目中
		fe.items[item] = struct{}{}
		// 设置条目的频率元素
		item.freqElement = el
		c.items[key] = item
	}

	// 如果定义了过期时间，则设置过期时间
	if c.expiration != nil {
		t := c.clock.Now().Add(*c.expiration)
		item.expiration = &t
	}

	// 如果定义了添加项的回调函数，则调用它
	if c.addedFunc != nil {
		c.addedFunc(key, value)
	}

	return item, nil
}

// Set 插入或更新指定的键值对
// @param key: 要设置的键
// @param value: 对应的值
// @return error: 操作失败时的错误
func (c *LFUCache[K, V]) Set(key K, value V) error {
	// 加写锁，确保并发安全
	c.mu.Lock()
	defer c.mu.Unlock()
	_, err := c.set(key, value)
	return err
}

// SetWithExpire 插入或更新指定的键值对，并设置过期时间
// @param key: 要设置的键
// @param value: 对应的值
// @param expiration: 过期时间
// @return error: 操作失败时的错误
func (c *LFUCache[K, V]) SetWithExpire(key K, value V, expiration time.Duration) error {
	// 加写锁，确保并发安全
	c.mu.Lock()
	defer c.mu.Unlock()
	item, err := c.set(key, value)
	if err != nil {
		return err
	}
	// 设置缓存项的过期时间
	t := c.clock.Now().Add(expiration)
	item.expiration = &t
	return nil
}
