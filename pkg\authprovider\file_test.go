// Author: chenjb
// Version: V1.0
// Date: 2025-06-16 17:26:03
// FilePath: /yaml_scan/pkg/authprovider/file_test.go
// Description:
package authprovider

import (
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"testing"
	"yaml_scan/pkg/authprovider/authx"
	urlutil "yaml_scan/utils/url"

	"github.com/stretchr/testify/require"
)

// TestNewFileAuthProvider 测试创建基于文件的认证提供者的函数
// 确保它能正确从文件加载认证数据并创建认证提供者
func TestNewFileAuthProvider(t *testing.T) {
	// 创建临时认证文件
	authContent := []byte(`
id: test-auth
info:
  name: Test Auth
static:
  - type: BasicAuth
    domains:
      - example.com
    username: test-user
    password: test-pass
`)
	tempFile := filepath.Join(t.TempDir(), "test-auth.yaml")
	err := os.WriteFile(tempFile, authContent, 0644)
	require.NoError(t, err, "写入临时认证文件不应该返回错误")
	
	// 创建文件认证提供者
	provider, err := NewFileAuthProvider(tempFile, nil)
	require.NoError(t, err, "创建文件认证提供者不应该返回错误")
	require.NotNil(t, provider, "应该返回非空的认证提供者")
	
	// 测试空文件的情况
	emptyContent := []byte(`
id: empty-auth
info:
  name: Empty Auth
`)
	emptyFile := filepath.Join(t.TempDir(), "empty-auth.yaml")
	err = os.WriteFile(emptyFile, emptyContent, 0644)
	require.NoError(t, err, "写入空认证文件不应该返回错误")
	
	_, err = NewFileAuthProvider(emptyFile, nil)
	require.Error(t, err, "使用空认证文件创建认证提供者应该返回错误")
	require.Equal(t, ErrNoSecrets, err, "应该返回ErrNoSecrets错误")
	
	
}

// TestFileAuthProvider_LookupAddr 测试文件认证提供者的LookupAddr方法
// 确保它能正确查找域名和返回适当的认证策略
func TestFileAuthProvider_LookupAddr(t *testing.T) {
	// 创建一个简单的文件认证提供者
	provider := &FileAuthProvider{
		domains: map[string][]authx.AuthStrategy{
			"example.com": {
				&authx.BasicAuthStrategy{
					Data: &authx.Secret{
						Username: "test-user",
						Password: "test-pass",
					},
				},
			},
			"api.example.com": {
				&authx.BearerTokenAuthStrategy{
					Data: &authx.Secret{
						Token: "test-token",
					},
				},
			},
		},
		compiled: make(map[*regexp.Regexp][]authx.AuthStrategy),
	}
	
	// 编译正则表达式并添加到provider
	regex, err := regexp.Compile(".*\\.api\\.example\\.org")
	require.NoError(t, err, "编译正则表达式不应该返回错误")
	
	provider.compiled[regex] = []authx.AuthStrategy{
		&authx.HeadersAuthStrategy{
			Data: &authx.Secret{
				Headers: []authx.KV{
					{Key: "X-API-Key", Value: "test-api-key"},
				},
			},
		},
	}
	
	// 测试精确域名匹配
	strategies := provider.LookupAddr("example.com")
	require.Len(t, strategies, 1, "应该找到1个认证策略")
	require.IsType(t, &authx.BasicAuthStrategy{}, strategies[0], "应该返回BasicAuthStrategy类型")
	
	// 测试端口规范化（删除标准HTTP/HTTPS端口）
	strategies = provider.LookupAddr("example.com:443")
	require.Len(t, strategies, 1, "应该找到1个认证策略")
	require.IsType(t, &authx.BasicAuthStrategy{}, strategies[0], "应该返回BasicAuthStrategy类型")
	
	strategies = provider.LookupAddr("example.com:80")
	require.Len(t, strategies, 1, "应该找到1个认证策略")
	require.IsType(t, &authx.BasicAuthStrategy{}, strategies[0], "应该返回BasicAuthStrategy类型")
	
	// 测试正则表达式匹配
	strategies = provider.LookupAddr("test.api.example.org")
	require.Len(t, strategies, 1, "应该找到1个认证策略")
	require.IsType(t, &authx.HeadersAuthStrategy{}, strategies[0], "应该返回HeadersAuthStrategy类型")
	
	// 测试不匹配的域名
	strategies = provider.LookupAddr("unknown.com")
	require.Empty(t, strategies, "对于不匹配的域名应该返回空的策略列表")
}

// TestFileAuthProvider_LookupURL 测试文件认证提供者的LookupURL方法
// 确保它能正确查找URL并返回适当的认证策略
func TestFileAuthProvider_LookupURL(t *testing.T) {
	// 创建一个简单的文件认证提供者
	provider := &FileAuthProvider{
		domains: map[string][]authx.AuthStrategy{
			"example.com": {
				&authx.BasicAuthStrategy{
					Data: &authx.Secret{
						Username: "test-user",
						Password: "test-pass",
					},
				},
			},
		},
	}
	
	// 创建测试URL
	testURL, err := url.Parse("https://example.com/path")
	require.NoError(t, err, "解析URL不应该返回错误")
	
	// 测试URL查找
	strategies := provider.LookupURL(testURL)
	require.Len(t, strategies, 1, "应该找到1个认证策略")
	require.IsType(t, &authx.BasicAuthStrategy{}, strategies[0], "应该返回BasicAuthStrategy类型")
	
	// 创建不匹配的URL
	unknownURL, err := url.Parse("https://unknown.com/path")
	require.NoError(t, err, "解析URL不应该返回错误")
	
	// 测试不匹配的URL
	strategies = provider.LookupURL(unknownURL)
	require.Empty(t, strategies, "对于不匹配的URL应该返回空的策略列表")
}

// TestFileAuthProvider_LookupURLX 测试文件认证提供者的LookupURLX方法
// 确保它能正确查找自定义URL并返回适当的认证策略
func TestFileAuthProvider_LookupURLX(t *testing.T) {
	// 创建一个简单的文件认证提供者
	provider := &FileAuthProvider{
		domains: map[string][]authx.AuthStrategy{
			"example.com": {
				&authx.BasicAuthStrategy{
					Data: &authx.Secret{
						Username: "test-user",
						Password: "test-pass",
					},
				},
			},
		},
	}
	
	// 创建测试自定义URL
	testURL := &urlutil.URL{
		URL: &url.URL{
			Host: "example.com",
		},
	}
	
	// 测试自定义URL查找
	strategies := provider.LookupURLX(testURL)
	require.Len(t, strategies, 1, "应该找到1个认证策略")
	require.IsType(t, &authx.BasicAuthStrategy{}, strategies[0], "应该返回BasicAuthStrategy类型")
	
	// 创建不匹配的自定义URL
	unknownURL := &urlutil.URL{
		URL: &url.URL{
			Host: "unknown.com",
		},
	}
	
	// 测试不匹配的自定义URL
	strategies = provider.LookupURLX(unknownURL)
	require.Empty(t, strategies, "对于不匹配的自定义URL应该返回空的策略列表")
}

// TestFileAuthProvider_GetTemplatePaths 测试文件认证提供者的GetTemplatePaths方法
// 确保它能正确返回所有模板路径
func TestFileAuthProvider_GetTemplatePaths(t *testing.T) {
	// 创建一个带有动态密钥的文件认证提供者
	provider := &FileAuthProvider{
		store: &authx.Authx{
			Dynamic: []authx.Dynamic{
				{TemplatePath: "template1.yaml"},
				{TemplatePath: "template2.yaml"},
				{TemplatePath: ""}, // 空模板路径
			},
		},
	}
	
	// 获取模板路径
	paths := provider.GetTemplatePaths()
	require.Len(t, paths, 2, "应该返回2个模板路径")
	require.Contains(t, paths, "template1.yaml", "应该包含template1.yaml")
	require.Contains(t, paths, "template2.yaml", "应该包含template2.yaml")
	require.NotContains(t, paths, "", "不应该包含空模板路径")
}
