// Author: chenjb
// Version: V1.0
// Date: 2025-06-13 14:53:14
// FilePath: /yaml_scan/pkg/authprovider/authx/basic_auth.go
// Description:实现了基本认证(BasicAuth)策略，用于HTTP Basic认证
package authx

import (
	"net/http"
	"yaml_scan/pkg/retryablehttp"
)

var (
	// 确保BasicAuthStrategy实现了AuthStrategy接口	
	_ AuthStrategy = &BasicAuthStrategy{}
)

// BasicAuthStrategy 是基本认证策略的实现
// 用于HTTP Basic认证，通过用户名和密码进行认证
type BasicAuthStrategy struct {
	// Data 包含基本认证所需的密钥信息（用户名和密码）
	Data *Secret
}

// NewBasicAuthStrategy 创建一个新的基本认证策略
// @param data *Secret: 包含用户名和密码的密钥结构体指针
// @return *BasicAuthStrategy *BasicAuthStrategy: 新创建的基本认证策略
func NewBasicAuthStrategy(data *Secret) *BasicAuthStrategy {
	return &BasicAuthStrategy{Data: data}
}


// Apply 将基本认证策略应用到HTTP请求上
// @receiver s 
// @param req *http.Request: 标准的HTTP请求指针
func (s *BasicAuthStrategy) Apply(req *http.Request) {
	// 使用Secret中的用户名和密码设置HTTP Basic认证
	req.SetBasicAuth(s.Data.Username, s.Data.Password)
}

// ApplyOnRR 将基本认证策略应用到可重试的HTTP请求上
// @receiver s 
// @param req *retryablehttp.Request: 可重试的HTTP请求指针
func (s *BasicAuthStrategy) ApplyOnRR(req *retryablehttp.Request) {
	req.SetBasicAuth(s.Data.Username, s.Data.Password)
}