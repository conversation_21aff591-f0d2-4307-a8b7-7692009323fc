package goflags

import (
	"testing"
)


// TestInsertionOrderedMap 测试 InsertionOrderedMap 的 Set 方法
func TestInsertionOrderedMap(t *testing.T) {
	// 创建一个新的 InsertionOrderedMap 实例
	mapInstance := newInsertionOrderedMap()

	// 创建一些 FlagData 实例
	flag1 := &FlagData{short: "flag1", long: "value1"}
	flag2 := &FlagData{short: "flag2", long: "value2"}
	flag3 := &FlagData{short: "flag3", long: "value3"}

	// 测试添加新键
	mapInstance.Set("key1", flag1)
	if len(mapInstance.keys) != 1 || mapInstance.keys[0] != "key1" {
		t.<PERSON><PERSON>("Expected keys to be [key1], got %v", mapInstance.keys)
	}
	if mapInstance.values["key1"] != flag1 {
		t.Errorf("Expected value for key1 to be %v, got %v", flag1, mapInstance.values["key1"])
	}

	// 测试添加另一个新键
	mapInstance.Set("key2", flag2)
	if len(mapInstance.keys) != 2 || mapInstance.keys[1] != "key2" {
		t.<PERSON><PERSON><PERSON>("Expected keys to be [key1 key2], got %v", mapInstance.keys)
	}
	if mapInstance.values["key2"] != flag2 {
		t.Errorf("Expected value for key2 to be %v, got %v", flag2, mapInstance.values["key2"])
	}

	// 测试更新现有键
	mapInstance.Set("key1", flag3)
	if len(mapInstance.keys) != 2 {
		t.Errorf("Expected keys to remain [key1 key2], got %v", mapInstance.keys)
	}
	if mapInstance.values["key1"] != flag3 {
		t.Errorf("Expected value for key1 to be updated to %v, got %v", flag3, mapInstance.values["key1"])
	}
}