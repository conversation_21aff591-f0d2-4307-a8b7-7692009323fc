//
// Author: chenjb
// Version: V1.0
// Date: 2025-07-18 11:10:30
// FilePath: /yaml_scan/pkg/wappalyzergo/fingerprint_body_test.go
// Description: fingerprint_body.go 的单元测试文件

package wappalyzergo

import (
    "strings"
    "testing"

    "github.com/stretchr/testify/require"
)

// TestCheckBody 测试HTML响应体指纹检查功能
func TestCheckBody(t *testing.T) {
    wappalyzer, err := New()
    require.NoError(t, err, "创建实例不应失败")

    t.Run("检测HTML中的技术指纹", func(t *testing.T) {
        // 包含jQuery的HTML内容
        body := []byte(`
            <html>
            <head>
                <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
            </head>
            <body>
                <div id="content">测试内容</div>
            </body>
            </html>
        `)

        normalizedBody := normalizeToLowercase(body)
        results := wappalyzer.checkBody(normalizedBody)
        require.NotNil(t, results, "结果不应为nil")
    })

    t.Run("检测Meta标签信息", func(t *testing.T) {
        body := []byte(`
            <html>
            <head>
                <meta name="generator" content="WordPress 5.8">
                <meta name="description" content="测试网站">
            </head>
            <body></body>
            </html>
        `)

        normalizedBody := normalizeToLowercase(body)
        results := wappalyzer.checkBody(normalizedBody)
        require.NotNil(t, results, "结果不应为nil")
    })

    t.Run("检测自闭合Meta标签", func(t *testing.T) {
        body := []byte(`
            <html>
            <head>
                <meta name="generator" content="Drupal 9.0" />
                <meta name="viewport" content="width=device-width" />
            </head>
            <body></body>
            </html>
        `)

        normalizedBody := normalizeToLowercase(body)
        results := wappalyzer.checkBody(normalizedBody)
        require.NotNil(t, results, "结果不应为nil")
    })

    t.Run("检测脚本标签", func(t *testing.T) {
        body := []byte(`
            <html>
            <head>
                <script type="text/javascript">
                    var angular = angular || {};
                </script>
                <script src="/js/angular.min.js"></script>
            </head>
            <body></body>
            </html>
        `)

        normalizedBody := normalizeToLowercase(body)
        results := wappalyzer.checkBody(normalizedBody)
        require.NotNil(t, results, "结果不应为nil")
    })

    t.Run("检测外部脚本源", func(t *testing.T) {
        body := []byte(`
            <html>
            <head>
                <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.0/dist/js/bootstrap.bundle.min.js"></script>
                <script src="/assets/js/app.js?v=1.2.3"></script>
            </head>
            <body></body>
            </html>
        `)

        normalizedBody := normalizeToLowercase(body)
        results := wappalyzer.checkBody(normalizedBody)
        require.NotNil(t, results, "结果不应为nil")
    })

    t.Run("检测内联脚本内容", func(t *testing.T) {
        body := []byte(`
            <html>
            <head>
                <script>
                    window.jQuery = window.$ = jQuery;
                    console.log('jQuery loaded');
                </script>
            </head>
            <body></body>
            </html>
        `)

        normalizedBody := normalizeToLowercase(body)
        results := wappalyzer.checkBody(normalizedBody)
        require.NotNil(t, results, "结果不应为nil")
    })

    t.Run("空HTML内容", func(t *testing.T) {
        body := []byte("")
        results := wappalyzer.checkBody(body)
        require.NotNil(t, results, "空内容结果不应为nil")
        require.Empty(t, results, "空内容应该返回空结果")
    })

    t.Run("无效HTML内容", func(t *testing.T) {
        body := []byte("这不是HTML内容")
        results := wappalyzer.checkBody(body)
        require.NotNil(t, results, "无效HTML结果不应为nil")
    })

    t.Run("包含特殊字符的HTML", func(t *testing.T) {
        body := []byte(`
            <html>
            <head>
                <title>测试页面 & 特殊字符 < > "</title>
                <meta name="description" content="包含&amp;特殊字符的内容">
            </head>
            <body>
                <div>内容包含特殊字符：&amp; &lt; &gt;</div>
            </body>
            </html>
        `)

        normalizedBody := normalizeToLowercase(body)
        results := wappalyzer.checkBody(normalizedBody)
        require.NotNil(t, results, "特殊字符HTML结果不应为nil")
    })

    t.Run("大型HTML文档", func(t *testing.T) {
        // 创建一个较大的HTML文档
        largeContent := make([]byte, 0, 10000)
        largeContent = append(largeContent, []byte("<html><head><title>大型文档</title></head><body>")...)
        
        // 添加重复内容
        for i := 0; i < 100; i++ {
            largeContent = append(largeContent, []byte("<div>重复内容块</div>")...)
        }
        largeContent = append(largeContent, []byte("</body></html>")...)

        normalizedBody := normalizeToLowercase(largeContent)
        results := wappalyzer.checkBody(normalizedBody)
        require.NotNil(t, results, "大型文档结果不应为nil")
    })

    t.Run("嵌套HTML标签", func(t *testing.T) {
        body := []byte(`
            <html>
            <head>
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        console.log('页面加载完成');
                    });
                </script>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>标题</h1>
                    </div>
                    <div class="content">
                        <p>段落内容</p>
                        <ul>
                            <li>列表项1</li>
                            <li>列表项2</li>
                        </ul>
                    </div>
                </div>
            </body>
            </html>
        `)

        normalizedBody := normalizeToLowercase(body)
        results := wappalyzer.checkBody(normalizedBody)
        require.NotNil(t, results, "嵌套HTML结果不应为nil")
    })

    t.Run("混合标签类型", func(t *testing.T) {
        body := []byte(`
            <html>
            <head>
                <meta name="generator" content="Hugo 0.88.0" />
                <script src="jquery.min.js"></script>
                <script>var test = 'value';</script>
            </head>
            <body>
                <div>内容</div>
            </body>
            </html>
        `)

        normalizedBody := normalizeToLowercase(body)
        results := wappalyzer.checkBody(normalizedBody)
        require.NotNil(t, results, "混合标签结果不应为nil")
    })

    t.Run("格式错误的HTML", func(t *testing.T) {
        body := []byte(`
            <html>
            <head>
                <meta name="generator" content="Test
                <script src="test.js"
            </head>
            <body>
                <div>未闭合标签
            </body>
        `)

        normalizedBody := normalizeToLowercase(body)
        results := wappalyzer.checkBody(normalizedBody)
        require.NotNil(t, results, "格式错误HTML结果不应为nil")
    })
}

// TestGetTitle 测试页面标题提取功能
func TestGetTitle(t *testing.T) {
    wappalyzer, err := New()
    require.NoError(t, err, "创建实例不应失败")

    t.Run("提取基本标题", func(t *testing.T) {
        body := []byte(`
            <html>
            <head>
                <title>测试页面标题</title>
            </head>
            <body></body>
            </html>
        `)

        title := wappalyzer.getTitle(body)
        require.Equal(t, "测试页面标题", title, "应该正确提取标题")
    })

    t.Run("提取包含特殊字符的标题", func(t *testing.T) {
        body := []byte(`
            <html>
            <head>
                <title>测试 & 特殊字符 < > " '</title>
            </head>
            <body></body>
            </html>
        `)

        title := wappalyzer.getTitle(body)
        require.Equal(t, "测试 & 特殊字符 < > \" '", title, "应该正确提取包含特殊字符的标题")
    })

    t.Run("提取空标题", func(t *testing.T) {
        body := []byte(`
            <html>
            <head>
                <title></title>
            </head>
            <body></body>
            </html>
        `)

        title := wappalyzer.getTitle(body)
        require.Empty(t, title, "空标题应该返回空字符串")
    })

    t.Run("无标题标签", func(t *testing.T) {
        body := []byte(`
            <html>
            <head>
                <meta name="description" content="无标题页面">
            </head>
            <body></body>
            </html>
        `)

        title := wappalyzer.getTitle(body)
        require.Empty(t, title, "无标题标签应该返回空字符串")
    })

    t.Run("多个标题标签", func(t *testing.T) {
        body := []byte(`
            <html>
            <head>
                <title>第一个标题</title>
                <title>第二个标题</title>
            </head>
            <body></body>
            </html>
        `)

        title := wappalyzer.getTitle(body)
        require.Equal(t, "第一个标题", title, "应该返回第一个标题")
    })

    t.Run("标题在body中", func(t *testing.T) {
        body := []byte(`
            <html>
            <head></head>
            <body>
                <title>Body中的标题</title>
            </body>
            </html>
        `)

        title := wappalyzer.getTitle(body)
        require.Equal(t, "Body中的标题", title, "应该能提取body中的标题")
    })

    t.Run("格式错误的标题", func(t *testing.T) {
        body := []byte(`
            <html>
            <head>
                <title>未闭合标题
            </head>
            <body></body>
            </html>
        `)

        title := wappalyzer.getTitle(body)
        require.Equal(t, "未闭合标题", title, "应该能处理格式错误的标题")
    })

    t.Run("长标题", func(t *testing.T) {
        longTitle := strings.Repeat("很长的标题内容 ", 100)
        body := []byte(`
            <html>
            <head>
                <title>` + longTitle + `</title>
            </head>
            <body></body>
            </html>
        `)

        title := wappalyzer.getTitle(body)
        require.Equal(t, longTitle, title, "应该能处理长标题")
    })
}

// TestGetMetaNameAndContent 测试Meta标签属性提取
func TestGetMetaNameAndContent(t *testing.T) {
    t.Run("提取完整Meta属性", func(t *testing.T) {
        // 模拟HTML token
        token := createMockMetaToken("generator", "WordPress 5.8")
        
        name, content, found := getMetaNameAndContent(token)
        require.True(t, found, "应该找到Meta属性")
        require.Equal(t, "generator", name, "name属性应该匹配")
        require.Equal(t, "WordPress 5.8", content, "content属性应该匹配")
    })

    t.Run("属性不足的Meta标签", func(t *testing.T) {
        token := createMockMetaTokenWithAttrs([]mockAttr{
            {Key: "name", Val: "generator"},
        })
        
        name, content, found := getMetaNameAndContent(token)
        require.False(t, found, "属性不足应该返回false")
        require.Empty(t, name, "name应该为空")
        require.Empty(t, content, "content应该为空")
    })

    t.Run("缺少name属性", func(t *testing.T) {
        token := createMockMetaTokenWithAttrs([]mockAttr{
            {Key: "content", Val: "WordPress 5.8"},
            {Key: "charset", Val: "utf-8"},
        })
        
        name, content, found := getMetaNameAndContent(token)
        require.False(t, found, "缺少name属性应该返回false")
    })

    t.Run("缺少content属性", func(t *testing.T) {
        token := createMockMetaTokenWithAttrs([]mockAttr{
            {Key: "name", Val: "generator"},
            {Key: "charset", Val: "utf-8"},
        })
        
        name, content, found := getMetaNameAndContent(token)
        require.False(t, found, "缺少content属性应该返回false")
    })

    t.Run("空属性值", func(t *testing.T) {
        token := createMockMetaToken("", "")
        
        name, content, found := getMetaNameAndContent(token)
        require.True(t, found, "空值也应该被识别")
        require.Empty(t, name, "name应该为空")
        require.Empty(t, content, "content应该为空")
    })

    t.Run("多余属性", func(t *testing.T) {
        token := createMockMetaTokenWithAttrs([]mockAttr{
            {Key: "name", Val: "generator"},
            {Key: "content", Val: "Hugo 0.88.0"},
            {Key: "charset", Val: "utf-8"},
            {Key: "http-equiv", Val: "refresh"},
        })
        
        name, content, found := getMetaNameAndContent(token)
        require.True(t, found, "应该找到Meta属性")
        require.Equal(t, "generator", name, "name属性应该匹配")
        require.Equal(t, "Hugo 0.88.0", content, "content属性应该匹配")
    })
}

// TestGetScriptSource 测试脚本源提取
func TestGetScriptSource(t *testing.T) {
    t.Run("提取基本脚本源", func(t *testing.T) {
        token := createMockScriptToken("jquery.min.js")
        
        source, found := getScriptSource(token)
        require.True(t, found, "应该找到脚本源")
        require.Equal(t, "jquery.min.js", source, "脚本源应该匹配")
    })

    t.Run("无属性的脚本标签", func(t *testing.T) {
        token := createMockScriptTokenWithAttrs([]mockAttr{})
        
        source, found := getScriptSource(token)
        require.False(t, found, "无属性应该返回false")
        require.Empty(t, source, "源应该为空")
    })

    t.Run("无src属性的脚本标签", func(t *testing.T) {
        token := createMockScriptTokenWithAttrs([]mockAttr{
            {Key: "type", Val: "text/javascript"},
            {Key: "async", Val: "true"},
        })
        
        source, found := getScriptSource(token)
        require.True(t, found, "应该返回true（即使src为空）")
        require.Empty(t, source, "源应该为空")
    })

    t.Run("空src属性", func(t *testing.T) {
        token := createMockScriptToken("")
        
        source, found := getScriptSource(token)
        require.True(t, found, "应该返回true")
        require.Empty(t, source, "空源应该为空字符串")
    })

    t.Run("完整URL脚本源", func(t *testing.T) {
        url := "https://cdn.jsdelivr.net/npm/bootstrap@5.1.0/dist/js/bootstrap.bundle.min.js"
        token := createMockScriptToken(url)
        
        source, found := getScriptSource(token)
        require.True(t, found, "应该找到脚本源")
        require.Equal(t, url, source, "完整URL应该匹配")
    })

    t.Run("带查询参数的脚本源", func(t *testing.T) {
        url := "/assets/js/app.js?v=1.2.3&t=123456"
        token := createMockScriptToken(url)
        
        source, found := getScriptSource(token)
        require.True(t, found, "应该找到脚本源")
        require.Equal(t, url, source, "带参数URL应该匹配")
    })
}

// TestUnsafeToString 测试字节数组到字符串转换
func TestUnsafeToString(t *testing.T) {
    t.Run("基本转换", func(t *testing.T) {
        data := []byte("Hello, World!")
        result := unsafeToString(data)
        require.Equal(t, "Hello, World!", result, "转换结果应该匹配")
    })

    t.Run("空字节数组", func(t *testing.T) {
        data := []byte{}
        result := unsafeToString(data)
        require.Empty(t, result, "空数组应该返回空字符串")
    })

    t.Run("包含特殊字符", func(t *testing.T) {
        data := []byte("测试\n\t\r特殊字符")
        result := unsafeToString(data)
        require.Equal(t, "测试\n\t\r特殊字符", result, "特殊字符应该正确转换")
    })

    t.Run("二进制数据", func(t *testing.T) {
        data := []byte{0x00, 0x01, 0x02, 0xFF}
        result := unsafeToString(data)
        require.Len(t, result, 4, "长度应该匹配")
    })

    t.Run("大数据转换", func(t *testing.T) {
        data := make([]byte, 10000)
        for i := range data {
            data[i] = byte(i % 256)
        }
        result := unsafeToString(data)
        require.Len(t, result, 10000, "大数据长度应该匹配")
    })
}

// TestBodyFingerprinting 测试响应体指纹识别的完整流程
func TestBodyFingerprinting(t *testing.T) {
    wappalyzer, err := New()
    require.NoError(t, err, "创建实例不应失败")

    testCases := []struct {
        name        string
        body        string
        expectTech  bool
        description string
    }{
        {
            name: "WordPress指纹",
            body: `<html><head><meta name="generator" content="WordPress 5.8"></head></html>`,
            expectTech: true,
            description: "应该识别WordPress",
        },
        {
            name: "jQuery指纹",
            body: `<html><head><script src="jquery-3.6.0.min.js"></script></head></html>`,
            expectTech: true,
            description: "应该识别jQuery",
        },
        {
            name: "Bootstrap指纹",
            body: `<html><head><link rel="stylesheet" href="bootstrap.min.css"></head></html>`,
            expectTech: true,
            description: "应该识别Bootstrap",
        },
        {
            name: "空内容",
            body: "",
            expectTech: false,
            description: "空内容不应识别技术",
        },
        {
            name: "纯文本",
            body: "这是纯文本内容，没有HTML标签",
            expectTech: false,
            description: "纯文本不应识别技术",
        },
        {
            name: "复合技术栈",
            body: `
                <html>
                <head>
                    <meta name="generator" content="Drupal 9.0">
                    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
                    <script src="/sites/all/modules/views/js/views.js"></script>
                </head>
                <body></body>
                </html>
            `,
            expectTech: true,
            description: "应该识别多种技术",
        },
    }

    for _, tc := range testCases {
        t.Run(tc.name, func(t *testing.T) {
            body := []byte(tc.body)
            normalizedBody := normalizeToLowercase(body)

            results := wappalyzer.checkBody(normalizedBody)
            require.NotNil(t, results, "结果不应为nil")

            if tc.expectTech {
                t.Logf("检测到 %d 个技术指纹", len(results))
            }
        })
    }
}

// 辅助函数：标准化为小写
func normalizeToLowercase(body []byte) []byte {
    normalizedBody := make([]byte, len(body))
    copy(normalizedBody, body)
    for i := range normalizedBody {
        if normalizedBody[i] >= 'A' && normalizedBody[i] <= 'Z' {
            normalizedBody[i] += 32
        }
    }
    return normalizedBody
}

// Mock 结构和函数用于测试
type mockAttr struct {
    Key string
    Val string
}

func createMockMetaToken(name, content string) html.Token {
    return createMockMetaTokenWithAttrs([]mockAttr{
        {Key: "name", Val: name},
        {Key: "content", Val: content},
    })
}

func createMockMetaTokenWithAttrs(attrs []mockAttr) html.Token {
    token := html.Token{
        Type: html.StartTagToken,
        Data: "meta",
        Attr: make([]html.Attribute, len(attrs)),
    }
    
    for i, attr := range attrs {
        token.Attr[i] = html.Attribute{
            Key: attr.Key,
            Val: attr.Val,
        }
    }
    
    return token
}

func createMockScriptToken(src string) html.Token {
    return createMockScriptTokenWithAttrs([]mockAttr{
        {Key: "src", Val: src},
    })
}

func createMockScriptTokenWithAttrs(attrs []mockAttr) html.Token {
    token := html.Token{
        Type: html.StartTagToken,
        Data: "script",
        Attr: make([]html.Attribute, len(attrs)),
    }
    
    for i, attr := range attrs {
        token.Attr[i] = html.Attribute{
            Key: attr.Key,
            Val: attr.Val,
        }
    }
    
    return token
}
