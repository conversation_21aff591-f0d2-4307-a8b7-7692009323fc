//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-19 20:06:55
//FilePath: /yaml_scan/pkg/input/formats/swagger/swagger.go
//Description: 解析Swagger格式

package swagger

import (
	"encoding/json"
	"io"
	"os"
	"path"

	"yaml_scan/pkg/input/formats"
	"yaml_scan/pkg/input/formats/openapi"

	"github.com/getkin/kin-openapi/openapi2"
	"github.com/getkin/kin-openapi/openapi2conv"
	"github.com/getkin/kin-openapi/openapi3"
	"github.com/invopop/yaml"
	"github.com/pkg/errors"
)

// SwaggerFormat 是一个Swagger Schema文件解析器
type SwaggerFormat struct {
	opts formats.InputFormatOptions
}

// New 创建一个新的Swagger格式解析器
func New() *SwaggerFormat {
	return &SwaggerFormat{}
}

// 确保 SwaggerFormat 实现了 formats.Format 接口
var _ formats.Format = &SwaggerFormat{}

// Name 返回格式的名称
func (j *SwaggerFormat) Name() string {
	return "swagger"
}

// SetOptions 设置解析器的选项
func (j *SwaggerFormat) SetOptions(options formats.InputFormatOptions) {
	j.opts = options
}

// Parse:  解析输入文件并为每个发现的 RawRequest 调用提供的回调函数
//
//	@receiver j *SwaggerFormat:
//	@param input string:  表示要解析的输入文件路径。
//	@param resultsCb formats.ParseReqRespCallback: 解析后处理结果的回调函数。
//	@return error error:如果解析过程中出现错误，返回相应的错误；否则返回nil。
func (j *SwaggerFormat) Parse(input string, resultsCb formats.ParseReqRespCallback) error {
	// 打开输入文件
	file, err := os.Open(input)
	if err != nil {
		return errors.Wrap(err, "could not open data file")
	}
	defer file.Close()

	// 创建一个OpenAPI 2.0的Schema对象
	schemav2 := &openapi2.T{}
	ext := path.Ext(input)

	// 根据文件扩展名选择解析方法
	if ext == ".yaml" || ext == ".yml" {
		// 读取YAML文件内容
		data, err_data := io.ReadAll(file)
		if err_data != nil {
			return errors.Wrap(err, "could not read data file")
		}
		err = yaml.Unmarshal(data, schemav2)
	} else {
		// 解析JSON数据到OpenAPI 2.0 Schema
		err = json.NewDecoder(file).Decode(schemav2)
	}
	if err != nil {
		return errors.Wrap(err, "could not decode openapi 2.0 schema")
	}
	// 将OpenAPI 2.0 Schema转换为OpenAPI 3.0 Schema
	schema, err := openapi2conv.ToV3(schemav2)
	if err != nil {
		return errors.Wrap(err, "could not convert openapi 2.0 schema to 3.0")
	}
	// 创建一个新的OpenAPI 3.0加载器
	loader := openapi3.NewLoader()
	// 解析并解决Schema中的引用
	err = loader.ResolveRefsIn(schema, nil)
	if err != nil {
		return errors.Wrap(err, "could not resolve openapi schema references")
	}
	return openapi.GenerateRequestsFromSchema(schema, j.opts, resultsCb)
}
