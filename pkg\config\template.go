//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-25 16:17:40
//FilePath: /yaml_scan/pkg/config/template.go
//Description:

package config

import (
	"path/filepath"

	"yaml_scan/pkg/templates/extensions"
	stringsutil "yaml_scan/utils/strings"
)

// 已知的配置文件
var knownConfigFiles = []string{"cves.json", "contributors.json", "TEMPLATES-STATS.json"}

// GetSupportedTemplateFileExtensions 获取支持的模板文件扩展名
func GetSupportTemplateFileExtensions() []string {
	return []string{extensions.YAML, extensions.JSON}
}

// isTemplate  是一个回调函数，用于由 goflags 决定给定的文件是否应该被读取。
// 只有当文件不是 yaml 模板文件时，才会读取该文件。
func IsTemplate(filename string) bool {

	// 检查文件名是否包含已知的配置文件名。
	// 如果文件名包含这些已知的配置文件名，则返回 false，表示该文件不是模板文件
	if stringsutil.ContainsAny(filename, knownConfigFiles...) {
		return false
	}

	// 检查文件的扩展名是否与支持的模板文件扩展名相匹配。
	// 如果文件扩展名与支持的模板文件扩展名匹配，则返回 true，表示该文件是模板文件。
	return stringsutil.EqualFoldAny(filepath.Ext(filename), GetSupportTemplateFileExtensions()...)
}
