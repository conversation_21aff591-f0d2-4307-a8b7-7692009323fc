//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-04 16:57:12
//FilePath: /yaml_scan/utils/conn/inflight.go
//Description:

package connpool

import (
	"errors"
	"net"
	mapsutil "yaml_scan/utils/maps"

	"go.uber.org/multierr"
)

// InFlightConns 用于管理正在进行的网络连接。
type InFlightConns struct {
	inflightConns *mapsutil.SyncLockMap[net.Conn, struct{}]
}

// NewInFlightConns:创建一个新的 InFlightConns 实例。
//
//	@return *InFlightConns *InFlightConns:
//	@return error error:
func NewInFlightConns() (*InFlightConns, error) {
	m := &mapsutil.SyncLockMap[net.Conn, struct{}]{
		Map: mapsutil.Map[net.Conn, struct{}]{},
	}
	return &InFlightConns{inflightConns: m}, nil
}

// Remove 从正在进行的连接中移除指定的连接。
func (i *InFlightConns) Remove(conn net.Conn) {
	i.inflightConns.Delete(conn)
}

// Add 将指定的连接添加到正在进行的连接中。
func (i *InFlightConns) Add(conn net.Conn) {
	_ = i.inflightConns.Set(conn, struct{}{})
}

// Close: 关闭所有正在进行的连接，并清空连接列表。
//
//	@receiver i *InFlightConns:
//	@return error error:
func (i *InFlightConns) Close() error {
	var errs []error

	// 遍历所有正在进行的连接并关闭它们
	_ = i.inflightConns.Iterate(func(conn net.Conn, _ struct{}) error {
		if err := conn.Close(); err != nil {
			errs = append(errs, err)
		}
		return nil
	})

	// 清空正在进行的连接列表
	if ok := i.inflightConns.Clear(); !ok {
		errs = append(errs, errors.New("couldn't empty in flight connections"))
	}

	return multierr.Combine(errs...)
}
