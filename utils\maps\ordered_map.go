//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-11 17:53:45
//FilePath: /yaml_scan/utils/maps/ordered_map.go
//Description: 有序字典映射

package mapsutil

// OrderedMap 是一个保留元素顺序的映射
// 注意：顺序仅对当前级别的 OrderedMap 保证
// 嵌套值仅在它们也是 OrderedMap 时保留顺序
type OrderedMap[k comparable, v any] struct {
	keys   []k     // 存储键的顺序
	m      map[k]v // 存储键值对的映射
	dirty  bool    // 如果在迭代时修改了键，则标记为脏
	inIter bool    // 当前是否在迭代中
}

// NewOrderedMap: 创建一个新的 OrderedMap
//
//	@return OrderedMap OrderedMap: 返回一个空的 OrderedMap，其中 keys 切片和 m 映射都被初始化为空。
func NewOrderedMap[k comparable, v any]() OrderedMap[k, v] {
	return OrderedMap[k, v]{
		keys: []k{},
		m:    map[k]v{},
	}
}

// IsEmpty:  检测OrderedMap是否为空
//
//	@return  bool: 空返回True 否则返回Fasle
func (o *OrderedMap[k, v]) IsEmpty() bool {
	return len(o.keys) == 0
}

// GetKeys 获取所有的keys
//
//	@return GetKeys GetKeys: 返回所有的keys
func (o *OrderedMap[k, v]) GetKeys() []k {
	return o.keys
}

// Get:  根据key获取value
//
//	@param key k: key
//	@return v bool: 返回value和指示键是否存在于 OrderedMap 中
func (o *OrderedMap[k, v]) Get(key k) (v, bool) {
	value, ok := o.m[key]
	return value, ok
}

// Set: 添加key value值
//
//	@param key:
//	@param value:
func (o *OrderedMap[k, v]) Set(key k, value v) {
	// 检查键是否已存在于映射中
	if _, ok := o.m[key]; !ok {
		// 如果键不存在，则将其添加到 keys 切片中
		o.keys = append(o.keys, key)
	}
	// 更新映射中的值
	o.m[key] = value
	// 如果当前在迭代中，标记为脏
	if o.inIter {
		o.dirty = true
	}
}

// Len: 获取OrderedMap的长度
//  @return int: 
func (o *OrderedMap[k, v]) Len() int {
	return len(o.keys)
}

// Iterate iterates over the OrderedMap in insertion order
func (o *OrderedMap[k, v]) Iterate(f func(key k, value v) bool) {
	o.inIter = true
	defer func() {
		o.inIter = false
	}()
	for _, key := range o.keys {
		// silently discard any missing keys from the map
		if _, ok := o.m[key]; !ok {
			continue
		}
		if !f(key, o.m[key]) {
			break
		}
	}
	if o.dirty {
		tmp := make([]k, len(o.m))
		for _, key := range o.keys {
			if _, ok := o.m[key]; ok {
				tmp = append(tmp, key)
			}
		}
		o.keys = tmp
		o.dirty = false
	}
}