{"TLS_AES_128_CCM_8_SHA256": "Secure", "TLS_AES_128_CCM_SHA256": "Secure", "TLS_AES_128_GCM_SHA256": "Recommended", "TLS_AES_256_GCM_SHA384": "Recommended", "TLS_CHACHA20_POLY1305_SHA256": "Recommended", "TLS_DHE_DSS_EXPORT_WITH_DES40_CBC_SHA": "Insecure", "TLS_DHE_DSS_WITH_3DES_EDE_CBC_SHA": "Weak", "TLS_DHE_DSS_WITH_AES_128_CBC_SHA": "Weak", "TLS_DHE_DSS_WITH_AES_128_CBC_SHA256": "Weak", "TLS_DHE_DSS_WITH_AES_128_GCM_SHA256": "Recommended", "TLS_DHE_DSS_WITH_AES_256_CBC_SHA": "Weak", "TLS_DHE_DSS_WITH_AES_256_CBC_SHA256": "Weak", "TLS_DHE_DSS_WITH_AES_256_GCM_SHA384": "Recommended", "TLS_DHE_DSS_WITH_ARIA_128_CBC_SHA256": "Weak", "TLS_DHE_DSS_WITH_ARIA_128_GCM_SHA256": "Recommended", "TLS_DHE_DSS_WITH_ARIA_256_CBC_SHA384": "Weak", "TLS_DHE_DSS_WITH_ARIA_256_GCM_SHA384": "Recommended", "TLS_DHE_DSS_WITH_CAMELLIA_128_CBC_SHA": "Weak", "TLS_DHE_DSS_WITH_CAMELLIA_128_CBC_SHA256": "Weak", "TLS_DHE_DSS_WITH_CAMELLIA_128_GCM_SHA256": "Recommended", "TLS_DHE_DSS_WITH_CAMELLIA_256_CBC_SHA": "Weak", "TLS_DHE_DSS_WITH_CAMELLIA_256_CBC_SHA256": "Weak", "TLS_DHE_DSS_WITH_CAMELLIA_256_GCM_SHA384": "Recommended", "TLS_DHE_DSS_WITH_DES_CBC_SHA": "Insecure", "TLS_DHE_DSS_WITH_SEED_CBC_SHA": "Weak", "TLS_DHE_PSK_WITH_3DES_EDE_CBC_SHA": "Weak", "TLS_DHE_PSK_WITH_AES_128_CBC_SHA": "Weak", "TLS_DHE_PSK_WITH_AES_128_CBC_SHA256": "Weak", "TLS_DHE_PSK_WITH_AES_128_CCM": "Secure", "TLS_DHE_PSK_WITH_AES_128_GCM_SHA256": "Recommended", "TLS_DHE_PSK_WITH_AES_256_CBC_SHA": "Weak", "TLS_DHE_PSK_WITH_AES_256_CBC_SHA384": "Weak", "TLS_DHE_PSK_WITH_AES_256_CCM": "Secure", "TLS_DHE_PSK_WITH_AES_256_GCM_SHA384": "Recommended", "TLS_DHE_PSK_WITH_ARIA_128_CBC_SHA256": "Weak", "TLS_DHE_PSK_WITH_ARIA_128_GCM_SHA256": "Recommended", "TLS_DHE_PSK_WITH_ARIA_256_CBC_SHA384": "Weak", "TLS_DHE_PSK_WITH_ARIA_256_GCM_SHA384": "Recommended", "TLS_DHE_PSK_WITH_CAMELLIA_128_CBC_SHA256": "Weak", "TLS_DHE_PSK_WITH_CAMELLIA_128_GCM_SHA256": "Recommended", "TLS_DHE_PSK_WITH_CAMELLIA_256_CBC_SHA384": "Weak", "TLS_DHE_PSK_WITH_CAMELLIA_256_GCM_SHA384": "Recommended", "TLS_DHE_PSK_WITH_CHACHA20_POLY1305_SHA256": "Recommended", "TLS_DHE_PSK_WITH_NULL_SHA": "Insecure", "TLS_DHE_PSK_WITH_NULL_SHA256": "Insecure", "TLS_DHE_PSK_WITH_NULL_SHA384": "Insecure", "TLS_DHE_PSK_WITH_RC4_128_SHA": "Insecure", "TLS_DHE_RSA_EXPORT_WITH_DES40_CBC_SHA": "Insecure", "TLS_DHE_RSA_WITH_3DES_EDE_CBC_SHA": "Weak", "TLS_DHE_RSA_WITH_AES_128_CBC_SHA": "Weak", "TLS_DHE_RSA_WITH_AES_128_CBC_SHA256": "Weak", "TLS_DHE_RSA_WITH_AES_128_CCM": "Secure", "TLS_DHE_RSA_WITH_AES_128_CCM_8": "Secure", "TLS_DHE_RSA_WITH_AES_128_GCM_SHA256": "Secure", "TLS_DHE_RSA_WITH_AES_256_CBC_SHA": "Weak", "TLS_DHE_RSA_WITH_AES_256_CBC_SHA256": "Weak", "TLS_DHE_RSA_WITH_AES_256_CCM": "Secure", "TLS_DHE_RSA_WITH_AES_256_CCM_8": "Secure", "TLS_DHE_RSA_WITH_AES_256_GCM_SHA384": "Secure", "TLS_DHE_RSA_WITH_ARIA_128_CBC_SHA256": "Weak", "TLS_DHE_RSA_WITH_ARIA_128_GCM_SHA256": "Secure", "TLS_DHE_RSA_WITH_ARIA_256_CBC_SHA384": "Weak", "TLS_DHE_RSA_WITH_ARIA_256_GCM_SHA384": "Secure", "TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA": "Weak", "TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA256": "Weak", "TLS_DHE_RSA_WITH_CAMELLIA_128_GCM_SHA256": "Secure", "TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA": "Weak", "TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA256": "Weak", "TLS_DHE_RSA_WITH_CAMELLIA_256_GCM_SHA384": "Secure", "TLS_DHE_RSA_WITH_CHACHA20_POLY1305_SHA256": "Secure", "TLS_DHE_RSA_WITH_DES_CBC_SHA": "Insecure", "TLS_DHE_RSA_WITH_SEED_CBC_SHA": "Weak", "TLS_DH_ANON_EXPORT_WITH_DES40_CBC_SHA": "Insecure", "TLS_DH_ANON_EXPORT_WITH_RC4_40_MD5": "Insecure", "TLS_DH_ANON_WITH_3DES_EDE_CBC_SHA": "Insecure", "TLS_DH_ANON_WITH_AES_128_CBC_SHA": "Insecure", "TLS_DH_ANON_WITH_AES_128_CBC_SHA256": "Insecure", "TLS_DH_ANON_WITH_AES_128_GCM_SHA256": "Insecure", "TLS_DH_ANON_WITH_AES_256_CBC_SHA": "Insecure", "TLS_DH_ANON_WITH_AES_256_CBC_SHA256": "Insecure", "TLS_DH_ANON_WITH_AES_256_GCM_SHA384": "Insecure", "TLS_DH_ANON_WITH_ARIA_128_CBC_SHA256": "Insecure", "TLS_DH_ANON_WITH_ARIA_128_GCM_SHA256": "Insecure", "TLS_DH_ANON_WITH_ARIA_256_CBC_SHA384": "Insecure", "TLS_DH_ANON_WITH_ARIA_256_GCM_SHA384": "Insecure", "TLS_DH_ANON_WITH_CAMELLIA_128_CBC_SHA": "Insecure", "TLS_DH_ANON_WITH_CAMELLIA_128_CBC_SHA256": "Insecure", "TLS_DH_ANON_WITH_CAMELLIA_128_GCM_SHA256": "Insecure", "TLS_DH_ANON_WITH_CAMELLIA_256_CBC_SHA": "Insecure", "TLS_DH_ANON_WITH_CAMELLIA_256_CBC_SHA256": "Insecure", "TLS_DH_ANON_WITH_CAMELLIA_256_GCM_SHA384": "Insecure", "TLS_DH_ANON_WITH_DES_CBC_SHA": "Insecure", "TLS_DH_ANON_WITH_RC4_128_MD5": "Insecure", "TLS_DH_ANON_WITH_SEED_CBC_SHA": "Insecure", "TLS_DH_DSS_EXPORT_WITH_DES40_CBC_SHA": "Insecure", "TLS_DH_DSS_WITH_3DES_EDE_CBC_SHA": "Weak", "TLS_DH_DSS_WITH_AES_128_CBC_SHA": "Weak", "TLS_DH_DSS_WITH_AES_128_CBC_SHA256": "Weak", "TLS_DH_DSS_WITH_AES_128_GCM_SHA256": "Weak", "TLS_DH_DSS_WITH_AES_256_CBC_SHA": "Weak", "TLS_DH_DSS_WITH_AES_256_CBC_SHA256": "Weak", "TLS_DH_DSS_WITH_AES_256_GCM_SHA384": "Weak", "TLS_DH_DSS_WITH_ARIA_128_CBC_SHA256": "Weak", "TLS_DH_DSS_WITH_ARIA_128_GCM_SHA256": "Weak", "TLS_DH_DSS_WITH_ARIA_256_CBC_SHA384": "Weak", "TLS_DH_DSS_WITH_ARIA_256_GCM_SHA384": "Weak", "TLS_DH_DSS_WITH_CAMELLIA_128_CBC_SHA": "Weak", "TLS_DH_DSS_WITH_CAMELLIA_128_CBC_SHA256": "Weak", "TLS_DH_DSS_WITH_CAMELLIA_128_GCM_SHA256": "Weak", "TLS_DH_DSS_WITH_CAMELLIA_256_CBC_SHA": "Weak", "TLS_DH_DSS_WITH_CAMELLIA_256_CBC_SHA256": "Weak", "TLS_DH_DSS_WITH_CAMELLIA_256_GCM_SHA384": "Weak", "TLS_DH_DSS_WITH_DES_CBC_SHA": "Insecure", "TLS_DH_DSS_WITH_SEED_CBC_SHA": "Weak", "TLS_DH_RSA_EXPORT_WITH_DES40_CBC_SHA": "Insecure", "TLS_DH_RSA_WITH_3DES_EDE_CBC_SHA": "Weak", "TLS_DH_RSA_WITH_AES_128_CBC_SHA": "Weak", "TLS_DH_RSA_WITH_AES_128_CBC_SHA256": "Weak", "TLS_DH_RSA_WITH_AES_128_GCM_SHA256": "Weak", "TLS_DH_RSA_WITH_AES_256_CBC_SHA": "Weak", "TLS_DH_RSA_WITH_AES_256_CBC_SHA256": "Weak", "TLS_DH_RSA_WITH_AES_256_GCM_SHA384": "Weak", "TLS_DH_RSA_WITH_ARIA_128_CBC_SHA256": "Weak", "TLS_DH_RSA_WITH_ARIA_128_GCM_SHA256": "Weak", "TLS_DH_RSA_WITH_ARIA_256_CBC_SHA384": "Weak", "TLS_DH_RSA_WITH_ARIA_256_GCM_SHA384": "Weak", "TLS_DH_RSA_WITH_CAMELLIA_128_CBC_SHA": "Weak", "TLS_DH_RSA_WITH_CAMELLIA_128_CBC_SHA256": "Weak", "TLS_DH_RSA_WITH_CAMELLIA_128_GCM_SHA256": "Weak", "TLS_DH_RSA_WITH_CAMELLIA_256_CBC_SHA": "Weak", "TLS_DH_RSA_WITH_CAMELLIA_256_CBC_SHA256": "Weak", "TLS_DH_RSA_WITH_CAMELLIA_256_GCM_SHA384": "Weak", "TLS_DH_RSA_WITH_DES_CBC_SHA": "Insecure", "TLS_DH_RSA_WITH_SEED_CBC_SHA": "Weak", "TLS_ECCPWD_WITH_AES_128_CCM_SHA256": "Secure", "TLS_ECCPWD_WITH_AES_128_GCM_SHA256": "Recommended", "TLS_ECCPWD_WITH_AES_256_CCM_SHA384": "Secure", "TLS_ECCPWD_WITH_AES_256_GCM_SHA384": "Recommended", "TLS_ECDHE_ECDSA_WITH_3DES_EDE_CBC_SHA": "Weak", "TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA": "Weak", "TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256": "Weak", "TLS_ECDHE_ECDSA_WITH_AES_128_CCM": "Secure", "TLS_ECDHE_ECDSA_WITH_AES_128_CCM_8": "Secure", "TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256": "Recommended", "TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA": "Weak", "TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384": "Weak", "TLS_ECDHE_ECDSA_WITH_AES_256_CCM": "Secure", "TLS_ECDHE_ECDSA_WITH_AES_256_CCM_8": "Secure", "TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384": "Recommended", "TLS_ECDHE_ECDSA_WITH_ARIA_128_CBC_SHA256": "Weak", "TLS_ECDHE_ECDSA_WITH_ARIA_128_GCM_SHA256": "Recommended", "TLS_ECDHE_ECDSA_WITH_ARIA_256_CBC_SHA384": "Weak", "TLS_ECDHE_ECDSA_WITH_ARIA_256_GCM_SHA384": "Recommended", "TLS_ECDHE_ECDSA_WITH_CAMELLIA_128_CBC_SHA256": "Weak", "TLS_ECDHE_ECDSA_WITH_CAMELLIA_128_GCM_SHA256": "Recommended", "TLS_ECDHE_ECDSA_WITH_CAMELLIA_256_CBC_SHA384": "Weak", "TLS_ECDHE_ECDSA_WITH_CAMELLIA_256_GCM_SHA384": "Recommended", "TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256": "Recommended", "TLS_ECDHE_ECDSA_WITH_NULL_SHA": "Insecure", "TLS_ECDHE_ECDSA_WITH_RC4_128_SHA": "Insecure", "TLS_ECDHE_PSK_WITH_3DES_EDE_CBC_SHA": "Weak", "TLS_ECDHE_PSK_WITH_AES_128_CBC_SHA": "Weak", "TLS_ECDHE_PSK_WITH_AES_128_CBC_SHA256": "Weak", "TLS_ECDHE_PSK_WITH_AES_128_CCM_8_SHA256": "Secure", "TLS_ECDHE_PSK_WITH_AES_128_CCM_SHA256": "Secure", "TLS_ECDHE_PSK_WITH_AES_128_GCM_SHA256": "Recommended", "TLS_ECDHE_PSK_WITH_AES_256_CBC_SHA": "Weak", "TLS_ECDHE_PSK_WITH_AES_256_CBC_SHA384": "Weak", "TLS_ECDHE_PSK_WITH_AES_256_GCM_SHA384": "Recommended", "TLS_ECDHE_PSK_WITH_ARIA_128_CBC_SHA256": "Weak", "TLS_ECDHE_PSK_WITH_ARIA_256_CBC_SHA384": "Weak", "TLS_ECDHE_PSK_WITH_CAMELLIA_128_CBC_SHA256": "Weak", "TLS_ECDHE_PSK_WITH_CAMELLIA_256_CBC_SHA384": "Weak", "TLS_ECDHE_PSK_WITH_CHACHA20_POLY1305_SHA256": "Recommended", "TLS_ECDHE_PSK_WITH_NULL_SHA": "Insecure", "TLS_ECDHE_PSK_WITH_NULL_SHA256": "Insecure", "TLS_ECDHE_PSK_WITH_NULL_SHA384": "Insecure", "TLS_ECDHE_PSK_WITH_RC4_128_SHA": "Insecure", "TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA": "Weak", "TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA": "Weak", "TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256": "Weak", "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256": "Secure", "TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA": "Weak", "TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384": "Weak", "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384": "Secure", "TLS_ECDHE_RSA_WITH_ARIA_128_CBC_SHA256": "Weak", "TLS_ECDHE_RSA_WITH_ARIA_128_GCM_SHA256": "Secure", "TLS_ECDHE_RSA_WITH_ARIA_256_CBC_SHA384": "Weak", "TLS_ECDHE_RSA_WITH_ARIA_256_GCM_SHA384": "Secure", "TLS_ECDHE_RSA_WITH_CAMELLIA_128_CBC_SHA256": "Weak", "TLS_ECDHE_RSA_WITH_CAMELLIA_128_GCM_SHA256": "Secure", "TLS_ECDHE_RSA_WITH_CAMELLIA_256_CBC_SHA384": "Weak", "TLS_ECDHE_RSA_WITH_CAMELLIA_256_GCM_SHA384": "Secure", "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256": "Secure", "TLS_ECDHE_RSA_WITH_NULL_SHA": "Insecure", "TLS_ECDHE_RSA_WITH_RC4_128_SHA": "Insecure", "TLS_ECDH_ANON_WITH_3DES_EDE_CBC_SHA": "Insecure", "TLS_ECDH_ANON_WITH_AES_128_CBC_SHA": "Insecure", "TLS_ECDH_ANON_WITH_AES_256_CBC_SHA": "Insecure", "TLS_ECDH_ANON_WITH_NULL_SHA": "Insecure", "TLS_ECDH_ANON_WITH_RC4_128_SHA": "Insecure", "TLS_ECDH_ECDSA_WITH_3DES_EDE_CBC_SHA": "Weak", "TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA": "Weak", "TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA256": "Weak", "TLS_ECDH_ECDSA_WITH_AES_128_GCM_SHA256": "Weak", "TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA": "Weak", "TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA384": "Weak", "TLS_ECDH_ECDSA_WITH_AES_256_GCM_SHA384": "Weak", "TLS_ECDH_ECDSA_WITH_ARIA_128_CBC_SHA256": "Weak", "TLS_ECDH_ECDSA_WITH_ARIA_128_GCM_SHA256": "Weak", "TLS_ECDH_ECDSA_WITH_ARIA_256_CBC_SHA384": "Weak", "TLS_ECDH_ECDSA_WITH_ARIA_256_GCM_SHA384": "Weak", "TLS_ECDH_ECDSA_WITH_CAMELLIA_128_CBC_SHA256": "Weak", "TLS_ECDH_ECDSA_WITH_CAMELLIA_128_GCM_SHA256": "Weak", "TLS_ECDH_ECDSA_WITH_CAMELLIA_256_CBC_SHA384": "Weak", "TLS_ECDH_ECDSA_WITH_CAMELLIA_256_GCM_SHA384": "Weak", "TLS_ECDH_ECDSA_WITH_NULL_SHA": "Insecure", "TLS_ECDH_ECDSA_WITH_RC4_128_SHA": "Insecure", "TLS_ECDH_RSA_WITH_3DES_EDE_CBC_SHA": "Weak", "TLS_ECDH_RSA_WITH_AES_128_CBC_SHA": "Weak", "TLS_ECDH_RSA_WITH_AES_128_CBC_SHA256": "Weak", "TLS_ECDH_RSA_WITH_AES_128_GCM_SHA256": "Weak", "TLS_ECDH_RSA_WITH_AES_256_CBC_SHA": "Weak", "TLS_ECDH_RSA_WITH_AES_256_CBC_SHA384": "Weak", "TLS_ECDH_RSA_WITH_AES_256_GCM_SHA384": "Weak", "TLS_ECDH_RSA_WITH_ARIA_128_CBC_SHA256": "Weak", "TLS_ECDH_RSA_WITH_ARIA_128_GCM_SHA256": "Weak", "TLS_ECDH_RSA_WITH_ARIA_256_CBC_SHA384": "Weak", "TLS_ECDH_RSA_WITH_ARIA_256_GCM_SHA384": "Weak", "TLS_ECDH_RSA_WITH_CAMELLIA_128_CBC_SHA256": "Weak", "TLS_ECDH_RSA_WITH_CAMELLIA_128_GCM_SHA256": "Weak", "TLS_ECDH_RSA_WITH_CAMELLIA_256_CBC_SHA384": "Weak", "TLS_ECDH_RSA_WITH_CAMELLIA_256_GCM_SHA384": "Weak", "TLS_ECDH_RSA_WITH_NULL_SHA": "Insecure", "TLS_ECDH_RSA_WITH_RC4_128_SHA": "Insecure", "TLS_GOSTR341112_256_WITH_28147_CNT_IMIT": "Insecure", "TLS_GOSTR341112_256_WITH_KUZNYECHIK_CTR_OMAC": "Insecure", "TLS_GOSTR341112_256_WITH_KUZNYECHIK_MGM_L": "Insecure", "TLS_GOSTR341112_256_WITH_KUZNYECHIK_MGM_S": "Recommended", "TLS_GOSTR341112_256_WITH_MAGMA_CTR_OMAC": "Insecure", "TLS_GOSTR341112_256_WITH_MAGMA_MGM_L": "Recommended", "TLS_GOSTR341112_256_WITH_MAGMA_MGM_S": "Recommended", "TLS_KRB5_EXPORT_WITH_DES_CBC_40_MD5": "Insecure", "TLS_KRB5_EXPORT_WITH_DES_CBC_40_SHA": "Insecure", "TLS_KRB5_EXPORT_WITH_RC2_CBC_40_MD5": "Insecure", "TLS_KRB5_EXPORT_WITH_RC2_CBC_40_SHA": "Insecure", "TLS_KRB5_EXPORT_WITH_RC4_40_MD5": "Insecure", "TLS_KRB5_EXPORT_WITH_RC4_40_SHA": "Insecure", "TLS_KRB5_WITH_3DES_EDE_CBC_MD5": "Insecure", "TLS_KRB5_WITH_3DES_EDE_CBC_SHA": "Weak", "TLS_KRB5_WITH_DES_CBC_MD5": "Insecure", "TLS_KRB5_WITH_DES_CBC_SHA": "Insecure", "TLS_KRB5_WITH_IDEA_CBC_MD5": "Insecure", "TLS_KRB5_WITH_IDEA_CBC_SHA": "Weak", "TLS_KRB5_WITH_RC4_128_MD5": "Insecure", "TLS_KRB5_WITH_RC4_128_SHA": "Insecure", "TLS_NULL_WITH_NULL_NULL": "Insecure", "TLS_PSK_DHE_WITH_AES_128_CCM_8": "Secure", "TLS_PSK_DHE_WITH_AES_256_CCM_8": "Secure", "TLS_PSK_WITH_3DES_EDE_CBC_SHA": "Weak", "TLS_PSK_WITH_AES_128_CBC_SHA": "Weak", "TLS_PSK_WITH_AES_128_CBC_SHA256": "Weak", "TLS_PSK_WITH_AES_128_CCM": "Weak", "TLS_PSK_WITH_AES_128_CCM_8": "Weak", "TLS_PSK_WITH_AES_128_GCM_SHA256": "Weak", "TLS_PSK_WITH_AES_256_CBC_SHA": "Weak", "TLS_PSK_WITH_AES_256_CBC_SHA384": "Weak", "TLS_PSK_WITH_AES_256_CCM": "Weak", "TLS_PSK_WITH_AES_256_CCM_8": "Weak", "TLS_PSK_WITH_AES_256_GCM_SHA384": "Weak", "TLS_PSK_WITH_ARIA_128_CBC_SHA256": "Weak", "TLS_PSK_WITH_ARIA_128_GCM_SHA256": "Weak", "TLS_PSK_WITH_ARIA_256_CBC_SHA384": "Weak", "TLS_PSK_WITH_ARIA_256_GCM_SHA384": "Weak", "TLS_PSK_WITH_CAMELLIA_128_CBC_SHA256": "Weak", "TLS_PSK_WITH_CAMELLIA_128_GCM_SHA256": "Weak", "TLS_PSK_WITH_CAMELLIA_256_CBC_SHA384": "Weak", "TLS_PSK_WITH_CAMELLIA_256_GCM_SHA384": "Weak", "TLS_PSK_WITH_CHACHA20_POLY1305_SHA256": "Weak", "TLS_PSK_WITH_NULL_SHA": "Insecure", "TLS_PSK_WITH_NULL_SHA256": "Insecure", "TLS_PSK_WITH_NULL_SHA384": "Insecure", "TLS_PSK_WITH_RC4_128_SHA": "Insecure", "TLS_RSA_EXPORT_WITH_DES40_CBC_SHA": "Insecure", "TLS_RSA_EXPORT_WITH_RC2_CBC_40_MD5": "Insecure", "TLS_RSA_EXPORT_WITH_RC4_40_MD5": "Insecure", "TLS_RSA_PSK_WITH_3DES_EDE_CBC_SHA": "Weak", "TLS_RSA_PSK_WITH_AES_128_CBC_SHA": "Weak", "TLS_RSA_PSK_WITH_AES_128_CBC_SHA256": "Weak", "TLS_RSA_PSK_WITH_AES_128_GCM_SHA256": "Weak", "TLS_RSA_PSK_WITH_AES_256_CBC_SHA": "Weak", "TLS_RSA_PSK_WITH_AES_256_CBC_SHA384": "Weak", "TLS_RSA_PSK_WITH_AES_256_GCM_SHA384": "Weak", "TLS_RSA_PSK_WITH_ARIA_128_CBC_SHA256": "Weak", "TLS_RSA_PSK_WITH_ARIA_128_GCM_SHA256": "Weak", "TLS_RSA_PSK_WITH_ARIA_256_CBC_SHA384": "Weak", "TLS_RSA_PSK_WITH_ARIA_256_GCM_SHA384": "Weak", "TLS_RSA_PSK_WITH_CAMELLIA_128_CBC_SHA256": "Weak", "TLS_RSA_PSK_WITH_CAMELLIA_128_GCM_SHA256": "Weak", "TLS_RSA_PSK_WITH_CAMELLIA_256_CBC_SHA384": "Weak", "TLS_RSA_PSK_WITH_CAMELLIA_256_GCM_SHA384": "Weak", "TLS_RSA_PSK_WITH_CHACHA20_POLY1305_SHA256": "Weak", "TLS_RSA_PSK_WITH_NULL_SHA": "Insecure", "TLS_RSA_PSK_WITH_NULL_SHA256": "Insecure", "TLS_RSA_PSK_WITH_NULL_SHA384": "Insecure", "TLS_RSA_PSK_WITH_RC4_128_SHA": "Insecure", "TLS_RSA_WITH_3DES_EDE_CBC_SHA": "Weak", "TLS_RSA_WITH_AES_128_CBC_SHA": "Weak", "TLS_RSA_WITH_AES_128_CBC_SHA256": "Weak", "TLS_RSA_WITH_AES_128_CCM": "Weak", "TLS_RSA_WITH_AES_128_CCM_8": "Weak", "TLS_RSA_WITH_AES_128_GCM_SHA256": "Weak", "TLS_RSA_WITH_AES_256_CBC_SHA": "Weak", "TLS_RSA_WITH_AES_256_CBC_SHA256": "Weak", "TLS_RSA_WITH_AES_256_CCM": "Weak", "TLS_RSA_WITH_AES_256_CCM_8": "Weak", "TLS_RSA_WITH_AES_256_GCM_SHA384": "Weak", "TLS_RSA_WITH_ARIA_128_CBC_SHA256": "Weak", "TLS_RSA_WITH_ARIA_128_GCM_SHA256": "Weak", "TLS_RSA_WITH_ARIA_256_CBC_SHA384": "Weak", "TLS_RSA_WITH_ARIA_256_GCM_SHA384": "Weak", "TLS_RSA_WITH_CAMELLIA_128_CBC_SHA": "Weak", "TLS_RSA_WITH_CAMELLIA_128_CBC_SHA256": "Weak", "TLS_RSA_WITH_CAMELLIA_128_GCM_SHA256": "Weak", "TLS_RSA_WITH_CAMELLIA_256_CBC_SHA": "Weak", "TLS_RSA_WITH_CAMELLIA_256_CBC_SHA256": "Weak", "TLS_RSA_WITH_CAMELLIA_256_GCM_SHA384": "Weak", "TLS_RSA_WITH_DES_CBC_SHA": "Insecure", "TLS_RSA_WITH_IDEA_CBC_SHA": "Weak", "TLS_RSA_WITH_NULL_MD5": "Insecure", "TLS_RSA_WITH_NULL_SHA": "Insecure", "TLS_RSA_WITH_NULL_SHA256": "Insecure", "TLS_RSA_WITH_RC4_128_MD5": "Insecure", "TLS_RSA_WITH_RC4_128_SHA": "Insecure", "TLS_RSA_WITH_SEED_CBC_SHA": "Weak", "TLS_SHA256_SHA256": "Insecure", "TLS_SHA384_SHA384": "Insecure", "TLS_SM4_CCM_SM3": "Insecure", "TLS_SM4_GCM_SM3": "Insecure", "TLS_SRP_SHA_DSS_WITH_3DES_EDE_CBC_SHA": "Weak", "TLS_SRP_SHA_DSS_WITH_AES_128_CBC_SHA": "Weak", "TLS_SRP_SHA_DSS_WITH_AES_256_CBC_SHA": "Weak", "TLS_SRP_SHA_RSA_WITH_3DES_EDE_CBC_SHA": "Weak", "TLS_SRP_SHA_RSA_WITH_AES_128_CBC_SHA": "Weak", "TLS_SRP_SHA_RSA_WITH_AES_256_CBC_SHA": "Weak", "TLS_SRP_SHA_WITH_3DES_EDE_CBC_SHA": "Weak", "TLS_SRP_SHA_WITH_AES_128_CBC_SHA": "Weak", "TLS_SRP_SHA_WITH_AES_256_CBC_SHA": "Weak"}