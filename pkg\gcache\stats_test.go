//
// Author: chenjb
// Version: V1.0
// Date: 2025-04-29 16:51:46
// FilePath: /yaml_scan/pkg/gcache/stats_test.go
// Description:

package gcache

import (
	"testing"

	"github.com/stretchr/testify/require"
)

// TestStats 测试 stats 结构体的统计功能
func TestStats(t *testing.T) {
	stats := &stats{}

	// 初始状态检查
	require.Equal(t, uint64(0), stats.HitCount(), "初始命中次数应为 0")
	require.Equal(t, uint64(0), stats.MissCount(), "初始未命中次数应为 0")
	require.Equal(t, uint64(0), stats.LookupCount(), "初始查找总次数应为 0")
	require.Equal(t, 0.0, stats.HitRate(), "初始命中率应为 0.0")

	// 模拟一些缓存操作
	stats.IncrHitCount()  // 增加命中
	stats.IncrMissCount() // 增加未命中
	stats.IncrHitCount()  // 再次增加命中

	// 断言统计信息
	require.Equal(t, uint64(2), stats.HitCount())                                                     // 期望命中次数为 2
	require.Equal(t, uint64(1), stats.MissCount())                                                    // 期望未命中次数为 1
	require.Equal(t, uint64(3), stats.LookupCount())                                                  // 期望查找次数为 3
	require.Equal(t, float64(2)/float64(3), stats.HitRate(), "Hit rate should be approximately 0.67") // 期望命中率约为 0.67
}
