// Author: chenjb
// Version: V1.0
// Date: 2025-06-19 15:37:00
// FilePath: /yaml_scan/pkg/dnsx/runner/runner.go
// Description: 实现DNS查询运行器，负责处理DNS查询和结果输出
package runner

import (
	"bufio"
	"context"
	"errors"
	"fmt"
	"io"
	"os"
	"strings"
	"sync"
	"time"
	"yaml_scan/pkg/cidr"
	"yaml_scan/pkg/clistats"
	"yaml_scan/pkg/dnsx/libs/dnsx"
	"yaml_scan/pkg/gologger"
	"yaml_scan/pkg/hybridMap/hybrid"
	"yaml_scan/pkg/ratelimit"
	"yaml_scan/pkg/retryabledns"
	"yaml_scan/pkg/utils"
	fileutil "yaml_scan/utils/file"
	iputil "yaml_scan/utils/ip"
	sliceutil "yaml_scan/utils/slice"

	"github.com/logrusorgru/aurora"
	"github.com/miekg/dns"
)

// Runner 是运行DNS查询枚举过程的客户端结构体
type Runner struct {
	options             *Options                  // 运行配置选项
	dnsx                *dnsx.DNSX                // DNS查询客户端
	wgoutputworker      *sync.WaitGroup           // 输出工作器等待组
	wgresolveworkers    *sync.WaitGroup           // 解析工作器等待组
	wgwildcardworker    *sync.WaitGroup           // 通配符工作器等待组
	workerchan          chan string               // 工作通道，用于传递待处理的主机名
	outputchan          chan string               // 输出通道，用于传递结果
	wildcardworkerchan  chan string               // 通配符工作通道
	wildcards           map[string]struct{}       // 存储已确认的通配符域名
	wildcardsmutex      sync.RWMutex              // 通配符映射的互斥锁
	wildcardscache      map[string][]string       // 通配符DNS响应缓存
	wildcardscachemutex sync.Mutex                // 通配符缓存的互斥锁
	limiter             *ratelimit.Limiter        // 查询速率限制器
	hm                  *hybrid.HybridMap         // 混合映射，用于存储查询结果
	stats               clistats.StatisticsClient // 统计信息客户端
	tmpStdinFile        string                    // 临时标准输入文件路径
	aurora              aurora.Aurora             // 控制台颜色输出工具
}

// New 创建一个新的Runner实例
// @param options *Options:  运行器配置选项
// @return *Runner *Runner: 创建的Runner实例
// @return error error:
func New(options *Options) (*Runner, error) {
	// 设置是否检查内部IP
	retryabledns.CheckInternalIPs = true

	// 配置DNS选项
	dnsxOptions := dnsx.DefaultOptions
	dnsxOptions.MaxRetries = options.Retries
	dnsxOptions.TraceMaxRecursion = options.TraceMaxRecursion
	dnsxOptions.Hostsfile = options.HostsFile
	dnsxOptions.Proxy = options.Proxy

	// 处理解析器设置
	if options.Resolvers != "" {
		dnsxOptions.BaseResolvers = []string{}
		// 如果是文件，从文件加载解析器
		if fileutil.FileExists(options.Resolvers) {
			rs, err := linesInFile(options.Resolvers)
			if err != nil {
				gologger.Fatal().Msgf("%s\n", err)
			}
			for _, rr := range rs {
				dnsxOptions.BaseResolvers = append(dnsxOptions.BaseResolvers, prepareResolver(rr))
			}
		} else {
			// 否则处理逗号分隔的解析器列表
			for _, rr := range strings.Split(options.Resolvers, ",") {
				dnsxOptions.BaseResolvers = append(dnsxOptions.BaseResolvers, prepareResolver(rr))
			}
		}
	}

	// 根据选项配置DNS查询类型
	var questionTypes []uint16
	if options.A {
		questionTypes = append(questionTypes, dns.TypeA)
	}
	if options.AAAA {
		questionTypes = append(questionTypes, dns.TypeAAAA)
	}
	if options.CNAME {
		questionTypes = append(questionTypes, dns.TypeCNAME)
	}
	if options.PTR {
		questionTypes = append(questionTypes, dns.TypePTR)
	}
	if options.SOA {
		questionTypes = append(questionTypes, dns.TypeSOA)
	}
	if options.ANY {
		questionTypes = append(questionTypes, dns.TypeANY)
	}
	if options.TXT {
		questionTypes = append(questionTypes, dns.TypeTXT)
	}
	if options.SRV {
		questionTypes = append(questionTypes, dns.TypeSRV)
	}
	if options.MX {
		questionTypes = append(questionTypes, dns.TypeMX)
	}
	if options.NS {
		questionTypes = append(questionTypes, dns.TypeNS)
	}
	if options.CAA {
		questionTypes = append(questionTypes, dns.TypeCAA)
	}

	// 如果没有指定查询类型或需要通配符过滤，默认使用A记录
	if len(questionTypes) == 0 || options.WildcardDomain != "" {
		options.A = true
		questionTypes = append(questionTypes, dns.TypeA)
	}
	dnsxOptions.QuestionTypes = questionTypes
	dnsxOptions.QueryAll = options.QueryAll

	// 创建DNS客户端
	dnsX, err := dnsx.New(dnsxOptions)
	if err != nil {
		return nil, err
	}

	// 配置速率限制
	limiter := ratelimit.NewUnlimited(context.Background())
	if options.RateLimit > 0 {
		limiter = ratelimit.New(context.Background(), uint(options.RateLimit), time.Second)
	}

	// 创建混合映射用于存储结果
	hm, err := hybrid.New(hybrid.DefaultDiskOptions)
	if err != nil {
		return nil, err
	}

	// 如果需要显示统计信息，创建统计客户端
	var stats clistats.StatisticsClient
	if options.ShowStatistics {
		stats, err = clistats.New()
		if err != nil {
			return nil, err
		}
	}

	// 检查是否禁用颜色输出
	if os.Getenv("NO_COLOR") == "true" {
		options.NoColor = true
	}

	// 创建并返回Runner实例
	r := Runner{
		options:            options,
		dnsx:               dnsX,
		wgoutputworker:     &sync.WaitGroup{},
		wgresolveworkers:   &sync.WaitGroup{},
		wgwildcardworker:   &sync.WaitGroup{},
		workerchan:         make(chan string),
		wildcardworkerchan: make(chan string),
		wildcards:          make(map[string]struct{}),
		wildcardscache:     make(map[string][]string),
		limiter:            limiter,
		hm:                 hm,
		stats:              stats,
		aurora:             aurora.NewAurora(!options.NoColor),
	}

	return &r, nil
}

// InputWorkerStream  处理流模式下的输入
// 从文件或标准输入读取域名或IP地址，并发送到工作通道
// @receiver r
func (r *Runner) InputWorkerStream() {
	var sc *bufio.Scanner
	// 尝试从文件加载列表
	if fileutil.FileExists(r.options.Hosts) {
		f, _ := os.Open(r.options.Hosts)
		sc = bufio.NewScanner(f)
	} else if fileutil.HasStdin() {
		sc = bufio.NewScanner(os.Stdin)
	}

	for sc.Scan() {
		item := strings.TrimSpace(sc.Text())
		switch {
		case iputil.IsCIDR(item):
			// 如果是CIDR，展开IP地址
			hostsC, _ := cidr.IPAddressesAsStream(item)
			for host := range hostsC {
				r.workerchan <- host
			}
		default:
			// 处理普通主机名
			r.workerchan <- item
		}
	}
	// 处理完关闭通道
	close(r.workerchan)
}

// InputWorker
// @receiver r
func (r *Runner) InputWorker() {
	// 扫描混合映射中的所有条目
	r.hm.Scan(func(k, _ []byte) error {
		// 如果显示统计信息，增加请求计数
		if r.options.ShowStatistics {
			r.stats.IncrementCounter("requests", len(r.dnsx.Options.QuestionTypes))
		}
		item := string(k)
		if r.options.resumeCfg != nil {
			// 如果需要从断点恢复，处理恢复逻辑
			r.options.resumeCfg.current = item
			r.options.resumeCfg.currentIndex++
			if r.options.resumeCfg.currentIndex <= r.options.resumeCfg.Index {
				return nil
			}
		}
		r.workerchan <- item
		return nil
	})
	close(r.workerchan)
}

// prepareInput 准备输入数据
// 处理各种输入源(文件、标准输入、域名、单词列表等)并将其加载到混合映射中
// @receiver r
// @return error error: 如果处理输入过程中出错，返回相应的错误
func (r *Runner) prepareInput() error {
	var (
		dataDomains chan string // 域名数据通道
		sc          chan string // 源数据通道
		err         error       // 错误对象
	)

	// 检查标准输入并进行处理
	// 如果用户从标准输入传入数据，先将其复制到临时文件，以便多次读取
	hasStdin := fileutil.HasStdin()
	if hasStdin {
		// 创建临时文件
		tmpStdinFile, err := fileutil.GetTempFileName()
		if err != nil {
			return err
		}
		r.tmpStdinFile = tmpStdinFile

		stdinFile, err := os.Create(r.tmpStdinFile)
		if err != nil {
			return err
		}
		// 将标准输入数据复制到临时文件
		if _, err := io.Copy(stdinFile, os.Stdin); err != nil {
			return err
		}
		// 关闭文件，因为我们将多次读取它来构建迭代
		stdinFile.Close()
		defer os.RemoveAll(r.tmpStdinFile)
	}

	// 处理域名参数 - 如果提供了域名，预处理它们
	if r.options.Domains != "" {
		dataDomains, err = r.preProcessArgument(r.options.Domains)
		if err != nil {
			return err
		}
		sc = dataDomains
	}

	// 如果没有域名源数据通道，尝试从主机列表或标准输入加载
	if sc == nil {
		// 尝试从文件加载列表
		if fileutil.FileExists(r.options.Hosts) {
			f, err := fileutil.ReadFile(r.options.Hosts)
			if err != nil {
				return err
			}
			sc = f
		} else if argumentHasStdin(r.options.Hosts) || hasStdin {
			// 如果主机参数是stdin标记或检测到有stdin输入
			// 读取之前保存的临时标准输入文件
			sc, err = fileutil.ReadFile(r.tmpStdinFile)
			if err != nil {
				return err
			}
		} else {
			return errors.New("hosts file or stdin not provided")
		}
	}

	// 开始处理输入项，计算主机数量
	numHosts := 0
	for item := range sc {
		// 标准化输入项(去除前后空白)
		item := normalize(item)
		var hosts []string
		switch {
		case strings.Contains(item, "FUZZ"):
			// 处理包含FUZZ占位符的情况 - 用单词列表中的词替换FUZZ
			// 获取单词列表
			fuzz, err := r.preProcessArgument(r.options.WordList)
			if err != nil {
				return err
			}
			for r := range fuzz {
				subdomain := strings.ReplaceAll(item, "FUZZ", r)
				hosts = append(hosts, subdomain)
			}
			// 将生成的主机名添加到映射中，并累计主机数量
			numHosts += r.addHostsToHMapFromList(hosts)
		case r.options.WordList != "":
			// 使用单词列表生成子域名(单词列表 + 域名)
			// 获取单词列表(前缀)
			prefixes, err := r.preProcessArgument(r.options.WordList)
			if err != nil {
				return err
			}
			for prefix := range prefixes {
				// 单词列表与域名的笛卡尔积 - 每个前缀与每个域名组合
				subdomain := strings.TrimSpace(prefix) + "." + item
				hosts = append(hosts, subdomain)
			}
			// 将生成的主机名添加到映射中，并累计主机数量
			numHosts += r.addHostsToHMapFromList(hosts)
		case iputil.IsCIDR(item):
			// 处理CIDR格式的输入(IP地址范围)
			// 将CIDR扩展为所有包含的IP地址
			hostC, err := cidr.IPAddressesAsStream(item)
			if err != nil {
				return err
			}
			// 将生成的IP地址添加到映射中，并累计主机数量
			numHosts += r.addHostsToHMapFromChan(hostC)
		default:
			// 处理普通主机名 - 直接作为一个项处理
			hosts = []string{item}
			numHosts += r.addHostsToHMapFromList(hosts)
		}
	}
	// 如果需要显示统计信息，设置统计数据
	if r.options.ShowStatistics {
		// 总主机数
		r.stats.AddStatic("hosts", numHosts)
		// 开始时间
		r.stats.AddStatic("startedAt", time.Now())
		// 请求计数器，初始为0
		r.stats.AddCounter("requests", 0)
		// 总请求数 = 主机数 × 查询类型数
		r.stats.AddCounter("total", uint64(numHosts*len(r.dnsx.Options.QuestionTypes)))

		// 添加动态统计信息回调
		r.stats.AddDynamic("summary", makePrintCallback())
		// nolint:errcheck
		// 启动统计客户端
		r.stats.Start()

		// 设置定期获取统计信息的回调
		r.stats.GetStatResponse(time.Second*5, func(s string, err error) error {
			// 如果有错误且处于详细模式，输出错误信息
			if err != nil && r.options.Verbose {
				gologger.Error().Msgf("Could not read statistics: %s\n", err)
			}
			return nil
		})
	}
	return nil
}

// addHostsToHMapFromChan 从通道添加主机到混合映射
// @receiver r
// @param hosts chan string:  主机名通道
// @return numHosts int: 添加的主机数量
func (r *Runner) addHostsToHMapFromChan(hosts chan string) (numHosts int) {
	for host := range hosts {
		// 仅用于获取目标的确切数量
		if _, ok := r.hm.Get(host); ok {
			continue
		}
		numHosts++
		// nolint:errcheck
		r.hm.Set(host, nil)
	}
	return
}

// addHostsToHMapFromList 从列表添加主机到混合映射
// @receiver r
// @param hosts []string:  主机名列表
// @return numHosts int: 添加的主机数量
func (r *Runner) addHostsToHMapFromList(hosts []string) (numHosts int) {
	for _, host := range hosts {
		// 仅用于获取目标的确切数量
		if _, ok := r.hm.Get(host); ok {
			continue
		}
		numHosts++
		// nolint:errcheck
		r.hm.Set(host, nil)
	}
	return
}

// preProcessArgument 预处理参数，从文件、标准输入或内联内容读取数据
// @receiver r
// @param arg string:  要处理的参数（文件路径、标准输入标记或直接内容）
// @return chan string: 处理后的内容通道
// @return error error: 可能的錯誤
func (r *Runner) preProcessArgument(arg string) (chan string, error) {
	// 读取来源:
	// 文件
	switch {
	case fileutil.FileExists(arg):
		return fileutil.ReadFile(arg)
	// 标准输入
	case argumentHasStdin(arg):
		return fileutil.ReadFile(r.tmpStdinFile)
	// 内联内容
	case arg != "":
		data := strings.ReplaceAll(arg, Comma, NewLine)
		return fileutil.ReadFileWithReader(strings.NewReader(data))
	default:
		return nil, errors.New("empty argument")
	}
}

func normalize(data string) string {
	return strings.TrimSpace(data)
}

// makePrintCallback 创建一个回调函数，用于打印统计信息
// @return stats clistats.StatisticsClient:
// @return func(stats clistats.StatisticsClient) interface{} func(stats clistats.StatisticsClient) interface{}:
func makePrintCallback() func(stats clistats.StatisticsClient) interface{} {
	builder := &strings.Builder{}
	return func(stats clistats.StatisticsClient) interface{} {
		builder.WriteRune('[')
		startedAt, _ := stats.GetStatic("startedAt")
		duration := time.Since(startedAt.(time.Time))
		builder.WriteString(fmtDuration(duration))
		builder.WriteRune(']')

		hosts, _ := stats.GetStatic("hosts")
		builder.WriteString(" | Hosts: ")
		builder.WriteString(clistats.String(hosts))

		requests, _ := stats.GetCounter("requests")
		total, _ := stats.GetCounter("total")

		builder.WriteString(" | RPS: ")
		builder.WriteString(clistats.String(uint64(float64(requests) / duration.Seconds())))

		builder.WriteString(" | Requests: ")
		builder.WriteString(clistats.String(requests))
		builder.WriteRune('/')
		builder.WriteString(clistats.String(total))
		builder.WriteRune(' ')
		builder.WriteRune('(')
		//nolint:gomnd // this is not a magic number
		builder.WriteString(clistats.String(uint64(float64(requests) / float64(total) * 100.0)))
		builder.WriteRune('%')
		builder.WriteRune(')')
		builder.WriteRune('\n')

		fmt.Fprintf(os.Stderr, "%s", builder.String())
		statString := builder.String()
		builder.Reset()
		return statString
	}
}

// SaveResumeConfig 保存恢复配置到文件
// @receiver r
// @return error error:
func (r *Runner) SaveResumeConfig() error {
	var resumeCfg ResumeCfg
	resumeCfg.Index = r.options.resumeCfg.currentIndex
	resumeCfg.ResumeFrom = r.options.resumeCfg.current
	return utils.Save(resumeCfg, DefaultResumeFile)
}

// Run 运行DNS查询任务
// @receiver r
// @return error error: 如果运行过程中出错，返回相应的错误
func (r *Runner) Run() error {
	// 如果是流模式，使用流处理
	if r.options.Stream {
		return r.runStream()
	}

	return r.run()
}

// run 执行常规DNS查询处理
// @receiver r
// @return error error: 可能的錯誤
func (r *Runner) run() error {
	// 准备输入数据 - 处理各种输入源并将其加载到混合映射中
	err := r.prepareInput()
	if err != nil {
		return err
	}

	// 如果用户启用了从断点恢复功能并且存在有效的恢复位置，通知用户
	if r.options.ShouldLoadResume() && r.options.resumeCfg.Index > 0 {
		gologger.Debug().Msgf("Resuming scan using file %s. Restarting at position %d: %s\n", DefaultResumeFile, r.options.resumeCfg.Index, r.options.resumeCfg.ResumeFrom)
	}

	// 启动工作器池 - 包括输入工作器、输出工作器和解析工作器
	r.startWorkers()

	// 等待所有解析工作器处理完队列中的所有域名
	r.wgresolveworkers.Wait()

	// 如果启用了统计功能，停止统计客户端
	if r.stats != nil {
		err = r.stats.Stop()
		if err != nil {
			return err
		}
	}

	// 关闭输出通道 - 标记没有更多数据会被发送
	close(r.outputchan)
	// 等待输出工作器处理完所有已发送的数据
	r.wgoutputworker.Wait()

	// 通配符域名处理部分 - 仅在指定了通配符域名时执行
	if r.options.WildcardDomain != "" {
		gologger.Print().Msgf("Starting to filter wildcard subdomains\n")
		// 创建IP到域名的映射 - 用于识别同一IP对应多个域名的情况(可能是通配符)
		ipDomain := make(map[string]map[string]struct{})
		listIPs := []string{}

		// 遍历之前存储的所有DNS查询结果，构建IP到域名的映射关系
		r.hm.Scan(func(k, v []byte) error {
			var dnsdata retryabledns.DNSData

			// 反序列化DNS数据
			err := dnsdata.Unmarshal(v)
			if err != nil {
				// 如果该项目没有有效DNS记录，直接跳过
				return nil
			}

			// 处理所有A记录(IP地址)，构建映射关系
			for _, a := range dnsdata.A {
				_, ok := ipDomain[a]
				if !ok {
					// 如果是新IP，初始化域名集合并添加到IP列表
					ipDomain[a] = make(map[string]struct{})
					listIPs = append(listIPs, a)
				}
				ipDomain[a][string(k)] = struct{}{}
			}

			return nil
		})

		// 设置通配符检测工作器数量 - 不超过IP数量
		numThreads := r.options.Threads
		if numThreads > len(listIPs) {
			numThreads = len(listIPs)
		}

		// 启动通配符检测工作器
		for i := 0; i < numThreads; i++ {
			r.wgwildcardworker.Add(1)
			go r.wildcardWorker()
		}

		// 处理可能是通配符的域名 - 当一个IP对应多个域名超过阈值时，检查这些域名
		seen := make(map[string]struct{})
		for _, a := range listIPs {
			hosts := ipDomain[a]
			// 如果IP对应的域名数量超过设定的通配符阈值，进一步检查
			if len(hosts) >= r.options.WildcardThreshold {
				// 遍历所有相关域名，放入通配符检测队列
				for host := range hosts {
					// 确保每个域名只处理一次
					if _, ok := seen[host]; !ok {
						// 标记为已处理
						seen[host] = struct{}{}
						r.wildcardworkerchan <- host
					}
				}
			}
		}
		// 关闭通配符工作通道，表示没有更多域名需要检查
		close(r.wildcardworkerchan)
		r.wgwildcardworker.Wait()

		// 重新启动输出工作器以输出最终结果
		r.startOutputWorker()
		seen = make(map[string]struct{})                   // 重置已处理域名跟踪
		seenRemovedSubdomains := make(map[string]struct{}) // 用于跟踪被移除的通配符子域名
		numRemovedSubdomains := 0                          // 记录被移除的通配符子域名数量

		for _, A := range listIPs {
			for host := range ipDomain[A] {
				if host == r.options.WildcardDomain {
					// 如果是通配符域名本身，直接输出(确保只输出一次)
					if _, ok := seen[host]; !ok {
						seen[host] = struct{}{}
						// 查询并输出域名信息
						_ = r.lookupAndOutput(host)
					}
				} else if _, ok := r.wildcards[host]; !ok {
					// 如果不是被标记为通配符的域名，则输出(确保只输出一次)
					if _, ok := seen[host]; !ok {
						seen[host] = struct{}{}
						_ = r.lookupAndOutput(host)
					}
				} else {
					// 如果是被标记为通配符的域名，统计但不输出
					if _, ok := seenRemovedSubdomains[host]; !ok {
						numRemovedSubdomains++
						seenRemovedSubdomains[host] = struct{}{}
					}
				}
			}
		}
		close(r.outputchan)
		// waiting output worker
		r.wgoutputworker.Wait()
		gologger.Print().Msgf("%d wildcard subdomains removed\n", numRemovedSubdomains)
	}

	return nil
}

// lookupAndOutput 查询主机并输出结果
// @receiver r
// @param host string: 要查询的主机名
// @return error error:
func (r *Runner) lookupAndOutput(host string) error {
	// 处理JSON格式输出
	if r.options.JSON {
		if data, ok := r.hm.Get(host); ok {
			var dnsData retryabledns.DNSData
			err := dnsData.Unmarshal(data)
			if err != nil {
				return err
			}
			dnsDataJson, err := dnsData.JSON()
			if err != nil {
				return err
			}
			r.outputchan <- dnsDataJson
			return err
		}
	}

	r.outputchan <- host
	return nil
}

// runStream 运行流模式处理
// @receiver r
// @return error error:
func (r *Runner) runStream() error {
	// 启动工作器
	r.startWorkers()

	// 等待所有解析工作器完成
	r.wgresolveworkers.Wait()

	close(r.outputchan)
	r.wgoutputworker.Wait()

	return nil
}

// HandleOutput 处理输出结果
// 将结果写入到文件（如果指定）和标准输出
// @receiver r
func (r *Runner) HandleOutput() {
	// 在函数结束时通知等待组，表示输出工作器已完成
	defer r.wgoutputworker.Done()

	// setup output
	var (
		foutput *os.File      // 输出文件句柄
		w       *bufio.Writer // 带缓冲的写入器
	)
	// 如果指定了输出文件，打开或创建该文件
	if r.options.OutputFile != "" {
		var err error
		// 以追加、创建、仅写模式打开文件，权限设为0644
		foutput, err = os.OpenFile(r.options.OutputFile, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
		if err != nil {
			gologger.Fatal().Msgf("%s\n", err)
		}
		defer foutput.Close()
		w = bufio.NewWriter(foutput)
		// 确保在函数结束时刷新缓冲内容到文件
		defer w.Flush()
	}
	// 从输出通道不断读取结果项，直到通道关闭
	for item := range r.outputchan {
		// 如果设置了输出文件，将结果写入文件
		if foutput != nil {
			// 使用缓冲区写入文件，每项后添加换行符
			_, _ = w.WriteString(item + "\n")
		}
		// 同时将结果写入标准输出（控制台）
		// 使用Silent级别确保即使在安静模式下也会输出
		gologger.Silent().Msgf("%s\n", item)
	}
}

// startOutputWorker 启动输出处理工作器
// @receiver r
func (r *Runner) startOutputWorker() {
	// 创建一个新的输出通道
	// 这个通道用于传递需要输出的DNS查询结果
	r.outputchan = make(chan string)
	// 增加输出等待组计数，表示有一个输出工作器在运行
	r.wgoutputworker.Add(1)
	// 启动输出处理函数作为工作器
	go r.HandleOutput()
}

// startWorkers  启动所有工作器（输入处理工作器、输出工作器和解析工作器）
// @receiver r
func (r *Runner) startWorkers() {
	if r.options.Stream {
		// 流模式：实时处理输入，适用于管道输入或实时数据流
		go r.InputWorkerStream()
	} else {
		// 批处理模式：一次性加载所有输入，适用于文件或固定列表输入
		go r.InputWorker()
	}

	// 启动输出处理工作器，用于处理结果输出
	r.startOutputWorker()

	// 启动多个DNS解析工作器，数量由Threads选项决定
	// 这些工作器将并行处理DNS查询任务+
	for i := 0; i < r.options.Threads; i++ {
		// 增加等待组计数，以便后续等待所有工作器完成
		r.wgresolveworkers.Add(1)
		// 启动一个DNS解析工作器
		go r.worker()
	}
}

// worker  DNS解析工作器
// 从工作通道获取主机名，进行DNS查询，并处理结果
// @receiver r
func (r *Runner) worker() {
	// 在工作器结束时通知等待组完成一个任务
	defer r.wgresolveworkers.Done()

	// 不断从工作通道获取域名，直到通道关闭
	for domain := range r.workerchan {
		// 如果输入的是URL而不是域名，提取其中的域名部分
		if isURL(domain) {
			domain = extractDomain(domain)
		}
		// 应用速率限制 - 防止查询过快导致DNS服务器拒绝服务
		r.limiter.Take()
		// 创建响应数据结构用于存储查询结果
		dnsData := dnsx.ResponseData{}

		// 执行DNS查询，获取多种记录类型的结果
		// 忽略错误，因为即使部分查询失败，我们仍然可以使用成功的查询结果
		dnsData.DNSData, _ = r.dnsx.QueryMultiple(domain)
		// 如果整个查询都失败返回了nil，则跳过处理
		if dnsData.DNSData == nil {
			continue
		}

		// 验证DNS响应数据的完整性 - 检查主机名和时间戳
		if dnsData.Host == "" || dnsData.Timestamp.IsZero() {
			continue
		}

		// 响应码过滤处理
		// hosts文件中的结果始终返回(因为它们不需要经过DNS服务器)
		if !dnsData.HostsFile {
			// 仅接受用户指定响应码的结果
			if len(r.options.rcodes) > 0 {
				// 如果定义了响应码过滤，检查当前响应码是否在允许列表中
				if _, ok := r.options.rcodes[dnsData.StatusCodeRaw]; !ok {
					continue
				}
			}
		}

		// 根据用户配置决定是否保留原始DNS响应内容
		if !r.options.Raw {
			dnsData.Raw = ""
		}

		// 处理DNS追踪功能 - 如果启用了追踪选项
		if r.options.Trace {
			// 执行DNS追踪并保存结果
			dnsData.TraceData, _ = r.dnsx.Trace(domain)
			if dnsData.TraceData != nil {
				// 遍历所有追踪结果，处理每个节点的数据
				for _, data := range dnsData.TraceData.DNSData {
					// 如果需要原始响应且存在原始响应数据
					if r.options.Raw && data.RawResp != nil {
						// 将原始响应转换为字符串
						rawRespString := data.RawResp.String()
						data.Raw = rawRespString
						// 将当前节点的响应追加到整个追踪链的原始数据中
						dnsData.Raw += fmt.Sprintln(rawRespString)
					}
					data.RawResp = nil
				}
			}
		}

		// 处理区域传送(AXFR)查询 - 尝试获取整个区域的DNS记录
		if r.options.AXFR {
			hasAxfrData := false
			// 执行AXFR查询
			axfrData, _ := r.dnsx.AXFR(domain)
			if axfrData != nil {
				// 保存AXFR数据到结果中
				dnsData.AXFRData = axfrData
				hasAxfrData = len(axfrData.DNSData) > 0
			}

			// 如果仅查询AXFR类型且没有AXFR数据，并且不是JSON输出模式
			// 则跳过输出(避免输出没有实际数据的结果)
			if len(r.dnsx.Options.QuestionTypes) == 1 && !hasAxfrData && !r.options.JSON {
				continue
			}
		}

		// 通配符域名处理 - 如果启用了通配符过滤
		if r.options.WildcardDomain != "" {
			// 只存储DNS数据，不立即输出
			// 后续会在通配符过滤阶段一并处理
			_ = r.storeDNSData(dnsData.DNSData)
			continue
		}

		// 处理JSON格式输出 - 如果用户请求JSON格式
		if r.options.JSON {
			var marshalOptions []dnsx.MarshalOption
			// 如果需要忽略原始响应，添加相应选项
			if r.options.OmitRaw {
				marshalOptions = append(marshalOptions, dnsx.WithoutAllRecords())
			}
			// 将DNS数据转换为JSON字符串
			jsons, _ := dnsData.JSON(marshalOptions...)
			r.outputchan <- jsons
			continue
		}
		// 处理原始输出模式 - 直接输出原始DNS响应
		if r.options.Raw {
			r.outputchan <- dnsData.Raw
			continue
		}
		// 处理响应码输出模式 - 只输出域名和对应的DNS响应码
		if r.options.hasRCodes {
			r.outputResponseCode(domain, dnsData.StatusCodeRaw)
			continue
		}

		// 根据查询类型输出不同类型的DNS记录
		// 只输出用户指定了相应选项的记录类型
		if r.options.A {
			r.outputRecordType(domain, dnsData.A, "A")
		}
		if r.options.AAAA {
			r.outputRecordType(domain, dnsData.AAAA, "AAAA")
		}
		if r.options.CNAME {
			r.outputRecordType(domain, dnsData.CNAME, "CNAME")
		}
		if r.options.PTR {
			r.outputRecordType(domain, dnsData.PTR, "PTR")
		}
		if r.options.MX {
			r.outputRecordType(domain, dnsData.MX, "MX")
		}
		if r.options.NS {
			r.outputRecordType(domain, dnsData.NS, "NS")
		}
		if r.options.SOA {
			r.outputRecordType(domain, sliceutil.Dedupe(dnsData.GetSOARecords()), "SOA")
		}
		if r.options.ANY {
			// 输出所有类型的记录 - 合并所有查询到的记录结果
			allParsedRecords := sliceutil.Merge(
				dnsData.A,
				dnsData.AAAA,
				dnsData.CNAME,
				dnsData.MX,
				dnsData.PTR,
				sliceutil.Dedupe(dnsData.GetSOARecords()),
				dnsData.NS,
				dnsData.TXT,
				dnsData.SRV,
				dnsData.CAA,
			)
			r.outputRecordType(domain, allParsedRecords, "ANY")
		}
		if r.options.TXT {
			r.outputRecordType(domain, dnsData.TXT, "TXT")
		}
		if r.options.SRV {
			r.outputRecordType(domain, dnsData.SRV, "SRV")
		}
		if r.options.CAA {
			r.outputRecordType(domain, dnsData.CAA, "CAA")
		}
	}
}

// outputRecordType 根据记录类型输出DNS记录
// @receiver r
// @param domain string: 域名
// @param items interface{}: 记录值
// @param queryType string:  查询类型
func (r *Runner) outputRecordType(domain string, items interface{}, queryType string) {
	var details string

	var records []string

	// 根据输入项类型处理不同格式的记录值
	switch items := items.(type) {
	case []string:
		// 如果输入是字符串切片(大多数记录类型)，直接使用
		records = items
	case []retryabledns.SOA:
		// 如果输入是SOA记录数组，提取NS和Mbox字段
		for _, item := range items {
			records = append(records, item.NS, item.Mbox)
		}
	}

	// 遍历所有记录，处理并输出每条记录
	for _, item := range records {
		// 将记录值转换为小写，保持输出一致性
		item := strings.ToLower(item)

		if r.options.ResponseOnly {
			// 仅响应模式 - 只输出记录值和详细信息
			// 格式: 记录值
			r.outputchan <- fmt.Sprintf("%s%s", item, details)
		} else if r.options.Response {
			// 响应模式 - 输出完整信息，包括域名、查询类型、记录值和详细信息
			// 格式: 域名 [查询类型] [记录值]
			// 其中查询类型用紫色高亮，记录值用绿色高亮(如果启用了颜色)
			r.outputchan <- fmt.Sprintf("%s [%s] [%s] %s", domain,
				r.aurora.Magenta(queryType), r.aurora.Green(item).String(), details)
		} else {
			// 默认模式 - 仅输出域名和详细信息，不包含记录值和查询类型
			// 格式: 域名
			r.outputchan <- fmt.Sprintf("%s%s", domain, details)
			break
		}
	}
}

// outputResponseCode 输出域名的响应码
// @receiver r
// @param domain string:  域名
// @param responsecode int: DNS响应码
func (r *Runner) outputResponseCode(domain string, responsecode int) {
	responseCodeExt, ok := dns.RcodeToString[responsecode]
	if ok {
		r.outputchan <- domain + " [" + responseCodeExt + "]"
	}
}

// storeDNSData 将DNS数据存储到混合映射中
// @receiver r
// @param dnsdata *retryabledns.DNSData: DNS数据
// @return error error:
func (r *Runner) storeDNSData(dnsdata *retryabledns.DNSData) error {
	data, err := dnsdata.Marshal()
	if err != nil {
		return err
	}
	return r.hm.Set(dnsdata.Host, data)
}

// Close 关闭运行实例
func (r *Runner) Close() {
	r.hm.Close()
}

// wildcardWorker 通配符检测工作器
// 检查域名是否为通配符域名并标记
// @receiver r
func (r *Runner) wildcardWorker() {
	// 当工作器结束时通知通配符等待组完成一个任务
	defer r.wgwildcardworker.Done()

	for {
		// 从通道获取一个主机名和通道状态
		host, more := <-r.wildcardworkerchan
		// 如果通道已关闭(more为false)，结束工作循环
		if !more {
			break
		}

		// 调用IsWildcard方法检查当前主机名是否是通配符域名
		// 通配符域名是解析到相同IP的多个随机子域名，如*.example.com
		if r.IsWildcard(host) {
			// 如果确认是通配符域名，将其添加到通配符映射中
			// 需要加锁以保证线程安全
			r.wildcardsmutex.Lock()
			r.wildcards[host] = struct{}{}
			r.wildcardsmutex.Unlock()
		}
	}
}
