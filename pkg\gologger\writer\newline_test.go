package writer

import (
	"testing"
)


func mockIsWindows(isWindows bool) {
	if isWindows {
		NewLine = "\r\n"
	} else {
		NewLine = "\n"
	}
}


func TestNewLine(t *testing.T) {
	// Test for Windows
	mockIsWindows(true)
	if NewLine != "\r\n" {
		t.<PERSON><PERSON><PERSON>("Expected NewLine to be '\\r\\n', got '%s'", NewLine)
	}

	// Test for non-Windows (e.g., Linux or macOS)
	mockIsWindows(false)
	if NewLine != "\n" {
		t.<PERSON><PERSON>rf("Expected NewLine to be '\\n', got '%s'", NewLine)
	}
}