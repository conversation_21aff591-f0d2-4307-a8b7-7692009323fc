package goflags

import (
	sliceutil "yaml_scan/utils/slice"
	"github.com/pkg/errors"
)

// StringSlice 是一个字符串切片类型
type StringSlice []string

var (
	optionMap           map[*StringSlice]Options   // 存储 StringSlice 指针与其对应的选项
	optionDefaultValues map[*StringSlice][]string    // 存储 StringSlice 指针与其默认值的映射
)

func init() {
	optionMap = make(map[*StringSlice]Options)
	optionDefaultValues = make(map[*StringSlice][]string)
}


// Set 方法将一个值追加到字符串切片中。
// 如果提供的新值与默认值相同，则会清空切片。
//
// value: 要添加到切片的字符串值。
// 返回值: 如果成功，返回 nil；如果发生错误，返回相应的错误。
func (stringSlice *StringSlice) Set(value string) error {
	// 检查输入值是否为空
	if value == "" {
		return errors.New("value cannot be empty") // 返回错误
	}

	// 从 optionMap 中获取与当前 StringSlice 关联的选项。
	option, ok := optionMap[stringSlice]
	if !ok {
		// 如果没有找到选项，则使用默认选项。
		option = StringSliceOptions
	}
	// 使用 ToStringSlice 函数将输入值转换为字符串切片。
	values, err := ToStringSlice(value, option)
	if err != nil {
		return err
	}
	// 如果提供的新值与默认值相同，则清空切片。
	if defaultValue, ok := optionDefaultValues[stringSlice]; ok {
		if sliceutil.Equal(*stringSlice, defaultValue) {
			*stringSlice = []string{}
		}
	}
	// 将新值追加到切片中。
	*stringSlice = append(*stringSlice, values...)
	return nil
}


func (stringSlice StringSlice) String() string {
	return ToString(stringSlice)
}