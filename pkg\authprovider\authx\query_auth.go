// Author: chenjb
// Version: V1.0
// Date: 2025-06-13 14:58:45
// FilePath: /yaml_scan/pkg/authprovider/authx/query_auth.go
// Description:实现了查询参数认证策略，用于通过URL查询参数进行认证	
package authx

import (
	"net/http"
	"yaml_scan/pkg/retryablehttp"
	urlutil "yaml_scan/utils/url"
)


var (
	// 确保QueryAuthStrategy实现了AuthStrategy接口
	_ AuthStrategy = &QueryAuthStrategy{}
)

// QueryAuthStrategy 是查询参数认证策略的实现
type QueryAuthStrategy struct {
	Data *Secret
}

// NewQueryAuthStrategy 创建一个新的查询参数认证策略
// @param data *Secret: 
// @return *QueryAuthStrategy *QueryAuthStrategy: 
func NewQueryAuthStrategy(data *Secret) *QueryAuthStrategy {
	return &QueryAuthStrategy{Data: data}
}

// Apply 将查询参数认证策略应用到HTTP请求上
// 它会使用密钥中的查询参数键值对设置URL查询参数
// @receiver s 
// @param req *http.Request: 
func (s *QueryAuthStrategy) Apply(req *http.Request) {
	// 创建有序参数对象并解析当前请求的查询参数
	q := urlutil.NewOrderedParams()
	q.Decode(req.URL.RawQuery)
	// 添加密钥中的所有查询参数
	for _, p := range s.Data.Params {
		q.Add(p.Key, p.Value)
	}
	// 将处理后的查询参数重新编码并设置到请求URL中
	req.URL.RawQuery = q.Encode()
}

// ApplyOnRR 将查询参数认证策略应用到可重试的HTTP请求上
// @receiver s 
// @param req *retryablehttp.Request: 
func (s *QueryAuthStrategy) ApplyOnRR(req *retryablehttp.Request) {
	q := urlutil.NewOrderedParams()
	q.Decode(req.Request.URL.RawQuery)
	for _, p := range s.Data.Params {
		q.Add(p.Key, p.Value)
	}
	req.Request.URL.RawQuery = q.Encode()
}