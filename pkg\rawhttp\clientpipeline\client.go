//
// Author: chenjb
// Version: V1.0
// Date: 2025-07-22 19:19:32
// FilePath: /yaml_scan/pkg/rawhttp/clientpipeline/client.go
// Description: HTTP管道客户端实现，支持连接复用和请求管道化以提高性能

// Package clientpipeline 提供HTTP管道客户端功能
// 支持连接池管理、请求管道化和高并发HTTP通信
package clientpipeline

import (
	"bufio"      // 缓冲I/O包
	"crypto/tls" // TLS加密包
	"errors"     // 错误处理包
	"net"        // 网络包
	"strconv"    // 字符串转换包
	"strings"    // 字符串处理包
	"sync"       // 同步原语包
	"time"       // 时间处理包
)

// 默认配置常量
const DefaultMaxConnsPerHost = 512                        // 每个主机的默认最大连接数
const DefaultMaxIdleConnDuration = 10 * time.Second       // 默认最大空闲连接持续时间
const DefaultMaxIdemponentCallAttempts = 5                // 默认最大幂等调用尝试次数
const DefaultDialTimeout = 3 * time.Second                // 默认拨号超时时间（3秒）
const defaultReadBufferSize = 4096                        // 默认读取缓冲区大小（4KB）
const defaultWriteBufferSize = 4096                       // 默认写入缓冲区大小（4KB）

// DialFunc 拨号函数类型定义
// 参数:
//   addr: 目标地址
// 返回:
//   net.Conn: 建立的网络连接
//   error: 连接错误，成功时为nil
type DialFunc func(addr string) (net.Conn, error)

// RetryIfFunc 重试条件判断函数类型定义
// 参数:
//   request: 要判断的请求对象
// 返回:
//   bool: 如果应该重试则返回true，否则返回false
type RetryIfFunc func(request *Request) bool

// 预定义错误变量
var (
	// ErrNoFreeConns 当没有可用的空闲连接时返回的错误
	ErrNoFreeConns = errors.New("no free connections available to host")

	// ErrConnectionClosed 当服务器在返回第一个响应字节之前关闭连接时返回的错误
	// 确保服务器在关闭连接前返回'Connection: close'响应头部
	ErrConnectionClosed = errors.New("the server closed connection before returning the first response byte. " +
		"Make sure the server returns 'Connection: close' response header before closing the connection")

	// ErrGetOnly 当服务器只期望GET请求但收到其他类型请求时返回的错误
	// (Server.GetOnly选项为true时)
	ErrGetOnly = errors.New("non-GET request received")
)

// timeoutError 超时错误类型
// 实现了net.Error接口，用于表示超时相关的错误
type timeoutError struct {
}

// Error 实现error接口，返回超时错误的描述
// 返回:
//   string: 错误描述字符串
func (e *timeoutError) Error() string {
	return "timeout"
}

// Timeout 实现net.Error接口，指示这是一个超时错误
// 返回:
//   bool: 始终返回true，表示这是超时错误
func (e *timeoutError) Timeout() bool {
	return true
}

// Temporary 实现net.Error接口，指示这是否是临时错误
// 返回:
//   bool: 始终返回true，表示超时错误通常是临时的，可以重试
func (e *timeoutError) Temporary() bool {
	return true
}

// 预定义的超时错误实例
var (
	ErrTimeout = &timeoutError{} // 全局超时错误实例
)

// newClientTLSConfig 创建新的客户端TLS配置
// 参数:
//   c: 基础TLS配置，可为nil
//   addr: 目标服务器地址
// 返回:
//   *tls.Config: 配置好的TLS配置对象
// 功能: 基于提供的配置创建适合客户端使用的TLS配置，包括会话缓存和服务器名称设置
func newClientTLSConfig(c *tls.Config, addr string) *tls.Config {
	if c == nil {
		c = &tls.Config{} // 如果没有提供配置，创建默认配置
	} else {
		c = c.Clone() // 克隆现有配置以避免修改原始配置
	}

	// 如果没有设置客户端会话缓存，创建LRU缓存
	if c.ClientSessionCache == nil {
		c.ClientSessionCache = tls.NewLRUClientSessionCache(0)
	}

	// 如果没有设置服务器名称，从地址中提取
	if len(c.ServerName) == 0 {
		serverName := tlsServerName(addr)
		if serverName == "*" {
			c.InsecureSkipVerify = true // 无法确定服务器名称时跳过验证
		} else {
			c.ServerName = serverName // 设置服务器名称用于SNI
		}
	}
	return c
}

// tlsServerName 从地址中提取TLS服务器名称
// 参数:
//   addr: 服务器地址（可能包含端口）
// 返回:
//   string: 提取的主机名，如果提取失败返回"*"
// 功能: 解析地址字符串，提取主机名部分用于TLS握手
func tlsServerName(addr string) string {
	if !strings.Contains(addr, ":") {
		return addr // 如果地址不包含端口，直接返回
	}
	host, _, err := net.SplitHostPort(addr) // 分离主机和端口
	if err != nil {
		return "*" // 解析失败返回通配符
	}
	return host // 返回主机名
}

// ErrTLSHandshakeTimeout TLS握手超时错误
// 当TLS握手操作超过指定时间限制时返回此错误
var ErrTLSHandshakeTimeout = errors.New("tls handshake timed out")

// timeoutErrorChPool 超时错误通道池，用于复用通道对象
var timeoutErrorChPool sync.Pool

// tlsClientHandshake 执行带超时的TLS客户端握手
// 参数:
//   rawConn: 原始网络连接
//   tlsConfig: TLS配置
//   timeout: 握手超时时间
// 返回:
//   net.Conn: TLS连接对象
//   error: 握手错误，成功时为nil
// 功能: 在指定超时时间内完成TLS握手，超时则返回错误
func tlsClientHandshake(rawConn net.Conn, tlsConfig *tls.Config, timeout time.Duration) (net.Conn, error) {
	tc := time.NewTimer(timeout) // 创建超时定时器
	defer tc.Stop()              // 确保定时器被停止

	// 从池中获取错误通道
	var ch chan error
	chv := timeoutErrorChPool.Get()
	if chv == nil {
		chv = make(chan error) // 池中没有可用通道时创建新的
	}
	ch = chv.(chan error)
	defer timeoutErrorChPool.Put(chv) // 使用完毕后放回池中

	conn := tls.Client(rawConn, tlsConfig) // 创建TLS客户端连接

	// 在goroutine中执行握手
	go func() {
		ch <- conn.Handshake() // 将握手结果发送到通道
	}()

	// 等待握手完成或超时
	select {
	case <-tc.C: // 超时情况
		rawConn.Close() // 关闭原始连接
		<-ch            // 等待握手goroutine结束
		return nil, ErrTLSHandshakeTimeout
	case err := <-ch: // 握手完成
		if err != nil {
			rawConn.Close() // 握手失败时关闭连接
			return nil, err
		}
		return conn, nil // 握手成功返回TLS连接
	}
}

// dialAddr 拨号连接到指定地址
// 参数:
//   addr: 目标地址
//   dial: 拨号函数，可为nil
//   dialDualStack: 是否使用双栈拨号
//   isTLS: 是否需要TLS连接
//   tlsConfig: TLS配置
//   timeout: 连接超时时间
// 返回:
//   net.Conn: 建立的网络连接
//   error: 连接错误，成功时为nil
// 功能: 根据参数建立网络连接，支持TLS和双栈拨号
func dialAddr(addr string, dial DialFunc, dialDualStack, isTLS bool, tlsConfig *tls.Config, timeout time.Duration) (net.Conn, error) {
	// 如果没有提供拨号函数，使用默认函数
	if dial == nil {
		if dialDualStack {
			dial = DialDualStack // 使用双栈拨号（IPv4和IPv6）
		} else {
			dial = Dial // 使用标准拨号
		}
		addr = addMissingPort(addr, isTLS) // 添加默认端口
	}
	// 执行拨号
	conn, err := dial(addr)
	if err != nil {
		return nil, err
	}
	if conn == nil {
		panic("BUG: DialFunc returned (nil, nil)") // 拨号函数不应返回(nil, nil)
	}
	// 检查连接是否已经是TLS连接
	_, isTLSAlready := conn.(*tls.Conn)
	if isTLS && !isTLSAlready {
		// 需要TLS但连接不是TLS时，进行TLS握手
		if timeout == 0 {
			return tls.Client(conn, tlsConfig), nil // 无超时的TLS握手
		}
		return tlsClientHandshake(conn, tlsConfig, timeout) // 带超时的TLS握手
	}
	return conn, nil
}

// addMissingPort 为地址添加缺失的端口号
// 参数:
//   addr: 服务器地址
//   isTLS: 是否为TLS连接
// 返回:
//   string: 包含端口号的完整地址
// 功能: 如果地址中没有端口号，根据协议类型添加默认端口
func addMissingPort(addr string, isTLS bool) string {
	n := strings.Index(addr, ":")
	if n >= 0 {
		return addr // 地址已包含端口号
	}
	port := 80 // HTTP默认端口
	if isTLS {
		port = 443 // HTTPS默认端口
	}
	return net.JoinHostPort(addr, strconv.Itoa(port)) // 组合主机和端口
}

// PipelineClient HTTP管道客户端结构体
// 管理多个连接的HTTP管道客户端，支持连接复用和请求管道化
type PipelineClient struct {
	Addr                string        // 目标服务器地址
	MaxConns            int           // 最大连接数
	MaxPendingRequests  int           // 最大待处理请求数
	MaxBatchDelay       time.Duration // 最大批处理延迟
	Dial                DialFunc      // 自定义拨号函数
	DialDualStack       bool          // 是否使用双栈拨号（IPv4和IPv6）
	IsTLS               bool          // 是否使用TLS连接
	TLSConfig           *tls.Config   // TLS配置
	MaxIdleConnDuration time.Duration // 最大空闲连接持续时间
	ReadBufferSize      int           // 读取缓冲区大小
	WriteBufferSize     int           // 写入缓冲区大小
	ReadTimeout         time.Duration // 读取超时时间
	WriteTimeout        time.Duration // 写入超时时间

	connClients     []*pipelineConnClient // 连接客户端数组
	connClientsLock sync.Mutex            // 连接客户端数组的互斥锁
}

// pipelineConnClient 管道连接客户端结构体
// 表示单个连接的管道客户端配置
type pipelineConnClient struct {
	Addr                string        // 目标服务器地址
	MaxPendingRequests  int           // 最大待处理请求数
	MaxBatchDelay       time.Duration // 最大批处理延迟
	Dial                DialFunc      // 自定义拨号函数
	DialDualStack       bool          // 是否使用双栈拨号
	IsTLS               bool          // 是否使用TLS连接
	TLSConfig           *tls.Config   // TLS配置
	MaxIdleConnDuration time.Duration // 最大空闲连接持续时间
	ReadBufferSize      int           // 读取缓冲区大小
	WriteBufferSize     int           // 写入缓冲区大小
	ReadTimeout         time.Duration // 读取超时时间
	WriteTimeout        time.Duration // 写入超时时间

	workPool sync.Pool // 工作对象池，用于复用pipelineWork对象

	chLock sync.Mutex            // 通道操作的互斥锁
	chW    chan *pipelineWork    // 写入工作通道，用于发送请求
	chR    chan *pipelineWork    // 读取工作通道，用于接收响应

	tlsConfigLock sync.Mutex // TLS配置的互斥锁
	tlsConfig     *tls.Config // TLS配置缓存
}

// pipelineWork 管道工作单元结构体
// 表示一个完整的请求-响应工作单元
type pipelineWork struct {
	req      *Request      // HTTP请求对象
	resp     *Response     // HTTP响应对象
	t        *time.Timer   // 超时定时器
	deadline time.Time     // 截止时间
	err      error         // 错误信息
	done     chan struct{} // 完成信号通道
}

// Do 执行HTTP请求
// 参数:
//   req: HTTP请求对象
//   resp: HTTP响应对象，用于接收响应数据
// 返回:
//   error: 执行错误，成功时为nil
// 功能: 通过管道客户端执行HTTP请求，支持连接复用
func (c *PipelineClient) Do(req *Request, resp *Response) error {
	return c.getConnClient().Do(req, resp) // 获取连接客户端并执行请求
}

// Do 在管道连接客户端上执行HTTP请求
// 参数:
//   req: HTTP请求对象
//   resp: HTTP响应对象
// 返回:
//   error: 执行错误，成功时为nil
// 功能: 将请求放入管道队列，等待响应完成
func (c *pipelineConnClient) Do(req *Request, resp *Response) error {
	c.init() // 初始化连接客户端

	// 从对象池获取工作单元
	w := acquirePipelineWork(&c.workPool, 0)
	w.req = req // 设置请求
	if resp != nil {
		w.resp = resp // 设置响应对象
	}

	// Put the request to outgoing queue - 将请求放入发送队列
	select {
	case c.chW <- w: // 尝试直接发送
	default:
		// Try substituting the oldest w with the current one.
		// 尝试用当前请求替换最旧的请求
		select {
		case wOld := <-c.chW: // 取出旧的工作单元
			wOld.err = ErrPipelineOverflow // 设置管道溢出错误
			wOld.done <- struct{}{}        // 通知旧请求完成
		default:
		}
		select {
		case c.chW <- w: // 再次尝试发送当前请求
		default:
			releasePipelineWork(&c.workPool, w) // 释放工作单元
			return ErrPipelineOverflow          // 返回管道溢出错误
		}
	}

	// Wait for the response - 等待响应
	<-w.done        // 等待完成信号
	err := w.err    // 获取错误信息

	releasePipelineWork(&c.workPool, w) // 释放工作单元回对象池

	return err // 返回执行结果
}

// getConnClient 获取可用的连接客户端
// 返回:
//   *pipelineConnClient: 可用的管道连接客户端
// 功能: 线程安全地获取或创建连接客户端
func (c *PipelineClient) getConnClient() *pipelineConnClient {
	c.connClientsLock.Lock()         // 加锁保护连接客户端数组
	cc := c.getConnClientUnlocked()  // 获取连接客户端（无锁版本）
	c.connClientsLock.Unlock()       // 解锁
	return cc
}

// getConnClientUnlocked 获取连接客户端（无锁版本）
// 返回:
//   *pipelineConnClient: 可用的管道连接客户端
// 功能: 从现有连接中选择或创建新的连接客户端
func (c *PipelineClient) getConnClientUnlocked() *pipelineConnClient {
	if len(c.connClients) == 0 {
		return c.newConnClient() // 如果没有连接客户端，创建新的
	}

	// Return the client with the minimum number of pending requests.
	// 返回具有最少待处理请求数的客户端
	minCC := c.connClients[0]           // 初始化为第一个客户端
	minReqs := minCC.PendingRequests()  // 获取其待处理请求数
	if minReqs == 0 {
		return minCC // 如果没有待处理请求，直接返回
	}
	// 遍历所有连接客户端，寻找最优的
	for i := 1; i < len(c.connClients); i++ {
		cc := c.connClients[i]
		reqs := cc.PendingRequests() // 获取当前客户端的待处理请求数
		if reqs == 0 {
			return cc // 找到空闲客户端，直接返回
		}
		if reqs < minReqs {
			minCC = cc      // 更新最少请求数的客户端
			minReqs = reqs  // 更新最少请求数
		}
	}

	// 检查是否可以创建新的连接客户端
	maxConns := c.MaxConns
	if maxConns <= 0 {
		maxConns = 1 // 默认最大连接数为1
	}
	if len(c.connClients) < maxConns {
		return c.newConnClient() // 如果未达到最大连接数，创建新客户端
	}
	return minCC // 返回最少请求数的客户端
}

// newConnClient 创建新的管道连接客户端
// 返回:
//   *pipelineConnClient: 新创建的管道连接客户端
// 功能: 基于PipelineClient的配置创建新的连接客户端
func (c *PipelineClient) newConnClient() *pipelineConnClient {
	cc := &pipelineConnClient{
		Addr:                c.Addr,                // 复制目标地址
		MaxPendingRequests:  c.MaxPendingRequests,  // 复制最大待处理请求数
		MaxBatchDelay:       c.MaxBatchDelay,       // 复制最大批处理延迟
		Dial:                c.Dial,                // 复制拨号函数
		DialDualStack:       c.DialDualStack,       // 复制双栈拨号设置
		IsTLS:               c.IsTLS,               // 复制TLS设置
		TLSConfig:           c.TLSConfig,           // 复制TLS配置
		MaxIdleConnDuration: c.MaxIdleConnDuration, // 复制最大空闲连接时间
		ReadBufferSize:      c.ReadBufferSize,      // 复制读取缓冲区大小
		WriteBufferSize:     c.WriteBufferSize,     // 复制写入缓冲区大小
		ReadTimeout:         c.ReadTimeout,         // 复制读取超时时间
		WriteTimeout:        c.WriteTimeout,        // 复制写入超时时间
	}
	c.connClients = append(c.connClients, cc) // 添加到连接客户端数组
	return cc
}

// ErrPipelineOverflow 管道溢出错误
// 当管道请求队列溢出时返回此错误，建议增加MaxConns和/或MaxPendingRequests
var ErrPipelineOverflow = errors.New("pipelined requests' queue has been overflown. Increase MaxConns and/or MaxPendingRequests")

// DefaultMaxPendingRequests 默认最大待处理请求数
const DefaultMaxPendingRequests = 1024

// init 初始化管道连接客户端
// 功能: 设置通道并启动工作协程，确保客户端正常运行
func (c *pipelineConnClient) init() {
	c.chLock.Lock() // 加锁保护通道初始化
	if c.chR == nil {
		// 设置最大待处理请求数
		maxPendingRequests := c.MaxPendingRequests
		if maxPendingRequests <= 0 {
			maxPendingRequests = DefaultMaxPendingRequests // 使用默认值
		}
		// 创建读取和写入通道
		c.chR = make(chan *pipelineWork, maxPendingRequests)
		if c.chW == nil {
			c.chW = make(chan *pipelineWork, maxPendingRequests)
		}
		// 启动工作协程
		go func() {
			// Keep restarting the worker if it fails (connection errors for example).
			// 如果工作协程失败则持续重启（例如连接错误）
			for {
				if err := c.worker(); err != nil {
					// 工作协程出错时，处理队列中的请求
					w := <-c.chW
					w.err = err
					w.done <- struct{}{}
				} else {
					// 检查是否应该停止工作协程
					c.chLock.Lock()
					stop := len(c.chR) == 0 && len(c.chW) == 0 // 队列为空时停止
					if !stop {
						c.chR = nil // 清空读取通道
						c.chW = nil // 清空写入通道
					}
					c.chLock.Unlock() // 解锁

					if stop {
						break // 停止工作协程
					}
				}
			}
		}()
	}
	c.chLock.Unlock() // 解锁
}

// worker 工作协程的主要逻辑
// 返回:
//   error: 工作过程中的错误，正常结束时为nil
// 功能: 建立连接，启动读写协程，处理请求和响应
func (c *pipelineConnClient) worker() error {
	tlsConfig := c.cachedTLSConfig() // 获取缓存的TLS配置
	// 建立到目标地址的连接
	conn, err := dialAddr(c.Addr, c.Dial, c.DialDualStack, c.IsTLS, tlsConfig, c.WriteTimeout)
	if err != nil {
		return err // 连接失败返回错误
	}

	// Start reader and writer - 启动读取器和写入器
	stopW := make(chan struct{}) // 写入器停止信号通道
	doneW := make(chan error)    // 写入器完成通道
	go func() {
		doneW <- c.writer(conn, stopW) // 启动写入器协程
	}()
	stopR := make(chan struct{}) // 读取器停止信号通道
	doneR := make(chan error)    // 读取器完成通道
	go func() {
		doneR <- c.reader(conn, stopR) // 启动读取器协程
	}()

	// Wait until reader and writer are stopped - 等待读取器和写入器停止
	select {
	case err = <-doneW: // 写入器先完成
		conn.Close()  // 关闭连接
		close(stopR)  // 停止读取器
		<-doneR       // 等待读取器完成
	case err = <-doneR: // 读取器先完成
		conn.Close()  // 关闭连接
		close(stopW)  // 停止写入器
		<-doneW       // 等待写入器完成
	}

	// Notify pending readers - 通知待处理的读取器
	for len(c.chR) > 0 {
		w := <-c.chR                      // 取出待处理的工作单元
		w.err = errPipelineConnStopped    // 设置连接停止错误
		w.done <- struct{}{}              // 通知工作单元完成
	}

	return err // 返回工作过程中的错误
}

// cachedTLSConfig 获取缓存的TLS配置
// 返回:
//   *tls.Config: TLS配置对象，如果不使用TLS则返回nil
// 功能: 线程安全地获取或创建TLS配置，避免重复创建
func (c *pipelineConnClient) cachedTLSConfig() *tls.Config {
	if !c.IsTLS {
		return nil // 不使用TLS时返回nil
	}

	c.tlsConfigLock.Lock() // 加锁保护TLS配置
	cfg := c.tlsConfig     // 获取缓存的配置
	if cfg == nil {
		// 如果没有缓存配置，创建新的
		cfg = newClientTLSConfig(c.TLSConfig, c.Addr)
		c.tlsConfig = cfg // 缓存配置
	}
	c.tlsConfigLock.Unlock() // 解锁

	return cfg // 返回TLS配置
}

// writer 写入器协程，负责将请求写入连接
// 参数:
//   conn: 网络连接
//   stopCh: 停止信号通道
// 返回:
//   error: 写入过程中的错误
// 功能: 从写入通道读取请求，批量写入到连接中
func (c *pipelineConnClient) writer(conn net.Conn, stopCh <-chan struct{}) error {
	// 设置写入缓冲区大小
	writeBufferSize := c.WriteBufferSize
	if writeBufferSize <= 0 {
		writeBufferSize = defaultWriteBufferSize // 使用默认大小
	}
	bw := bufio.NewWriterSize(conn, writeBufferSize) // 创建缓冲写入器
	defer bw.Flush()                                 // 确保在函数结束时刷新缓冲区
	chR := c.chR                                     // 读取通道引用
	chW := c.chW                                     // 写入通道引用
	writeTimeout := c.WriteTimeout                   // 写入超时时间

	// 设置最大空闲连接时间
	maxIdleConnDuration := c.MaxIdleConnDuration
	if maxIdleConnDuration <= 0 {
		maxIdleConnDuration = DefaultMaxIdleConnDuration // 使用默认值
	}
	maxBatchDelay := c.MaxBatchDelay // 最大批处理延迟

	// 初始化定时器和变量
	var (
		stopTimer      = time.NewTimer(time.Hour) // 停止定时器
		flushTimer     = time.NewTimer(time.Hour) // 刷新定时器
		flushTimerCh   <-chan time.Time           // 刷新定时器通道
		instantTimerCh = make(chan time.Time)     // 即时定时器通道

		w   *pipelineWork // 当前工作单元
		err error         // 错误信息
	)
	close(instantTimerCh) // 关闭即时定时器通道，使其立即可读
	for {
	againChW: // 标签，用于跳转
		select {
		case w = <-chW: // 从写入通道接收工作单元
			// Fast path: len(chW) > 0 - 快速路径：写入通道有数据
		default:
			// Slow path - 慢速路径：写入通道为空，需要等待
			stopTimer.Reset(maxIdleConnDuration) // 重置停止定时器，开始计算空闲时间
			select {
			case w = <-chW: // 等待新的工作单元到达
			case <-stopTimer.C: // 空闲时间超过限制，停止写入器
				return nil
			case <-stopCh: // 收到外部停止信号
				return nil
			case <-flushTimerCh: // 刷新定时器触发，需要刷新缓冲区
				if err = bw.Flush(); err != nil {
					return err // 刷新失败返回错误
				}
				flushTimerCh = nil // 清空刷新定时器通道
				goto againChW      // 重新尝试获取工作单元
			}
		}

		// 检查工作单元是否已超时
		if !w.deadline.IsZero() && time.Since(w.deadline) >= 0 {
			w.err = ErrTimeout   // 设置超时错误
			w.done <- struct{}{} // 通知工作单元完成
			continue             // 继续处理下一个工作单元
		}

		// 设置写入超时
		if writeTimeout > 0 {
			// Set Deadline every time, since golang has fixed the performance issue
			// 每次都设置截止时间，因为golang已经修复了性能问题
			// See https://github.com/golang/go/issues/15133#issuecomment-271571395 for details
			// 详见：https://github.com/golang/go/issues/15133#issuecomment-271571395
			currentTime := time.Now()
			if err = conn.SetWriteDeadline(currentTime.Add(writeTimeout)); err != nil {
				w.err = err          // 设置错误
				w.done <- struct{}{} // 通知完成
				return err           // 返回错误
			}
		}
		// 将请求写入缓冲写入器
		if err = w.req.Write(bw); err != nil {
			w.err = err          // 设置写入错误
			w.done <- struct{}{} // 通知完成
			return err           // 返回错误
		}
		// 决定是否需要设置刷新定时器
		// 条件：当前没有刷新定时器 且 (写入队列为空 或 读取队列已满)
		if flushTimerCh == nil && (len(chW) == 0 || len(chR) == cap(chR)) {
			if maxBatchDelay > 0 {
				// 设置批处理延迟定时器
				flushTimer.Reset(maxBatchDelay)
				flushTimerCh = flushTimer.C
			} else {
				// 立即刷新（使用已关闭的通道）
				flushTimerCh = instantTimerCh
			}
		}

	againChR: // 标签，用于重新尝试将工作单元放入读取队列
		select {
		case chR <- w: // 尝试将工作单元放入读取通道
			// Fast path: len(chR) < cap(chR) - 快速路径：读取通道未满
		default:
			// Slow path - 慢速路径：读取通道已满，需要等待
			select {
			case chR <- w: // 等待读取通道有空间
			case <-stopCh: // 收到停止信号
				w.err = errPipelineConnStopped // 设置连接停止错误
				w.done <- struct{}{}           // 通知工作单元完成
				return nil                     // 停止写入器
			case <-flushTimerCh: // 刷新定时器触发
				if err = bw.Flush(); err != nil {
					w.err = err          // 设置刷新错误
					w.done <- struct{}{} // 通知完成
					return err           // 返回错误
				}
				flushTimerCh = nil // 清空刷新定时器通道
				goto againChR      // 重新尝试放入读取队列
			}
		}
	} // 写入器主循环结束
}

// reader 读取器协程，负责从连接读取响应
// 参数:
//   conn: 网络连接
//   stopCh: 停止信号通道
// 返回:
//   error: 读取过程中的错误
// 功能: 从读取通道获取工作单元，读取对应的HTTP响应
func (c *pipelineConnClient) reader(conn net.Conn, stopCh <-chan struct{}) error {
	// 设置读取缓冲区大小
	readBufferSize := c.ReadBufferSize
	if readBufferSize <= 0 {
		readBufferSize = defaultReadBufferSize // 使用默认大小
	}
	br := bufio.NewReaderSize(conn, readBufferSize) // 创建缓冲读取器
	chR := c.chR                                    // 读取通道引用
	readTimeout := c.ReadTimeout                    // 读取超时时间

	// 初始化变量
	var (
		w   *pipelineWork // 当前工作单元
		err error         // 错误信息
	)
	for { // 读取器主循环
		select {
		case w = <-chR: // 从读取通道获取工作单元
			// Fast path: len(chR) > 0 - 快速路径：读取通道有数据
		default:
			// Slow path - 慢速路径：读取通道为空，需要等待
			select {
			case w = <-chR: // 等待工作单元到达
			case <-stopCh: // 收到停止信号
				return nil // 停止读取器
			}
		}

		// 设置读取超时
		if readTimeout > 0 {
			// Set Deadline every time, since golang has fixed the performance issue
			// 每次都设置截止时间，因为golang已经修复了性能问题
			// See https://github.com/golang/go/issues/15133#issuecomment-271571395 for details
			// 详见：https://github.com/golang/go/issues/15133#issuecomment-271571395
			currentTime := time.Now()
			if err = conn.SetReadDeadline(currentTime.Add(readTimeout)); err != nil {
				w.err = err          // 设置截止时间错误
				w.done <- struct{}{} // 通知完成
				return err           // 返回错误
			}
		}
		// 读取HTTP响应数据
		if err = w.resp.Read(br); err != nil {
			w.err = err          // 设置读取错误
			w.done <- struct{}{} // 通知完成
			return err           // 返回错误
		}

		w.done <- struct{}{} // 通知工作单元完成，响应读取成功
	} // 读取器主循环结束
}

// PendingRequests 获取所有连接客户端的待处理请求总数
// 返回:
//   int: 待处理请求的总数
// 功能: 统计所有连接客户端的待处理请求数量
func (c *PipelineClient) PendingRequests() int {
	c.connClientsLock.Lock() // 加锁保护连接客户端数组
	n := 0
	// 遍历所有连接客户端，累加待处理请求数
	for _, cc := range c.connClients {
		n += cc.PendingRequests()
	}
	c.connClientsLock.Unlock() // 解锁
	return n                   // 返回总数
}

// PendingRequests 获取单个连接客户端的待处理请求数
// 返回:
//   int: 待处理请求数量
// 功能: 计算读取和写入通道中的工作单元总数
func (c *pipelineConnClient) PendingRequests() int {
	c.init() // 确保客户端已初始化

	c.chLock.Lock()                  // 加锁保护通道
	n := len(c.chR) + len(c.chW)     // 计算两个通道的工作单元总数
	c.chLock.Unlock()                // 解锁
	return n                         // 返回待处理请求数
}

// errPipelineConnStopped 管道连接已停止的错误
var errPipelineConnStopped = errors.New("pipeline connection has been stopped")

// acquirePipelineWork 从对象池获取管道工作单元
// 参数:
//   pool: 对象池
//   timeout: 超时时间
// 返回:
//   *pipelineWork: 工作单元对象
// 功能: 从池中获取或创建新的工作单元，设置超时定时器
func acquirePipelineWork(pool *sync.Pool, timeout time.Duration) *pipelineWork {
	v := pool.Get() // 从池中获取对象
	if v == nil {
		// 池中没有对象时创建新的
		v = &pipelineWork{
			done: make(chan struct{}, 1), // 创建完成信号通道
		}
	}
	w := v.(*pipelineWork) // 类型断言
	if timeout > 0 {
		// 设置超时定时器
		if w.t == nil {
			w.t = time.NewTimer(timeout) // 创建新定时器
		} else {
			w.t.Reset(timeout) // 重置现有定时器
		}
		w.deadline = time.Now().Add(timeout) // 设置截止时间
	}

	return w // 返回工作单元
}

// releasePipelineWork 释放管道工作单元回对象池
// 参数:
//   pool: 对象池
//   w: 要释放的工作单元
// 功能: 清理工作单元的状态并放回对象池以供复用
func releasePipelineWork(pool *sync.Pool, w *pipelineWork) {
	if w.t != nil {
		w.t.Stop() // 停止定时器
	}
	// 清理工作单元的字段
	w.req = nil  // 清空请求
	w.resp = nil // 清空响应
	w.err = nil  // 清空错误
	pool.Put(w)  // 放回对象池
}