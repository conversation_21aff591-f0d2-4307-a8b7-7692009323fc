// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-17 19:16:17
// FilePath: /yaml_scan/utils/auth/pdcp/creds.go
// Description: 
package pdcp

var (
	PDCPDir      = filepath.Join(folderutil.HomeDirOrDefault(""), ".pdcp")
	PDCPCredFile = filepath.Join(PDCPDir, "credentials.yaml")
	ErrNoCreds   = fmt.Errorf("no credentials found in %s", PDCPDir)
)

// PDCPCredHandler is interface for adding / retrieving pdcp credentials
// from file system
type PDCPCredHandler struct{}

// GetCreds retrieves the credentials from the file system or environment variables
func (p *PDCPCredHandler) GetCreds() (*PDCPCredentials, error) {
	credsFromEnv := p.getCredsFromEnv()
	if credsFromEnv != nil {
		return credsFromEnv, nil
	}
	if !fileutil.FolderExists(PDCPDir) || !fileutil.FileExists(PDCPCredFile) {
		return nil, ErrNoCreds
	}
	bin, err := os.Open(PDCPCredFile)
	if err != nil {
		return nil, err
	}
	// for future use-cases
	var creds []PDCPCredentials
	err = yaml.NewDecoder(bin).Decode(&creds)
	if err != nil {
		return nil, err
	}
	if len(creds) == 0 {
		return nil, ErrNoCreds
	}
	return &creds[0], nil
}
