// Author: chenjb
// Version: V1.0
// Date: 2025-05-16 09:55:18
// FilePath: /yaml_scan/pkg/hybridMap/disk/leveldb.go
// Description:
package disk

import (
	"bytes"
	"strconv"
	"sync"
	"time"

	"github.com/syndtr/goleveldb/leveldb"
	"github.com/syndtr/goleveldb/leveldb/iterator"
	"github.com/syndtr/goleveldb/leveldb/opt"
	"github.com/syndtr/goleveldb/leveldb/util"
)

// Megabyte 定义了一兆字节的大小常量
const Megabyte = 1 << 20

// expSeparator 定义了过期时间和实际数据之间的分隔符
const expSeparator = ";"

// LevelDB - 表示 LevelDB 数据库实现
// 使用 goleveldb 库实现了 DB 接口，提供高性能的键值存储能力
type LevelDB struct {
	db *leveldb.DB
	sync.RWMutex
}

// OpenLevelDB 打开指定路径的 LevelDB 数据库
// @param path string:  数据库存储路径
// @return *LevelDB *LevelDB: 数据库实例指针
// @return error error: 可能得错误
func OpenLevelDB(path string) (*LevelDB, error) {
	// 创建数据库，设置较大的压缩表大小以提高性能
	db, err := leveldb.OpenFile(path, &opt.Options{
		CompactionTableSize: 256 * Megabyte,
	})
	if err != nil {
		return nil, err
	}

	ldb := new(LevelDB)
	ldb.db = db

	return ldb, nil
}

// Size 返回数据库占用的字节数
// @receiver ldb 
// @return int64 int64: 数据库大小，如果无法获取统计信息则返回 -1
func (ldb *LevelDB) Size() int64 {
	// 获取数据库统计信息
	var stats leveldb.DBStats
	if nil != ldb.db.Stats(&stats) {
		return -1
	}
	size := int64(0)
	// 计算所有层级的数据大小总和
	for _, v := range stats.LevelSizes {
		size += v
	}
	return size
}

// Close 关闭数据库连接并释放相关资源
func (ldb *LevelDB) Close() {
	ldb.db.Close()
}

// GC - 运行垃圾回收，对数据库进行压缩以回收空间
func (ldb *LevelDB) GC() error {
	return ldb.db.CompactRange(util.Range{})
}

// Incr 将指定键的值增加指定数量
// @receiver ldb 
// @param k string: 要增加值的键
// @param by int64: 要增加的数量
// @return int64 int64: 增加后的新值
// @return error error: 可能的错误
func (ldb *LevelDB) Incr(k string, by int64) (int64, error) {
	// 使用互斥锁保证操作原子性
	ldb.Lock()
	defer ldb.Unlock()

	// 尝试获取当前值，如果不存在则使用空字节数组
	val, err := ldb.get(k)
	if err != nil {
		val = []byte{}
	}

	// 将字节数组转换为整数并增加指定值
	valFloat, _ := strconv.ParseInt(string(val), 10, 64)
	valFloat += by

	// 存储新值
	err = ldb.set([]byte(k), intToByteSlice(valFloat), -1)
	if err != nil {
		return 0, err
	}

	return valFloat, nil
}

// intToByteSlice 将整数转换为字节数组
// @param v int64: 要转换的整数
// @return []byte []byte: 表示该整数的字节数组
func intToByteSlice(v int64) []byte {
	return []byte(strconv.FormatInt(v, 10))
}

// set 内部方法，设置键值对并处理过期时间
// @receiver ldb 
// @param k []byte:  键名字节数组
// @param v []byte:  值字节数组
// @param ttl time.Duration:  过期时间，-1表示永不过期
// @return error error: 可能的错误
func (ldb *LevelDB) set(k, v []byte, ttl time.Duration) error {
	// 计算过期时间戳
	var expires int64
	if ttl > 0 {
		expires = time.Now().Add(ttl).Unix()
	}
	// 将过期时间添加到值的前面，形如 "expireTime;actualValue"
	expiresBytes := append(intToByteSlice(expires), expSeparator[:]...)
	v = append(expiresBytes, v...)
	return ldb.db.Put(k, v, nil)
}

// Set 设置键值对并处理过期时间
// @receiver ldb 
// @param k string:  键名
// @param v []byte:  值字节数组
// @param ttl time.Duration:  过期时间，-1表示永不过期
// @return error error: 可能的错误
func (ldb *LevelDB) Set(k string, v []byte, ttl time.Duration) error {
	return ldb.set([]byte(k), v, ttl)
}

// MSet 批量设置多个键值对
// @receiver ldb 
// @param data map[string][]byte: 要设置的键值对映射
// @return error error: 可能的错误
func (ldb *LevelDB) MSet(data map[string][]byte) error {
	// 创建批处理对象以提高性能
	batch := new(leveldb.Batch)
	for k, v := range data {
		// 所有值默认永不过期（过期时间为0）
		v = append([]byte("0;"), v...)
		batch.Put([]byte(k), v)
	}
	return ldb.db.Write(batch, nil)
}

// get 内部方法，获取指定键的值并处理过期逻辑
// @receiver ldb 
// @param k string: 要获取的键名
// @return []byte []byte: 键对应的值（如果存在且未过期）
// @return error error: 可能的错误，如键不存在或已过期
func (ldb *LevelDB) get(k string) ([]byte, error) {
	var data []byte
	var err error

	delete := false

	// 从数据库中读取原始数据
	item, err := ldb.db.Get([]byte(k), nil)
	if err != nil {
		return []byte{}, err
	}

	// 分离过期时间和实际数据
	parts := bytes.SplitN(item, []byte(expSeparator), 2)
	expires, actual := parts[0], parts[1]
	
	// 检查是否已过期
	if exp, _ := strconv.Atoi(string(expires)); exp > 0 && int(time.Now().Unix()) >= exp {
		delete = true
	} else {
		data = actual
	}

	// 如果过期则删除该键
	if delete {
		errDelete := ldb.db.Delete([]byte(k), nil)
		if errDelete != nil {
			return data, errDelete
		}
		return data, ErrNotFound
	}

	return data, nil
}

// Get 获取指定键的值
// @receiver ldb 
// @param k string: 要获取的键名
// @return []byte []byte: 键对应的值（如果存在且未过期）
// @return error error: 可能的错误，如键不存在或已过期
func (ldb *LevelDB) Get(k string) ([]byte, error) {
	return ldb.get(k)
}

// MGet 批量获取多个键的值
// @receiver ldb 
// @param keys []string:  要获取的键名列表
// @return [] []: 对应的值列表，返回的数组内项的顺序与传入的keys一致 
// 如果某个键不存在或已过期，对应位置为空字节数组
func (ldb *LevelDB) MGet(keys []string) [][]byte {
	var data [][]byte
	for _, key := range keys {
		val, err := ldb.get(key)
		if err != nil {
			data = append(data, []byte{})
			continue
		}
		data = append(data, val)
	}
	return data
}

// TTL 返回指定键值对的剩余生存时间（秒）
// @receiver ldb 
// @param key string:  要查询的键名
// @return int64 int64: 剩余生存时间（秒），-1表示永不过期，-2表示键不存在或已过期
func (ldb *LevelDB) TTL(key string) int64 {
	// 获取键对应的原始值
	item, err := ldb.db.Get([]byte(key), nil)
	if err != nil {
		return -2
	}
	
	// 提取过期时间
	parts := bytes.SplitN(item, []byte(expSeparator), 2)
	exp, _ := strconv.Atoi(string(parts[0]))
	if exp == 0 {
		return -1 // 永不过期
	}

	// 计算剩余时间
	now := time.Now().Unix()
	if now >= int64(exp) {
		return -2  // 已过期
	}

	return int64(exp) - now
}

// MDel 批量删除多个键
// @receiver ldb 
// @param keys []string: 要删除的键名列表
// @return error error: 可能的错误
func (ldb *LevelDB) MDel(keys []string) error {
	// 创建批处理对象以提高性能
	batch := new(leveldb.Batch)
	for _, key := range keys {
		batch.Delete([]byte(key))
	}
	return ldb.db.Write(batch, nil)
}

// Del 删除指定的键
// @receiver ldb 
// @param key string: 要删除的键名
// @return error error: 可能得错误
func (ldb *LevelDB) Del(key string) error {
	return ldb.db.Delete([]byte(key), nil)
}

// Scan 使用指定的处理函数遍历整个存储
// @receiver ldb 
// @param scannerOpt ScannerOptions: 扫描选项，包含偏移量、前缀过滤、处理函数等
// @return error error: 可能的错误
func (ldb *LevelDB) Scan(scannerOpt ScannerOptions) error {
	var iter iterator.Iterator

	// 根据偏移量创建迭代器
	if scannerOpt.Offset == "" {
		iter = ldb.db.NewIterator(nil, nil)
	} else {
		iter = ldb.db.NewIterator(&util.Range{Start: []byte(scannerOpt.Offset)}, nil)
		if !scannerOpt.IncludeOffset {
			iter.Next() // 如果不包含偏移量，移动到下一个项
		}
	}
	defer iter.Release() // 确保在函数结束时释放迭代器资源

	// 定义验证键是否符合扫描条件的函数
	valid := func(k []byte) bool {
		if k == nil {
			return false
		}

		// 检查前缀过滤条件
		if scannerOpt.Prefix != "" && !bytes.HasPrefix(k, []byte(scannerOpt.Prefix)) {
			return false
		}

		return true
	}

	// 遍历数据并调用处理函数
	for iter.Next() {
		key := iter.Key()
		if !valid(key) {
			continue // 如果键不符合条件，跳过处理下一个
		}
		
		// 提取实际数据（去除过期时间信息）
		val := iter.Value()
		parts := bytes.SplitN(val, []byte(expSeparator), 2)
		if len(parts) != 2 {
			continue // 跳过格式不正确的数据
		}
		
		data := parts[1]
		
		if scannerOpt.Handler(key, data) != nil {
			break // 如果处理函数返回错误，中断扫描
		}
	}

	return iter.Error()
}
