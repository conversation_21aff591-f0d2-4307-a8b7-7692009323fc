// Author: chenjb
// Version: V1.0
// Date: 2025-05-16 10:02:08
// FilePath: /yaml_scan/pkg/hybridMap/hybrid/memoryguard.go
// Description: 内存监控模块，用于监控和控制HybridMap的内存使用情况
package hybrid

import "time"

// memoryguard 内存监控器结构体
// 负责定期检查内存使用情况，并在内存使用超过阈值时调整存储策略
// 该监控器作为一个独立的goroutine运行，定期触发内存检查逻辑
type memoryguard struct {
	Enabled  bool          // 是否启用内存监控，可用于动态控制监控功能
	Interval time.Duration // 内存检查的时间间隔
	stop     chan bool     // 用于停止内存监控的通道，接收信号后会终止监控协程
}

// Run 运行内存监控循环
// @receiver mg
// @param hm *HybridMap: 要监控的HybridMap实例，将调用其TuneMemory方法来调整内存策略
func (mg *memoryguard) Run(hm *HybridMap) {
	// 创建定时器，按指定间隔触发
	ticker := time.NewTicker(mg.Interval)
	for {
		select {
		case <-ticker.C:
			// 这会检查当前内存使用情况，并在必要时激活强制磁盘存储模式
			hm.TuneMemory()
		case <-mg.stop:
			ticker.Stop()
			return
		}
	}
}

// stopMemoryGuard 停止内存监控
// @param hm *HybridMap: 要停止监控的HybridMap实例
func stopMemoryGuard(hm *HybridMap) {
	hm.memoryguard.stop <- true
}

// runMemoryGuard 启动内存监控
// @param c *HybridMap: 要监控的HybridMap实例
// @param ci time.Duration: 内存检查的时间间隔
func runMemoryGuard(c *HybridMap, ci time.Duration) {
	mg := &memoryguard{
		Interval: ci,
		stop:     make(chan bool),
	}
	c.memoryguard = mg
	go mg.Run(c)
}
