// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-16 16:21:45
// FilePath: /yaml_scan/pkg/authprovider/authx/file_test.go
// Description: 
package authx

import (
	"os"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestKV_Validate 测试KV结构体的Validate方法
// 确保它能正确验证键值对的有效性
func TestKV_Validate(t *testing.T) {
	// 测试有效的键值对
	validKV := KV{
		Key:   "test-key",
		Value: "test-value",
	}
	err := validKV.Validate()
	require.NoError(t, err, "有效的键值对应该通过验证")

	// 测试缺少键的键值对
	missingKeyKV := KV{
		Value: "test-value",
	}
	err = missingKeyKV.Validate()
	require.Error(t, err, "缺少键的键值对应该返回错误")
	require.Contains(t, err.Error(), "key cannot be empty", "错误信息应该指出键为空")

	// 测试缺少值的键值对
	missingValueKV := KV{
		Key: "test-key",
	}
	err = missingValueKV.Validate()
	require.Error(t, err, "缺少值的键值对应该返回错误")
	require.Contains(t, err.Error(), "value cannot be empty", "错误信息应该指出值为空")
}

// TestCookie_Validate 测试Cookie结构体的Validate方法
// 确保它能正确验证Cookie的有效性
func TestCookie_Validate(t *testing.T) {
	// 测试有效的键值Cookie
	validCookie := Cookie{
		Key:   "test-cookie",
		Value: "test-value",
	}
	err := validCookie.Validate()
	require.NoError(t, err, "有效的Cookie应该通过验证")

	// 测试有效的原始Cookie
	validRawCookie := Cookie{
		Raw: "Set-Cookie: test-cookie=test-value; Path=/",
	}
	err = validRawCookie.Validate()
	require.NoError(t, err, "有效的原始Cookie应该通过验证")

	// 测试缺少键的Cookie
	missingKeyCookie := Cookie{
		Value: "test-value",
	}
	err = missingKeyCookie.Validate()
	require.Error(t, err, "缺少键的Cookie应该返回错误")
	require.Contains(t, err.Error(), "key cannot be empty", "错误信息应该指出键为空")

	// 测试缺少值的Cookie
	missingValueCookie := Cookie{
		Key: "test-cookie",
	}
	err = missingValueCookie.Validate()
	require.Error(t, err, "缺少值的Cookie应该返回错误")
	require.Contains(t, err.Error(), "value cannot be empty", "错误信息应该指出值为空")
}

// TestCookie_Parse 测试Cookie结构体的Parse方法
// 确保它能正确解析原始Cookie字符串
func TestCookie_Parse(t *testing.T) {
	// 测试有效的原始Cookie
	validRawCookie := Cookie{
		Raw: "Set-Cookie: test-cookie=test-value; Path=/",
	}
	err := validRawCookie.Parse()
	require.NoError(t, err, "有效的原始Cookie应该能被解析")
	require.Equal(t, "test-cookie", validRawCookie.Key, "解析后的Cookie键应该正确")
	require.Equal(t, "test-value", validRawCookie.Value, "解析后的Cookie值应该正确")

	// 测试不带前缀的原始Cookie
	noPrefixCookie := Cookie{
		Raw: "test-cookie=test-value; Path=/",
	}
	err = noPrefixCookie.Parse()
	require.NoError(t, err, "不带前缀的原始Cookie应该能被解析")
	require.Equal(t, "test-cookie", noPrefixCookie.Key, "解析后的Cookie键应该正确")
	require.Equal(t, "test-value", noPrefixCookie.Value, "解析后的Cookie值应该正确")

	// 测试无效的原始Cookie格式
	invalidFormatCookie := Cookie{
		Raw: "test-cookie-invalid",
	}
	err = invalidFormatCookie.Parse()
	require.Error(t, err, "无效格式的原始Cookie应该返回错误")

	// 测试空的原始Cookie
	emptyCookie := Cookie{}
	err = emptyCookie.Parse()
	require.Error(t, err, "空的原始Cookie应该返回错误")
	require.Contains(t, err.Error(), "raw cookie cannot be empty", "错误信息应该指出原始Cookie为空")
}

// TestSecret_GetStrategy 测试Secret结构体的GetStrategy方法
// 确保它能根据类型返回正确的认证策略
func TestSecret_GetStrategy(t *testing.T) {
	// 测试基本认证类型
	basicAuthSecret := &Secret{
		Type:     string(BasicAuth),
		Username: "test-user",
		Password: "test-pass",
	}
	strategy := basicAuthSecret.GetStrategy()
	require.IsType(t, &BasicAuthStrategy{}, strategy, "应该返回BasicAuthStrategy类型")

	// 测试令牌认证类型
	bearerTokenSecret := &Secret{
		Type:  string(BearerTokenAuth),
		Token: "test-token",
	}
	strategy = bearerTokenSecret.GetStrategy()
	require.IsType(t, &BearerTokenAuthStrategy{}, strategy, "应该返回BearerTokenAuthStrategy类型")

	// 测试请求头认证类型
	headersSecret := &Secret{
		Type: string(HeadersAuth),
		Headers: []KV{
			{Key: "X-API-Key", Value: "test-api-key"},
		},
	}
	strategy = headersSecret.GetStrategy()
	require.IsType(t, &HeadersAuthStrategy{}, strategy, "应该返回HeadersAuthStrategy类型")

	// 测试Cookie认证类型
	cookiesSecret := &Secret{
		Type: string(CookiesAuth),
		Cookies: []Cookie{
			{Key: "session", Value: "test-session"},
		},
	}
	strategy = cookiesSecret.GetStrategy()
	require.IsType(t, &CookiesAuthStrategy{}, strategy, "应该返回CookiesAuthStrategy类型")

	// 测试查询参数认证类型
	querySecret := &Secret{
		Type: string(QueryAuth),
		Params: []KV{
			{Key: "api_key", Value: "test-api-key"},
		},
	}
	strategy = querySecret.GetStrategy()
	require.IsType(t, &QueryAuthStrategy{}, strategy, "应该返回QueryAuthStrategy类型")

	// 测试无效认证类型
	invalidTypeSecret := &Secret{
		Type: "InvalidType",
	}
	strategy = invalidTypeSecret.GetStrategy()
	require.Nil(t, strategy, "无效的认证类型应该返回nil")
}

// TestSecret_Validate 测试Secret结构体的Validate方法
// 确保它能正确验证密钥的有效性
func TestSecret_Validate(t *testing.T) {
	// 测试有效的基本认证密钥
	validBasicAuthSecret := &Secret{
		Type:     string(BasicAuth),
		Domains:  []string{"example.com"},
		Username: "test-user",
		Password: "test-pass",
	}
	err := validBasicAuthSecret.Validate()
	require.NoError(t, err, "有效的基本认证密钥应该通过验证")

	// 测试有效的令牌认证密钥
	validBearerTokenSecret := &Secret{
		Type:    string(BearerTokenAuth),
		Domains: []string{"example.com"},
		Token:   "test-token",
	}
	err = validBearerTokenSecret.Validate()
	require.NoError(t, err, "有效的令牌认证密钥应该通过验证")

	// 测试无效的认证类型
	invalidTypeSecret := &Secret{
		Type:    "InvalidType",
		Domains: []string{"example.com"},
	}
	err = invalidTypeSecret.Validate()
	require.Error(t, err, "无效的认证类型应该返回错误")
	require.Contains(t, err.Error(), "invalid type", "错误信息应该指出类型无效")

	// 测试缺少域名的密钥
	missingDomainsSecret := &Secret{
		Type:     string(BasicAuth),
		Username: "test-user",
		Password: "test-pass",
	}
	err = missingDomainsSecret.Validate()
	require.Error(t, err, "缺少域名的密钥应该返回错误")
	require.Contains(t, err.Error(), "domains or domains-regex cannot be empty", "错误信息应该指出域名为空")
}

// TestGetAuthDataFromYAML 测试从YAML数据读取认证数据的函数
// 确保它能正确解析YAML格式的认证数据
func TestGetAuthDataFromYAML(t *testing.T) {
	// 有效的YAML数据
	yamlData := []byte(`
id: test-auth
info:
  name: Test Auth
static:
  - type: BasicAuth
    domains:
      - example.com
    username: test-user
    password: test-pass
`)

	auth, err := GetAuthDataFromYAML(yamlData)
	require.NoError(t, err, "有效的YAML数据应该能被解析")
	require.Equal(t, "test-auth", auth.ID, "解析出的ID应该正确")
	require.Equal(t, "Test Auth", auth.Info.Name, "解析出的名称应该正确")
	require.Len(t, auth.Secrets, 1, "应该有1个静态密钥")
}

// TestGetAuthDataFromJSON 测试从JSON数据读取认证数据的函数
// 确保它能正确解析JSON格式的认证数据
func TestGetAuthDataFromJSON(t *testing.T) {
	// 有效的JSON数据
	jsonData := []byte(`{
		"id": "test-auth",
		"info": {
			"name": "Test Auth",
			"author": "Test Author",
			"severity": "medium",
			"description": "Test Description"
		},
		"static": [
			{
				"type": "BasicAuth",
				"domains": ["example.com"],
				"username": "test-user",
				"password": "test-pass"
			}
		],
		"dynamic": [
			{
				"template": "test-template",
				"variables": [
					{
						"key": "test-key",
						"value": "test-value"
					}
				],
				"domains": ["example.org"]
			}
		]
	}`)

	auth, err := GetAuthDataFromJSON(jsonData)
	require.NoError(t, err, "有效的JSON数据应该能被解析")
	require.Equal(t, "test-auth", auth.ID, "解析出的ID应该正确")
	require.Equal(t, "Test Auth", auth.Info.Name, "解析出的名称应该正确")
	require.Len(t, auth.Secrets, 1, "应该有1个静态密钥")
	require.Len(t, auth.Dynamic, 1, "应该有1个动态密钥")
}

// TestGetAuthDataFromFile 测试从文件读取认证数据的函数
// 确保它能根据文件扩展名选择正确的解析方法
func TestGetAuthDataFromFile(t *testing.T) {
	// 创建临时YAML文件
	yamlContent := []byte(`
id: test-auth-yaml
info:
  name: Test Auth YAML
static:
  - type: BasicAuth
    domains:
      - example.com
    username: test-user
    password: test-pass
`)
	yamlTempFile := filepath.Join(t.TempDir(), "test-auth.yaml")
	err := os.WriteFile(yamlTempFile, yamlContent, 0644)
	require.NoError(t, err, "写入临时YAML文件不应该返回错误")

	// 创建临时JSON文件
	jsonContent := []byte(`{
		"id": "test-auth-json",
		"info": {
			"name": "Test Auth JSON"
		},
		"static": [
			{
				"type": "BasicAuth",
				"domains": ["example.com"],
				"username": "test-user",
				"password": "test-pass"
			}
		]
	}`)
	jsonTempFile := filepath.Join(t.TempDir(), "test-auth.json")
	err = os.WriteFile(jsonTempFile, jsonContent, 0644)
	require.NoError(t, err, "写入临时JSON文件不应该返回错误")

	// 测试读取YAML文件
	yamlAuth, err := GetAuthDataFromFile(yamlTempFile)
	require.NoError(t, err, "从YAML文件读取不应该返回错误")
	require.Equal(t, "test-auth-yaml", yamlAuth.ID, "解析出的ID应该正确")

	// 测试读取JSON文件
	jsonAuth, err := GetAuthDataFromFile(jsonTempFile)
	require.NoError(t, err, "从JSON文件读取不应该返回错误")
	require.Equal(t, "test-auth-json", jsonAuth.ID, "解析出的ID应该正确")

	// 测试读取不支持的文件类型
	invalidFile := filepath.Join(t.TempDir(), "invalid.txt")
	err = os.WriteFile(invalidFile, []byte("invalid"), 0644)
	require.NoError(t, err, "写入临时无效文件不应该返回错误")

	_, err = GetAuthDataFromFile(invalidFile)
	require.Error(t, err, "读取不支持的文件类型应该返回错误")
	require.Contains(t, err.Error(), "invalid file extension", "错误信息应该指出文件扩展名无效")
}

// TestSupportedAuthTypes 测试获取支持的认证类型列表的函数
// 确保它返回所有支持的认证类型
func TestSupportedAuthTypes(t *testing.T) {
	types := SupportedAuthTypes()
	require.Len(t, types, 5, "应该有5种支持的认证类型")
	require.Contains(t, types, string(BasicAuth), "应该包含BasicAuth类型")
	require.Contains(t, types, string(BearerTokenAuth), "应该包含BearerTokenAuth类型")
	require.Contains(t, types, string(HeadersAuth), "应该包含HeadersAuth类型")
	require.Contains(t, types, string(CookiesAuth), "应该包含CookiesAuth类型")
	require.Contains(t, types, string(QueryAuth), "应该包含QueryAuth类型")
}

