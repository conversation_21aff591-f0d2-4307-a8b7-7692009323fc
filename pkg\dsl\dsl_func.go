// Author: chenjb
// Version: V1.0
// Date: 2025-05-28 15:01:47
// FilePath: /yaml_scan/pkg/dsl/dsl_func.go
// Description: 实现DSL系统中的各种辅助函数，提供字符串处理、加密、编码、随机数生成等功能
package dsl

import (
	"archive/zip"
	"bytes"
	"compress/flate"
	"compress/gzip"
	"compress/zlib"
	"crypto/aes"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/md5"
	"crypto/rand"
	"crypto/sha1"
	"crypto/sha256"
	"crypto/sha512"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"hash"
	"html"
	"io"
	"math"
	"net"
	"reflect"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"yaml_scan/pkg/cidr"
	"yaml_scan/pkg/dsl/deserialization"
	"yaml_scan/pkg/dsl/llm"
	"yaml_scan/pkg/gologger"
	jarm "yaml_scan/utils/crypto/jarm"
	gostruct "yaml_scan/utils/gostruct"
	randint "yaml_scan/utils/rand"
	stringsutil "yaml_scan/utils/strings"

	"github.com/asaskevich/govalidator"
	"github.com/hashicorp/go-version"
	"github.com/kataras/jwt"
	"github.com/sashabaranov/go-openai"
	"github.com/spaolacci/murmur3"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"
)

const (
	// numbers 包含所有数字字符，用于随机生成
	numbers = "1234567890"
	// letters 包含所有字母字符，用于随机生成
	letters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
)

var (
	// ErrInvalidDslFunction 表示DSL函数签名无效或使用不当的错误
	ErrInvalidDslFunction       = errors.New("invalid DSL function signature")
	// DefaultMaxDecompressionSize 定义解压缩操作的默认最大大小限制（10MB）
	DefaultMaxDecompressionSize = int64(10 * 1024 * 1024)
	// dateFormatRegex 用于匹配日期格式中的占位符
	dateFormatRegex             = regexp.MustCompile("%([A-Za-z])")
	// defaultDateTimeLayouts 定义默认支持的日期时间格式
	defaultDateTimeLayouts      = []string{
		time.RFC3339,
		"2006-01-02 15:04:05 Z07:00",
		"2006-01-02 15:04:05",
		"2006-01-02 15:04 Z07:00",
		"2006-01-02 15:04",
		"2006-01-02 Z07:00",
		"2006-01-02",
	}
	// PrintDebugCallback 是用于调试输出的回调函数
	PrintDebugCallback func(args ...interface{}) error
)

// Index 注册索引函数，用于获取字符串或切片中指定位置的元素
//
// 函数签名: index(value, position)
// 参数:
//   - value: 要索引的字符串或切片
//   - position: 索引位置（基于0）
//
// 返回值:
//   - 字符串中对应位置的字符，或切片中对应位置的元素
//
// 该函数允许从字符串或字符串切片中获取指定索引位置的值。
// 索引基于0，如果索引超出范围则返回错误。
func Index() {
	MustAddFunction(NewWithPositionalArgs("index", 2, true, func(args ...interface{}) (interface{}, error) {
		// 将第二个参数转换为整数索引
		index, err := strconv.ParseInt(toString(args[1]), 10, 64)
		if err != nil {
			return nil, err
		}
		// 如果第一个参数是切片，则对切片进行索引
		switch v := args[0].(type) {
		case []string:
			l := int64(len(v))
			if index < 0 || index >= l {
				return nil, fmt.Errorf("index out of range for %v: %d", v, index)
			}
			return v[index], nil
		default:
			// 否则，对字符串进行索引
			str := toString(v)
			l := int64(len(str))
			if index < 0 || index >= l {
				return nil, fmt.Errorf("index out of range for %v: %d", v, index)
			}
			return string(str[index]), nil
		}
	}))
}

// Len 注册长度函数，用于获取字符串、切片或映射的长度
//
// 函数签名: len(value)
// 参数:
//   - value: 要测量长度的值，可以是字符串、切片或映射
//
// 返回值:
//   - 目标对象的长度，作为float64类型
//
// 该函数根据输入值的类型返回适当的长度：
// - 对于字符串，返回字符数
// - 对于切片，返回元素数量
// - 对于映射，返回键值对数量
func Len() {
	MustAddFunction(NewWithPositionalArgs("len", 1, true, func(args ...interface{}) (interface{}, error) {
		var length int
		// 使用反射获取值的类型
		value := reflect.ValueOf(args[0])
		switch value.Kind() {
		case reflect.Slice:
			length = value.Len()
		case reflect.Map:
			length = value.Len()
		default:
			length = len(toString(args[0]))
		}
		return float64(length), nil
	}))
}

// ToUpper 注册转大写函数，将字符串转换为大写形式
//
// 函数签名: to_upper(str)
// 参数:
//   - str: 要转换的字符串
//
// 返回值:
//   - 转换为大写后的字符串
//
// 该函数将输入字符串中的所有字母转换为对应的大写形式
func ToUpper() {
	MustAddFunction(NewWithPositionalArgs("to_upper", 1, true, func(args ...interface{}) (interface{}, error) {
		return strings.ToUpper(toString(args[0])), nil
	}))
}

// ToLower 注册转小写函数，将字符串转换为小写形式
//
// 函数签名: to_lower(str)
// 参数:
//   - str: 要转换的字符串
//
// 返回值:
//   - 转换为小写后的字符串
//
// 该函数将输入字符串中的所有字母转换为对应的小写形式
func ToLower() {
	MustAddFunction(NewWithPositionalArgs("to_lower", 1, true, func(args ...interface{}) (interface{}, error) {
		return strings.ToLower(toString(args[0])), nil
	}))
}

// Sort 注册排序函数，可对字符串字符或多个元素进行排序
//
// 函数签名:
//   - sort(input) - 排序单个字符串中的字符
//   - sort(elements...) - 排序多个元素
//
// 参数:
//   - input: 要排序的单个字符串或数字
//   - elements: 要排序的多个元素
//
// 返回值:
//   - 排序后的字符串或字符串切片
//
// 该函数有两种用法：
// 1. 对单个字符串的字符进行排序，返回排序后的字符串
// 2. 对多个参数进行排序，返回排序后的字符串切片
func Sort() {
	MustAddFunction(NewWithMultipleSignatures("sort", []string{
		"(input string) string",
		"(input number) string",
		"(elements ...interface{}) []interface{}"},
		true,
		func(args ...interface{}) (interface{}, error) {
			argCount := len(args)
			if argCount == 0 {
				return nil, ErrInvalidDslFunction
			} else if argCount == 1 {
				// 处理单个参数情况：对字符串中的字符进行排序
				runes := []rune(toString(args[0]))
				sort.Slice(runes, func(i int, j int) bool {
					return runes[i] < runes[j]
				})
				return string(runes), nil
			} else {
				// 处理多个参数情况：将所有参数转换为字符串并排序
				tokens := make([]string, 0, argCount)
				for _, arg := range args {
					tokens = append(tokens, toString(arg))
				}
				sort.Strings(tokens)
				return tokens, nil
			}
		},
	))
}


// Zip 注册压缩函数，用于创建ZIP压缩文件
//
// 函数签名: zip(file_entry string, content string, ...)
// 参数:
//   - file_entry: 文件条目名称
//   - content: 对应的文件内容
//   - ...: 可以包含多对文件名和内容
//
// 返回值:
//   - []byte: 生成的ZIP文件的二进制数据
//
// 该函数接收成对的参数，每对参数中第一个是文件名，第二个是文件内容，
// 将它们压缩成一个ZIP文件并返回其二进制数据。参数必须是偶数个。
func Zip() {
	MustAddFunction(NewWithMultipleSignatures("zip", []string{"(file_entry string, content string, ... ) []byte"}, true, func(args ...interface{}) (interface{}, error) {
		if len(args) == 0 || len(args)%2 != 0 {
			return nil, fmt.Errorf("zip function requires pairs of file entry and content")
		}

		// 创建内存缓冲区和ZIP写入器
		buf := new(bytes.Buffer)
		zipWriter := zip.NewWriter(buf)

		// 处理每一对文件名和内容
		for i := 0; i < len(args); i += 2 {
			// 验证文件名参数类型
			fileEntry, ok := args[i].(string)
			if !ok {
				return nil, fmt.Errorf("file entry must be a string")
			}
			// 验证文件内容参数类型
			content, ok := args[i+1].(string)
			if !ok {
				return nil, fmt.Errorf("content must be a string")
			}
			
			// 创建文件条目并写入内容
			f, err := zipWriter.Create(fileEntry)
			if err != nil {
				return nil, err
			}
			_, err = f.Write([]byte(content))
			if err != nil {
				return nil, err
			}
		}

		err := zipWriter.Close()
		if err != nil {
			return nil, err
		}

		return buf.Bytes(), nil
	}))
}

// Uniq 注册去重函数，用于移除字符串或切片中的重复元素
//
// 函数签名:
//   - uniq(input) - 去除单个字符串中的重复字符
//   - uniq(elements...) - 去除多个参数中的重复元素
//
// 参数:
//   - input: 要去重的单个字符串或数字
//   - elements: 要去重的多个元素
//
// 返回值:
//   - 去重后的字符串或字符串切片
//
// 该函数有两种用法：
// 1. 对单个字符串中的字符去重，返回不含重复字符的字符串
// 2. 对多个参数进行去重，返回不含重复元素的字符串切片
func Uniq() {
	MustAddFunction(NewWithMultipleSignatures("uniq", []string{
		"(input string) string",
		"(input number) string",
		"(elements ...interface{}) []interface{}"},
		true,
		func(args ...interface{}) (interface{}, error) {
			argCount := len(args)
			if argCount == 0 {
				return nil, ErrInvalidDslFunction
			} else if argCount == 1 {
				// 处理单个参数情况：对字符串中的字符去重
				builder := &strings.Builder{}
				visited := make(map[rune]struct{})
				for _, i := range toString(args[0]) {
					if _, isRuneSeen := visited[i]; !isRuneSeen {
						builder.WriteRune(i)
						visited[i] = struct{}{}
					}
				}
				return builder.String(), nil
			} else {
				// 处理多个参数情况：对多个参数去重
				result := make([]string, 0, argCount)
				visited := make(map[string]struct{})
				for _, i := range args[0:] {
					if _, isStringSeen := visited[toString(i)]; !isStringSeen {
						result = append(result, toString(i))
						visited[toString(i)] = struct{}{}
					}
				}
				return result, nil
			}
		},
	))
}

// Repeat 注册重复函数，用于重复字符串指定次数
//
// 函数签名: repeat(str, count)
// 参数:
//   - str: 要重复的字符串
//   - count: 重复次数
//
// 返回值:
//   - 重复后的字符串
//
// 该函数将输入字符串重复指定次数并返回结果。
// 如果count参数无法转换为整数，将返回错误。
func Repeat() {
	MustAddFunction(NewWithPositionalArgs("repeat", 2, true, func(args ...interface{}) (interface{}, error) {
		count, err := strconv.Atoi(toString(args[1]))
		if err != nil {
			return nil, ErrInvalidDslFunction
		}
		return strings.Repeat(toString(args[0]), count), nil
	}))
}

// Replace 注册替换函数，用于替换字符串中的所有匹配子串
//
// 函数签名: replace(str, old, new)
// 参数:
//   - str: 要处理的原始字符串
//   - old: 要被替换的子串
//   - new: 替换后的新子串
//
// 返回值:
//   - 替换后的字符串
//
// 该函数使用strings.ReplaceAll实现，将字符串中所有指定的子串替换为新子串。
func Replace() {
	MustAddFunction(NewWithPositionalArgs("replace", 3, true, func(args ...interface{}) (interface{}, error) {
		return strings.ReplaceAll(toString(args[0]), toString(args[1]), toString(args[2])), nil
	}))
}

// ReplaceRegex 注册正则替换函数，用于通过正则表达式替换字符串
//
// 函数签名: replace_regex(str, pattern, replacement)
// 参数:
//   - str: 要处理的原始字符串
//   - pattern: 正则表达式模式
//   - replacement: 替换值
//
// 返回值:
//   - 替换后的字符串
//
// 该函数使用正则表达式进行替换，将匹配正则模式的所有子串替换为指定的替换值。
// 如果正则表达式无效，将返回错误。
func ReplaceRegex() {
	MustAddFunction(NewWithPositionalArgs("replace_regex", 3, true, func(args ...interface{}) (interface{}, error) {
		compiled, err := regexp.Compile(toString(args[1]))
		if err != nil {
			return nil, err
		}
		return compiled.ReplaceAllString(toString(args[0]), toString(args[2])), nil
	}))
}

// Trim 注册修剪函数，用于删除字符串首尾指定的字符
//
// 函数签名: trim(str, cutset)
// 参数:
//   - str: 要处理的字符串
//   - cutset: 要移除的字符集合
//
// 返回值:
//   - 处理后的字符串
//
// 该函数使用strings.Trim实现，从字符串的首尾删除包含在cutset中的所有字符。
func Trim() {
	MustAddFunction(NewWithPositionalArgs("trim", 2, true, func(args ...interface{}) (interface{}, error) {
		return strings.Trim(toString(args[0]), toString(args[1])), nil
	}))
}

// TrimLeft 注册左侧修剪函数，用于删除字符串开头指定的字符
//
// 函数签名: trim_left(str, cutset)
// 参数:
//   - str: 要处理的字符串
//   - cutset: 要移除的字符集合
//
// 返回值:
//   - 处理后的字符串
//
// 该函数使用strings.TrimLeft实现，从字符串的开头删除包含在cutset中的所有字符。
func TrimLeft() {
	MustAddFunction(NewWithPositionalArgs("trim_left", 2, true, func(args ...interface{}) (interface{}, error) {
		return strings.TrimLeft(toString(args[0]), toString(args[1])), nil
	}))
}

// TrimRight 注册右侧修剪函数，用于删除字符串结尾指定的字符
//
// 函数签名: trim_right(str, cutset)
// 参数:
//   - str: 要处理的字符串
//   - cutset: 要移除的字符集合
//
// 返回值:
//   - 处理后的字符串
//
// 该函数使用strings.TrimRight实现，从字符串的结尾删除包含在cutset中的所有字符。
func TrimRight() {
	MustAddFunction(NewWithPositionalArgs("trim_right", 2, true, func(args ...interface{}) (interface{}, error) {
		return strings.TrimRight(toString(args[0]), toString(args[1])), nil
	}))
}

// TrimSpace 注册空白修剪函数，用于删除字符串首尾的空白字符
//
// 函数签名: trim_space(str)
// 参数:
//   - str: 要处理的字符串
//
// 返回值:
//   - 处理后的字符串
//
// 该函数使用strings.TrimSpace实现，从字符串的首尾删除所有的空白字符，
// 包括空格、制表符、换行符等。
func TrimSpace() {
	MustAddFunction(NewWithPositionalArgs("trim_space", 1, true, func(args ...interface{}) (interface{}, error) {
		return strings.TrimSpace(toString(args[0])), nil
	}))
}

// TrimPrefix 注册前缀修剪函数，用于删除字符串开头的指定前缀
//
// 函数签名: trim_prefix(str, prefix)
// 参数:
//   - str: 要处理的字符串
//   - prefix: 要移除的前缀
//
// 返回值:
//   - 处理后的字符串
//
// 该函数使用strings.TrimPrefix实现，如果字符串以指定前缀开头，则移除该前缀；
// 否则返回原字符串。
func TrimPrefix() {
	MustAddFunction(NewWithPositionalArgs("trim_prefix", 2, true, func(args ...interface{}) (interface{}, error) {
		return strings.TrimPrefix(toString(args[0]), toString(args[1])), nil
	}))
}

// TrimSuffix 注册后缀修剪函数，用于删除字符串结尾的指定后缀
//
// 函数签名: trim_suffix(str, suffix)
// 参数:
//   - str: 要处理的字符串
//   - suffix: 要移除的后缀
//
// 返回值:
//   - 处理后的字符串
//
// 该函数使用strings.TrimSuffix实现，如果字符串以指定后缀结尾，则移除该后缀；
// 否则返回原字符串。
func TrimSuffix() {
	MustAddFunction(NewWithPositionalArgs("trim_suffix", 2, true, func(args ...interface{}) (interface{}, error) {
		return strings.TrimSuffix(toString(args[0]), toString(args[1])), nil
	}))
}

// Reverse 注册字符串反转函数，用于反转字符串的字符顺序
//
// 函数签名: reverse(str)
// 参数:
//   - str: 要反转的字符串
//
// 返回值:
//   - 反转后的字符串
//
// 该函数使用stringsutil.Reverse实现，将输入字符串的字符顺序完全反转。
// 例如："hello" 反转后变成 "olleh"。
func Reverse() {
	MustAddFunction(NewWithPositionalArgs("reverse", 1, true, func(args ...interface{}) (interface{}, error) {
		return stringsutil.Reverse(toString(args[0])), nil
	}))
}


// Base64 注册Base64编码函数，用于将字符串转换为Base64编码
//
// 函数签名: base64(str)
// 参数:
//   - str: 要编码的字符串
//
// 返回值:
//   - Base64编码后的字符串
//
// 该函数使用标准的Base64编码算法，将输入字符串转换为Base64编码格式。
// 它使用Go标准库中的base64.StdEncoding.EncodeToString实现。
func Base64() {
	MustAddFunction(NewWithPositionalArgs("base64", 1, true, func(args ...interface{}) (interface{}, error) {
		return base64.StdEncoding.EncodeToString([]byte(toString(args[0]))), nil
	}))
}

// Gzip 注册Gzip压缩函数，用于压缩字符串数据
//
// 函数签名: gzip(data)
// 参数:
//   - data: 要压缩的字符串数据
//
// 返回值:
//   - 压缩后的二进制数据（以字符串形式返回）
//
// 该函数使用gzip算法压缩输入的字符串数据，并返回压缩后的二进制结果。
// 压缩后的数据可以使用gzip_decode函数解压缩。
func Gzip() {
	MustAddFunction(NewWithPositionalArgs("gzip", 1, true, func(args ...interface{}) (interface{}, error) {
		buffer := &bytes.Buffer{}
		writer := gzip.NewWriter(buffer)
		if _, err := writer.Write([]byte(args[0].(string))); err != nil {
			_ = writer.Close()
			return "", err
		}
		_ = writer.Close()

		return buffer.String(), nil
	}))
}

// GzipDecode 注册Gzip解压函数，用于解压缩gzip格式的数据
//
// 函数签名: gzip_decode(data, optionalReadLimit)
// 参数:
//   - data: 要解压的gzip格式数据
//   - optionalReadLimit: 可选的读取限制大小（默认为10MB）
//
// 返回值:
//   - 解压后的字符串
//
// 该函数解压由gzip函数压缩的数据。可以指定读取限制大小，防止解压炸弹攻击。
// 如果未指定限制，默认使用DefaultMaxDecompressionSize（10MB）
func GzipDecode() {
	MustAddFunction(NewWithSingleSignature("gzip_decode",
		"(data string, optionalReadLimit int) string",
		true,
		func(args ...interface{}) (interface{}, error) {
			if len(args) == 0 {
				return nil, ErrInvalidDslFunction
			}

			argData := toString(args[0])
			readLimit := DefaultMaxDecompressionSize

			if len(args) > 1 {
				if limit, ok := args[1].(float64); ok {
					readLimit = int64(limit)
				}
			}

			reader, err := gzip.NewReader(strings.NewReader(argData))
			if err != nil {
				return "", err
			}
			limitReader := io.LimitReader(reader, readLimit)

			data, err := io.ReadAll(limitReader)
			if err != nil && err != io.EOF {
				_ = reader.Close()

				return "", err
			}
			_ = reader.Close()

			return string(data), nil
		}))
}


// Zlib 注册Zlib压缩函数，用于压缩字符串数据
//
// 函数签名: zlib(data)
// 参数:
//   - data: 要压缩的字符串数据
//
// 返回值:
//   - 压缩后的二进制数据（以字符串形式返回）
//
// 该函数使用zlib算法压缩输入的字符串数据，并返回压缩后的二进制结果。
// 压缩后的数据可以使用zlib_decode函数解压缩。
func Zlib() {
	MustAddFunction(NewWithPositionalArgs("zlib", 1, true, func(args ...interface{}) (interface{}, error) {
		buffer := &bytes.Buffer{}
		writer := zlib.NewWriter(buffer)
		if _, err := writer.Write([]byte(args[0].(string))); err != nil {
			_ = writer.Close()
			return "", err
		}
		_ = writer.Close()

		return buffer.String(), nil
	}))
}

// ZlibDecode 注册Zlib解压函数，用于解压缩zlib格式的数据
//
// 函数签名: zlib_decode(data, optionalReadLimit)
// 参数:
//   - data: 要解压的zlib格式数据
//   - optionalReadLimit: 可选的读取限制大小（默认为10MB）
//
// 返回值:
//   - 解压后的字符串
//
// 该函数解压由zlib函数压缩的数据。可以指定读取限制大小，防止解压炸弹攻击。
// 如果未指定限制，默认使用DefaultMaxDecompressionSize（10MB）。
func ZlibDecode() {
	MustAddFunction(NewWithSingleSignature("zlib_decode",
		"(data string, optionalReadLimit int) string",
		true,
		func(args ...interface{}) (interface{}, error) {
			if len(args) == 0 {
				return nil, ErrInvalidDslFunction
			}

			argData := toString(args[0])
			readLimit := DefaultMaxDecompressionSize

			if len(args) > 1 {
				if limit, ok := args[1].(float64); ok {
					readLimit = int64(limit)
				}
			}

			reader, err := zlib.NewReader(strings.NewReader(argData))
			if err != nil {
				return "", err
			}
			limitReader := io.LimitReader(reader, readLimit)

			data, err := io.ReadAll(limitReader)
			if err != nil && err != io.EOF {
				_ = reader.Close()

				return "", err
			}
			_ = reader.Close()

			return string(data), nil
		}))
}

// Deflate 注册Deflate压缩函数，用于压缩字符串数据
//
// 函数签名: deflate(data)
// 参数:
//   - data: 要压缩的字符串数据
//
// 返回值:
//   - 压缩后的二进制数据（以字符串形式返回）
//
// 该函数使用deflate算法（RFC 1951）压缩输入的字符串数据，并返回压缩后的二进制结果。
// 压缩后的数据可以使用inflate函数解压缩。使用的是最佳压缩级别（-1）。
func Deflate() {
	MustAddFunction(NewWithPositionalArgs("deflate", 1, true, func(args ...interface{}) (interface{}, error) {
		buffer := &bytes.Buffer{}
		writer, err := flate.NewWriter(buffer, -1)
		if err != nil {
			return "", err
		}
		if _, err := writer.Write([]byte(args[0].(string))); err != nil {
			_ = writer.Close()
			return "", err
		}
		_ = writer.Close()

		return buffer.String(), nil
	}))
}

// Inflate 注册Inflate解压函数，用于解压缩deflate格式的数据
//
// 函数签名: inflate(data, optionalReadLimit)
// 参数:
//   - data: 要解压的deflate格式数据
//   - optionalReadLimit: 可选的读取限制大小（默认为10MB）
//
// 返回值:
//   - 解压后的字符串
//
// 该函数解压由deflate函数压缩的数据。可以指定读取限制大小，防止解压炸弹攻击。
// 如果未指定限制，默认使用DefaultMaxDecompressionSize（10MB）。
func Inflate() {
	MustAddFunction(NewWithSingleSignature("inflate",
		"(data string, optionalReadLimit int) string",
		true,
		func(args ...interface{}) (interface{}, error) {
			if len(args) == 0 {
				return nil, ErrInvalidDslFunction
			}

			argData := toString(args[0])
			readLimit := DefaultMaxDecompressionSize

			if len(args) > 1 {
				if limit, ok := args[1].(float64); ok {
					readLimit = int64(limit)
				}
			}

			reader := flate.NewReader(strings.NewReader(argData))
			limitReader := io.LimitReader(reader, readLimit)

			data, err := io.ReadAll(limitReader)
			if err != nil && err != io.EOF {
				_ = reader.Close()

				return "", err
			}
			_ = reader.Close()

			return string(data), nil
		}))
}

// DateTime 注册日期时间格式化函数，用于格式化时间或当前时间
//
// 函数签名: date_time(dateTimeFormat, optionalUnixTime)
// 参数:
//   - dateTimeFormat: 日期时间格式字符串
//   - optionalUnixTime: 可选的Unix时间戳
//
// 返回值:
//   - 格式化后的日期时间字符串
//
// 该函数格式化指定的时间（如果提供）或当前时间。格式化字符串支持两种模式：
// 1. Go标准库时间格式（如"2006-01-02 15:04:05"）
// 2. 简化格式（如"%Y-%m-%d %H:%M:%S"），其中：
//    - %Y: 年份
//    - %M: 月份
//    - %D 或 %d: 日期
//    - %H 或 %h: 小时
//    - %m: 分钟
//    - %S 或 %s: 秒
func DateTime() {
	MustAddFunction(NewWithSingleSignature("date_time",
		"(dateTimeFormat string, optionalUnixTime interface{}) string",
		false,
		func(arguments ...interface{}) (interface{}, error) {
			// 获取格式化字符串
			dateTimeFormat := toString(arguments[0])
			// 查找格式化片段（如%Y、%m等）
			dateTimeFormatFragment := dateFormatRegex.FindAllStringSubmatch(dateTimeFormat, -1)

			// 验证参数数量
			argumentsSize := len(arguments)
			if argumentsSize < 1 && argumentsSize > 2 {
				return nil, ErrInvalidDslFunction
			}

			// 解析时间，如果没有提供则使用当前时间
			currentTime, err := parseTimeOrNow(arguments)
			if err != nil {
				return nil, err
			}

			// 根据格式化方式选择处理方法
			if len(dateTimeFormatFragment) > 0 {
				// 使用简化格式（如%Y-%m-%d）
				return doSimpleTimeFormat(dateTimeFormatFragment, currentTime, dateTimeFormat)
			} else {
				// 使用Go标准库格式（如2006-01-02）
				return currentTime.Format(dateTimeFormat), nil
			}
		}))
}

// Base64Py 注册Python风格的Base64编码函数
//
// 函数签名: base64_py(str)
// 参数:
//   - str: 要编码的字符串
//
// 返回值:
//   - Python风格的Base64编码字符串
//
// 该函数模拟Python的base64.encode行为，每76个字符后添加换行符("\n")。
func Base64Py() {
	MustAddFunction(NewWithPositionalArgs("base64_py", 1, true, func(args ...interface{}) (interface{}, error) {
		// 首先使用标准Base64编码
		stdBase64 := base64.StdEncoding.EncodeToString([]byte(toString(args[0])))
		// 然后每76个字节插入一个换行符（Python的默认行为）
		return insertInto(stdBase64, 76, '\n'), nil
	}))
}

// Base64Decode 注册Base64解码函数，用于将Base64编码的字符串解码为原始字符串
//
// 函数签名: base64_decode(encodedStr)
// 参数:
//   - encodedStr: Base64编码的字符串
//
// 返回值:
//   - 解码后的原始字符串
//
// 该函数使用标准的Base64解码算法，将Base64编码的字符串转换回原始数据。
// 它使用Go标准库中的base64.StdEncoding.DecodeString实现。
// 如果输入不是有效的Base64编码，将返回解码错误。
func Base64Decode() {
	MustAddFunction(NewWithPositionalArgs("base64_decode", 1, true, func(args ...interface{}) (interface{}, error) {
		data, err := base64.StdEncoding.DecodeString(toString(args[0]))
		return string(data), err
	}))
}

// UrlEncode 注册URL编码函数，用于将字符串编码为URL安全格式
//
// 函数签名: url_encode(s, optionalEncodeAllSpecialChars)
// 参数:
//   - s: 要编码的字符串
//   - optionalEncodeAllSpecialChars: 可选参数，是否编码所有特殊字符，默认为false
//
// 返回值:
//   - URL编码后的字符串
//
// 该函数将字符串编码为URL安全格式。默认情况下，字母、数字和某些安全字符（如'-'、'_'、'.'等）
// 将保持不变，其他字符将被编码为百分号加十六进制形式（如空格变为'%20'）。
// 如果optionalEncodeAllSpecialChars设为true，则只有字母和数字保持不变，所有其他字符都会被编码。
func UrlEncode() {
	MustAddFunction(NewWithSingleSignature("url_encode",
		"(s string, optionalEncodeAllSpecialChars bool) string",
		true,
		func(args ...interface{}) (interface{}, error) {
			var encodeAllChars bool
			// 获取第一个参数（待编码的字符串）
			s := toString(args[0])

			// 检查是否提供了第二个参数（是否编码所有特殊字符）
			if len(args) > 1 {
				switch v := args[1].(type) {
				case bool:
					encodeAllChars = v
				case int, int64:
					encodeAllChars = v == 1
				}
			}

			// 定义字符是否需要转义的判断函数
			shouldEscape := func(c rune, encodeAllChars bool) bool {
				// 判断是否为字母或数字
				isAlphanums := (c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z') || (c >= '0' && c <= '9')
				if encodeAllChars {
					return isAlphanums
				}
				// 默认模式下，字母、数字和一些安全字符不需要转义
				return isAlphanums || (c == '-' || c == '_' || c == '.' || c == '!' || c == '~' || c == '*' || c == '\'' || c == '(' || c == ')')
			}

			// 构建编码后的字符串
			var result strings.Builder
			for _, c := range s {
				if shouldEscape(c, encodeAllChars) {
					// 不需要转义的字符保持原样
					result.WriteRune(c)
				} else {
					// 需要转义的字符转换为%XX格式
					for _, b := range []byte(string(c)) {
						result.WriteString(fmt.Sprintf("%%%02X", b))
					}
				}
			}
			return result.String(), nil
		},
	))
}

// UrlDecode 注册URL解码函数，用于将URL编码的字符串解码为原始字符串
//
// 函数签名: url_decode(encodedStr)
// 参数:
//   - encodedStr: URL编码的字符串
//
// 返回值:
//   - 解码后的原始字符串
//
// 该函数将URL编码的字符串（如包含%XX格式的字符）解码为原始形式。
// 它会将所有的%XX序列（其中XX是十六进制数）转换为对应的ASCII字符。
func UrlDecode() {
	MustAddFunction(NewWithPositionalArgs("url_decode", 1, true, func(args ...interface{}) (interface{}, error) {
		s := toString(args[0])
		var result strings.Builder
		// 解析字符串，处理%XX序列
		for i := 0; i < len(s); i++ {
			if s[i] == '%' && i+2 < len(s) {
				// 尝试将%后的两个字符解析为十六进制数
				if hex, err := strconv.ParseUint(s[i+1:i+3], 16, 8); err == nil {
					// 解析成功，添加对应的ASCII字符
					result.WriteByte(byte(hex))
					i += 2
				} else {
					result.WriteByte(s[i])
				}
			} else {
				result.WriteByte(s[i])
			}
		}
		return result.String(), nil
	}))

}

// HexEncode 注册十六进制编码函数，用于将字符串编码为十六进制表示
//
// 函数签名: hex_encode(str)
// 参数:
//   - str: 要编码的字符串
//
// 返回值:
//   - 十六进制编码后的字符串
//
// 该函数将输入字符串的每个字节转换为两个十六进制字符，生成的结果字符串长度是原始字符串的两倍。
// 它使用Go标准库中的hex.EncodeToString实现。
func HexEncode() {
	MustAddFunction(NewWithPositionalArgs("hex_encode", 1, true, func(args ...interface{}) (interface{}, error) {
		return hex.EncodeToString([]byte(toString(args[0]))), nil
	}))
}

// HexDecode 注册十六进制解码函数，用于将十六进制编码的字符串解码为原始字符串
//
// 函数签名: hex_decode(encodedStr)
// 参数:
//   - encodedStr: 十六进制编码的字符串
//
// 返回值:
//   - 解码后的原始字符串
//
// 该函数将十六进制编码的字符串转换回原始二进制数据。输入必须是有效的十六进制字符串
// （每两个字符表示一个字节），否则将返回解码错误。
// 它使用Go标准库中的hex.DecodeString实现。
func HexDecode() {
	MustAddFunction(NewWithPositionalArgs("hex_decode", 1, true, func(args ...interface{}) (interface{}, error) {
		decodeString, err := hex.DecodeString(toString(args[0]))
		return string(decodeString), err
	}))
}

// Hmac 注册HMAC哈希函数，用于生成消息认证码
//
// 函数签名: hmac(algorithm, data, secretKey)
// 参数:
//   - algorithm: 使用的哈希算法，支持 "sha1"/"sha-1", "sha256"/"sha-256", "sha512"/"sha-512"
//   - data: 要进行哈希的原始数据
//   - secretKey: 用于HMAC算法的密钥
//
// 返回值:
//   - 十六进制编码的HMAC哈希结果
//
// 该函数使用指定的哈希算法和密钥对输入数据生成HMAC（哈希消息认证码），
// 通常用于验证消息的完整性和真实性。返回的哈希值以十六进制字符串形式表示。
func Hmac() {
	MustAddFunction(NewWithPositionalArgs("hmac", 3, true, func(args ...interface{}) (interface{}, error) {
		hashAlgorithm := args[0]
		data := args[1].(string)
		secretKey := args[2].(string)

		var hashFunction func() hash.Hash
		switch hashAlgorithm {
		case "sha1", "sha-1":
			hashFunction = sha1.New
		case "sha256", "sha-256":
			hashFunction = sha256.New
		case "sha512", "sha-512":
			hashFunction = sha512.New
		default:
			return nil, fmt.Errorf("unsupported hash algorithm: '%s'", hashAlgorithm)
		}

		h := hmac.New(hashFunction, []byte(secretKey))
		h.Write([]byte(data))
		return hex.EncodeToString(h.Sum(nil)), nil
	}))
}

// HtmlEscape 注册HTML转义函数，用于转义HTML特殊字符
//
// 函数签名: html_escape(str)
// 参数:
//   - str: 要进行HTML转义的字符串
//
// 返回值:
//   - HTML转义后的字符串
//
// 该函数将字符串中的HTML特殊字符（如 <, >, &, ", ' 等）转换为对应的HTML实体，
func HtmlEscape() {
	MustAddFunction(NewWithPositionalArgs("html_escape", 1, true, func(args ...interface{}) (interface{}, error) {
		return html.EscapeString(toString(args[0])), nil
	}))
}

// HtmlUnEscape 注册HTML反转义函数，用于将HTML实体转换回原始字符
//
// 函数签名: html_unescape(str)
// 参数:
//   - str: 包含HTML实体的字符串
//
// 返回值:
//   - 反转义后的原始字符串
//
// 该函数将HTML实体（如 &lt;, &gt;, &amp;, &quot; 等）转换回对应的原始字符，
// 是HtmlEscape函数的反向操作。它使用Go标准库中的html.UnescapeString实现。
func HtmlUnEscape() {
	MustAddFunction(NewWithPositionalArgs("html_unescape", 1, true, func(args ...interface{}) (interface{}, error) {
		return html.UnescapeString(toString(args[0])), nil
	}))
}

// MD5 注册MD5哈希函数，用于计算字符串的MD5摘要
//
// 函数签名: md5(str)
// 参数:
//   - str: 要计算哈希的字符串
//
// 返回值:
//   - 十六进制编码的MD5哈希值
//
// 该函数计算输入字符串的MD5哈希值，并以十六进制字符串形式返回。
// MD5算法生成一个128位（16字节）的哈希值，转换为十六进制后是32个字符。
func MD5() {
	MustAddFunction(NewWithPositionalArgs("md5", 1, true, func(args ...interface{}) (interface{}, error) {
		return toHexEncodedHash(md5.New(), toString(args[0]))
	}))
}

// Sha512 注册SHA-512哈希函数，用于计算字符串的SHA-512摘要
//
// 函数签名: sha512(str)
// 参数:
//   - str: 要计算哈希的字符串
//
// 返回值:
//   - 十六进制编码的SHA-512哈希值
//
// 该函数计算输入字符串的SHA-512哈希值，并以十六进制字符串形式返回。
// SHA-512算法生成一个512位（64字节）的哈希值，转换为十六进制后是128个字符。
func Sha512() {
	MustAddFunction(NewWithPositionalArgs("sha512", 1, true, func(args ...interface{}) (interface{}, error) {
		return toHexEncodedHash(sha512.New(), toString(args[0]))
	}))
}

// Sha256 注册SHA-256哈希函数，用于计算字符串的SHA-256摘要
//
// 函数签名: sha256(str)
// 参数:
//   - str: 要计算哈希的字符串
//
// 返回值:
//   - 十六进制编码的SHA-256哈希值
//
// 该函数计算输入字符串的SHA-256哈希值，并以十六进制字符串形式返回。
// SHA-256算法生成一个256位（32字节）的哈希值，转换为十六进制后是64个字符。
func Sha256() {
	MustAddFunction(NewWithPositionalArgs("sha256", 1, true, func(args ...interface{}) (interface{}, error) {
		return toHexEncodedHash(sha256.New(), toString(args[0]))
	}))
}

// Sha1 注册SHA-1哈希函数，用于计算字符串的SHA-1摘要
//
// 函数签名: sha1(str)
// 参数:
//   - str: 要计算哈希的字符串
//
// 返回值:
//   - 十六进制编码的SHA-1哈希值
//
// 该函数计算输入字符串的SHA-1哈希值，并以十六进制字符串形式返回。
// SHA-1算法生成一个160位（20字节）的哈希值，转换为十六进制后是40个字符。
func Sha1() {
	MustAddFunction(NewWithPositionalArgs("sha1", 1, true, func(args ...interface{}) (interface{}, error) {
		return toHexEncodedHash(sha1.New(), toString(args[0]))
	}))
}

// Mmh3 注册Murmur3哈希函数，用于计算字符串的Murmur3哈希值
//
// 函数签名: mmh3(str)
// 参数:
//   - str: 要计算哈希的字符串
//
// 返回值:
//   - Murmur3哈希值的字符串表示
//
// 该函数计算输入字符串的Murmur3哈希值，并以字符串形式返回。
func Mmh3() {
	MustAddFunction(NewWithPositionalArgs("mmh3", 1, true, func(args ...interface{}) (interface{}, error) {
		hasher := murmur3.New32WithSeed(0)
		hasher.Write([]byte(fmt.Sprint(args[0])))
		return fmt.Sprintf("%d", int32(hasher.Sum32())), nil
	}))
}


// Contains 注册字符串包含函数，用于检查字符串是否包含指定子串
//
// 函数签名: contains(str, substr)
// 参数:
//   - str: 要检查的字符串
//   - substr: 要查找的子字符串
//
// 返回值:
//   - bool: 如果str包含substr则返回true，否则返回false
//
// 该函数检查第一个字符串中是否包含第二个字符串作为子串。
func Contains() {
	MustAddFunction(NewWithPositionalArgs("contains", 2, true, func(args ...interface{}) (interface{}, error) {
		return strings.Contains(toString(args[0]), toString(args[1])), nil
	}))
}

// ContainsAll 注册全部包含函数，用于检查字符串是否包含所有指定的子串
//
// 函数签名: contains_all(body, substrs...)
// 参数:
//   - body: 要检查的主字符串
//   - substrs: 一个或多个要查找的子字符串
//
// 返回值:
//   - bool: 如果body包含所有substrs，则返回true，否则返回false
//
// 该函数检查主字符串中是否包含所有指定的子字符串。
// 只有当所有子字符串都被找到时才返回true，否则返回false。
func ContainsAll() {
	MustAddFunction(NewWithSingleSignature("contains_all",
		"(body interface{}, substrs ...string) bool",
		true,
		func(arguments ...interface{}) (interface{}, error) {
			body := toString(arguments[0])
			for _, value := range arguments[1:] {
				if !strings.Contains(body, toString(value)) {
					return false, nil
				}
			}
			return true, nil
		}))
}

// ContainsAny 注册任一包含函数，用于检查字符串是否包含任一指定的子串
//
// 函数签名: contains_any(body, substrs...)
// 参数:
//   - body: 要检查的主字符串
//   - substrs: 一个或多个要查找的子字符串
//
// 返回值:
//   - bool: 如果body包含任一substrs，则返回true，否则返回false
//
// 该函数检查主字符串中是否包含任一指定的子字符串。
// 只要有一个子字符串被找到就返回true；如果没有任何子字符串被找到，则返回false。
func ContainsAny() {
	MustAddFunction(NewWithSingleSignature("contains_any",
		"(body interface{}, substrs ...string) bool",
		true,
		func(arguments ...interface{}) (interface{}, error) {
			body := toString(arguments[0])
			for _, value := range arguments[1:] {
				if strings.Contains(body, toString(value)) {
					return true, nil
				}
			}
			return false, nil
		}))
}

// StartsWith 注册前缀检查函数，用于判断字符串是否以指定前缀开始
//
// 函数签名: starts_with(str, prefix...)
// 参数:
//   - str: 要检查的字符串
//   - prefix: 一个或多个可能的前缀
//
// 返回值:
//   - bool: 如果str以任一prefix开头，则返回true，否则返回false
//
// 该函数检查给定字符串是否以任一指定的前缀开始。
// 如果有多个前缀，只要字符串以其中任一前缀开头就返回true。
func StartsWith() {
	MustAddFunction(NewWithSingleSignature("starts_with",
		"(str string, prefix ...string) bool",
		true,
		func(args ...interface{}) (interface{}, error) {
			if len(args) < 2 {
				return nil, ErrInvalidDslFunction
			}
			for _, prefix := range args[1:] {
				if strings.HasPrefix(toString(args[0]), toString(prefix)) {
					return true, nil
				}
			}
			return false, nil
		}))
}

// LineStartsWith 注册行前缀检查函数，用于判断字符串的任一行是否以指定前缀开始
//
// 函数签名: line_starts_with(str, prefix...)
// 参数:
//   - str: 要检查的多行字符串
//   - prefix: 一个或多个可能的前缀
//
// 返回值:
//   - bool: 如果str中任一行以任一prefix开头，则返回true，否则返回false
//
// 该函数将输入字符串按换行符分割为多行，然后检查是否有任一行以任一指定前缀开始。
func LineStartsWith() {
	MustAddFunction(NewWithSingleSignature("line_starts_with",
		"(str string, prefix ...string) bool",
		true,
		func(args ...interface{}) (interface{}, error) {
			if len(args) < 2 {
				return nil, ErrInvalidDslFunction
			}
			for _, line := range strings.Split(toString(args[0]), "\n") {
				for _, prefix := range args[1:] {
					if strings.HasPrefix(line, toString(prefix)) {
						return true, nil
					}
				}
			}
			return false, nil
		}))
}

// EndsWith 注册后缀检查函数，用于判断字符串是否以指定后缀结束
//
// 函数签名: ends_with(str, suffix...)
// 参数:
//   - str: 要检查的字符串
//   - suffix: 一个或多个可能的后缀
//
// 返回值:
//   - bool: 如果str以任一suffix结尾，则返回true，否则返回false
//
// 该函数检查给定字符串是否以任一指定的后缀结束。
// 如果有多个后缀，只要字符串以其中任一后缀结尾就返回true。
func EndsWith() {
	MustAddFunction(NewWithSingleSignature("ends_with",
		"(str string, suffix ...string) bool",
		true,
		func(args ...interface{}) (interface{}, error) {
			if len(args) < 2 {
				return nil, ErrInvalidDslFunction
			}
			for _, suffix := range args[1:] {
				if strings.HasSuffix(toString(args[0]), toString(suffix)) {
					return true, nil
				}
			}
			return false, nil
		}))
}

// LineEndsWith 注册行后缀检查函数，用于判断字符串的任一行是否以指定后缀结束
//
// 函数签名: line_ends_with(str, suffix...)
// 参数:
//   - str: 要检查的多行字符串
//   - suffix: 一个或多个可能的后缀
//
// 返回值:
//   - bool: 如果str中任一行以任一suffix结尾，则返回true，否则返回false
//
// 该函数将输入字符串按换行符分割为多行，然后检查是否有任一行以任一指定后缀结束。
func LineEndsWith() {
	MustAddFunction(NewWithSingleSignature("line_ends_with",
		"(str string, suffix ...string) bool",
		true,
		func(args ...interface{}) (interface{}, error) {
			if len(args) < 2 {
				return nil, ErrInvalidDslFunction
			}
			for _, line := range strings.Split(toString(args[0]), "\n") {
				for _, suffix := range args[1:] {
					if strings.HasSuffix(line, toString(suffix)) {
						return true, nil
					}
				}
			}
			return false, nil
		}))
}

// Concat 注册字符串连接函数，用于连接多个字符串
//
// 函数签名: concat(args...)
// 参数:
//   - args: 要连接的一个或多个字符串
//
// 返回值:
//   - 所有输入字符串连接后的结果
//
// 该函数将所有输入参数转换为字符串，并按顺序连接成一个字符串返回。
// 它不会在字符串之间添加分隔符（这与join函数不同）。
func Concat() {
	MustAddFunction(NewWithSingleSignature("concat",
		"(args ...interface{}) string",
		true,
		func(arguments ...interface{}) (interface{}, error) {
			builder := &strings.Builder{}
			for _, argument := range arguments {
				builder.WriteString(toString(argument))
			}
			return builder.String(), nil
		}))
}

// Split 注册字符串分割函数，用于将字符串分割为多个部分
//
// 函数签名:
//   - split(input, n) - 将字符串分割为n个等长的块
//   - split(input, separator, optionalChunkSize) - 按分隔符分割字符串
//
// 参数:
//   - input: 要分割的字符串
//   - n: 分割为几个等长部分（第一种用法）
//   - separator: 分割字符串（第二种用法）
//   - optionalChunkSize: 可选的分割次数限制（第二种用法）
//
// 返回值:
//   - 分割后的字符串数组
//
// 该函数有两种用法：
// 1. 将字符串分割为指定数量的等长块
// 2. 按指定的分隔符分割字符串，可选择限制分割次数
func Split() {
	MustAddFunction(NewWithMultipleSignatures("split", []string{
		"(input string, n int) []string",
		"(input string, separator string, optionalChunkSize) []string"},
		true,
		func(arguments ...interface{}) (interface{}, error) {
			argumentsSize := len(arguments)
			if argumentsSize == 2 {
				input := toString(arguments[0])
				separatorOrCount := toString(arguments[1])

				count, err := strconv.Atoi(separatorOrCount)
				if err != nil {
					return strings.SplitN(input, separatorOrCount, -1), nil
				}
				return toChunks(input, count), nil
			} else if argumentsSize == 3 {
				input := toString(arguments[0])
				separator := toString(arguments[1])
				count, err := strconv.Atoi(toString(arguments[2]))
				if err != nil {
					return nil, ErrInvalidDslFunction
				}
				return strings.SplitN(input, separator, count), nil
			} else {
				return nil, ErrInvalidDslFunction
			}
		}))
}

// Join 注册字符串连接函数，用于使用指定分隔符连接多个字符串
//
// 函数签名:
//   - join(separator, elements...) - 连接多个元素
//   - join(separator, elements[]) - 连接数组中的元素
//
// 参数:
//   - separator: 用于连接各元素的分隔符
//   - elements: 要连接的多个元素，或者一个字符串数组
//
// 返回值:
//   - 使用指定分隔符连接后的字符串
//
// 该函数有两种用法：
// 1. 连接多个单独的参数，如join(",", "a", "b", "c") 返回 "a,b,c"
// 2. 连接一个字符串数组，如join(",", ["a", "b", "c"]) 返回 "a,b,c"
func Join() {
	MustAddFunction(NewWithMultipleSignatures("join", []string{
		"(separator string, elements ...interface{}) string",
		"(separator string, elements []interface{}) string"},
		true,
		func(arguments ...interface{}) (interface{}, error) {
			argumentsSize := len(arguments)
			if argumentsSize < 2 {
				return nil, ErrInvalidDslFunction
			} else if argumentsSize == 2 {
				separator := toString(arguments[0])
				elements, ok := arguments[1].([]string)

				if !ok {
					return nil, errors.New("cannot cast elements into string")
				}

				return strings.Join(elements, separator), nil
			} else {
				separator := toString(arguments[0])
				elements := arguments[1:argumentsSize]

				stringElements := make([]string, 0, argumentsSize)
				for _, element := range elements {
					if _, ok := element.([]string); ok {
						return nil, errors.New("cannot use join on more than one slice element")
					}

					stringElements = append(stringElements, toString(element))
				}
				return strings.Join(stringElements, separator), nil
			}
		}))
}


// Reg 注册正则表达式匹配函数，用于检查字符串是否匹配正则表达式
//
// 函数签名: regex(pattern, text)
// 参数:
//   - pattern: 正则表达式模式
//   - text: 要检查的文本
//
// 返回值:
//   - bool: 如果文本匹配正则表达式则返回true，否则返回false
//
// 该函数使用正则表达式来检查文本是否匹配指定的模式。
// 使用Go标准库的regexp包实现，支持RE2语法。
// 如果正则表达式无效，将返回错误。
func Reg() {
	MustAddFunction(NewWithPositionalArgs("regex", 2, true, func(args ...interface{}) (interface{}, error) {
		compiled, err := regexp.Compile(toString(args[0]))
		if err != nil {
			return nil, err
		}
		return compiled.MatchString(toString(args[1])), nil
	}))
}

// RegexAll 注册全部正则匹配函数，用于检查字符串是否匹配所有指定的正则表达式
//
// 函数签名: regex_all(text, patterns)
// 参数:
//   - text: 要检查的文本
//   - patterns: 包含多个正则表达式的数组或可变参数列表
//
// 返回值:
//   - bool: 如果文本匹配所有正则表达式则返回true，否则返回false
//
// 该函数检查文本是否同时匹配所有指定的正则表达式模式。
// 只有当所有模式都匹配时才返回true。
func RegexAll() {
	MustAddFunction(NewWithPositionalArgs("regex_all", 2, true, func(args ...interface{}) (interface{}, error) {
		if len(args) < 2 {
			return nil, ErrInvalidDslFunction
		}

		compiled, err := regexp.Compile(toString(args[0]))
		if err != nil {
			return nil, err
		}

		for _, arg := range args[1:] {
			if !compiled.MatchString(toString(arg)) {
				return false, nil
			}
		}

		return true, nil
	}))
}

// RegexAny 注册任一正则匹配函数，用于检查字符串是否匹配任一指定的正则表达式
//
// 函数签名: regex_any(text, patterns)
// 参数:
//   - text: 要检查的文本
//   - patterns: 包含多个正则表达式的数组或可变参数列表
//
// 返回值:
//   - bool: 如果文本匹配任一正则表达式则返回true，否则返回false
//
// 该函数检查文本是否匹配任一指定的正则表达式模式。
// 只要有一个模式匹配，就返回true；如果所有模式都不匹配，则返回false。
func RegexAny() {
	MustAddFunction(NewWithPositionalArgs("regex_any", 2, true, func(args ...interface{}) (interface{}, error) {
		if len(args) < 2 {
			return nil, ErrInvalidDslFunction
		}

		pattern := toString(args[0])
		compiled, err := regexp.Compile(pattern)
		if err != nil {
			return nil, err
		}

		for _, arg := range args[1:] {
			if compiled.MatchString(toString(arg)) {
				return true, nil
			}
		}

		return false, nil
	}))
}

// EqualsAny 注册相等性检查函数，用于检查一个值是否与任一指定值相等
//
// 函数签名: equals_any(value, possibleValues)
// 参数:
//   - value: 要检查的值
//   - possibleValues: 包含多个可能值的数组或可变参数列表
//
// 返回值:
//   - bool: 如果value等于possibleValues中的任一值则返回true，否则返回false
//
// 该函数检查第一个参数是否与第二个参数中的任一值相等。
// 比较使用简单的相等性检查（==），适用于字符串、数字等基本类型。
func EqualsAny() {
	MustAddFunction(NewWithPositionalArgs("equals_any", 2, true, func(args ...interface{}) (interface{}, error) {
		if len(args) < 2 {
			return nil, ErrInvalidDslFunction
		}

		s := toString(args[0])

		for _, arg := range args[1:] {
			if toString(arg) == s {
				return true, nil
			}
		}

		return false, nil
	}))
}

// RemoveBadChars 注册字符移除函数，用于从字符串中删除指定的字符
//
// 函数签名: remove_bad_chars(input, badChars)
// 参数:
//   - input: 要处理的输入字符串
//   - badChars: 需要从输入中移除的字符集合
//
// 返回值:
//   - 移除指定字符后的字符串
//
// 该函数从输入字符串中删除所有出现在badChars中的字符。
// 它使用TrimAll辅助函数实现，对每个要移除的字符执行完整的字符串替换。
func RemoveBadChars() {
	MustAddFunction(NewWithPositionalArgs("remove_bad_chars", 2, true, func(args ...interface{}) (interface{}, error) {
		input := toString(args[0])
		badChars := toString(args[1])
		return TrimAll(input, badChars), nil
	}))
}

// RandChar 注册随机字符函数，用于生成随机字符
//
// 函数签名: rand_char(optionalCharSet)
// 参数:
//   - optionalCharSet: 可选的字符集合，默认使用字母和数字
//
// 返回值:
//   - 从指定或默认字符集中随机选择的一个字符
//
// 该函数从指定的字符集（或默认的字母数字字符集）中随机选择并返回一个字符。
func RandChar() {
	MustAddFunction(NewWithSingleSignature("rand_char",
		"(optionalCharSet string) string",
		false,
		func(args ...interface{}) (interface{}, error) {
			// 默认使用字母和数字作为字符集
			charSet := letters + numbers

			argSize := len(args)
			if argSize != 0 && argSize != 1 {
				return nil, ErrInvalidDslFunction
			}

			// 如果提供了自定义字符集
			if argSize >= 1 {
				inputCharSet := toString(args[0])
				if strings.TrimSpace(inputCharSet) != "" {
					charSet = inputCharSet
				}
			}
			rint, err := randint.IntN(len(charSet))
			return string(charSet[rint]), err
		}))
}

// RandBase 注册基本随机字符串函数，用于生成指定长度的随机字符串
//
// 函数签名: rand_base(length, optionalCharSet)
// 参数:
//   - length: 要生成的随机字符串长度
//   - optionalCharSet: 可选的字符集合，默认使用字母和数字
//
// 返回值:
//   - 生成的随机字符串
//
// 该函数生成一个指定长度的随机字符串。默认使用字母和数字作为字符集，
// 也可以提供自定义字符集。适用于生成随机标识符、密码等。
func RandBase() {
	MustAddFunction(NewWithSingleSignature("rand_base",
		"(length uint, optionalCharSet string) string",
		false,
		func(args ...interface{}) (interface{}, error) {
			var length int
			charSet := letters + numbers

			argSize := len(args)
			if argSize < 1 || argSize > 3 {
				return nil, ErrInvalidDslFunction
			}

			length = int(args[0].(float64))

			if argSize == 2 {
				inputCharSet := toString(args[1])
				if strings.TrimSpace(inputCharSet) != "" {
					charSet = inputCharSet
				}
			}
			return RandSeq(charSet, length), nil
		}))
}

// RandTextAlp 注册字母数字随机文本函数，用于生成字母数字字符的随机文本
//
// 函数签名: rand_text_alphanumeric(length, optionalBadChars)
// 参数:
//   - length: 要生成的随机文本长度
//   - optionalBadChars: 可选的要排除的字符集合
//
// 返回值:
//   - 生成的随机字母数字文本
//
// 该函数生成包含字母和数字的随机文本，可以指定要排除的字符。
func RandTextAlp() {
	MustAddFunction(NewWithSingleSignature("rand_text_alphanumeric",
		"(length uint, optionalBadChars string) string",
		false,
		func(args ...interface{}) (interface{}, error) {
			length := 0
			badChars := ""

			argSize := len(args)
			if argSize != 1 && argSize != 2 {
				return nil, ErrInvalidDslFunction
			}

			length = int(args[0].(float64))

			if argSize == 2 {
				badChars = toString(args[1])
			}
			chars := TrimAll(letters+numbers, badChars)
			return RandSeq(chars, length), nil
		}))

}

// RandTextAlpha 注册纯字母随机文本函数，用于生成仅包含字母的随机文本
//
// 函数签名: rand_text_alpha(length, optionalBadChars)
// 参数:
//   - length: 要生成的随机文本长度
//   - optionalBadChars: 可选的要排除的字符集合
//
// 返回值:
//   - 生成的仅包含字母的随机文本
//
// 该函数生成只包含字母（不包含数字或其他字符）的随机文本，
func RandTextAlpha() {
	MustAddFunction(NewWithSingleSignature("rand_text_alpha",
		"(length uint, optionalBadChars string) string",
		false,
		func(args ...interface{}) (interface{}, error) {
			var length int
			badChars := ""

			argSize := len(args)
			if argSize != 1 && argSize != 2 {
				return nil, ErrInvalidDslFunction
			}

			length = int(args[0].(float64))

			if argSize == 2 {
				badChars = toString(args[1])
			}
			chars := TrimAll(letters, badChars)
			return RandSeq(chars, length), nil
		}))
}

// RandTextNumeric 注册数字随机文本函数，用于生成数字字符的随机文本
//
// 函数签名: rand_text_numeric(length, optionalBadNumbers)
// 参数:
//   - length: 要生成的随机文本长度
//   - optionalBadNumbers: 可选的要排除的数字集合
//
// 返回值:
//   - 生成的随机数字文本
//
// 该函数生成包含数字的随机文本，可以指定要排除的数字。
func RandTextNumeric() {
	MustAddFunction(NewWithSingleSignature("rand_text_numeric",
		"(length uint, optionalBadNumbers string) string",
		false,
		func(args ...interface{}) (interface{}, error) {
			argSize := len(args)
			if argSize != 1 && argSize != 2 {
				return nil, ErrInvalidDslFunction
			}

			length := int(args[0].(float64))
			badNumbers := ""

			if argSize == 2 {
				badNumbers = toString(args[1])
			}

			chars := TrimAll(numbers, badNumbers)
			return RandSeq(chars, length), nil
		}))
}

// RandInt 注册整数随机函数，用于生成指定范围内的随机整数
//
// 函数签名: rand_int(optionalMin, optionalMax)
// 参数:
//   - optionalMin: 可选的最小值，默认为0
//   - optionalMax: 可选的最大值，默认为math.MaxInt32
//
// 返回值:
//   - 生成的随机整数
//
// 该函数生成一个指定范围内的随机整数。如果没有提供最小值和最大值，则默认生成0到math.MaxInt32之间的随机整数。
func RandInt() {
	MustAddFunction(NewWithSingleSignature("rand_int",
		"(optionalMin, optionalMax uint) int",
		false,
		func(args ...interface{}) (interface{}, error) {
			argSize := len(args)
			if argSize > 2 {
				return nil, ErrInvalidDslFunction
			}

			min := 0
			max := math.MaxInt32

			if argSize >= 1 {
				min = int(args[0].(float64))
			}
			if argSize == 2 {
				max = int(args[1].(float64))
			}

			rint, err := randint.IntN(max - min)
			return rint + min, err
		}))
}

// RandIp 注册IP地址随机函数，用于生成指定CIDR范围内的随机IP地址
//
// 函数签名: rand_ip(cidr ...)
// 参数:
//   - cidr: 一个或多个CIDR格式的IP地址
//
// 返回值:
//   - 生成的随机IP地址
//
// 该函数生成一个指定CIDR范围内的随机IP地址。如果没有提供CIDR，则返回错误。
func RandIp() {
	MustAddFunction(NewWithSingleSignature("rand_ip",
		"(cidr ...string) string",
		false,
		func(args ...interface{}) (interface{}, error) {
			if len(args) == 0 {
				return nil, ErrInvalidDslFunction
			}
			var cidrs []string
			for _, arg := range args {
				cidrs = append(cidrs, arg.(string))
			}
			return GetRandomIPWithCidr(cidrs...)
		}))
}

// GenJavaGadget 注册Java Gadget生成函数，用于生成Java Gadget
//
// 函数签名: generate_java_gadget(gadget, cmd, encoding)
// 参数:
//   - gadget: Java Gadget的类名
//   - cmd: 要执行的命令
//   - encoding: 编码方式
//
// 返回值:
//   - 生成的Java Gadget
//
// 该函数生成一个Java Gadget，用于执行指定的命令。
func GenJavaGadget() {
	MustAddFunction(NewWithPositionalArgs("generate_java_gadget", 3, true, func(args ...interface{}) (interface{}, error) {
		gadget := args[0].(string)
		cmd := args[1].(string)
		encoding := args[2].(string)
		data := deserialization.GenerateJavaGadget(gadget, cmd, encoding)
		return data, nil
	}))
}

// UnixTime 注册Unix时间函数，用于获取当前时间或指定秒数的Unix时间
//
// 函数签名: unix_time(optionalSeconds)
// 参数:
//   - optionalSeconds: 可选的秒数，默认为0
//
// 返回值:
//   - 当前时间或指定秒数的Unix时间
//
// 该函数返回当前时间或指定秒数的Unix时间。如果没有提供秒数，则返回当前时间的Unix时间。
func UnixTime() {
	MustAddFunction(NewWithSingleSignature("unix_time",
		"(optionalSeconds uint) float64",
		false,
		func(args ...interface{}) (interface{}, error) {
			seconds := 0

			argSize := len(args)
			if argSize != 0 && argSize != 1 {
				return nil, ErrInvalidDslFunction
			} else if argSize == 1 {
				seconds = int(args[0].(float64))
			}

			offset := time.Now().Add(time.Duration(seconds) * time.Second)
			return float64(offset.Unix()), nil
		}))
}

// ToUnixTime 注册Unix时间转换函数，用于将字符串转换为Unix时间戳
//
// 函数签名: to_unix_time(input, optionalLayout)
// 参数:
//   - input: 要转换的时间字符串或数字
//   - optionalLayout: 可选的时间格式布局，默认尝试多种常见格式
//
// 返回值:
//   - int64: 时间的Unix时间戳（秒）
//
// 该函数尝试将输入的字符串解析为时间，然后转换为Unix时间戳（自1970年1月1日起的秒数）。
// 如果输入是数字，则直接将其转换为Unix时间戳。
// 如果未指定布局，函数会尝试使用多种常见的日期时间格式进行解析。
func ToUnixTime() {
	MustAddFunction(NewWithSingleSignature("to_unix_time",
		"(input string, optionalLayout string) int64",
		true,
		func(args ...interface{}) (interface{}, error) {
			input := toString(args[0])

			nr, err := strconv.ParseFloat(input, 64)
			if err == nil {
				return int64(nr), nil
			}

			if len(args) == 1 {
				for _, layout := range defaultDateTimeLayouts {
					parsedTime, err := time.Parse(layout, input)
					if err == nil {
						return parsedTime.Unix(), nil
					}
				}
				return nil, fmt.Errorf("could not parse the current input with the default layouts")
			} else if len(args) == 2 {
				layout := toString(args[1])
				parsedTime, err := time.Parse(layout, input)
				if err != nil {
					return nil, fmt.Errorf("could not parse the current input with the '%s' layout", layout)
				}
				return parsedTime.Unix(), err
			} else {
				return nil, ErrInvalidDslFunction
			}
		}))
}

// WaitFor 注册等待函数，用于在执行过程中暂停指定时间
//
// 函数签名: wait_for(seconds)
// 参数:
//   - seconds: 要等待的秒数
//
// 返回值:
//   - 等待完成后返回true
func WaitFor() {
	MustAddFunction(NewWithSingleSignature("wait_for",
		"(seconds uint)",
		false,
		func(args ...interface{}) (interface{}, error) {
			if len(args) != 1 {
				return nil, ErrInvalidDslFunction
			}
			seconds := args[0].(float64)
			time.Sleep(time.Duration(seconds) * time.Second)
			return true, nil
		}))
}

// CompareVersion 注册版本比较函数，用于比较语义化版本号
//
// 函数签名: compare_versions(firstVersion, constraints...)
// 参数:
//   - firstVersion: 要比较的版本号
//   - constraints: 一个或多个版本约束条件
//
// 返回值:
//   - 如果版本满足约束条件则返回true，否则返回false
//
// 该函数使用语义化版本规则比较版本号。约束条件支持多种格式，如：
// - "= 1.0.0": 等于1.0.0
// - "> 1.0.0": 大于1.0.0
// - ">= 1.0.0": 大于等于1.0.0
// - "< 1.0.0": 小于1.0.0
// - "<= 1.0.0": 小于等于1.0.0
// 还支持组合约束条件，如">= 1.0.0, < 2.0.0"表示版本需在1.0.0和2.0.0之间。
func CompareVersion() {
	MustAddFunction(NewWithSingleSignature("compare_versions",
		"(firstVersion, constraints ...string) bool",
		true,
		func(args ...interface{}) (interface{}, error) {
			if len(args) < 2 {
				return nil, ErrInvalidDslFunction
			}

			firstParsed, parseErr := version.NewVersion(toString(args[0]))
			if parseErr != nil {
				return nil, errors.New("error parsing argument value")
			}

			var versionConstraints []string
			for _, constraint := range args[1:] {
				versionConstraints = append(versionConstraints, toString(constraint))
			}
			constraint, constraintErr := version.NewConstraint(strings.Join(versionConstraints, ","))
			if constraintErr != nil {
				return nil, constraintErr
			}
			result := constraint.Check(firstParsed)
			return result, nil
		}))
}

// Padding 注册字符串填充函数，用于在字符串前后添加填充字符
//
// 函数签名: padding(str, padChar, length, mode)
// 参数:
//   - str: 要填充的原始字符串
//   - padChar: 用于填充的字符
//   - length: 填充后的目标长度
//   - mode: 填充模式，可以是"prefix"（前缀填充）或"suffix"（后缀填充）
//
// 返回值:
//   - 填充后的字符串
//
// 该函数用指定的字符填充字符串，直到达到指定的长度。
// 填充可以在字符串前面（prefix模式）或后面（suffix模式）进行。
// 如果原始字符串长度已经达到或超过目标长度，则不会进行填充。
func Padding() {
	MustAddFunction(NewWithPositionalArgs("padding", 4, true, func(args ...interface{}) (interface{}, error) {
		// padding('Test String', 'A', 50, 'prefix') // will pad "Test String" up to 50 characters with "A" as padding byte, prefixing it.
		bLen := 0
		switch value := args[2].(type) {
		case float64:
			bLen = int(value)
		case int:
			bLen = value
		default:
			strLen := toString(args[2])
			floatVal, err := strconv.ParseFloat(strLen, 64)
			if err != nil {
				return nil, err
			}
			bLen = int(floatVal)
		}
		if bLen == 0 {
			return nil, errors.New("invalid padding length")
		}
		bByte := []byte(toString(args[1]))
		if len(bByte) == 0 {
			return nil, errors.New("invalid padding byte")
		}
		bData := []byte(toString(args[0]))
		dataLen := len(bData)
		if dataLen >= bLen {
			return toString(bData), nil // Note: if given string is longer than the desired length, it will not be truncated
		}

		padMode, ok := args[3].(string)
		if !ok || (padMode != "prefix" && padMode != "suffix") {
			return nil, errors.New("padding mode must be 'prefix' or 'suffix'")
		}

		paddingLen := bLen - dataLen
		padding := make([]byte, paddingLen)
		for i := 0; i < paddingLen; i++ {
			padding[i] = bByte[i%len(bByte)]
		}

		if padMode == "prefix" {
			return toString(append(padding, bData...)), nil
		} else { // suffix
			return toString(append(bData, padding...)), nil
		}
	}))
}

// PrintDebug 注册调试打印函数，用于输出调试信息
//
// 函数签名: print_debug(args...)
// 参数:
//   - args: 要打印的一个或多个值
//
// 返回值:
//   - 成功打印返回true
//
// 该函数用于在执行过程中打印调试信息。如果设置了PrintDebugCallback回调函数，
func PrintDebug() {
	MustAddFunction(NewWithSingleSignature("print_debug",
		"(args ...interface{})",
		false,
		func(args ...interface{}) (interface{}, error) {
			if len(args) < 1 {
				return nil, ErrInvalidDslFunction
			}
			if PrintDebugCallback != nil {
				if err := PrintDebugCallback(args...); err != nil {
					return nil, err
				}
			} else {
				gologger.Info().Msgf("print_debug value: %s", fmt.Sprint(args))
			}
			return true, nil
		}))
}

// ToNumber 注册数值转换函数，用于将字符串转换为数字
//
// 函数签名: to_number(str)
// 参数:
//   - str: 要转换为数字的字符串
//
// 返回值:
//   - 转换后的浮点数值
//
// 该函数尝试将输入字符串解析为数值。它首先尝试将其解析为整数，
// 如果失败则尝试解析为浮点数。如果两种解析都失败，则返回错误。
func ToNumber() {
	MustAddFunction(NewWithPositionalArgs("to_number", 1, true, func(args ...interface{}) (interface{}, error) {
		argStr := toString(args[0])
		if govalidator.IsInt(argStr) {
			sint, err := strconv.Atoi(argStr)
			return float64(sint), err
		} else if govalidator.IsFloat(argStr) {
			sint, err := strconv.ParseFloat(argStr, 64)
			return float64(sint), err
		}
		return nil, fmt.Errorf("%v could not be converted to int", argStr)
	}))
}

// ToString 注册字符串转换函数，用于将任意值转换为字符串
//
// 函数签名: to_string(value)
// 参数:
//   - value: 要转换为字符串的值
//
// 返回值:
//   - 转换后的字符串
//
func ToString() {
	MustAddFunction(NewWithPositionalArgs("to_string", 1, true, func(args ...interface{}) (interface{}, error) {
		return toString(args[0]), nil
	}))
}

// DecToHex 注册十进制转十六进制函数，用于将十进制数值转换为十六进制字符串
//
// 函数签名: dec_to_hex(number)
// 参数:
//   - number: 要转换的十进制数值
//
// 返回值:
//   - 转换后的十六进制字符串
//
// 该函数将十进制数字转换为其十六进制表示形式。输入必须是有效的数值，
// 否则将返回错误。结果不包含"0x"前缀
func DecToHex() {
	MustAddFunction(NewWithPositionalArgs("dec_to_hex", 1, true, func(args ...interface{}) (interface{}, error) {
		if number, ok := args[0].(float64); ok {
			hexNum := strconv.FormatInt(int64(number), 16)
			return toString(hexNum), nil
		}
		return nil, fmt.Errorf("invalid number: %T", args[0])
	}))
}

// HexToDec 注册十六进制转十进制函数，用于将十六进制字符串转换为十进制数值
//
// 函数签名: hex_to_dec(hexStr)
// 参数:
//   - hexStr: 要转换的十六进制字符串
//
// 返回值:
//   - 转换后的十进制数值
//
// 该函数将十六进制字符串转换为对应的十进制数值。输入可以有"0x"前缀，也可以没有。
func HexToDec() {
	MustAddFunction(NewWithPositionalArgs("hex_to_dec", 1, true, func(args ...interface{}) (interface{}, error) {
		return stringNumberToDecimal(args, "0x", 16)
	}))
}

// OctToDec 注册八进制转十进制函数，用于将八进制字符串转换为十进制数值
//
// 函数签名: oct_to_dec(octStr)
// 参数:
//   - octStr: 要转换的八进制字符串
//
// 返回值:
//   - 转换后的十进制数值
//
// 该函数将八进制字符串转换为对应的十进制数值。输入可以有"0o"前缀，也可以没有。
func OctToDec() {
	MustAddFunction(NewWithPositionalArgs("oct_to_dec", 1, true, func(args ...interface{}) (interface{}, error) {
		return stringNumberToDecimal(args, "0o", 8)
	}))
}

// BinToDec 注册二进制转十进制函数，用于将二进制字符串转换为十进制数值
//
// 函数签名: bin_to_dec(binStr)
// 参数:
//   - binStr: 要转换的二进制字符串
//
// 返回值:
//   - 转换后的十进制数值
//
// 该函数将二进制字符串转换为对应的十进制数值。输入可以有"0b"前缀，也可以没有。
func BinToDec() {
	MustAddFunction(NewWithPositionalArgs("bin_to_dec", 1, true, func(args ...interface{}) (interface{}, error) {
		return stringNumberToDecimal(args, "0b", 2)
	}))
}


// SubStr 注册子字符串提取函数，用于从字符串中提取子串
//
// 函数签名: substr(str, start, optionalEnd)
// 参数:
//   - str: 源字符串
//   - start: 起始位置（基于0的索引）
//   - optionalEnd: 可选的结束位置（不包含）
//
// 返回值:
//   - 提取的子字符串
//
// 该函数从源字符串中提取指定范围的子字符串。如果只提供起始位置，
// 则提取从该位置到字符串末尾的所有字符。如果同时提供起始和结束位置，
// 则提取这个范围内的字符（包含起始位置，不包含结束位置）。
func SubStr() {
	MustAddFunction(NewWithSingleSignature("substr",
		"(str string, start int, optionalEnd int)",
		true,
		func(args ...interface{}) (interface{}, error) {
			if len(args) < 2 {
				return nil, ErrInvalidDslFunction
			}
			argStr := toString(args[0])
			if len(argStr) == 0 {
				return nil, errors.New("empty string")
			}
			start, err := strconv.Atoi(toString(args[1]))
			if err != nil {
				return nil, errors.New("invalid start position")
			}
			if start > len(argStr) {
				return nil, errors.New("start position bigger than slice length")
			}
			if len(args) == 2 {
				return argStr[start:], nil
			}

			end, err := strconv.Atoi(toString(args[2]))
			if err != nil {
				return nil, errors.New("invalid end position")
			}
			if end < 0 {
				return nil, errors.New("negative end position")
			}
			if end < start {
				return nil, errors.New("end position before start")
			}
			if end > len(argStr) {
				return nil, errors.New("end position bigger than slice length start")
			}
			return argStr[start:end], nil
		}))
}

// AesCBC 注册AES-CBC加密函数，用于使用AES-CBC模式加密数据
//
// 函数签名: aes_cbc(plaintext, key, iv)
// 参数:
//   - plaintext: 要加密的明文
//   - key: 加密密钥
//   - iv: 初始化向量
//
// 返回值:
//   - 加密后的二进制数据
//
// 该函数使用AES-CBC模式加密提供的明文。密钥长度必须为16、24或32字节
// （分别对应AES-128、AES-192或AES-256）。初始化向量(IV)必须为16字节。
func AesCBC() {
	MustAddFunction(NewWithPositionalArgs("aes_cbc", 3, false, func(args ...interface{}) (interface{}, error) {
		bKey := []byte(args[1].(string))
		bIV := []byte(args[2].(string))
		bPlaintext := pkcs5padding([]byte(args[0].(string)), aes.BlockSize, len(args[0].(string)))
		block, _ := aes.NewCipher(bKey)
		ciphertext := make([]byte, len(bPlaintext))
		mode := cipher.NewCBCEncrypter(block, bIV)
		mode.CryptBlocks(ciphertext, bPlaintext)
		return ciphertext, nil
	}))
}

// AesGCM 注册AES-GCM加密函数，用于使用AES-GCM模式加密数据
//
// 函数签名: aes_gcm(key, plaintext)
// 参数:
//   - key: 加密密钥
//   - plaintext: 要加密的明文
//
// 返回值:
//   - 加密后的二进制数据，包含随机生成的nonce
//
// 该函数使用AES-GCM模式加密提供的明文。密钥长度必须为16、24或32字节
// （分别对应AES-128、AES-192或AES-256）。GCM模式除了提供加密外，
// 还提供认证功能，可以检测加密数据是否被篡改。函数会自动随机生成适当大小的nonce，
// 并将其作为返回数据的前缀。
func AesGCM() {
	MustAddFunction(NewWithPositionalArgs("aes_gcm", 2, false, func(args ...interface{}) (interface{}, error) {
		key := args[0].(string)
		value := args[1].(string)

		c, err := aes.NewCipher([]byte(key))
		if nil != err {
			return "", err
		}
		gcm, err := cipher.NewGCM(c)
		if nil != err {
			return "", err
		}

		nonce := make([]byte, gcm.NonceSize())

		if _, err = rand.Read(nonce); err != nil {
			return "", err
		}
		data := gcm.Seal(nonce, nonce, []byte(value), nil)
		return data, nil
	}))
}

// GenerateJwt 注册JWT生成函数，用于创建JSON Web Token
//
// 函数签名: generate_jwt(jsonString, algorithm, optionalSignature, optionalMaxAgeUnix)
// 参数:
//   - jsonStr: 要压缩的JSON字符串
//
// 返回值:
//   - 压缩后的JSON字符串
//
// 该函数将JSON字符串解析后重新序列化，移除所有不必要的空白字符，
// 生成紧凑的、无格式化的JSON字符串。如果输入不是有效的JSON，将返回错误。
func GenerateJwt() {
	MustAddFunction(NewWithSingleSignature("generate_jwt",
		"(jsonString, algorithm, optionalSignature string, optionalMaxAgeUnix interface{}) string",
		true,
		func(args ...interface{}) (interface{}, error) {
			var algorithm string
			var optionalSignature []byte
			var optionalMaxAgeUnix time.Time

			var signOpts []jwt.SignOption
			var jsonData jwt.Map

			argSize := len(args)

			if argSize < 2 || argSize > 4 {
				return nil, ErrInvalidDslFunction
			}
			jsonString := args[0].(string)

			err := json.Unmarshal([]byte(jsonString), &jsonData)
			if err != nil {
				return nil, err
			}

			var jwtAlgorithm jwt.Alg
			alg := args[1].(string)
			algorithm = strings.ToUpper(alg)

			switch algorithm {
			case "":
				jwtAlgorithm = jwt.NONE
			case "HS256":
				jwtAlgorithm = jwt.HS256
			case "HS384":
				jwtAlgorithm = jwt.HS384
			case "HS512":
				jwtAlgorithm = jwt.HS512
			case "RS256":
				jwtAlgorithm = jwt.RS256
			case "RS384":
				jwtAlgorithm = jwt.RS384
			case "RS512":
				jwtAlgorithm = jwt.RS512
			case "PS256":
				jwtAlgorithm = jwt.PS256
			case "PS384":
				jwtAlgorithm = jwt.PS384
			case "PS512":
				jwtAlgorithm = jwt.PS512
			case "ES256":
				jwtAlgorithm = jwt.ES256
			case "ES384":
				jwtAlgorithm = jwt.ES384
			case "ES512":
				jwtAlgorithm = jwt.ES512
			case "EDDSA":
				jwtAlgorithm = jwt.EdDSA
			}

			if isjwtAlgorithmNone(alg) {
				jwtAlgorithm = &algNONE{algValue: alg}
			}
			if jwtAlgorithm == nil {
				return nil, fmt.Errorf("invalid algorithm: %s", algorithm)
			}

			if argSize > 2 {
				optionalSignature = []byte(args[2].(string))
			}

			if argSize > 3 {
				times := make([]interface{}, 2)
				times[0] = nil
				times[1] = args[3]

				optionalMaxAgeUnix, err = parseTimeOrNow(times)
				if err != nil {
					return nil, err
				}

				duration := time.Until(optionalMaxAgeUnix)
				signOpts = append(signOpts, jwt.MaxAge(duration))
			}

			return jwt.Sign(jwtAlgorithm, optionalSignature, jsonData, signOpts...)
		}))
}

func JsonMinify() {
	MustAddFunction(NewWithPositionalArgs("json_minify", 1, true, func(args ...interface{}) (interface{}, error) {
		var data map[string]interface{}

		err := json.Unmarshal([]byte(args[0].(string)), &data)
		if err != nil {
			return nil, err
		}

		minified, err := json.Marshal(data)
		if err != nil {
			return nil, err
		}

		return string(minified), nil
	}))
}

// JsonPrettify 注册JSON美化函数，用于将JSON字符串格式化为易读格式
//
// 函数签名: json_prettify(jsonStr)
// 参数:
//   - jsonStr: 要美化的JSON字符串
//
// 返回值:
//   - 格式化后的JSON字符串
//
// 该函数将JSON字符串重新格式化，添加适当的缩进和换行，使其更易于阅读。
// 格式化使用4个空格作为缩进。如果输入不是有效的JSON，将返回错误。
func JsonPrettify() {
	MustAddFunction(NewWithPositionalArgs("json_prettify", 1, true, func(args ...interface{}) (interface{}, error) {
		var buf bytes.Buffer

		err := json.Indent(&buf, []byte(args[0].(string)), "", "    ")
		if err != nil {
			return nil, err
		}

		return buf.String(), nil
	}))
}

func IpFormat() {
	MustAddFunction(NewWithPositionalArgs("ip_format", 2, true, func(args ...interface{}) (interface{}, error) {
		ipFormat, err := strconv.ParseInt(toString(args[1]), 10, 64)
		if err != nil {
			return nil, err
		}
		if ipFormat <= 0 || ipFormat > 11 {
			return nil, fmt.Errorf("invalid format, format must be in range 1-11")
		}
		formattedIps := cidr.AlterIP(toString(args[0]), []string{toString(args[1])}, 3, false)
		if len(formattedIps) == 0 {
			return nil, fmt.Errorf("no formatted IP returned")
		}
		return formattedIps[0], nil
	}))
}

// LLMPrompt 注册LLM提示函数，用于向大型语言模型发送提示并获取回复
//
// 函数签名: llm_prompt(prompt, optionalModel)
// 参数:
//   - prompt: 发送给语言模型的提示文本
//   - optionalModel: 可选的模型名称，默认使用GPT4oMini
//
// 返回值:
//   - 语言模型生成的回复文本
//
// 该函数将提示发送到大型语言模型（如GPT系列），并返回模型生成的回复。
// 如果未指定模型，将默认使用openai.GPT4oMini。这对于在DSL中动态获取
func LLMPrompt() {
	MustAddFunction(NewWithSingleSignature("llm_prompt",
		"(prompt string, optionalModel string) string",
		false,
		func(args ...interface{}) (interface{}, error) {
			if len(args) < 1 {
				return nil, ErrInvalidDslFunction
			}

			prompt, ok := args[0].(string)
			if !ok {
				return nil, errors.New("invalid prompt")
			}

			model := openai.GPT4oMini // default model
			if len(args) == 2 {
				if model, ok = args[1].(string); !ok {
					return nil, errors.New("invalid model")
				}
			}

			return llm.Query(prompt, model)
		}))
}

func Unpack() {
	MustAddFunction(NewWithPositionalArgs("unpack", 2, true, func(args ...interface{}) (interface{}, error) {
		// format as string (ref: https://docs.python.org/3/library/struct.html#format-characters)
		format, ok := args[0].(string)
		if !ok {
			return nil, errors.New("invalid format")
		}
		// binary packed data
		data, ok := args[1].(string)
		if !ok {
			return nil, errors.New("invalid data")
		}
		// convert flat format into slice (eg. ">I" => [">","I"])
		var formatParts []string
		for idx := range format {
			formatParts = append(formatParts, string(format[idx]))
		}
		// the dsl function supports unpacking only one type at a time
		unpackedData, err := gostruct.UnPack(formatParts, []byte(data))
		if len(unpackedData) > 0 {
			return unpackedData[0], err
		}
		return nil, errors.New("no result")
	}))
}

// XOR 注册异或操作函数，用于对多个字符串或字节数组执行按位异或操作
//
// 函数签名: xor(args...)
// 参数:
//   - args: 两个或更多的字符串或字节数组，长度必须相同
//
// 返回值:
//   - 异或操作后的字节数组
//
// 该函数对输入的所有参数执行按位异或(XOR)操作。所有参数必须具有相同的长度。
func XOR() {
	MustAddFunction(NewWithSingleSignature("xor",
		"(args ...interface{}) interface{}",
		true,
		func(args ...interface{}) (interface{}, error) {
			if len(args) < 2 {
				return nil, errors.New("at least two arguments needed")
			}

			n := -1
			for _, arg := range args {
				var b []byte
				switch v := arg.(type) {
				case string:
					b = []byte(v)
				case []byte:
					b = v
				default:
					return nil, fmt.Errorf("invalid argument type %T", arg)
				}
				if n == -1 {
					n = len(b)
				} else if len(b) != n {
					return nil, errors.New("all arguments must have the same length")
				}
			}

			result := make([]byte, n)
			for i := 0; i < n; i++ {
				for _, arg := range args {
					b, ok := arg.([]byte)
					if !ok {
						b = []byte(arg.(string))
					}
					result[i] ^= b[i]
				}
			}

			return result, nil
		}))
}

// PublicIp 注册公网IP获取函数，用于获取当前主机的公网IP地址
//
// 函数签名: public_ip()
// 参数: 无
//
// 返回值:
//   - 当前主机的公网IP地址
//
// 该函数尝试获取当前主机的公网IP地址。如果无法获取（例如，没有互联网连接），
// 将返回错误。该函数使用缓存机制，多次调用不会重复请求。
func PublicIp() {
	MustAddFunction(NewWithSingleSignature("public_ip",
		"() string",
		true,
		func(args ...interface{}) (interface{}, error) {
			publicIP := GetPublicIP()
			if publicIP == "" {
				return nil, errors.New("could not retrieve public ip")
			}
			return publicIP, nil
		}))

}

// Jarm 注册JARM指纹获取函数，用于获取TLS服务的JARM指纹
//
// 函数签名: jarm(host)
// 参数:
//   - host: 要获取JARM指纹的主机，格式为"主机名:端口"
//
// 返回值:
//   - 目标主机的JARM指纹字符串
//
// 该函数获取目标主机的JARM指纹。JARM是一种主动TLS指纹识别方法，
// 可以用于识别在不同网络位置使用相同TLS堆栈的服务器。
func Jarm() {
	MustAddFunction(NewWithPositionalArgs("jarm", 1, true, func(args ...interface{}) (interface{}, error) {
		host, ok := args[0].(string)
		if !ok {
			return nil, errors.New("invalid target")
		}
		hostname, portRaw, err := net.SplitHostPort(host)
		if err != nil {
			return nil, err
		}
		port, err := strconv.Atoi(portRaw)
		if err != nil {
			return nil, err
		}
		return jarm.HashWithDialer(nil, hostname, port, 10)
	}))
}

// Count 注册字符串计数函数，用于计算字符串中子字符串出现的次数
//
// 函数签名: count(str, substr)
// 参数:
//   - str: 要搜索的主字符串
//   - substr: 要计数的子字符串
//
// 返回值:
//   - 子字符串在主字符串中出现的次数
//
// 该函数计算一个子字符串在主字符串中出现的次数。
func Count() {
	MustAddFunction(NewWithSingleSignature("count",
		"(str, substr string) int",
		true,
		func(args ...interface{}) (interface{}, error) {
			if len(args) < 2 {
				return nil, ErrInvalidDslFunction
			}

			str := toString(args[0])
			substr := toString(args[1])

			return strings.Count(str, substr), nil
		},
	))

}

// ToTitle 注册标题大小写转换函数，将字符串转换为标题大小写格式
//
// 函数签名: to_title(s, optionalLang)
// 参数:
//   - s: 要转换的字符串
//   - optionalLang: 可选的语言代码，用于指定语言特定的标题大小写规则
//
// 返回值:
//   - 转换为标题大小写的字符串
//
// 该函数将字符串中每个单词的首字母转换为大写，其余字母保持不变，
// 生成标题大小写格式的字符串。可以指定语言代码以使用特定语言的规则；
func ToTitle() {
	MustAddFunction(NewWithSingleSignature("to_title",
		"(s, optionalLang string) string",
		true,
		func(args ...interface{}) (interface{}, error) {
			var lang = language.Und
			var s string
			var err error

			argSize := len(args)
			if argSize < 1 {
				return nil, ErrInvalidDslFunction
			}
			s = toString(args[0])

			if argSize >= 2 {
				lang, err = language.Parse(toString(args[1]))
				if err != nil {
					lang = language.Und
				}
			}

			return cases.Title(lang).String(s), nil
		}))
}
