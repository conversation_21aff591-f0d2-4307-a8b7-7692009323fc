// Author: chenjb
// Version: V1.0
// Date: 2025-06-13 14:55:29
// FilePath: /yaml_scan/pkg/authprovider/authx/headers_auth.go
// Description:实现了请求头认证(Headers)策略，用于通过自定义HTTP请求头进行认证
package authx

import (
	"net/http"
	"yaml_scan/pkg/retryablehttp"
)

var (
	// 确保HeadersAuthStrategy实现了AuthStrategy接口
	_ AuthStrategy = &HeadersAuthStrategy{}
)

// HeadersAuthStrategy 是请求头认证策略的实现
type HeadersAuthStrategy struct {
	Data *Secret
}

// NewHeadersAuthStrategy 创建一个新的请求头认证策略
func NewHeadersAuthStrategy(data *Secret) *HeadersAuthStrategy {
	return &HeadersAuthStrategy{Data: data}
}

// Apply // Apply 将请求头认证策略应用到HTTP请求上
// 它会使用密钥中的请求头键值对设置HTTP请求头
// @receiver s 
// @param req *http.Request: 
func (s *HeadersAuthStrategy) Apply(req *http.Request) {
	for _, header := range s.Data.Headers {
		req.Header.Set(header.Key, header.Value)
	}
}

// ApplyOnRR 将请求头认证策略应用到可重试的HTTP
// @receiver s 
// @param req *retryablehttp.Request: 
func (s *HeadersAuthStrategy) ApplyOnRR(req *retryablehttp.Request) {
	for _, header := range s.Data.Headers {
		req.Header.Set(header.Key, header.Value)
	}
}

