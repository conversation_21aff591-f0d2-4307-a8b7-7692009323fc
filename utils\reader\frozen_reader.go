// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 19:09:48
// FilePath: /yaml_scan/utils/reader/frozen_reader.go
// Description: 提供了一个特殊的读取器实现，用于模拟永不返回的读取操作
package reader

import (
	"io"
	"math"
	"time"
)

// FrozenReader 是一个永不返回的读取器
// 这种读取器在需要模拟一个永久阻塞的情况时非常有用，
// 例如在测试超时处理机制或模拟网络连接挂起的场景
type FrozenReader struct{}

// Read 实现了io.Reader接口的Read方法
// @receiver reader 
// @param p []byte: 用于存储读取数据的字节切片
// @return n int: 读取的字节数，总是0
// @return err error:  错误信息，理论上永远不会到达返回这一步
func (reader FrozenReader) Read(p []byte) (n int, err error) {
	// 尝试休眠一个极长的时间，实际效果是无限阻塞
	time.Sleep(math.MaxInt32 * time.Second)
	// 以下代码理论上不会执行，因为上面的Sleep会阻塞非常长的时间
	return 0, io.EOF
}

