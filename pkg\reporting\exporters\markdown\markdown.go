// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-17 17:05:15
// FilePath: /yaml_scan/pkg/reporting/exporters/markdown/markdown.go
// Description: 
package markdown


// Options contains the configuration options for GitHub issue tracker client
type Options struct {
	// Directory is the directory to export found results to
	Directory string `yaml:"directory"`
	OmitRaw   bool   `yaml:"omit-raw"`
	SortMode  string `yaml:"sort-mode"`
}
