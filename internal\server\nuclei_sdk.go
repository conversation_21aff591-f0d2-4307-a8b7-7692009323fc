// Author: chenjb
// Version: V1.0
// Date: 2025-06-17 19:51:34
// FilePath: /yaml_scan/internal/server/nuclei_sdk.go
// Description:
package server

import (
	"yaml_scan/pkg/fuzz/stats"
	"yaml_scan/pkg/projectfile"
	"yaml_scan/pkg/protocols/common/interactsh"
	browserEngine "yaml_scan/pkg/protocols/headless/engine"
	"yaml_scan/pkg/ratelimit"
	"yaml_scan/pkg/types"
)

type NucleiExecutorOptions struct {
	Options            *types.Options
	Output             output.Writer
	Progress           progress.Progress
	Catalog            catalog.Catalog
	IssuesClient       reporting.Client
	RateLimiter        *ratelimit.Limiter
	Interactsh         *interactsh.Client
	ProjectFile        *projectfile.ProjectFile
	Browser            *browserEngine.Browser
	FuzzStatsDB        *stats.Tracker
	Colorizer          aurora.Aurora
	Parser             parser.Parser
	TemporaryDirectory string
}

