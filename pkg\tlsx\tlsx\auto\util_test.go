// Author: chenjb
// Version: V1.0
// Date: 2025-07-01 15:26:54
// FilePath: /yaml_scan/pkg/tlsx/tlsx/auto/util_test.go
// Description:
package auto

import (
	"testing"

	"github.com/stretchr/testify/require"
)

// TestCipherNamesContent 测试密码套件名称的内容
// 验证聚合的密码套件包含预期的常见密码套件
func TestCipherNamesContent(t *testing.T) {
	// 一些常见的密码套件，应该在聚合列表中
	expectedCiphers := []string{
		"TLS_RSA_WITH_AES_128_CBC_SHA",
		"TLS_RSA_WITH_AES_256_CBC_SHA",
		"TLS_RSA_WITH_AES_128_GCM_SHA256",
		"TLS_RSA_WITH_AES_256_GCM_SHA384",
	}

	// 将allCiphersNames转换为map以便快速查找
	cipherMap := make(map[string]bool)
	for _, cipher := range allCiphersNames {
		cipherMap[cipher] = true
	}

	// 检查常见密码套件是否存在（允许部分不存在，因为不同实现可能支持不同的密码套件）
	foundCount := 0
	for _, expected := range expectedCiphers {
		if cipherMap[expected] {
			foundCount++
			t.Logf("找到预期的密码套件: %s", expected)
		}
	}

	// 至少应该找到一些常见的密码套件
	require.Greater(t, foundCount, 0, "应该至少包含一些常见的密码套件")
	t.Logf("在 %d 个预期密码套件中找到了 %d 个", len(expectedCiphers), foundCount)
}

// TestTlsVersionsContent 测试TLS版本的内容
// 验证聚合的TLS版本包含预期的版本
func TestTlsVersionsContent(t *testing.T) {
	// 常见的TLS版本
	expectedVersions := []string{
		"tls10",
		"tls11",
		"tls12",
		"tls13",
	}

	// 将supportedTlsVersions转换为map以便快速查找
	versionMap := make(map[string]bool)
	for _, version := range supportedTlsVersions {
		versionMap[version] = true
	}

	// 检查常见TLS版本是否存在
	foundCount := 0
	for _, expected := range expectedVersions {
		if versionMap[expected] {
			foundCount++
			t.Logf("找到预期的TLS版本: %s", expected)
		}
	}

	// 至少应该支持TLS 1.2和1.3
	require.True(t, versionMap["tls12"] || versionMap["tls13"], "应该至少支持TLS 1.2或1.3")
	t.Logf("在 %d 个预期TLS版本中找到了 %d 个", len(expectedVersions), foundCount)
}
