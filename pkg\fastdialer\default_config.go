// Author: chenjb
// Version: V1.0
// Date: 2025-05-21 17:01:54
// FilePath: /yaml_scan/pkg/fastdialer/default_config.go
// Description:
package fastdialer

import (
	"crypto/tls"

	ztls "github.com/zmap/zcrypto/tls"
)

// getUnsafeCipherSuites 返回所有被Go标准库认为不安全的密码套件列表
// @return []uint16 []uint16: 含所有不安全密码套件ID的切片
func getUnsafeCipherSuites() []uint16 {
	unsafeCipherSuites := make([]uint16, 0, len(tls.InsecureCipherSuites())+len(tls.CipherSuites()))
	// 添加所有被标记为不安全的密码套件
	for _, suite := range tls.InsecureCipherSuites() {
		unsafeCipherSuites = append(unsafeCipherSuites, suite.ID)
	}
	// 添加标准密码套件
	for _, suite := range tls.CipherSuites() {
		unsafeCipherSuites = append(unsafeCipherSuites, suite.ID)
	}

	return unsafeCipherSuites
}

// DefaultTLSConfig 默认TLS配置
var DefaultTLSConfig = &tls.Config{
	Renegotiation:      tls.RenegotiateOnceAsClient, // 允许客户端进行一次TLS重新协商
	InsecureSkipVerify: true,                        // 跳过证书验证，不验证服务器证书的有效性
	MinVersion:         tls.VersionTLS10,            // 最低支持的TLS版本为1.0
	CipherSuites:       getUnsafeCipherSuites(),     // 使用包括不安全选项在内的所有密码套件
}

// DefaultZTLSConfig 默认ZTLS配置
var DefaultZTLSConfig = &ztls.Config{
	InsecureSkipVerify: true,                    // 跳过证书验证
	MinVersion:         ztls.VersionTLS10,       // 最低支持的TLS版本为1.0
	CipherSuites:       getUnsafeCipherSuites(), // 使用包括不安全选项在内的所有密码套件
}
