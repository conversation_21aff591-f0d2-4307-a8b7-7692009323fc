// Author: chenjb
// Version: V1.0
// Date: 2025-05-28 17:30:56
// FilePath: /yaml_scan/pkg/dsl/engine.go
// Description: 实现DSL表达式引擎，负责表达式的解析和执行
package dsl

import (
	"regexp"
	"sync"
	mapsutil "yaml_scan/utils/maps"

	"github.com/Knetic/govaluate"
)

var (
	// defaultEngine 是全局默认的表达式引擎实例
	defaultEngine *Engine
	// RegexStore 是一个线程安全的正则表达式缓存，用于存储已编译的正则表达式
	RegexStore = &mapsutil.SyncLockMap[string, *regexp.Regexp]{Map: make(mapsutil.Map[string, *regexp.Regexp])}
)

// Engine 是DSL表达式引擎的核心结构
type Engine struct {
	// HelperFunctions 存储所有可用于表达式的辅助函数
	HelperFunctions map[string]govaluate.ExpressionFunction
	// ExpressionStore 缓存已编译的表达式
	ExpressionStore map[string]*govaluate.EvaluableExpression
	// exprmux 用于保护表达式存储的互斥锁
	exprmux sync.RWMutex
}

// NewEngine 创建一个新的DSL表达式引擎
// @return *Engine *Engine: 新创建的引擎实例
// @return error error: 可能得错误
func NewEngine() (*Engine, error) {
	engine := &Engine{
		HelperFunctions: DefaultHelperFunctions,
		ExpressionStore: make(map[string]*govaluate.EvaluableExpression),
	}
	return engine, nil
}

// EvalExpr 解析并执行表达式
// @receiver e
// @param expr string:  要执行的表达式字符串
// @param vars map[string]interface{}:  表达式中使用的变量映射
// @return interface{} interface{}: 表达式执行结果
// @return error error: 可能的错误
func (e *Engine) EvalExpr(expr string, vars map[string]interface{}) (interface{}, error) {
	// 加锁以确保线程安全
	e.exprmux.Lock()
	defer e.exprmux.Unlock()
	// 使用辅助函数编译表达式
	compiled, err := govaluate.NewEvaluableExpressionWithFunctions(expr, e.HelperFunctions)
	if err != nil {
		return nil, err
	}

	// 执行表达式并返回结果
	e.ExpressionStore[expr] = compiled

	// 执行表达式并返回结果
	return compiled.Evaluate(vars)
}

// EvalExprFromCache 尝试从缓存获取表达式并执行
// @receiver e
// @param expr string: 要执行的表达式字符串
// @param vars map[string]interface{}: 表达式中使用的变量映射
// @return interface{} interface{}: 表达式执行结果
// @return error error: 可能的错误
func (e *Engine) EvalExprFromCache(expr string, vars map[string]interface{}) (interface{}, error) {
	// 检查表达式是否已编译
	compiled, ok := e.ExpressionStore[expr]
	if !ok {
		return e.EvalExpr(expr, vars)
	}

	return compiled.Evaluate(vars)
}

// EvalExpr 是一个全局函数，用于执行表达式
// @param expr string: 要执行的表达式字符串
// @param vars map[string]interface{}: 表达式中使用的变量映射
// @return interface{} interface{}: 表达式执行结果
// @return error error: 可能得错误
func EvalExpr(expr string, vars map[string]interface{}) (interface{}, error) {
	if defaultEngine == nil {
		var err error
		defaultEngine, err = NewEngine()
		if err != nil {
			return nil, err
		}
	}
	// 使用默认引擎执行表达式
	return defaultEngine.EvalExprFromCache(expr, vars)
}

// Regex 编译正则表达式并缓存结果
// @param regxp string:要编译的正则表达式字符串 
// @return *regexp.Regexp *regexp.Regexp:  编译后的正则表达式对象
// @return error error: 可能的错误
func Regex(regxp string) (*regexp.Regexp, error) {
	if compiled, ok := RegexStore.Get(regxp); ok {
		return compiled, nil
	}

	compiled, err := regexp.Compile(regxp)
	if err != nil {
		return nil, err
	}
	_ = RegexStore.Set(regxp, compiled)

	return compiled, nil
}
