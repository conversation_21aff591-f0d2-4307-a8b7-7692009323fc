// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-30 17:38:13
// FilePath: /yaml_scan/pkg/tlsx/tlsx/openssl/openssl_exec_test.go
// Description: 
package openssl

import (
	"context"
	"crypto/x509"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestCMDOUT 测试CMDOUT结构体
// 验证命令输出结构的基本功能
func TestCMDOUT(t *testing.T) {
	t.Run("CMDOUT结构体基本功能", func(t *testing.T) {
		cmdout := &CMDOUT{
			Command: "openssl version",
			Stdout:  "OpenSSL 1.1.1",
			Stderr:  "",
		}

		require.NotNil(t, cmdout, "CMDOUT结构体不应该为nil")
		require.Equal(t, "openssl version", cmdout.Command, "Command字段应该正确设置")
		require.Equal(t, "OpenSSL 1.1.1", cmdout.Stdout, "Stdout字段应该正确设置")
		require.Empty(t, cmdout.Stderr, "Stderr字段应该为空")
	})

	t.Run("CMDOUT字段类型验证", func(t *testing.T) {
		cmdout := &CMDOUT{}
		
		// 验证字段类型
		require.IsType(t, "", cmdout.Command, "Command应该是字符串类型")
		require.IsType(t, "", cmdout.Stdout, "Stdout应该是字符串类型")
		require.IsType(t, "", cmdout.Stderr, "Stderr应该是字符串类型")
	})
}

// TestExecOpenSSL 测试execOpenSSL函数
// 验证OpenSSL命令执行功能
func TestExecOpenSSL(t *testing.T) {
	if !IsAvailable() {
		t.Skip("OpenSSL不可用，跳过execOpenSSL测试")
	}

	t.Run("执行version命令", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		result, err := execOpenSSL(ctx, []string{"version"})
		require.NoError(t, err, "执行version命令应该成功")
		require.NotNil(t, result, "结果不应该为nil")
		require.NotEmpty(t, result.Stdout, "标准输出不应该为空")
		require.Contains(t, strings.ToLower(result.Stdout), "openssl", "输出应该包含openssl")
		require.Contains(t, result.Command, "version", "命令字符串应该包含version")

		t.Logf("OpenSSL版本: %s", strings.TrimSpace(result.Stdout))
	})

	t.Run("执行无效命令", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		result, err := execOpenSSL(ctx, []string{"invalid_command"})
		// 可能返回错误或在stderr中包含错误信息
		if err != nil {
			t.Logf("执行无效命令返回错误: %v", err)
		} else {
			require.NotEmpty(t, result.Stderr, "无效命令应该在stderr中有错误信息")
		}
	})

	t.Run("上下文超时测试", func(t *testing.T) {
		// 创建一个很短的超时上下文
		ctx, cancel := context.WithTimeout(context.Background(), 1*time.Millisecond)
		defer cancel()

		// 等待上下文超时
		time.Sleep(2 * time.Millisecond)

		result, err := execOpenSSL(ctx, []string{"version"})
		// 应该因为上下文超时而失败
		if err != nil {
			require.Contains(t, err.Error(), "context", "错误应该与上下文相关")
		}
		require.NotNil(t, result, "即使失败也应该返回结果结构")
	})
}

// TestGetCiphers 测试getCiphers函数
// 验证密码套件获取功能
func TestGetCiphers(t *testing.T) {
	if !IsAvailable() {
		t.Skip("OpenSSL不可用，跳过getCiphers测试")
	}

	t.Run("获取密码套件列表", func(t *testing.T) {
		ciphers, err := getCiphers()
		require.NoError(t, err, "获取密码套件应该成功")
		require.NotEmpty(t, ciphers, "密码套件列表不应该为空")
		require.Greater(t, len(ciphers), 10, "应该有足够数量的密码套件")

		// 验证密码套件格式
		for i, cipher := range ciphers {
			require.NotEmpty(t, cipher, "密码套件[%d]不应该为空", i)
			// 大多数密码套件应该包含常见的关键词
			if i < 5 { // 只检查前几个，避免测试过于严格
				t.Logf("密码套件[%d]: %s", i, cipher)
			}
		}

		t.Logf("总共获取到 %d 个密码套件", len(ciphers))
	})

	t.Run("密码套件唯一性验证", func(t *testing.T) {
		ciphers, err := getCiphers()
		require.NoError(t, err, "获取密码套件应该成功")

		// 检查是否有重复的密码套件
		cipherSet := make(map[string]bool)
		duplicates := []string{}

		for _, cipher := range ciphers {
			if cipherSet[cipher] {
				duplicates = append(duplicates, cipher)
			} else {
				cipherSet[cipher] = true
			}
		}

		if len(duplicates) > 0 {
			t.Logf("发现重复的密码套件: %v", duplicates)
		}
	})
}

// TestReadSessionData 测试readSessionData函数
// 验证会话数据解析功能
func TestReadSessionData(t *testing.T) {
	t.Run("解析有效会话数据", func(t *testing.T) {
		// 模拟OpenSSL s_client的输出
		sessionData := `
SSL-Session:
    Protocol  : TLSv1.2
    Cipher    : ECDHE-ECDSA-AES256-GCM-SHA384
    Session-ID: 8A1E70132CE02478C4643E7CCE8B7EB6CF56875A623A4850AAC9DF3DD850DA56
    Session-ID-ctx:
    Master-Key: 4E1BFA3CFD0C0283743CE620A475FF1896B1D711EB11D217F7C3A0C0DEB309BB0F30F5922B8B696887B84C5871F38E1D
    PSK identity: None
    PSK identity hint: None
    SRP username: None
    TLS session ticket lifetime hint: 83100 (seconds)
    Start Time: 1751285516
    Timeout   : 7200 (sec)
    Verify return code: 20 (unable to get local issuer certificate)
    Extended master secret: no

`

		session, err := readSessionData(sessionData)
		require.NoError(t, err, "解析会话数据应该成功")
		require.NotNil(t, session, "会话对象不应该为nil")
		require.Equal(t, "TLSv1.2", session.Protocol, "协议版本应该正确")
		require.Equal(t, "ECDHE-ECDSA-AES256-GCM-SHA384", session.Cipher, "密码套件应该正确")
		require.Equal(t, "4E1BFA3CFD0C0283743CE620A475FF1896B1D711EB11D217F7C3A0C0DEB309BB0F30F5922B8B696887B84C5871F38E1D", session.MasterKey, "主密钥应该正确")

		t.Logf("解析的会话信息: Protocol=%s, Cipher=%s", session.Protocol, session.Cipher)
	})

	t.Run("解析不完整会话数据", func(t *testing.T) {
		// 只包含部分会话信息
		sessionData := `
SSL-Session:
    Protocol  : TLSv1.2
    Timeout   : 300 (sec)
`

		session, err := readSessionData(sessionData)
		require.NoError(t, err, "解析不完整会话数据应该成功")
		require.NotNil(t, session, "会话对象不应该为nil")
		require.Equal(t, "TLSv1.2", session.Protocol, "协议版本应该正确")
		require.Empty(t, session.Cipher, "密码套件应该为空")
		require.Empty(t, session.MasterKey, "主密钥应该为空")
	})

	t.Run("解析无会话数据", func(t *testing.T) {
		// 不包含SSL-Session块的数据
		sessionData := `
CONNECTED(00000003)
depth=0 CN = example.com
verify error:num=18:self signed certificate
verify return:1
`

		session, err := readSessionData(sessionData)
		require.NoError(t, err, "解析无会话数据应该成功")
		require.NotNil(t, session, "会话对象不应该为nil")
		require.Empty(t, session.Protocol, "协议版本应该为空")
		require.Empty(t, session.Cipher, "密码套件应该为空")
	})

	t.Run("解析空数据", func(t *testing.T) {
		session, err := readSessionData("")
		require.NoError(t, err, "解析空数据应该成功")
		require.NotNil(t, session, "会话对象不应该为nil")
	})
}

// TestGetx509Certificate 测试getx509Certificate函数
// 验证X.509证书解析功能
func TestGetx509Certificate(t *testing.T) {
	t.Run("解析有效PEM证书", func(t *testing.T) {
		// 创建一个简单的测试证书PEM数据
		testCertPEM := `-----BEGIN CERTIFICATE-----
MIIFmzCCBSGgAwIBAgIQCtiTuvposLf7ekBPBuyvmjAKBggqhkjOPQQDAzBZMQsw
CQYDVQQGEwJVUzEVMBMGA1UEChMMRGlnaUNlcnQgSW5jMTMwMQYDVQQDEypEaWdp
Q2VydCBHbG9iYWwgRzMgVExTIEVDQyBTSEEzODQgMjAyMCBDQTEwHhcNMjUwMTE1
MDAwMDAwWhcNMjYwMTE1MjM1OTU5WjCBjjELMAkGA1UEBhMCVVMxEzARBgNVBAgT
CkNhbGlmb3JuaWExFDASBgNVBAcTC0xvcyBBbmdlbGVzMTwwOgYDVQQKEzNJbnRl
cm5ldCBDb3Jwb3JhdGlvbiBmb3IgQXNzaWduZWQgTmFtZXMgYW5kIE51bWJlcnMx
FjAUBgNVBAMMDSouZXhhbXBsZS5jb20wWTATBgcqhkjOPQIBBggqhkjOPQMBBwNC
AASaSJeELWFsCMlqFKDIOIDmAMCH+plXDhsA4tiHklfnCPs8XrDThCg3wSQRjtMg
cXS9k49OCQPOAjuw5GZzz6/uo4IDkzCCA48wHwYDVR0jBBgwFoAUiiPrnmvX+Tdd
+W0hOXaaoWfeEKgwHQYDVR0OBBYEFPDBajIN7NrH6o/NDW0ZElnRvnLtMCUGA1Ud
EQQeMByCDSouZXhhbXBsZS5jb22CC2V4YW1wbGUuY29tMD4GA1UdIAQ3MDUwMwYG
Z4EMAQICMCkwJwYIKwYBBQUHAgEWG2h0dHA6Ly93d3cuZGlnaWNlcnQuY29tL0NQ
UzAOBgNVHQ8BAf8EBAMCA4gwHQYDVR0lBBYwFAYIKwYBBQUHAwEGCCsGAQUFBwMC
MIGfBgNVHR8EgZcwgZQwSKBGoESGQmh0dHA6Ly9jcmwzLmRpZ2ljZXJ0LmNvbS9E
aWdpQ2VydEdsb2JhbEczVExTRUNDU0hBMzg0MjAyMENBMS0yLmNybDBIoEagRIZC
aHR0cDovL2NybDQuZGlnaWNlcnQuY29tL0RpZ2lDZXJ0R2xvYmFsRzNUTFNFQ0NT
SEEzODQyMDIwQ0ExLTIuY3JsMIGHBggrBgEFBQcBAQR7MHkwJAYIKwYBBQUHMAGG
GGh0dHA6Ly9vY3NwLmRpZ2ljZXJ0LmNvbTBRBggrBgEFBQcwAoZFaHR0cDovL2Nh
Y2VydHMuZGlnaWNlcnQuY29tL0RpZ2lDZXJ0R2xvYmFsRzNUTFNFQ0NTSEEzODQy
MDIwQ0ExLTIuY3J0MAwGA1UdEwEB/wQCMAAwggF7BgorBgEEAdZ5AgQCBIIBawSC
AWcBZQB0AA5XlLzzrqk+MxssmQez95Dfm8I9cTIl3SGpJaxhxU4hAAABlGd6v8cA
AAQDAEUwQwIfJBcPWkx80ik7uLYW6OGvNYvJ4NmOR2RXc9uviFPH6QIgUtuuUenH
IT5UNWJffBBRq31tUGi7ZDTSrrM0f4z1Va4AdQBkEcRspBLsp4kcogIuALyrTygH
1B41J6vq/tUDyX3N8AAAAZRnesAFAAAEAwBGMEQCIHCu6NgHhV1Qvif/G7BHq7ci
MGH8jdch/xy4LzrYlesXAiByMFMvDhGg4sYm1MsrDGVedcwpE4eN0RuZcFGmWxwJ
cgB2AEmcm2neHXzs/DbezYdkprhbrwqHgBnRVVL76esp3fjDAAABlGd6wBkAAAQD
AEcwRQIgaFh67yEQ2lwgm3X16n2iWjEQFII2b2fpONtBVibZVWwCIQD5psqjXDYs
IEb1hyh0S8bBN3O4u2sA9zisKIlYjZg8wjAKBggqhkjOPQQDAwNoADBlAjEA+aaC
RlPbb+VY+u4avPyaG7fvUDJqN8KwlrXD4XptT7QL+D03+BA/FUEo3dD1iz37AjBk
Y3jhsuLAW7pWsDbtX/Qwxp6kNsK4jh1/RjvV/260sxQwM/GM7t0+T0uP2L+Y12U=
-----END CERTIFICATE-----`

		cert, err := getx509Certificate([]byte(testCertPEM))
		require.NoError(t, err, "解析有效PEM证书应该成功")
		require.NotNil(t, cert, "证书对象不应该为nil")
		require.IsType(t, &x509.Certificate{}, cert, "应该返回x509.Certificate类型")

		t.Logf("证书主题: %s", cert.Subject.String())
	})

	t.Run("解析空证书数据", func(t *testing.T) {
		cert, err := getx509Certificate([]byte{})
		require.Error(t, err, "解析空证书数据应该失败")
		require.Nil(t, cert, "证书对象应该为nil")
		require.Contains(t, err.Error(), "empty", "错误信息应该包含'empty'")
	})

	t.Run("解析无效PEM数据", func(t *testing.T) {
		invalidPEM := []byte("这不是有效的PEM数据")
		
		cert, err := getx509Certificate(invalidPEM)
		require.Error(t, err, "解析无效PEM数据应该失败")
		require.Nil(t, cert, "证书对象应该为nil")
		require.Contains(t, err.Error(), "pem", "错误信息应该包含'pem'")
	})

	t.Run("解析格式正确但内容无效的PEM", func(t *testing.T) {
		invalidCertPEM := `-----BEGIN CERTIFICATE-----
这是无效的证书内容
-----END CERTIFICATE-----`

		cert, err := getx509Certificate([]byte(invalidCertPEM))
		require.Error(t, err, "解析无效证书内容应该失败")
		require.Nil(t, cert, "证书对象应该为nil")
	})
}

// TestParseCertificates 测试parseCertificates函数
// 验证证书链解析功能
func TestParseCertificates(t *testing.T) {
	t.Run("解析单个证书", func(t *testing.T) {
		// 模拟包含单个证书的OpenSSL输出
		opensslOutput := `
CONNECTED(00000003)
depth=0 CN = example.com
-----BEGIN CERTIFICATE-----
MIIFmzCCBSGgAwIBAgIQCtiTuvposLf7ekBPBuyvmjAKBggqhkjOPQQDAzBZMQsw
CQYDVQQGEwJVUzEVMBMGA1UEChMMRGlnaUNlcnQgSW5jMTMwMQYDVQQDEypEaWdp
Q2VydCBHbG9iYWwgRzMgVExTIEVDQyBTSEEzODQgMjAyMCBDQTEwHhcNMjUwMTE1
MDAwMDAwWhcNMjYwMTE1MjM1OTU5WjCBjjELMAkGA1UEBhMCVVMxEzARBgNVBAgT
CkNhbGlmb3JuaWExFDASBgNVBAcTC0xvcyBBbmdlbGVzMTwwOgYDVQQKEzNJbnRl
cm5ldCBDb3Jwb3JhdGlvbiBmb3IgQXNzaWduZWQgTmFtZXMgYW5kIE51bWJlcnMx
FjAUBgNVBAMMDSouZXhhbXBsZS5jb20wWTATBgcqhkjOPQIBBggqhkjOPQMBBwNC
AASaSJeELWFsCMlqFKDIOIDmAMCH+plXDhsA4tiHklfnCPs8XrDThCg3wSQRjtMg
cXS9k49OCQPOAjuw5GZzz6/uo4IDkzCCA48wHwYDVR0jBBgwFoAUiiPrnmvX+Tdd
+W0hOXaaoWfeEKgwHQYDVR0OBBYEFPDBajIN7NrH6o/NDW0ZElnRvnLtMCUGA1Ud
EQQeMByCDSouZXhhbXBsZS5jb22CC2V4YW1wbGUuY29tMD4GA1UdIAQ3MDUwMwYG
Z4EMAQICMCkwJwYIKwYBBQUHAgEWG2h0dHA6Ly93d3cuZGlnaWNlcnQuY29tL0NQ
UzAOBgNVHQ8BAf8EBAMCA4gwHQYDVR0lBBYwFAYIKwYBBQUHAwEGCCsGAQUFBwMC
MIGfBgNVHR8EgZcwgZQwSKBGoESGQmh0dHA6Ly9jcmwzLmRpZ2ljZXJ0LmNvbS9E
aWdpQ2VydEdsb2JhbEczVExTRUNDU0hBMzg0MjAyMENBMS0yLmNybDBIoEagRIZC
aHR0cDovL2NybDQuZGlnaWNlcnQuY29tL0RpZ2lDZXJ0R2xvYmFsRzNUTFNFQ0NT
SEEzODQyMDIwQ0ExLTIuY3JsMIGHBggrBgEFBQcBAQR7MHkwJAYIKwYBBQUHMAGG
GGh0dHA6Ly9vY3NwLmRpZ2ljZXJ0LmNvbTBRBggrBgEFBQcwAoZFaHR0cDovL2Nh
Y2VydHMuZGlnaWNlcnQuY29tL0RpZ2lDZXJ0R2xvYmFsRzNUTFNFQ0NTSEEzODQy
MDIwQ0ExLTIuY3J0MAwGA1UdEwEB/wQCMAAwggF7BgorBgEEAdZ5AgQCBIIBawSC
AWcBZQB0AA5XlLzzrqk+MxssmQez95Dfm8I9cTIl3SGpJaxhxU4hAAABlGd6v8cA
AAQDAEUwQwIfJBcPWkx80ik7uLYW6OGvNYvJ4NmOR2RXc9uviFPH6QIgUtuuUenH
IT5UNWJffBBRq31tUGi7ZDTSrrM0f4z1Va4AdQBkEcRspBLsp4kcogIuALyrTygH
1B41J6vq/tUDyX3N8AAAAZRnesAFAAAEAwBGMEQCIHCu6NgHhV1Qvif/G7BHq7ci
MGH8jdch/xy4LzrYlesXAiByMFMvDhGg4sYm1MsrDGVedcwpE4eN0RuZcFGmWxwJ
cgB2AEmcm2neHXzs/DbezYdkprhbrwqHgBnRVVL76esp3fjDAAABlGd6wBkAAAQD
AEcwRQIgaFh67yEQ2lwgm3X16n2iWjEQFII2b2fpONtBVibZVWwCIQD5psqjXDYs
IEb1hyh0S8bBN3O4u2sA9zisKIlYjZg8wjAKBggqhkjOPQQDAwNoADBlAjEA+aaC
RlPbb+VY+u4avPyaG7fvUDJqN8KwlrXD4XptT7QL+D03+BA/FUEo3dD1iz37AjBk
Y3jhsuLAW7pWsDbtX/Qwxp6kNsK4jh1/RjvV/260sxQwM/GM7t0+T0uP2L+Y12U=
-----END CERTIFICATE-----
verify return:1
`

		certs, err := parseCertificates(opensslOutput)
		require.NoError(t, err, "解析单个证书应该成功")
		require.Len(t, certs, 1, "应该解析出1个证书")
		require.NotNil(t, certs[0], "证书不应该为nil")

		t.Logf("解析出证书主题: %s", certs[0].Subject.String())
	})

	t.Run("解析无证书数据", func(t *testing.T) {
		opensslOutput := `
CONNECTED(00000003)
depth=0 CN = example.com
verify error:num=18:self signed certificate
verify return:1
`

		certs, err := parseCertificates(opensslOutput)
		require.NoError(t, err, "解析无证书数据应该成功")
		require.Empty(t, certs, "证书列表应该为空")
	})

	t.Run("解析包含无效证书的数据", func(t *testing.T) {
		opensslOutput := `
CONNECTED(00000003)
-----BEGIN CERTIFICATE-----
这是无效的证书内容
-----END CERTIFICATE-----
`

		certs, err := parseCertificates(opensslOutput)
		require.Error(t, err, "解析无效证书应该失败")
		require.Empty(t, certs, "证书列表应该为空")
	})
}

