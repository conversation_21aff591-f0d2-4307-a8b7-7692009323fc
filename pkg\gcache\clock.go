//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-17 17:30:08
//FilePath: /yaml_scan/pkg/gcache/clock.go
//Description:

package gcache

import (
	"sync"
	"time"
)

// Clock 接口定义了获取当前时间的方法
type Clock interface {
	Now() time.Time // 返回当前时间
}

// RealClock 是 Clock 接口的实现
type RealClock struct{}

// NewRealClock  创建一个新的 RealClock 实例
// @return Clock Clock:
func NewRealClock() Clock {
	return RealClock{}
}

// Now  返回当前的真实时间
// @receiver rc
// @return time.Time time.Time:
func (rc RealClock) Now() time.Time {
	t := time.Now()
	return t
}

// FakeClock 接口扩展了 Clock，增加了时间推进功能
type FakeClock interface {
	Clock

	Advance(d time.Duration)
}

// NewFakeClock 创建一个新的 FakeClock 实例
// @return FakeClock FakeClock:
func NewFakeClock() FakeClock {
	return &fakeclock{
		// 初始化固定时间点，避免 time.Time.IsZero() 返回 true
		now: time.Date(1984, time.April, 4, 0, 0, 0, 0, time.UTC),
	}
}

// fakeclock 是 FakeClock 接口的具体实现
type fakeclock struct {
	now time.Time //当前的模拟时间

	mutex sync.RWMutex // 读写锁，用于并发安全
}

// Now 返回当前的模拟时间
// @receiver fc 
// @return time.Time time.Time: 
func (fc *fakeclock) Now() time.Time {
	fc.mutex.RLock()
	defer fc.mutex.RUnlock()
	t := fc.now
	return t
}

// Advance  将模拟时间向前推进指定的持续时间
// @receiver fc 
// @param d time.Duration: 指定的持续时间
func (fc *fakeclock) Advance(d time.Duration) {
	fc.mutex.Lock()
	defer fc.mutex.Unlock()
	fc.now = fc.now.Add(d)
}
