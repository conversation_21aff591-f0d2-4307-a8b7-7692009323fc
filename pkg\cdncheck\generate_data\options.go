// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-19 16:28:42
// FilePath: /yaml_scan/pkg/cdncheck/generate/generate/options.go
// Description: 
package generate



import (
	"net/http"
	"os"
)

type Options struct {
	IPInfoToken string
	HTTPClient  *http.Client
}

// HasAuthInfo returns true if auth info has been provided
func (options *Options) HasAuthInfo() bool {
	return options.IPInfoToken != ""
}

// ParseFromEnv parses auth tokens from env or file
func (options *Options) ParseFromEnv() {
	if ipInfoToken := os.Getenv("IPINFO_TOKEN"); ipInfoToken != "" {
		options.IPInfoToken = ipInfoToken
	}
}