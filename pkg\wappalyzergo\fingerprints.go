// Author: chenjb
// Version: V1.0
// Date: 2025-07-18 11:12:12
// FilePath: /yaml_scan/pkg/wappalyzergo/fingerprints.go
// Description: 定义指纹数据结构和编译功能，提供技术栈识别的核心数据模型

package wappalyzergo

import "fmt"

// Fingerprint 表示单个应用的指纹特征
// 包含该应用在不同检测维度上的特征模式，用于全方位的技术栈识别
// 每个字段代表一种检测方法，支持多种匹配模式的组合使用
type Fingerprint struct {
	Cats        []int                             `json:"cats"`      // 应用分类ID列表，对应技术类型（如Web服务器、数据库、框架等）
	CSS         []string                          `json:"css"`       // CSS选择器模式，用于检测特定的CSS类名或样式
	Cookies     map[string]string                 `json:"cookies"`   // Cookie名称到模式的映射，检测特征性的Cookie
	Dom         map[string]map[string]interface{} `json:"dom"`       // DOM元素检测模式，分析HTML元素的属性和内容
	JS          map[string]string                 `json:"js"`        // JavaScript变量检测模式，检测全局变量或对象
	Headers     map[string]string                 `json:"headers"`   // HTTP头部检测模式，分析响应头中的技术信息
	HTML        []string                          `json:"html"`      // HTML内容匹配模式，检测HTML源码中的特征字符串
	Script      []string                          `json:"scripts"`   // 内联脚本检测模式，分析<script>标签内的代码
	ScriptSrc   []string                          `json:"scriptSrc"` // 外部脚本URL模式，检测引用的JavaScript文件路径
	Meta        map[string][]string               `json:"meta"`      // Meta标签检测模式，分析<meta>标签的name和content属性
	Implies     []string                          `json:"implies"`   // 隐含技术列表，当检测到此技术时自动推断的其他技术
	Description string                            `json:"description"` // 技术描述信息，提供技术的详细说明
	Website     string                            `json:"website"`   // 技术官方网站URL，用于获取更多信息
	CPE         string                            `json:"cpe"`       // 通用平台枚举标识符，用于安全漏洞关联
	Icon        string                            `json:"icon"`      // 技术图标URL或base64数据，用于UI展示
}

// Fingerprints 包含用于检测的指纹映射
// 这是从JSON文件解析得到的原始指纹数据结构，包含所有技术的完整指纹信息
type Fingerprints struct {
	// Apps 以<应用名称, 指纹>的形式组织指纹数据
	// 键为技术名称（如"nginx", "WordPress", "jQuery"），值为对应的指纹特征
	Apps map[string]*Fingerprint `json:"apps"`
}

// CompiledFingerprint 包含从技术JSON编译后的指纹数据
// 相比原始Fingerprint，该结构体包含预编译的正则表达式，提供更高的匹配性能
// 所有的字符串模式都被编译为ParsedPattern对象，避免运行时重复编译
type CompiledFingerprint struct {
	// cats 包含与此技术相关的分类ID列表
	cats []int
	// implies 包含此技术隐含的其他技术列表（如使用jQuery隐含使用JavaScript）
	implies []string
	// description 包含技术的描述信息
	description string
	// website 包含与技术相关的官方网站URL
	website string
	// icon 包含技术的图标URL或base64数据
	icon string
	// cookies 包含用于Cookie检测的编译模式映射
	cookies map[string]*ParsedPattern
	// js 包含用于JavaScript检测的编译模式映射
	js map[string]*ParsedPattern
	// dom 包含用于DOM元素检测的编译模式映射
	dom map[string]map[string]*ParsedPattern
	// headers 包含用于HTTP头部检测的编译模式映射
	headers map[string]*ParsedPattern
	// html 包含用于HTML内容检测的编译模式列表
	html []*ParsedPattern
	// script 包含用于内联脚本检测的编译模式列表
	script []*ParsedPattern
	// scriptSrc 包含用于外部脚本URL检测的编译模式列表
	scriptSrc []*ParsedPattern
	// meta 包含用于Meta标签检测的编译模式映射
	meta map[string][]*ParsedPattern
	// cpe 包含技术的CPE标识符
	cpe string
}

// CompiledFingerprints 包含用于技术检测的编译指纹映射
// 该结构体是技术识别引擎的核心数据结构，包含所有预编译的指纹模式
type CompiledFingerprints struct {
	// Apps 以<技术名称, 编译指纹>的形式组织
	// 所有正则表达式都已预编译，可直接用于高效的模式匹配
	Apps map[string]*CompiledFingerprint
}

// GetJSRules 返回JavaScript检测规则
// 该方法用于获取当前指纹的所有JavaScript变量检测模式
//
// 返回值:
//   - map[string]*ParsedPattern: JavaScript变量名到编译模式的映射
//
// 使用场景:
//   - 在浏览器环境中检测全局JavaScript变量
//   - 识别前端框架和库（如jQuery, Angular, React等）
//   - 检测JavaScript插件和组件
func (f *CompiledFingerprint) GetJSRules() map[string]*ParsedPattern {
	return f.js
}

// GetDOMRules 返回DOM元素检测规则
// 该方法用于获取当前指纹的所有DOM元素检测模式
//
// 返回值:
//   - map[string]map[string]*ParsedPattern: DOM选择器到属性模式映射的二级映射
//     * 第一级键为DOM选择器（如"div.container", "#header"）
//     * 第二级键为属性名称（如"class", "id", "data-*"）
//     * 值为对应的编译模式
//
// 使用场景:
//   - 检测特定的HTML结构和CSS类名
//   - 识别前端框架的特征DOM元素
//   - 分析页面布局和组件结构
func (f *CompiledFingerprint) GetDOMRules() map[string]map[string]*ParsedPattern {
	return f.dom
}

// AppInfo 包含应用的基本信息
// 该结构体用于向用户展示识别到的技术的详细信息
type AppInfo struct {
	Description string   // 技术描述，说明技术的用途和特点
	Website     string   // 官方网站URL，用户可访问获取更多信息
	CPE         string   // 通用平台枚举标识符，用于安全漏洞数据库关联
	Icon        string   // 技术图标，用于UI界面展示
	Categories  []string // 技术分类列表，如["Web服务器", "反向代理"]
}

// CatsInfo 包含应用的分类信息
// 该结构体专门用于技术分类相关的操作和统计
type CatsInfo struct {
	Cats []int // 分类ID列表，对应预定义的技术分类
}

// matchPartResult 表示指纹匹配的结果
// 该结构体包含单个技术识别的完整信息，用于返回匹配结果
type matchPartResult struct {
	application string // 技术名称，如"nginx", "WordPress", "jQuery"
	version     string // 技术版本号，如"1.18.0", "5.8", "3.6.0"
	confidence  int    // 匹配置信度（0-100），表示识别结果的可信程度
}

// compileFingerprint 将原始指纹编译为可执行的指纹模式
// 该函数是指纹编译的核心，将JSON格式的字符串模式转换为预编译的正则表达式
//
// 参数:
//   - fingerprint: 原始指纹数据，包含各种字符串模式
//
// 返回值:
//   - *CompiledFingerprint: 编译后的指纹，包含预编译的正则表达式
//
// 编译过程:
//   1. 复制基本元数据（分类、描述、网站等）
//   2. 遍历各种检测模式（DOM、Cookie、JS、Headers等）
//   3. 将每个字符串模式解析并编译为ParsedPattern
//   4. 构建优化的数据结构以提高匹配性能
//
// 性能优化:
//   - 预编译所有正则表达式，避免运行时编译开销
//   - 使用适当的数据结构减少内存占用
//   - 跳过无效或错误的模式，确保系统稳定性
func compileFingerprint(fingerprint *Fingerprint) *CompiledFingerprint {
	// 初始化编译后的指纹结构，复制基本元数据
	compiled := &CompiledFingerprint{
		cats:        fingerprint.Cats,        // 复制分类ID列表
		implies:     fingerprint.Implies,     // 复制隐含技术列表
		description: fingerprint.Description, // 复制技术描述
		website:     fingerprint.Website,     // 复制官方网站URL
		icon:        fingerprint.Icon,        // 复制图标信息
		dom:         make(map[string]map[string]*ParsedPattern), // 初始化DOM检测映射
		cookies:     make(map[string]*ParsedPattern),            // 初始化Cookie检测映射
		js:          make(map[string]*ParsedPattern),            // 初始化JS检测映射
		headers:     make(map[string]*ParsedPattern),            // 初始化Headers检测映射
		html:        make([]*ParsedPattern, 0, len(fingerprint.HTML)),      // 预分配HTML模式切片
		script:      make([]*ParsedPattern, 0, len(fingerprint.Script)),    // 预分配Script模式切片
		scriptSrc:   make([]*ParsedPattern, 0, len(fingerprint.ScriptSrc)), // 预分配ScriptSrc模式切片
		meta:        make(map[string][]*ParsedPattern),                      // 初始化Meta检测映射
		cpe:         fingerprint.CPE, // 复制CPE标识符
	}

	// 编译DOM元素检测模式
	// DOM模式支持多种检测方式：元素存在性、文本内容、属性值等
	for dom, patterns := range fingerprint.Dom {
		compiled.dom[dom] = make(map[string]*ParsedPattern)

		// 遍历DOM元素的各种检测属性
		for attr, value := range patterns {
			switch attr {
			case "exists", "text":
				// 处理元素存在性检测和文本内容检测
				pattern, err := ParsePattern(value.(string))
				if err != nil {
					continue // 跳过无效模式，确保系统稳定性
				}
				compiled.dom[dom]["main"] = pattern
			case "attributes":
				// 处理元素属性检测
				attrMap, ok := value.(map[string]interface{})
				if !ok {
					continue // 类型断言失败，跳过此模式
				}
				compiled.dom[dom] = make(map[string]*ParsedPattern)
				// 编译每个属性的检测模式
				for attrName, value := range attrMap {
					pattern, err := ParsePattern(value.(string))
					if err != nil {
						continue // 跳过编译失败的模式
					}
					compiled.dom[dom][attrName] = pattern
				}
			}
		}
	}

	// 编译Cookie检测模式
	// Cookie模式用于检测特征性的Cookie名称和值
	for header, pattern := range fingerprint.Cookies {
		fingerprint, err := ParsePattern(pattern)
		if err != nil {
			continue // 跳过编译失败的Cookie模式
		}
		compiled.cookies[header] = fingerprint
	}

	// 编译JavaScript变量检测模式
	// JS模式用于检测全局JavaScript变量和对象
	for k, pattern := range fingerprint.JS {
		fingerprint, err := ParsePattern(pattern)
		if err != nil {
			continue // 跳过编译失败的JS模式
		}
		compiled.js[k] = fingerprint
	}

	// 编译HTTP头部检测模式
	// Headers模式是最常用的检测方式，包含服务器、框架等信息
	for header, pattern := range fingerprint.Headers {
		fingerprint, err := ParsePattern(pattern)
		if err != nil {
			continue // 跳过编译失败的Headers模式
		}
		compiled.headers[header] = fingerprint
	}

	// 编译HTML内容检测模式
	// HTML模式用于检测页面源码中的特征字符串
	for _, pattern := range fingerprint.HTML {
		fingerprint, err := ParsePattern(pattern)
		if err != nil {
			continue // 跳过编译失败的HTML模式
		}
		compiled.html = append(compiled.html, fingerprint)
	}

	// 编译内联脚本检测模式
	// Script模式用于检测<script>标签内的代码特征
	for _, pattern := range fingerprint.Script {
		fingerprint, err := ParsePattern(pattern)
		if err != nil {
			continue // 跳过编译失败的Script模式
		}
		compiled.script = append(compiled.script, fingerprint)
	}

	// 编译外部脚本URL检测模式
	// ScriptSrc模式用于检测引用的外部JavaScript文件路径
	for _, pattern := range fingerprint.ScriptSrc {
		fingerprint, err := ParsePattern(pattern)
		if err != nil {
			continue // 跳过编译失败的ScriptSrc模式
		}
		compiled.scriptSrc = append(compiled.scriptSrc, fingerprint)
	}

	// 编译Meta标签检测模式
	// Meta模式用于检测<meta>标签的name和content属性
	for meta, patterns := range fingerprint.Meta {
		var compiledList []*ParsedPattern

		// 编译该Meta标签的所有检测模式
		for _, pattern := range patterns {
			fingerprint, err := ParsePattern(pattern)
			if err != nil {
				continue // 跳过编译失败的Meta模式
			}
			compiledList = append(compiledList, fingerprint)
		}
		compiled.meta[meta] = compiledList
	}
	return compiled
}

// part 表示指纹匹配的部分类型
// 用于标识当前正在匹配的是哪种类型的数据（Cookie、JS、Headers等）
type part int

// 定义可匹配的指纹部分常量
// 这些常量用于在匹配过程中标识数据类型，便于选择合适的匹配策略
const (
	cookiesPart part = iota + 1 // Cookie检测部分，用于匹配HTTP Cookie
	jsPart                      // JavaScript检测部分，用于匹配JS变量和对象
	headersPart                 // HTTP头部检测部分，用于匹配响应头
	htmlPart                    // HTML内容检测部分，用于匹配页面源码
	scriptPart                  // 脚本检测部分，用于匹配内联和外部脚本
	metaPart                    // Meta标签检测部分，用于匹配Meta标签属性
)

// matchMapString 对键值对映射进行指纹匹配
// 该方法是核心匹配函数之一，用于处理具有键值对结构的数据（如HTTP头部、Cookie、Meta标签）
//
// 参数:
//   - keyValue: 待匹配的键值对映射，键为属性名，值为属性值
//   - part: 指纹匹配类型，指定当前匹配的数据类型
//
// 返回值:
//   - []matchPartResult: 匹配到的技术列表，包含技术名称、版本和置信度
//
// 匹配逻辑:
//   1. 遍历所有已编译的指纹
//   2. 根据part类型选择相应的匹配策略
//   3. 对每个指纹的相关模式进行匹配
//   4. 提取版本信息和置信度
//   5. 处理隐含技术关系
//
// 支持的匹配类型:
//   - cookiesPart: 匹配Cookie名称和值
//   - headersPart: 匹配HTTP响应头
//   - metaPart: 匹配Meta标签的name和content属性
func (f *CompiledFingerprints) matchMapString(keyValue map[string]string, part part) []matchPartResult {
	var matched bool
	var technologies []matchPartResult

	// 遍历所有应用的指纹进行匹配
	for app, fingerprint := range f.Apps {
		var version string
		confidence := 100

		// 根据匹配类型选择相应的匹配策略
		switch part {
		case cookiesPart:
			// 匹配Cookie指纹
			for data, pattern := range fingerprint.cookies {
				value, ok := keyValue[data]
				if !ok {
					continue // 如果键值对中不存在该Cookie，跳过
				}
				if pattern == nil {
					matched = true // 如果模式为空，表示只检查存在性
				}
				// 使用编译的模式进行匹配和版本提取
				if valid, versionString := pattern.Evaluate(value); valid {
					matched = true
					if version == "" && versionString != "" {
						version = versionString
					}
					confidence = pattern.Confidence
					break
				}
			}
		case headersPart:
			// 匹配HTTP头部指纹
			for data, pattern := range fingerprint.headers {
				value, ok := keyValue[data]
				if !ok {
					continue // 如果键值对中不存在该头部，跳过
				}

				// 使用编译的模式进行匹配和版本提取
				if valid, versionString := pattern.Evaluate(value); valid {
					matched = true
					if version == "" && versionString != "" {
						version = versionString
					}
					confidence = pattern.Confidence
					break
				}
			}
		case metaPart:
			// 匹配Meta标签指纹
			for data, patterns := range fingerprint.meta {
				value, ok := keyValue[data]
				if !ok {
					continue // 如果键值对中不存在该Meta标签，跳过
				}

				// 遍历该Meta标签的所有模式
				for _, pattern := range patterns {
					if valid, versionString := pattern.Evaluate(value); valid {
						matched = true
						if version == "" && versionString != "" {
							version = versionString
						}
						confidence = pattern.Confidence
						break
					}
				}
			}
		}

		// 如果没有匹配，继续下一个指纹
		if !matched {
			continue
		}

		// 添加匹配的技术到结果列表
		technologies = append(technologies, matchPartResult{
			application: app,
			version:     version,
			confidence:  confidence,
		})
		
		// 处理隐含技术关系
		// 如果当前技术隐含其他技术，也将其添加到结果中
		if len(fingerprint.implies) > 0 {
			for _, implies := range fingerprint.implies {
				technologies = append(technologies, matchPartResult{
					application: implies,
					confidence:  confidence,
				})
			}
		}
		matched = false // 重置匹配状态，准备下一个指纹
	}
	return technologies
}

// matchString 对字符串进行指纹匹配
// 该方法用于处理单一字符串数据的匹配（如HTML内容、JavaScript代码、脚本URL）
//
// 参数:
//   - data: 待匹配的字符串数据
//   - part: 指纹匹配类型，指定当前匹配的数据类型
//
// 返回值:
//   - []matchPartResult: 匹配到的技术列表，包含技术名称、版本和置信度
//
// 匹配逻辑:
//   1. 遍历所有已编译的指纹
//   2. 根据part类型选择相应的模式集合
//   3. 对每个模式进行字符串匹配
//   4. 提取版本信息和置信度
//   5. 处理隐含技术关系
//
// 支持的匹配类型:
//   - jsPart: 匹配JavaScript变量和对象
//   - scriptPart: 匹配外部脚本URL
//   - htmlPart: 匹配HTML内容特征
func (f *CompiledFingerprints) matchString(data string, part part) []matchPartResult {
	var matched bool
	var technologies []matchPartResult

	// 遍历所有应用的指纹进行匹配
	for app, fingerprint := range f.Apps {
		var version string
		confidence := 100

		// 根据匹配类型选择相应的匹配策略
		switch part {
		case jsPart:
			// 匹配JavaScript指纹
			for _, pattern := range fingerprint.js {
				if valid, versionString := pattern.Evaluate(data); valid {
					matched = true
					if version == "" && versionString != "" {
						version = versionString
					}
					confidence = pattern.Confidence
				}
			}
		case scriptPart:
			// 匹配外部脚本URL指纹
			for _, pattern := range fingerprint.scriptSrc {
				if valid, versionString := pattern.Evaluate(data); valid {
					matched = true
					if version == "" && versionString != "" {
						version = versionString
					}
					confidence = pattern.Confidence
				}
			}
		case htmlPart:
			// 匹配HTML内容指纹
			for _, pattern := range fingerprint.html {
				if valid, versionString := pattern.Evaluate(data); valid {
					matched = true
					if version == "" && versionString != "" {
						version = versionString
					}
					confidence = pattern.Confidence
				}
			}
		}

		// 如果没有匹配，继续下一个指纹
		if !matched {
			continue
		}

		// 添加匹配的技术和隐含技术到结果列表
		technologies = append(technologies, matchPartResult{
			application: app,
			version:     version,
			confidence:  confidence,
		})
		if len(fingerprint.implies) > 0 {
			for _, implies := range fingerprint.implies {
				technologies = append(technologies, matchPartResult{
					application: implies,
					confidence:  confidence,
				})
			}
		}
		matched = false // 重置匹配状态，准备下一个指纹
	}
	return technologies
}

// matchKeyValueString 对特定键值对进行指纹匹配
// 该方法用于精确匹配特定键名的值（如特定的Cookie名称、HTTP头部名称、Meta标签名称）
//
// 参数:
//   - key: 要匹配的键名（如Cookie名称、头部名称、Meta标签名称）
//   - value: 对应的值
//   - part: 指纹匹配类型，指定当前匹配的数据类型
//
// 返回值:
//   - []matchPartResult: 匹配到的技术列表，包含技术名称、版本和置信度
//
// 匹配逻辑:
//   1. 遍历所有已编译的指纹
//   2. 根据part类型选择相应的匹配策略
//   3. 只匹配指定键名的模式
//   4. 对匹配的模式进行值匹配
//   5. 提取版本信息和置信度
//   6. 处理隐含技术关系
//
// 使用场景:
//   - 精确匹配特定的Cookie（如"PHPSESSID"）
//   - 精确匹配特定的HTTP头部（如"Server"）
//   - 精确匹配特定的Meta标签（如"generator"）
func (f *CompiledFingerprints) matchKeyValueString(key, value string, part part) []matchPartResult {
	var matched bool
	var technologies []matchPartResult

	// 遍历所有应用的指纹进行匹配
	for app, fingerprint := range f.Apps {
		var version string
		confidence := 100

		// 根据匹配类型选择相应的匹配策略
		switch part {
		case cookiesPart:
			// 精确匹配指定Cookie名称的指纹
			for data, pattern := range fingerprint.cookies {
				if data != key {
					continue // 如果Cookie名称不匹配，跳过
				}

				// 对Cookie值进行模式匹配
				if valid, versionString := pattern.Evaluate(value); valid {
					matched = true
					if version == "" && versionString != "" {
						version = versionString
					}
					confidence = pattern.Confidence
					break
				}
			}
		case headersPart:
			// 精确匹配指定HTTP头部名称的指纹
			for data, pattern := range fingerprint.headers {
				if data != key {
					continue // 如果头部名称不匹配，跳过
				}

				// 对头部值进行模式匹配
				if valid, versionString := pattern.Evaluate(value); valid {
					matched = true
					if version == "" && versionString != "" {
						version = versionString
					}
					confidence = pattern.Confidence
					break
				}
			}
		case metaPart:
			// 精确匹配指定Meta标签名称的指纹
			for data, patterns := range fingerprint.meta {
				if data != key {
					continue // 如果Meta标签名称不匹配，跳过
				}

				// 遍历该Meta标签的所有模式
				for _, pattern := range patterns {
					if valid, versionString := pattern.Evaluate(value); valid {
						matched = true
						if version == "" && versionString != "" {
							version = versionString
						}
						confidence = pattern.Confidence
						break
					}
				}
			}
		}

		// 如果没有匹配，继续下一个指纹
		if !matched {
			continue
		}

		// 添加匹配的技术到结果列表
		technologies = append(technologies, matchPartResult{
			application: app,
			version:     version,
			confidence:  confidence,
		})
		
		// 处理隐含技术关系
		if len(fingerprint.implies) > 0 {
			for _, implies := range fingerprint.implies {
				technologies = append(technologies, matchPartResult{
					application: implies,
					confidence:  confidence,
				})
			}
		}
		matched = false // 重置匹配状态，准备下一个指纹
	}
	return technologies
}

// FormatAppVersion 格式化应用名称和版本号
// 该函数用于统一应用标识的格式，便于结果展示和后续处理
//
// 参数:
//   - app: 应用名称（如"nginx", "WordPress", "jQuery"）
//   - version: 版本号字符串（如"1.18.0", "5.8", "3.6.0"）
//
// 返回值:
//   - string: 格式化后的应用标识
//     * 如果version为空，返回应用名称
//     * 如果version不为空，返回"应用名:版本号"格式
//
// 使用示例:
//   FormatAppVersion("nginx", "1.18.0")     // 返回: "nginx:1.18.0"
//   FormatAppVersion("WordPress", "")       // 返回: "WordPress"
//   FormatAppVersion("jQuery", "3.6.0")     // 返回: "jQuery:3.6.0"
func FormatAppVersion(app, version string) string {
	if version == "" {
		return app
	}
	return fmt.Sprintf("%s:%s", app, version)
}

// GetFingerprints 返回来自wappalyzer的指纹字符串
// 该函数提供对内嵌指纹数据的访问，通常用于调试或数据导出
//
// 返回值:
//   - string: JSON格式的指纹数据字符串，包含所有技术的完整指纹信息
//
// 注意事项:
//   - 返回的是原始JSON字符串，需要进一步解析才能使用
//   - 该数据通常在编译时嵌入，包含数千种技术的指纹信息
//   - 数据大小可能较大，建议仅在必要时调用
func GetFingerprints() string {
	return fingerprints
}


