// 
// Author: chenjb
// Version: V1.0
// Date: 2025-05-16 10:44:02
// FilePath: /yaml_scan/pkg/hybridMap/file/strategy.go
// Description:  定义文件数据库的去重策略，支持多种不同的去重机制
package filekv

// Strategy 去重策略类型
type Strategy uint8

const (
	// None 不进行去重处理
	None Strategy = iota

	// MemoryMap 使用Go标准map进行去重，无淘汰机制
	// 内存消耗会随着项目数量线性增长
	MemoryMap

	// MemoryLRU 使用LRU缓存进行去重，保留最近使用的固定数量项目
	MemoryLRU

	// MemoryFilter 使用布隆过滤器进行概率性去重
	MemoryFilter
	
	// DiskFilter 使用磁盘键值存储进行完整去重
	DiskFilter
)

