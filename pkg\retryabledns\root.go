//
//Author: chenjb
//Version: V1.0
//Date: 2025-04-25 14:48:29
//FilePath: /yaml_scan/pkg/retryabledns/root.go
//Description:

package retryabledns

// RootDNS表 示一个根 DNS 服务器的配置信息
type RootDNS struct {
	Host     string // 根服务器的主机名
	IPv4     string // 根服务器的 IPv4 地址
	IPv6     string // 根服务器的 IPv6 地址
	Operator string // 运营该根服务器的机构名称
}

// https://www.iana.org/domains/root/servers

// RootDNSServers 包含所有根 DNS 服务器的配置列表。
var RootDNSServers = []RootDNS{
	{"a.root-servers.net", "**********", "2001:503:ba3e::2:30", "Verisign, Inc"},
	{"b.root-servers.net", "*************", "2801:1b8:10::b", "University of Southern California, Information Sciences Institute"},
	{"c.root-servers.net", "***********", "2001:500:2::c", "Cogent Communications"},
	{"d.root-servers.net", "***********", "2001:500:2d::d", "University of Maryland"},
	{"e.root-servers.net", "**************", "2001:500:a8::e", "NASA (Ames Research Center)"},
	{"f.root-servers.net", "***********", "2001:500:2f::f", "Internet Systems Consortium, Inc."},
	{"g.root-servers.net", "************", "2001:500:12::d0d", "US Department of Defense (NIC)"},
	{"h.root-servers.net", "*************", "2001:500:1::53", "US Army (Research Lab)"},
	{"i.root-servers.net", "*************", "2001:7fe::53", "Netnod"},
	{"j.root-servers.net", "*************", "2001:503:c27::2:30", "Verisign, Inc"},
	{"k.root-servers.net", "************", "2001:7fd::1", "RIPE NCC"},
	{"l.root-servers.net", "***********", "2001:500:9f::42", "ICANN"},
	{"m.root-servers.net", "************", "2001:dc3::35", "WIDE Project"},
}

// RootDNSServersIPv4 包含所有根 DNS 服务器的 IPv4 地址列表（带端口）。
var RootDNSServersIPv4 = []string{
	"**********:53", "*************:53", "***********:53", "***********:53",
	"**************:53", "***********:53", "************:53", "*************:53",
	"*************:53", "*************:53", "************:53", "***********:53",
	"************:53",
}
