//
// Author: chenjb
// Version: V1.0
// Date: 2025-04-29 19:22:32
// FilePath: /yaml_scan/pkg/gcache/clock_test.go
// Description:

package gcache

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// TestRealClock 测试 RealClock 的功能
func TestRealClock(t *testing.T) {
	rc := NewRealClock()                                    // 创建 RealClock 实例
	now := rc.Now()                                         // 获取当前时间
	require.False(t, now.IsZero())                          // 验证时间不是零值
	require.WithinDuration(t, time.Now(), now, time.Second) // 验证时间与系统时间相差不超过 1 秒
}

// TestFakeClock 测试 FakeClock 的功能
func TestFakeClock(t *testing.T) {
	fc := NewFakeClock()                                                                // 创建 FakeClock 实例
	initialTime := fc.Now()                                                             // 获取初始时间
	require.Equal(t, time.Date(1984, time.April, 4, 0, 0, 0, 0, time.UTC), initialTime) // 验证初始时间正确

	// 测试时间推进
	duration := 1 * time.Hour                                 // 定义推进的时长：1 小时
	fc.Advance(duration)                                      // 推进时间
	advancedTime := fc.Now()                                  // 获取推进后的时间
	require.Equal(t, initialTime.Add(duration), advancedTime) // 验证时间推进正确

	// 测试再次推进时间
	fc.Advance(2 * time.Hour)                                 // 再次推进 2 小时
	finalTime := fc.Now()                                     // 获取最终时间
	require.Equal(t, initialTime.Add(3*time.Hour), finalTime) // 验证总计推进 3 小时
}
