// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 17:00:58
// FilePath: /yaml_scan/pkg/retryablehttp/trace.go
// Description:定义HTTP请求跟踪相关的数据结构，用于收集和存储HTTP请求的各个阶段的时间和信息
package retryablehttp

import "time"

// TraceEventInfo 存储跟踪事件的信息，包括事件发生时间和相关数据
type TraceEventInfo struct {
	Time time.Time   // 事件发生的时间点
	Info interface{} // 事件的相关信息，类型不限，可以是连接信息、DNS查询结果等
}

// TraceInfo 包含HTTP请求生命周期中各个关键事件的跟踪信息
type TraceInfo struct {
	GotConn              TraceEventInfo // 获取到连接时的信息，包括是否重用连接等
	DNSDone              TraceEventInfo // DNS解析完成时的信息
	GetConn              TraceEventInfo // 开始获取连接时的信息
	PutIdleConn          TraceEventInfo // 将连接放回连接池时的信息
	GotFirstResponseByte TraceEventInfo // 收到第一个响应字节时的信息
	Got100Continue       TraceEventInfo // 收到100 Continue响应时的信息
	DNSStart             TraceEventInfo // 开始DNS解析时的信息
	ConnectStart         TraceEventInfo // 开始建立TCP连接时的信息
	ConnectDone          TraceEventInfo // TCP连接建立完成时的信息
	TLSHandshakeStart    TraceEventInfo // 开始TLS握手时的信息
	TLSHandshakeDone     TraceEventInfo // TLS握手完成时的信息
	WroteHeaders         TraceEventInfo // 写入请求头完成时的信息
	WroteRequest         TraceEventInfo // 写入完整请求完成时的信息
}
