package config

import (
	"fmt"
	"os"
	"path"
	"path/filepath"
	"runtime"
	"sync"
	"time"

	"gopkg.in/yaml.v2"
)

type Config struct {
	Log      LogConfig      `yaml:"log"`
	Flag     FlagConfig     `yaml:"flag"`
	Template TemplateConfig `yaml:"template"`
	Global   GlobalConfig   `yaml:"global"`
	Timeout  TimeoutConfig  `yaml:"timeout"`
}

// DefaultConfig 默认配置
// 所有配置值和默认值都集中在这里
var DefaultConfig *Config

// LogConfig 包含日志相关的配置
type LogConfig struct {
	Location         string        `yaml:"location"`
	Rotate           bool          `yaml:"rotate"`
	Rotationcheck    string        `yaml:"rotate_check"`
	CheckDuration    time.Duration `yaml:"-"` // 转换后的 time.Duration
	RotationInterval string        `yaml:"rotation_interval"`
	RotationDuration time.Duration `yaml:"-"` // 转换后的 time.Duration
	MaxSize          int           `yaml:"max_size"`
	Compress         bool          `yaml:"compress"`
	BackupTimeFormat string        `yaml:"backup_time_format"`
	ArchiveFormat    string        `yaml:"archive_format"`
	RotateEachHour   bool          `yaml:"rotate_each_hour"`
	RotateEachDay    bool          `yaml:"rotate_each_day"`
	MaxLevel         string        `yaml:"max_level"`
	FileName         string        `yaml:"file_name"`
}

// 命令行参数配置
type FlagConfig struct {
	CaseSensitive bool   `yaml:"caseSensitive"`
	Description   string `yaml:"description"`
	MaxRedirects  int    `yaml:"max_redirects"`
}

// 模板配置
type TemplateConfig struct {
	TemplatesDirectory string `yaml:"templates_directory"`
}

// 全局配置
type GlobalConfig struct {
	configDir string `yaml:"-"` // 全局配置文件路径
}

// 超时配置
type TimeoutConfig struct {
	DialTimeout                int `yaml:"dial_timeout"`
	TcpReadTimeout             int `yaml:tcp_read_timeout`
	HttpResponseHeaderTimeout  int `yaml:http_response_header_timeout`
	HttpTimeout                int `yaml:http_timeout`
	JsCompilerExecutionTimeout int `yaml:jscompiler_execution_timeout`
	CodeExecutionTimeout       int `yaml:code_execution_timeout`
}

// 全局的 Config 实例
var (
	config *Config
	once   sync.Once
)

// loadConfig 加载配置文件
func loadConfig(filename string) error {
	var absPath string
	_, dir, _, ok := runtime.Caller(0)
	if ok {
		absPath = path.Dir(dir)
	}
	absPath = filepath.Join(absPath, filename)

	file, err := os.ReadFile(absPath)
	if err != nil {
		return err
	}

	config = &Config{}
	if err := yaml.Unmarshal(file, config); err != nil {
		return err
	}

	// 转换时间
	if err := parseDurations(); err != nil {
		return err
	}

	return nil
}

// parseDurations 解析时间相关的配置
func parseDurations() error {
	rotationInterval, err := time.ParseDuration(config.Log.RotationInterval)
	if err != nil {
		return fmt.Errorf("invalid rotation interval: %v", err)
	}
	config.Log.RotationDuration = rotationInterval

	checkDuration, err := time.ParseDuration(config.Log.Rotationcheck)
	if err != nil {
		return fmt.Errorf("invalid rotate check duration: %v", err)
	}
	config.Log.CheckDuration = checkDuration

	return nil
}

// GetLogConfig 获取日志配置
func GetLogConfig() *LogConfig {
	return &config.Log
}

// GetFlagConfig 获取命令行配置
func GetFlagConfig() *FlagConfig {
	return &config.Flag
}

func GetTimeoutConfig() *TimeoutConfig {
	return &config.Timeout
}
// GetTemplateDir 获取模板目录的绝对路径
func (c *Config) GetTemplateDir() string {
	val, _ := filepath.Abs(c.Template.TemplatesDirectory)
	return val
}

// GetKeysDir 获取模板签名keys目录
func (c *Config) GetKeysDir() string {
	return filepath.Join(c.Global.configDir, "keys")
}

func init() {
	once.Do(func() {
		if err := loadConfig("setting.yaml"); err != nil {
			panic(fmt.Sprintf("Failed to load config: %v", err))
		}
	})
}
