package rand

import (
	"testing"

	"github.com/stretchr/testify/require"
)

func TestIntN(t *testing.T) {
	tests := []struct {
		max      int
		expected bool // true if the result is in the range [0, max)
	}{
		{10, true},
		{0, false}, // should return an error
		{-5, false}, // should return an error
		{1, true}, // should return 0
	}

	for _, test := range tests {
		result, err := IntN(test.max)
		if test.expected {
			require.NoError(t, err)
			require.GreaterOrEqual(t, result, 0)
			require.Less(t, result, test.max)
		} else {
			require.Error(t, err)
		}
	}
}