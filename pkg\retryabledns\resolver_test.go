package retryabledns

import (
	"testing"

	"github.com/stretchr/testify/require"
)


// TestNetworkResolver_String 测试NetworkResolver的String()方法
func TestNetworkResolverString(t *testing.T) {
    resolver := NetworkResolver{
        Protocol: UDP,
        Host:     "example.com",
        Port:     "53",
    }

    expected := "example.com:53"
    if resolver.String() != expected {
        t.Errorf("Expected String() to be '%s', got '%s'", expected, resolver.String())
    }
}

// TestDohResolver_Method 测试DohResolver的Method()方法
func TestDohResolverMethod(t *testing.T) {
    // 测试POST协议
    resolver := DohResolver{
        Protocol: POST,
        URL:      "https://doh.example.com/dns-query",
    }

    expected := "post"
    if resolver.Method() != expected {
        t.Errorf("Expected Method() to be '%s', got '%s'", expected, resolver.Method())
    }

    // 测试GET协议
    resolver = DohResolver{
        Protocol: GET,
        URL:      "https://doh.example.com/dns-query",
    }

    expected = "get"
    if resolver.Method() != expected {
        t.Errorf("Expected Method() to be '%s', got '%s'", expected, resolver.Method())
    }

    // 测试未知协议
    resolver = DohResolver{
        Protocol: DohProtocol("unknown"),
        URL:      "https://doh.example.com/dns-query",
    }

    expected = "get"
    if resolver.Method() != expected {
        t.Errorf("Expected Method() to be '%s' for unknown protocol, got '%s'", expected, resolver.Method())
    }
}

// TestDohResolver_String 测试DohResolver的String()方法
func TestDohResolverString(t *testing.T) {
    resolver := DohResolver{
        Protocol: GET,
        URL:      "https://doh.example.com/dns-query",
    }

    expected := "https://doh.example.com/dns-query"
    if resolver.String() != expected {
        t.Errorf("Expected String() to be '%s', got '%s'", expected, resolver.String())
    }
}

func TestTrimProtocol(t *testing.T) {
	tests := []struct {
		name     string
		resolver string
		expected string
	}{
		{
			name:     "UDP protocol",
			resolver: "udp:*******:53",
			expected: "*******:53",
		},
		{
			name:     "TCP protocol",
			resolver: "tcp:*******:53",
			expected: "*******:53",
		},
		{
			name:     "DOH protocol",
			resolver: "doh:example.com:443",
			expected: "example.com:443",
		},
		{
			name:     "DOT protocol",
			resolver: "dot:example.com:853",
			expected: "example.com:853",
		},
		{
			name:     "No protocol",
			resolver: "*******:53",
			expected: "*******:53",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := trimProtocol(tt.resolver)
			require.Equal(t, tt.expected, result)
		})
	}
}

// TestParseHostPort 测试 parseHostPort 函数的正确性
func TestParseHostPort(t *testing.T) {
	// 定义测试用例
	tests := []struct {
		name          string      // 测试用例名称
		input         string      // 输入的主机和端口字符串
		protocol      Protocol    // 使用的协议
		expectedHost  string      // 预期的主机名
		expectedPort  string      // 预期的端口号
	}{
		{
			name:         "With port", // 测试用例名称
			input:        "*******:53", // 输入字符串
			protocol:     UDP,          // 使用的协议
			expectedHost: "*******",    // 预期主机名
			expectedPort: "53",         // 预期端口号
		},
		{
			name:         "Without port",
			input:        "*******",
			protocol:     UDP,
			expectedHost: "*******",
			expectedPort: "53",
		},
		{
			name:         "With DOT protocol without port",
			input:        "example.com",
			protocol:     DOT,
			expectedHost: "example.com",
			expectedPort: "853",
		},
		{
			name:         "With DOT protocol with port",
			input:        "example.com:853",
			protocol:     DOT,
			expectedHost: "example.com",
			expectedPort: "853",
		},
	}

	// 遍历每个测试用例
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建一个 NetworkResolver 实例，并设置协议
			resolver := &NetworkResolver{Protocol: tt.protocol}
			// 调用 parseHostPort 函数进行解析
			parseHostPort(resolver, tt.input)
			// 断言解析结果是否符合预期
			require.Equal(t, tt.expectedHost, resolver.Host)
			require.Equal(t, tt.expectedPort, resolver.Port)
		})
	}
}


// TestParseResolver 测试 parseResolver 函数的正确性
func TestParseResolver(t *testing.T) {
	tests := []struct {
		name     string      // 测试用例名称
		input    string      // 输入的解析器字符串
		expected string      // 预期的解析器字符串表示
	}{
		{
			name:     "Valid DOH resolver",
			input:    "doh:example.com:443",
			expected: "example.com:443", // 预期的解析器字符串表示
		},
		{
			name:     "Valid UDP resolver",
			input:    "udp:*******:53",
			expected: "*******:53",
		},
		{
			name:     "Valid TCP resolver",
			input:    "tcp:*******:53",
			expected: "*******:53",
		},
		{
			name:     "Valid DOT resolver",
			input:    "dot:example.com:853",
			expected: "example.com:853",
		},
	}

	// 遍历每个测试用例
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 调用 parseResolver 函数进行解析
			resolver := parseResolver(tt.input)
			// 断言解析结果是否符合预期
			require.Equal(t, tt.expected, resolver.String())
		})
	}
}


// TestParseResolvers 测试 parseResolvers 函数的正确性
func TestParseResolvers(t *testing.T) {
	tests := []struct {
		name     string   // 测试用例名称
		input    []string // 输入的解析器字符串切片
		expected []string // 预期的解析器字符串表示切片
	}{
		{
			name:     "Multiple resolvers",
			input:    []string{"udp:*******:53", "tcp:*******:53", "doh:example.com:443"},
			expected: []string{"*******:53", "*******:53", "example.com:443"},
		},
		{
			name:     "Single resolver",
			input:    []string{"dot:example.com:853"},
			expected: []string{"example.com:853"},
		},
		{
			name:     "Empty input",
			input:    []string{},
			expected: []string{},
		},
	}

	// 遍历每个测试用例
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 调用 parseResolvers 函数进行解析
			resolvers := parseResolvers(tt.input)

			// 断言解析结果的长度是否与预期相同
			require.Equal(t, len(tt.expected), len(resolvers))

			// 逐个断言每个解析器的字符串表示是否符合预期
			for i, resolver := range resolvers {
				require.Equal(t, tt.expected[i], resolver.String())
			}
		})
	}
}