// Author: chenjb
// Version: V1.0
// Date: 2025-06-10 16:28:39
// FilePath: /yaml_scan/pkg/retryablehttp/request_test.go
// Description:
package retryablehttp

import (
	"context"
	"net/http"
	"strings"
	"testing"
	urlutil "yaml_scan/utils/url"

	"github.com/stretchr/testify/require"
)

// TestNewRequest 测试创建新请求的功能
func TestNewRequest(t *testing.T) {

	// 测试用例1: 创建GET请求
	t.Run("创建GET请求", func(t *testing.T) {
		r := require.New(t)
		url := "https://example.com/path?query=value"

		req, err := NewRequest("GET", url, nil)
		r.NoError(err, "创建请求不应有错误")
		r.NotNil(req, "请求不应为nil")
		r.Equal("GET", req.Method, "请求方法应为GET")
		r.Equal("example.com", req.Host, "主机名应为example.com")
		r.Equal("/path", req.URL.Path, "路径应为/path")
		r.Equal("query=value", req.URL.RawQuery, "查询字符串应正确")
	})

	// 测试用例2: 创建带请求体的POST请求
	t.Run("创建带请求体的POST请求", func(t *testing.T) {
		r := require.New(t)
		url := "https://example.com/api"
		body := strings.NewReader("test=data")

		req, err := NewRequest("POST", url, body)
		r.NoError(err, "创建请求不应有错误")
		r.NotNil(req, "请求不应为nil")
		r.Equal("POST", req.Method, "请求方法应为POST")

		// 验证请求体
		bodyBytes, err := req.BodyBytes()
		r.NoError(err, "读取请求体不应有错误")
		r.Equal([]byte("test=data"), bodyBytes, "请求体应正确")
	})

	// 测试用例3: 创建带上下文的请求
	t.Run("创建带上下文的请求", func(t *testing.T) {
		r := require.New(t)
		url := "https://example.com/api"
		ctx := context.WithValue(context.Background(), "testKey", "testValue")

		req, err := NewRequestWithContext(ctx, "GET", url, nil)
		r.NoError(err, "创建请求不应有错误")
		r.NotNil(req, "请求不应为nil")
		r.Equal("testValue", req.Context().Value("testKey"), "上下文值应正确传递")
	})
}


// TestRequestClone 测试请求克隆功能
func TestRequestClone(t *testing.T) {
	r := require.New(t)

	// 创建一个原始请求
	originalReq, err := NewRequest("POST", "https://example.com/api", strings.NewReader("test=data"))
	r.NoError(err, "创建请求不应有错误")

	// 添加认证信息
	originalReq.Auth = &Auth{
		Type:     DigestAuth,
		Username: "testuser",
		Password: "testpass",
	}

	// 克隆请求
	ctx := context.WithValue(context.Background(), "testKey", "testValue")
	clonedReq := originalReq.Clone(ctx)

	// 验证克隆结果
	r.NotNil(clonedReq, "克隆的请求不应为nil")
	r.Equal("POST", clonedReq.Method, "请求方法应为POST")
	r.Equal("example.com", clonedReq.Host, "主机名应为example.com")
	r.Equal("testValue", clonedReq.Context().Value("testKey"), "上下文值应正确传递")

	// 验证认证信息已正确克隆
	r.NotNil(clonedReq.Auth, "认证信息不应为nil")
	r.Equal(DigestAuth, clonedReq.Auth.Type, "认证类型应正确克隆")
	r.Equal("testuser", clonedReq.Auth.Username, "用户名应正确克隆")
	r.Equal("testpass", clonedReq.Auth.Password, "密码应正确克隆")

	// 验证指标被重置
	r.Equal(0, clonedReq.Metrics.Failures, "失败计数器应被重置")
	r.Equal(0, clonedReq.Metrics.Retries, "重试计数器应被重置")
	r.Equal(0, clonedReq.Metrics.DrainErrors, "排空错误计数器应被重置")
}

// TestRequestWithContext 测试更新请求上下文的功能
func TestRequestWithContext(t *testing.T) {
	r := require.New(t)

	// 创建一个原始请求
	req, err := NewRequest("GET", "https://example.com/api", nil)
	r.NoError(err, "创建请求不应有错误")

	// 创建新上下文
	ctx := context.WithValue(context.Background(), "testKey", "testValue")

	// 更新请求上下文
	newReq := req.WithContext(ctx)

	// 验证上下文已更新
	r.Equal(req, newReq, "WithContext应返回相同的请求实例")
	r.Equal("testValue", newReq.Context().Value("testKey"), "上下文值应正确传递")
}

// TestRequestBodyBytes 测试访问请求体的功能
func TestRequestBodyBytes(t *testing.T) {

	// 测试用例1: 有请求体的情况
	t.Run("有请求体", func(t *testing.T) {
		r := require.New(t)
		req, err := NewRequest("POST", "https://example.com/api", strings.NewReader("test=data"))
		r.NoError(err, "创建请求不应有错误")

		// 读取请求体
		bodyBytes, err := req.BodyBytes()
		r.NoError(err, "读取请求体不应有错误")
		r.Equal([]byte("test=data"), bodyBytes, "请求体应正确")

		// 再次读取，确保请求体可重用
		bodyBytes2, err := req.BodyBytes()
		r.NoError(err, "第二次读取请求体不应有错误")
		r.Equal([]byte("test=data"), bodyBytes2, "第二次读取的请求体应正确")
	})

	// 测试用例2: 无请求体的情况
	t.Run("无请求体", func(t *testing.T) {
		r := require.New(t)
		req, err := NewRequest("GET", "https://example.com/api", nil)
		r.NoError(err, "创建请求不应有错误")

		// 读取请求体
		bodyBytes, err := req.BodyBytes()
		r.NoError(err, "读取请求体不应有错误")
		r.Nil(bodyBytes, "无请求体时应返回nil")
	})
}

// TestRequestDump 测试请求转储功能
func TestRequestDump(t *testing.T) {
	// 测试用例1: 转储GET请求
	t.Run("转储GET请求", func(t *testing.T) {
		r := require.New(t)
		req, err := NewRequest("GET", "https://example.com/api?param=value", nil)
		r.NoError(err, "创建请求不应有错误")

		// 添加请求头
		req.Header.Set("User-Agent", "test-agent")

		// 转储请求
		dump, err := req.Dump()
		r.NoError(err, "转储请求不应有错误")
		r.NotNil(dump, "转储不应为nil")

		// 验证转储内容
		dumpStr := string(dump)
		r.Contains(dumpStr, "GET /api?param=value HTTP/1.1", "转储应包含请求行")
		r.Contains(dumpStr, "Host: example.com", "转储应包含主机头")
		r.Contains(dumpStr, "User-Agent: test-agent", "转储应包含自定义请求头")
	})

	// 测试用例2: 转储带请求体的POST请求
	t.Run("转储带请求体的POST请求", func(t *testing.T) {
		r := require.New(t)
		req, err := NewRequest("POST", "https://example.com/api", strings.NewReader("test=data"))
		r.NoError(err, "创建请求不应有错误")

		// 添加请求头
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

		// 转储请求
		dump, err := req.Dump()
		r.NoError(err, "转储请求不应有错误")

		// 验证转储内容
		dumpStr := string(dump)
		r.Contains(dumpStr, "POST /api HTTP/1.1", "转储应包含请求行")
		r.Contains(dumpStr, "Content-Type: application/x-www-form-urlencoded", "转储应包含内容类型头")
		r.Contains(dumpStr, "test=data", "转储应包含请求体")
	})
}

// TestFromRequest 测试从标准http.Request创建retryablehttp.Request的功能
func TestFromRequest(t *testing.T) {
	r := require.New(t)

	// 创建一个标准http.Request
	stdReq, err := http.NewRequest("GET", "https://example.com/api", nil)
	r.NoError(err, "创建标准请求不应有错误")
	stdReq.Header.Set("User-Agent", "test-agent")

	// 从标准请求创建retryablehttp.Request
	req, err := FromRequest(stdReq)
	r.NoError(err, "从标准请求创建不应有错误")
	r.NotNil(req, "创建的请求不应为nil")

	// 验证请求属性
	r.Equal("GET", req.Method, "请求方法应为GET")
	r.Equal("example.com", req.Host, "主机名应为example.com")
	r.Equal("/api", req.URL.Path, "路径应为/api")
	r.Equal("test-agent", req.Header.Get("User-Agent"), "请求头应正确传递")
}

// TestSetURL 测试设置URL功能
func TestSetURL(t *testing.T) {
	r := require.New(t)

	// 创建一个请求
	req, err := NewRequest("GET", "https://example.com/api", nil)
	r.NoError(err, "创建请求不应有错误")

	// 创建新URL
	newURL, err := urlutil.Parse("https://newexample.com/newpath?query=newvalue")
	r.NoError(err, "解析URL不应有错误")

	// 设置新URL
	req.SetURL(newURL)

	// 验证URL已更新
	r.Equal("/newpath", req.URL.Path, "路径应已更新")
	r.Equal("query=newvalue", req.URL.RawQuery, "查询字符串应已更新")
}

// TestUpdateScheme 测试更新协议方案的功能
func TestUpdateScheme(t *testing.T) {

	// 保存原始PreferHTTP值
	originalPreferHTTP := PreferHTTP
	defer func() { PreferHTTP = originalPreferHTTP }() // 测试结束后恢复

	// 测试用例1: PreferHTTP=true
	t.Run("PreferHTTP=true", func(t *testing.T) {
		r := require.New(t)
		PreferHTTP = true

		req, err := NewRequest("GET", "example.com/api", nil)
		r.NoError(err, "创建请求不应有错误")

		// 验证协议方案
		r.Equal("http", req.URL.Scheme, "协议方案应为http")
	})

	// 测试用例2: PreferHTTP=false
	t.Run("PreferHTTP=false", func(t *testing.T) {
		r := require.New(t)
		PreferHTTP = false

		req, err := NewRequest("GET", "https://example.com/api", nil)
		r.NoError(err, "创建请求不应有错误")

		// 验证协议方案
		r.Equal("https", req.URL.Scheme, "协议方案应为https")
	})
}

// TestFromRequestWithTrace 测试创建带跟踪的请求
func TestFromRequestWithTrace(t *testing.T) {
	r := require.New(t)

	// 创建一个标准http.Request
	stdReq, err := http.NewRequest("GET", "https://example.com/api", nil)
	r.NoError(err, "创建标准请求不应有错误")

	// 创建带跟踪的请求
	req, err := FromRequestWithTrace(stdReq)
	r.NoError(err, "创建带跟踪的请求不应有错误")
	r.NotNil(req, "创建的请求不应为nil")

	// 验证请求属性
	r.Equal("GET", req.Method, "请求方法应为GET")
	r.Equal("example.com", req.Host, "主机名应为example.com")
	r.Equal("/api", req.URL.Path, "路径应为/api")
}