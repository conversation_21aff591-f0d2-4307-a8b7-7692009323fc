//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-07 11:02:43
//FilePath: /yaml_scan/pkg/networkpolicy/networkpolicy.go
//Description:

package networkpolicy

import (
	"net"
	"net/netip"
	"regexp"
	"strconv"

	iputil "yaml_scan/utils/ip"
	urlutil "yaml_scan/utils/url"

	"github.com/gaissmai/bart"
)

// DefaultOptions 默认配置
var DefaultOptions Options

// Options 策略拦截器
type Options struct {
	DenyList        []string // 被拒绝的列表
	AllowList       []string // 被允许的列表
	AllowSchemeList []string // 被允许的协议（如 http, https）
	DenySchemeList  []string // 拒绝的协议列表，例如 "ftp", "file"
	AllowPortList   []int    // 允许的端口列表，例如 80, 443
	DenyPortList    []int    // 拒绝的端口列表，例如 21, 25
}

// NetworkPolicy 结构体用于定义网络策略，包含各种选项和规则。
// 该结构体用于控制网络流量的允许和拒绝规则。
type NetworkPolicy struct {
	Options    *Options // 网络策略的配置选项
	hasFilters bool     // 是否存在过滤器

	DenyRanger  *bart.Table[net.IP] // 拒绝的 IP 范围
	AllowRanger *bart.Table[net.IP] // 允许的 IP 范围

	AllowRules      map[string]*regexp.Regexp // 允许的规则，使用正则表达式
	DenyRules       map[string]*regexp.Regexp // 拒绝的规则，使用正则表达式
	AllowSchemeList map[string]struct{}       // 允许的协议列表
	DenySchemeList  map[string]struct{}       // 拒绝的协议列表
	AllowPortList   map[int]struct{}          // 允许的端口列表
	DenyPortList    map[int]struct{}          // 拒绝的端口列表
}

func init() {
	DefaultOptions.DenyList = append(DefaultOptions.DenyList, DefaultIPv4DenylistRanges...)
	DefaultOptions.DenyList = append(DefaultOptions.DenyList, DefaultIPv6Denylist...)
	DefaultOptions.DenyList = append(DefaultOptions.DenyList, DefaultIPv4DenylistRanges...)
	DefaultOptions.DenyList = append(DefaultOptions.DenyList, DefaultIPv6Denylist...)
	DefaultOptions.AllowSchemeList = append(DefaultOptions.DenyList, DefaultSchemeAllowList...)
}

// asCidr: 将给定的字符串转换为 CIDR 表示法的 IP 前缀。
// 如果输入是有效的 IP 地址，则默认使用 /32（IPv4）或 /128（IPv6）后缀。
//
//	@param s string: 要转换的字符串，可以是 IP 地址或 CIDR 表示法
//	@return netip.Prefix netip.Prefix:
//	@return error error:
func asCidr(s string) (netip.Prefix, error) {
	if iputil.IsIP(s) {
		// 检查 IP 地址的类型
		if net.ParseIP(s).To4() != nil {
			// IPv4 地址，添加 /32 后缀
			s += "/32"
		} else {
			// IPv6 地址，添加 /128 后缀
			s += "/128"
		}
	}
	// 尝试解析字符串为 CIDR 前缀
	cidr, err := netip.ParsePrefix(s)
	if err != nil {
		return cidr, err
	}
	return cidr, nil
}

// New:创建一个新的网络策略实例，使用提供的选项进行初始化。
//
//	@param options Options: 网络策略的配置选项
//	@return *NetworkPolicy *NetworkPolicy:返回指向 NetworkPolicy 的指针
//	@return error error:可能的错误。
func New(options Options) (*NetworkPolicy, error) {
	// 初始化允许的协议列表
	allowSchemeList := make(map[string]struct{})
	for _, scheme := range options.AllowSchemeList {
		allowSchemeList[scheme] = struct{}{}
	}

	// 初始化拒绝的协议列表
	denySchemeList := make(map[string]struct{})
	for _, scheme := range options.DenySchemeList {
		denySchemeList[scheme] = struct{}{}
	}

	// 初始化允许和拒绝的规则
	allowRules := make(map[string]*regexp.Regexp)
	denyRules := make(map[string]*regexp.Regexp)

	// 初始化允许的 IP 范围
	var allowRanger *bart.Table[net.IP]

	if len(options.AllowList) > 0 {
		allowRanger = new(bart.Table[net.IP])

		for _, r := range options.AllowList {
			// 处理 IP 或 CIDR
			cidr, err := asCidr(r)
			if err == nil {
				allowRanger.Insert(cidr, nil)
				continue
			}

			// 处理正则表达式
			rgx, err := regexp.Compile(r)
			if err != nil {
				return nil, err
			}
			allowRules[r] = rgx
		}
	}

	// 初始化拒绝的 IP 范围
	var denyRanger *bart.Table[net.IP]
	if len(options.DenyList) > 0 {
		denyRanger = new(bart.Table[net.IP])

		for _, r := range options.DenyList {
			// 处理 IP 或 CIDR
			cidr, err := asCidr(r)
			if err == nil {
				denyRanger.Insert(cidr, nil)
				continue
			}

			// 处理正则表达式
			rgx, err := regexp.Compile(r)
			if err != nil {
				return nil, err
			}
			denyRules[r] = rgx
		}
	}
	// 初始化允许和拒绝的端口列表
	allowPortList := make(map[int]struct{})
	for _, p := range options.AllowPortList {
		allowPortList[p] = struct{}{}
	}

	denyPortList := make(map[int]struct{})
	for _, p := range options.DenyPortList {
		denyPortList[p] = struct{}{}
	}

	// 检查是否存在过滤器
	hasFilters := len(options.DenyList)+len(options.AllowList)+len(options.AllowSchemeList)+len(options.DenySchemeList)+len(options.AllowPortList)+len(options.DenyPortList) > 0

	// 返回新的 NetworkPolicy 实例
	return &NetworkPolicy{Options: &options, DenyRanger: denyRanger, AllowRanger: allowRanger, AllowSchemeList: allowSchemeList,
		DenySchemeList: denySchemeList, AllowRules: allowRules, DenyRules: denyRules, AllowPortList: allowPortList, DenyPortList: denyPortList, hasFilters: hasFilters}, nil
}

// rangerContains:  检查给定的 IP 地址是否存在于 ranger 表中。
//
//	@param ranger *bart.Table[net.IP]: 指向 bart.Table[net.IP] 的指针，不能为空。
//	@param IP netip.Addr: 要检查的 IP 地址
//	@return bool bool: 如果 IP 存在于表中，返回 true；否则返回 false。
func rangerContains(ranger *bart.Table[net.IP], IP netip.Addr) bool {
	_, ok := ranger.Lookup(IP)
	return ok
}

// Validate:  验证给定的 host 是否符合网络策略规则。
//
//	@receiver r NetworkPolicy: 网络策略结构体。
//	@param host string: 要验证的主机名或 IP 地址。
//	@return bool bool: 果 host 符合允许的条件，返回 true；否则返回 false。
func (r NetworkPolicy) Validate(host string) bool {
	//  如果没有过滤器 直接返回 true。
	if !r.hasFilters {
		return true
	}

	// 检查是否是 IP 地址
	if iputil.IsIP(host) {
		parsed, err := netip.ParseAddr(host)
		if err != nil {
			return false
		}
		// 检查 DenyRanger
		if r.DenyRanger != nil && r.DenyRanger.Size() > 0 && rangerContains(r.DenyRanger, parsed) {
			return false
		}

		// 检查 AllowRanger
		if r.AllowRanger != nil && r.AllowRanger.Size() > 0 {
			return rangerContains(r.AllowRanger, parsed)
		}

		return true
	}

	// 检测url
	var hasPort bool
	var port int
	var hasScheme bool
	var scheme string

	// 尝试解析为 URL
	if URL, err := urlutil.Parse(host); err == nil {
		// 提取 scheme
		scheme := URL.Scheme
		hasScheme = scheme != ""
		// 提取 port
		port, err = strconv.Atoi(URL.Port())
		if err == nil {
			hasPort = true
		}
	}

	// 检查端口
	var isPortInDenyList, isPortInAllowedList bool

	if len(r.DenyPortList) > 0 && hasPort {
		_, isPortInDenyList = r.DenyPortList[port]
	}

	if len(r.AllowPortList) > 0 && hasPort {
		_, isPortInAllowedList = r.AllowPortList[port]
	} else {
		isPortInAllowedList = true
	}

	// 检查协议
	var isSchemeInDenyList, isSchemeInAllowedList bool
	if len(r.DenySchemeList) > 0 && hasScheme {
		_, isSchemeInDenyList = r.DenySchemeList[scheme]
	}

	if len(r.AllowSchemeList) > 0 && hasScheme {
		_, isSchemeInAllowedList = r.AllowSchemeList[scheme]
	} else {
		isSchemeInAllowedList = true
	}

	// 正则表达式匹配
	var isInDenyList, isInAllowedList bool
	for _, reg := range r.DenyRules {
		if reg.MatchString(host) {
			isInDenyList = true
			break
		}
	}
	if len(r.AllowRules) > 0 {
		for _, reg := range r.AllowRules {
			if reg.MatchString(host) {
				isInAllowedList = true
				break
			}
		}
	} else {
		isInAllowedList = true
	}

	// 综合判断
	return !isSchemeInDenyList && !isInDenyList && isInAllowedList && isSchemeInAllowedList && !isPortInDenyList && isPortInAllowedList
}

// ValidateAddress: 验证给定的 IP 地址是否符合网络策略中的允许或拒绝规则。
//
//	@receiver r NetworkPolicy: 网络策略结构体。
//	@param IP string: 要验证的 IP 地址字符串。
//	@return bool bool: 如果 IP 地址符合允许的条件，返回 true；否则返回 false。
func (r NetworkPolicy) ValidateAddress(IP string) bool {
	IPDest, err := netip.ParseAddr(IP)
	if err != nil {
		return false
	}

	// 检查 DenyRanger 是否存在且不为空，并且 IP 地址在 DenyRanger 中
	if r.DenyRanger != nil && r.DenyRanger.Size() > 0 && rangerContains(r.DenyRanger, IPDest) {
		return false
	}

	// 检查 AllowRanger 是否存在且不为空
	if r.AllowRanger != nil && r.AllowRanger.Size() > 0 {
		return rangerContains(r.AllowRanger, IPDest)
	}

	return true
}

// ValidateURLWithIP: 验证给定的 URL 和对应的 IP 地址是否符合网络策略规则。
//
//	@receiver r NetworkPolicy: 网络策略结构体。
//	@param host string: 要验证的 URL 字符串。
//	@param ip string: 要验证的 IP 地址字符串。
//	@return bool bool: 如果 URL 和 IP 地址都符合允许的条件，返回 true；否则返回 false。
func (r NetworkPolicy) ValidateURLWithIP(host string, ip string) bool {
	if !r.hasFilters {
		return true
	}
	return r.Validate(host) && r.ValidateAddress(ip)
}

// portIsListed: 检查指定的端口是否存在于给定的端口集合中。
//
//	@param list map[int]struct{}: 表示允许或拒绝的端口集合。
//	@param port int: 要检查的端口。
//	@return bool bool:  如果端口存在于映射中，返回 true；否则返回 false。
func portIsListed(list map[int]struct{}, port int) bool {
	_, ok := list[port]
	return ok
}

// ValidatePort: 验证给定的端口是否符合网络策略中的允许或拒绝规则。
//
//	@receiver r NetworkPolicy: 网络策略结构体。
//	@param port int:要验证的端口号。
//	@return bool bool: 如果端口符合允许的条件，返回 true；否则返回 false。
func (r NetworkPolicy) ValidatePort(port int) bool {
	// 检查 DenyPortList 是否存在且不为空，并且端口在 DenyPortList 中
	if len(r.DenyPortList) > 0 && portIsListed(r.DenyPortList, port) {
		return false
	}

	// 检查 AllowPortList 是否存在且不为空
	if len(r.AllowPortList) > 0 {
		return portIsListed(r.AllowPortList, port)
	}

	return true
}

// ValidateAddressWithPort:验证给定的 IP 地址和对应的端口是否符合网络策略中的允许或拒绝规则
//
//	@receiver r NetworkPolicy: 网络策略结构体。
//	@param IP string: 要验证的 IP 地址字符串。
//	@param port int: 要验证的端口号，整数类型。
//	@return bool bool: 如果 IP 地址和端口都符合允许的条件，返回 true；否则返回 false。
func (r NetworkPolicy) ValidateAddressWithPort(IP string, port int) bool {
	return r.ValidateAddress(IP) && r.ValidatePort(port)
}

// ValidateHost: 检查与主机名关联的所有 IP 地址，并返回第一个符合网络策略的 IP 地址及其验证结果。
//
//	@receiver r NetworkPolicy: 网络策略结构体。
//	@param host string: 要验证的主机名字符串。
//	@return string string: 符合网络策略的 IP 地址字符串。
//	@return bool bool:是否找到符合策略的 IP 地址。
func (r NetworkPolicy) ValidateHost(host string) (string, bool) {
	// 检查输入是否为有效的 IP 地址
	if iputil.IsIP(host) {
		return host, r.Validate(host)
	}
	// 解析主机名，获取所有对应的 IP 地址
	addrs, err := net.LookupHost(host)
	if err != nil {
		// no addresses
		return "", false
	}

	// 遍历所有解析得到的 IP 地址
	for _, addr := range addrs {
		// 验证当前 IP 地址是否符合网络策略
		if r.Validate(addr) {
			return addr, true
		}
	}

	return "", false
}
