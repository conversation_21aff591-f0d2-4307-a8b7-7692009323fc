package writer


import (
	"os"
	"sync"

	"yaml_scan/pkg/gologger/levels"
)

// CLI  是一个并发输出写入器，用于将日志信息输出到终端.
type CLI struct {
	mutex *sync.Mutex  // 互斥锁，用于控制并发访问
}

// 确保 CLI 实现了 Writer 接口
var _ Writer = &CLI{}

// NewCLI  返回一个新的 CLI 并发日志写入器。
func NewCLI() *CLI {
	return &CLI{mutex: &sync.Mutex{}}
}

// Write方法将输出写入底层文件
func (w *CLI) Write(data []byte, level levels.Level) {
	  // 获取互斥锁以确保线程安全
	w.mutex.Lock()
	// 在函数结束时释放互斥锁
	defer w.mutex.Unlock()

	switch level {
	case levels.LevelSilent:
		os.Stdout.Write(data)
		os.Stdout.WriteString(NewLine)
	default:
		os.Stderr.Write(data)
		os.Stderr.WriteString(NewLine)
	}
}