//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-11 19:12:16
//FilePath: /yaml_scan/utils/url/rawparam.go
//Description: 提供了 URL 编码和解码的功能，支持处理特殊字符和非 ASCII 字符

package urlutil

import (
	"bytes"
	"encoding/hex"
	"sort"
	"strconv"
	"strings"
)

// RFCEscapeCharSet 中包含的字符是根据 RFC 标准定义的保留字符 需要对其进行编码
var RFCEscapeCharSet []rune = []rune{'!', '*', '\'', '(', ')', ';', ':', '@', '&', '=', '+', '$', ',', '/', '?', '%', '#', '[', ']'}

type Params map[string][]string

// getrunemap: 将给定的 rune 切片转换为一个映射，方便后续查找
//
//	@param runes []rune: 需要转换的rune切片
//	@return map map: 返回一个映射，键为 rune，值为空结构体（用于占位）。
func getrunemap(runes []rune) map[rune]struct{} {
	x := map[rune]struct{}{}
	for _, v := range runes {
		x[v] = struct{}{}
	}
	return x
}

// getasciihex: : 将 ASCII 字符转换为其十六进制表示
//
//	@param r rune: 需要处理的rune字符
//	@return string string: 返回字符的大写十六进制字符串。
func getasciihex(r rune) string {
	val := strconv.FormatInt(int64(r), 16)
	if len(val) == 1 {
		// 如果长度为 1，则在前面补 0，以确保是两位十六进制表示
		val = "0" + val
	}
	return strings.ToUpper(val) // 返回大写的十六进制字符串
}

// getutf8hex: 将 UTF-8 非 ASCII 字符转换为其十六进制表示，使用百分号编码。
//
//	@param r rune:需要处理的rune字符
//	@return string string: 返回字符的十六进制字符串，格式为 %XX。
func getutf8hex(r rune) string {
	// 百分号编码仅在十六进制值和 ASCII 范围内进行
	// 其他 UTF-8 字符（如中文等）可以通过 UTF-8 编码和字节转换来使用
	// 让 Go 处理 rune 的 UTF-8 编码
	var buff bytes.Buffer
	// 将 rune 转换为字符串
	utfchar := string(r)
	// 将字符串转换为字节并编码为十六进制
	hexencstr := hex.EncodeToString([]byte(utfchar))
	for k, v := range hexencstr {
		if k != 0 && k%2 == 0 {
			// 在每个第二个十六进制数字前添加 '%'
			buff.WriteRune('%')
		}
		buff.WriteRune(v)
	}
	return buff.String()
}

// URLEncodeWithEscapes: 对给定的数据进行 URL 编码，并对指定的特殊字符进行转义
//
//	@param data string: 要编码的字符串
//	@param charset ...rune: 需要转义的字符集（可变参数)
//	@return string string: 返回编码后的字符串
func URLEncodeWithEscapes(data string, charset ...rune) string {
	// 创建一个必须转义的字符映射
	mustescape := getrunemap(charset)
	var buff bytes.Buffer
	// 预分配缓冲区大小
	buff.Grow(len(data))

	for _, r := range data {
		switch {
		case r < rune(20):
			// 控制字符
			buff.WriteRune('%')
			buff.WriteString(getasciihex(r)) // 写入 2 位十六进制
		case r == ' ':
			// 空格使用 '+' 表示
			buff.WriteRune('+')
		case r < rune(127):
			if _, ok := mustescape[r]; ok {
				// 保留字符必须转义
				buff.WriteRune('%')
				buff.WriteString(getasciihex(r))
			} else {
				// 不进行百分号编码
				buff.WriteRune(r)
			}
		case r == rune(127):
			// [DEL] 字符应被编码
			buff.WriteRune('%')
			buff.WriteString(getasciihex(r))
		case r > rune(128):
			// 非 ASCII 字符（例如中文字符或其他 UTF-8 字符）
			buff.WriteRune('%')
			buff.WriteString(getutf8hex(r))
		}
	}
	return buff.String()
}

// ParamEncode: 对参数的键进行编码，处理空格、不可打印字符和非 ASCII 字符
//
//	@param data string: 要处理的字符
//	@return string string: 返回编码后的字符串
func ParamEncode(data string) string {
	return URLEncodeWithEscapes(data)
}

// Encode: 对 Params 中的所有键值对进行 URL 编码，并按键排序。
//
//	@receiver p Params:
//	@return string string: 返回编码后的字符串，格式为 key=value 的形式
func (p Params) Encode() string {
	if p == nil {
		return ""
	}
	var buf strings.Builder
	keys := make([]string, 0, len(p))

	// 收集所有键
	for k := range p {
		keys = append(keys, k)
	}
	// 对键进行排序
	sort.Strings(keys)
	for _, k := range keys {
		vs := p[k]
		// 编码键
		keyEscaped := ParamEncode(k)
		for _, v := range vs {
			if buf.Len() > 0 {
				buf.WriteByte('&') // 在参数之间添加 '&'
			}
			buf.WriteString(keyEscaped)
			// 编码值
			value := ParamEncode(v)
			// 如果参数没有值，则不指定 '='
			if value != "" {
				buf.WriteRune('=')
				buf.WriteString(value)
			}
		}
	}
	return buf.String()
}

// Has: 检查给定的键是否存在于 Params 中
//
//	@receiver p Params:
//	@param key string: 需要检查的key
//	@return bool bool: 如果键存在，返回 true；否则返回 false
func (p Params) Has(key string) bool {
	if p == nil {
		p = make(Params)
	}
	_, ok := p[key]
	return ok
}

// Add: 向 Params 中添加键值对。如果键已存在，则将新值追加到现有值的切片中。
//
//	@receiver p Params:
//	@param key string: 要添加的键
//	@param value ...string: 要添加的值（可变参数)
func (p Params) Add(key string, value ...string) {
	if p.Has(key) {
		p[key] = append(p[key], value...)
	} else {
		p[key] = value
	}
}

// Decode: 解析给定的 URL 编码字符串，并将其转换为 Params 结构。
//
//	@receiver p Params:
//	@param raw string: 要解析的原始字符串
func (p Params) Decode(raw string) {
	if raw == "" {
		return
	}

	// 如果为 nil，则初始化
	if p == nil {
		p = make(Params)
	}

	arr := []string{}
	var tbuff bytes.Buffer
	for _, v := range raw {
		switch v {
		case '&':
			arr = append(arr, tbuff.String()) // 在 '&' 处分割
			tbuff.Reset()                     // 重置缓冲区
		case ';':
			tbuff.WriteRune(v) // 将 ';' 写入缓冲区
		default:
			tbuff.WriteRune(v) // 将其他字符写入缓冲区
		}
	}

	// 添加剩余的缓冲区内容
	if tbuff.Len() > 0 {
		arr = append(arr, tbuff.String())
	}

	for _, pair := range arr {
		// 在第一个 '=' 处分割
		d := strings.SplitN(pair, "=", 2)
		if len(d) == 2 {
			p.Add(d[0], d[1]) // 添加键值对
		} else if len(d) == 1 {
			p.Add(d[0], "") // 添加只有键的条目，值为空
		}
	}
}
