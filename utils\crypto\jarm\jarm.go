//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-04 16:42:02
//FilePath: /yaml_scan/utils/crypto/jarm/jarm.go
//Description:

package jarm

import (
	"context"
	"fmt"
	"net"
	"strings"
	"time"

	connpool "yaml_scan/utils/conn"

	gojarm "github.com/hdm/jarm-go"
)

// PoolCount 定义连接池中保持的连接数量
var PoolCount = 3

// HashWithDialer: 使用指定的拨号器对单个主机和端口进行探测，并返回结果的哈希值。
//
//	@param dialer connpool.Dialer: 自定义拨号器，用于创建连接。
//	@param host string:要探测的主机名。
//	@param port int:要探测的端口号。
//	@param duration int:探测的持续时间（秒）。
//	@return string string:返回探测结果的哈希值。
//	@return error error:返回可能发生的错误。
func HashWithDialer(dialer connpool.Dialer, host string, port int, duration int) (string, error) {
	var results []string
	// 组合主机和端口为地址
	addr := net.JoinHostPort(host, fmt.Sprintf("%d", port))
	// 设置超时时间
	timeout := time.Duration(duration) * time.Second
	// 创建带有超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), (time.Duration(duration*PoolCount) * time.Second))
	defer cancel()

	// 使用连接池，因为我们需要多个探测
	pool, err := connpool.NewOneTimePool(ctx, addr, PoolCount)
	if err != nil {
		return "", err
	}
	// 设置自定义拨号器
	pool.Dialer = dialer

	// 确保在函数结束时关闭连接池
	defer func() { _ = pool.Close() }()
	// 启动连接池的运行
	go func() { _ = pool.Run() }()

	for _, probe := range gojarm.GetProbes(host, port) {
		// 从连接池获取连接
		conn, err := pool.Acquire(ctx)
		if err != nil {
			continue
		}
		if conn == nil {
			continue
		}
		// 设置写入截止时间
		_ = conn.SetWriteDeadline(time.Now().Add(timeout))
		// 发送探测数据
		_, err = conn.Write(gojarm.BuildProbe(probe))
		if err != nil {
			results = append(results, "")
			_ = conn.Close()
			continue
		}
		// 设置读取截止时间
		_ = conn.SetReadDeadline(time.Now().Add(timeout))
		buff := make([]byte, 1484)
		_, _ = conn.Read(buff)
		_ = conn.Close()
		// 解析响应
		ans, err := gojarm.ParseServerHello(buff, probe)
		if err != nil {
			results = append(results, "")
			continue
		}
		results = append(results, ans)
	}
	// 将结果转换为哈希
	hash := gojarm.RawHashToFuzzyHash(strings.Join(results, ","))
	return hash, nil
}
