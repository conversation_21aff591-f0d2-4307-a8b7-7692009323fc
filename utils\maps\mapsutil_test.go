//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-15 17:34:06
//FilePath: /yaml_scan/utils/maps/mapsutil_test.go
//Description:

package mapsutil

import (
	"reflect"
	"sort"
	"testing"
)

// TestGetKeys 测试 GetKeys 函数
func TestGetMapKeys(t *testing.T) {
	// 定义测试用例
	tests := []struct {
		name   string           // 测试用例名称
		input  []map[int]string // 输入的映射切片
		output []int            // 期望的输出键切片
	}{
		{
			name: "Single map", // 单个映射的测试
			input: []map[int]string{
				{1: "one", 2: "two"},
			},
			output: []int{1, 2}, // 期望输出
		},
		{
			name: "Multiple maps", // 多个映射的测试
			input: []map[int]string{
				{1: "one", 2: "two"},
				{3: "three", 4: "four"},
			},
			output: []int{1, 2, 3, 4}, // 期望输出
		},
		{
			name: "Duplicate keys", // 包含重复键的测试
			input: []map[int]string{
				{1: "one", 2: "two"},
				{2: "two", 3: "three"},
			},
			output: []int{1, 2, 2, 3}, // 期望输出包含重复的键
		},
	}

	// 遍历测试用例
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetKeys(tt.input...) // 调用 GetKeys 函数

			if !slicesEqualUnordered(got, tt.output) { // 使用 reflect.DeepEqual 比较切片
				t.Errorf("GetKeys() = %v, want %v", got, tt.output) // 输出错误信息
			}
		})
	}
}

func slicesEqualUnordered(s1, s2 []int) bool {
	if len(s1) != len(s2) {
		return false
	}

	// 对切片进行排序
	sort.Ints(s1)
	sort.Ints(s2)
	// 使用 reflect.DeepEqual 比较
	return reflect.DeepEqual(s1, s2)
}

// TestGetSortedKeys 测试 GetSortedKeys 函数
func TestGetSortedKeys(t *testing.T) {
	// 定义测试用例
	tests := []struct {
		name   string           // 测试用例名称
		input  []map[int]string // 输入的映射切片
		output []int            // 期望的输出键切片
	}{
		{
			name: "Single map", // 单个映射的测试
			input: []map[int]string{
				{3: "three", 1: "one", 2: "two"},
			},
			output: []int{1, 2, 3}, // 排序后的期望输出
		},
		{
			name: "Multiple maps", // 多个映射的测试
			input: []map[int]string{
				{3: "three", 1: "one"},
				{4: "four", 2: "two"},
			},
			output: []int{1, 2, 3, 4}, // 排序后的期望输出
		},
		{
			name: "Duplicate keys", // 包含重复键的测试
			input: []map[int]string{
				{1: "one", 2: "two"},
				{2: "two", 3: "three"},
			},
			output: []int{1, 2, 2, 3}, // 期望输出包含重复的键
		},
	}

	// 遍历测试用例
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetSortedKeys(tt.input...) // 调用 GetSortedKeys 函数
			// 打印调试信息
			t.Logf("Input maps: %v, Got sorted keys: %v", tt.input, got)
			if !slicesEqualUnordered(got, tt.output) { 
				t.Errorf("GetSortedKeys() = %v, want %v", got, tt.output) // 输出错误信息
			}
		})
	}
}
