package config

import (
	"testing"
)


func TestIsTemplate(t *testing.T) {
	tests := []struct {
		filename string
		expected bool
	}{
		{"cves.json", false},                  // 已知配置文件，不是模板
		{"contributors.json", false},           // 已知配置文件，不是模板
		{"TEMPLATES-STATS.json", false},       // 已知配置文件，不是模板
		{"template.yaml", true},                // 支持的模板文件
		{"template.json", true},                // 支持的模板文件
		{"template.txt", false},                // 不支持的模板文件
		{"unknown_file.yaml", true},            // 支持的模板文件
		{"unknown_file.json", true},            // 支持的模板文件
		{"another_file.txt", false},            // 不支持的模板文件
	}

	for _, test := range tests {
		result := IsTemplate(test.filename)
		if result != test.expected {
			t.Errorf("IsTemplate(%q) = %v; want %v", test.filename, result, test.expected)
		}
	}
}