//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-11 17:45:19
//FilePath: /yaml_scan/pkg/input/types/http.go
//Description: http请求响应相关定义

package types

import (
	"bufio"
	"bytes"
	"fmt"
	"io"
	"net/textproto"
	"strings"
	"sync"

	"yaml_scan/pkg/retryablehttp"
	"yaml_scan/utils/conversion"
	mapsutil "yaml_scan/utils/maps"
	urlutil "yaml_scan/utils/url"
)

// HttpRequest 是一个包含 HTTP 请求的结构体
type HttpRequest struct {
	Method  string                              `json:"method"`  // method 是请求的方法，例如 GET、POST、PUT 等
	Headers mapsutil.OrderedMap[string, string] `json:"headers"` // headers 是请求的头部，使用 OrderedMap 保持顺序
	Body    string                              `json:"body"`    // body 是请求的主体
	Raw     string                              `json:"raw"`     // raw 是原始请求字符串，包括方法、头部、主体等
}

// HttpResponse 是一个包含 HTTP 响应的结构体
type HttpResponse struct {
	StatusCode int                                 `json:"status_code"` // StatusCode 是响应的状态码，例如 200、404、500 等
	Headers    mapsutil.OrderedMap[string, string] `json:"headers"`     // Headers 是响应的头部，使用 OrderedMap 保持顺序
	Body       string                              `json:"body"`        // Body 是响应的主体，通常包含返回的数据
	Raw        string                              `json:"raw"`         // Raw 是原始响应字符串，包括状态码、头部、主体等
}

// RequestResponse 是一个包含请求和响应的结构体
// 从输入格式中获取请求和响应。
// 这个结构体可以被视为请求和响应的标准格式
type RequestResponse struct {
	URL urlutil.URL `json:"url"` // URL 是请求的 URL

	Request *HttpRequest `json:"request"` // Request 是请求的详细信息

	Response *HttpResponse `json:"response"` // Response 是响应的详细信息

	req *retryablehttp.Request `json:"-"` // req 用于发送请求。这个字段是未导出的，表示它是内部使用的

	reqErr error `json:"-"` // 存储在构建请求时可能发生的错误。

	once sync.Once `json:"-"` // 用于确保请求的构建只执行一次，避免重复构建。
}

// ParseRawRequest:  解析原始请求字符串并返回请求和响应对象。
// 只解析请求部分，响应部分需要手动添加
//
//	@param raw string: 原始请求字符串
//	@return rr *RequestResponse: 解析后的 RequestResponse 对象。
//	@return err error: 可能的错误。
func ParseRawRequest(raw string) (rr *RequestResponse, err error) {
	// 创建一个文本协议读取器，用于逐行读取原始请求字符串
	protoReader := textproto.NewReader(bufio.NewReader(strings.NewReader(raw)))
	// 读取请求方法行
	methodLine, err := protoReader.ReadLine()
	if err != nil {
		return nil, fmt.Errorf("failed to read method line: %s", err)
	}

	rr = &RequestResponse{
		Request: &HttpRequest{},
	}

	// 必须至少包含 3 个部分（方法、URL、HTTP 版本）
	parts := strings.Split(methodLine, " ")
	if len(parts) < 3 {
		return nil, fmt.Errorf("invalid method line: %s", methodLine)
	}
	// 获取请求方法并存储
	method := parts[0]
	rr.Request.Method = method

	// 解析相对 URL
	urlx, err := urlutil.ParseRawRelativePath(parts[1], true)
	if err != nil {
		return nil, fmt.Errorf("failed to parse url: %s", err)
	}
	rr.URL = *urlx // 将解析后的 URL 存储到 RequestResponse 中

	// 解析主机行
	hostLine, err := protoReader.ReadLine()
	if err != nil {
		return nil, fmt.Errorf("failed to read host line: %s", err)
	}
	// 查找主机行中的冒号，分隔主机名和端口
	sep := strings.Index(hostLine, ":")
	if sep <= 0 || sep >= len(hostLine)-1 {
		return nil, fmt.Errorf("invalid host line: %s", hostLine)
	}
	// 提取主机名并存储到 URL 中
	hostLine = hostLine[sep+2:] // 跳过冒号和空格
	rr.URL.Host = hostLine

	// 解析头部
	rr.Request.Headers = mapsutil.NewOrderedMap[string, string]()
	for {
		// 逐行读取头部信息
		line, err := protoReader.ReadLine()
		if err != nil {
			return nil, fmt.Errorf("failed to read header line: %s", err)
		}
		if line == "" {
			// 头部结束，接下来是主体
			break
		}
		// 解析头部行，分隔键和值
		parts := strings.SplitN(line, ":", 2)
		if len(parts) != 2 {
			return nil, fmt.Errorf("invalid header line: %s", line)
		}
		// 去掉冒号后的空格并存储到头部
		rr.Request.Headers.Set(parts[0], parts[1][1:])
	}

	// 解析主体
	rr.Request.Body = ""
	// 创建一个缓冲区用于存储主体内容
	var buff bytes.Buffer
	// 从读取器中读取主体内容
	_, err = buff.ReadFrom(protoReader.R)
	if err != nil && err != io.EOF {
		return nil, fmt.Errorf("failed to read body: %s", err)
	}
	// 如果缓冲区中有内容，处理尾随换行符
	if buff.Len() > 0 {
		// 获取字节数组
		bin := buff.Bytes()
		// 移除尾随的换行符
		if bin[len(bin)-1] == '\n' {
			bin = bin[:len(bin)-1]
		}
		if bin[len(bin)-1] == '\r' || bin[len(bin)-1] == '\n' {
			bin = bin[:len(bin)-1]
		}
		// 将处理后的字节数组转换为字符串并存储到主体
		rr.Request.Body = conversion.String(bin)
	}

	// 设置原始请求字符串
	rr.Request.Raw = raw
	return rr, nil
}

// ParseRawRequestWithURL: 解析原始请求字符串并使用给定的 URL。
//
//	@param raw string: 原始请求字符串
//	@param url string: 要使用的 URL 字符串。
//	@return rr *RequestResponse:解析后的 RequestResponse 对象。
//	@return err error: 可能的错误。
func ParseRawRequestWithURL(raw, url string) (rr *RequestResponse, err error) {
	rr, err = ParseRawRequest(raw)
	if err != nil {
		return nil, err
	}
	urlx, err := urlutil.ParseAbsoluteURL(url, false)
	if err != nil {
		return nil, err
	}
	rr.URL = *urlx
	return rr, nil
}
