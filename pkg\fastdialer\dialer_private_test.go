// 
// Author: chenjb
// Version: V1.0
// Date: 2025-05-27 15:13:02
// FilePath: /yaml_scan/pkg/fastdialer/dialer_private_test.go
// Description: 
package fastdialer

import (
	"context"
	"crypto/tls"
	"net"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
	"yaml_scan/pkg/fastdialer/ja3"

	"github.com/stretchr/testify/require"
)

// 创建测试服务器并返回其URL和关闭函数
func setupTestServer(t *testing.T) (string, func()) {
	// 创建简单的HTTP测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write([]byte("test response"))
	}))
	
	return server.URL, func() {
		server.Close()
	}
}

// 创建带TLS的测试服务器
func setupTLSTestServer(t *testing.T) (string, func()) {
	// 创建带TLS的HTTP测试服务器
	server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write([]byte("test response"))
	}))
	
	return server.URL, func() {
		server.Close()
	}
}

// TestDialOptions_ConnHash 测试连接哈希生成函数
func TestDialOptions_ConnHash(t *testing.T) {
	// 创建测试用dialOptions
	opts := &dialOptions{
		network: "tcp",
		address: "example.com:80",
	}
	
	// 测试生成的哈希值
	hash := opts.connHash()
	require.Equal(t, "tcp-example.com:80", hash, "连接哈希值应为'network-address'格式")
}

// TestDialOptions_LogAddress 测试日志地址获取函数
func TestDialOptions_LogAddress(t *testing.T) {
	// 测试使用主机名的情况
	opts1 := &dialOptions{
		hostname: "example.com",
		ips:      []string{"*******"},
		port:     "80",
	}
	logAddr1 := opts1.logAddress()
	require.Equal(t, "example.com:80", logAddr1, "有主机名时应使用主机名作为日志地址")
	
	// 测试使用IP的情况
	opts2 := &dialOptions{
		hostname: "", // 空主机名
		ips:      []string{"*******"},
		port:     "443",
	}
	logAddr2 := opts2.logAddress()
	require.Equal(t, "*******:443", logAddr2, "无主机名时应使用第一个IP作为日志地址")
}

// TestDial_BasicConnection 测试基本的拨号连接功能
func TestDial_BasicConnection(t *testing.T) {
	// 设置测试服务器
	serverURL, cleanup := setupTestServer(t)
	defer cleanup()
	
	// 从URL中解析主机和端口
	serverHost, serverPort, err := net.SplitHostPort(serverURL[7:]) // 移除"http://"
	require.NoError(t, err, "无法解析服务器URL")
	
	// 创建拨号器
	dialer, err := NewDialer(DefaultOptions)
	require.NoError(t, err, "创建拨号器失败")
	defer dialer.Close()
	
	// 创建拨号选项
	opts := &dialOptions{
		network:      "tcp",
		address:      net.JoinHostPort(serverHost, serverPort),
		shouldUseTLS: false,
		ips:          []string{serverHost}, // 直接使用解析的IP
		port:         serverPort,
		hostname:     serverHost,
	}
	
	// 测试拨号
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	conn, err := dialer.dial(ctx, opts)
	require.NoError(t, err, "拨号连接应该成功")
	require.NotNil(t, conn, "连接不应为nil")
	defer conn.Close()
}

// TestDial_TLSConnection 测试TLS连接
func TestDial_TLSConnection(t *testing.T) {
	// 设置TLS测试服务器
	serverURL, cleanup := setupTLSTestServer(t)
	defer cleanup()
	
	// 从URL中解析主机和端口
	serverHost, serverPort, err := net.SplitHostPort(serverURL[8:]) // 移除"https://"
	require.NoError(t, err, "无法解析TLS服务器URL")
	
	// 创建拨号器
	dialer, err := NewDialer(DefaultOptions)
	require.NoError(t, err, "创建拨号器失败")
	defer dialer.Close()
	
	// 创建TLS配置
	tlsConfig := &tls.Config{
		InsecureSkipVerify: true, // 忽略证书验证，仅用于测试
	}
	
	// 创建拨号选项
	opts := &dialOptions{
		network:      "tcp",
		address:      net.JoinHostPort(serverHost, serverPort),
		shouldUseTLS: true,
		tlsconfig:    tlsConfig,
		ips:          []string{serverHost},
		port:         serverPort,
		hostname:     serverHost,
	}
	
	// 测试TLS拨号
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	conn, err := dialer.dial(ctx, opts)
	require.NoError(t, err, "TLS拨号连接应该成功")
	require.NotNil(t, conn, "TLS连接不应为nil")
	defer conn.Close()
	
	// 验证是否为TLS连接
	_, ok := conn.(*tls.Conn)
	require.True(t, ok, "连接应该是TLS连接类型")
}

// TestDial_JA3Impersonation 测试JA3指纹模拟
func TestDial_JA3Impersonation(t *testing.T) {
	// 设置TLS测试服务器
	serverURL, cleanup := setupTLSTestServer(t)
	defer cleanup()
	
	// 从URL中解析主机和端口
	serverHost, serverPort, err := net.SplitHostPort(serverURL[8:]) // 移除"https://"
	require.NoError(t, err, "无法解析TLS服务器URL")
	
	// 创建拨号器
	dialer, err := NewDialer(DefaultOptions)
	require.NoError(t, err, "创建拨号器失败")
	defer dialer.Close()
	
	// 创建TLS配置
	tlsConfig := &tls.Config{
		InsecureSkipVerify: true, // 忽略证书验证，仅用于测试
	}
	
	// 测试Chrome指纹模拟
	opts := &dialOptions{
		network:             "tcp",
		address:             net.JoinHostPort(serverHost, serverPort),
		shouldUseTLS:        true,
		tlsconfig:           tlsConfig,
		impersonateStrategy: ja3.Chrome,
		ips:                 []string{serverHost},
		port:                serverPort,
		hostname:            serverHost,
	}
	
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	conn, err := dialer.dial(ctx, opts)
	require.NoError(t, err, "使用Chrome指纹的TLS拨号应该成功")
	require.NotNil(t, conn, "使用Chrome指纹的TLS连接不应为nil")
	conn.Close()
}

// TestHandleDialError 测试错误处理功能
func TestHandleDialError(t *testing.T) {
	// 创建拨号器
	dialer, err := NewDialer(DefaultOptions)
	require.NoError(t, err, "创建拨号器失败")
	defer dialer.Close()
	
	// 创建测试用选项
	opts := &dialOptions{
		network:  "tcp",
		address:  "example.com:80",
		hostname: "example.com",
		port:     "80",
		ips:      []string{"*******"},
	}
	
	// 测试nil错误
	result := dialer.handleDialError(nil, opts)
	require.Nil(t, result, "nil错误应该返回nil")
	
	// 测试超时错误处理
	timeoutErr := &net.OpError{
		Op:  "dial",
		Err: &timeoutError{},
	}
	
	// 第一次超时错误应被标记为临时错误
	result = dialer.handleDialError(timeoutErr, opts)
	require.NotNil(t, result, "处理后的错误不应为nil")
	require.Contains(t, result.Error(), "address=example.com:80", "错误应包含地址属性")
	
	// 模拟多次超时，应转为永久错误
	for i := 0; i < 3; i++ {
		dialer.handleDialError(timeoutErr, opts)
	}
	
	// 现在应该被视为永久错误
	result = dialer.handleDialError(timeoutErr, opts)
	require.NotNil(t, result, "处理后的错误不应为nil")
}

// timeoutError 模拟超时错误
type timeoutError struct{}

func (e *timeoutError) Error() string   { return "i/o timeout" }
func (e *timeoutError) Timeout() bool   { return true }
func (e *timeoutError) Temporary() bool { return true } 

