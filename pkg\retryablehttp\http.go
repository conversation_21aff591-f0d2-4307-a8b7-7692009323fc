// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 16:45:58
// FilePath: /yaml_scan/pkg/retryablehttp/http.go
// Description:提供HTTP客户端的默认传输配置和实用工具，包括连接池和TLS配置
package retryablehttp

import (
	"context"
	"crypto/tls"
	"net"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"
	"yaml_scan/pkg/fastdialer"
)

var (
	// fdInit 用于确保fastdialer只被初始化一次的同步原语
	fdInit = &sync.Once{}
	// fd 保存全局的fastdialer实例
	fd     *fastdialer.Dialer
	// err 保存fastdialer初始化过程中可能发生的错误
	err    error
)

// DisableZTLSFallback 控制是否禁用在TLS握手出错时使用ztls的回退选项
// 可以通过将DISABLE_ZTLS_FALLBACK环境变量设置为true来禁用
var DisableZTLSFallback = false

func init() {
	value := os.Getenv("DISABLE_ZTLS_FALLBACK")
	if strings.EqualFold(value, "true") {
		DisableZTLSFallback = true
	}
}

// getFastDialer 返回一个fastdialer.Dialer实例，避免重复创建
// 此函数使用sync.Once确保无论调用多少次，fastdialer只初始化一次
// @return *fastdialer.Dialer *fastdialer.Dialer: 快速拨号器实例
// @return error error: 可能的错误
func getFastDialer() (*fastdialer.Dialer, error) {
	fdInit.Do(func() {
		// 使用默认选项，但设置缓存类型为内存
		opts := fastdialer.DefaultOptions
		opts.CacheType = fastdialer.Memory
		fd, err = fastdialer.NewDialer(fastdialer.DefaultOptions)
	})
	return fd, err
}

// DefaultReusePooledTransport 返回一个新的http.Transport，配置类似于http.DefaultTransport
//
// 不要将此用于临时传输，因为随着时间的推移它可能会泄漏文件描述符
// 只将此用于将重复用于相同主机的传输
// @return *http.Transport *http.Transport:
func DefaultReusePooledTransport() *http.Transport {
	fd, _ := getFastDialer()
	transport := &http.Transport{
		// 使用环境变量中配置的代理
		Proxy: http.ProxyFromEnvironment,
		// 最大空闲连接数
		MaxIdleConns: 100,
		// 空闲连接超时时间
		IdleConnTimeout: 90 * time.Second,
		// TLS握手超时时间
		TLSHandshakeTimeout: 10 * time.Second,
		// Expect: 100-continue响应超时时间
		ExpectContinueTimeout: 1 * time.Second,
		// 每个主机的最大空闲连接数
		MaxIdleConnsPerHost:    100,
		// 响应头的最大字节数（net/http默认是10Mb）
		MaxResponseHeaderBytes: 4096,
		// TLS客户端配置
		TLSClientConfig: &tls.Config{
			// 允许客户端重新协商（在TLS 1.3中不支持）
			Renegotiation:      tls.RenegotiateOnceAsClient,
			// 跳过证书验证
			InsecureSkipVerify: true,
			// 最低TLS版本为1.0
			MinVersion:         tls.VersionTLS10,
		},
	}
	// 如果fastdialer初始化成功，使用它来替换默认的拨号函数
	if fd != nil {
		// 设置TCP连接的拨号函数
		transport.DialContext = func(ctx context.Context, network, addr string) (net.Conn, error) {
			return fd.Dial(ctx, network, addr)
		}
		// 设置TLS连接的拨号函数
		transport.DialTLSContext = func(ctx context.Context, network, addr string) (net.Conn, error) {
			return fd.DialTLS(ctx, network, addr)
		}
	}
	return transport
}

// DefaultHostSprayingTransport 返回一个新的http.Transport，配置类似于http.DefaultTransport
// 但禁用了空闲连接和保活功能
// @return *http.Transport *http.Transport:
func DefaultHostSprayingTransport() *http.Transport {
	transport := DefaultReusePooledTransport()
	// 禁用HTTP长连接（keep-alive）
	transport.DisableKeepAlives = true
	// 禁用每主机连接池（设置为-1表示不限制但实际上是禁用，因为DisableKeepAlives=true）
	transport.MaxIdleConnsPerHost = -1
	return transport
}

// DefaultClient 返回一个新的http.Client，配置类似于标准http.Client
// 但具有非共享的Transport，并且禁用了空闲连接和保活功能
// @return *http.Client *http.Client:
func DefaultClient() *http.Client {
	return &http.Client{
		Transport: DefaultHostSprayingTransport(),
	}
}

// DefaultPooledClient 回一个新的http.Client，配置类似于标准http.Client
// 但有共享的Transport，启用连接重用
// @return *http.Client *http.Client: 
func DefaultPooledClient() *http.Client {
	return &http.Client{
		Transport: DefaultReusePooledTransport(),
	}
}
