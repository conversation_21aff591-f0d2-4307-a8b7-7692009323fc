//
// Author: chenjb
// Version: V1.0
// Date: 2025-07-18 11:10:30
// FilePath: /yaml_scan/pkg/wappalyzergo/tech.go
// Description: 提供Web应用技术识别功能

// Package wappalyzergo 提供基于指纹识别的Web应用技术栈检测功能
// 该包能够通过分析HTTP响应头、Cookie、HTML内容等信息来识别目标网站使用的技术栈
// 支持识别Web服务器、编程语言、框架、CMS、数据库等多种技术类型
package wappalyzergo

import (
	"bytes"
	"encoding/json"
	"fmt"
	"os"
	"strings"
)

// Wappalyze 是用于检测的客户端结构体
// 包含原始指纹数据和编译后的指纹数据，用于高效的技术栈识别
// original 字段保存从JSON解析的原始指纹格式，用于数据查询和调试
// fingerprints 字段保存编译后的指纹数据，包含预编译的正则表达式，用于快速匹配
type Wappalyze struct {
	original     *Fingerprints         // 原始指纹数据，保存从JSON解析的原始格式
	fingerprints *CompiledFingerprints // 编译后的指纹数据，用于快速匹配
}

// New 创建一个新的技术检测实例
// 该函数会自动加载内嵌的指纹数据库并编译所有指纹规则
// 
// 返回值:
//   - *Wappalyze: 初始化完成的检测实例，可用于技术栈识别
//   - error: 如果指纹数据加载或编译失败则返回错误
//
// 使用示例:
//   wappalyzer, err := New()
//   if err != nil {
//       log.Fatal(err)
//   }
//   technologies := wappalyzer.Fingerprint(headers, body)
func New() (*Wappalyze, error) {
	// 初始化Wappalyze结构体，创建空的编译指纹映射
	wappalyze := &Wappalyze{
		fingerprints: &CompiledFingerprints{
			Apps: make(map[string]*CompiledFingerprint),
		},
	}

	// 加载并编译内嵌的指纹数据
	err := wappalyze.loadFingerprints()
	if err != nil {
		return nil, err
	}
	return wappalyze, nil
}

// NewFromFile 从指定文件创建新的技术检测实例
// 该函数允许使用最新的指纹数据而无需重新编译代码
//
// 参数:
//   - filePath: 指纹文件的路径，文件应为JSON格式
//   - loadEmbedded: 是否同时加载内嵌的指纹数据
//   - supersede: 当loadEmbedded为true时，是否用文件中的指纹覆盖内嵌指纹（如果应用名称冲突）
//
// 返回值:
//   - *Wappalyze: 初始化完成的检测实例
//   - error: 如果文件读取、解析或编译失败则返回错误
//
// 使用示例:
//   // 仅加载文件指纹
//   wappalyzer, err := NewFromFile("custom_fingerprints.json", false, false)
//   
//   // 加载内嵌指纹并用文件指纹覆盖冲突项
//   wappalyzer, err := NewFromFile("latest_fingerprints.json", true, true)
func NewFromFile(filePath string, loadEmbedded, supersede bool) (*Wappalyze, error) {
	// 初始化Wappalyze结构体
	wappalyze := &Wappalyze{
		fingerprints: &CompiledFingerprints{
			Apps: make(map[string]*CompiledFingerprint),
		},
	}

	// 从文件加载指纹数据，根据参数决定是否加载内嵌数据和覆盖策略
	// loadEmbedded控制是否同时加载内嵌指纹，supersede控制冲突时的覆盖策略
	err := wappalyze.loadFingerprintsFromFile(filePath, loadEmbedded, supersede)
	if err != nil {
		return nil, err
	}

	return wappalyze, nil
}

// GetFingerprints 返回原始指纹数据
// 该方法用于获取未经编译的原始指纹数据，通常用于调试、数据导出或二次处理
//
// 返回值:
//   - *Fingerprints: 原始指纹数据结构，包含所有应用的完整指纹信息
func (s *Wappalyze) GetFingerprints() *Fingerprints {
	return s.original
}

// GetCompiledFingerprints 返回编译后的指纹数据
// 该方法用于获取已编译的指纹数据，包含预编译的正则表达式，用于性能优化场景
//
// 返回值:
//   - *CompiledFingerprints: 编译后的指纹数据，包含预编译的正则表达式和优化的数据结构
func (s *Wappalyze) GetCompiledFingerprints() *CompiledFingerprints {
	return s.fingerprints
}

// loadFingerprints 加载内嵌的指纹数据并编译
// 该方法从内嵌的JSON字符串中解析指纹数据，然后编译所有正则表达式模式
//
// 返回值:
//   - error: 如果JSON解析失败或指纹编译失败则返回错误
func (s *Wappalyze) loadFingerprints() error {
	var fingerprintsStruct Fingerprints
	// 解析内嵌的JSON指纹数据
	err := json.Unmarshal([]byte(fingerprints), &fingerprintsStruct)
	if err != nil {
		return err
	}

	// 保存原始指纹数据
	s.original = &fingerprintsStruct
	// 遍历所有应用指纹，编译正则表达式并存储到编译指纹映射中
	for i, fingerprint := range fingerprintsStruct.Apps {
		s.fingerprints.Apps[i] = compileFingerprint(fingerprint)
	}
	return nil
}

// loadFingerprintsFromFile 从指定文件加载指纹数据并编译
// 该方法支持灵活的指纹数据加载策略，可以单独使用文件指纹或与内嵌指纹合并
//
// 参数:
//   - filePath: 指纹文件路径，必须是有效的JSON格式文件
//   - loadEmbedded: 是否同时加载内嵌的指纹数据库
//   - supersede: 当存在同名应用时，是否用文件中的指纹覆盖内嵌指纹
//
// 返回值:
//   - error: 文件读取、JSON解析或指纹编译过程中的错误
//
// 处理逻辑:
//   1. 读取并解析指定的JSON文件
//   2. 根据loadEmbedded参数决定是否加载内嵌指纹
//   3. 根据supersede参数处理指纹冲突
//   4. 编译所有指纹数据为可执行的正则表达式
func (s *Wappalyze) loadFingerprintsFromFile(filePath string, loadEmbedded, supersede bool) error {
	// 读取指纹文件内容
	f, err := os.ReadFile(filePath)
	if err != nil {
		return err
	}

	// 解析JSON格式的指纹数据
	var fingerprintsStruct Fingerprints
	err = json.Unmarshal(f, &fingerprintsStruct)
	if err != nil {
		return err
	}

	// 验证文件中是否包含有效的指纹数据
	if len(fingerprintsStruct.Apps) == 0 {
		return fmt.Errorf("no fingerprints found in file: %s", filePath)
	}

	// 根据loadEmbedded参数决定是否合并内嵌指纹
	if loadEmbedded {
		// 解析内嵌的指纹数据
		var embedded Fingerprints
		err := json.Unmarshal([]byte(fingerprints), &embedded)
		if err != nil {
			return err
		}

		// 以内嵌指纹为基础
		s.original = &embedded

		// 遍历文件中的指纹，根据supersede参数处理冲突
		for app, fingerprint := range fingerprintsStruct.Apps {
			// 检查是否存在同名应用
			if _, ok := s.original.Apps[app]; ok && supersede {
				// 如果存在冲突且允许覆盖，则用文件指纹覆盖内嵌指纹
				s.original.Apps[app] = fingerprint
			} else {
				// 如果不存在冲突或不允许覆盖，则直接添加文件指纹
				s.original.Apps[app] = fingerprint
			}
		}
	} else {
		// 仅使用文件中的指纹数据
		s.original = &fingerprintsStruct
	}

	// 编译所有指纹数据为可执行的正则表达式
	// 遍历原始指纹数据，为每个应用编译指纹模式
	for i, fingerprint := range s.original.Apps {
		s.fingerprints.Apps[i] = compileFingerprint(fingerprint)
	}

	return nil
}

// Fingerprint 基于HTTP响应头和响应体识别技术栈
// 该方法是核心识别功能，通过分析HTTP响应的各个部分来匹配已知的技术指纹
//
// 参数:
//   - headers: HTTP响应头映射，键为头部名称，值为头部值列表
//   - body: HTTP响应体的字节数组，不应在函数执行期间被修改
//
// 返回值:
//   - map[string]struct{}: 识别到的技术名称集合，使用struct{}作为值以节省内存
//
// 注意事项:
//   - body参数在函数执行期间不应被修改，否则可能导致不可预期的结果
//   - 返回的技术名称可能包含版本信息，格式为"技术名:版本号"
//
// 使用示例:
//   headers := map[string][]string{
//       "Server": {"nginx/1.18.0"},
//       "X-Powered-By": {"PHP/7.4.0"},
//   }
//   body := []byte("<html>...</html>")
//   technologies := wappalyzer.Fingerprint(headers, body)
func (s *Wappalyze) Fingerprint(headers map[string][]string, body []byte) map[string]struct{} {
	// 创建唯一指纹集合，用于去重和版本管理
	uniqueFingerprints := NewUniqueFingerprints()

	// 将响应体转换为小写以进行大小写不敏感的匹配
	normalizedBody := bytes.ToLower(body)
	// 标准化HTTP头部格式（转小写，合并多值）
	normalizedHeaders := s.normalizeHeaders(headers)

	// 执行基于HTTP头部的指纹识别
	// 如果头部检查数量大于0，则运行头部指纹识别
	for _, app := range s.checkHeaders(normalizedHeaders) {
		// 将识别结果添加到唯一集合中，避免重复
		uniqueFingerprints.SetIfNotExists(app.application, app.version, app.confidence)
	}

	// 从HTTP头部中提取Cookie信息
	cookies := s.findSetCookie(normalizedHeaders)
	// 如果存在Set-Cookie头部，则执行基于Cookie的指纹识别
	if len(cookies) > 0 {
		for _, app := range s.checkCookies(cookies) {
			uniqueFingerprints.SetIfNotExists(app.application, app.version, app.confidence)
		}
	}

	// 最后检查响应体中的技术特征
	bodyTech := s.checkBody(normalizedBody)
	for _, app := range bodyTech {
		uniqueFingerprints.SetIfNotExists(app.application, app.version, app.confidence)
	}
	// 返回去重后的技术名称集合
	return uniqueFingerprints.GetValues()
}

// UniqueFingerprints 用于管理唯一指纹识别结果的结构体
// 该结构体确保每个技术只被记录一次，并保存最高置信度的版本信息
type UniqueFingerprints struct {
	values map[string]uniqueFingerprintMetadata // 技术名称到元数据的映射
}

// uniqueFingerprintMetadata 存储指纹识别的元数据信息
// 包含置信度和版本信息，用于结果去重和版本选择
type uniqueFingerprintMetadata struct {
	confidence int    // 识别置信度（0-100）
	version    string // 技术版本号
}

// NewUniqueFingerprints 创建新的唯一指纹管理器
// 该函数初始化一个空的指纹结果集合，用于后续的去重和版本管理
//
// 返回值:
//   - UniqueFingerprints: 初始化完成的唯一指纹管理器
func NewUniqueFingerprints() UniqueFingerprints {
	return UniqueFingerprints{
		values: make(map[string]uniqueFingerprintMetadata),
	}
}

// GetValues 获取所有有效的技术识别结果
// 该方法将内部的元数据映射转换为最终的技术名称集合
//
// 返回值:
//   - map[string]struct{}: 技术名称集合，键为格式化的技术名称（可能包含版本）
//
// 处理逻辑:
//   1. 过滤掉置信度为0的结果
//   2. 使用FormatAppVersion格式化技术名称和版本
//   3. 返回去重后的技术集合
func (u UniqueFingerprints) GetValues() map[string]struct{} {
	values := make(map[string]struct{}, len(u.values))
	for k, v := range u.values {
		// 跳过置信度为0的结果（表示无效或被排除的匹配）
		if v.confidence == 0 {
			continue
		}
		// 格式化技术名称和版本，添加到结果集合中
		values[FormatAppVersion(k, v.version)] = struct{}{}
	}
	return values
}

// SetIfNotExists 添加或更新技术识别结果
// 该方法实现智能的结果合并逻辑，确保每个技术只保留最佳的识别结果
//
// 参数:
//   - value: 技术名称
//   - version: 技术版本号
//   - confidence: 识别置信度（0-100）
//
// 处理逻辑:
//   1. 如果技术已存在，累加置信度（最大100）
//   2. 如果现有版本为空且新版本不为空，更新版本信息
//   3. 如果技术不存在，直接添加新的识别结果
func (u UniqueFingerprints) SetIfNotExists(value, version string, confidence int) {
	if _, ok := u.values[value]; ok {
		// 技术已存在，更新元数据
		new := u.values[value]
		// 累加置信度，但不超过100
		updatedConfidence := new.confidence + confidence
		if updatedConfidence > 100 {
			updatedConfidence = 100
		}
		new.confidence = updatedConfidence
		// 如果当前版本为空且新版本不为空，则更新版本信息
		if new.version == "" && version != "" {
			new.version = version
		}
		u.values[value] = new
		return
	}

	// 技术不存在，添加新的识别结果
	u.values[value] = uniqueFingerprintMetadata{
		confidence: confidence,
		version:    version,
	}
}

// matchPartResult 表示单个指纹匹配的结果
// 该结构体包含匹配到的技术信息和相关元数据
type matchPartResult struct {
	application string // 应用/技术名称
	confidence  int    // 匹配置信度（0-100）
	version     string // 提取的版本号
}

// FingerprintWithTitle 识别技术栈并返回页面标题
// 该方法在标准指纹识别的基础上，额外提取HTML页面的标题信息
//
// 参数:
//   - headers: HTTP响应头映射
//   - body: HTTP响应体字节数组
//
// 返回值:
//   - map[string]struct{}: 识别到的技术名称集合
//   - string: 页面标题，如果不是HTML页面则返回空字符串
//
// 注意事项:
//   - 只有当Content-Type包含"text/html"时才会提取标题和执行完整的body检查
//   - body参数在函数执行期间不应被修改
func (s *Wappalyze) FingerprintWithTitle(headers map[string][]string, body []byte) (map[string]struct{}, string) {
	// 创建唯一指纹集合
	uniqueFingerprints := NewUniqueFingerprints()

	// 标准化输入数据
	normalizedBody := bytes.ToLower(body)
	normalizedHeaders := s.normalizeHeaders(headers)

	// 执行HTTP头部指纹识别
	for _, app := range s.checkHeaders(normalizedHeaders) {
		uniqueFingerprints.SetIfNotExists(app.application, app.version, app.confidence)
	}

	// 执行Cookie指纹识别
	cookies := s.findSetCookie(normalizedHeaders)
	if len(cookies) > 0 {
		for _, app := range s.checkCookies(cookies) {
			uniqueFingerprints.SetIfNotExists(app.application, app.version, app.confidence)
		}
	}

	// 只有当内容类型为HTML时才执行body检查和标题提取
	if strings.Contains(normalizedHeaders["content-type"], "text/html") {
		// 执行响应体指纹识别
		bodyTech := s.checkBody(normalizedBody)
		for _, app := range bodyTech {
			uniqueFingerprints.SetIfNotExists(app.application, app.version, app.confidence)
		}
		// 提取页面标题
		title := s.getTitle(body)
		return uniqueFingerprints.GetValues(), title
	}
	// 非HTML内容返回空标题
	return uniqueFingerprints.GetValues(), ""
}

// versionSeparator 定义版本分隔符常量
// 用于在技术名称和版本号之间进行分隔，格式为"技术名:版本号"
const versionSeparator = ":"

// FingerprintWithInfo 识别技术栈并返回详细信息
// 该方法不仅识别技术栈，还返回每个技术的详细信息，如描述、网站URL、图标等
//
// 参数:
//   - headers: HTTP响应头映射
//   - body: HTTP响应体字节数组
//
// 返回值:
//   - map[string]AppInfo: 技术名称到详细信息的映射，包含描述、网站、图标、分类等信息
//
// 注意事项:
//   - 返回的映射键可能包含版本信息（格式为"技术名:版本号"）
//   - 如果技术名称包含版本分隔符，会尝试解析基础技术名称来获取信息
func (s *Wappalyze) FingerprintWithInfo(headers map[string][]string, body []byte) map[string]AppInfo {
	// 首先执行标准指纹识别
	apps := s.Fingerprint(headers, body)
	// 创建结果映射，预分配容量以提高性能
	result := make(map[string]AppInfo, len(apps))

	// 为每个识别到的技术获取详细信息
	for app := range apps {
		// 直接查找技术指纹信息
		if fingerprint, ok := s.fingerprints.Apps[app]; ok {
			result[app] = AppInfoFromFingerprint(fingerprint)
		}

		// 处理包含版本分隔符的技术名称
		// 例如："nginx:1.18.0" -> 查找 "nginx" 的信息
		if strings.Contains(app, versionSeparator) {
			if parts := strings.Split(app, versionSeparator); len(parts) == 2 {
				// 使用基础技术名称查找指纹信息
				if fingerprint, ok := s.fingerprints.Apps[parts[0]]; ok {
					result[app] = AppInfoFromFingerprint(fingerprint)
				}
			}
		}
	}

	return result
}

// AppInfoFromFingerprint 从编译指纹创建应用信息
// 该函数将编译后的指纹数据转换为用户友好的应用信息格式
//
// 参数:
//   - fingerprint: 编译后的指纹数据，包含技术的所有识别特征和元数据
//
// 返回值:
//   - AppInfo: 包含技术描述、网站、图标、CPE标识符和分类信息的结构体
func AppInfoFromFingerprint(fingerprint *CompiledFingerprint) AppInfo {
	// 创建分类名称切片，预分配容量
	categories := make([]string, 0, len(fingerprint.cats))
	// 将分类ID转换为分类名称
	for _, cat := range fingerprint.cats {
		if category, ok := categoriesMapping[cat]; ok {
			categories = append(categories, category.Name)
		}
	}
	// 构建并返回应用信息结构体
	return AppInfo{
		Description: fingerprint.description, // 技术描述
		Website:     fingerprint.website,     // 官方网站URL
		Icon:        fingerprint.icon,        // 图标URL或数据
		CPE:         fingerprint.cpe,         // 通用平台枚举标识符
		Categories:  categories,              // 技术分类列表
	}
}

// FingerprintWithCats 识别技术栈并返回分类信息
// 该方法专注于返回技术的分类信息，用于技术栈的分类统计和分析
//
// 参数:
//   - headers: HTTP响应头映射
//   - body: HTTP响应体字节数组
//
// 返回值:
//   - map[string]CatsInfo: 技术名称到分类信息的映射
//
// 使用场景:
//   - 技术栈分类统计
//   - 安全评估中的技术分类分析
//   - 技术栈可视化展示
func (s *Wappalyze) FingerprintWithCats(headers map[string][]string, body []byte) map[string]CatsInfo {
	// 执行标准指纹识别
	apps := s.Fingerprint(headers, body)
	// 创建结果映射
	result := make(map[string]CatsInfo, len(apps))

	// 为每个识别到的技术获取分类信息
	for app := range apps {
		if fingerprint, ok := s.fingerprints.Apps[app]; ok {
			result[app] = CatsInfo{
				Cats: fingerprint.cats, // 分类ID列表
			}
		}
	}

	return result
}


