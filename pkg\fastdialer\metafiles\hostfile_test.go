// 
// Author: chenjb
// Version: V1.0
// Date: 2025-05-26 11:48:21
// FilePath: /yaml_scan/pkg/fastdialer/metafiles/hostfile_test.go
// Description: 
package metafiles

import (
	"os"
	"path/filepath"
	"sync"
	"testing"

	"yaml_scan/pkg/hybridMap/hybrid"

	"github.com/stretchr/testify/require"
)

// TestIsComment 测试注释行检测功能
func TestIsComment(t *testing.T) {
	testCases := []struct {
		name     string
		line     string
		expected bool
	}{
		{
			name:     "纯注释行",
			line:     "# This is a comment",
			expected: true,
		},
		{
			name:     "带前导空格的注释行",
			line:     "   # This is a comment with leading spaces",
			expected: true,
		},
		{
			name:     "非注释行",
			line:     "127.0.0.1 localhost",
			expected: false,
		},
		{
			name:     "空行",
			line:     "",
			expected: false,
		},
		{
			name:     "仅包含空格的行",
			line:     "    ",
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := IsComment(tc.line)
			require.Equal(t, tc.expected, result, "IsComment返回值与预期不符")
		})
	}
}

// TestHasComment 测试行中包含注释的检测功能
func TestHasComment(t *testing.T) {
	testCases := []struct {
		name     string
		line     string
		expected bool
	}{
		{
			name:     "纯注释行",
			line:     "# This is a comment",
			expected: true,
		},
		{
			name:     "行末有注释",
			line:     "127.0.0.1 localhost # This is localhost",
			expected: true,
		},
		{
			name:     "无注释行",
			line:     "127.0.0.1 localhost",
			expected: false,
		},
		{
			name:     "带井号但非注释的行",
			line:     "127.0.0.1 server#1",
			expected: true, // 严格来说这会被检测为有注释，因为简单地检查#字符
		},
		{
			name:     "空行",
			line:     "",
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := HasComment(tc.line)
			require.Equal(t, tc.expected, result, "HasComment返回值与预期不符")
		})
	}
}

// TestHandleHostLine 测试hosts文件行解析功能
func TestHandleHostLine(t *testing.T) {
	testCases := []struct {
		name        string
		line        string
		expectedIP  string
		expectedLen int // 期望的主机名数量
	}{
		{
			name:        "标准hosts行",
			line:        "127.0.0.1 localhost",
			expectedIP:  "127.0.0.1",
			expectedLen: 1,
		},
		{
			name:        "多主机名",
			line:        "*********** host1 host2 host3",
			expectedIP:  "***********",
			expectedLen: 3,
		},
		{
			name:        "带注释的行",
			line:        "******** server1 # 这是服务器1",
			expectedIP:  "********",
			expectedLen: 1,
		},
		{
			name:        "IPv6地址",
			line:        "::1 localhost ip6-localhost ip6-loopback",
			expectedIP:  "::1",
			expectedLen: 3,
		},
		{
			name:        "注释行",
			line:        "# 这是一个注释",
			expectedIP:  "",
			expectedLen: 0,
		},
		{
			name:        "空行",
			line:        "",
			expectedIP:  "",
			expectedLen: 0,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			ip, hosts := HandleHostLine(tc.line)
			require.Equal(t, tc.expectedIP, ip, "解析的IP地址与预期不符")
			require.Len(t, hosts, tc.expectedLen, "解析的主机名数量与预期不符")
		})
	}
}

// TestGetHostsFileDnsData 测试hosts文件DNS数据获取功能
func TestGetHostsFileDnsData(t *testing.T) {
	// 创建临时hosts文件
	tempDir := t.TempDir()
	tempHostsFile := filepath.Join(tempDir, "hosts")
	
	hostsContent := `
# 这是测试用hosts文件
127.0.0.1 localhost
::1 localhost ip6-localhost
*********** server1 server2 # 测试服务器
******** test-host
`
	
	err := os.WriteFile(tempHostsFile, []byte(hostsContent), 0644)
	require.NoError(t, err, "写入临时hosts文件失败")
	
	// 保存原始环境变量和全局变量，测试后恢复
	oldHostsPath := os.Getenv("HOSTS_PATH")
	oldLoadAllEntries := LoadAllEntries
	oldMaxEntries := MaxHostsEntires
	
	// 设置环境变量指向测试文件
	os.Setenv("HOSTS_PATH", tempHostsFile)
	
	// 确保测试结束后清理
	defer func() {
		// 恢复环境变量
		if oldHostsPath != "" {
			os.Setenv("HOSTS_PATH", oldHostsPath)
		} else {
			os.Unsetenv("HOSTS_PATH")
		}
		
		// 恢复全局变量
		LoadAllEntries = oldLoadAllEntries
		MaxHostsEntires = oldMaxEntries
		
		// 重置单例变量
		hmMem = nil
		hmErr = nil
		hostMemInit = sync.OnceFunc(func() {
			// 空函数，仅用于重置
		})
		
		hmHybrid = nil
		hmHybErr = nil
		hmHybInit = sync.OnceFunc(func() {
			// 空函数，仅用于重置
		})
	}()
	
	// 1. 测试内存模式
	LoadAllEntries = false
	hm, err := GetHostsFileDnsData(InMemory)
	require.NoError(t, err, "获取内存模式的hosts数据应该成功")
	require.NotNil(t, hm, "返回的混合映射不应为nil")
	
	// 验证数据是否正确加载
	serverData, ok := hm.Get("server1")
	require.True(t, ok, "应该找到server1主机")
	require.NotEmpty(t, serverData, "server1的DNS数据不应为空")
	
	// 2. 测试混合模式
	hmHybrid = nil // 重置混合存储实例
	hmHybErr = nil
	hmHybInit = sync.OnceFunc(func() {
		// 使用新函数重置单例
		opts := hybrid.DefaultHybridOptions
		opts.Cleanup = true
		hmHybrid, hmHybErr = hybrid.New(opts)
		if hmHybErr != nil {
			return
		}
		hmHybErr = loadHostsFile(hmHybrid, -1)
		if hmHybErr != nil {
			hmHybrid.Close()
		}
	})
	
	hm, err = GetHostsFileDnsData(Hybrid)
	require.NoError(t, err, "获取混合模式的hosts数据应该成功")
	require.NotNil(t, hm, "返回的混合映射不应为nil")
	
	// 3. 测试LoadAllEntries设为true的情况
	LoadAllEntries = true
	hm, err = GetHostsFileDnsData(InMemory) // 虽然请求内存模式，但应返回混合模式
	require.NoError(t, err, "启用LoadAllEntries时获取hosts数据应该成功")
	require.NotNil(t, hm, "返回的混合映射不应为nil")
	
	// 4. 测试条目限制
	MaxHostsEntires = 1
	hmMem = nil // 重置内存存储实例
	hmErr = nil
	hostMemInit = sync.OnceFunc(func() {
		// 使用新函数重置单例
		opts := hybrid.DefaultMemoryOptions
		hmMem, hmErr = hybrid.New(opts)
		if hmErr != nil {
			return
		}
		hmErr = loadHostsFile(hmMem, MaxHostsEntires)
		if hmErr != nil {
			hmMem.Close()
		}
	})
	
	LoadAllEntries = false
	hm, err = GetHostsFileDnsData(InMemory)
	require.NoError(t, err, "限制条目数时获取hosts数据应该成功")
	require.NotNil(t, hm, "返回的混合映射不应为nil")
} 

