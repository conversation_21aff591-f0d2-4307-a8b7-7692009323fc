// Author: chenjb
// Version: V1.0
// Date: 2025-05-30 17:31:32
// FilePath: /yaml_scan/pkg/cidr/cidr.go
// Description: 提供CIDR相关的工具函数，用于IP地址范围计算、子网划分和地址转换
package cidr

import (
	"fmt"
	"math"
	"math/big"
	"net"
)

// AddressRange 返回给定CIDR范围内的第一个和最后一个IP地址。
// @param network *net.IPNet: 表示CIDR的IPNet对象
// @return firstIP net.IP: CIDR范围内的第一个IP地址	
// @return lastIP net.IP: CIDR范围内的最后一个IP地址
// @return err error: 如果有错误发生，返回相应的错误
func AddressRange(network *net.IPNet) (firstIP, lastIP net.IP, err error) {
	// 第一个IP地址就是IPNet中的IP地址（网络地址）
	firstIP = network.IP

	// 获取CIDR的前缀长度和总比特数
	prefixLen, bits := network.Mask.Size()
	// 如果前缀长度等于总比特数，表示这是单个IP而非范围
	if prefixLen == bits {
		lastIP := make([]byte, len(firstIP))
		copy(lastIP, firstIP)
		return firstIP, lastIP, nil
	}

	// 将第一个IP地址转换为整数表示形式
	firstIPInt, bits, err := IPToInteger(firstIP)
	if err != nil {
		return nil, nil, err
	}
	// 计算主机部分的比特长度
	hostLen := uint(bits) - uint(prefixLen)
	// 创建一个表示为1的大整数
	lastIPInt := big.NewInt(1)
	// 将1左移hostLen位，得到2^hostLen
	lastIPInt.Lsh(lastIPInt, hostLen)
	// 减1，得到全为1的主机位
	lastIPInt.Sub(lastIPInt, big.NewInt(1))
	// 与firstIPInt进行OR操作，得到CIDR范围内的最后一个IP地址的整数表示
	lastIPInt.Or(lastIPInt, firstIPInt)
	// 将整数表示转换回IP地址
	lastIP = IntegerToIP(lastIPInt, bits)
	return
}

// AddressCountIpnet 返回IPNet结构中包含的IP地址数量。
// @param network *net.IPNet: 表示CIDR的IPNet对象
// @return uint64 uint64: CIDR范围内的IP地址总数
func AddressCountIpnet(network *net.IPNet) uint64 {
	// 获取CIDR的前缀长度和总比特数
	prefixLen, bits := network.Mask.Size()
	// 计算地址数量: 2^(位数-前缀长度)
	return 1 << (uint64(bits) - uint64(prefixLen))
}

// AddressCount 返回CIDR字符串表示的网络范围内的IP地址数量。
// @param cidr string:  CIDR表示法的字符串，如"***********/24"
// @return uint64 uint64: CIDR范围内的IP地址总数
// @return error error: 如果CIDR字符串格式不正确，返回错误
func AddressCount(cidr string) (uint64, error) {
	// 解析CIDR字符串为IPNet对象
	_, ipnet, err := net.ParseCIDR(cidr)
	if err != nil {
		return 0, err
	}
	// 调用AddressCountIpnet计算地址数量
	return AddressCountIpnet(ipnet), nil
}


// SplitByNumber 将给定的CIDR划分为多个子网，使得每个子网中的主机数量尽可能接近指定数量。
// @param iprange string: CIDR表示法的字符串
// @param number int:  每个子网中期望的主机数量
// @return []*net.IPNet []*net.IPNet: 划分后的子网列表
// @return error error: 如果CIDR字符串格式不正确，返回错误
func SplitByNumber(iprange string, number int) ([]*net.IPNet, error) {
	// 解析CIDR字符串为IPNet对象
	_, ipnet, err := net.ParseCIDR(iprange)
	if err != nil {
		return nil, err
	}
	return SplitIPNetByNumber(ipnet, number)
}

// SplitIPNetByNumber 将IPNet划分为多个子网，使得每个子网中的主机数量尽可能接近指定数量。
// @param ipnet *net.IPNet: 表示CIDR的IPNet对象
// @param number int: 每个子网中期望的主机数量
// @return []*net.IPNet []*net.IPNet: 划分后的子网列表
// @return error error: 如果发生错误，返回相应的错误
func SplitIPNetByNumber(ipnet *net.IPNet, number int) ([]*net.IPNet, error) {
	// 计算原始CIDR中的IP地址总数
	ipsNumber := AddressCountIpnet(ipnet)

	// 计算最佳划分数量：总地址数除以每个子网期望的地址数
	optimalSplit := int(ipsNumber / uint64(number))
	// 调用SplitIPNetIntoN函数，将网络划分为optimalSplit个子网
	return SplitIPNetIntoN(ipnet, optimalSplit)
}


// IPToInteger 将IP地址转换为其整数表示形式。
// 同时支持IPv4和IPv6地址。
// @param ip net.IP: 要转换的IP地址
// @return *big.Int *big.Int: IP地址的大整数表示
// @return int int: IP地址的比特长度（IPv4为32，IPv6为128）
// @return error error: 如果IP地址格式不受支持，返回错误
func IPToInteger(ip net.IP) (*big.Int, int, error) {
	// 创建一个新的大整数用于存储结果
	val := new(big.Int)

	// 检查是否为IPv4地址，并转换为4字节表示形式
	if ipv4 := ip.To4(); ipv4 != nil {
		val.SetBytes(ipv4)
		return val, 32, nil
	}

	// 检查是否为IPv6地址，并转换为16字节表示形式
	if ipv6 := ip.To16(); ipv6 != nil {
		val.SetBytes(ipv6)
		return val, 128, nil
	}

	return nil, 0, fmt.Errorf("unsupported IP address format")
}

// IntegerToIP 将整数表示的IP地址转换为net.IP格式。
// @param ipInt *big.Int: IP地址的大整数表示
// @param bits int:  IP地址的比特长度（IPv4为32，IPv6为128）
// @return net.IP net.IP: 转换后的IP地址
func IntegerToIP(ipInt *big.Int, bits int) net.IP {
	// 获取大整数的字节表示
	ipBytes := ipInt.Bytes()
	// 创建一个与IP地址长度匹配的字节切片
	ret := make([]byte, bits/8) //nolint
		// 将字节从右到左依次填充，保持大端字节序
	for i := 1; i <= len(ipBytes); i++ {
		ret[len(ret)-i] = ipBytes[len(ipBytes)-i]
	}
	return net.IP(ret)
}

// isPowerOfTwo  检查一个数字是否为2的幂。
// @param x int: 要检查的整数
// @return bool bool: 如果x是2的幂，返回true；否则返回false
func isPowerOfTwo(x int) bool {
	// 0不是2的幂，且2的幂在二进制中只有一位为1，所以x&(x-1)会清除最低位的1
	return x != 0 && (x&(x-1)) == 0
}

// isPowerOfTwoPlusOne 检查一个数字是否为(2^n+1)形式。
// @param x int:  要检查的整数
// @return bool bool: 如果x是2^n+1形式，返回true；否则返回false
func isPowerOfTwoPlusOne(x int) bool {
	return isPowerOfTwo(x - 1)
}


// nextPowerOfTwo 返回大于或等于v的最小2的幂。
// @param v uint32:  输入的无符号32位整数
// @return uint32 uint32: 大于或等于v的最小2的幂
func nextPowerOfTwo(v uint32) uint32 {
	// 先将v减1，防止v本身就是2的幂的情况
	v--
	// 一系列右移和OR操作，将v二进制表示中最高位的1右侧全部填充为1
	v |= v >> 1
	v |= v >> 2
	v |= v >> 4
	v |= v >> 8
	v |= v >> 16
	// 加1，得到全部右侧位被清零的结果，即大于原值的最小2的幂
	v++
	return v
}

// closestPowerOfTwo 返回最接近v的2的幂。
// @param v uint32: 输入的无符号32位整数
// @return uint32 uint32: 最接近v的2的幂
func closestPowerOfTwo(v uint32) uint32 {
	// 获取大于或等于v的最小2的幂
	next := nextPowerOfTwo(v)
	if prev := next / 2; (v - prev) < (next - v) {
		// 如果v与prev的距离小于v与next的距离，返回prev
		next = prev
	}
	return next
}

// currentSubnet 根据给定的前缀长度计算当前子网。
// @param network *net.IPNet: 原始网络的IPNet对象
// @param prefixLen int: 新的前缀长度
// @return *net.IPNet *net.IPNet: 计算出的当前子网
// @return error error: 如果有错误发生，返回相应的错误
func currentSubnet(network *net.IPNet, prefixLen int) (*net.IPNet, error) {
	// 获取原始网络的第一个IP地址
	currentFirst, _, err := AddressRange(network)
	if err != nil {
		return nil, err
	}
	// 根据新的前缀长度创建子网掩码
	mask := net.CIDRMask(prefixLen, 8*len(currentFirst)) //nolint
	// 返回新的子网，IP地址为原始第一个IP地址按新掩码截断后的结果
	return &net.IPNet{IP: currentFirst.Mask(mask), Mask: mask}, nil
}

// nextSubnet 返回给定网络的下一个子网。
// @param network *net.IPNet:  当前网络的IPNet对象
// @param prefixLen int: 子网的前缀长度
// @return *net.IPNet *net.IPNet: 下一个子网
// @return error error:  如果有错误发生，返回相应的错误
func nextSubnet(network *net.IPNet, prefixLen int) (*net.IPNet, error) {
	// 获取当前网络的最后一个IP地址
	_, currentLast, err := AddressRange(network)
	if err != nil {
		return nil, err
	}
	// 根据前缀长度创建子网掩码
	mask := net.CIDRMask(prefixLen, 8*len(currentLast)) //nolint
	// 计算当前IP对应的子网
	currentSubnet := &net.IPNet{IP: currentLast.Mask(mask), Mask: mask}
	// 获取该子网的最后一个IP
	_, last, err := AddressRange(currentSubnet)
	if err != nil {
		return nil, err
	}
	// 最后一个IP加1，得到下一个子网的第一个IP
	last = inc(last)
	// 创建下一个子网
	next := &net.IPNet{IP: last.Mask(mask), Mask: mask}
	// 处理IP溢出情况（如IPv4地址***************+1）
	if last.Equal(net.IPv4zero) || last.Equal(net.IPv6zero) {
		return next, nil
	}
	return next, nil
}

// divideIPNet 将一个IPNet划分为两个更小的IPNet结构。
// @param ipnet *net.IPNet: 要划分的IPNet对象
// @return []*net.IPNet []*net.IPNet:  划分后的两个IPNet对象组成的切片
// @return error error:  如果有错误发生，返回相应的错误
func divideIPNet(ipnet *net.IPNet) ([]*net.IPNet, error) {
	// 创建容量为2的子网切片
	subnets := make([]*net.IPNet, 0, 2) //nolint
	
	// 获取当前掩码长度
	maskBits, _ := ipnet.Mask.Size()
	// 新子网的掩码长度增加1位（子网数量翻倍，每个子网大小减半）
	wantedMaskBits := maskBits + 1

	// 计算第一个子网
	currentSubnet, err := currentSubnet(ipnet, wantedMaskBits)
	if err != nil {
		return nil, err
	}
	// 添加到结果切片
	subnets = append(subnets, currentSubnet)
	// 计算第二个子网（下一个子网）
	nextSubnet, err := nextSubnet(currentSubnet, wantedMaskBits)
	if err != nil {
		return nil, err
	}
	subnets = append(subnets, nextSubnet)

	return subnets, nil
}

// splitIPNet 将IPNet划分为近似N个子网。
// @param ipnet *net.IPNet: 要划分的IPNet对象
// @param n int: 期望划分的子网数量
// @return []*net.IPNet []*net.IPNet: 划分后的子网列表
// @return error error: 可能的错误
func splitIPNet(ipnet *net.IPNet, n int) ([]*net.IPNet, error) {
	var err error
	// 创建子网切片，预分配n个容量
	subnets := make([]*net.IPNet, 0, n)

	// 获取当前掩码长度
	maskBits, _ := ipnet.Mask.Size()
	// 找到最接近n的2的幂
	closestPow2 := int(closestPowerOfTwo(uint32(n)))
	// 计算该2的幂的指数
	pow2 := int(math.Log2(float64(closestPow2)))

	// 计算新的掩码长度：原长度加上所需的位数
	wantedMaskBits := maskBits + pow2

	// 计算第一个子网
	currentSubnet, err := currentSubnet(ipnet, wantedMaskBits)
	if err != nil {
		return nil, err
	}
	// 添加到结果切片
	subnets = append(subnets, currentSubnet)
	nxtSubnet := currentSubnet
	// 计算剩余子网
	for i := 0; i < closestPow2-1; i++ {
		// 获取下一个子网
		nxtSubnet, err = nextSubnet(nxtSubnet, wantedMaskBits)
		if err != nil {
			return nil, err
		}
		subnets = append(subnets, nxtSubnet)
	}

	// 如果生成的子网数量小于期望值n，继续划分最后一个子网
	if len(subnets) < n {
		lastSubnet := subnets[len(subnets)-1]
		subnets = subnets[:len(subnets)-1]
		// 将最后一个子网划分为两个
		ipnets, err := divideIPNet(lastSubnet)
		if err != nil {
			return nil, err
		}
		// 将划分结果添加到结果切片
		subnets = append(subnets, ipnets...)
	}
	return subnets, nil
}

// reverseIPNet reverses an ipnet slice
func reverseIPNet(ipnets []*net.IPNet) {
	for i, j := 0, len(ipnets)-1; i < j; i, j = i+1, j-1 {
		ipnets[i], ipnets[j] = ipnets[j], ipnets[i]
	}
}


// SplitIPNetIntoN 尝试将一个IPNet划分为精确数量的子网。
// @param iprange *net.IPNet: 要划分的IPNet对象
// @param n int: 期望划分的子网数量
// @return []*net.IPNet []*net.IPNet: 划分后的子网列表
// @return error error: 如果有错误发生，返回相应的错误
func SplitIPNetIntoN(iprange *net.IPNet, n int) ([]*net.IPNet, error) {
	var err error
	// 创建子网切片，预分配n个容量
	subnets := make([]*net.IPNet, 0, n)

	// 检查无效值：n<=1或原始网络IP数小于n
	if n <= 1 || AddressCountIpnet(iprange) < uint64(n) {
		subnets = append(subnets, iprange)
		return subnets, nil
	}
	// 如果n是2的幂或(2的幂+1)，可以直接使用splitIPNet函数
	if isPowerOfTwo(n) || isPowerOfTwoPlusOne(n) {
		return splitIPNet(iprange, n)
	}

	var closestMinorPowerOfTwo int
	// 寻找小于n的最大2的幂
	for i := n; i > 0; i-- {
		if isPowerOfTwo(i) {
			closestMinorPowerOfTwo = i
			break
		}
	}

	// 首先将网络划分为closestMinorPowerOfTwo个子网
	subnets, err = splitIPNet(iprange, closestMinorPowerOfTwo)
	if err != nil {
		return nil, err
	}
	// 继续划分，直到达到期望的子网数量
	for len(subnets) < n {
		var newSubnets []*net.IPNet
		level := 1
		// 从后向前遍历现有子网
		for i := len(subnets) - 1; i >= 0; i-- {
			// 将当前子网划分为两个
			divided, err := divideIPNet(subnets[i])
			if err != nil {
				return nil, err
			}
			// 添加到新子网列表
			newSubnets = append(newSubnets, divided...)
			// 检查是否已达到期望的子网数量
			if len(subnets)-level+len(newSubnets) == n {
				// 反转新子网列表（保持地址顺序）
				reverseIPNet(newSubnets)
				subnets = subnets[:len(subnets)-level]
				subnets = append(subnets, newSubnets...)
				return subnets, nil
			}
			level++
		}
		// 如果所有子网都已划分但仍未达到n，使用新划分的子网继续迭代
		reverseIPNet(newSubnets)
		subnets = newSubnets
	}
	return subnets, nil
}

// SplitN 尝试将一个CIDR字符串表示的网络划分为精确数量的子网。
// @param iprange string: CIDR表示法的字符串
// @param n int: 期望划分的子网数量
// @return []*net.IPNet []*net.IPNet: 划分后的子网列表
// @return error error: 如果有错误发生，返回相应的错误
func SplitN(iprange string, n int) ([]*net.IPNet, error) {
	// 解析CIDR字符串为IPNet对象
	_, ipnet, err := net.ParseCIDR(iprange)
	if err != nil {
		return nil, err
	}
	return SplitIPNetIntoN(ipnet, n)
}

// IPAddresses 返回CIDR范围内的所有IP地址。
// @param cidr string: CIDR表示法的字符串
// @return []string []string: CIDR范围内所有IP地址的字符串表示组成的切片
// @return error error: 可能的错误
func IPAddresses(cidr string) ([]string, error) {
	// 解析CIDR字符串为IPNet对象
	_, ipnet, err := net.ParseCIDR(cidr)
	if err != nil {
		return []string{}, err
	}
	return IPAddressesIPnet(ipnet), nil
}

// IPAddressesIPnet 返回IPNet中的所有IP地址。
// @param ipnet *net.IPNet:  表示CIDR的IPNet对象
// @return ips []string: IPNet中所有IP地址的字符串表示组成的切片
func IPAddressesIPnet(ipnet *net.IPNet) (ips []string) {
	for ip := range IpAddresses(ipnet) {
		ips = append(ips, ip)
	}
	return ips
}

// IpAddresses  以流的形式返回IPNet中的IP地址。
// @param ipnet *net.IPNet: 表示CIDR的IPNet对象
// @return ips chan string: 包含IP地址字符串的通道
func IpAddresses(ipnet *net.IPNet) (ips chan string) {
	// 创建字符串通道
	ips = make(chan string)
	// 启动goroutine处理IP地址生成
	go func() {
		defer close(ips)

		// 将IPNet转换为网络范围
		netWithRange := ipNetToRange(*ipnet)
		// 从第一个IP开始，迭代到最后一个IP之前
		for ip := *netWithRange.First; !ip.Equal(*netWithRange.Last); ip = GetNextIP(ip) {
			ips <- ip.String()
		}

		// 添加最后一个IP地址
		ips <- netWithRange.Last.String()
	}()
	return ips
}

// IPAddressesAsStream 以流的形式返回CIDR字符串表示的网络中的所有IP地址。
// @param cidr string: CIDR表示法的字符串
// @return chan string: 包含IP地址字符串的通道
// @return error error: 可能的错误
func IPAddressesAsStream(cidr string) (chan string, error) {
	_, ipnet, err := net.ParseCIDR(cidr)
	if err != nil {
		return nil, err
	}
	return IpAddresses(ipnet), nil
}

