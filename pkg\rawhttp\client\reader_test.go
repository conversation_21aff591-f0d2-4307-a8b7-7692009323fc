// Author: chenjb
// Version: V1.0
// Date: 2025-07-25
// FilePath: /yaml_scan/pkg/rawhttp/client/reader_test.go
// Description: rawhttp响应读取器模块单元测试

package client

import (
	"bufio"
	"io"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
)

// TestReader_ReadVersion 测试ReadVersion方法
func TestReader_ReadVersion(t *testing.T) {
	testCases := []struct {
		input    string
		expected Version
		hasError bool
		desc     string
	}{
		{"HTTP/1.0 ", Version{Major: 1, Minor: 0}, false, "HTTP/1.0版本解析"},
		{"HTTP/1.1 ", Version{Major: 1, Minor: 1}, false, "HTTP/1.1版本解析"},
		{"HTTP/2 ", Version{Major: 2, Minor: 0}, false, "HTTP/2版本解析"},
		{"HTTP/3 ", Version{Major: 3, Minor: 0}, false, "HTTP/3版本解析"},
		{"XTTP/1.1 ", Version{}, true, "无效的协议名称"},
		{"HTTP/X.1 ", Version{}, true, "无效的主版本号"},
		{"HTTP/1.X ", Version{}, true, "无效的次版本号"},
		{"HTTP/1.1", Version{}, true, "缺少结尾空格"},
		{"", Version{}, true, "空输入"},
	}
	
	for _, tc := range testCases {
		r := &reader{Reader: bufio.NewReader(strings.NewReader(tc.input))}
		
		version, err := r.ReadVersion()
		
		if tc.hasError {
			require.Error(t, err, tc.desc+"应该返回错误")
		} else {
			require.NoError(t, err, tc.desc+"不应该返回错误")
			require.Equal(t, tc.expected, version, tc.desc+"版本应该正确")
		}
	}
}

// TestReader_ReadStatusCode 测试ReadStatusCode方法
func TestReader_ReadStatusCode(t *testing.T) {
	testCases := []struct {
		input    string
		expected int
		hasError bool
		desc     string
	}{
		{"200 ", 200, false, "标准200状态码"},
		{"404 ", 404, false, "标准404状态码"},
		{"500\r", 500, false, "以回车符结尾的状态码"},
		{"999 ", 999, false, "非标准状态码"},
		{"0 ", 0, false, "零状态码"},
		{"abc ", 0, true, "非数字状态码"},
		{"", 0, true, "空输入"},
	}
	
	for _, tc := range testCases {
		r := &reader{Reader: bufio.NewReader(strings.NewReader(tc.input))}
		
		code, err := r.ReadStatusCode()
		
		if tc.hasError {
			require.Error(t, err, tc.desc+"应该返回错误")
		} else {
			require.NoError(t, err, tc.desc+"不应该返回错误")
			require.Equal(t, tc.expected, code, tc.desc+"状态码应该正确")
		}
	}
}

// TestReader_ReadStatusLine 测试ReadStatusLine方法
func TestReader_ReadStatusLine(t *testing.T) {
	testCases := []struct {
		input           string
		expectedVersion Version
		expectedCode    int
		expectedMsg     string
		hasError        bool
		desc            string
	}{
		{
			"HTTP/1.1 200 OK\r\n",
			Version{Major: 1, Minor: 1},
			200,
			"OK",
			false,
			"标准HTTP响应状态行",
		},
		{
			"HTTP/1.0 404 Not Found\r\n",
			Version{Major: 1, Minor: 0},
			404,
			"Not Found",
			false,
			"HTTP/1.0 404响应",
		},
		{
			"HTTP/2 500 Internal Server Error\r\n",
			Version{Major: 2, Minor: 0},
			500,
			"Internal Server Error",
			false,
			"HTTP/2响应",
		},
		{
			"HTTP/1.1 204 \r\n",
			Version{Major: 1, Minor: 1},
			204,
			"",
			false,
			"空状态消息",
		},
		{
			"INVALID/1.1 200 OK\r\n",
			Version{},
			0,
			"",
			true,
			"无效的HTTP版本",
		},
	}
	
	for _, tc := range testCases {
		r := &reader{Reader: bufio.NewReader(strings.NewReader(tc.input))}
		
		version, code, msg, err := r.ReadStatusLine()
		
		if tc.hasError {
			require.Error(t, err, tc.desc+"应该返回错误")
		} else {
			require.NoError(t, err, tc.desc+"不应该返回错误")
			require.Equal(t, tc.expectedVersion, version, tc.desc+"版本应该正确")
			require.Equal(t, tc.expectedCode, code, tc.desc+"状态码应该正确")
			require.Equal(t, tc.expectedMsg, msg, tc.desc+"状态消息应该正确")
		}
	}
}

// TestReader_ReadHeader 测试ReadHeader方法
func TestReader_ReadHeader(t *testing.T) {
	testCases := []struct {
		input        string
		expectedKey  string
		expectedVal  string
		expectedDone bool
		hasError     bool
		desc         string
	}{
		{
			"Content-Type: application/json\r\n",
			"Content-Type",
			"application/json",
			false,
			false,
			"标准头部",
		},
		{
			"Host: example.com\r\n",
			"Host",
			"example.com",
			false,
			false,
			"Host头部",
		},
		{
			"  User-Agent  :  test-agent  \r\n",
			"User-Agent",
			"test-agent",
			false,
			false,
			"带空格的头部",
		},
		{
			"\r\n",
			"",
			"",
			true,
			false,
			"空行（头部结束）",
		},
		{
			"\n",
			"",
			"",
			true,
			false,
			"仅换行符的空行",
		},
		{
			"Invalid-Header-Line\r\n",
			"",
			"",
			false,
			true,
			"无效的头部行（缺少冒号）",
		},
	}
	
	for _, tc := range testCases {
		r := &reader{Reader: bufio.NewReader(strings.NewReader(tc.input))}
		
		key, val, done, err := r.ReadHeader()
		
		if tc.hasError {
			require.Error(t, err, tc.desc+"应该返回错误")
		} else {
			require.NoError(t, err, tc.desc+"不应该返回错误")
			require.Equal(t, tc.expectedKey, key, tc.desc+"头部键应该正确")
			require.Equal(t, tc.expectedVal, val, tc.desc+"头部值应该正确")
			require.Equal(t, tc.expectedDone, done, tc.desc+"完成标志应该正确")
		}
	}
}

// TestReader_ReadBody 测试ReadBody方法
func TestReader_ReadBody(t *testing.T) {
	testData := "这是响应体内容"
	r := &reader{Reader: bufio.NewReader(strings.NewReader(testData))}
	
	body := r.ReadBody()
	require.NotNil(t, body, "响应体读取器不应该为nil")
	
	// 验证返回的是reader自身
	require.Equal(t, r, body, "ReadBody应该返回reader自身")
	
	// 验证可以从返回的读取器中读取数据
	data, err := io.ReadAll(body)
	require.NoError(t, err, "读取响应体应该成功")
	require.Equal(t, testData, string(data), "响应体内容应该正确")
}

// TestReader_ReadLine 测试readLine方法
func TestReader_ReadLine(t *testing.T) {
	testCases := []struct {
		input    string
		expected string
		hasError bool
		desc     string
	}{
		{
			"Hello World\n",
			"Hello World\n",
			false,
			"标准行",
		},
		{
			"Line1\nLine2\n",
			"Line1\n",
			false,
			"多行中的第一行",
		},
		{
			"No newline",
			"",
			true,
			"没有换行符的行",
		},
		{
			"",
			"",
			true,
			"空输入",
		},
	}
	
	for _, tc := range testCases {
		r := &reader{Reader: bufio.NewReader(strings.NewReader(tc.input))}
		
		line, err := r.readLine()
		
		if tc.hasError {
			require.Error(t, err, tc.desc+"应该返回错误")
		} else {
			require.NoError(t, err, tc.desc+"不应该返回错误")
			require.Equal(t, tc.expected, string(line), tc.desc+"行内容应该正确")
		}
	}
}

// TestReader_CompleteHTTPResponse 测试完整的HTTP响应解析
func TestReader_CompleteHTTPResponse(t *testing.T) {
	httpResponse := "HTTP/1.1 200 OK\r\n" +
		"Content-Type: application/json\r\n" +
		"Content-Length: 13\r\n" +
		"Connection: close\r\n" +
		"\r\n" +
		"{\"test\":true}"
	
	r := &reader{Reader: bufio.NewReader(strings.NewReader(httpResponse))}
	
	// 读取状态行
	version, code, msg, err := r.ReadStatusLine()
	require.NoError(t, err, "读取状态行应该成功")
	require.Equal(t, Version{Major: 1, Minor: 1}, version, "版本应该正确")
	require.Equal(t, 200, code, "状态码应该正确")
	require.Equal(t, "OK", msg, "状态消息应该正确")
	
	// 读取头部
	headers := make(map[string]string)
	for {
		key, val, done, err := r.ReadHeader()
		require.NoError(t, err, "读取头部应该成功")
		if done {
			break
		}
		headers[key] = val
	}
	
	// 验证头部
	require.Equal(t, "application/json", headers["Content-Type"], "Content-Type应该正确")
	require.Equal(t, "13", headers["Content-Length"], "Content-Length应该正确")
	require.Equal(t, "close", headers["Connection"], "Connection应该正确")
	
	// 读取响应体
	body := r.ReadBody()
	bodyData, err := io.ReadAll(body)
	require.NoError(t, err, "读取响应体应该成功")
	require.Equal(t, "{\"test\":true}", string(bodyData), "响应体内容应该正确")
}
