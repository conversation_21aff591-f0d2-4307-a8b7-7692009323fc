//
//Author: chenjb
//Version: V1.0
//Date: 2024-12-09 16:44:47
//FilePath: /yaml_scan/pkg/retryabledns/doh/options.go
//Description:

package doh

import (
	"fmt"
	"net/http"
	"time"
)

// DefaultTimeout 默认超时时间
var DefaultTimeout = 5 * time.Second

// Method 定义 HTTP 请求的方法类型
type Method string

const (
	MethodGet  Method = http.MethodGet
	MethodPost Method = http.MethodPost
)

// Resolver 结构体表示一个 DNS over HTTPS (DoH) 解析器
type Resolver struct {
	Name string // 解析器的名称
	URL  string // 解析器的 URL
}

// Options 定义了用于配置 Client 的选项
type Options struct {
	DefaultResolver Resolver     // 默认解析器
	HttpClient      *http.Client // 自定义的 http.Client
}

// 用 DoH 解析器实例
var (
	Tencent = Resolver{Name: "Tencent", URL: "https://doh.pub/dns-query"}
	Google  = Resolver{Name: "Google", URL: "https://dns.google.com/resolve"}
	Quad    = Resolver{Name: "Quad", URL: "https://***************/dns-query"}
	Ali     = Resolver{Name: "Ali", URL: "https://dns.alidns.com/dns-query?"}
)

// QuestionType 定义 DNS 查询的类型
type QuestionType string

// ToString:  将 QuestionType 转换为字符串形式
//
//	@receiver q QuestionType:
//	@return string string: 表示查询类型的字符串
func (q QuestionType) ToString() string {
	return fmt.Sprint(q)
}

const (
	A     QuestionType = "A"
	AAAA  QuestionType = "AAAA"
	MX    QuestionType = "MX"
	NS    QuestionType = "NS"
	SOA   QuestionType = "SOA"
	PTR   QuestionType = "PTR"
	ANY   QuestionType = "ANY"
	CNAME QuestionType = "CNAME"
)

// Question 表示 DNS 查询的问题部分
type Question struct {
	Name string `json:"name"` // 查询的域名，例如 "example.com"
	Type int    `json:"type"` // 查询类型，例如 1 表示 A 记录
}

// Answer 表示 DNS 响应的答案部分
type Answer struct {
	Name string `json:"name"` // 域名，例如 "example.com"
	Type int    `json:"type"` // 记录类型，例如 1 表示 A 记录
	TTL  int    `json:"TTL"`  // 生存时间（秒），表示记录的有效期
	Data string `json:"data"` // 记录数据
}

// Response DNS 响应的整体结构
type Response struct {
	Status   int        `json:"Status"`   // 响应状态码，0 表示成功
	TC       bool       `json:"TC"`       // 是否被截断（Truncated）
	RD       bool       `json:"RD"`       // 是否期望递归（Recursion Desired）
	RA       bool       `json:"RA"`       // 是否支持递归（Recursion Available）
	AD       bool       `json:"AD"`       // 是否经过认证（Authenticated DNSSEC）
	CD       bool       `json:"CD"`       // 是否禁用认证（Checking DNSSEC）
	Question []Question `json:"Question"` // 查询的问题列表
	Answer   []Answer   `json:"Answer"`   // 响应的答案列表
	Comment  string     // 注释，用于附加说明
}
