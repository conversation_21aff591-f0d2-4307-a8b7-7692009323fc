// 
// Author: chenjb
// Version: V1.0
// Date: 2025-06-09 20:06:12
// FilePath: /yaml_scan/utils/reader/timeout_reader_test.go
// Description: 
package reader

import (
	"bytes"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

// 创建一个慢速读取器，用于测试超时
type slowReader struct {
	data     []byte
	delay    time.Duration
	position int
}

func (r *slowReader) Read(p []byte) (n int, err error) {
	// 休眠指定时间，模拟慢速读取
	time.Sleep(r.delay)

	// 如果数据已读完，返回0
	if r.position >= len(r.data) {
		return 0, nil
	}

	// 计算要读取的字节数
	readCount := len(p)
	if readCount > len(r.data)-r.position {
		readCount = len(r.data) - r.position
	}

	// 复制数据
	copy(p, r.data[r.position:r.position+readCount])
	r.position += readCount

	return readCount, nil
}

func TestTimeoutReader_Read(t *testing.T) {
	// 测试TimeoutReader的Read方法
	t.Run("正常读取不超时", func(t *testing.T) {
		r := require.New(t)

		// 创建一个快速读取器
		input := []byte("测试数据")
		baseReader := bytes.NewReader(input)

		// 创建一个TimeoutReader，设置足够长的超时时间
		timeoutReader := TimeoutReader{
			Timeout: 1 * time.Second,
			Reader:  baseReader,
		}

		// 读取数据
		buffer := make([]byte, 100)
		n, err := timeoutReader.Read(buffer)

		// 验证结果
		r.NoError(err)
		r.Equal(len(input), n)
		r.Equal(input, buffer[:n])
	})

	t.Run("读取超时", func(t *testing.T) {
		r := require.New(t)

		// 创建一个慢速读取器，延迟200毫秒
		input := []byte("测试数据")
		baseReader := &slowReader{
			data:  input,
			delay: 200 * time.Millisecond,
		}

		// 创建一个TimeoutReader，设置较短的超时时间
		timeoutReader := TimeoutReader{
			Timeout: 50 * time.Millisecond,
			Reader:  baseReader,
		}

		// 读取数据
		buffer := make([]byte, 100)
		_, err := timeoutReader.Read(buffer)

		// 验证超时错误
		r.Error(err)
		r.Equal(ErrTimeout, err)
	})

}


