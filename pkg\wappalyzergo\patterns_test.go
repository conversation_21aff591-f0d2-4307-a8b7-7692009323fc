//
// Author: chenjb
// Version: V1.0
// Date: 2025-07-18 11:10:30
// FilePath: /yaml_scan/pkg/wappalyzergo/patterns_test.go
// Description: patterns.go 的单元测试文件

package wappalyzergo

import (
    "testing"

    "github.com/stretchr/testify/require"
)

// TestParsePattern 测试解析指纹模式字符串
func TestParsePattern(t *testing.T) {
    t.Run("解析简单正则模式", func(t *testing.T) {
        pattern, err := ParsePattern("nginx")
        require.NoError(t, err, "解析简单模式不应返回错误")
        require.NotNil(t, pattern, "解析结果不应为nil")
        require.Equal(t, 100, pattern.Confidence, "默认置信度应为100")
        require.False(t, pattern.SkipRegex, "不应跳过正则匹配")
    })

    t.Run("解析带版本提取的模式", func(t *testing.T) {
        pattern, err := ParsePattern("nginx/([\\d.]+)")
        require.NoError(t, err, "解析版本模式不应返回错误")
        require.NotNil(t, pattern, "解析结果不应为nil")
        require.NotNil(t, pattern.regex, "正则表达式不应为nil")
    })

    t.Run("解析带置信度的模式", func(t *testing.T) {
        pattern, err := ParsePattern("nginx\\;confidence:90")
        require.NoError(t, err, "解析置信度模式不应返回错误")
        require.Equal(t, 90, pattern.Confidence, "置信度应为90")
    })

    t.Run("解析复合模式", func(t *testing.T) {
        pattern, err := ParsePattern("nginx/([\\d.]+)\\;confidence:95\\;version:\\1")
        require.NoError(t, err, "解析复合模式不应返回错误")
        require.Equal(t, 95, pattern.Confidence, "置信度应为95")
        require.NotEmpty(t, pattern.VersionRegex, "版本正则不应为空")
    })

    t.Run("解析空模式（跳过正则）", func(t *testing.T) {
        pattern, err := ParsePattern("\\;confidence:80")
        require.NoError(t, err, "解析空模式不应返回错误")
        require.True(t, pattern.SkipRegex, "应该跳过正则匹配")
        require.Equal(t, 80, pattern.Confidence, "置信度应为80")
    })

    t.Run("解析无效正则表达式", func(t *testing.T) {
        pattern, err := ParsePattern("[invalid")
        require.Error(t, err, "无效正则应返回错误")
        require.Nil(t, pattern, "错误时结果应为nil")
    })
}

// TestParsedPatternEvaluate 测试模式评估功能
func TestParsedPatternEvaluate(t *testing.T) {
    t.Run("简单字符串匹配", func(t *testing.T) {
        pattern, err := ParsePattern("nginx")
        require.NoError(t, err, "解析不应失败")

        matched, version := pattern.Evaluate("Server: nginx/1.18.0")
        require.True(t, matched, "应该匹配nginx")
        require.Empty(t, version, "简单匹配不应提取版本")
    })

    t.Run("版本提取匹配", func(t *testing.T) {
        pattern, err := ParsePattern("nginx/([\\d.]+)")
        require.NoError(t, err, "解析不应失败")

        matched, version := pattern.Evaluate("Server: nginx/1.18.0")
        require.True(t, matched, "应该匹配nginx版本")
        require.Equal(t, "1.18.0", version, "应该提取正确版本")
    })

    t.Run("不匹配的情况", func(t *testing.T) {
        pattern, err := ParsePattern("apache")
        require.NoError(t, err, "解析不应失败")

        matched, version := pattern.Evaluate("Server: nginx/1.18.0")
        require.False(t, matched, "不应匹配apache")
        require.Empty(t, version, "不匹配时版本应为空")
    })

    t.Run("跳过正则的情况", func(t *testing.T) {
        pattern, err := ParsePattern("\\;confidence:100")
        require.NoError(t, err, "解析不应失败")

        matched, version := pattern.Evaluate("任意字符串")
        require.True(t, matched, "跳过正则应该总是匹配")
        require.Empty(t, version, "跳过正则不应提取版本")
    })

    t.Run("正则为nil的情况", func(t *testing.T) {
        pattern := &ParsedPattern{
            Confidence: 100,
            SkipRegex:  false,
            regex:      nil, // 模拟正则为nil的情况
        }

        matched, version := pattern.Evaluate("测试字符串")
        require.False(t, matched, "正则为nil应该不匹配")
        require.Empty(t, version, "正则为nil版本应为空")
    })

    t.Run("复杂版本提取", func(t *testing.T) {
        pattern, err := ParsePattern("WordPress ([\\d.]+)")
        require.NoError(t, err, "解析不应失败")

        matched, version := pattern.Evaluate("WordPress 5.8.2")
        require.True(t, matched, "应该匹配WordPress")
        require.Equal(t, "5.8.2", version, "应该提取正确版本")
    })

    t.Run("多组捕获的版本提取", func(t *testing.T) {
        pattern, err := ParsePattern("jQuery ([\\d.]+)")
        require.NoError(t, err, "解析不应失败")

        matched, version := pattern.Evaluate("jQuery 3.6.0")
        require.True(t, matched, "应该匹配jQuery")
        require.Equal(t, "3.6.0", version, "应该提取正确版本")
    })
}

// TestVersionRegexConstants 测试版本正则常量
func TestVersionRegexConstants(t *testing.T) {
    t.Run("verCap1常量测试", func(t *testing.T) {
        require.NotEmpty(t, verCap1, "verCap1不应为空")
        require.Contains(t, verCap1, "\\d", "verCap1应包含数字匹配")
    })

    t.Run("verCap2常量测试", func(t *testing.T) {
        require.NotEmpty(t, verCap2, "verCap2不应为空")
        require.Contains(t, verCap2, "\\d", "verCap2应包含数字匹配")
    })

    t.Run("verCap2Limited常量测试", func(t *testing.T) {
        require.NotEmpty(t, verCap2Limited, "verCap2Limited不应为空")
        require.Contains(t, verCap2Limited, "{1,20}", "verCap2Limited应包含长度限制")
    })
}

// TestPatternEdgeCases 测试模式解析的边界情况
func TestPatternEdgeCases(t *testing.T) {
    t.Run("空字符串模式", func(t *testing.T) {
        pattern, err := ParsePattern("")
        require.NoError(t, err, "空字符串应该可以解析")
        require.True(t, pattern.SkipRegex, "空字符串应该跳过正则")
    })

    t.Run("只有分隔符的模式", func(t *testing.T) {
        pattern, err := ParsePattern("\\;")
        require.NoError(t, err, "只有分隔符应该可以解析")
        require.True(t, pattern.SkipRegex, "只有分隔符应该跳过正则")
    })

    t.Run("多个分隔符的模式", func(t *testing.T) {
        pattern, err := ParsePattern("test\\;confidence:50\\;version:\\1\\;extra:value")
        require.NoError(t, err, "多个分隔符应该可以解析")
        require.Equal(t, 50, pattern.Confidence, "应该正确解析置信度")
    })

    t.Run("无效置信度值", func(t *testing.T) {
        pattern, err := ParsePattern("test\\;confidence:invalid")
        require.NoError(t, err, "无效置信度应该使用默认值")
        require.Equal(t, 100, pattern.Confidence, "无效置信度应该使用默认值100")
    })

    t.Run("超出范围的置信度", func(t *testing.T) {
        pattern, err := ParsePattern("test\\;confidence:150")
        require.NoError(t, err, "超出范围的置信度应该可以解析")
        require.Equal(t, 150, pattern.Confidence, "应该保留原始置信度值")
    })
}