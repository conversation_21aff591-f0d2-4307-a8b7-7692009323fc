{"info": {"_postman_id": "20a3fd41-6a86-4e49-8860-f796559d0223", "name": "advancedsearch", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "List Projects, Assets and Hosts", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/v1/search/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "v1", "search", ""]}}, "response": []}, {"name": "List Assets and Hosts", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/v1/search/?projectId=1,2", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "v1", "search", ""], "query": [{"key": "projectId", "value": "1,2"}]}}, "response": []}, {"name": "List Hosts", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/v1/search/?projectId=1,2&assetId=1,2", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "v1", "search", ""], "query": [{"key": "projectId", "value": "1,2"}, {"key": "assetId", "value": "1,2"}]}}, "response": []}, {"name": "Search Request", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"query\": \"query\",\n\t\"projectId\": [4,3,4],\n\t\"assetId\": [2,3,4],\n\t\"hostId\": [1,2,3],\n    \"limit\": 10,\n    \"offset\": 10\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://127.0.0.1:8000/api/v1/search/", "protocol": "http", "host": ["127", "0", "0", "1"], "port": "8000", "path": ["api", "v1", "search", ""]}}, "response": []}], "protocolProfileBehavior": {}}