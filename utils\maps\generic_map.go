//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-21 11:19:29
//FilePath: /yaml_scan/utils/maps/generic_map.go
//Description:

package mapsutil

import "golang.org/x/exp/maps"

// Map wraps a generic map type
type Map[K, V comparable] map[K]V

// IsEmpty checks if the current map is empty
func (m Map[K, V]) IsEmpty() bool {
	return len(m) == 0
}

// Clear the map
func (m Map[K, V]) Clear() bool {
	maps.Clear(m)
	return m.IsEmpty()
}
