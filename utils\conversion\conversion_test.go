//
//Author: chenjb
//Version: V1.0
//Date: 2024-11-11 17:39:30
//FilePath: /yaml_scan/utils/conversion/conversion_test.go
//Description: 单测

package conversion



import (
	"testing"
)

// TestBytes 测试 Bytes 函数
func TestBytes(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected []byte
	}{
		{
			name:     "普通字符串",
			input:    "hello",
			expected: []byte("hello"),
		},
		{
			name:     "空字符串",
			input:    "",
			expected: []byte(""),
		},
		{
			name:     "包含特殊字符",
			input:    "你好",
			expected: []byte("你好"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := Bytes(tt.input)
			if string(result) != string(tt.expected) {
				t.<PERSON>rrorf("expected: %v, got: %v", tt.expected, result)
			}
		})
	}
}

// TestString 测试 String 函数
func TestString(t *testing.T) {
	tests := []struct {
		name     string
		input    []byte
		expected string
	}{
		{
			name:     "普通字节切片",
			input:    []byte("hello"),
			expected: "hello",
		},
		{
			name:     "空字节切片",
			input:    []byte(""),
			expected: "",
		},
		{
			name:     "包含特殊字符",
			input:    []byte("你好"),
			expected: "你好",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := String(tt.input)
			if result != tt.expected {
				t.Errorf("expected: %v, got: %v", tt.expected, result)
			}
		})
	}
}