// 
// Author: chenjb
// Version: V1.0
// Date: 2025-07-01 11:31:58
// FilePath: /yaml_scan/pkg/tlsx/tlsx/openssl/util_test.go
// Description: 
package openssl

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	errorutil "yaml_scan/utils/errors"
)

// TestAllCiphersNames 测试全局密码套件名称列表
// 验证AllCiphersNames的初始化和内容
func TestAllCiphersNames(t *testing.T) {
	if !IsAvailable() {
		t.Skip("OpenSSL不可用，跳过AllCiphersNames测试")
	}

	t.Run("全局变量初始化验证", func(t *testing.T) {
		require.NotNil(t, AllCiphersNames, "AllCiphersNames不应该为nil")
		
		if len(AllCiphersNames) > 0 {
			require.Greater(t, len(AllCiphersNames), 10, "应该有足够数量的密码套件")
			
			// 验证密码套件名称格式
			for i, cipher := range AllCiphersNames {
				require.NotEmpty(t, cipher, "密码套件[%d]不应该为空", i)
				if i < 5 { // 只记录前几个
					t.Logf("密码套件[%d]: %s", i, cipher)
				}
			}
			
			t.Logf("AllCiphersNames包含 %d 个密码套件", len(AllCiphersNames))
		} else {
			t.Log("AllCiphersNames为空，可能是OpenSSL初始化失败")
		}
	})

	t.Run("密码套件名称唯一性", func(t *testing.T) {
		if len(AllCiphersNames) == 0 {
			t.Skip("AllCiphersNames为空，跳过唯一性测试")
		}

		cipherSet := make(map[string]bool)
		duplicates := []string{}

		for _, cipher := range AllCiphersNames {
			if cipherSet[cipher] {
				duplicates = append(duplicates, cipher)
			} else {
				cipherSet[cipher] = true
			}
		}

		if len(duplicates) > 0 {
			t.Logf("发现重复的密码套件: %v", duplicates)
		}
		require.Empty(t, duplicates, "密码套件名称应该唯一")
	})
}

// TestCipherMap 测试密码套件映射表
// 验证cipherMap的初始化和一致性
func TestCipherMap(t *testing.T) {
	if !IsAvailable() {
		t.Skip("OpenSSL不可用，跳过cipherMap测试")
	}

	t.Run("映射表与名称列表一致性", func(t *testing.T) {
		if len(AllCiphersNames) == 0 {
			t.Skip("AllCiphersNames为空，跳过一致性测试")
		}

		// 验证AllCiphersNames中的每个密码套件都在cipherMap中
		for _, cipher := range AllCiphersNames {
			_, exists := cipherMap[cipher]
			require.True(t, exists, "密码套件'%s'应该在cipherMap中", cipher)
		}

		// 验证cipherMap的大小与AllCiphersNames一致
		require.Equal(t, len(AllCiphersNames), len(cipherMap), 
			"cipherMap大小应该与AllCiphersNames一致")

		t.Logf("cipherMap包含 %d 个条目", len(cipherMap))
	})

	t.Run("映射表快速查找性能", func(t *testing.T) {
		if len(AllCiphersNames) == 0 {
			t.Skip("AllCiphersNames为空，跳过性能测试")
		}

		// 测试查找性能
		testCipher := AllCiphersNames[0]
		const iterations = 10000

		start := time.Now()
		for i := 0; i < iterations; i++ {
			_, exists := cipherMap[testCipher]
			require.True(t, exists)
		}
		duration := time.Since(start)

		avgDuration := duration / iterations
		t.Logf("cipherMap查找性能: %d次查找，总耗时: %v，平均耗时: %v",
			iterations, duration, avgDuration)

		// 映射表查找应该非常快
		require.Less(t, avgDuration, time.Microsecond, "映射表查找应该非常快")
	})
}

// TestToOpenSSLCiphers 测试toOpenSSLCiphers函数
// 验证密码套件验证功能
func TestToOpenSSLCiphers(t *testing.T) {
	if !IsAvailable() {
		t.Skip("OpenSSL不可用，跳过toOpenSSLCiphers测试")
	}

	t.Run("验证有效密码套件", func(t *testing.T) {
		if len(AllCiphersNames) == 0 {
			t.Skip("AllCiphersNames为空，跳过有效密码套件测试")
		}

		// 使用前几个已知的有效密码套件
		validCiphers := AllCiphersNames[:min(3, len(AllCiphersNames))]
		
		result, err := toOpenSSLCiphers(validCiphers...)
		require.NoError(t, err, "验证有效密码套件应该成功")
		require.Equal(t, len(validCiphers), len(result), "结果长度应该与输入一致")
		require.Equal(t, validCiphers, result, "结果应该与输入相同")

		t.Logf("成功验证了 %d 个密码套件", len(result))
	})

	t.Run("验证无效密码套件", func(t *testing.T) {
		invalidCiphers := []string{
			"INVALID_CIPHER_SUITE_1",
			"NONEXISTENT_CIPHER_2",
			"FAKE_CIPHER_3",
		}

		result, err := toOpenSSLCiphers(invalidCiphers...)
		require.Error(t, err, "验证无效密码套件应该失败")
		require.Contains(t, err.Error(), "not supported", "错误信息应该包含'not supported'")
		require.Contains(t, err.Error(), "openssl", "错误信息应该包含'openssl'标签")
		require.Empty(t, result, "无效密码套件验证失败时结果应该为空")
	})

	t.Run("验证混合有效无效密码套件", func(t *testing.T) {
		if len(AllCiphersNames) == 0 {
			t.Skip("AllCiphersNames为空，跳过混合测试")
		}

		mixedCiphers := []string{
			AllCiphersNames[0], // 有效
			"INVALID_CIPHER",   // 无效
		}

		result, err := toOpenSSLCiphers(mixedCiphers...)
		require.Error(t, err, "包含无效密码套件应该失败")
		require.Len(t, result, 1, "应该返回验证成功的密码套件")
		require.Equal(t, AllCiphersNames[0], result[0], "应该包含有效的密码套件")
	})

	t.Run("验证空密码套件列表", func(t *testing.T) {
		result, err := toOpenSSLCiphers()
		require.NoError(t, err, "验证空列表应该成功")
		require.Empty(t, result, "空列表验证结果应该为空")
	})

	t.Run("验证重复密码套件", func(t *testing.T) {
		if len(AllCiphersNames) == 0 {
			t.Skip("AllCiphersNames为空，跳过重复测试")
		}

		duplicateCiphers := []string{
			AllCiphersNames[0],
			AllCiphersNames[0], // 重复
		}

		result, err := toOpenSSLCiphers(duplicateCiphers...)
		require.NoError(t, err, "验证重复密码套件应该成功")
		require.Len(t, result, 2, "应该保留重复的密码套件")
		require.Equal(t, duplicateCiphers, result, "结果应该包含重复项")
	})
}

// TestParseSessionValue 测试parseSessionValue函数
// 验证会话值解析功能
func TestParseSessionValue(t *testing.T) {
	t.Run("解析标准格式", func(t *testing.T) {
		testCases := []struct {
			name     string
			input    string
			expected string
		}{
			{
				name:     "协议版本",
				input:    "    Protocol  : TLSv1.2",
				expected: "TLSv1.2",
			},
			{
				name:     "密码套件",
				input:    "    Cipher    : ECDHE-RSA-AES256-GCM-SHA384",
				expected: "ECDHE-RSA-AES256-GCM-SHA384",
			},
			{
				name:     "会话ID",
				input:    "    Session-ID: 1234567890ABCDEF",
				expected: "1234567890ABCDEF",
			},
			{
				name:     "主密钥",
				input:    "    Master-Key: FEDCBA0987654321",
				expected: "FEDCBA0987654321",
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				result := parseSessionValue(tc.input)
				require.Equal(t, tc.expected, result, "解析结果应该匹配")
			})
		}
	})
}

// TestWrap 测试Wrap函数
// 验证错误包装功能
func TestWrap(t *testing.T) {
	t.Run("包装两个非nil错误", func(t *testing.T) {
		err1 := errorutil.NewWithTag("test", "first error")
		err2 := errorutil.NewWithTag("test", "second error")

		result := Wrap(err1, err2)
		require.NotNil(t, result, "包装结果不应该为nil")

		// 验证包装后的错误包含两个错误的信息
		errStr := result.Error()
		require.Contains(t, errStr, "first error", "应该包含第一个错误信息")
		require.Contains(t, errStr, "second error", "应该包含第二个错误信息")
	})

	t.Run("第一个错误为nil", func(t *testing.T) {
		err2 := errorutil.NewWithTag("test", "second error")

		result := Wrap(nil, err2)
		require.NotNil(t, result, "包装结果不应该为nil")
		require.Equal(t, err2, result, "应该直接返回第二个错误")
	})

	t.Run("第二个错误为nil", func(t *testing.T) {
		err1 := errorutil.NewWithTag("test", "first error")

		result := Wrap(err1, nil)
		require.NotNil(t, result, "包装结果不应该为nil")
		// 当err2为nil时，应该返回包装后的结果
		errStr := result.Error()
		require.Contains(t, errStr, "first error", "应该包含第一个错误信息")
	})

	t.Run("两个错误都为nil", func(t *testing.T) {
		result := Wrap(nil, nil)
		require.Nil(t, result, "两个错误都为nil时结果应该为nil")
	})
}

// TestIsClientCertRequired 测试isClientCertRequired函数
// 验证客户端证书要求检测功能
func TestIsClientCertRequired(t *testing.T) {
	t.Run("检测SSL alert number 42", func(t *testing.T) {
		opensslOutput := `
CONNECTED(00000003)
depth=0 CN = example.com
verify error:num=18:self signed certificate
SSL alert number 42
verify return:1
`

		result := isClientCertRequired(opensslOutput)
		require.True(t, result, "应该检测到SSL alert number 42")
	})

	t.Run("检测SSL alert number 116", func(t *testing.T) {
		opensslOutput := `
CONNECTED(00000003)
SSL alert number 116
Connection closed
`

		result := isClientCertRequired(opensslOutput)
		require.True(t, result, "应该检测到SSL alert number 116")
	})

	t.Run("检测多个警告", func(t *testing.T) {
		opensslOutput := `
CONNECTED(00000003)
SSL alert number 42
Some other output
SSL alert number 116
Connection closed
`

		result := isClientCertRequired(opensslOutput)
		require.True(t, result, "应该检测到任一相关警告")
	})

	t.Run("无相关警告", func(t *testing.T) {
		opensslOutput := `
CONNECTED(00000003)
depth=0 CN = example.com
verify error:num=18:self signed certificate
verify return:1
SSL-Session:
    Protocol  : TLSv1.2
    Cipher    : ECDHE-RSA-AES256-GCM-SHA384
`

		result := isClientCertRequired(opensslOutput)
		require.False(t, result, "不应该检测到客户端证书要求")
	})

	t.Run("其他SSL警告", func(t *testing.T) {
		opensslOutput := `
CONNECTED(00000003)
SSL alert number 40
SSL alert number 50
Connection closed
`

		result := isClientCertRequired(opensslOutput)
		require.False(t, result, "其他SSL警告不应该被识别为客户端证书要求")
	})

	t.Run("空输出", func(t *testing.T) {
		result := isClientCertRequired("")
		require.False(t, result, "空输出不应该检测到客户端证书要求")
	})

	t.Run("部分匹配", func(t *testing.T) {
		opensslOutput := `
CONNECTED(00000003)
SSL alert number 4
SSL alert number 1
Connection closed
`

		result := isClientCertRequired(opensslOutput)
		require.False(t, result, "部分匹配不应该被识别")
	})
}


// min 辅助函数，返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}


