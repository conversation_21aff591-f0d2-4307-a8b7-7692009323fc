// Author: chenjb
// Version: V1.0
// Date: 2025-06-18 11:40:08
// FilePath: /yaml_scan/internal/runner/inputs.go
// Description:
package runner

import (
	"context"
	"fmt"
	"sync/atomic"
	"time"
	"yaml_scan/pkg/gologger"
	"yaml_scan/pkg/hybridMap/hybrid"
	"yaml_scan/pkg/input/provider"
	"yaml_scan/pkg/protocols/common/contextargs"
	"yaml_scan/pkg/protocols/common/protocolstate"
	stringsutil "yaml_scan/utils/strings"
	syncutil "yaml_scan/utils/sync"
)

// initializeTemplatesHTTPInput initializes the http form of input
// for any loaded http templates if input is in non-standard format.
func (r *Runner) initializeTemplatesHTTPInput() (*hybrid.HybridMap, error) {
	hm, err := hybrid.New(hybrid.DefaultDiskOptions)
	if err != nil {
		return nil, fmt.Errorf("could not create temporary input file %s", err)
	}
	if r.inputProvider.InputType() == provider.MultiFormatInputProvider {
		// currently http probing for input mode types is not supported
		return hm, nil
	}
	gologger.Info().Msgf("Running httpx on input host")

	httpxOptions := httpx.DefaultOptions
	if r.options.AliveHttpProxy != "" {
		httpxOptions.Proxy = r.options.AliveHttpProxy
	} else if r.options.AliveSocksProxy != "" {
		httpxOptions.Proxy = r.options.AliveSocksProxy
	}
	httpxOptions.RetryMax = r.options.Retries
	httpxOptions.Timeout = time.Duration(r.options.Timeout) * time.Second
	httpxOptions.NetworkPolicy = protocolstate.NetworkPolicy
	httpxClient, err := httpx.New(&httpxOptions)
	if err != nil {
		return nil, fmt.Errorf("could not create httpx client, %s", err)
	}

	// Probe the non-standard URLs and store them in cache
	swg, err := syncutil.New(syncutil.WithSize(r.options.BulkSize))
	if err != nil {
		return nil, fmt.Errorf("could not create adaptive group", err)
	}
	var count atomic.Int32
	r.inputProvider.Iterate(func(value *contextargs.MetaInput) bool {
		if stringsutil.HasPrefixAny(value.Input, "http://", "https://") {
			return true
		}

		if r.options.ProbeConcurrency > 0 && swg.Size != r.options.ProbeConcurrency {
			if err := swg.Resize(context.Background(), r.options.ProbeConcurrency); err != nil {
				gologger.Error().Msgf("Could not resize workpool: %s\n", err)
			}
		}

		swg.Add()
		go func(input *contextargs.MetaInput) {
			defer swg.Done()

			if result := utils.ProbeURL(input.Input, httpxClient); result != "" {
				count.Add(1)
				_ = hm.Set(input.Input, []byte(result))
			}
		}(value)
		return true
	})
	swg.Wait()

	gologger.Info().Msgf("Found %d URL from httpx", count.Load())
	return hm, nil
}
