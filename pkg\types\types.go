package types

import (
	"time"

	"yaml_scan/pkg/goflags"
	"yaml_scan/pkg/model/types/severity"
	"yaml_scan/pkg/templates/types"
	"yaml_scan/pkg/config"
	unitutils "yaml_scan/utils/unit"
)


// Options 包含扫描器的所有配置项
type Options struct {
	// Tags 包含要执行模板的标签列表。可以使用 -l 标志指定多个路径，并且可以与 -tags 组合使用
	Tags goflags.StringSlice
	// ExcludeTags 要排除的标签列表
	ExcludeTags goflags.StringSlice
	// Workflows 指定工作流。
	Workflows goflags.StringSlice
	// WorkflowURLs 指定要使用的工作流的 URL 列表
	WorkflowURLs goflags.StringSlice
	// Templates 指定要使用的模板/模板列表。
	Templates goflags.StringSlice
	// TemplateURLs 指定要使用的模板的 URL 列表。
	TemplateURLs goflags.StringSlice
	// RemoteTemplates 允许从中加载远程模板的 URL 列表。
	RemoteTemplateDomainList goflags.StringSlice
	// 	ExcludedTemplates  指定要排除的模板/模板列表。
	ExcludedTemplates goflags.StringSlice
	// ExcludeMatchers 要排除处理的匹配器列表。
	ExcludeMatchers goflags.StringSlice
	// CustomHeaders 自定义全局headers列表。
	CustomHeaders goflags.StringSlice
	// Vars 自定义全局变量的列表。
	Vars goflags.RuntimeMap
	// Severities 根据严重性过滤模板，仅运行匹配的模板。
	Severities severity.Severities
	// ExcludeSeverities 指定要排除的严重性。
	ExcludeSeverities severity.Severities
	// Authors 根据作者过滤模板，仅运行匹配的模板。
	Authors goflags.StringSlice
	// Protocols 允许执行的协议列表。
	Protocols types.ProtocolTypes
	// ExcludeProtocols 不执行的协议列表。
	ExcludeProtocols types.ProtocolTypes
	// IncludeTags 包含指定标签，即使在拒绝列表中也要运行。
	IncludeTags goflags.StringSlice
	// IncludeTemplates 包含指定模板，即使在拒绝列表中也要运行。
	IncludeTemplates goflags.StringSlice
	// IncludeIds includes specified ids to be run even while being in denylist
	IncludeIds goflags.StringSlice
	// ExcludeIds 不执行的模板 ID 列表。
	ExcludeIds goflags.StringSlice
	// InternalResolversList 从解析器标志和提供的文件中规范化的解析器列表。
	InternalResolversList []string 
	// ProjectPath 允许 使用用户定义的项目文件夹。
	ProjectPath string
	// InteractshURL Interactsh 服务器的 URL
	InteractshURL string
	// Interactsh 自托管服务器的 Interactsh 授权头部值。
	InteractshToken string
	// Target 使用模板扫描的目标 URL/域名
	Targets goflags.StringSlice
	// ExcludeTargets 要排除的扫描目标 URL/域名。
	ExcludeTargets goflags.StringSlice
	// TargetsFilePath 指定要扫描的目标文件路径。
	TargetsFilePath string
	// Resume 从存储在恢复配置文件中的状态恢复扫描。
	Resume string
	// Output 写入找到的结果的文件。
	Output string
	// ProxyInternal 是否使用内部代理请求。
	ProxyInternal bool
	// 显示所有支持的 DSL 签名。
	ListDslSignatures bool
	// 要使用的 HTTP(S)/SOCKS5 代理列表（以逗号分隔或文件输入）。
	Proxy goflags.StringSlice
	// TemplatesDirectory 用于存储模板的目录。
	NewTemplatesDirectory string
	// TraceLogFile 指定写入所有请求跟踪的文件。
	TraceLogFile string
	// ErrorLogFile 指定写入所有请求错误的文件。
	ErrorLogFile string
	// ReportingDB 报告存储和去重的数据库。
	ReportingDB string
	// ReportingConfig 报告模块的配置文件
	ReportingConfig string
	// MarkdownExportDirectory 导出 Markdown 格式报告的目录。
	MarkdownExportDirectory string
	// MarkdownExportSortMode 报告的排序方法（选项：严重性、模板、主机、无）。
	MarkdownExportSortMode string
	// SarifExport 导出 SARIF 输出格式的文件。
	SarifExport string
	// ResolversFile 包含解析器的文件。
	ResolversFile string
	// StatsInterval 显示统计信息的间隔（秒）
	StatsInterval int
	// MetricsPort 显示指标的端口
	MetricsPort int
	// HTTPStats 启用HTTP统计信息跟踪和显示。
	HTTPStats bool
	// MaxHostError 每个主机允许的最大错误数
	MaxHostError int
	// TrackError 额外的错误消息，计入每个主机允许的最大错误数。
	TrackError goflags.StringSlice
	// NoHostErrors 超过最大错误数后禁用主机跳过。
	NoHostErrors bool
	// BulkSize 每个模板并行分析的目标数量。
	BulkSize int
	// TemplateThreads 并行执行的模板数量。
	TemplateThreads int
	// HeadlessBulkSize 每个无头模板并行分析的目标数量
	HeadlessBulkSize int
	// HeadlessTemplateThreads 并行执行的无头模板数量。
	HeadlessTemplateThreads int
	// Timeout 等待服务器响应的秒数。
	Timeout int
	// Retries 请求的重试次数。
	Retries int
	// Rate-Limit 每个指定目标的最大请求数。
	RateLimit int
	// Rate-Limit 每分钟的最大请求数。
	RateLimitMinute int
	// PageTimeout 等待页面的最大时间（秒）。
	PageTimeout int
	// InteractionsCacheSize 缓存中保持的交互 URL->请求 的数量。
	InteractionsCacheSize int
	// InteractionsPollDuration 每次交互轮询之前等待的秒数。
	InteractionsPollDuration int
	// 自动丢弃交互请求的秒数。
	InteractionsEviction int
	// 关闭轮询器后等待交互的额外秒数。
	InteractionsCoolDownPeriod int
	// MaxRedirects 要跟随的最大重定向次数。
	MaxRedirects int
	// FollowRedirects 启用 HTTP 请求模块的重定向跟随。
	FollowRedirects bool
	// FollowRedirects 仅在同一主机上启用 HTTP 请求模块的重定向跟随。
	FollowHostRedirects bool
	// 指定离线处理 HTTP 响应的标志，使用相同的匹配器/提取器，而无需发送新请求，从文件中读取响应。
	OfflineHTTP bool
	// 强制尝试 HTTP/2 请求。
	ForceAttemptHTTP2 bool
	// 以 JSON 格式写入统计输出。
	StatsJSON bool
	// Headless 指定是否允许无头模式模板。
	Headless bool
	// ShowBrowser 指定是否在无头模式下显示浏览器。
	ShowBrowser bool
	// HeadlessOptionalArguments 要传递给 Chrome 的可选参数
	HeadlessOptionalArguments goflags.StringSlice
	// DisableClustering 禁用模板的聚类。
	DisableClustering bool
	// UseInstalledChrome 跳过 Chrome 安装并使用本地实例。
	UseInstalledChrome bool
	// SystemResolvers 启用覆盖  DNS 客户端，选择使用系统解析器堆栈。
	SystemResolvers bool
	// ShowActions 显示所有无头操作的列表。
	ShowActions bool
	// 通过 HTTP 端点显示指标的功能，默认启用。
	Metrics bool
	// Debug 启用请求/响应调试模式。
	Debug bool
	// DebugRequests 启用请求调试模式。
	DebugRequests bool
	// DebugResponse 启用响应调试模式。
	DebugResponse bool
	// DisableHTTPProbe 禁用输入规范化的 HTTP 探测功能。
	DisableHTTPProbe bool
	// LeaveDefaultPorts 跳过默认端口的规范化。
	LeaveDefaultPorts bool
	// AutomaticScan 启用基于Wappalyzer技术的标签映射自动扫描
	AutomaticScan bool
	// Silent 抑制任何额外文本，仅在屏幕上写入找到的 URL。
	Silent bool
	// Validate 验证传递给引擎的模板。
	Validate bool
	// NoStrictSyntax 禁用模板的严格语法检查（允许自定义键值对）。
	NoStrictSyntax bool
	// Verbose 指示是否显示详细输出的标志。
	Verbose        bool
	// 指示是否显示更详细的输出。
	VerboseVerbose bool
	// ShowVarDump 显示变量转储。
	ShowVarDump bool
	// No-Color 禁用彩色输出。
	NoColor bool
	// UpdateTemplates 更新启动时安装的模板（云端也用于更新数据源）。
	UpdateTemplates bool
	// JSON 将 JSON 行输出写入文件。
	JSONL bool
	// J写入匹配的请求/响应的 JSON 输出（已弃用：现在 JSONRequests（包括原始请求）始终为真）。
	JSONRequests bool
	// OmitRawRequests 在 JSON 输出中省略匹配的请求/响应。
	OmitRawRequests bool
	// OmitTemplate 在 JSON 输出中省略编码的模板
	OmitTemplate bool
	// JSONExport 导出 JSON 输出格式的文件。
	JSONExport string
	// JSONLExport 导出 JSONL 输出格式的文件。
	JSONLExport string
	// EnableProgressBar 启用进度条。
	EnableProgressBar bool
	// TemplateDisplay 显示模板内容。
	TemplateDisplay bool
	// TemplateList 列出可用模板。
	TemplateList bool
	// TagList 列出所有可用的标签
	TagList bool
	// HangMonitor 启用挂起监控
	HangMonitor bool
	// Stdin 指示是否向进程提供了标准输入。
	Stdin bool
	// StopAtFirstMatch 在第一个完整匹配处停止处理模板（这可能会破坏链式请求）。
	StopAtFirstMatch bool
	// Stream 无需排序地流式输入
	Stream bool
	// NoMeta 禁用匹配的元数据的显示。
	NoMeta bool
	// Timestamp 启用匹配器的时间戳显示。
	Timestamp bool
	// Project 用于避免多次发送相同的 HTTP 请求。
	Project bool
	// NewTemplates 仅运行从存储库中新增的模板。
	NewTemplates bool
	// NewTemplatesWithVersion  运行特定版本中新增的模板。
	NewTemplatesWithVersion goflags.StringSlice
	// NoInteractsh 禁用使用 Interactsh 服务器进行交互轮询。
	NoInteractsh bool
	// EnvironmentVariables 启用对环境变量的支持。
	EnvironmentVariables bool
	// MatcherStatus 显示失败匹配的可选状态。
	MatcherStatus bool
	// ClientCertFile 用于对扫描主机进行身份验证的客户端证书文件（PEM 编码）。
	ClientCertFile string
	// ClientKeyFile 用于对扫描主机进行身份验证的客户端密钥文件（PEM 编码）。
	ClientKeyFile string
	// ClientCAFile 用于对扫描主机进行身份验证的客户端证书颁发机构文件（PEM 编码）。
	ClientCAFile string
	// 已弃用：使用 ZTLS 库。
	ZTLS bool
	// AllowLocalFileAccess  允许从模板有效负载访问本地文件。
	AllowLocalFileAccess bool
	// RestrictLocalNetworkAccess 限制模板请求的本地网络访问。
	RestrictLocalNetworkAccess bool
	// ShowMatchLine 启用匹配行号的显示。
	ShowMatchLine bool
	// EnablePprof 启用通过 Web 服务器公开 pprof 运行时信息。
	EnablePprof bool
	// StoreResponse 将接收到的响应存储到输出目录。
	StoreResponse bool
	// StoreResponseDir 将接收到的响应存储到自定义目录。
	StoreResponseDir string
	// DisableRedirects  禁用 HTTP 请求模块的重定向跟随。
	DisableRedirects bool
	// SNI  自定义主机名。
	SNI string
	// DialerTimeout 设置网络请求的超时时间。
	DialerTimeout time.Duration
	// DialerKeepAlive 设置网络请求的保持活动时间。
	DialerKeepAlive time.Duration
	// Interface 用于网络扫描的接口。
	Interface string
	// SourceIP  设置网络请求的自定义源 IP 地址
	SourceIP string
	// AttackType 覆盖模板级别的攻击类型配置。
	AttackType string
	// ResponseReadSize  要读取的响应的最大大小。
	ResponseReadSize int
	// ResponseSaveSize 要保存的响应的最大大小。
	ResponseSaveSize int
	// Health  健康检查
	HealthCheck bool
	// 在关闭流之前等待每次输入读取操作的时间。
	InputReadTimeout time.Duration
	// 禁用标准输入进行输入处理。
	DisableStdin bool
	// IncludeConditions  模板应匹配的条件列表。
	IncludeConditions goflags.StringSlice
	// 启用 Uncover 引擎
	Uncover bool
	// Uncover 搜索查询
	UncoverQuery goflags.StringSlice
	// Uncover 搜索引擎
	UncoverEngine goflags.StringSlice
	// Uncover 搜索字段
	UncoverField string
	// Uncover 搜索限制
	UncoverLimit int
	// Uncover 搜索延迟
	UncoverRateLimit int
	// 扫描与 DNS 记录关联的所有 IP
	ScanAllIPs bool
	// IPVersion 要扫描的 IP 版本（4,6)
	IPVersion goflags.StringSlice
	// PublicTemplateDisableDownload 禁用从模板公共存储库下载模板。
	PublicTemplateDisableDownload bool
	// GitHub 用于从私有存储库克隆/拉取自定义模板的 GitHub 令牌。
	GitHubToken string
	// GitHubTemplateRepo 自定义公共/私有模板 GitHub 存储库的列表。
	GitHubTemplateRepo []string
	// GitHubTemplateDisableDownload 禁用从自定义 GitHub 存储库下载模板。
	GitHubTemplateDisableDownload bool
	// GitLabServerURL 用于自定义模板的 GitLab 服务器。
	GitLabServerURL string
	// GitLabToken 用于从私有存储库克隆/拉取自定义模板的 GitLab 令牌。
	GitLabToken string
	// GitLabTemplateRepositoryIDs 自定义 GitLab 存储库 ID 的逗号分隔列表。
	GitLabTemplateRepositoryIDs []int
	// GitLabTemplateDisableDownload  禁用从自定义 GitLab 存储库下载模板。
	GitLabTemplateDisableDownload bool
	// AWS 用于从 S3 存储桶下载模板的 AWS 访问密钥。
	AwsAccessKey string
	// AWS  用于从 S3 存储桶下载模板的 AWS 秘密密钥。
	AwsSecretKey string
	// AWS 用于从 S3 存储桶下载模板的 AWS 存储桶名称。
	AwsBucketName string
	// AWS S3 存储桶所在的区域名称。
	AwsRegion string
	// AwsTemplateDisableDownload 禁用从 AWS S3 存储桶下载模板。
	AwsTemplateDisableDownload bool
	// AzureContainerName 从 Azure Blob 存储下载模板的容器名称。例如：templates。
	AzureContainerName string
	// 从 Azure Blob 存储下载模板的租户 ID。例如：00000000-0000-0000-0000-000000000000。
	AzureTenantID string
	// AzureClientID for downloading templates from Azure Blob Storage. Example: 00000000-0000-0000-0000-000000000000
	AzureClientID string
	// AzureClientSecret for downloading templates from Azure Blob Storage. Example: 00000000-0000-0000-0000-000000000000
	AzureClientSecret string
	// AzureServiceURL for downloading templates from Azure Blob Storage. Example: https://XXXXXXXXXX.blob.core.windows.net/
	AzureServiceURL string
	// AzureTemplateDisableDownload disables downloading templates from Azure Blob Storage
	AzureTemplateDisableDownload bool
	// Scan Strategy  扫描策略（auto, hosts-spray, templates-spray）。
	ScanStrategy string
	// Fuzzing  覆盖模板级别的模糊类型配置。
	FuzzingType string
	// Fuzzing 覆盖模板级别的模糊模式配置
	FuzzingMode string
	// TlsImpersonate 启用 TLS 冒充。
	TlsImpersonate bool
	// CodeTemplateSignaturePublicKey 用于验证模板签名的自定义公钥（算法根据长度自动推断）。
	CodeTemplateSignaturePublicKey string
	// CodeTemplateSignatureAlgorithm  指定签名算法（rsa, ecdsa）。
	CodeTemplateSignatureAlgorithm string
	// SignTemplates 启用模板签名
	SignTemplates bool
	// EnableCodeTemplates 启用代码模板
	EnableCodeTemplates bool
	// Disables 禁用云上传
	EnableCloudUpload bool
	// ScanID用于云上传的扫描 ID。
	ScanID string
	// JsConcurrency 要运行的并发 JS协程的数量
	JsConcurrency int
	// InputFileMode 指定输入文件的模式（如 jsonl、burp、openapi、swagger 等）
	InputFileMode string
	// FormatUseRequiredOnly 仅在生成请求时使用必填字段
	FormatUseRequiredOnly bool
	// SkipFormatValidation 用于跳过格式验证
	SkipFormatValidation bool
	// DisableUnsignedTemplates 禁用运行未签名模板或签名不匹配的模板
	DisableUnsignedTemplates bool
	// Redact 编辑给定的key
	Redact goflags.StringSlice
	// ListTemplateProfiles 列出所有可用的模板配置文件
	ListTemplateProfiles bool
	// HttpApiEndpoint 是实验性的http api端点
	HttpApiEndpoint string
	// Dast 只运行DAST模板
	DAST bool
	// DASTServer  DAST server标志
	DASTServer bool
	// DASTReport 启用dast server报告和最终的报表生成
	DASTReport bool
	// DASTServerToken is the token optional for the dast server
	DASTServerToken string
	// DASTServerAddress is the address for the dast server
	DASTServerAddress string
	// DisplayFuzzPoints 允许显示fuzz的模糊点
	DisplayFuzzPoints bool
	// FuzzParamFrequency fuzz参数的频率
	FuzzParamFrequency int
	// FuzzAggressionLevel fuzz攻击的级别（低、中、高）。
	FuzzAggressionLevel string
	// Scope 包含范围内url的正则表达式列表
	Scope goflags.StringSlice
	// OutOfScope 包含范围外url的正则表达式列表
	OutOfScope goflags.StringSlice
	// Rate Limit Duration 时间间隔
	RateLimitDuration time.Duration
	// PayloadConcurrency 每个模板要并发运行的payload数量
	PayloadConcurrency int
	// ProbeConcurrency  是与 httpx 一起运行的并发 HTTP 探测的数量。
	ProbeConcurrency int
	// VarDumpLimit 限制var dump中显示的字符数
	VarDumpLimit int
	// EnableSelfContainedTemplates 禁用自包含模板的处理
	EnableSelfContainedTemplates bool
	// EnableGlobalMatchersTemplates 启用处理全局匹配器模板
	EnableGlobalMatchersTemplates bool
	// EnableFileTemplates 启用文件模板
	EnableFileTemplates bool
	timeouts *Timeouts
	// AliveProxy is the alive proxy to use
	AliveHttpProxy string
	// AliveSocksProxy is the alive socks proxy to use
	AliveSocksProxy string
	// ScanName is the name of the scan to be uploaded
	ScanName string
	// TeamID is the team ID to use for cloud upload
	TeamID string
	// SecretsFile is file containing secrets for nuclei
	SecretsFile goflags.StringSlice
	// PreFetchSecrets pre-fetches the secrets from the auth provider
	PreFetchSecrets bool
}

// Timeouts 包含了所有超时变体
type Timeouts struct {
	// DialTimeout 是 fastdialer 的拨号超时（默认 10s）
	DialTimeout time.Duration
	// TcpReadTimeout 是 TCP（网络协议）从连接读取的超时（默认 5s）
	TcpReadTimeout time.Duration
	// HttpResponseHeaderTimeout 是 HTTP 响应头的超时（默认 10s）
	// 该超时防止服务器引起的无限挂起
	// 当使用 @timeout 请求注释时，暂时覆盖此超时
	HttpResponseHeaderTimeout time.Duration
	// HttpTimeout 是 HTTP 客户端的超时（默认 -> 3 x dial-timeout = 30s）
	HttpTimeout time.Duration
	// JsCompilerExec 是 JavaScript 编译器执行的超时/截止时间（默认 -> 2 x dial-timeout = 20s）
	JsCompilerExecutionTimeout time.Duration
	// CodeExecutionTimeout 是代码执行的超时（默认 -> 3 x dial-timeout = 30s）
	CodeExecutionTimeout time.Duration
}

// ApplyDefaults: 为超时变体应用默认值（如果缺失）
//  @receiver tv *Timeouts: 
func (tv *Timeouts) ApplyDefaults() {
	timeoutConfig := config.GetTimeoutConfig()
	if tv.DialTimeout == 0 {
		tv.DialTimeout = time.Duration(timeoutConfig.DialTimeout) * time.Second
	}
	if tv.TcpReadTimeout == 0 {
		tv.TcpReadTimeout = time.Duration(timeoutConfig.TcpReadTimeout) * time.Second
	}
	if tv.HttpResponseHeaderTimeout == 0 {
		tv.HttpResponseHeaderTimeout = time.Duration(timeoutConfig.HttpResponseHeaderTimeout) * time.Second
	}
	if tv.HttpTimeout == 0 {
		tv.HttpTimeout = time.Duration(timeoutConfig.HttpTimeout) * time.Second
	}
	if tv.JsCompilerExecutionTimeout == 0 {
		tv.JsCompilerExecutionTimeout = time.Duration(timeoutConfig.JsCompilerExecutionTimeout) * time.Second
	}
	if tv.CodeExecutionTimeout == 0 {
		tv.CodeExecutionTimeout = time.Duration(timeoutConfig.CodeExecutionTimeout) * time.Second
	}
}

// DefaultOptions 默认配置
func DefaultOptions() *Options {
	return &Options{
		RateLimit:               150,
		RateLimitDuration:       time.Second,
		BulkSize:                25,
		TemplateThreads:         25,
		HeadlessBulkSize:        10,
		PayloadConcurrency:      25,
		HeadlessTemplateThreads: 10,
		ProbeConcurrency:        50,
		Timeout:                 5,
		Retries:                 1,
		MaxHostError:            30,
		ResponseReadSize:        10 * unitutils.Mega,
		ResponseSaveSize:        unitutils.Mega,
	}
}


// NewTimeoutVariant: 创建一个新的超时变体，使用给定的拨号超时（以秒为单位）
//  @param dialTimeoutSec int: 拨号超时的值，以秒为单位。
//  @return *Timeouts *Timeouts: 
func NewTimeoutVariant(dialTimeoutSec int) *Timeouts {
	tv := &Timeouts{
		DialTimeout: time.Duration(dialTimeoutSec) * time.Second,
	}
	tv.ApplyDefaults()
	return tv
}

// GetTimeouts: 返回执行器使用的超时设置
//  @receiver eo *Options: 
//  @return *Timeouts *Timeouts: 
func (eo *Options) GetTimeouts() *Timeouts {
	if eo.timeouts != nil {
		// 冗余操作，但应用默认值以避免潜在问题
		eo.timeouts.ApplyDefaults()
		return eo.timeouts
	}
	// 设置超时值
	eo.timeouts = NewTimeoutVariant(eo.Timeout)
	eo.timeouts.ApplyDefaults()
	return eo.timeouts
}


// ShouldLoadResume resume file
func (options *Options) ShouldLoadResume() bool {
	return options.Resume != "" && fileutil.FileExists(options.Resume)
}


func (options *Options) ShouldUseHostError() bool {
	return options.MaxHostError > 0 && !options.NoHostErrors
}
